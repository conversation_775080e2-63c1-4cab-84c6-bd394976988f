# Data Ingestion Service

A FastAPI-based microservice for processing documents and managing data ingestion pipelines.

## Features

- **FastAPI Endpoints**: RESTful API for document processing
- **Background Processing**: Asynchronous file processing with task tracking
- **Health Monitoring**: Service health checks and status monitoring
- **Multiple File Types**: Support for PDF, DOCX, TXT, and other document formats
- **Qdrant Integration**: Vector database storage and retrieval
- **Embedding Support**: Integration with embedding services
- **Configurable Processing**: Customizable chunk sizes, splitting strategies, and LLM models

## Quick Start

### 1. Install Dependencies

```bash
cd Data_Ingestion_Service
pip install -r requirements.txt
```

### 2. Configure Environment

Copy and configure the environment file:

```bash
cp environment.env .env
```

Edit `.env` with your configuration:
- Database URLs
- Qdrant connection details
- API keys (Gemini, etc.)
- Service ports and hosts

### 3. Run the Service

```bash
# Option 1: Using the run script
python run_service.py

# Option 2: Direct uvicorn
uvicorn main:app --host 0.0.0.0 --port 8001 --reload
```

The service will be available at: http://localhost:8001

## API Endpoints

### Health Check
```http
GET /health
```

Response:
```json
{
  "status": "healthy",
  "models_loaded": true,
  "pipeline_ready": true,
  "embedding_service": true
}
```

### Ingest Files
```http
POST /ingest
```

Form data:
- `files`: Multiple file uploads
- `username`: User identifier
- `collection_name`: Qdrant collection name
- `gemini_api_key`: Gemini API key
- `qdrant_url`: Qdrant server URL
- `qdrant_api_key`: Qdrant API key (optional)
- `chunk_size`: Text chunk size (default: 750)
- `chunk_overlap`: Chunk overlap (default: 150)

Response:
```json
{
  "success": true,
  "message": "Files queued for processing",
  "task_id": "user_collection_20231201_120000",
  "processed_documents": 0,
  "failed_documents": 0,
  "total_processing_time": 0.0
}
```


## Integration with Existing Backend

The service includes a client module (`client.py`) that provides seamless integration with your existing backend:

```python
from Data_Ingestion_Service.client import DataIngestionClient

# Initialize client
client = DataIngestionClient("http://localhost:8001")

# Ingest files
result = await client.ingest_files(
    files=file_objects,
    username="user123",
    collection_name="user_collection",
    processing_config={...},
    gemini_api_key="your_key"
)
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATA_INGESTION_PORT` | Service port | 8001 |
| `HOST` | Service host | 0.0.0.0 |
| `ENVIRONMENT` | Environment (Dev/Prod) | Dev |
| `LOG_LEVEL` | Logging level | INFO |
| `QDRANT_URL_Dev` | Qdrant URL for Dev | http://**************:6335 |
| `QDRANT_URL_Prod` | Qdrant URL for Prod | http://**************:6333 |
| `GEMINI_API_KEY` | Gemini API key | (required) |

## File Structure

```
Data_Ingestion_Service/
├── main.py                 # FastAPI application
├── client.py              # Client for backend integration
├── run_service.py         # Service runner script
├── requirements.txt       # Python dependencies
├── environment.env        # Environment configuration
├── config.py             # Configuration classes
├── pipeline.py           # Main processing pipeline
├── data_ingestion_pipeline.py  # Pipeline functions
├── embedding_client.py   # Embedding service client
├── core.py               # Core utilities
└── unstructure.py        # Document processing utilities
```

## Docker Support

The service can be containerized using Docker. A Dockerfile is available in the parent directory.

## Monitoring and Logging

- All processing activities are logged with timestamps
- Health endpoints provide service status
- Background task processing with error handling
- Configurable log levels via environment variables

## Error Handling

The service includes comprehensive error handling:
- File validation and processing errors
- Network and service connectivity issues
- Configuration and authentication errors
- Graceful degradation and fallback mechanisms

## Performance Considerations

- Asynchronous processing for multiple files
- Background task execution for long-running operations
- Configurable timeouts and concurrency limits
- Efficient memory usage with streaming for large files

## Security

- API key authentication for Gemini services
- Input validation and sanitization
- Secure file handling and temporary file cleanup
- CORS configuration for web integration


