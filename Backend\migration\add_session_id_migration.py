#!/usr/bin/env python3
"""
Database migration script to add session_id column to expert_chat_interactions table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.database import get_db, engine
from app.models.feedback import ExpertChatInteraction

def migrate_add_session_id():
    """Add session_id column to expert_chat_interactions table"""
    
    print("Starting migration: Adding session_id column to expert_chat_interactions table...")
    
    try:
        with engine.connect() as connection:
            # Start transaction
            trans = connection.begin()
            
            try:
                # Check if column already exists
                result = connection.execute(text("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'expert_chat_interactions' 
                    AND column_name = 'session_id'
                """))
                
                if result.fetchone():
                    print("✅ session_id column already exists. Migration not needed.")
                    trans.rollback()
                    return True
                
                # Add the session_id column
                print("Adding session_id column...")
                connection.execute(text("""
                    ALTER TABLE expert_chat_interactions 
                    ADD COLUMN session_id VARCHAR(100) DEFAULT 'default'
                """))
                
                # Update existing records to have 'default' session_id
                print("Updating existing records...")
                result = connection.execute(text("""
                    UPDATE expert_chat_interactions 
                    SET session_id = 'default' 
                    WHERE session_id IS NULL
                """))
                print(f"Updated {result.rowcount} existing records")
                
                # Create index for better performance
                print("Creating index...")
                connection.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_expert_interactions_session 
                    ON expert_chat_interactions (user_id, session_id, agent_type, created_at)
                """))
                
                # Commit transaction
                trans.commit()
                print("✅ Migration completed successfully!")
                return True
                
            except Exception as e:
                trans.rollback()
                print(f"❌ Error during migration: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Failed to connect to database: {e}")
        return False

if __name__ == "__main__":
    success = migrate_add_session_id()
    if success:
        print("\n🎉 Database migration completed successfully!")
        print("You can now use database-based chat history instead of Redis.")
    else:
        print("\n💥 Migration failed! Please check the errors above.")
        sys.exit(1)

