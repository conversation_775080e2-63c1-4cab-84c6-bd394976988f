'use client';

import React from 'react';

interface User {
  id: number;
  username: string;
  email: string;
  industry: string;
  role: string;
  status: string;
  is_active: boolean;
  created_at: string;
}

interface AccountRequestCardProps {
  user: User;
  onClick: (user: User) => void;
  currentUserId?: number;
  onApprove?: (userId: number) => void;
  onReject?: (userId: number) => void;
}

export default function AccountRequestCard({ user, onClick, currentUserId, onApprove, onReject }: AccountRequestCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    return status ? status.charAt(0).toUpperCase() + status.slice(1) : 'N/A';
  };

  const getRoleColor = (role: string) => {
    return role === 'admin' ? 'text-red-800 bg-red-200' : 'text-green-800 bg-green-200';
  };

  const getRoleLabel = (role: string) => {
    return role === 'admin' ? 'Admin' : 'User';
  };

  const canTakeAction = user.role !== 'admin' && user.id !== currentUserId;

  return (
    <div
      className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-pointer hover:bg-gray-50 hover:shadow-md transition-all"
      onClick={() => onClick(user)}
    >
      <div className="flex items-center space-x-4">
        <div className="relative">
          <div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-900 font-medium text-base">
              {user.username.charAt(0).toUpperCase()}
            </span>
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex flex-col space-y-1">
            <div className="flex items-center justify-between">
              <div className="text-base font-medium text-gray-900 truncate">{user.username}</div>
              <div className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                {getRoleLabel(user.role)}
              </div>
            </div>
            <div className="text-sm text-gray-500 truncate">{user.email}</div>
            <div className="flex items-center justify-between">
              <div className="text-xs text-gray-600">
                {user.industry || 'N/A'}
              </div>
              <div className="text-xs text-gray-500">
                {formatDate(user.created_at)}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between mt-3">
        <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
          {getStatusLabel(user.status)}
        </div>

        {canTakeAction && (
          <div className="flex space-x-2">
            {user.status === 'pending' && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onApprove?.(user.id);
                  }}
                  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium border border-green-600 text-green-600 hover:bg-green-50 transition-colors"
                >
                  Approve
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onReject?.(user.id);
                  }}
                  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium border border-red-600 text-red-600 hover:bg-red-50 transition-colors"
                >
                  Reject
                </button>
              </>
            )}
            {user.status === 'rejected' && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onApprove?.(user.id);
                }}
                className="inline-flex items-center px-2 py-1 rounded text-xs font-medium border border-green-600 text-green-600 hover:bg-green-50 transition-colors"
              >
                Approve
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
