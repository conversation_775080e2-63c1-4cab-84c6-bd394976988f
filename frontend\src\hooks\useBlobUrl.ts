import { useState, useCallback, useEffect } from 'react';

/**
 * Custom hook for managing blob URLs with proper cleanup
 */
export const useBlobUrl = () => {
  const [blobUrl, setBlobUrl] = useState<string | null>(null);

  const createBlobUrl = useCallback((blob: Blob) => {
    // Clean up previous URL if it exists
    if (blobUrl) {
      URL.revokeObjectURL(blobUrl);
    }
    
    const newUrl = URL.createObjectURL(blob);
    setBlobUrl(newUrl);
    return newUrl;
  }, [blobUrl]);

  const clearBlobUrl = useCallback(() => {
    if (blobUrl) {
      URL.revokeObjectURL(blobUrl);
      setBlobUrl(null);
    }
  }, [blobUrl]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, []); // Empty dependency array for unmount cleanup only

  return { blobUrl, createBlobUrl, clearBlobUrl };
};