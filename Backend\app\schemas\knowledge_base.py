from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class KnowledgeDocumentBase(BaseModel):
    title: str = Field(..., max_length=200, description="Title of the document")
    description: Optional[str] = Field(None, description="Brief description of the document")
    space_id: int = Field(..., description="Space the document belongs to")

class KnowledgeDocumentCreate(KnowledgeDocumentBase):
    document_type: str = Field(
        ...,
        pattern="^(pdf|docx|text)$",
        description="Type of document (pdf, docx, or text)"
    )
    content: Optional[str] = Field(None, description="Text content (for text documents)")
    
    class Config:
        from_attributes = True

class KnowledgeDocumentUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = Field(None)
    space_id: Optional[int] = Field(None)
    
    class Config:
        from_attributes = True

class KnowledgeDocumentResponse(KnowledgeDocumentBase):
    id: int
    space_name: Optional[str]
    document_type: str
    filename: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: int
    creator_username: Optional[str]
    has_file: bool
    has_content: bool
    content: Optional[str] = None
    indexed: bool
    indexed_at: Optional[datetime]
    collection_name: Optional[str]
    
    class Config:
        from_attributes = True
        
class FailedUpload(BaseModel):
    filename: str
    reason: str

class BulkUploadResponse(BaseModel):
    success: bool
    uploaded_count: int
    failed_count: int
    uploaded_documents: List[Dict[str, Any]]
    failed_uploads: List[FailedUpload]
    duplicate_files: List[Dict[str, Any]] = []
class DocumentIndexRequest(BaseModel):
    """Request model for indexing documents"""
    document_ids: Optional[List[int]] = Field(None, description="Specific document IDs to index. If not provided, all non-indexed documents will be indexed.")
    
    class Config:
        json_schema_extra = {
            "example": {
                "document_ids": [1, 2, 3]
            }
        }

class DocumentIndexResponse(BaseModel):
    """Response model for document indexing"""
    success: bool
    message: str
    indexed_count: int
    failed_count: Optional[int] = 0
    collection_name: Optional[str] = None
    total_processing_time: Optional[float] = 0 