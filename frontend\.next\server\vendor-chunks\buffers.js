/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/buffers";
exports.ids = ["vendor-chunks/buffers"];
exports.modules = {

/***/ "(ssr)/./node_modules/buffers/index.js":
/*!***************************************!*\
  !*** ./node_modules/buffers/index.js ***!
  \***************************************/
/***/ ((module) => {

eval("module.exports = Buffers;\n\nfunction Buffers (bufs) {\n    if (!(this instanceof Buffers)) return new Buffers(bufs);\n    this.buffers = bufs || [];\n    this.length = this.buffers.reduce(function (size, buf) {\n        return size + buf.length\n    }, 0);\n}\n\nBuffers.prototype.push = function () {\n    for (var i = 0; i < arguments.length; i++) {\n        if (!Buffer.isBuffer(arguments[i])) {\n            throw new TypeError('Tried to push a non-buffer');\n        }\n    }\n    \n    for (var i = 0; i < arguments.length; i++) {\n        var buf = arguments[i];\n        this.buffers.push(buf);\n        this.length += buf.length;\n    }\n    return this.length;\n};\n\nBuffers.prototype.unshift = function () {\n    for (var i = 0; i < arguments.length; i++) {\n        if (!Buffer.isBuffer(arguments[i])) {\n            throw new TypeError('Tried to unshift a non-buffer');\n        }\n    }\n    \n    for (var i = 0; i < arguments.length; i++) {\n        var buf = arguments[i];\n        this.buffers.unshift(buf);\n        this.length += buf.length;\n    }\n    return this.length;\n};\n\nBuffers.prototype.copy = function (dst, dStart, start, end) {\n    return this.slice(start, end).copy(dst, dStart, 0, end - start);\n};\n\nBuffers.prototype.splice = function (i, howMany) {\n    var buffers = this.buffers;\n    var index = i >= 0 ? i : this.length - i;\n    var reps = [].slice.call(arguments, 2);\n    \n    if (howMany === undefined) {\n        howMany = this.length - index;\n    }\n    else if (howMany > this.length - index) {\n        howMany = this.length - index;\n    }\n    \n    for (var i = 0; i < reps.length; i++) {\n        this.length += reps[i].length;\n    }\n    \n    var removed = new Buffers();\n    var bytes = 0;\n    \n    var startBytes = 0;\n    for (\n        var ii = 0;\n        ii < buffers.length && startBytes + buffers[ii].length < index;\n        ii ++\n    ) { startBytes += buffers[ii].length }\n    \n    if (index - startBytes > 0) {\n        var start = index - startBytes;\n        \n        if (start + howMany < buffers[ii].length) {\n            removed.push(buffers[ii].slice(start, start + howMany));\n            \n            var orig = buffers[ii];\n            //var buf = new Buffer(orig.length - howMany);\n            var buf0 = new Buffer(start);\n            for (var i = 0; i < start; i++) {\n                buf0[i] = orig[i];\n            }\n            \n            var buf1 = new Buffer(orig.length - start - howMany);\n            for (var i = start + howMany; i < orig.length; i++) {\n                buf1[ i - howMany - start ] = orig[i]\n            }\n            \n            if (reps.length > 0) {\n                var reps_ = reps.slice();\n                reps_.unshift(buf0);\n                reps_.push(buf1);\n                buffers.splice.apply(buffers, [ ii, 1 ].concat(reps_));\n                ii += reps_.length;\n                reps = [];\n            }\n            else {\n                buffers.splice(ii, 1, buf0, buf1);\n                //buffers[ii] = buf;\n                ii += 2;\n            }\n        }\n        else {\n            removed.push(buffers[ii].slice(start));\n            buffers[ii] = buffers[ii].slice(0, start);\n            ii ++;\n        }\n    }\n    \n    if (reps.length > 0) {\n        buffers.splice.apply(buffers, [ ii, 0 ].concat(reps));\n        ii += reps.length;\n    }\n    \n    while (removed.length < howMany) {\n        var buf = buffers[ii];\n        var len = buf.length;\n        var take = Math.min(len, howMany - removed.length);\n        \n        if (take === len) {\n            removed.push(buf);\n            buffers.splice(ii, 1);\n        }\n        else {\n            removed.push(buf.slice(0, take));\n            buffers[ii] = buffers[ii].slice(take);\n        }\n    }\n    \n    this.length -= removed.length;\n    \n    return removed;\n};\n \nBuffers.prototype.slice = function (i, j) {\n    var buffers = this.buffers;\n    if (j === undefined) j = this.length;\n    if (i === undefined) i = 0;\n    \n    if (j > this.length) j = this.length;\n    \n    var startBytes = 0;\n    for (\n        var si = 0;\n        si < buffers.length && startBytes + buffers[si].length <= i;\n        si ++\n    ) { startBytes += buffers[si].length }\n    \n    var target = new Buffer(j - i);\n    \n    var ti = 0;\n    for (var ii = si; ti < j - i && ii < buffers.length; ii++) {\n        var len = buffers[ii].length;\n        \n        var start = ti === 0 ? i - startBytes : 0;\n        var end = ti + len >= j - i\n            ? Math.min(start + (j - i) - ti, len)\n            : len\n        ;\n        \n        buffers[ii].copy(target, ti, start, end);\n        ti += end - start;\n    }\n    \n    return target;\n};\n\nBuffers.prototype.pos = function (i) {\n    if (i < 0 || i >= this.length) throw new Error('oob');\n    var l = i, bi = 0, bu = null;\n    for (;;) {\n        bu = this.buffers[bi];\n        if (l < bu.length) {\n            return {buf: bi, offset: l};\n        } else {\n            l -= bu.length;\n        }\n        bi++;\n    }\n};\n\nBuffers.prototype.get = function get (i) {\n    var pos = this.pos(i);\n\n    return this.buffers[pos.buf].get(pos.offset);\n};\n\nBuffers.prototype.set = function set (i, b) {\n    var pos = this.pos(i);\n\n    return this.buffers[pos.buf].set(pos.offset, b);\n};\n\nBuffers.prototype.indexOf = function (needle, offset) {\n    if (\"string\" === typeof needle) {\n        needle = new Buffer(needle);\n    } else if (needle instanceof Buffer) {\n        // already a buffer\n    } else {\n        throw new Error('Invalid type for a search string');\n    }\n\n    if (!needle.length) {\n        return 0;\n    }\n\n    if (!this.length) {\n        return -1;\n    }\n\n    var i = 0, j = 0, match = 0, mstart, pos = 0;\n\n    // start search from a particular point in the virtual buffer\n    if (offset) {\n        var p = this.pos(offset);\n        i = p.buf;\n        j = p.offset;\n        pos = offset;\n    }\n\n    // for each character in virtual buffer\n    for (;;) {\n        while (j >= this.buffers[i].length) {\n            j = 0;\n            i++;\n\n            if (i >= this.buffers.length) {\n                // search string not found\n                return -1;\n            }\n        }\n\n        var char = this.buffers[i][j];\n\n        if (char == needle[match]) {\n            // keep track where match started\n            if (match == 0) {\n                mstart = {\n                    i: i,\n                    j: j,\n                    pos: pos\n                };\n            }\n            match++;\n            if (match == needle.length) {\n                // full match\n                return mstart.pos;\n            }\n        } else if (match != 0) {\n            // a partial match ended, go back to match starting position\n            // this will continue the search at the next character\n            i = mstart.i;\n            j = mstart.j;\n            pos = mstart.pos;\n            match = 0;\n        }\n\n        j++;\n        pos++;\n    }\n};\n\nBuffers.prototype.toBuffer = function() {\n    return this.slice();\n}\n\nBuffers.prototype.toString = function(encoding, start, end) {\n    return this.slice(start, end).toString(encoding);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffers/index.js\n");

/***/ })

};
;