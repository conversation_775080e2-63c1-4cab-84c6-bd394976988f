'use client';

import React from 'react';
import { TicketProvider, useTicketContext } from '@/contexts';
import { TicketFilters } from './TicketFilters';
import { TicketList } from './TicketList';
import TicketModal from './TicketModal';
import TicketCreationModal from './TicketCreationModal';
import { Pagination } from './Pagination';
import { ErrorBanner } from '@/components/ui';
import { ErrorBoundary } from '@/components/ErrorBoundary';

/**
 * Internal component that uses the TicketContext
 */
function UserTicketsPanelContent() {
  const {
    tickets,
    loading,
    error,
    total,
    currentPage,
    perPage,
    hasAnyTickets,
    activeFilter,
    setActiveFilter,
    setPage,
    selectedTicket,
    isCreateModalOpen,
    isViewModalOpen,
    openCreateModal,
    closeCreateModal,
    openViewModal,
    closeViewModal,
    refreshTickets,
  } = useTicketContext();

  return (
    <div className="mt-8">
      {/* Header */}
      <div className="text-left mb-6 flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">My Support Tickets</h2>
          <p className="mt-1 text-sm text-gray-600">Track the status of your submitted tickets</p>
        </div>
        <button
          onClick={openCreateModal}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-900 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          New Ticket
        </button>
      </div>

      {/* Error Banner */}
      {error && <ErrorBanner message={error} />}

      {/* Filters */}
      <TicketFilters
        activeFilter={activeFilter}
        onFilterChange={setActiveFilter}
      />

      {/* Ticket List */}
      <TicketList
        tickets={tickets}
        loading={loading}
        hasAnyTickets={hasAnyTickets}
        activeFilter={activeFilter}
        onTicketClick={openViewModal}
        onCreateTicket={openCreateModal}
        isAdmin={false}
      />

      {/* Pagination */}
      {!loading && total > perPage && (
        <Pagination
          currentPage={currentPage}
          totalItems={total}
          itemsPerPage={perPage}
          onPageChange={setPage}
          className="mt-6"
        />
      )}

      {/* Modals */}
      <TicketCreationModal
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
        onTicketCreated={refreshTickets}
      />

      <TicketModal
        isOpen={isViewModalOpen}
        onClose={closeViewModal}
        ticket={selectedTicket}
        isAdmin={false}
        onRefresh={refreshTickets}
      />
    </div>
  );
}

/**
 * Main component with Provider wrapper
 */
export default function UserTicketsPanel() {
  return (
    <ErrorBoundary>
      <TicketProvider isAdmin={false}>
        <UserTicketsPanelContent />
      </TicketProvider>
    </ErrorBoundary>
  );
}