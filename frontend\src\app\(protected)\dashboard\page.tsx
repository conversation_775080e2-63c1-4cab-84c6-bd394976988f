'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { MainLayout } from '@/components/layout';
import { AccountRequestsPanel, TicketsPanel } from '@/components/admin';
import { DashboardSummary, UserMetricsList } from '@/components/dashboard';
import { API_BASE_URL } from '@/lib/api';
import ProfileModal from '@/components/profile/ProfileModal';

type AdminNavigationTab = 'overview' | 'profile' | 'user-metrics' | 'tickets' | 'account-requests';

interface SummaryStats {
  users: {
    total: number;
    active: number;
    pending: number;
    approved: number;
    online: number;
  };
  ai_queries: {
    total: number;
    recent_week: number;
  };
  knowledge_docs: {
    total: number;
    indexed: number;
    recent_week: number;
  };
  feedback: {
    total: number;
    helpful: number;
    helpful_percentage: number;
  };
  shared_responses: {
    total: number;
  };
}

export default function DashboardPage() {
  const [summaryStats, setSummaryStats] = useState<SummaryStats | null>(null);
  const [summaryLoading, setSummaryLoading] = useState(true);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<AdminNavigationTab>('overview');
  const router = useRouter();

  // Get current user and check permissions
  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        if (user.role !== 'admin') {
          router.push('/');
          return;
        }
        setCurrentUser(user);
      } catch {
        router.push('/login');
      }
    } else {
      router.push('/login');
    }
  }, [router]);

  // Fetch summary statistics
  const fetchSummaryStats = async () => {
    try {
      setSummaryLoading(true);
      const token = localStorage.getItem('access_token');
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/dashboard/summary-stats`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSummaryStats(data.summary);
      }
    } catch (error) {
      console.error('Error fetching summary stats:', error);
    } finally {
      setSummaryLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser) {
      fetchSummaryStats();
    }
  }, [currentUser, refreshTrigger]);

  // Refresh all data
  const handleRefresh = () => {
    setIsRefreshing(true);
    setRefreshTrigger(prev => prev + 1);
    // Reset refreshing state after a short delay
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  const navigationItems = [
    {
      id: 'overview' as AdminNavigationTab,
      label: 'Overview',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
    },
    {
      id: 'profile' as AdminNavigationTab,
      label: 'Profile',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
    },
    {
      id: 'user-metrics' as AdminNavigationTab,
      label: 'User Metrics',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
    },
    {
      id: 'tickets' as AdminNavigationTab,
      label: 'Support Tickets',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
        </svg>
      ),
    },
    {
      id: 'account-requests' as AdminNavigationTab,
      label: 'Account Requests',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
        </svg>
      ),
    },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div>
            {summaryStats && (
              <DashboardSummary stats={summaryStats} loading={summaryLoading} />
            )}
          </div>
        );
      
      case 'profile':
        return (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 sm:p-8 hover:shadow-xl transition-shadow duration-300">
              {currentUser && <ProfileModal user={currentUser} variant="inline" />}
            </div>
          </div>
        );
      
      case 'user-metrics':
        return (
          <div>
            <UserMetricsList refreshTrigger={refreshTrigger} />
          </div>
        );
      
      case 'tickets':
        return (
          <div>
            <TicketsPanel />
          </div>
        );
      
      case 'account-requests':
        return (
          <div>
            <AccountRequestsPanel refreshTrigger={refreshTrigger} />
          </div>
        );
      
      default:
        return null;
    }
  };

  if (!currentUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="flex h-full">
        {/* Sidebar Navigation - 20% */}
        <aside className="hidden lg:flex lg:flex-col lg:w-1/5 bg-white border-r border-gray-200 sticky top-0 h-screen overflow-y-auto">
          <div className="p-6">
            <h1 className="text-lg font-semibold text-gray-900">Admin Dashboard</h1>
            <p className="text-xs text-gray-500 mt-1">System management</p>
          </div>
          
          <nav className="flex-1 px-3 space-y-1">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === item.id
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                {item.icon}
                <span>{item.label}</span>
              </button>
            ))}
          </nav>

          
        </aside>

        {/* Mobile Navigation */}
        <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-10">
          <nav className="flex justify-around items-center px-2 py-2">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`flex flex-col items-center gap-1 px-2 py-2 rounded-lg text-xs font-medium transition-colors ${
                  activeTab === item.id
                    ? 'text-blue-700'
                    : 'text-gray-600'
                }`}
              >
                {item.icon}
                <span className="text-[10px]">{item.label.split(' ')[0]}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Main Content Area - 80% */}
        <main className="flex-1 overflow-y-auto bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8 pb-20 lg:pb-8">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">
                  {activeTab === 'overview' && 'System Overview'}
                  {activeTab === 'profile' && 'Admin Profile'}
                  {activeTab === 'user-metrics' && 'User Metrics'}
                  {activeTab === 'tickets' && 'Support Tickets Management'}
                  {activeTab === 'account-requests' && 'Account Requests Management'}
                </h1>
                <p className="mt-1 text-sm text-gray-500">
                  {activeTab === 'overview' && 'Monitor key system metrics and performance statistics'}
                  {activeTab === 'profile' && 'Manage your administrator profile information'}
                  {activeTab === 'user-metrics' && 'View detailed user activity metrics and engagement analytics'}
                  {activeTab === 'tickets' && 'Review and manage all user support tickets'}
                  {activeTab === 'account-requests' && 'Process pending user registration requests'}
                </p>
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="inline-flex items-center justify-center p-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
                  title="Refresh"
                >
                  <svg className={`w-5 h-5 ${isRefreshing ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Dynamic Content */}
            {renderContent()}
          </div>
        </main>
      </div>
    </MainLayout>
  );
}

