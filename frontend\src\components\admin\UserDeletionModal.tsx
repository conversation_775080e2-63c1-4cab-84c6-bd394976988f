'use client';

import React, { useState, useEffect } from 'react';
import { API_BASE_URL } from '@/lib/api';

interface UserData {
  email: string;
  role: string;
  status: string;
}

interface UserDeletionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  userId: number;
  username: string;
}

export default function UserDeletionModal({ 
  isOpen, 
  onClose, 
  onConfirm, 
  userId, 
  username 
}: UserDeletionModalProps) {
  const [confirmText, setConfirmText] = useState('');
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(false);

  // Fetch user data when modal opens
  useEffect(() => {
    if (isOpen && userId) {
      fetchUserData();
    }
  }, [isOpen, userId]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setConfirmText('');
      setUserData(null);
    }
  }, [isOpen]);

  const fetchUserData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('access_token');
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/user-deletion/${userId}/delete-preview`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        setUserData({
          email: data.user.email,
          role: data.user.role,
          status: data.user.status
        });
      }
    } catch (err) {
      console.error('Failed to fetch user data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = () => {
    if (confirmText === 'DELETE') {
      onConfirm();
      onClose();
    }
  };

  const isConfirmValid = confirmText === 'DELETE';

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-3 sm:p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-sm sm:max-w-md w-full max-h-[90vh] sm:max-h-[85vh] flex flex-col">
        {/* Sticky Header */}
        <div className="sticky top-0 bg-red-50 border-b border-red-200 p-4 sm:p-6 rounded-t-xl z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-bold text-red-900">Account Deletion</h3>
                <p className="text-sm text-red-700">User data will be preserved</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-red-400 hover:text-red-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto p-4 sm:p-6">
          {/* Warning Message */}
          <div className="mb-4">
            <p className="text-gray-900 font-medium mb-2">
              You are about to delete the account for user:
            </p>
            <div className="bg-gray-100 rounded-lg p-3">
              <p className="text-lg font-bold text-gray-900">{username}</p>
              {loading ? (
                <div className="mt-2 text-sm text-gray-600">Loading user details...</div>
              ) : userData ? (
                <div className="mt-2 space-y-1 text-sm text-gray-600">
                  <div><span className="font-medium">Email:</span> {userData.email}</div>
                  <div><span className="font-medium">Role:</span> {userData.role}</div>
                  <div><span className="font-medium">Status:</span> {userData.status}</div>
                </div>
              ) : null}
            </div>
            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800 font-medium">
                ⚠️ This will remove the user's login access only
              </p>
              <p className="text-sm text-green-700 mt-1">
                ✅ All user data will remain preserved
              </p>
            </div>
          </div>
        </div>

        {/* Sticky Footer */}
        <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4 sm:p-6 rounded-b-xl">
          {/* Confirmation Input */}
          <div className="mb-4">
            <label className="block text-sm font-semibold text-gray-900 mb-2">
              Type "DELETE" to confirm:
            </label>
            <input
              type="text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2 ${
                confirmText === 'DELETE' 
                  ? 'border-green-300 focus:ring-green-500 bg-green-50' 
                  : 'border-red-300 focus:ring-red-500'
              }`}
              placeholder="Type DELETE here"
              autoComplete="off"
            />
            {confirmText && confirmText !== 'DELETE' && (
              <p className="text-xs text-red-600 mt-1">You must type "DELETE" exactly</p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              disabled={!isConfirmValid}
              className={`flex-1 px-4 py-2 text-sm font-medium rounded-lg focus:outline-none focus:ring-2 transition-colors ${
                isConfirmValid
                  ? 'text-white bg-red-600 border border-red-600 hover:bg-red-700 focus:ring-red-500'
                  : 'text-gray-400 bg-gray-100 border border-gray-300 cursor-not-allowed'
              }`}
            >
              Delete Account
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
