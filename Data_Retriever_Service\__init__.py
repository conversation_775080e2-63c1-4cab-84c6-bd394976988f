"""
Retriever Package - A Multi-Modal RAG Pipeline with Query Classification

This package provides a comprehensive retrieval-augmented generation (RAG) system
that can handle both text and image data, with intelligent query classification
for conversational and technical queries.
"""

# Import main classes that users will need
from config import RetrieverConfig
# Import HTTP client for embedding service communication
from embedding_http_client import EmbeddingClient, EmbeddingConfig, DenseEmbeddingWrapper, SparseEmbeddingWrapper

__all__ = [
    # Main classes
    "RetrieverConfig",

    # Embedding classes (HTTP client)
    "EmbeddingClient",
    "EmbeddingConfig", 
    "DenseEmbeddingWrapper",
    "SparseEmbeddingWrapper",

    # Package metadata
    "__version__",
    "__author__",
    "__email__",
    "__description__"
]
