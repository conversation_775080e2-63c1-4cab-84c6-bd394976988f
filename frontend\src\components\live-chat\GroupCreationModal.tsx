import React, { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { useChat } from '@/contexts/Livechatcontext';
import { fetchUser, UserWithMetrics } from '@/lib/api';

interface GroupCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function GroupCreationModal({ isOpen, onClose }: GroupCreationModalProps) {
  const { createGroup } = useChat();
  const [name, setName] = useState('');
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<UserWithMetrics[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [searchUser, setSearchUser] = useState('');
  const [searchResult, setSearchResult] = useState<UserWithMetrics | null>(null);
  const [hasSearched, setHasSearched] = useState(false);

  const findUser = async () => {
    if (!searchUser.trim()) return;
    
    try {
      const userData = await fetchUser(searchUser);
      setSearchResult(userData);
      setHasSearched(true);
    } catch (error) {
      console.error('Error finding user:', error);
      setSearchResult(null);
      setHasSearched(true);
    }
  };

  const addUserToGroup = (user: UserWithMetrics) => {
    if (!user || !user.id) {
      console.error('Invalid user data:', user);
      return;
    }
    
    // Check if user is already added
    if (selectedIds.includes(user.id)) {
      
      // Clear search anyway to allow searching for another user
      setSearchResult(null);
      setSearchUser('');
      setHasSearched(false);
      return;
    }
    
    try {
      // Add the user to the selected users list
      const newSelectedIds = [...selectedIds, user.id];
      const newSelectedUsers = [...selectedUsers, user];
      
      setSelectedIds(newSelectedIds);
      setSelectedUsers(newSelectedUsers);
      
      // Clear search fields for next search
      setSearchResult(null);
      setSearchUser('');
      setHasSearched(false);
      
  
    } catch (error) {
      console.error('Error adding user to group:', error);
    }
  };

  const removeUser = (userId: number) => {
    setSelectedIds(prev => prev.filter(id => id !== userId));
    setSelectedUsers(prev => prev.filter(user => user.id !== userId));
  };

  const handleCreate = async () => {
    if (!name.trim() || selectedIds.length === 0) return;
    
    try {
      setSubmitting(true);
      
      // Ensure we're sending valid member IDs (not including current user)
      // The backend will add the current user as admin automatically
      const currentUserId = parseInt(localStorage.getItem('id') || '0', 10);
      
      // Filter out any invalid IDs and the current user's ID
      const validMemberIds = selectedIds.filter(id => 
        id !== currentUserId && !isNaN(id) && id > 0
      );
      
    
      
      if (validMemberIds.length === 0) {
        alert('Please add at least one other member to the group.');
        setSubmitting(false);
        return;
      }
      
      const grp = await createGroup(name.trim(), validMemberIds);
      
      if (grp) {
        
        setName('');
        setSelectedIds([]);
        setSelectedUsers([]);
        onClose();
      } else {
        console.error('Failed to create group - returned null');
        alert('Failed to create group. Please try again.');
      }
    } catch (error) {
      console.error('Error creating group:', error);
      alert('An error occurred while creating the group.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      findUser();
    }
  };

  return (
    <Dialog key="group-creation-dialog" open={isOpen} onClose={onClose} className="fixed z-50 inset-0 overflow-y-auto">
      <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center backdrop-blur-[2px]">
        <div className="bg-blue-50 p-6 rounded-xl shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto border border-blue-100 relative z-10">
          <div className="flex justify-between items-center mb-6 border-b border-blue-100 pb-3">
            <h3 className="text-xl font-semibold text-blue-800">Create Group</h3>
            <button
              onClick={onClose}
              className="text-blue-500 hover:text-blue-700 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Group Name */}
          <div className="mb-6 relative">
            <label htmlFor="group-name" className="block text-sm font-medium text-gray-700 mb-2">Group Name</label>
            <input
              id="group-name"
              name="group-name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-4 py-3 border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900 transition-all shadow-sm"
              placeholder="Enter group name"
            />
          </div>
          
          {/* Search for users */}
          <div className="mb-6 relative">
            <label htmlFor="search-user" className="block text-sm font-medium text-gray-700 mb-2">
              Search Users {selectedUsers.length > 0 ? `(Add more members)` : ""}
            </label>
            <div className="relative">
              <input
                id="search-user"
                name="search-user"
                type="text"
                placeholder="Search by username..."
                value={searchUser}
                onChange={(e) => {
                  setHasSearched(false);
                  setSearchUser(e.target.value);
                }}
                onKeyPress={handleKeyPress}
                className="w-full px-4 py-3 pl-10 border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900 transition-all shadow-sm"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            <button 
              type="button"
              onClick={findUser} 
              className="mt-3 w-full bg-blue-900 hover:bg-blue-700 text-white px-4 py-2.5 rounded-lg font-medium transition-colors flex items-center justify-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Search
            </button>
          </div>
          
          {/* Search Results */}
          <div className="mt-4">
            {searchUser && hasSearched && (
              searchResult ? (
                <div
                  key={`search-result-${searchResult.id}`}
                  className="flex items-center space-x-4 p-4 hover:bg-blue-100 rounded-lg cursor-pointer transition-colors border border-blue-100 shadow-sm bg-white"
                >
                  <div className="relative">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white text-lg font-medium shadow-md">
                      {searchResult.username[0]?.toUpperCase()}
                    </div>
                    {(searchResult.is_active || searchResult.metrics?.is_online) && (
                      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full shadow"></div>
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="text-base font-medium text-gray-900">{searchResult.username}</p>
                    <p className="text-sm text-blue-900 capitalize">{searchResult.role}</p>
                  </div>
                  <button 
                    type="button"
                    className="bg-blue-100 text-blue-700 px-3 py-1.5 rounded text-xs font-medium hover:bg-blue-200"
                    onClick={() => {
                      addUserToGroup(searchResult);
                    }}
                  >
                    Add
                  </button>
                </div>
              ) : (
                <div className="text-center py-6 bg-white rounded-lg border border-blue-200">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mx-auto text-blue-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-gray-700 font-medium">No user found with username <span className="text-blue-900">"{searchUser}"</span></p>
                  <p className="text-sm text-gray-500 mt-1">Try searching with a different username</p>
                </div>
              )
            )}
          </div>
          
          {/* Selected Users */}
          {selectedUsers.length > 0 && (
            <div key="selected-users-container" className="mt-6">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-gray-700">Selected Members ({selectedUsers.length})</h4>
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {selectedUsers.map(user => (
                  <div 
                    key={`selected-user-${user.id}`} 
                    className="flex items-center justify-between p-2 bg-white rounded-lg border border-blue-100"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        {user.username[0]?.toUpperCase()}
                      </div>
                      <span className="font-medium text-gray-800">{user.username}</span>
                    </div>
                    <button 
                      type="button"
                      onClick={() => removeUser(user.id)}
                      className="text-red-500 hover:text-red-700 p-1"
                      aria-label={`Remove ${user.username}`}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-blue-100">
            <button 
              onClick={onClose} 
              type="button"
              className="px-5 py-2 text-sm rounded-lg bg-gray-200 hover:bg-gray-300 font-medium"
            >
              Cancel
            </button>
            <button
              type="button"
              disabled={submitting || !name.trim() || selectedIds.length === 0}
              onClick={handleCreate}
              className="px-5 py-2 text-sm rounded-lg bg-blue-900 text-white hover:bg-blue-700 disabled:opacity-50 font-medium"
            >
              {submitting ? 'Creating...' : 'Create'}
            </button>
          </div>
        </div>
      </div>
    </Dialog>
  );
}