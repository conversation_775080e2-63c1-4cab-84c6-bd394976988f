from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel
from typing import Optional, List, Dict, Any, Union
from ..auth.dependencies import get_current_user
from ..models.user import User
from ..models.user_space import UserSpace
from ..models.knowledge_base import KnowledgeDocument
from ..agents import agent_instances
from ..agents.image_agent import image_vision_agent
from ..models.feedback import ExpertChatInteraction
from ..services.space_permissions import require_space_access
from ..services.minio_service import minio_service
from ..database import get_db
from ..services.redis_service import invalidate_admin_cache, redis_service
from ..services.redis_logger import cached_get, cached_set, cached_delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, update, and_, delete
import asyncio

# hashlib removed - no more query hashing
import logging
import uuid
import json
from datetime import datetime
from .. import config

router = APIRouter(
    prefix="/agents",
    tags=["agents"],
    responses={404: {"description": "Not found"}},
)

logger = logging.getLogger(__name__)

# Global dictionary to track active query sessions and their cancellation tokens
active_query_sessions: Dict[str, asyncio.Event] = {}

# Helper functions for agent chat history management
async def store_agent_chat_history(
    user_id: int,
    agent_type: str,
    user_message: str,
    agent_response: str,
    response_id: str,
    chat_history: list,
    full_response_data: dict = None,
    chat_session_id: str = "default"
):
    """Store agent chat history in Redis for session persistence"""
    
    cache_key = f"{config.CACHE_PREFIX_CHAT}:agent_history:{user_id}:{chat_session_id}:{agent_type}"
    
    # Get existing history
    existing_history = await cached_get(cache_key)
    if existing_history is None:
        existing_history = []
    
    # Add new user message
    existing_history.append({
        "role": "user",
        "content": user_message,
        "timestamp": str(datetime.utcnow())
    })
    
    # Add new agent response with complete data
    assistant_message = {
        "role": "assistant", 
        "content": agent_response,
        "response_id": response_id,
        "timestamp": str(datetime.utcnow())
    }
    
    # Add complete response data if available
    if full_response_data:
        assistant_message.update({
            "sources": full_response_data.get("sources", []),
            "web_search_enabled": full_response_data.get("web_search_enabled", False),
            "document_search_enabled": full_response_data.get("document_search_enabled", False),
            "deep_search_enabled": full_response_data.get("deep_search_enabled", False),
            "reasoning": full_response_data.get("reasoning", []),
            "progress_messages": full_response_data.get("progress_messages", []),
            "cached": full_response_data.get("cached", False),
            "cache_confidence": full_response_data.get("cache_confidence"),
            "agent_name": full_response_data.get("agent_name", ""),
            "used_rag": full_response_data.get("used_rag"),
            "processing_stats": full_response_data.get("processing_stats")
        })
    
    existing_history.append(assistant_message)
    
    # Keep only last 100 messages to prevent memory bloat
    if len(existing_history) > 100:
        existing_history = existing_history[-100:]
    
    # Store back in Redis with extended TTL for chat history
    await cached_set(cache_key, existing_history, ttl=config.CACHE_TTL_LONG)
    logger.info(f"Stored agent chat history for user {user_id}, agent {agent_type}")

async def get_agent_chat_history(user_id: int, agent_type: str, chat_session_id: str = "default", db: AsyncSession = None) -> list:
    """Retrieve agent chat history, preferring Redis and falling back to DB."""
    # Try Redis first
    try:
        cache_key = f"{config.CACHE_PREFIX_CHAT}:agent_history:{user_id}:{chat_session_id}:{agent_type}"
        cached_history = await cached_get(cache_key)
        if cached_history is not None:
            return cached_history
    except Exception as e:
        logger.debug(f"Agent history cache miss or error: {e}")

    # Fallback to DB
    if db is None:
        
        return []

    try:
        result = await db.execute(
            select(ExpertChatInteraction).where(
                ExpertChatInteraction.user_id == user_id,
                ExpertChatInteraction.agent_type == agent_type,
                ExpertChatInteraction.session_id == chat_session_id
            ).order_by(ExpertChatInteraction.created_at.asc())
        )
        interactions = result.scalars().all()

        chat_history = []
        for interaction in interactions:
            chat_history.append({
                "role": "user",
                "content": interaction.query,
                "timestamp": interaction.created_at.isoformat() if interaction.created_at else None
            })

            # Include sources and metadata when retrieving from database
            assistant_message = {
                "role": "assistant",
                "content": interaction.response,
                "response_id": interaction.response_id,
                "timestamp": interaction.created_at.isoformat() if interaction.created_at else None
            }

            # Add sources if available
            if interaction.sources:
                assistant_message["sources"] = interaction.sources
                # You can add other metadata here if stored in DB later

            chat_history.append(assistant_message)
        
        return chat_history

    except Exception as e:
        logger.error(f"Error loading chat history from database: {e}")
        return []

class ChatMessage(BaseModel):
    role: str
    content: str
    sources: Optional[List[Any]] = None

class ChatHistory(BaseModel):
    messages: List[ChatMessage]


class QueryRequest(BaseModel):
    agent_type: str
    query: str
    chat_history: Optional[ChatHistory] = None
    use_web_search: bool = False
    use_document_search: bool = False
    use_deep_search: bool = False
    allowed_files: Optional[List[str]] = None
    chat_session_id: Optional[str] = "default"
    query_session_id: Optional[str] = None  # For tracking cancellation
    space_id: Optional[int] = None  # Specify which space to query for document search

class CancelRequest(BaseModel):
    query_session_id: str

class QueryResponse(BaseModel):
    agent_name: str
    query: str
    answer: str

    web_search_enabled: bool = False
    document_search_enabled: bool = False
    deep_search_enabled: bool = False
    sources: Optional[List] = None    
    selected_files: Optional[List[str]] = None
    used_rag: Optional[bool] = None
    processing_stats: Optional[Dict[str, Any]] = None
    reasoning: Optional[List[Dict[str, Any]]] = None
    progress_messages: Optional[List[str]] = None
    error: Optional[str] = None
    
    # Fields for feedback tracking (caching removed)
    response_id: Optional[str] = None  # UUID for tracking feedback
    cached: Optional[bool] = False  # Always False - caching removed
    cache_confidence: Optional[float] = None  # Always None - caching removed
    query_session_id: Optional[str] = None  # For tracking cancellation
    suggested_followups: Optional[List[str]] = None
    # Internal metadata describing context usage/token stats; echoed for UI
    context_meta: Optional[Dict[str, Any]] = None

async def _handle_image_query(
    query: str,
    space: UserSpace,
    space_id: int,
    allowed_files: Optional[List[str]],
    history: Optional[List[Dict]],
    current_user: User,
    db: AsyncSession,
    cancellation_event: asyncio.Event,
    agent_type: str
) -> Dict[str, Any]:
    # Delegate to the ImageVisionAgent to keep the router thin
    return await image_vision_agent.analyze_images(
        query=query,
        space=space,
        space_id=space_id,
        allowed_files=allowed_files,
        history=history,
        current_user=current_user,
        db=db,
        cancellation_event=cancellation_event,
        agent_type=agent_type,
        agent_name=agent_instances[agent_type].name,
    )


@router.get("/", summary="Get available agents")
async def get_agents(current_user: User = Depends(get_current_user)):
    """Return the single universal agent that is now available to all users."""
    available_agents = [
        {"id": "general", "name": agent_instances["general"].name},
    ]
    return {"agents": available_agents}


@router.post("/query", response_model=QueryResponse, summary="Query an agent")
async def query_agent(request: QueryRequest, current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)):
    """
    Query a specific agent with a question.
    
    - **agent_type**: Type of agent to query (hr, it, finance)
    - **query**: The question or request for the agent
    - **chat_history**: Optional chat history for context
    - **use_web_search**: Whether to enable web search capability (default: false)
    - **use_document_search**: Whether to enable document search capability (default: false)
    - **use_deep_search**: Whether to enable deep research capability (default: false)
    - **allowed_files**: Optional list of specific files to search in (only with document search)
    """
    # Validate conflicting search settings
    search_count = sum([request.use_web_search, request.use_document_search, request.use_deep_search])
    if search_count > 1:
        raise HTTPException(
            status_code=400, 
            detail="Only one search type (web_search, document_search, or deep_search) can be enabled at a time"
        )
    
    # Validate agent type (support legacy identifiers by mapping them internally)
    if request.agent_type not in agent_instances:
        raise HTTPException(
            status_code=400,
            detail=f"Agent type '{request.agent_type}' not recognised. Use 'general'.",
        )

    # Get the requested agent
    agent = agent_instances[request.agent_type]
    
    # Determine if config needs updating
    config_changed = (
        agent.use_web_search != request.use_web_search or 
        agent.use_document_search != request.use_document_search or
        agent.use_deep_search != request.use_deep_search or
        getattr(agent, 'allowed_files', None) != request.allowed_files
    )
    
    if config_changed:
        # Update agent configuration including file filtering
        agent.update_config(
            use_web_search=request.use_web_search, 
            use_document_search=request.use_document_search,
            use_deep_search=request.use_deep_search,
            allowed_files=request.allowed_files
        )
    
    # Process the query
    try:
        logger.info(f"Processing query: {request.query[:50]}...")
        try:
            hist_len = len(request.chat_history.messages) if request.chat_history and request.chat_history.messages else 0
        except Exception:
            hist_len = 0
        logger.info(f"[ChatContext] Incoming chat_history messages={hist_len}, chat_session_id={request.chat_session_id}")
        
        # Create and track cancellation token for this query
        query_session_id = request.query_session_id or str(uuid.uuid4())
        cancellation_event = asyncio.Event()
        active_query_sessions[query_session_id] = cancellation_event
        
        logger.info(f"Created query session: {query_session_id}")
        logger.info(f"Active sessions after creation: {list(active_query_sessions.keys())}")
        
        try:
            # Get the requested agent
            agent = agent_instances[request.agent_type]
            
            # Process the query
            history = None
            if request.chat_history:
                history = []
                for msg in request.chat_history.messages:
                    entry = {"role": msg.role, "content": msg.content}
                    if msg.sources:
                        entry["sources"] = msg.sources
                    history.append(entry)
            
            # Handle space-specific queries
            collection_name = None
            if request.use_document_search and request.space_id:
                # Validate space access
                space = await require_space_access(current_user, request.space_id, db, "query")
                
                # Branch based on playground type
                if space.is_image_playground():
                    # Handle image query (bypass RAG)
                    logger.info(f"Detected image space {request.space_id}, using image query handler")
                    response = await _handle_image_query(
                        query=request.query,
                        space=space,
                        space_id=request.space_id,
                        allowed_files=request.allowed_files,
                        history=history,
                        current_user=current_user,
                        db=db,
                        cancellation_event=cancellation_event,
                        agent_type=request.agent_type
                    )
                else:
                    # Document space - use existing RAG flow
                    collection_name = space.collection_name
                    logger.info(f"Using collection '{collection_name}' for document space {request.space_id}")
                    
                    # Retrieve previous summary (if any) to support progressive chaining
                    prev_summary_text = None
                    try:
                        from ..services.redis_logger import get_context_snapshot_meta
                        prev_meta = await get_context_snapshot_meta(
                            current_user.id,
                            request.agent_type,
                            request.chat_session_id
                        )
                        if prev_meta and prev_meta.get('context_type') == 'summary':
                            prev_summary_text = prev_meta.get('summary_text')
                    except Exception as _e:
                        logger.debug(f"Skipping prev summary lookup: {_e}")

                    # Get response from agent - always call the model
                    response = await agent.process_query(
                        request.query,
                        history,
                        request.allowed_files,
                        username=current_user.username,
                        db=db,
                        cancellation_event=cancellation_event,  # Pass cancellation token
                        collection_name=collection_name,  # Pass specific collection for shared spaces
                        prev_summary_text=prev_summary_text,
                    )
            else:
                # No space specified or not using document search
                # Retrieve previous summary (if any) to support progressive chaining
                prev_summary_text = None
                try:
                    from ..services.redis_logger import get_context_snapshot_meta
                    prev_meta = await get_context_snapshot_meta(
                        current_user.id,
                        request.agent_type,
                        request.chat_session_id
                    )
                    if prev_meta and prev_meta.get('context_type') == 'summary':
                        prev_summary_text = prev_meta.get('summary_text')
                except Exception as _e:
                    logger.debug(f"Skipping prev summary lookup: {_e}")

                # Get response from agent - always call the model (no caching)
                response = await agent.process_query(
                    request.query,
                    history,
                    request.allowed_files,
                    username=current_user.username,
                    db=db,
                    cancellation_event=cancellation_event,  # Pass cancellation token
                    collection_name=collection_name,  # Pass specific collection for shared spaces
                    prev_summary_text=prev_summary_text,
                )
        finally:
            # Clean up the session tracking
            logger.info(f"Cleaning up query session: {query_session_id}")
            # Set cancellation event one more time to ensure all processes stop
            if query_session_id in active_query_sessions:
                try:
                    active_query_sessions[query_session_id].set()
                except:
                    pass
            active_query_sessions.pop(query_session_id, None)
            logger.info(f"Active sessions after cleanup: {list(active_query_sessions.keys())}")
        
        # Generate unique response ID
        response_id = str(uuid.uuid4())
        
        # Store the interaction in the database
        interaction = ExpertChatInteraction(
            user_id=current_user.id,
            agent_type=request.agent_type,
            session_id=request.chat_session_id or 'default',
            query=request.query,
            response=response["answer"],
            sources=response.get("sources", []),  # Store sources permanently
            response_id=response_id
        )
        
        db.add(interaction)
        await db.commit()
        await db.refresh(interaction)
        
        # Store chat history in Redis for persistence across sessions
        try:
            await store_agent_chat_history(
                user_id=current_user.id,
                agent_type=request.agent_type,
                user_message=request.query,
                agent_response=response["answer"],
                response_id=response_id,
                chat_history=history or [],
                full_response_data=response,  # Pass the complete response data
                chat_session_id=request.chat_session_id or 'default'
            )
        except Exception as e:
            logger.warning(f"Failed to store chat history in Redis: {e}")
        
        # Invalidate admin/dashboard caches so AI query counts reflect immediately
        try:
            await invalidate_admin_cache()
            
            # Instead of invalidating the sessions cache, we can update it directly
            # This avoids unnecessary database queries on the next request
            sessions_cache_key = f"{config.CACHE_PREFIX_CHAT}:sessions:{current_user.id}:{request.agent_type}"
            
            # Get existing sessions from cache
            existing_sessions = await cached_get(sessions_cache_key)
            
            # If we have existing sessions in cache, update them
            if existing_sessions is not None:
                # Check if the current session is already in the list
                session_exists = False
                for session in existing_sessions:
                    if session.get("session_id") == request.chat_session_id:
                        # Update existing session
                        session["message_count"] += 2  # User message + AI response
                        session["last_updated"] = datetime.utcnow().isoformat()
                        if session.get("title") == "New Chat" and request.query:
                            # Update title for new sessions
                            session["title"] = request.query[:50]
                        session_exists = True
                        break
                
                # If session doesn't exist, add it
                if not session_exists and request.chat_session_id:
                    existing_sessions.append({
                        "session_id": request.chat_session_id,
                        "title": request.query[:50] if request.query else "New Chat",
                        "message_count": 2,  # User message + AI response
                        "last_updated": datetime.utcnow().isoformat()
                    })
                
                # Sort sessions
                def sort_key(s):
                    is_default = s.get('session_id') == 'default'
                    last_updated = s.get('last_updated', '') or ''
                    return (not is_default, last_updated)
                
                existing_sessions.sort(key=sort_key, reverse=True)
                
                # Update cache with new session data
                await cached_set(sessions_cache_key, existing_sessions, ttl=config.CACHE_TTL_LONG)
                logger.info(f"Updated chat sessions cache for user {current_user.id}, agent {request.agent_type}")
            else:
                # If no cache exists, just invalidate so it will be rebuilt on next request
                await redis_service.delete(sessions_cache_key)
                logger.info(f"Invalidated chat sessions cache for user {current_user.id}, agent {request.agent_type}")
        except Exception as e:
            logger.warning(f"Cache update/invalidation error: {e}")
        
        logger.info(f"Stored expert chat interaction: {interaction.id}")
        
        # Store context snapshot metadata for chat session tracking and analysis
        try:
            context_meta = response.get("context_meta")
            if context_meta:
                # Determine the search mode based on request parameters
                if request.use_web_search:
                    mode = 'web'
                elif request.use_document_search:
                    mode = 'document'
                elif request.use_deep_search:
                    mode = 'deep'
                else:
                    mode = 'basic'
                
                from ..models.feedback import AgentChatContextSnapshot, AgentChatContextInteraction
                from sqlalchemy.exc import IntegrityError
                
                # Attempt to link summary contexts to their parent context for hierarchical tracking
                parent_context_id = None
                try:
                    if context_meta.get('context_type') == 'summary':
                        from ..services.redis_logger import get_context_snapshot_meta
                        # Retrieve previous context metadata from Redis cache
                        prev_meta = await get_context_snapshot_meta(
                            current_user.id,
                            request.agent_type,
                            request.chat_session_id
                        )
                        # Link to parent if previous context was also a summary
                        if prev_meta and prev_meta.get('context_type') == 'summary':
                            parent_context_id = prev_meta.get('snapshot_id')
                except Exception as _e:
                    # Parent discovery is optional - continue without linking if it fails
                    logger.debug(f"Context parent discovery skipped: {_e}")

                # Create context snapshot record with token usage statistics
                snapshot = AgentChatContextSnapshot(
                    interaction_id=interaction.id,
                    user_id=current_user.id,
                    agent_type=request.agent_type,
                    chat_session_id=request.chat_session_id,
                    mode=mode,
                    context_type=context_meta['context_type'],
                    summary_text=context_meta.get('summary_text'),
                    parent_context_id=parent_context_id,
                    model_window_tokens=context_meta['token_stats']['model_window_tokens'],
                    reserve_for_answer=context_meta['token_stats']['reserve_for_answer'],
                    instruction_tokens=context_meta['token_stats']['instruction_tokens'],
                    input_tokens_used=context_meta['token_stats']['input_tokens_used'],
                    instruction_prefix=context_meta.get('instruction_prefix')
                )
                
                # Handle potential duplicate snapshot creation with integrity constraint
                try:
                    db.add(snapshot)
                    await db.flush()  # Get the snapshot ID without committing
                except IntegrityError:
                    # If snapshot already exists for this interaction, retrieve it
                    await db.rollback()
                    result = await db.execute(
                        select(AgentChatContextSnapshot).where(
                            AgentChatContextSnapshot.interaction_id == interaction.id
                        )
                    )
                    snapshot = result.scalars().first()
                    if not snapshot:
                        raise
                
                # Track which prior interactions were included in the context window
                raw_pairs_count = context_meta.get('raw_pairs_used_count', 0)
                included_interaction_ids: list = []
                if raw_pairs_count > 0:
                    # Query prior interactions in the same chat session
                    result = await db.execute(
                        select(ExpertChatInteraction).where(
                            ExpertChatInteraction.user_id == current_user.id,
                            ExpertChatInteraction.agent_type == request.agent_type,
                            ExpertChatInteraction.session_id == request.chat_session_id,
                            ExpertChatInteraction.id != interaction.id  # Exclude current interaction
                        ).order_by(ExpertChatInteraction.created_at.asc())
                    )
                    prior_interactions = result.scalars().all()
                    
                    # Select the most recent interactions that fit in the context window
                    included_count = raw_pairs_count
                    included_interactions = prior_interactions[-included_count:] if len(prior_interactions) >= included_count else prior_interactions
                    included_interaction_ids = [pi.id for pi in included_interactions]
                    
                    # Clear any existing context interaction mappings for this snapshot
                    await db.execute(
                        delete(AgentChatContextInteraction).where(
                            AgentChatContextInteraction.context_id == snapshot.id
                        )
                    )
                    
                    # Create new mappings with position tracking
                    for position, prior_interaction in enumerate(included_interactions):
                        mapping = AgentChatContextInteraction(
                            context_id=snapshot.id,
                            interaction_id=prior_interaction.id,
                            position=position
                        )
                        db.add(mapping)
                
                # Commit all context snapshot and interaction mapping changes
                await db.commit()
                
                # Log context snapshot creation with appropriate detail level
                if parent_context_id:
                    logger.info(f"Stored context snapshot {snapshot.id} (summary linked to parent {parent_context_id}) for interaction {interaction.id}")
                else:
                    logger.info(f"Stored context snapshot {snapshot.id} for interaction {interaction.id} (type={context_meta['context_type']}, mode={mode})")
                
                # Cache context metadata in Redis for quick access by subsequent requests
                from ..services.redis_logger import store_context_snapshot_meta
                redis_payload = {
                    'snapshot_id': snapshot.id,
                    'context_type': context_meta['context_type'],
                    'included_interaction_ids': included_interaction_ids,
                    'summary_text': context_meta.get('summary_text'),
                    'token_stats': context_meta['token_stats'],
                    'parent_context_id': parent_context_id
                }
                await store_context_snapshot_meta(
                    current_user.id,
                    request.agent_type,
                    request.chat_session_id,
                    redis_payload,
                    config.CACHE_TTL_LONG
                )
        except Exception as e:
            # Context snapshot persistence is non-critical - log error but don't fail the request
            logger.error(f"Failed to persist context snapshot: {e}")
            import traceback
            traceback.print_exc()
            
        result = QueryResponse(
            agent_name=response["agent_name"],
            query=response["query"],
            answer=response["answer"],
            web_search_enabled=response["web_search_enabled"],
            document_search_enabled=response["document_search_enabled"],
            deep_search_enabled=response["deep_search_enabled"],
            sources=response.get("sources", []),
            selected_files=response.get("selected_files"),
            used_rag=response.get("used_rag"),
            processing_stats=response.get("processing_stats"),
            reasoning=response.get("reasoning", []),
            progress_messages=response.get("progress_messages", []),
            error=response.get("error"),
            response_id=response_id,
            cached=False,
            cache_confidence=None,
            query_session_id=query_session_id,
            context_meta=context_meta if 'context_meta' in response else None
        )
        
        return result
    except Exception as e:
        logger.error(f"Error processing agent query: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/cancel", summary="Cancel an active query")
async def cancel_query(request: CancelRequest, current_user: User = Depends(get_current_user)):
    """
    Cancel an active query by its session ID.
    
    - **query_session_id**: The session ID of the query to cancel
    """
    query_session_id = request.query_session_id
    
    logger.info(f"Cancel request received for query_session_id: {query_session_id}")
    logger.info(f"Active query sessions: {list(active_query_sessions.keys())}")
    
    if query_session_id not in active_query_sessions:
        raise HTTPException(
            status_code=404,
            detail=f"Query session '{query_session_id}' not found or already completed"
        )
    
    # Signal cancellation
    cancellation_event = active_query_sessions[query_session_id]
    cancellation_event.set()
    
    logger.info(f"Query session '{query_session_id}' has been cancelled by user {current_user.username}")
    
    return {"message": f"Query session '{query_session_id}' has been cancelled successfully"}

@router.get("/chat-history/{agent_type}")
async def get_chat_history(
    agent_type: str,
    current_user: User = Depends(get_current_user),
    chat_session_id: str = "default",
    db: AsyncSession = Depends(get_db)
):
    """Get chat history for a specific agent and user"""
    try:
        history = await get_agent_chat_history(current_user.id, agent_type, chat_session_id, db)
        return {"agent_type": agent_type, "chat_session_id": chat_session_id, "messages": history}
    except Exception as e:
        logger.error(f"Error retrieving chat history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving chat history: {str(e)}"
        )


@router.get("/chat-sessions/{agent_type}")
async def get_chat_sessions(
    agent_type: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    force_refresh: bool = Query(False)
):
    """Get all chat sessions for a specific agent and user (cached in Redis)."""
    try:
        cache_key = f"{config.CACHE_PREFIX_CHAT}:sessions:{current_user.id}:{agent_type}"
        
        # Only skip cache if explicitly requested with force_refresh
        # This reduces unnecessary database queries
        if not force_refresh:
            cached = await cached_get(cache_key)
            if cached is not None:
                logger.info("Chat sessions cache hit for user %s agent %s", current_user.id, agent_type)
                return {"agent_type": agent_type, "sessions": cached}

        # DB query (same as before)
        logger.info(f"Fetching chat sessions from DB for user {current_user.id}, agent {agent_type}")
        result = await db.execute(
            select(
                ExpertChatInteraction.session_id,
                func.count(ExpertChatInteraction.id).label('message_count'),
                func.max(ExpertChatInteraction.created_at).label('last_updated')
            ).where(
                ExpertChatInteraction.user_id == current_user.id,
                ExpertChatInteraction.agent_type == agent_type
            ).group_by(ExpertChatInteraction.session_id)
        )
        session_query = result.all()
        
        logger.info(f"Found {len(session_query)} chat sessions in DB")
        
        # Step 1: Batch fetch all titles from Redis at once
        session_ids = [session_data[0] for session_data in session_query]
        title_keys = [
            f"{config.CACHE_PREFIX_CHAT}:session_title:{current_user.id}:{agent_type}:{session_id}"
            for session_id in session_ids
        ]
        
        # Bulk get from Redis (single operation instead of 53)
        cached_titles = await redis_service.mget(title_keys) if title_keys else []
        
        # Create mapping of session_id -> cached_title
        title_cache_map = {}
        cache_hits = 0
        cache_misses = 0
        sessions_needing_titles = []
        
        for idx, session_data in enumerate(session_query):
            session_id = session_data[0]
            cached_title = cached_titles[idx] if idx < len(cached_titles) else None
            
            if cached_title:
                title_cache_map[session_id] = cached_title
                cache_hits += 1
            else:
                sessions_needing_titles.append(session_id)
                cache_misses += 1
        
        # Step 2: Batch fetch all missing titles from DB in a single query
        if sessions_needing_titles:
            # Use a subquery to get first message for each session efficiently
            from sqlalchemy import literal_column
            subquery_stmt = select(
                ExpertChatInteraction.session_id,
                ExpertChatInteraction.query,
                ExpertChatInteraction.created_at,
                func.row_number().over(
                    partition_by=ExpertChatInteraction.session_id,
                    order_by=ExpertChatInteraction.created_at.asc()
                ).label('rn')
            ).where(
                ExpertChatInteraction.user_id == current_user.id,
                ExpertChatInteraction.agent_type == agent_type,
                ExpertChatInteraction.session_id.in_(sessions_needing_titles)
            ).subquery()
            
            result = await db.execute(
                select(
                    subquery_stmt.c.session_id,
                    subquery_stmt.c.query
                ).where(
                    subquery_stmt.c.rn == 1
                )
            )
            first_messages = result.all()
            
            # Store in cache and map
            titles_to_cache = []
            for session_id, query in first_messages:
                title = query[:50] if query else "New Chat"
                title_cache_map[session_id] = title
                title_key = f"{config.CACHE_PREFIX_CHAT}:session_title:{current_user.id}:{agent_type}:{session_id}"
                titles_to_cache.append((title_key, title))
            
            # Handle sessions with no messages
            for session_id in sessions_needing_titles:
                if session_id not in title_cache_map:
                    title_cache_map[session_id] = "New Chat"
                    title_key = f"{config.CACHE_PREFIX_CHAT}:session_title:{current_user.id}:{agent_type}:{session_id}"
                    titles_to_cache.append((title_key, "New Chat"))
            
            # Batch cache all new titles (single pipeline operation)
            if titles_to_cache:
                for title_key, title in titles_to_cache:
                    await redis_service.set(title_key, title, ttl=config.CACHE_TTL_LONG)
        
        # Step 3: Build final sessions list
        sessions = []
        for session_data in session_query:
            session_id, message_count, last_updated = session_data
            title = title_cache_map.get(session_id, "New Chat")
            
            sessions.append({
                "session_id": session_id,
                "title": title,
                "message_count": message_count * 2,
                "last_updated": last_updated.isoformat() if last_updated else None
            })
        
        # Log summary of cache performance
        logger.info(f"Session titles cache summary: {cache_hits} hits, {cache_misses} misses ({len(session_query)} total)")
        def sort_key(s):
            # First sort by whether it's the default session (False comes before True when not reversed)
            # Then sort by last_updated timestamp (most recent first)
            is_default = s.get('session_id') == 'default'
            last_updated = s.get('last_updated', '') or ''  # Ensure we don't get None
            return (not is_default, last_updated)
        
        # Sort with newest sessions first (except default session which should be last)
        sessions.sort(key=sort_key, reverse=True)
        # Cache result with longer TTL to reduce database queries
        logger.info(f"Returning {len(sessions)} chat sessions for user {current_user.id}, agent {agent_type}")
        await cached_set(cache_key, sessions, ttl=config.CACHE_TTL_LONG, log_operation=False)
        return {"agent_type": agent_type, "sessions": sessions}
    except Exception as e:
        logger.error("Error retrieving chat sessions: %s", e)
        raise HTTPException(status_code=500, detail=f"Error retrieving chat sessions: {e}")

@router.put("/chat-sessions/{agent_type}/{session_id}")
async def rename_chat_session(
    agent_type: str,
    session_id: str,
    request: dict,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Rename a chat session for a specific agent and user.
    Store custom titles in Redis since the database doesn't have a title column.
    """
    try:
        new_title = request.get("title", "").strip()
        if not new_title:
            raise HTTPException(status_code=400, detail="Title cannot be empty")

        # Check if the session exists
        result = await db.execute(
            select(ExpertChatInteraction.id).where(
                and_(
                    ExpertChatInteraction.user_id == current_user.id,
                    ExpertChatInteraction.agent_type == agent_type,
                    ExpertChatInteraction.session_id == session_id
                )
            ).limit(1)
        )
        session_exists = result.first() is not None

        if not session_exists:
            raise HTTPException(status_code=404, detail="Session not found")

        # Store the custom title in Redis
        title_key = f"{config.CACHE_PREFIX_CHAT}:session_title:{current_user.id}:{agent_type}:{session_id}"
        await cached_set(title_key, new_title, ttl=config.CACHE_TTL_LONG)

        # Invalidate the chat sessions cache to force refresh
        cache_key = f"{config.CACHE_PREFIX_CHAT}:chat_sessions:{current_user.id}:{agent_type}"
        await cached_delete(cache_key)

        logger.info(f"Renamed chat session {session_id} for user {current_user.id}, agent {agent_type} to '{new_title}'")
        return {"message": "Session renamed successfully", "title": new_title}

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error renaming chat session: %s", e)
        raise HTTPException(status_code=500, detail=f"Error renaming chat session: {e}")

class FollowupRequest(BaseModel):
    chat_history: ChatHistory
    latest_query: str
    latest_answer: str
    latest_sources: Optional[List[Dict[str, Any]]] = None  # Sources from the latest response
    response_id: str  # To identify which interaction to update in DB

@router.post("/followups", response_model=List[str], summary="Generate follow-up suggestions")
async def generate_followups(request: FollowupRequest, current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)):
    """
    Generate follow-up questions based on chat history, latest query, and latest answer.
    Also stores the generated suggestions in the database.
    """
    # Build chat history (without the latest exchange to avoid duplication)
    chat_history = []
    for msg in request.chat_history.messages:
        entry = {"role": msg.role, "content": msg.content}
        if msg.sources:
            entry["sources"] = msg.sources
        chat_history.append(entry)
    
    # Create session summary for context
    logger.info("Router: Creating session summary for follow-up generation")
    # Include latest exchange for complete context
    full_history_for_summary = chat_history + [
        {"role": "user", "content": request.latest_query},
        {"role": "assistant", "content": request.latest_answer}
    ]
    agent = agent_instances.get("general")
    session_summary = agent.create_chat_summary(full_history_for_summary) if agent else None
    if session_summary and session_summary.get("type") == "summary":
        summary_text = session_summary.get("content")
        logger.info(f"Router: Generated session summary (length: {len(summary_text) if summary_text else 0})")
    else:
        summary_text = None
        logger.warning(f"Router: Failed to generate session summary (type: {session_summary.get('type') if session_summary else 'None'})")
    
    try:
        sugg_agent = agent_instances.get("followup")
        if not sugg_agent:
            logger.error("FollowUp agent not found in agent_instances")
            suggestions = []
        else:
            # Use sources from the latest response if provided, otherwise try to extract from chat history
            latest_sources = request.latest_sources or []
            if not latest_sources:
                try:
                    if chat_history:
                        last_assistant_message = next((msg for msg in reversed(chat_history) if msg.get("role") == "assistant"), None)
                        if last_assistant_message and isinstance(last_assistant_message, dict):
                            candidate_sources = last_assistant_message.get("sources")
                            if isinstance(candidate_sources, list):
                                latest_sources = candidate_sources
                except Exception as e:
                    logger.warning(f"Router: Failed to extract sources for follow-up prompt: {e}")

            suggestions = sugg_agent.generate(
                chat_history,
                request.latest_query,
                request.latest_answer,
                session_summary=summary_text,
                latest_sources=latest_sources
            )
            logger.info(f"Generated {len(suggestions)} follow-up suggestions using reused agent instance")
    except Exception as e:
        logger.error(f"Failed to generate follow-up suggestions: {e}")
        suggestions = []  # Return empty list on failure

    # Store suggestions in database
    try:
        result = await db.execute(
            select(ExpertChatInteraction).where(
                ExpertChatInteraction.response_id == request.response_id,
                ExpertChatInteraction.user_id == current_user.id
            )
        )
        interaction = result.scalars().first()
        
        if interaction:
            interaction.suggested_followups = suggestions
            await db.commit()
            logger.info(f"Stored {len(suggestions)} follow-up suggestions for response_id: {request.response_id}")
        else:
            logger.warning(f"No interaction found for response_id: {request.response_id}")
    except Exception as e:
        logger.error(f"Failed to store follow-up suggestions in database: {e}")
        # Don't fail the request if database update fails
    
    return suggestions


@router.get("/context-meta")
async def get_context_meta(
    agent_type: str = Query(..., description="Agent type"),
    chat_session_id: str = Query(..., description="Chat session ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Retrieve the latest context metadata for a chat session to hydrate the UI.
    Returns token stats and context information from Redis cache or DB fallback.
    """
    try:
        # Try Redis first for fast retrieval
        from ..services.redis_logger import get_context_snapshot_meta
        
        context_meta = await get_context_snapshot_meta(
            current_user.id,
            agent_type,
            chat_session_id
        )
        
        if context_meta:
            # Normalize legacy payloads that may miss new token fields
            try:
                ts = context_meta.get('token_stats', {}) or {}
                # Compute input_budget if missing
                if 'input_budget' not in ts and 'model_window_tokens' in ts and 'reserve_for_answer' in ts:
                    ts['input_budget'] = max(0, int(ts['model_window_tokens']) - int(ts['reserve_for_answer']))
                # Ensure question_tokens key exists
                if 'question_tokens' not in ts:
                    ts['question_tokens'] = 0
                # Compute context_tokens_used if missing
                if 'context_tokens_used' not in ts and 'input_tokens_used' in ts and 'instruction_tokens' in ts:
                    used = int(ts.get('input_tokens_used', 0))
                    instr = int(ts.get('instruction_tokens', 0))
                    q = int(ts.get('question_tokens', 0))
                    ts['context_tokens_used'] = max(0, used - instr - q)
                context_meta['token_stats'] = ts
                # Backfill normalized payload into Redis so subsequent reads are correct
                from ..services.redis_logger import store_context_snapshot_meta
                await store_context_snapshot_meta(
                    current_user.id,
                    agent_type,
                    chat_session_id,
                    context_meta,
                    config.CACHE_TTL_LONG
                )
            except Exception as _e:
                logger.debug(f"Context meta normalization skipped: {_e}")

            logger.info(f"Context meta retrieved from Redis for user={current_user.id}, agent={agent_type}, session={chat_session_id}")
            return {"context_meta": context_meta}
        
        # Fallback to database if Redis miss
        from ..models.feedback import AgentChatContextSnapshot
        
        result = await db.execute(
            select(AgentChatContextSnapshot).where(
                AgentChatContextSnapshot.user_id == current_user.id,
                AgentChatContextSnapshot.agent_type == agent_type,
                AgentChatContextSnapshot.chat_session_id == chat_session_id
            ).order_by(AgentChatContextSnapshot.created_at.desc())
        )
        latest_snapshot = result.scalars().first()
        
        if latest_snapshot:
            # Build context_meta from DB snapshot
            context_meta_from_db = {
                'snapshot_id': latest_snapshot.id,
                'context_type': latest_snapshot.context_type,
                'summary_text': latest_snapshot.summary_text,
                'token_stats': {
                    'model_window_tokens': latest_snapshot.model_window_tokens,
                    'reserve_for_answer': latest_snapshot.reserve_for_answer,
                    'instruction_tokens': latest_snapshot.instruction_tokens,
                    'question_tokens': 0,  # Not stored in old snapshots; will be 0 for legacy
                    'input_budget': latest_snapshot.model_window_tokens - latest_snapshot.reserve_for_answer,
                    'input_tokens_used': latest_snapshot.input_tokens_used,
                    'context_tokens_used': max(0, latest_snapshot.input_tokens_used - latest_snapshot.instruction_tokens)
                },
                'parent_context_id': latest_snapshot.parent_context_id
            }
            
            # Optionally cache it back to Redis for next time
            from ..services.redis_logger import store_context_snapshot_meta
            await store_context_snapshot_meta(
                current_user.id,
                agent_type,
                chat_session_id,
                context_meta_from_db,
                config.CACHE_TTL_LONG
            )
            
            logger.info(f"Context meta retrieved from DB and cached for user={current_user.id}, agent={agent_type}, session={chat_session_id}")
            return {"context_meta": context_meta_from_db}
        
        # No snapshot found
        logger.info(f"No context meta found for user={current_user.id}, agent={agent_type}, session={chat_session_id}")
        return {"context_meta": None}
        
    except Exception as e:
        logger.error(f"Error retrieving context meta: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve context metadata") 