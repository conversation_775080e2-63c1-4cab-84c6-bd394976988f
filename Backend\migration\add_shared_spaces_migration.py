#!/usr/bin/env python3
"""
Migration: Add Shared Spaces Support

This migration adds:
1. space_type field to user_spaces table
2. space_memberships table for managing shared space members
3. Indexes for performance
"""

import logging
import os
import sys
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the parent directory to the path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config import DATABASE_URL

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def run_migration():
    """Run the shared spaces migration"""
    logger.info("Starting shared spaces migration...")
    
    with engine.connect() as connection:
        # Start transaction
        trans = connection.begin()
        
        try:
            # 1. Add space_type column to user_spaces table
            logger.info("Adding space_type column to user_spaces table...")
            connection.execute(text("""
                ALTER TABLE user_spaces 
                ADD COLUMN IF NOT EXISTS space_type VARCHAR(20) NOT NULL DEFAULT 'personal'
            """))
            
            # 2. Add index on space_type
            logger.info("Adding index on space_type...")
            connection.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_user_spaces_space_type 
                ON user_spaces(space_type)
            """))
            
            # 3. Create space_memberships table
            logger.info("Creating space_memberships table...")
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS space_memberships (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                    space_id INTEGER NOT NULL REFERENCES user_spaces(id) ON DELETE CASCADE,
                    role VARCHAR(20) NOT NULL DEFAULT 'member',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER NOT NULL REFERENCES users(id),
                    
                    -- Ensure unique user-space combinations
                    CONSTRAINT unique_user_space_membership UNIQUE (user_id, space_id)
                )
            """))
            
            # 4. Add indexes for space_memberships
            logger.info("Adding indexes for space_memberships...")
            connection.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_space_memberships_user_id 
                ON space_memberships(user_id)
            """))
            
            connection.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_space_memberships_space_id 
                ON space_memberships(space_id)
            """))
            
            connection.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_space_memberships_role 
                ON space_memberships(role)
            """))
            
            # 5. Update any existing spaces to ensure they have the correct space_type
            logger.info("Updating existing spaces to have correct space_type...")
            result = connection.execute(text("""
                UPDATE user_spaces 
                SET space_type = 'personal' 
                WHERE space_type IS NULL OR space_type = ''
            """))
            logger.info(f"Updated {result.rowcount} existing spaces to personal type")
            
            # Commit transaction
            trans.commit()
            logger.info("Shared spaces migration completed successfully!")
            
        except Exception as e:
            # Rollback on error
            trans.rollback()
            logger.error(f"Migration failed: {e}")
            raise

def verify_migration():
    """Verify that the migration was applied correctly"""
    logger.info("Verifying migration...")
    
    with engine.connect() as connection:
        # Check if space_type column exists
        result = connection.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'user_spaces' AND column_name = 'space_type'
        """))
        
        if not result.fetchone():
            raise Exception("space_type column not found in user_spaces table")
        
        # Check if space_memberships table exists
        result = connection.execute(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_name = 'space_memberships'
        """))
        
        if not result.fetchone():
            raise Exception("space_memberships table not found")
        
        # Check indexes
        result = connection.execute(text("""
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = 'user_spaces' AND indexname = 'idx_user_spaces_space_type'
        """))
        
        if not result.fetchone():
            logger.warning("Index idx_user_spaces_space_type not found")
        
        logger.info("Migration verification completed successfully!")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Shared Spaces Migration")
    parser.add_argument("--verify", action="store_true", help="Verify the migration only (no changes)")
    args = parser.parse_args()
    
    try:
        if args.verify:
            verify_migration()
        else:
            run_migration()
            verify_migration()
    except Exception as e:
        logger.error(f"Migration operation failed: {e}")
        sys.exit(1)
