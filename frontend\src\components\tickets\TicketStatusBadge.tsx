import React from 'react';
import { Badge } from '@/components/ui';
import { TicketStatus, TICKET_STATUSES } from '@/types/ticket';

interface TicketStatusBadgeProps {
  status: TicketStatus;
  size?: 'sm' | 'md' | 'lg';
}

export function TicketStatusBadge({ status, size = 'md' }: TicketStatusBadgeProps) {
  const config = TICKET_STATUSES[status];
  
  // Map ticket statuses to badge variants
  const variantMap: Record<TicketStatus, 'success' | 'warning' | 'info' | 'neutral'> = {
    open: 'warning',
    in_progress: 'info',
    resolved: 'success',
    closed: 'neutral'
  };

  return (
    <Badge variant={variantMap[status]} size={size}>
      {config.label}
    </Badge>
  );
}
