{"name": "company-assistant-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^1.0.6", "date-fns": "^4.1.0", "docx-preview": "^0.3.5", "emoji-picker-react": "^4.12.2", "framer-motion": "^12.23.24", "gsap": "^3.13.0", "katex": "^0.16.22", "mqtt": "^5.3.4", "next": "^14.0.4", "pptx-preview": "^1.0.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-katex": "^3.1.0", "react-markdown": "^9.0.1", "react-pdf": "^9.2.1", "recharts": "^2.15.4", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "swiper": "^12.0.2", "xlsx-preview": "^1.0.4"}, "devDependencies": {"@types/node": "^20.10.7", "@types/react": "^18.3.23", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}