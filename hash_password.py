#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to generate hashed password using the same algorithm as the application
"""

from passlib.context import Crypt<PERSON>ontext

# Same password hashing context as used in the application
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    """Hash a password using bcrypt"""
    return pwd_context.hash(password)

if __name__ == "__main__":
    password = "pvadmin@123#"
    hashed_password = hash_password(password)

    print(f"Password: {password}")
    print(f"Hashed Password: {hashed_password}")
    print(f"Hash Length: {len(hashed_password)}")

    # Verify the hash works
    is_valid = pwd_context.verify(password, hashed_password)
    print(f"Verification successful: {is_valid}")
