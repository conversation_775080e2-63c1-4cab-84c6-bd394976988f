import React from 'react';

export interface Tab {
  value: string;
  label: string;
  count?: number;
}

interface TabsProps {
  tabs: readonly Tab[] | Tab[];
  activeTab: string;
  onChange: (value: string) => void;
  className?: string;
}

export function Tabs({ tabs, activeTab, onChange, className = '' }: TabsProps) {
  return (
    <div className={`border-b border-gray-200 mb-6 ${className}`}>
      <nav className="inline-flex -mb-px rounded-lg shadow-sm bg-gray-100 p-1">
        {tabs.map((tab) => (
          <button
            key={tab.value}
            onClick={() => onChange(tab.value)}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors duration-150 focus:outline-none ${
              activeTab === tab.value
                ? 'bg-white shadow text-blue-900'
                : 'text-gray-600 hover:bg-white hover:shadow'
            }`}
          >
            {tab.label}
            {typeof tab.count === 'number' && (
              <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${
                activeTab === tab.value 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'bg-gray-200 text-gray-700'
              }`}>
                {tab.count}
              </span>
            )}
          </button>
        ))}
      </nav>
    </div>
  );
}
