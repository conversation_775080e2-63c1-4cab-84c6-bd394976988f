import React from 'react';
import { useChat } from '@/contexts';
import SearchToggle from '../ui/SearchToggle';

export default function DeepSearchToggle() {
  const { useDeepSearch, setUseDeepSearch, selectedAgent } = useChat();

  if (!selectedAgent) {
    return null;
  }

  return (
    <SearchToggle
      enabled={useDeepSearch}
      onToggle={() => setUseDeepSearch(!useDeepSearch)}
      label="Enable Deep Research"
      variant="purple"
    />
  );
} 