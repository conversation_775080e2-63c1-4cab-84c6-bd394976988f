'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';

// Types
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  sender?: {
    id: number;
    username: string;
  };
  conversationId?: number;
  timestamp: Date;
  read: boolean;
}

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  soundEnabled: boolean;
}

// Action types
type NotificationAction =
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'MARK_AS_READ'; payload: string }
  | { type: 'MARK_ALL_AS_READ' }
  | { type: 'TOGGLE_SOUND'; payload: boolean }
  | { type: 'CLEAR_ALL' };

// Initial state
const initialState: NotificationState = {
  notifications: [],
  unreadCount: 0,
  soundEnabled: true,
};

// Reducer
function notificationReducer(state: NotificationState, action: NotificationAction): NotificationState {
  switch (action.type) {
    case 'ADD_NOTIFICATION':
      const newNotifications = [action.payload, ...state.notifications];
      return {
        ...state,
        notifications: newNotifications,
        unreadCount: state.unreadCount + 1,
      };

    case 'REMOVE_NOTIFICATION':
      const updatedNotifications = state.notifications.filter(
        notification => notification.id !== action.payload
      );
      const wasUnread = state.notifications.find(n => n.id === action.payload && !n.read);
      return {
        ...state,
        notifications: updatedNotifications,
        unreadCount: wasUnread ? Math.max(0, state.unreadCount - 1) : state.unreadCount,
      };

    case 'MARK_AS_READ':
      const markedNotifications = state.notifications.map(notification =>
        notification.id === action.payload ? { ...notification, read: true } : notification
      );
      const wasMarkedUnread = state.notifications.find(n => n.id === action.payload && !n.read);
      return {
        ...state,
        notifications: markedNotifications,
        unreadCount: wasMarkedUnread ? Math.max(0, state.unreadCount - 1) : state.unreadCount,
      };

    case 'MARK_ALL_AS_READ':
      return {
        ...state,
        notifications: state.notifications.map(notification => ({ ...notification, read: true })),
        unreadCount: 0,
      };

    case 'TOGGLE_SOUND':
      return {
        ...state,
        soundEnabled: action.payload,
      };

    case 'CLEAR_ALL':
      return {
        ...state,
        notifications: [],
        unreadCount: 0,
      };

    default:
      return state;
  }
}

// Context
interface NotificationContextType {
  state: NotificationState;
  dispatch: React.Dispatch<NotificationAction>;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  removeNotification: (id: string) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearAll: () => void;
  toggleSound: (enabled: boolean) => void;
  handleNotificationClick: (notification: Notification) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Provider component
interface NotificationProviderProps {
  children: ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const [state, dispatch] = useReducer(notificationReducer, initialState);
  const router = useRouter();
  
  // Load sound effect
  useEffect(() => {
    // Load sound preference from localStorage
    const soundPref = localStorage.getItem('notification_sound_enabled');
    if (soundPref !== null) {
      dispatch({ type: 'TOGGLE_SOUND', payload: soundPref === 'true' });
    }
  }, []);

  // Add notification
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Math.random().toString(36).substring(2, 15),
      timestamp: new Date(),
      read: false,
    };
    
    dispatch({ type: 'ADD_NOTIFICATION', payload: newNotification });
    
    // Play sound if enabled
    if (state.soundEnabled) {
      playNotificationSound();
    }
  };

  // Play notification sound
  const playNotificationSound = () => {
    try {
      const audio = new Audio('/notification-sound.mp3');
      audio.volume = 0.5;
      audio.play().catch(e => console.error('Error playing notification sound:', e));
    } catch (error) {
      console.error('Failed to play notification sound:', error);
    }
  };

  // Remove notification
  const removeNotification = (id: string) => {
    dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
  };

  // Mark notification as read
  const markAsRead = (id: string) => {
    dispatch({ type: 'MARK_AS_READ', payload: id });
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    dispatch({ type: 'MARK_ALL_AS_READ' });
  };

  // Clear all notifications
  const clearAll = () => {
    dispatch({ type: 'CLEAR_ALL' });
  };

  // Toggle sound
  const toggleSound = (enabled: boolean) => {
    dispatch({ type: 'TOGGLE_SOUND', payload: enabled });
    localStorage.setItem('notification_sound_enabled', String(enabled));
  };

  // Handle notification click
  const handleNotificationClick = (notification: Notification) => {
    // Mark as read
    markAsRead(notification.id);
    
    if (notification.conversationId && notification.sender) {
      // Store both conversation ID and sender ID for targeted navigation
      localStorage.setItem('open_conversation_id', String(notification.conversationId));
      localStorage.setItem('open_sender_id', String(notification.sender.id));  // Assuming sender has an ID
      router.push('/live-chat?senderId=' + notification.sender.id);  // Append sender ID to URL for specific chat room
    } else if (notification.conversationId) {
      localStorage.setItem('open_conversation_id', String(notification.conversationId));
      router.push('/live-chat');  // Fallback to general chat if sender is not available
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        state,
        dispatch,
        addNotification,
        removeNotification,
        markAsRead,
        markAllAsRead,
        clearAll,
        toggleSound,
        handleNotificationClick
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
}

// Hook for using the notification context
export function useNotification() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
} 