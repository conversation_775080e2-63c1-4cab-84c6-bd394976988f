// Centralized prompts catalog for PromptGallery.
// Audience: non-technical employees with workplace questions.

export type PromptItem = { text: string };

const GENERAL_PROMPTS: PromptItem[] = [
  { text: 'Help me prioritize today\'s tasks: [list tasks]. Use the Eisenhower matrix and suggest the first 3 concrete steps.' },
  { text: 'Turn these bullet points into a concise update for my manager. Include accomplishments, blockers, and next steps: [paste bullets].' },
  { text: 'Draft a polite follow‑up email to [recipient] about [topic], referencing our last exchange on [date]. Keep it friendly and concise.' },
  { text: 'Write a 30‑minute meeting agenda for [topic]. Include objective, timeboxes, decisions needed, and who should attend.' },
  { text: 'Create a SMART goal for [objective] with 3 milestones, success metrics, and a target date.' },
  { text: 'Rewrite this message to sound professional, clear, and friendly. Keep intent the same: [paste message].' },
  { text: 'Summarize this long thread into 5 bullets and list 2 action items with owners: [paste text].' },
  { text: 'Prepare talking points to escalate an issue about [issue] to [manager/team]. Be solution‑oriented and brief.' },
  { text: 'Draft a thank‑you note to [name] for helping with [task]. Personalize it with this detail: [detail].' },
  { text: 'Create a weekly plan with time blocks for [tasks]. Include two focus blocks per day and buffer time.' },
  { text: 'Turn these notes into a simple FAQ for colleagues (questions and short answers): [paste notes].' },
  { text: 'Make a handoff checklist for when I\'m on PTO. Include status, owner, risks, and key contacts.' },
  { text: 'Draft a Slack/Teams announcement about [change]. Include what\'s changing, why, when, and a clear call to action.' },
  { text: 'Create a one‑slide summary for [project]: key result, timeline, risks, and what I\'m asking from leadership.' },
  { text: 'Draft polite reminders for these overdue items. One sentence each, friendly tone: [list items].' },
  { text: 'Role‑play as a colleague reviewing my message: [paste message]. Suggest 3 improvements for clarity and tone.' },
  { text: 'Suggest 5 ways to reduce meeting load next week based on this calendar snapshot (themes, async options, and combine opportunities).' },
  { text: 'Turn this rough idea into a short proposal (problem, approach, benefits, effort): [paste idea].' },
];

const DOCUMENT_PROMPTS: PromptItem[] = [
  { text: 'Summarize the document for a busy executive: 5 key points, 3 risks, 3 next steps.' },
  { text: 'Extract action items from these meeting notes with owner and due date. Output a table' },
  { text: 'Turn this policy into a simple explainer for employees: what it is, why it matters, how to follow it, where to get help.' },
  { text: 'Convert this text into a table with columns [columns]. Keep original wording where possible.' },
  { text: 'Rewrite this email to be shorter and clearer. Keep intent, add a helpful subject line.' },
  { text: 'Create an SOP from these steps. Include purpose, scope, prerequisites, step‑by‑step, quality checks, and contacts.' },
  { text: 'Suggest edits to improve clarity, tone, and flow. Provide redline‑style suggestions.' },
  { text: 'Explain this document in plain language for non‑experts. Use short sentences and avoid jargon.' },
  { text: 'Extract key metrics, dates, and names as a bullet list.' },
  { text: 'Create meeting minutes: decisions made, action items (owner, due), and open questions.' },
  { text: 'Turn this problem statement into a project brief: context, goals, scope, out of scope, timeline, stakeholders.' },
  { text: 'Identify risks, assumptions, and dependencies in this plan. Propose mitigations.' },
  { text: 'Create a simple training outline (learning objectives, agenda, exercises, materials) based on this content.' },
  { text: 'Summarize differences between Document A and Document B and highlight what changed for employees.' },
  { text: 'Reformat this text into a clean, scannable memo: headline, key points, actions, timeline.' },
];

const WEB_SEARCH_PROMPTS: PromptItem[] = [
  { text: 'What are the latest updates on [topic] in [region]? Summarize top 5 insights with links, publication dates, and source credibility.' },
  { text: 'Compare pricing and features for [product/service] from at least 3 vendors. Provide a comparison table with source links.' },
  { text: 'Find best practices or templates for [process]. List 5 reputable sources and note when each was last updated.' },
  { text: 'Summarize recent regulations affecting [industry/topic] since [date]. Include links to official sources and effective dates.' },
  { text: 'Analyze customer sentiment about [brand/product]. Summarize common themes from recent articles and reviews with citations.' },
  { text: 'List upcoming conferences or trainings about [topic] in the next 6 months. Include dates, location/virtual, cost, and registration links.' },
  { text: 'Benchmark [metric] for [industry/role] using recent reports. Provide figures, publication dates, and citations.' },
  { text: 'Find case studies of organizations like [company type] implementing [initiative]. Summarize results and link to sources.' },
  { text: 'Identify top tools for [task]. Compare key features, pricing tiers, and pros/cons. Include vendor pages and at least one independent review.' },
  { text: 'Provide authoritative definitions and guidance for the term [term]. Cite standards bodies or government sources where possible.' },
  { text: 'Gather policies or guidance from government or official sites on [topic]. Include the most recent versions with links.' },
  { text: 'Summarize competitive moves by [competitor names] over the last 3 months with links to press releases or news.' },
  { text: 'Find grant or funding opportunities for [purpose/industry]. List eligibility, amounts, deadlines, and application links.' },
  { text: 'Identify common compliance frameworks for [area] (e.g., ISO, SOC, HIPAA). Summarize scope and provide official references.' },
  { text: 'Collect recent thought leadership on [topic] from reputable sources. Provide 5 links with 1‑sentence takeaways for each.' },
];

const DEEP_WEB_SEARCH_PROMPTS: PromptItem[] = [
  { text: 'Conduct an in‑depth review of [topic]. Synthesize findings from academic papers, government sites, and reputable media. Provide at least 8 cited sources with dates and links.' },
  { text: 'Create a systematic comparison of [policies/frameworks/tools]. Define evaluation criteria, weigh pros/cons, and recommend a choice with cited evidence.' },
  { text: 'Trace the history and timeline of [issue/policy] over the last [N] years. Include key events, dates, and source links.' },
  { text: 'Identify risks, compliance obligations, and privacy considerations for [initiative] in [regions]. Cite official guidance and laws.' },
  { text: 'Summarize conflicting viewpoints on [topic]. Present both sides fairly, note evidence quality, and state a reasoned conclusion with citations.' },
  { text: 'Compile a dataset of [metric] from [start year] to [end year] using credible sources. Provide a table, brief analysis, and links.' },
  { text: 'Map the patent landscape for [technology]. List recent patents, assignees, filing dates, and short descriptions with links.' },
  { text: 'Perform vendor due diligence on [vendor]. Summarize security/privacy certifications, recent incidents, and trust signals with sources.' },
  { text: 'Compare regulatory requirements across [countries/regions] for [topic]. Provide a side‑by‑side summary with citations.' },
  { text: 'Identify operational best practices for large‑scale rollouts of [initiative]. Synthesize from whitepapers, standards, and case studies with references.' },
  { text: 'Evaluate the business case for [investment/initiative]. Summarize benefits, costs, risks, and ROI assumptions with cited evidence.' },
  { text: 'Produce a literature scan on [topic] limited to the last 12 months. Note methodology and highlight gaps or areas of uncertainty.' },
];

export function getPromptsForMode(flags: {
  useWebSearch: boolean;
  useDocumentSearch: boolean;
  useDeepSearch: boolean;
}): PromptItem[] {
  if (flags.useDeepSearch) return DEEP_WEB_SEARCH_PROMPTS;
  if (flags.useDocumentSearch) return DOCUMENT_PROMPTS;
  if (flags.useWebSearch) return WEB_SEARCH_PROMPTS;
  return GENERAL_PROMPTS;
}

export const PromptCatalog = {
  general: GENERAL_PROMPTS,
  document: DOCUMENT_PROMPTS,
  web: WEB_SEARCH_PROMPTS,
  deep: DEEP_WEB_SEARCH_PROMPTS,
};

