from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class TicketBase(BaseModel):
    title: str
    description: str
    category: str
    priority: str = "medium"

class TicketCreate(TicketBase):
    pass

class TicketUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    priority: Optional[str] = None
    status: Optional[str] = None
    assigned_to: Optional[int] = None
    admin_notes: Optional[str] = None

class TicketResponse(TicketBase):
    id: int
    status: str
    created_by: int
    assigned_to: Optional[int]
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime]
    admin_notes: Optional[str]

    # User information
    creator_username: Optional[str] = None
    creator_email: Optional[str] = None
    assignee_username: Optional[str] = None

    class Config:
        from_attributes = True

class TicketSummary(BaseModel):
    total: int
    open: int
    in_progress: int
    resolved: int
    closed: int

class TicketAttachmentResponse(BaseModel):
    id: int
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    minio_bucket: str
    minio_object_key: str
    uploaded_by: int
    uploader_username: Optional[str] = None
    uploaded_at: datetime

    class Config:
        from_attributes = True

class TicketResponse(TicketBase):
    id: int
    status: str
    created_by: int
    assigned_to: Optional[int]
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime]
    admin_notes: Optional[str]

    # User information
    creator_username: Optional[str] = None
    creator_email: Optional[str] = None
    assignee_username: Optional[str] = None

    # Attachments
    attachments: Optional[List[TicketAttachmentResponse]] = None

    class Config:
        from_attributes = True

class TicketListResponse(BaseModel):
    tickets: list[TicketResponse]
    total: int
    page: int
    per_page: int
