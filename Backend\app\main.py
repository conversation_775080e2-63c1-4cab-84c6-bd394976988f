import asyncio
import logging
import sys
import os


from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from fastapi import status
from datetime import datetime
import json
from . import config as settings
from .routers import agent_router, auth_router, user_space_router, knowledge_router, chat_router, document_router, group_chat_router, feedback, notes_router
from .routers.account_requests import router as account_requests_router
from .routers.user_deletion import router as user_deletion_router
from .routers.dashboard import router as dashboard_router
from .routers.ticket import router as ticket_router

from .database import get_db
from .services.minio_service import minio_service
from .services.redis_service import redis_service


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_PREFIX}/openapi.json",
    docs_url=f"{settings.API_PREFIX}/docs",
    redoc_url=f"{settings.API_PREFIX}/redoc",
)


# Helper functions for validation formatting
def _min_length_info(err):
    if err.get("type") != "string_too_short":
        return None, None
    ctx = err.get("ctx") or {}
    if "min_length" not in ctx:
        return None, None
    loc = err.get("loc") or []
    field = loc[-1] if loc else None
    return field, ctx.get("min_length")


def _extract_min_lengths(errors, fields):
    min_lengths = {}
    for err in errors:
        field, min_length = _min_length_info(err)
        if field in fields and min_length is not None:
            min_lengths[field] = min_length
    return min_lengths


def _format_min_length_message(label_to_length):
    items = [(label, length) for label, length in label_to_length.items() if length]
    if not items:
        return None
    unique_lengths = {length for _, length in items}
    if len(items) == 2 and len(unique_lengths) == 1:
        label_1, length = items[0]
        label_2, _ = items[1]
        return f"{label_1} and {label_2.lower()} must be at least {length} characters long"
    return "; ".join(
        f"{label} must be at least {length} characters long" for label, length in items
    )


# Add validation exception handler
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    # Improve error messages for better client feedback
    # IMPORTANT: Compute once and mutate this list; calling exc.errors() again
    # returns fresh dicts and would ignore our modifications.
    errors_list = exc.errors()
    for error in errors_list:
        field_name, min_length = _min_length_info(error)
        if min_length is not None:
            error["msg"] = f"The {field_name or 'field'} must be at least {min_length} characters long"

    # Special-case: Provide a single, user-friendly message for password length
    # errors on the change-password endpoint, similar to other password errors.
    try:
        path = str(request.url.path)
    except Exception:
        path = ""

    # Handle special-case password length errors in a unified, data-driven way
    validation_cases = [
        (
            "/auth/me/change-password",
            {"new_password": "New password", "confirm_password": "Confirm password"},
        ),
        (
            "/auth/register",
            {"password": "Password"},
        ),
    ]

    for pattern, field_to_label in validation_cases:
        if pattern in path:
            mins = _extract_min_lengths(errors_list, set(field_to_label.keys()))
            msg = _format_min_length_message({
                label: mins.get(field) for field, label in field_to_label.items()
            })
            if msg:
                logger.error(f"Validation error on {path}: {msg}")
                return JSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    content={"detail": msg}
                )
    
    # Log simplified error information
    logger.error(f"Validation error on {request.url.path}: {str(exc)}")
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder({"detail": errors_list, "body": exc.body}),
    )

# Set up CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
    allow_methods=settings.CORS_ALLOW_METHODS,
    allow_headers=settings.CORS_ALLOW_HEADERS,
)

# Include routers
app.include_router(auth_router, prefix=settings.API_PREFIX)
app.include_router(agent_router, prefix=settings.API_PREFIX)
app.include_router(user_space_router, prefix=settings.API_PREFIX)
app.include_router(knowledge_router, prefix=settings.API_PREFIX)
app.include_router(chat_router, prefix=settings.API_PREFIX)
app.include_router(document_router, prefix=settings.API_PREFIX)
app.include_router(group_chat_router, prefix=settings.API_PREFIX)
app.include_router(notes_router, prefix=settings.API_PREFIX)
app.include_router(feedback.router, prefix=settings.API_PREFIX)
app.include_router(account_requests_router, prefix=settings.API_PREFIX)
app.include_router(user_deletion_router, prefix=settings.API_PREFIX)
app.include_router(dashboard_router, prefix=settings.API_PREFIX)
app.include_router(ticket_router, prefix=settings.API_PREFIX)


@app.on_event("startup")
async def startup_event():
    logger.info("Starting up TechSartHAI API...")
    
    # Initialize Redis (with retry-on-demand strategy)
    redis_connected = await redis_service.initialize_async()
    if redis_connected:
        logger.info("Redis service initialized successfully")
    else:
        logger.warning("Redis service initialization failed - caching disabled")
    
    logger.info("TechSartHAI API startup completed")


@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Shutting down TechSartHAI API...")
    
    # Dispose of database engine
    from .database import engine
    await engine.dispose()
    logger.info("Database engine disposed")
    
    # Close Redis connections
    await redis_service.close()
    
    logger.info("TechSartHAI API shutdown completed")


@app.get("/", tags=["status"])
async def root():

    # Check MinIO status
    minio_status = minio_service.health_check()
    
    return {
        "status": "online",
        "message": "AI Sarthi API is running",
        "minio_status": minio_status["status"],
        "minio_endpoint": minio_status.get("endpoint"),
        "minio_buckets": minio_status.get("buckets", [])
    }

@app.get("/ping", tags=["status"])
async def ping():
    """Simple health check."""
    return {"status": "ok", "message": "pong"}

@app.get("/health", tags=["status"])
async def health_check():
    """Comprehensive health check for all services."""
    try:
        # Check MinIO
        minio_status = minio_service.health_check()
        
        # Check database connection
        db_status = "connected"
        try:
            from .database import async_session
            from sqlalchemy import select
            async with async_session() as db:
                await db.scalar(select(1))
        except Exception as e:
            db_status = f"error: {str(e)}"
        
        # Check Redis connection
        redis_status = await redis_service.health_check()
        # If unhealthy, try to re-initialize once
        if redis_status.get("status") != "healthy":
            logger.warning("Redis unhealthy in /health. Attempting re-init...")
            await redis_service.initialize_async()
            redis_status = await redis_service.health_check()
        
        return {
            "status": "healthy",
            "services": {
                "database": db_status,
                "minio": minio_status,
                "redis": redis_status
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }