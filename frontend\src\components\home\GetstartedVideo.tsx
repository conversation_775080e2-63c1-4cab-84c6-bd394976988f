"use client";
import Link from "next/link";

export default function GetstartedVideo() {
  return (
    <>
      {/* Full width section */}
      <section className="w-full">
        {/* Small side space: tweak these to taste */}
        <div className="mx-3 sm:mx-6 lg:mx-8">
          {/* Keep your rounded banner inside; no need for max-w-7xl/8xl */}
          <div className="rounded-2xl overflow-hidden relative h-[350px] sm:h-[400px] md:h-[450px]">
            {/* Background Video */}
            <video
              autoPlay
              loop
              muted
              playsInline
              className="absolute inset-0 w-full h-full object-cover"
            >
              <source src="/images/bottom-bg.mp4" type="video/mp4" />
            </video>

            {/* Optional dark overlay */}
            <div className="absolute inset-0 bg-black/50" />

            {/* Content */}
            <div className="relative z-10 p-8 sm:p-12 flex flex-col justify-between h-full">
              {/* Top Section: Heading */}
              <div>
                <h2 className="text-3xl sm:text-4xl md:text-5xl text-white leading-snug">
                  Get All The Tools You Need
                  <br className="block" />
                  In a Single Platform
                </h2>
              </div>

              {/* Bottom Section: Button */}
              <div>
                <Link href="/login">
                  <button className="group bg-white/90 text-purple-700 px-6 py-3 rounded-full hover:bg-transparent hover:text-white border border-white transition-all duration-300 flex items-center gap-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-6 h-6 -ml-3 transition-transform duration-300 group-hover:rotate-90"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                      />
                    </svg>
                    Get Started
                  </button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}

