'use client';

import React, { useState, useMemo } from 'react';
import Link from 'next/link';
import { MainLayout } from '@/components/layout';
import { ChevronDownIcon } from '@heroicons/react/solid';
import { faqData, categories } from '@/data/faq_data';

export default function FAQPage() {
  const [activeCategory, setActiveCategory] = useState('general');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleItem = (id: string) => {
    setExpandedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  // Filter FAQs based on category and search term - memoized for performance
  const filteredFAQs = useMemo(() => {
    return faqData.filter(faq => {
      const matchesCategory = faq.category === activeCategory;
      const matchesSearch = !searchTerm || 
        faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (typeof faq.answer === 'string' && faq.answer.toLowerCase().includes(searchTerm.toLowerCase()));
      
      return matchesCategory && matchesSearch;
    });
  }, [activeCategory, searchTerm]);

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Simple Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Frequently Asked Questions</h1>
        </div>
        
        {/* Search Input */}
        <div className="mb-6">
          <input
            type="text"
            placeholder="Search FAQs..."
            className="w-full border rounded-md px-4 py-2"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* Category Tabs */}
        <div className="mb-8 border-b">
          <div className="flex flex-wrap -mb-px">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`inline-block p-4 font-medium text-center ${
                  activeCategory === category.id
                    ? 'text-blue-900 border-b-2 border-blue-900'
                    : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {filteredFAQs.length === 0 ? (
            <p className="text-center text-gray-500 py-8">No FAQs found matching your search.</p>
          ) : (
            filteredFAQs.map((faq) => (
              <div key={faq.id} className="border rounded-md overflow-hidden">
                <button
                  onClick={() => toggleItem(faq.id)}
                  className="w-full flex items-center justify-between px-6 py-4 bg-white hover:bg-gray-50 text-left"
                >
                  <h3 className="font-medium text-gray-900">{faq.question}</h3>
                  <ChevronDownIcon
                    className={`w-5 h-5 text-gray-500 transform transition-transform ${
                      expandedItems.includes(faq.id) ? 'rotate-180' : ''
                    }`}
                  />
                </button>
                {expandedItems.includes(faq.id) && (
                  <div className="px-6 py-4 bg-green-100 border-t">
                    <p className="text-gray-700 rounded-md">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </MainLayout>
  );
}