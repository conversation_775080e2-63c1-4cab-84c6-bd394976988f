"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/duplexer2";
exports.ids = ["vendor-chunks/duplexer2"];
exports.modules = {

/***/ "(ssr)/./node_modules/duplexer2/index.js":
/*!*****************************************!*\
  !*** ./node_modules/duplexer2/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar stream = __webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\");\n\nfunction DuplexWrapper(options, writable, readable) {\n  if (typeof readable === \"undefined\") {\n    readable = writable;\n    writable = options;\n    options = null;\n  }\n\n  stream.Duplex.call(this, options);\n\n  if (typeof readable.read !== \"function\") {\n    readable = (new stream.Readable(options)).wrap(readable);\n  }\n\n  this._writable = writable;\n  this._readable = readable;\n  this._waiting = false;\n\n  var self = this;\n\n  writable.once(\"finish\", function() {\n    self.end();\n  });\n\n  this.once(\"finish\", function() {\n    writable.end();\n  });\n\n  readable.on(\"readable\", function() {\n    if (self._waiting) {\n      self._waiting = false;\n      self._read();\n    }\n  });\n\n  readable.once(\"end\", function() {\n    self.push(null);\n  });\n\n  if (!options || typeof options.bubbleErrors === \"undefined\" || options.bubbleErrors) {\n    writable.on(\"error\", function(err) {\n      self.emit(\"error\", err);\n    });\n\n    readable.on(\"error\", function(err) {\n      self.emit(\"error\", err);\n    });\n  }\n}\n\nDuplexWrapper.prototype = Object.create(stream.Duplex.prototype, {constructor: {value: DuplexWrapper}});\n\nDuplexWrapper.prototype._write = function _write(input, encoding, done) {\n  this._writable.write(input, encoding, done);\n};\n\nDuplexWrapper.prototype._read = function _read() {\n  var buf;\n  var reads = 0;\n  while ((buf = this._readable.read()) !== null) {\n    this.push(buf);\n    reads++;\n  }\n  if (reads === 0) {\n    this._waiting = true;\n  }\n};\n\nmodule.exports = function duplex2(options, writable, readable) {\n  return new DuplexWrapper(options, writable, readable);\n};\n\nmodule.exports.DuplexWrapper = DuplexWrapper;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/duplexer2/index.js\n");

/***/ })

};
;