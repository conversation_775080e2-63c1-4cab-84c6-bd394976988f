@tailwind base;
@tailwind components;
@tailwind utilities;


:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
}



.text-primary {
  color: var(--foreground);
}

/* Override default styles for chat components */
.chat-message-user {
  background-color: #000000;
  color: #ffffff;
}

.chat-message-assistant {
  background-color: #f8f8f8;
  color: #000000;
  border: 1px solid #000000;
}

/* Fast spinning animation for refresh button */
@keyframes spin-fast {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-fast {
  animation: spin-fast 0.5s linear infinite;
}

/* Smooth scrolling for entire page */
html {
  scroll-behavior: smooth;
}

/* Improved text rendering */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
