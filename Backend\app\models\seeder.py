import logging
from sqlalchemy.orm import Session
from .user import User
from .knowledge_base import KnowledgeDocument
logger = logging.getLogger(__name__)
def seed_default_users(db: Session) -> None:
    """Seed default users into the database"""
    logger.info("Seeding default users...")
    
    try:
        default_users_data = User.get_default_users_data()
        
        for username, email, industry, role in default_users_data:
            existing_user = db.query(User).filter(
                (User.username == username) | (User.email == email)
            ).first()
            
            if not existing_user:
                user = User(
                    username=username,
                    email=email,
                    industry=industry,
                    role=role
                )
                # Set role-based default password
                default_password = 'admin@123' if role == 'admin' else 'user@123'
                user.set_password(default_password)
                db.add(user)
                logger.info(f"Created default user: {username} ({role})")
            else:
                logger.info(f"User {username} already exists, skipping...")
        
        db.commit()
        logger.info("Default users seeded successfully")
        
    except Exception as e:
        logger.error(f"Error seeding default users: {e}")
        db.rollback()
        raise


def clear_knowledge_documents(db: Session) -> None:
    """Clear existing knowledge documents"""
    try:
        count = db.query(KnowledgeDocument).delete()
        logger.info(f"Cleared {count} existing knowledge documents")
        db.commit()
    except Exception as e:
        logger.error(f"Error clearing knowledge documents: {e}")
        db.rollback()
        raise


def get_sample_knowledge_documents_data():
    """Get sample knowledge documents data"""
    return [
        # IT Department documents
        {
            "title": "IT Support Guidelines",
            "description": "Guidelines for IT support requests and escalation procedures",
            "document_type": "text",
            "content": """# IT Support Guidelines

## Request Process
1. Submit a ticket through the support portal
2. Provide detailed information about your issue
3. Attach screenshots if applicable

## Priority Levels
- Low: Minor issues, cosmetic problems
- Medium: Functionality impaired but workaround exists
- High: Critical function not working
- Urgent: System down or security breach

## Escalation Procedure
If your ticket has not been addressed within the SLA:
1. Contact your department's IT liaison
2. If unresolved, contact the IT Manager
3. For critical issues, call the emergency support line

## Response Times
- Urgent: 1 hour
- High: 4 hours
- Medium: 1 business day
- Low: 3 business days"""
        },
        # HR Department documents
        {
            "title": "Employee Handbook 2025",
            "description": "Official employee handbook with company policies",
            "document_type": "text",
            "content": """# Employee Handbook 2025

## Company Mission
Our mission is to deliver innovative solutions while maintaining the highest standards of quality and customer service.

## Work Hours
Standard work hours are 9:00 AM to 5:00 PM, Monday through Friday.
Flexible scheduling options are available with manager approval.

## Leave Policy
- Vacation: 15 days per year, accrued monthly
- Sick Leave: 10 days per year
- Personal Days: 3 days per year
- Parental Leave: 12 weeks

## Code of Conduct
All employees are expected to maintain professional behavior and adhere to ethical standards.
Harassment of any kind will not be tolerated.

## Remote Work Policy
Remote work options are available for eligible positions with manager approval."""
        },
        # Finance Department documents
        {
            "title": "Expense Reimbursement Process",
            "description": "Step-by-step guide for submitting and processing expense claims",
            "document_type": "text",
            "content": """# Expense Reimbursement Process

## Eligible Expenses
- Business travel (airfare, hotel, meals)
- Client entertainment
- Office supplies
- Professional development

## Submission Process
1. Keep all original receipts
2. Complete the expense report form within 30 days
3. Attach receipts and supporting documentation
4. Get manager approval
5. Submit to Finance department

## Approval Thresholds
- Up to $500: Department Manager
- $501-$5,000: Department Director
- Above $5,000: CFO approval required

## Reimbursement Timeline
Approved expenses will be reimbursed within 2 pay cycles."""
        }
    ]


def seed_sample_knowledge_documents(db: Session) -> None:
    """Create sample knowledge documents for testing"""
    logger.info("Seeding sample knowledge documents...")
    
    try:
        # Check if knowledge documents already exist
        if db.query(KnowledgeDocument).count() > 0:
            logger.info("Knowledge documents already exist, skipping creation.")
            return
        
        # Get admin user for ownership
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            logger.warning("Admin user not found, cannot create sample documents.")
            return
        
        # Ensure a default sample space exists for admin
        from .user_space import UserSpace
        sample_space = db.query(UserSpace).filter_by(owner_id=admin_user.id, name="Sample Space").first()
        if not sample_space:
            sample_space = UserSpace(name="Sample Space", description="Default space for sample documents", owner_id=admin_user.id)
            db.add(sample_space)
            db.commit()
            db.refresh(sample_space)
        
        # Create sample documents in the sample space
        documents_data = get_sample_knowledge_documents_data()
        for doc_data in documents_data:
            document = KnowledgeDocument(
                title=doc_data["title"],
                description=doc_data["description"],
                space_id=sample_space.id,
                document_type=doc_data["document_type"],
                content=doc_data["content"],
                created_by=admin_user.id
            )
            db.add(document)
        
        db.commit()
        logger.info(f"Created {len(documents_data)} sample knowledge documents")
        
    except Exception as e:
        logger.error(f"Error creating sample knowledge documents: {e}")
        db.rollback()
        raise


def seed_all_data(db: Session, clear_existing: bool = False) -> None:
    """Seed all default data (users and knowledge documents)"""
    logger.info("Starting complete data seeding...")
    
    try:
        # Seed users first (needed for knowledge documents)
        seed_default_users(db)
        
        # Clear existing knowledge documents if requested
        if clear_existing:
            clear_knowledge_documents(db)
        
        # Seed knowledge documents
        seed_sample_knowledge_documents(db)
        
        logger.info("All data seeded successfully")
        
    except Exception as e:
        logger.error(f"Error during complete data seeding: {e}")
        raise


def print_seeding_summary():
    """Print summary of seeded data"""
    default_users_data = User.get_default_users_data()
    print("DATABASE SEEDING COMPLETE")
    print("\nDefault users created (role-based passwords):")