"""
Lightweight logging wrapper around redis_service to emit clear CACHE-HIT/MISS/SET lines.
"""

import logging
from typing import Any

from .redis_service import redis_service

log = logging.getLogger("redis")


async def cached_get(key: str, log_operation: bool = True) -> Any:
    value = await redis_service.get(key)
    if log_operation:
        if value is None:
            log.info("CACHE-MISS %s", key)
        else:
            log.info("CACHE-HIT  %s", key)
    return value


async def cached_set(key: str, value: Any, ttl: int, log_operation: bool = True) -> bool:
    if log_operation:
        log.info("CACHE-SET  %s  ttl=%s", key, ttl)
    return await redis_service.set(key, value, ttl=ttl)


async def cached_delete(*keys: str) -> int:
    log.info("CACHE-DEL  %s", ', '.join(keys))
    return await redis_service.delete(*keys)


async def store_context_snapshot_meta(user_id: int, agent_type: str, chat_session_id: str, payload: dict, ttl: int) -> bool:
    """
    Store context snapshot metadata in Redis cache for a specific chat session.
    
    Args:
        user_id: The ID of the user associated with the chat session
        agent_type: The type of agent (e.g., 'assistant', 'support')
        chat_session_id: Unique identifier for the chat session
        payload: Dictionary containing the context metadata to store
        ttl: Time-to-live in seconds for the cached data
        
    Returns:
        bool: True if the data was successfully stored, False otherwise
    """
    from .. import config
    import json
    
    # Build hierarchical cache key for context metadata
    key = f"{config.CACHE_PREFIX_CHAT}:context:{user_id}:{chat_session_id}:{agent_type}"
    log.info("CACHE-SET  %s  ttl=%s (context snapshot meta)", key, ttl)
    
    try:
        # Attempt to serialize payload as JSON for consistent storage
        data = json.dumps(payload)
    except Exception:
        # Fall back to storing raw payload if JSON serialization fails
        data = payload
    
    return await redis_service.set(key, data, ttl=ttl)


async def get_context_snapshot_meta(user_id: int, agent_type: str, chat_session_id: str) -> Any:
    """
    Retrieve context snapshot metadata from Redis cache for a specific chat session.
    
    Args:
        user_id: The ID of the user associated with the chat session
        agent_type: The type of agent (e.g., 'assistant', 'support')
        chat_session_id: Unique identifier for the chat session
        
    Returns:
        Any: The cached context metadata, or None if not found
    """
    from .. import config
    import json
    
    # Build hierarchical cache key for context metadata
    key = f"{config.CACHE_PREFIX_CHAT}:context:{user_id}:{chat_session_id}:{agent_type}"
    value = await redis_service.get(key)
    
    if value is None:
        log.info("CACHE-MISS %s (context snapshot meta)", key)
        return None
    
    log.info("CACHE-HIT  %s (context snapshot meta)", key)
    
    # Return data as-is if it's already a dict or list (likely from Redis JSON storage)
    if isinstance(value, (dict, list)):
        return value
    
    try:
        # Attempt to deserialize JSON string back to Python object
        return json.loads(value)
    except Exception:
        # Return raw value if JSON deserialization fails
        return value


