from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Optional

from ..database import get_db
from ..auth.dependencies import get_current_user
from ..services.redis_service import invalidate_notes_cache
from ..services.redis_logger import cached_get, cached_set
from .. import config
from fastapi.encoders import jsonable_encoder
from ..models.user import User
from ..models.note import Note
from ..models.feedback import ExpertChatInteraction
from ..schemas.note import NoteCreate, NoteUpdate, NoteResponse


router = APIRouter(
    prefix="/notes",
    tags=["notes"],
    responses={404: {"description": "Not found"}},
)


# Cache key builders to align with invalidation patterns
def build_list_key(prefix: str):
    def _build(*args, **kwargs) -> str:
        user = kwargs.get('current_user')
        user_id = getattr(user, 'id', 'anon')
        search = kwargs.get('search') or ''
        page = kwargs.get('page', 1)
        page_size = kwargs.get('page_size', 50)
        return f"{prefix}:user:{user_id}:search:{search}:page:{page}:size:{page_size}"
    return _build


def build_note_key(prefix: str):
    def _build(*args, **kwargs) -> str:
        user = kwargs.get('current_user')
        user_id = getattr(user, 'id', 'anon')
        note_id = kwargs.get('note_id')
        return f"{prefix}:user:{user_id}:id:{note_id}"
    return _build


@router.post("", response_model=NoteResponse, status_code=status.HTTP_201_CREATED)
async def create_note(
    payload: NoteCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    interaction_id: Optional[int] = payload.interaction_id

    # Resolve response_id to interaction_id if provided
    if not interaction_id and payload.response_id:
        interaction = await ExpertChatInteraction.get_by_response_id(db, payload.response_id)
        if not interaction:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Response not found")
        interaction_id = interaction.id

    # Idempotency: return existing note for this user+interaction if it already exists
    if interaction_id:
        result = await db.execute(
            select(Note).where(
                Note.user_id == current_user.id,
                Note.interaction_id == interaction_id
            )
        )
        existing = result.scalars().first()
        if existing:
            return existing

    note = Note(
        user_id=current_user.id,
        interaction_id=interaction_id,
        title=payload.title.strip()[:255],
        combined_text=payload.combined_text,
        extra_text=payload.extra_text,
        sources=payload.sources,
        session_id=payload.session_id,
        # tags removed
    )
    db.add(note)
    await db.commit()
    await db.refresh(note)

    # Invalidate list/detail caches for this user/note
    try:
        await invalidate_notes_cache(user_id=current_user.id, note_id=note.id)
    except Exception:
        pass

    return note


@router.get("", response_model=List[NoteResponse])
async def list_notes(
    search: Optional[str] = Query(None, description="Search by title or content"),
    page: int = Query(1, ge=1),
    page_size: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    # Cache attempt
    cache_key = f"{config.CACHE_PREFIX_NOTES}:list:user:{current_user.id}:search:{search or ''}:page:{page}:size:{page_size}"
    cached_value = await cached_get(cache_key)
    if cached_value is not None:
        return cached_value

    stmt = select(Note).where(Note.user_id == current_user.id)

    if search:
        like = f"%{search}%"
        from sqlalchemy import or_
        stmt = stmt.where(or_(Note.title.ilike(like), Note.combined_text.ilike(like)))

    stmt = stmt.order_by(Note.created_at.desc()).offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(stmt)
    items = result.scalars().all()

    # Store in cache
    try:
        serializable = jsonable_encoder(items)
        await cached_set(cache_key, serializable, ttl=config.CACHE_TTL_MEDIUM)
    except Exception:
        pass

    return items


@router.get("/{note_id}", response_model=NoteResponse)
async def get_note(
    note_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    # Cache attempt
    cache_key = f"{config.CACHE_PREFIX_NOTES}:note:user:{current_user.id}:id:{note_id}"
    cached_value = await cached_get(cache_key)
    if cached_value is not None:
        return cached_value

    result = await db.execute(select(Note).where(Note.id == note_id, Note.user_id == current_user.id))
    note = result.scalars().first()
    if not note:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Note not found")
    # Store in cache
    try:
        serializable = jsonable_encoder(note)
        await cached_set(cache_key, serializable, ttl=config.CACHE_TTL_MEDIUM)
    except Exception:
        pass

    return note


@router.put("/{note_id}", response_model=NoteResponse)
async def update_note(
    note_id: int,
    payload: NoteUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    result = await db.execute(
        select(Note).where(Note.id == note_id, Note.user_id == current_user.id)
    )
    note = result.scalars().first()
    if not note:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Note not found")

    if payload.title is not None:
        title = payload.title.strip()
        if not title:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Title cannot be empty")
        note.title = title[:255]
    if payload.extra_text is not None:
        note.extra_text = payload.extra_text
    if payload.combined_text is not None:
        note.combined_text = payload.combined_text
    # pin/archive removed
    # tags removed

    await db.commit()
    await db.refresh(note)

    # Invalidate caches impacted by this note update
    try:
        await invalidate_notes_cache(user_id=current_user.id, note_id=note.id)
    except Exception:
        pass

    return note


@router.delete("/{note_id}")
async def delete_note(
    note_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    result = await db.execute(
        select(Note).where(Note.id == note_id, Note.user_id == current_user.id)
    )
    note = result.scalars().first()
    if not note:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Note not found")
    await db.delete(note)
    await db.commit()

    # Invalidate caches impacted by this note deletion
    try:
        await invalidate_notes_cache(user_id=current_user.id, note_id=note.id)
    except Exception:
        pass

    return {"message": "deleted"}


