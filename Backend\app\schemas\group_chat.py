from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class GroupMemberResponse(BaseModel):
    user_id: int
    username: Optional[str]
    role: str
    joined_at: datetime

    class Config:
        from_attributes = True

class GroupCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    member_ids: List[int] = Field(default_factory=list, description="IDs of users to add to the group (excluding creator if desired)")
    space_id: Optional[int] = Field(None, description="ID of the shared space this group is linked to")

class GroupUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)

class GroupResponse(BaseModel):
    id: int
    name: str
    creator_id: int
    space_id: Optional[int]
    created_at: datetime
    updated_at: datetime
    is_active: bool
    last_message: Optional[dict]
    members: List[GroupMemberResponse] = Field(default_factory=list)

    class Config:
        from_attributes = True

class GroupMessageCreate(BaseModel):
    content: str = Field(..., min_length=1, max_length=5000)
    message_type: str = Field(default="text", pattern="^(text|image|file)$")

class GroupMessageResponse(BaseModel):
    id: int
    group_id: int
    sender_id: int
    sender_username: Optional[str]
    content: str
    message_type: str
    created_at: datetime
    is_read: bool
    is_edited: bool
    edited_at: Optional[datetime]

    class Config:
        from_attributes = True 

class GroupMembersAdd(BaseModel):
    member_ids: List[int] = Field(..., min_items=1) 