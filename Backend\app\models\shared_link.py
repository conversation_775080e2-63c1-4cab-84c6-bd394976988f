from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, Index
from sqlalchemy.orm import relationship
from datetime import datetime

from ..database import Base

class SharedLink(Base):
    """Represents a hyperlink that was included in a chat message."""
    __tablename__ = "shared_links"

    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(Integer, ForeignKey('messages.id', ondelete='CASCADE'), nullable=False)
    conversation_id = Column(Integer, ForeignKey('conversations.id', ondelete='CASCADE'), nullable=False)
    url = Column(Text, nullable=False)
    title = Column(String(255), nullable=True)
    preview_img = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    message = relationship("Message")
    conversation = relationship("Conversation")

    __table_args__ = (
        Index('idx_link_conversation', 'conversation_id', 'created_at'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'message_id': self.message_id,
            'conversation_id': self.conversation_id,
            'url': self.url,
            'title': self.title,
            'preview_img': self.preview_img,
            'created_at': self.created_at.isoformat() if self.created_at else None,
        } 