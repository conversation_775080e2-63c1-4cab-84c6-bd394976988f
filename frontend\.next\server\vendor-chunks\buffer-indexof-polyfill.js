/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/buffer-indexof-polyfill";
exports.ids = ["vendor-chunks/buffer-indexof-polyfill"];
exports.modules = {

/***/ "(ssr)/./node_modules/buffer-indexof-polyfill/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/buffer-indexof-polyfill/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nvar initBuffer = __webpack_require__(/*! ./init-buffer */ \"(ssr)/./node_modules/buffer-indexof-polyfill/init-buffer.js\");\n\nif (!Buffer.prototype.indexOf) {\n    Buffer.prototype.indexOf = function (value, offset) {\n        offset = offset || 0;\n\n        // Always wrap the input as a Buffer so that this method will support any\n        // data type such as array octet, string or buffer.\n        if (typeof value === \"string\" || value instanceof String) {\n            value = initBuffer(value);\n        } else if (typeof value === \"number\" || value instanceof Number) {\n            value = initBuffer([ value ]);\n        }\n\n        var len = value.length;\n\n        for (var i = offset; i <= this.length - len; i++) {\n            var mismatch = false;\n            for (var j = 0; j < len; j++) {\n                if (this[i + j] != value[j]) {\n                    mismatch = true;\n                    break;\n                }\n            }\n\n            if (!mismatch) {\n                return i;\n            }\n        }\n\n        return -1;\n    };\n}\n\nfunction bufferLastIndexOf (value, offset) {\n\n    // Always wrap the input as a Buffer so that this method will support any\n    // data type such as array octet, string or buffer.\n    if (typeof value === \"string\" || value instanceof String) {\n        value = initBuffer(value);\n    } else if (typeof value === \"number\" || value instanceof Number) {\n        value = initBuffer([ value ]);\n    }\n\n    var len = value.length;\n    offset = offset || this.length - len;\n\n    for (var i = offset; i >= 0; i--) {\n        var mismatch = false;\n        for (var j = 0; j < len; j++) {\n            if (this[i + j] != value[j]) {\n                mismatch = true;\n                break;\n            }\n        }\n\n        if (!mismatch) {\n            return i;\n        }\n    }\n\n    return -1;\n}\n\n\nif (Buffer.prototype.lastIndexOf) {\n    // check Buffer#lastIndexOf is usable: https://github.com/nodejs/node/issues/4604\n    if (initBuffer(\"ABC\").lastIndexOf (\"ABC\") === -1)\n        Buffer.prototype.lastIndexOf = bufferLastIndexOf;\n} else {\n    Buffer.prototype.lastIndexOf = bufferLastIndexOf;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWluZGV4b2YtcG9seWZpbGwvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYixpQkFBaUIsbUJBQU8sQ0FBQyxrRkFBZTs7QUFFeEM7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7O0FBRUE7O0FBRUEsNkJBQTZCLHdCQUF3QjtBQUNyRDtBQUNBLDRCQUE0QixTQUFTO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSx5QkFBeUIsUUFBUTtBQUNqQztBQUNBLHdCQUF3QixTQUFTO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2J1ZmZlci1pbmRleG9mLXBvbHlmaWxsL2luZGV4LmpzPzk5YWMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgaW5pdEJ1ZmZlciA9IHJlcXVpcmUoXCIuL2luaXQtYnVmZmVyXCIpO1xuXG5pZiAoIUJ1ZmZlci5wcm90b3R5cGUuaW5kZXhPZikge1xuICAgIEJ1ZmZlci5wcm90b3R5cGUuaW5kZXhPZiA9IGZ1bmN0aW9uICh2YWx1ZSwgb2Zmc2V0KSB7XG4gICAgICAgIG9mZnNldCA9IG9mZnNldCB8fCAwO1xuXG4gICAgICAgIC8vIEFsd2F5cyB3cmFwIHRoZSBpbnB1dCBhcyBhIEJ1ZmZlciBzbyB0aGF0IHRoaXMgbWV0aG9kIHdpbGwgc3VwcG9ydCBhbnlcbiAgICAgICAgLy8gZGF0YSB0eXBlIHN1Y2ggYXMgYXJyYXkgb2N0ZXQsIHN0cmluZyBvciBidWZmZXIuXG4gICAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09IFwic3RyaW5nXCIgfHwgdmFsdWUgaW5zdGFuY2VvZiBTdHJpbmcpIHtcbiAgICAgICAgICAgIHZhbHVlID0gaW5pdEJ1ZmZlcih2YWx1ZSk7XG4gICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlID09PSBcIm51bWJlclwiIHx8IHZhbHVlIGluc3RhbmNlb2YgTnVtYmVyKSB7XG4gICAgICAgICAgICB2YWx1ZSA9IGluaXRCdWZmZXIoWyB2YWx1ZSBdKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHZhciBsZW4gPSB2YWx1ZS5sZW5ndGg7XG5cbiAgICAgICAgZm9yICh2YXIgaSA9IG9mZnNldDsgaSA8PSB0aGlzLmxlbmd0aCAtIGxlbjsgaSsrKSB7XG4gICAgICAgICAgICB2YXIgbWlzbWF0Y2ggPSBmYWxzZTtcbiAgICAgICAgICAgIGZvciAodmFyIGogPSAwOyBqIDwgbGVuOyBqKyspIHtcbiAgICAgICAgICAgICAgICBpZiAodGhpc1tpICsgal0gIT0gdmFsdWVbal0pIHtcbiAgICAgICAgICAgICAgICAgICAgbWlzbWF0Y2ggPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmICghbWlzbWF0Y2gpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gaTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiAtMTtcbiAgICB9O1xufVxuXG5mdW5jdGlvbiBidWZmZXJMYXN0SW5kZXhPZiAodmFsdWUsIG9mZnNldCkge1xuXG4gICAgLy8gQWx3YXlzIHdyYXAgdGhlIGlucHV0IGFzIGEgQnVmZmVyIHNvIHRoYXQgdGhpcyBtZXRob2Qgd2lsbCBzdXBwb3J0IGFueVxuICAgIC8vIGRhdGEgdHlwZSBzdWNoIGFzIGFycmF5IG9jdGV0LCBzdHJpbmcgb3IgYnVmZmVyLlxuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09IFwic3RyaW5nXCIgfHwgdmFsdWUgaW5zdGFuY2VvZiBTdHJpbmcpIHtcbiAgICAgICAgdmFsdWUgPSBpbml0QnVmZmVyKHZhbHVlKTtcbiAgICB9IGVsc2UgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gXCJudW1iZXJcIiB8fCB2YWx1ZSBpbnN0YW5jZW9mIE51bWJlcikge1xuICAgICAgICB2YWx1ZSA9IGluaXRCdWZmZXIoWyB2YWx1ZSBdKTtcbiAgICB9XG5cbiAgICB2YXIgbGVuID0gdmFsdWUubGVuZ3RoO1xuICAgIG9mZnNldCA9IG9mZnNldCB8fCB0aGlzLmxlbmd0aCAtIGxlbjtcblxuICAgIGZvciAodmFyIGkgPSBvZmZzZXQ7IGkgPj0gMDsgaS0tKSB7XG4gICAgICAgIHZhciBtaXNtYXRjaCA9IGZhbHNlO1xuICAgICAgICBmb3IgKHZhciBqID0gMDsgaiA8IGxlbjsgaisrKSB7XG4gICAgICAgICAgICBpZiAodGhpc1tpICsgal0gIT0gdmFsdWVbal0pIHtcbiAgICAgICAgICAgICAgICBtaXNtYXRjaCA9IHRydWU7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIW1pc21hdGNoKSB7XG4gICAgICAgICAgICByZXR1cm4gaTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiAtMTtcbn1cblxuXG5pZiAoQnVmZmVyLnByb3RvdHlwZS5sYXN0SW5kZXhPZikge1xuICAgIC8vIGNoZWNrIEJ1ZmZlciNsYXN0SW5kZXhPZiBpcyB1c2FibGU6IGh0dHBzOi8vZ2l0aHViLmNvbS9ub2RlanMvbm9kZS9pc3N1ZXMvNDYwNFxuICAgIGlmIChpbml0QnVmZmVyKFwiQUJDXCIpLmxhc3RJbmRleE9mIChcIkFCQ1wiKSA9PT0gLTEpXG4gICAgICAgIEJ1ZmZlci5wcm90b3R5cGUubGFzdEluZGV4T2YgPSBidWZmZXJMYXN0SW5kZXhPZjtcbn0gZWxzZSB7XG4gICAgQnVmZmVyLnByb3RvdHlwZS5sYXN0SW5kZXhPZiA9IGJ1ZmZlckxhc3RJbmRleE9mO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-indexof-polyfill/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-indexof-polyfill/init-buffer.js":
/*!*************************************************************!*\
  !*** ./node_modules/buffer-indexof-polyfill/init-buffer.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("module.exports = function initBuffer(val) {\n  // assume old version\n    var nodeVersion = process && process.version ? process.version : \"v5.0.0\";\n    var major = nodeVersion.split(\".\")[0].replace(\"v\", \"\");\n    return major < 6\n      ? new Buffer(val)\n      : Buffer.from(val);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWluZGV4b2YtcG9seWZpbGwvaW5pdC1idWZmZXIuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2J1ZmZlci1pbmRleG9mLXBvbHlmaWxsL2luaXQtYnVmZmVyLmpzP2FmZmMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBpbml0QnVmZmVyKHZhbCkge1xuICAvLyBhc3N1bWUgb2xkIHZlcnNpb25cbiAgICB2YXIgbm9kZVZlcnNpb24gPSBwcm9jZXNzICYmIHByb2Nlc3MudmVyc2lvbiA/IHByb2Nlc3MudmVyc2lvbiA6IFwidjUuMC4wXCI7XG4gICAgdmFyIG1ham9yID0gbm9kZVZlcnNpb24uc3BsaXQoXCIuXCIpWzBdLnJlcGxhY2UoXCJ2XCIsIFwiXCIpO1xuICAgIHJldHVybiBtYWpvciA8IDZcbiAgICAgID8gbmV3IEJ1ZmZlcih2YWwpXG4gICAgICA6IEJ1ZmZlci5mcm9tKHZhbCk7XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-indexof-polyfill/init-buffer.js\n");

/***/ })

};
;