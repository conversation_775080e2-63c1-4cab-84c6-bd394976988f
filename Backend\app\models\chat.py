from sqlalchemy import Column, Integer, String, Text, DateTime, Foreign<PERSON>ey, Boolean, Index
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

class Conversation(Base):
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    participant1_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    participant2_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    participant1 = relationship("User", foreign_keys=[participant1_id])
    participant2 = relationship("User", foreign_keys=[participant2_id])
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    
    # Indexes for better performance
    __table_args__ = (
        Index('idx_conversation_participants', participant1_id, participant2_id),
        Index('idx_conversation_updated', updated_at.desc()),
    )
    
    def __repr__(self):
        return f'<Conversation {self.id}: {self.participant1_id} <-> {self.participant2_id}>'
    
    def to_dict(self) -> dict:
        """Convert conversation to dictionary"""
        # Safe access to participant relationships (avoid lazy loading)
        if 'participant1' in self.__dict__ and self.participant1:
            participant1_username = self.participant1.username
        else:
            participant1_username = None
        
        if 'participant2' in self.__dict__ and self.participant2:
            participant2_username = self.participant2.username
        else:
            participant2_username = None
        
        # Safe access to messages relationship (avoid lazy loading)
        if 'messages' in self.__dict__ and self.messages:
            last_message = self.messages[-1].to_dict()
        else:
            last_message = None
        
        return {
            'id': self.id,
            'participant1_id': self.participant1_id,
            'participant2_id': self.participant2_id,
            'participant1_username': participant1_username,
            'participant2_username': participant2_username,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_active': self.is_active,
            'last_message': last_message
        }

class Message(Base):
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey('conversations.id'), nullable=False)
    sender_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    content = Column(Text, nullable=False)
    message_type = Column(String(20), default='text')  # 'text', 'image', 'file'
    created_at = Column(DateTime, default=datetime.utcnow)
    is_read = Column(Boolean, default=False)
    is_edited = Column(Boolean, default=False)
    edited_at = Column(DateTime, nullable=True)
    
    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
    sender = relationship("User", foreign_keys=[sender_id])
    
    # Indexes for better performance
    __table_args__ = (
        Index('idx_message_conversation', conversation_id),
        Index('idx_message_created', created_at.desc()),
        Index('idx_message_unread', is_read, sender_id),
    )
    
    def __repr__(self):
        return f'<Message {self.id}: {self.sender_id} -> {self.conversation_id}>'
    
    def to_dict(self) -> dict:
        """Convert message to dictionary"""
        # Safe access to sender relationship (avoid lazy loading)
        if 'sender' in self.__dict__ and self.sender:
            sender_username = self.sender.username
        else:
            sender_username = None
        
        return {
            'id': self.id,
            'conversation_id': self.conversation_id,
            'sender_id': self.sender_id,
            'sender_username': sender_username,
            'content': self.content,
            'message_type': self.message_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'is_read': self.is_read,
            'is_edited': self.is_edited,
            'edited_at': self.edited_at.isoformat() if self.edited_at else None
        }

class UserStatus(Base):
    __tablename__ = "user_status"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, unique=True)
    is_online = Column(Boolean, default=False)
    last_seen = Column(DateTime, default=datetime.utcnow)
    socket_id = Column(String(100), nullable=True)  # For WebSocket connection tracking
    
    # Relationship
    user = relationship("User")
    
    # Index for performance
    __table_args__ = (
        Index('idx_user_status_online', is_online),
        Index('idx_user_status_last_seen', last_seen.desc()),
    )
    
    def __repr__(self):
        return f'<UserStatus {self.user_id}: {"Online" if self.is_online else "Offline"}>'
    
    def to_dict(self) -> dict:
        """Convert user status to dictionary"""
        return {
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'is_online': self.is_online,
            'last_seen': self.last_seen.isoformat() if self.last_seen else None
        } 