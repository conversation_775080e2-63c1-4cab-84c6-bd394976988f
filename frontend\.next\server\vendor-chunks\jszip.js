"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jszip";
exports.ids = ["vendor-chunks/jszip"];
exports.modules = {

/***/ "(ssr)/./node_modules/jszip/lib/base64.js":
/*!******************************************!*\
  !*** ./node_modules/jszip/lib/base64.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\n// private property\nvar _keyStr = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n\n\n// public method for encoding\nexports.encode = function(input) {\n    var output = [];\n    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;\n    var i = 0, len = input.length, remainingBytes = len;\n\n    var isArray = utils.getTypeOf(input) !== \"string\";\n    while (i < input.length) {\n        remainingBytes = len - i;\n\n        if (!isArray) {\n            chr1 = input.charCodeAt(i++);\n            chr2 = i < len ? input.charCodeAt(i++) : 0;\n            chr3 = i < len ? input.charCodeAt(i++) : 0;\n        } else {\n            chr1 = input[i++];\n            chr2 = i < len ? input[i++] : 0;\n            chr3 = i < len ? input[i++] : 0;\n        }\n\n        enc1 = chr1 >> 2;\n        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n        enc3 = remainingBytes > 1 ? (((chr2 & 15) << 2) | (chr3 >> 6)) : 64;\n        enc4 = remainingBytes > 2 ? (chr3 & 63) : 64;\n\n        output.push(_keyStr.charAt(enc1) + _keyStr.charAt(enc2) + _keyStr.charAt(enc3) + _keyStr.charAt(enc4));\n\n    }\n\n    return output.join(\"\");\n};\n\n// public method for decoding\nexports.decode = function(input) {\n    var chr1, chr2, chr3;\n    var enc1, enc2, enc3, enc4;\n    var i = 0, resultIndex = 0;\n\n    var dataUrlPrefix = \"data:\";\n\n    if (input.substr(0, dataUrlPrefix.length) === dataUrlPrefix) {\n        // This is a common error: people give a data url\n        // (data:image/png;base64,iVBOR...) with a {base64: true} and\n        // wonders why things don't work.\n        // We can detect that the string input looks like a data url but we\n        // *can't* be sure it is one: removing everything up to the comma would\n        // be too dangerous.\n        throw new Error(\"Invalid base64 input, it looks like a data url.\");\n    }\n\n    input = input.replace(/[^A-Za-z0-9+/=]/g, \"\");\n\n    var totalLength = input.length * 3 / 4;\n    if(input.charAt(input.length - 1) === _keyStr.charAt(64)) {\n        totalLength--;\n    }\n    if(input.charAt(input.length - 2) === _keyStr.charAt(64)) {\n        totalLength--;\n    }\n    if (totalLength % 1 !== 0) {\n        // totalLength is not an integer, the length does not match a valid\n        // base64 content. That can happen if:\n        // - the input is not a base64 content\n        // - the input is *almost* a base64 content, with a extra chars at the\n        //   beginning or at the end\n        // - the input uses a base64 variant (base64url for example)\n        throw new Error(\"Invalid base64 input, bad content length.\");\n    }\n    var output;\n    if (support.uint8array) {\n        output = new Uint8Array(totalLength|0);\n    } else {\n        output = new Array(totalLength|0);\n    }\n\n    while (i < input.length) {\n\n        enc1 = _keyStr.indexOf(input.charAt(i++));\n        enc2 = _keyStr.indexOf(input.charAt(i++));\n        enc3 = _keyStr.indexOf(input.charAt(i++));\n        enc4 = _keyStr.indexOf(input.charAt(i++));\n\n        chr1 = (enc1 << 2) | (enc2 >> 4);\n        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\n        chr3 = ((enc3 & 3) << 6) | enc4;\n\n        output[resultIndex++] = chr1;\n\n        if (enc3 !== 64) {\n            output[resultIndex++] = chr2;\n        }\n        if (enc4 !== 64) {\n            output[resultIndex++] = chr3;\n        }\n\n    }\n\n    return output;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/base64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/compressedObject.js":
/*!****************************************************!*\
  !*** ./node_modules/jszip/lib/compressedObject.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\nvar DataWorker = __webpack_require__(/*! ./stream/DataWorker */ \"(ssr)/./node_modules/jszip/lib/stream/DataWorker.js\");\nvar Crc32Probe = __webpack_require__(/*! ./stream/Crc32Probe */ \"(ssr)/./node_modules/jszip/lib/stream/Crc32Probe.js\");\nvar DataLengthProbe = __webpack_require__(/*! ./stream/DataLengthProbe */ \"(ssr)/./node_modules/jszip/lib/stream/DataLengthProbe.js\");\n\n/**\n * Represent a compressed object, with everything needed to decompress it.\n * @constructor\n * @param {number} compressedSize the size of the data compressed.\n * @param {number} uncompressedSize the size of the data after decompression.\n * @param {number} crc32 the crc32 of the decompressed file.\n * @param {object} compression the type of compression, see lib/compressions.js.\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the compressed data.\n */\nfunction CompressedObject(compressedSize, uncompressedSize, crc32, compression, data) {\n    this.compressedSize = compressedSize;\n    this.uncompressedSize = uncompressedSize;\n    this.crc32 = crc32;\n    this.compression = compression;\n    this.compressedContent = data;\n}\n\nCompressedObject.prototype = {\n    /**\n     * Create a worker to get the uncompressed content.\n     * @return {GenericWorker} the worker.\n     */\n    getContentWorker: function () {\n        var worker = new DataWorker(external.Promise.resolve(this.compressedContent))\n            .pipe(this.compression.uncompressWorker())\n            .pipe(new DataLengthProbe(\"data_length\"));\n\n        var that = this;\n        worker.on(\"end\", function () {\n            if (this.streamInfo[\"data_length\"] !== that.uncompressedSize) {\n                throw new Error(\"Bug : uncompressed data size mismatch\");\n            }\n        });\n        return worker;\n    },\n    /**\n     * Create a worker to get the compressed content.\n     * @return {GenericWorker} the worker.\n     */\n    getCompressedWorker: function () {\n        return new DataWorker(external.Promise.resolve(this.compressedContent))\n            .withStreamInfo(\"compressedSize\", this.compressedSize)\n            .withStreamInfo(\"uncompressedSize\", this.uncompressedSize)\n            .withStreamInfo(\"crc32\", this.crc32)\n            .withStreamInfo(\"compression\", this.compression)\n        ;\n    }\n};\n\n/**\n * Chain the given worker with other workers to compress the content with the\n * given compression.\n * @param {GenericWorker} uncompressedWorker the worker to pipe.\n * @param {Object} compression the compression object.\n * @param {Object} compressionOptions the options to use when compressing.\n * @return {GenericWorker} the new worker compressing the content.\n */\nCompressedObject.createWorkerFrom = function (uncompressedWorker, compression, compressionOptions) {\n    return uncompressedWorker\n        .pipe(new Crc32Probe())\n        .pipe(new DataLengthProbe(\"uncompressedSize\"))\n        .pipe(compression.compressWorker(compressionOptions))\n        .pipe(new DataLengthProbe(\"compressedSize\"))\n        .withStreamInfo(\"compression\", compression);\n};\n\nmodule.exports = CompressedObject;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/compressedObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/compressions.js":
/*!************************************************!*\
  !*** ./node_modules/jszip/lib/compressions.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\nexports.STORE = {\n    magic: \"\\x00\\x00\",\n    compressWorker : function () {\n        return new GenericWorker(\"STORE compression\");\n    },\n    uncompressWorker : function () {\n        return new GenericWorker(\"STORE decompression\");\n    }\n};\nexports.DEFLATE = __webpack_require__(/*! ./flate */ \"(ssr)/./node_modules/jszip/lib/flate.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2NvbXByZXNzaW9ucy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixvQkFBb0IsbUJBQU8sQ0FBQyxzRkFBd0I7O0FBRXBELGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrRkFBb0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvY29tcHJlc3Npb25zLmpzP2Y5ZGEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciBHZW5lcmljV29ya2VyID0gcmVxdWlyZShcIi4vc3RyZWFtL0dlbmVyaWNXb3JrZXJcIik7XG5cbmV4cG9ydHMuU1RPUkUgPSB7XG4gICAgbWFnaWM6IFwiXFx4MDBcXHgwMFwiLFxuICAgIGNvbXByZXNzV29ya2VyIDogZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gbmV3IEdlbmVyaWNXb3JrZXIoXCJTVE9SRSBjb21wcmVzc2lvblwiKTtcbiAgICB9LFxuICAgIHVuY29tcHJlc3NXb3JrZXIgOiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBuZXcgR2VuZXJpY1dvcmtlcihcIlNUT1JFIGRlY29tcHJlc3Npb25cIik7XG4gICAgfVxufTtcbmV4cG9ydHMuREVGTEFURSA9IHJlcXVpcmUoXCIuL2ZsYXRlXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/compressions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/crc32.js":
/*!*****************************************!*\
  !*** ./node_modules/jszip/lib/crc32.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\n/**\n * The following functions come from pako, from pako/lib/zlib/crc32.js\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */\n\n// Use ordinary array, since untyped makes no boost here\nfunction makeTable() {\n    var c, table = [];\n\n    for(var n =0; n < 256; n++){\n        c = n;\n        for(var k =0; k < 8; k++){\n            c = ((c&1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1));\n        }\n        table[n] = c;\n    }\n\n    return table;\n}\n\n// Create table on load. Just 255 signed longs. Not a problem.\nvar crcTable = makeTable();\n\n\nfunction crc32(crc, buf, len, pos) {\n    var t = crcTable, end = pos + len;\n\n    crc = crc ^ (-1);\n\n    for (var i = pos; i < end; i++ ) {\n        crc = (crc >>> 8) ^ t[(crc ^ buf[i]) & 0xFF];\n    }\n\n    return (crc ^ (-1)); // >>> 0;\n}\n\n// That's all for the pako functions.\n\n/**\n * Compute the crc32 of a string.\n * This is almost the same as the function crc32, but for strings. Using the\n * same function for the two use cases leads to horrible performances.\n * @param {Number} crc the starting value of the crc.\n * @param {String} str the string to use.\n * @param {Number} len the length of the string.\n * @param {Number} pos the starting position for the crc32 computation.\n * @return {Number} the computed crc32.\n */\nfunction crc32str(crc, str, len, pos) {\n    var t = crcTable, end = pos + len;\n\n    crc = crc ^ (-1);\n\n    for (var i = pos; i < end; i++ ) {\n        crc = (crc >>> 8) ^ t[(crc ^ str.charCodeAt(i)) & 0xFF];\n    }\n\n    return (crc ^ (-1)); // >>> 0;\n}\n\nmodule.exports = function crc32wrapper(input, crc) {\n    if (typeof input === \"undefined\" || !input.length) {\n        return 0;\n    }\n\n    var isArray = utils.getTypeOf(input) !== \"string\";\n\n    if(isArray) {\n        return crc32(crc|0, input, input.length, 0);\n    } else {\n        return crc32str(crc|0, input, input.length, 0);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/crc32.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/defaults.js":
/*!********************************************!*\
  !*** ./node_modules/jszip/lib/defaults.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.base64 = false;\nexports.binary = false;\nexports.dir = false;\nexports.createFolders = true;\nexports.date = null;\nexports.compression = null;\nexports.compressionOptions = null;\nexports.comment = null;\nexports.unixPermissions = null;\nexports.dosPermissions = null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2RlZmF1bHRzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsY0FBYztBQUNkLGNBQWM7QUFDZCxXQUFXO0FBQ1gscUJBQXFCO0FBQ3JCLFlBQVk7QUFDWixtQkFBbUI7QUFDbkIsMEJBQTBCO0FBQzFCLGVBQWU7QUFDZix1QkFBdUI7QUFDdkIsc0JBQXNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2RlZmF1bHRzLmpzP2FkNGUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5leHBvcnRzLmJhc2U2NCA9IGZhbHNlO1xuZXhwb3J0cy5iaW5hcnkgPSBmYWxzZTtcbmV4cG9ydHMuZGlyID0gZmFsc2U7XG5leHBvcnRzLmNyZWF0ZUZvbGRlcnMgPSB0cnVlO1xuZXhwb3J0cy5kYXRlID0gbnVsbDtcbmV4cG9ydHMuY29tcHJlc3Npb24gPSBudWxsO1xuZXhwb3J0cy5jb21wcmVzc2lvbk9wdGlvbnMgPSBudWxsO1xuZXhwb3J0cy5jb21tZW50ID0gbnVsbDtcbmV4cG9ydHMudW5peFBlcm1pc3Npb25zID0gbnVsbDtcbmV4cG9ydHMuZG9zUGVybWlzc2lvbnMgPSBudWxsO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/defaults.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/external.js":
/*!********************************************!*\
  !*** ./node_modules/jszip/lib/external.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// load the global object first:\n// - it should be better integrated in the system (unhandledRejection in node)\n// - the environment may have a custom Promise implementation (see zone.js)\nvar ES6Promise = null;\nif (typeof Promise !== \"undefined\") {\n    ES6Promise = Promise;\n} else {\n    ES6Promise = __webpack_require__(/*! lie */ \"(ssr)/./node_modules/lie/lib/index.js\");\n}\n\n/**\n * Let the user use/change some implementations.\n */\nmodule.exports = {\n    Promise: ES6Promise\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2V4dGVybmFsLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRixpQkFBaUIsbUJBQU8sQ0FBQyxrREFBSztBQUM5Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvZXh0ZXJuYWwuanM/M2YzYiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLy8gbG9hZCB0aGUgZ2xvYmFsIG9iamVjdCBmaXJzdDpcbi8vIC0gaXQgc2hvdWxkIGJlIGJldHRlciBpbnRlZ3JhdGVkIGluIHRoZSBzeXN0ZW0gKHVuaGFuZGxlZFJlamVjdGlvbiBpbiBub2RlKVxuLy8gLSB0aGUgZW52aXJvbm1lbnQgbWF5IGhhdmUgYSBjdXN0b20gUHJvbWlzZSBpbXBsZW1lbnRhdGlvbiAoc2VlIHpvbmUuanMpXG52YXIgRVM2UHJvbWlzZSA9IG51bGw7XG5pZiAodHlwZW9mIFByb21pc2UgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICBFUzZQcm9taXNlID0gUHJvbWlzZTtcbn0gZWxzZSB7XG4gICAgRVM2UHJvbWlzZSA9IHJlcXVpcmUoXCJsaWVcIik7XG59XG5cbi8qKlxuICogTGV0IHRoZSB1c2VyIHVzZS9jaGFuZ2Ugc29tZSBpbXBsZW1lbnRhdGlvbnMuXG4gKi9cbm1vZHVsZS5leHBvcnRzID0ge1xuICAgIFByb21pc2U6IEVTNlByb21pc2Vcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/external.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/flate.js":
/*!*****************************************!*\
  !*** ./node_modules/jszip/lib/flate.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar USE_TYPEDARRAY = (typeof Uint8Array !== \"undefined\") && (typeof Uint16Array !== \"undefined\") && (typeof Uint32Array !== \"undefined\");\n\nvar pako = __webpack_require__(/*! pako */ \"(ssr)/./node_modules/pako/index.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\nvar ARRAY_TYPE = USE_TYPEDARRAY ? \"uint8array\" : \"array\";\n\nexports.magic = \"\\x08\\x00\";\n\n/**\n * Create a worker that uses pako to inflate/deflate.\n * @constructor\n * @param {String} action the name of the pako function to call : either \"Deflate\" or \"Inflate\".\n * @param {Object} options the options to use when (de)compressing.\n */\nfunction FlateWorker(action, options) {\n    GenericWorker.call(this, \"FlateWorker/\" + action);\n\n    this._pako = null;\n    this._pakoAction = action;\n    this._pakoOptions = options;\n    // the `meta` object from the last chunk received\n    // this allow this worker to pass around metadata\n    this.meta = {};\n}\n\nutils.inherits(FlateWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nFlateWorker.prototype.processChunk = function (chunk) {\n    this.meta = chunk.meta;\n    if (this._pako === null) {\n        this._createPako();\n    }\n    this._pako.push(utils.transformTo(ARRAY_TYPE, chunk.data), false);\n};\n\n/**\n * @see GenericWorker.flush\n */\nFlateWorker.prototype.flush = function () {\n    GenericWorker.prototype.flush.call(this);\n    if (this._pako === null) {\n        this._createPako();\n    }\n    this._pako.push([], true);\n};\n/**\n * @see GenericWorker.cleanUp\n */\nFlateWorker.prototype.cleanUp = function () {\n    GenericWorker.prototype.cleanUp.call(this);\n    this._pako = null;\n};\n\n/**\n * Create the _pako object.\n * TODO: lazy-loading this object isn't the best solution but it's the\n * quickest. The best solution is to lazy-load the worker list. See also the\n * issue #446.\n */\nFlateWorker.prototype._createPako = function () {\n    this._pako = new pako[this._pakoAction]({\n        raw: true,\n        level: this._pakoOptions.level || -1 // default compression\n    });\n    var self = this;\n    this._pako.onData = function(data) {\n        self.push({\n            data : data,\n            meta : self.meta\n        });\n    };\n};\n\nexports.compressWorker = function (compressionOptions) {\n    return new FlateWorker(\"Deflate\", compressionOptions);\n};\nexports.uncompressWorker = function () {\n    return new FlateWorker(\"Inflate\", {});\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/flate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/generate/ZipFileWorker.js":
/*!**********************************************************!*\
  !*** ./node_modules/jszip/lib/generate/ZipFileWorker.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ../stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar utf8 = __webpack_require__(/*! ../utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar crc32 = __webpack_require__(/*! ../crc32 */ \"(ssr)/./node_modules/jszip/lib/crc32.js\");\nvar signature = __webpack_require__(/*! ../signature */ \"(ssr)/./node_modules/jszip/lib/signature.js\");\n\n/**\n * Transform an integer into a string in hexadecimal.\n * @private\n * @param {number} dec the number to convert.\n * @param {number} bytes the number of bytes to generate.\n * @returns {string} the result.\n */\nvar decToHex = function(dec, bytes) {\n    var hex = \"\", i;\n    for (i = 0; i < bytes; i++) {\n        hex += String.fromCharCode(dec & 0xff);\n        dec = dec >>> 8;\n    }\n    return hex;\n};\n\n/**\n * Generate the UNIX part of the external file attributes.\n * @param {Object} unixPermissions the unix permissions or null.\n * @param {Boolean} isDir true if the entry is a directory, false otherwise.\n * @return {Number} a 32 bit integer.\n *\n * adapted from http://unix.stackexchange.com/questions/14705/the-zip-formats-external-file-attribute :\n *\n * TTTTsstrwxrwxrwx0000000000ADVSHR\n * ^^^^____________________________ file type, see zipinfo.c (UNX_*)\n *     ^^^_________________________ setuid, setgid, sticky\n *        ^^^^^^^^^________________ permissions\n *                 ^^^^^^^^^^______ not used ?\n *                           ^^^^^^ DOS attribute bits : Archive, Directory, Volume label, System file, Hidden, Read only\n */\nvar generateUnixExternalFileAttr = function (unixPermissions, isDir) {\n\n    var result = unixPermissions;\n    if (!unixPermissions) {\n        // I can't use octal values in strict mode, hence the hexa.\n        //  040775 => 0x41fd\n        // 0100664 => 0x81b4\n        result = isDir ? 0x41fd : 0x81b4;\n    }\n    return (result & 0xFFFF) << 16;\n};\n\n/**\n * Generate the DOS part of the external file attributes.\n * @param {Object} dosPermissions the dos permissions or null.\n * @param {Boolean} isDir true if the entry is a directory, false otherwise.\n * @return {Number} a 32 bit integer.\n *\n * Bit 0     Read-Only\n * Bit 1     Hidden\n * Bit 2     System\n * Bit 3     Volume Label\n * Bit 4     Directory\n * Bit 5     Archive\n */\nvar generateDosExternalFileAttr = function (dosPermissions) {\n    // the dir flag is already set for compatibility\n    return (dosPermissions || 0)  & 0x3F;\n};\n\n/**\n * Generate the various parts used in the construction of the final zip file.\n * @param {Object} streamInfo the hash with information about the compressed file.\n * @param {Boolean} streamedContent is the content streamed ?\n * @param {Boolean} streamingEnded is the stream finished ?\n * @param {number} offset the current offset from the start of the zip file.\n * @param {String} platform let's pretend we are this platform (change platform dependents fields)\n * @param {Function} encodeFileName the function to encode the file name / comment.\n * @return {Object} the zip parts.\n */\nvar generateZipParts = function(streamInfo, streamedContent, streamingEnded, offset, platform, encodeFileName) {\n    var file = streamInfo[\"file\"],\n        compression = streamInfo[\"compression\"],\n        useCustomEncoding = encodeFileName !== utf8.utf8encode,\n        encodedFileName = utils.transformTo(\"string\", encodeFileName(file.name)),\n        utfEncodedFileName = utils.transformTo(\"string\", utf8.utf8encode(file.name)),\n        comment = file.comment,\n        encodedComment = utils.transformTo(\"string\", encodeFileName(comment)),\n        utfEncodedComment = utils.transformTo(\"string\", utf8.utf8encode(comment)),\n        useUTF8ForFileName = utfEncodedFileName.length !== file.name.length,\n        useUTF8ForComment = utfEncodedComment.length !== comment.length,\n        dosTime,\n        dosDate,\n        extraFields = \"\",\n        unicodePathExtraField = \"\",\n        unicodeCommentExtraField = \"\",\n        dir = file.dir,\n        date = file.date;\n\n\n    var dataInfo = {\n        crc32 : 0,\n        compressedSize : 0,\n        uncompressedSize : 0\n    };\n\n    // if the content is streamed, the sizes/crc32 are only available AFTER\n    // the end of the stream.\n    if (!streamedContent || streamingEnded) {\n        dataInfo.crc32 = streamInfo[\"crc32\"];\n        dataInfo.compressedSize = streamInfo[\"compressedSize\"];\n        dataInfo.uncompressedSize = streamInfo[\"uncompressedSize\"];\n    }\n\n    var bitflag = 0;\n    if (streamedContent) {\n        // Bit 3: the sizes/crc32 are set to zero in the local header.\n        // The correct values are put in the data descriptor immediately\n        // following the compressed data.\n        bitflag |= 0x0008;\n    }\n    if (!useCustomEncoding && (useUTF8ForFileName || useUTF8ForComment)) {\n        // Bit 11: Language encoding flag (EFS).\n        bitflag |= 0x0800;\n    }\n\n\n    var extFileAttr = 0;\n    var versionMadeBy = 0;\n    if (dir) {\n        // dos or unix, we set the dos dir flag\n        extFileAttr |= 0x00010;\n    }\n    if(platform === \"UNIX\") {\n        versionMadeBy = 0x031E; // UNIX, version 3.0\n        extFileAttr |= generateUnixExternalFileAttr(file.unixPermissions, dir);\n    } else { // DOS or other, fallback to DOS\n        versionMadeBy = 0x0014; // DOS, version 2.0\n        extFileAttr |= generateDosExternalFileAttr(file.dosPermissions, dir);\n    }\n\n    // date\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/52/13.html\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/65/16.html\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/66/16.html\n\n    dosTime = date.getUTCHours();\n    dosTime = dosTime << 6;\n    dosTime = dosTime | date.getUTCMinutes();\n    dosTime = dosTime << 5;\n    dosTime = dosTime | date.getUTCSeconds() / 2;\n\n    dosDate = date.getUTCFullYear() - 1980;\n    dosDate = dosDate << 4;\n    dosDate = dosDate | (date.getUTCMonth() + 1);\n    dosDate = dosDate << 5;\n    dosDate = dosDate | date.getUTCDate();\n\n    if (useUTF8ForFileName) {\n        // set the unicode path extra field. unzip needs at least one extra\n        // field to correctly handle unicode path, so using the path is as good\n        // as any other information. This could improve the situation with\n        // other archive managers too.\n        // This field is usually used without the utf8 flag, with a non\n        // unicode path in the header (winrar, winzip). This helps (a bit)\n        // with the messy Windows' default compressed folders feature but\n        // breaks on p7zip which doesn't seek the unicode path extra field.\n        // So for now, UTF-8 everywhere !\n        unicodePathExtraField =\n            // Version\n            decToHex(1, 1) +\n            // NameCRC32\n            decToHex(crc32(encodedFileName), 4) +\n            // UnicodeName\n            utfEncodedFileName;\n\n        extraFields +=\n            // Info-ZIP Unicode Path Extra Field\n            \"\\x75\\x70\" +\n            // size\n            decToHex(unicodePathExtraField.length, 2) +\n            // content\n            unicodePathExtraField;\n    }\n\n    if(useUTF8ForComment) {\n\n        unicodeCommentExtraField =\n            // Version\n            decToHex(1, 1) +\n            // CommentCRC32\n            decToHex(crc32(encodedComment), 4) +\n            // UnicodeName\n            utfEncodedComment;\n\n        extraFields +=\n            // Info-ZIP Unicode Path Extra Field\n            \"\\x75\\x63\" +\n            // size\n            decToHex(unicodeCommentExtraField.length, 2) +\n            // content\n            unicodeCommentExtraField;\n    }\n\n    var header = \"\";\n\n    // version needed to extract\n    header += \"\\x0A\\x00\";\n    // general purpose bit flag\n    header += decToHex(bitflag, 2);\n    // compression method\n    header += compression.magic;\n    // last mod file time\n    header += decToHex(dosTime, 2);\n    // last mod file date\n    header += decToHex(dosDate, 2);\n    // crc-32\n    header += decToHex(dataInfo.crc32, 4);\n    // compressed size\n    header += decToHex(dataInfo.compressedSize, 4);\n    // uncompressed size\n    header += decToHex(dataInfo.uncompressedSize, 4);\n    // file name length\n    header += decToHex(encodedFileName.length, 2);\n    // extra field length\n    header += decToHex(extraFields.length, 2);\n\n\n    var fileRecord = signature.LOCAL_FILE_HEADER + header + encodedFileName + extraFields;\n\n    var dirRecord = signature.CENTRAL_FILE_HEADER +\n        // version made by (00: DOS)\n        decToHex(versionMadeBy, 2) +\n        // file header (common to file and central directory)\n        header +\n        // file comment length\n        decToHex(encodedComment.length, 2) +\n        // disk number start\n        \"\\x00\\x00\" +\n        // internal file attributes TODO\n        \"\\x00\\x00\" +\n        // external file attributes\n        decToHex(extFileAttr, 4) +\n        // relative offset of local header\n        decToHex(offset, 4) +\n        // file name\n        encodedFileName +\n        // extra field\n        extraFields +\n        // file comment\n        encodedComment;\n\n    return {\n        fileRecord: fileRecord,\n        dirRecord: dirRecord\n    };\n};\n\n/**\n * Generate the EOCD record.\n * @param {Number} entriesCount the number of entries in the zip file.\n * @param {Number} centralDirLength the length (in bytes) of the central dir.\n * @param {Number} localDirLength the length (in bytes) of the local dir.\n * @param {String} comment the zip file comment as a binary string.\n * @param {Function} encodeFileName the function to encode the comment.\n * @return {String} the EOCD record.\n */\nvar generateCentralDirectoryEnd = function (entriesCount, centralDirLength, localDirLength, comment, encodeFileName) {\n    var dirEnd = \"\";\n    var encodedComment = utils.transformTo(\"string\", encodeFileName(comment));\n\n    // end of central dir signature\n    dirEnd = signature.CENTRAL_DIRECTORY_END +\n        // number of this disk\n        \"\\x00\\x00\" +\n        // number of the disk with the start of the central directory\n        \"\\x00\\x00\" +\n        // total number of entries in the central directory on this disk\n        decToHex(entriesCount, 2) +\n        // total number of entries in the central directory\n        decToHex(entriesCount, 2) +\n        // size of the central directory   4 bytes\n        decToHex(centralDirLength, 4) +\n        // offset of start of central directory with respect to the starting disk number\n        decToHex(localDirLength, 4) +\n        // .ZIP file comment length\n        decToHex(encodedComment.length, 2) +\n        // .ZIP file comment\n        encodedComment;\n\n    return dirEnd;\n};\n\n/**\n * Generate data descriptors for a file entry.\n * @param {Object} streamInfo the hash generated by a worker, containing information\n * on the file entry.\n * @return {String} the data descriptors.\n */\nvar generateDataDescriptors = function (streamInfo) {\n    var descriptor = \"\";\n    descriptor = signature.DATA_DESCRIPTOR +\n        // crc-32                          4 bytes\n        decToHex(streamInfo[\"crc32\"], 4) +\n        // compressed size                 4 bytes\n        decToHex(streamInfo[\"compressedSize\"], 4) +\n        // uncompressed size               4 bytes\n        decToHex(streamInfo[\"uncompressedSize\"], 4);\n\n    return descriptor;\n};\n\n\n/**\n * A worker to concatenate other workers to create a zip file.\n * @param {Boolean} streamFiles `true` to stream the content of the files,\n * `false` to accumulate it.\n * @param {String} comment the comment to use.\n * @param {String} platform the platform to use, \"UNIX\" or \"DOS\".\n * @param {Function} encodeFileName the function to encode file names and comments.\n */\nfunction ZipFileWorker(streamFiles, comment, platform, encodeFileName) {\n    GenericWorker.call(this, \"ZipFileWorker\");\n    // The number of bytes written so far. This doesn't count accumulated chunks.\n    this.bytesWritten = 0;\n    // The comment of the zip file\n    this.zipComment = comment;\n    // The platform \"generating\" the zip file.\n    this.zipPlatform = platform;\n    // the function to encode file names and comments.\n    this.encodeFileName = encodeFileName;\n    // Should we stream the content of the files ?\n    this.streamFiles = streamFiles;\n    // If `streamFiles` is false, we will need to accumulate the content of the\n    // files to calculate sizes / crc32 (and write them *before* the content).\n    // This boolean indicates if we are accumulating chunks (it will change a lot\n    // during the lifetime of this worker).\n    this.accumulate = false;\n    // The buffer receiving chunks when accumulating content.\n    this.contentBuffer = [];\n    // The list of generated directory records.\n    this.dirRecords = [];\n    // The offset (in bytes) from the beginning of the zip file for the current source.\n    this.currentSourceOffset = 0;\n    // The total number of entries in this zip file.\n    this.entriesCount = 0;\n    // the name of the file currently being added, null when handling the end of the zip file.\n    // Used for the emitted metadata.\n    this.currentFile = null;\n\n\n\n    this._sources = [];\n}\nutils.inherits(ZipFileWorker, GenericWorker);\n\n/**\n * @see GenericWorker.push\n */\nZipFileWorker.prototype.push = function (chunk) {\n\n    var currentFilePercent = chunk.meta.percent || 0;\n    var entriesCount = this.entriesCount;\n    var remainingFiles = this._sources.length;\n\n    if(this.accumulate) {\n        this.contentBuffer.push(chunk);\n    } else {\n        this.bytesWritten += chunk.data.length;\n\n        GenericWorker.prototype.push.call(this, {\n            data : chunk.data,\n            meta : {\n                currentFile : this.currentFile,\n                percent : entriesCount ? (currentFilePercent + 100 * (entriesCount - remainingFiles - 1)) / entriesCount : 100\n            }\n        });\n    }\n};\n\n/**\n * The worker started a new source (an other worker).\n * @param {Object} streamInfo the streamInfo object from the new source.\n */\nZipFileWorker.prototype.openedSource = function (streamInfo) {\n    this.currentSourceOffset = this.bytesWritten;\n    this.currentFile = streamInfo[\"file\"].name;\n\n    var streamedContent = this.streamFiles && !streamInfo[\"file\"].dir;\n\n    // don't stream folders (because they don't have any content)\n    if(streamedContent) {\n        var record = generateZipParts(streamInfo, streamedContent, false, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);\n        this.push({\n            data : record.fileRecord,\n            meta : {percent:0}\n        });\n    } else {\n        // we need to wait for the whole file before pushing anything\n        this.accumulate = true;\n    }\n};\n\n/**\n * The worker finished a source (an other worker).\n * @param {Object} streamInfo the streamInfo object from the finished source.\n */\nZipFileWorker.prototype.closedSource = function (streamInfo) {\n    this.accumulate = false;\n    var streamedContent = this.streamFiles && !streamInfo[\"file\"].dir;\n    var record = generateZipParts(streamInfo, streamedContent, true, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);\n\n    this.dirRecords.push(record.dirRecord);\n    if(streamedContent) {\n        // after the streamed file, we put data descriptors\n        this.push({\n            data : generateDataDescriptors(streamInfo),\n            meta : {percent:100}\n        });\n    } else {\n        // the content wasn't streamed, we need to push everything now\n        // first the file record, then the content\n        this.push({\n            data : record.fileRecord,\n            meta : {percent:0}\n        });\n        while(this.contentBuffer.length) {\n            this.push(this.contentBuffer.shift());\n        }\n    }\n    this.currentFile = null;\n};\n\n/**\n * @see GenericWorker.flush\n */\nZipFileWorker.prototype.flush = function () {\n\n    var localDirLength = this.bytesWritten;\n    for(var i = 0; i < this.dirRecords.length; i++) {\n        this.push({\n            data : this.dirRecords[i],\n            meta : {percent:100}\n        });\n    }\n    var centralDirLength = this.bytesWritten - localDirLength;\n\n    var dirEnd = generateCentralDirectoryEnd(this.dirRecords.length, centralDirLength, localDirLength, this.zipComment, this.encodeFileName);\n\n    this.push({\n        data : dirEnd,\n        meta : {percent:100}\n    });\n};\n\n/**\n * Prepare the next source to be read.\n */\nZipFileWorker.prototype.prepareNextSource = function () {\n    this.previous = this._sources.shift();\n    this.openedSource(this.previous.streamInfo);\n    if (this.isPaused) {\n        this.previous.pause();\n    } else {\n        this.previous.resume();\n    }\n};\n\n/**\n * @see GenericWorker.registerPrevious\n */\nZipFileWorker.prototype.registerPrevious = function (previous) {\n    this._sources.push(previous);\n    var self = this;\n\n    previous.on(\"data\", function (chunk) {\n        self.processChunk(chunk);\n    });\n    previous.on(\"end\", function () {\n        self.closedSource(self.previous.streamInfo);\n        if(self._sources.length) {\n            self.prepareNextSource();\n        } else {\n            self.end();\n        }\n    });\n    previous.on(\"error\", function (e) {\n        self.error(e);\n    });\n    return this;\n};\n\n/**\n * @see GenericWorker.resume\n */\nZipFileWorker.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if (!this.previous && this._sources.length) {\n        this.prepareNextSource();\n        return true;\n    }\n    if (!this.previous && !this._sources.length && !this.generatedError) {\n        this.end();\n        return true;\n    }\n};\n\n/**\n * @see GenericWorker.error\n */\nZipFileWorker.prototype.error = function (e) {\n    var sources = this._sources;\n    if(!GenericWorker.prototype.error.call(this, e)) {\n        return false;\n    }\n    for(var i = 0; i < sources.length; i++) {\n        try {\n            sources[i].error(e);\n        } catch(e) {\n            // the `error` exploded, nothing to do\n        }\n    }\n    return true;\n};\n\n/**\n * @see GenericWorker.lock\n */\nZipFileWorker.prototype.lock = function () {\n    GenericWorker.prototype.lock.call(this);\n    var sources = this._sources;\n    for(var i = 0; i < sources.length; i++) {\n        sources[i].lock();\n    }\n};\n\nmodule.exports = ZipFileWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/generate/ZipFileWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/generate/index.js":
/*!**************************************************!*\
  !*** ./node_modules/jszip/lib/generate/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar compressions = __webpack_require__(/*! ../compressions */ \"(ssr)/./node_modules/jszip/lib/compressions.js\");\nvar ZipFileWorker = __webpack_require__(/*! ./ZipFileWorker */ \"(ssr)/./node_modules/jszip/lib/generate/ZipFileWorker.js\");\n\n/**\n * Find the compression to use.\n * @param {String} fileCompression the compression defined at the file level, if any.\n * @param {String} zipCompression the compression defined at the load() level.\n * @return {Object} the compression object to use.\n */\nvar getCompression = function (fileCompression, zipCompression) {\n\n    var compressionName = fileCompression || zipCompression;\n    var compression = compressions[compressionName];\n    if (!compression) {\n        throw new Error(compressionName + \" is not a valid compression method !\");\n    }\n    return compression;\n};\n\n/**\n * Create a worker to generate a zip file.\n * @param {JSZip} zip the JSZip instance at the right root level.\n * @param {Object} options to generate the zip file.\n * @param {String} comment the comment to use.\n */\nexports.generateWorker = function (zip, options, comment) {\n\n    var zipFileWorker = new ZipFileWorker(options.streamFiles, comment, options.platform, options.encodeFileName);\n    var entriesCount = 0;\n    try {\n\n        zip.forEach(function (relativePath, file) {\n            entriesCount++;\n            var compression = getCompression(file.options.compression, options.compression);\n            var compressionOptions = file.options.compressionOptions || options.compressionOptions || {};\n            var dir = file.dir, date = file.date;\n\n            file._compressWorker(compression, compressionOptions)\n                .withStreamInfo(\"file\", {\n                    name : relativePath,\n                    dir : dir,\n                    date : date,\n                    comment : file.comment || \"\",\n                    unixPermissions : file.unixPermissions,\n                    dosPermissions : file.dosPermissions\n                })\n                .pipe(zipFileWorker);\n        });\n        zipFileWorker.entriesCount = entriesCount;\n    } catch (e) {\n        zipFileWorker.error(e);\n    }\n\n    return zipFileWorker;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/generate/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/jszip/lib/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/**\n * Representation a of zip file in js\n * @constructor\n */\nfunction JSZip() {\n    // if this constructor is used without `new`, it adds `new` before itself:\n    if(!(this instanceof JSZip)) {\n        return new JSZip();\n    }\n\n    if(arguments.length) {\n        throw new Error(\"The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.\");\n    }\n\n    // object containing the files :\n    // {\n    //   \"folder/\" : {...},\n    //   \"folder/data.txt\" : {...}\n    // }\n    // NOTE: we use a null prototype because we do not\n    // want filenames like \"toString\" coming from a zip file\n    // to overwrite methods and attributes in a normal Object.\n    this.files = Object.create(null);\n\n    this.comment = null;\n\n    // Where we are in the hierarchy\n    this.root = \"\";\n    this.clone = function() {\n        var newObj = new JSZip();\n        for (var i in this) {\n            if (typeof this[i] !== \"function\") {\n                newObj[i] = this[i];\n            }\n        }\n        return newObj;\n    };\n}\nJSZip.prototype = __webpack_require__(/*! ./object */ \"(ssr)/./node_modules/jszip/lib/object.js\");\nJSZip.prototype.loadAsync = __webpack_require__(/*! ./load */ \"(ssr)/./node_modules/jszip/lib/load.js\");\nJSZip.support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nJSZip.defaults = __webpack_require__(/*! ./defaults */ \"(ssr)/./node_modules/jszip/lib/defaults.js\");\n\n// TODO find a better way to handle this version,\n// a require('package.json').version doesn't work with webpack, see #327\nJSZip.version = \"3.10.1\";\n\nJSZip.loadAsync = function (content, options) {\n    return new JSZip().loadAsync(content, options);\n};\n\nJSZip.external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\nmodule.exports = JSZip;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/load.js":
/*!****************************************!*\
  !*** ./node_modules/jszip/lib/load.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar ZipEntries = __webpack_require__(/*! ./zipEntries */ \"(ssr)/./node_modules/jszip/lib/zipEntries.js\");\nvar Crc32Probe = __webpack_require__(/*! ./stream/Crc32Probe */ \"(ssr)/./node_modules/jszip/lib/stream/Crc32Probe.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/jszip/lib/nodejsUtils.js\");\n\n/**\n * Check the CRC32 of an entry.\n * @param {ZipEntry} zipEntry the zip entry to check.\n * @return {Promise} the result.\n */\nfunction checkEntryCRC32(zipEntry) {\n    return new external.Promise(function (resolve, reject) {\n        var worker = zipEntry.decompressed.getContentWorker().pipe(new Crc32Probe());\n        worker.on(\"error\", function (e) {\n            reject(e);\n        })\n            .on(\"end\", function () {\n                if (worker.streamInfo.crc32 !== zipEntry.decompressed.crc32) {\n                    reject(new Error(\"Corrupted zip : CRC32 mismatch\"));\n                } else {\n                    resolve();\n                }\n            })\n            .resume();\n    });\n}\n\nmodule.exports = function (data, options) {\n    var zip = this;\n    options = utils.extend(options || {}, {\n        base64: false,\n        checkCRC32: false,\n        optimizedBinaryString: false,\n        createFolders: false,\n        decodeFileName: utf8.utf8decode\n    });\n\n    if (nodejsUtils.isNode && nodejsUtils.isStream(data)) {\n        return external.Promise.reject(new Error(\"JSZip can't accept a stream when loading a zip file.\"));\n    }\n\n    return utils.prepareContent(\"the loaded zip file\", data, true, options.optimizedBinaryString, options.base64)\n        .then(function (data) {\n            var zipEntries = new ZipEntries(options);\n            zipEntries.load(data);\n            return zipEntries;\n        }).then(function checkCRC32(zipEntries) {\n            var promises = [external.Promise.resolve(zipEntries)];\n            var files = zipEntries.files;\n            if (options.checkCRC32) {\n                for (var i = 0; i < files.length; i++) {\n                    promises.push(checkEntryCRC32(files[i]));\n                }\n            }\n            return external.Promise.all(promises);\n        }).then(function addFiles(results) {\n            var zipEntries = results.shift();\n            var files = zipEntries.files;\n            for (var i = 0; i < files.length; i++) {\n                var input = files[i];\n\n                var unsafeName = input.fileNameStr;\n                var safeName = utils.resolve(input.fileNameStr);\n\n                zip.file(safeName, input.decompressed, {\n                    binary: true,\n                    optimizedBinaryString: true,\n                    date: input.date,\n                    dir: input.dir,\n                    comment: input.fileCommentStr.length ? input.fileCommentStr : null,\n                    unixPermissions: input.unixPermissions,\n                    dosPermissions: input.dosPermissions,\n                    createFolders: options.createFolders\n                });\n                if (!input.dir) {\n                    zip.file(safeName).unsafeOriginalName = unsafeName;\n                }\n            }\n            if (zipEntries.zipComment.length) {\n                zip.comment = zipEntries.zipComment;\n            }\n\n            return zip;\n        });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/load.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/nodejsUtils.js":
/*!***********************************************!*\
  !*** ./node_modules/jszip/lib/nodejsUtils.js ***!
  \***********************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n    /**\n     * True if this is running in Nodejs, will be undefined in a browser.\n     * In a browser, browserify won't include this file and the whole module\n     * will be resolved an empty object.\n     */\n    isNode : typeof Buffer !== \"undefined\",\n    /**\n     * Create a new nodejs Buffer from an existing content.\n     * @param {Object} data the data to pass to the constructor.\n     * @param {String} encoding the encoding to use.\n     * @return {Buffer} a new Buffer.\n     */\n    newBufferFrom: function(data, encoding) {\n        if (Buffer.from && Buffer.from !== Uint8Array.from) {\n            return Buffer.from(data, encoding);\n        } else {\n            if (typeof data === \"number\") {\n                // Safeguard for old Node.js versions. On newer versions,\n                // Buffer.from(number) / Buffer(number, encoding) already throw.\n                throw new Error(\"The \\\"data\\\" argument must not be a number\");\n            }\n            return new Buffer(data, encoding);\n        }\n    },\n    /**\n     * Create a new nodejs Buffer with the specified size.\n     * @param {Integer} size the size of the buffer.\n     * @return {Buffer} a new Buffer.\n     */\n    allocBuffer: function (size) {\n        if (Buffer.alloc) {\n            return Buffer.alloc(size);\n        } else {\n            var buf = new Buffer(size);\n            buf.fill(0);\n            return buf;\n        }\n    },\n    /**\n     * Find out if an object is a Buffer.\n     * @param {Object} b the object to test.\n     * @return {Boolean} true if the object is a Buffer, false otherwise.\n     */\n    isBuffer : function(b){\n        return Buffer.isBuffer(b);\n    },\n\n    isStream : function (obj) {\n        return obj &&\n            typeof obj.on === \"function\" &&\n            typeof obj.pause === \"function\" &&\n            typeof obj.resume === \"function\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/nodejsUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js":
/*!*******************************************************************!*\
  !*** ./node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ../stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * A worker that use a nodejs stream as source.\n * @constructor\n * @param {String} filename the name of the file entry for this stream.\n * @param {Readable} stream the nodejs stream.\n */\nfunction NodejsStreamInputAdapter(filename, stream) {\n    GenericWorker.call(this, \"Nodejs stream input adapter for \" + filename);\n    this._upstreamEnded = false;\n    this._bindStream(stream);\n}\n\nutils.inherits(NodejsStreamInputAdapter, GenericWorker);\n\n/**\n * Prepare the stream and bind the callbacks on it.\n * Do this ASAP on node 0.10 ! A lazy binding doesn't always work.\n * @param {Stream} stream the nodejs stream to use.\n */\nNodejsStreamInputAdapter.prototype._bindStream = function (stream) {\n    var self = this;\n    this._stream = stream;\n    stream.pause();\n    stream\n        .on(\"data\", function (chunk) {\n            self.push({\n                data: chunk,\n                meta : {\n                    percent : 0\n                }\n            });\n        })\n        .on(\"error\", function (e) {\n            if(self.isPaused) {\n                this.generatedError = e;\n            } else {\n                self.error(e);\n            }\n        })\n        .on(\"end\", function () {\n            if(self.isPaused) {\n                self._upstreamEnded = true;\n            } else {\n                self.end();\n            }\n        });\n};\nNodejsStreamInputAdapter.prototype.pause = function () {\n    if(!GenericWorker.prototype.pause.call(this)) {\n        return false;\n    }\n    this._stream.pause();\n    return true;\n};\nNodejsStreamInputAdapter.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if(this._upstreamEnded) {\n        this.end();\n    } else {\n        this._stream.resume();\n    }\n\n    return true;\n};\n\nmodule.exports = NodejsStreamInputAdapter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js":
/*!********************************************************************!*\
  !*** ./node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Readable = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Readable);\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nutils.inherits(NodejsStreamOutputAdapter, Readable);\n\n/**\n* A nodejs stream using a worker as source.\n* @see the SourceWrapper in http://nodejs.org/api/stream.html\n* @constructor\n* @param {StreamHelper} helper the helper wrapping the worker\n* @param {Object} options the nodejs stream options\n* @param {Function} updateCb the update callback.\n*/\nfunction NodejsStreamOutputAdapter(helper, options, updateCb) {\n    Readable.call(this, options);\n    this._helper = helper;\n\n    var self = this;\n    helper.on(\"data\", function (data, meta) {\n        if (!self.push(data)) {\n            self._helper.pause();\n        }\n        if(updateCb) {\n            updateCb(meta);\n        }\n    })\n        .on(\"error\", function(e) {\n            self.emit(\"error\", e);\n        })\n        .on(\"end\", function () {\n            self.push(null);\n        });\n}\n\n\nNodejsStreamOutputAdapter.prototype._read = function() {\n    this._helper.resume();\n};\n\nmodule.exports = NodejsStreamOutputAdapter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/object.js":
/*!******************************************!*\
  !*** ./node_modules/jszip/lib/object.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar StreamHelper = __webpack_require__(/*! ./stream/StreamHelper */ \"(ssr)/./node_modules/jszip/lib/stream/StreamHelper.js\");\nvar defaults = __webpack_require__(/*! ./defaults */ \"(ssr)/./node_modules/jszip/lib/defaults.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/./node_modules/jszip/lib/compressedObject.js\");\nvar ZipObject = __webpack_require__(/*! ./zipObject */ \"(ssr)/./node_modules/jszip/lib/zipObject.js\");\nvar generate = __webpack_require__(/*! ./generate */ \"(ssr)/./node_modules/jszip/lib/generate/index.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/jszip/lib/nodejsUtils.js\");\nvar NodejsStreamInputAdapter = __webpack_require__(/*! ./nodejs/NodejsStreamInputAdapter */ \"(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js\");\n\n\n/**\n * Add a file in the current folder.\n * @private\n * @param {string} name the name of the file\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data of the file\n * @param {Object} originalOptions the options of the file\n * @return {Object} the new file.\n */\nvar fileAdd = function(name, data, originalOptions) {\n    // be sure sub folders exist\n    var dataType = utils.getTypeOf(data),\n        parent;\n\n\n    /*\n     * Correct options.\n     */\n\n    var o = utils.extend(originalOptions || {}, defaults);\n    o.date = o.date || new Date();\n    if (o.compression !== null) {\n        o.compression = o.compression.toUpperCase();\n    }\n\n    if (typeof o.unixPermissions === \"string\") {\n        o.unixPermissions = parseInt(o.unixPermissions, 8);\n    }\n\n    // UNX_IFDIR  0040000 see zipinfo.c\n    if (o.unixPermissions && (o.unixPermissions & 0x4000)) {\n        o.dir = true;\n    }\n    // Bit 4    Directory\n    if (o.dosPermissions && (o.dosPermissions & 0x0010)) {\n        o.dir = true;\n    }\n\n    if (o.dir) {\n        name = forceTrailingSlash(name);\n    }\n    if (o.createFolders && (parent = parentFolder(name))) {\n        folderAdd.call(this, parent, true);\n    }\n\n    var isUnicodeString = dataType === \"string\" && o.binary === false && o.base64 === false;\n    if (!originalOptions || typeof originalOptions.binary === \"undefined\") {\n        o.binary = !isUnicodeString;\n    }\n\n\n    var isCompressedEmpty = (data instanceof CompressedObject) && data.uncompressedSize === 0;\n\n    if (isCompressedEmpty || o.dir || !data || data.length === 0) {\n        o.base64 = false;\n        o.binary = true;\n        data = \"\";\n        o.compression = \"STORE\";\n        dataType = \"string\";\n    }\n\n    /*\n     * Convert content to fit.\n     */\n\n    var zipObjectContent = null;\n    if (data instanceof CompressedObject || data instanceof GenericWorker) {\n        zipObjectContent = data;\n    } else if (nodejsUtils.isNode && nodejsUtils.isStream(data)) {\n        zipObjectContent = new NodejsStreamInputAdapter(name, data);\n    } else {\n        zipObjectContent = utils.prepareContent(name, data, o.binary, o.optimizedBinaryString, o.base64);\n    }\n\n    var object = new ZipObject(name, zipObjectContent, o);\n    this.files[name] = object;\n    /*\n    TODO: we can't throw an exception because we have async promises\n    (we can have a promise of a Date() for example) but returning a\n    promise is useless because file(name, data) returns the JSZip\n    object for chaining. Should we break that to allow the user\n    to catch the error ?\n\n    return external.Promise.resolve(zipObjectContent)\n    .then(function () {\n        return object;\n    });\n    */\n};\n\n/**\n * Find the parent folder of the path.\n * @private\n * @param {string} path the path to use\n * @return {string} the parent folder, or \"\"\n */\nvar parentFolder = function (path) {\n    if (path.slice(-1) === \"/\") {\n        path = path.substring(0, path.length - 1);\n    }\n    var lastSlash = path.lastIndexOf(\"/\");\n    return (lastSlash > 0) ? path.substring(0, lastSlash) : \"\";\n};\n\n/**\n * Returns the path with a slash at the end.\n * @private\n * @param {String} path the path to check.\n * @return {String} the path with a trailing slash.\n */\nvar forceTrailingSlash = function(path) {\n    // Check the name ends with a /\n    if (path.slice(-1) !== \"/\") {\n        path += \"/\"; // IE doesn't like substr(-1)\n    }\n    return path;\n};\n\n/**\n * Add a (sub) folder in the current folder.\n * @private\n * @param {string} name the folder's name\n * @param {boolean=} [createFolders] If true, automatically create sub\n *  folders. Defaults to false.\n * @return {Object} the new folder.\n */\nvar folderAdd = function(name, createFolders) {\n    createFolders = (typeof createFolders !== \"undefined\") ? createFolders : defaults.createFolders;\n\n    name = forceTrailingSlash(name);\n\n    // Does this folder already exist?\n    if (!this.files[name]) {\n        fileAdd.call(this, name, null, {\n            dir: true,\n            createFolders: createFolders\n        });\n    }\n    return this.files[name];\n};\n\n/**\n* Cross-window, cross-Node-context regular expression detection\n* @param  {Object}  object Anything\n* @return {Boolean}        true if the object is a regular expression,\n* false otherwise\n*/\nfunction isRegExp(object) {\n    return Object.prototype.toString.call(object) === \"[object RegExp]\";\n}\n\n// return the actual prototype of JSZip\nvar out = {\n    /**\n     * @see loadAsync\n     */\n    load: function() {\n        throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n    },\n\n\n    /**\n     * Call a callback function for each entry at this folder level.\n     * @param {Function} cb the callback function:\n     * function (relativePath, file) {...}\n     * It takes 2 arguments : the relative path and the file.\n     */\n    forEach: function(cb) {\n        var filename, relativePath, file;\n        // ignore warning about unwanted properties because this.files is a null prototype object\n        /* eslint-disable-next-line guard-for-in */\n        for (filename in this.files) {\n            file = this.files[filename];\n            relativePath = filename.slice(this.root.length, filename.length);\n            if (relativePath && filename.slice(0, this.root.length) === this.root) { // the file is in the current root\n                cb(relativePath, file); // TODO reverse the parameters ? need to be clean AND consistent with the filter search fn...\n            }\n        }\n    },\n\n    /**\n     * Filter nested files/folders with the specified function.\n     * @param {Function} search the predicate to use :\n     * function (relativePath, file) {...}\n     * It takes 2 arguments : the relative path and the file.\n     * @return {Array} An array of matching elements.\n     */\n    filter: function(search) {\n        var result = [];\n        this.forEach(function (relativePath, entry) {\n            if (search(relativePath, entry)) { // the file matches the function\n                result.push(entry);\n            }\n\n        });\n        return result;\n    },\n\n    /**\n     * Add a file to the zip file, or search a file.\n     * @param   {string|RegExp} name The name of the file to add (if data is defined),\n     * the name of the file to find (if no data) or a regex to match files.\n     * @param   {String|ArrayBuffer|Uint8Array|Buffer} data  The file data, either raw or base64 encoded\n     * @param   {Object} o     File options\n     * @return  {JSZip|Object|Array} this JSZip object (when adding a file),\n     * a file (when searching by string) or an array of files (when searching by regex).\n     */\n    file: function(name, data, o) {\n        if (arguments.length === 1) {\n            if (isRegExp(name)) {\n                var regexp = name;\n                return this.filter(function(relativePath, file) {\n                    return !file.dir && regexp.test(relativePath);\n                });\n            }\n            else { // text\n                var obj = this.files[this.root + name];\n                if (obj && !obj.dir) {\n                    return obj;\n                } else {\n                    return null;\n                }\n            }\n        }\n        else { // more than one argument : we have data !\n            name = this.root + name;\n            fileAdd.call(this, name, data, o);\n        }\n        return this;\n    },\n\n    /**\n     * Add a directory to the zip file, or search.\n     * @param   {String|RegExp} arg The name of the directory to add, or a regex to search folders.\n     * @return  {JSZip} an object with the new directory as the root, or an array containing matching folders.\n     */\n    folder: function(arg) {\n        if (!arg) {\n            return this;\n        }\n\n        if (isRegExp(arg)) {\n            return this.filter(function(relativePath, file) {\n                return file.dir && arg.test(relativePath);\n            });\n        }\n\n        // else, name is a new folder\n        var name = this.root + arg;\n        var newFolder = folderAdd.call(this, name);\n\n        // Allow chaining by returning a new object with this folder as the root\n        var ret = this.clone();\n        ret.root = newFolder.name;\n        return ret;\n    },\n\n    /**\n     * Delete a file, or a directory and all sub-files, from the zip\n     * @param {string} name the name of the file to delete\n     * @return {JSZip} this JSZip object\n     */\n    remove: function(name) {\n        name = this.root + name;\n        var file = this.files[name];\n        if (!file) {\n            // Look for any folders\n            if (name.slice(-1) !== \"/\") {\n                name += \"/\";\n            }\n            file = this.files[name];\n        }\n\n        if (file && !file.dir) {\n            // file\n            delete this.files[name];\n        } else {\n            // maybe a folder, delete recursively\n            var kids = this.filter(function(relativePath, file) {\n                return file.name.slice(0, name.length) === name;\n            });\n            for (var i = 0; i < kids.length; i++) {\n                delete this.files[kids[i].name];\n            }\n        }\n\n        return this;\n    },\n\n    /**\n     * @deprecated This method has been removed in JSZip 3.0, please check the upgrade guide.\n     */\n    generate: function() {\n        throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n    },\n\n    /**\n     * Generate the complete zip file as an internal stream.\n     * @param {Object} options the options to generate the zip file :\n     * - compression, \"STORE\" by default.\n     * - type, \"base64\" by default. Values are : string, base64, uint8array, arraybuffer, blob.\n     * @return {StreamHelper} the streamed zip file.\n     */\n    generateInternalStream: function(options) {\n        var worker, opts = {};\n        try {\n            opts = utils.extend(options || {}, {\n                streamFiles: false,\n                compression: \"STORE\",\n                compressionOptions : null,\n                type: \"\",\n                platform: \"DOS\",\n                comment: null,\n                mimeType: \"application/zip\",\n                encodeFileName: utf8.utf8encode\n            });\n\n            opts.type = opts.type.toLowerCase();\n            opts.compression = opts.compression.toUpperCase();\n\n            // \"binarystring\" is preferred but the internals use \"string\".\n            if(opts.type === \"binarystring\") {\n                opts.type = \"string\";\n            }\n\n            if (!opts.type) {\n                throw new Error(\"No output type specified.\");\n            }\n\n            utils.checkSupport(opts.type);\n\n            // accept nodejs `process.platform`\n            if(\n                opts.platform === \"darwin\" ||\n                opts.platform === \"freebsd\" ||\n                opts.platform === \"linux\" ||\n                opts.platform === \"sunos\"\n            ) {\n                opts.platform = \"UNIX\";\n            }\n            if (opts.platform === \"win32\") {\n                opts.platform = \"DOS\";\n            }\n\n            var comment = opts.comment || this.comment || \"\";\n            worker = generate.generateWorker(this, opts, comment);\n        } catch (e) {\n            worker = new GenericWorker(\"error\");\n            worker.error(e);\n        }\n        return new StreamHelper(worker, opts.type || \"string\", opts.mimeType);\n    },\n    /**\n     * Generate the complete zip file asynchronously.\n     * @see generateInternalStream\n     */\n    generateAsync: function(options, onUpdate) {\n        return this.generateInternalStream(options).accumulate(onUpdate);\n    },\n    /**\n     * Generate the complete zip file asynchronously.\n     * @see generateInternalStream\n     */\n    generateNodeStream: function(options, onUpdate) {\n        options = options || {};\n        if (!options.type) {\n            options.type = \"nodebuffer\";\n        }\n        return this.generateInternalStream(options).toNodejsStream(onUpdate);\n    }\n};\nmodule.exports = out;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/object.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/ArrayReader.js":
/*!******************************************************!*\
  !*** ./node_modules/jszip/lib/reader/ArrayReader.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar DataReader = __webpack_require__(/*! ./DataReader */ \"(ssr)/./node_modules/jszip/lib/reader/DataReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\nfunction ArrayReader(data) {\n    DataReader.call(this, data);\n    for(var i = 0; i < this.data.length; i++) {\n        data[i] = data[i] & 0xFF;\n    }\n}\nutils.inherits(ArrayReader, DataReader);\n/**\n * @see DataReader.byteAt\n */\nArrayReader.prototype.byteAt = function(i) {\n    return this.data[this.zero + i];\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nArrayReader.prototype.lastIndexOfSignature = function(sig) {\n    var sig0 = sig.charCodeAt(0),\n        sig1 = sig.charCodeAt(1),\n        sig2 = sig.charCodeAt(2),\n        sig3 = sig.charCodeAt(3);\n    for (var i = this.length - 4; i >= 0; --i) {\n        if (this.data[i] === sig0 && this.data[i + 1] === sig1 && this.data[i + 2] === sig2 && this.data[i + 3] === sig3) {\n            return i - this.zero;\n        }\n    }\n\n    return -1;\n};\n/**\n * @see DataReader.readAndCheckSignature\n */\nArrayReader.prototype.readAndCheckSignature = function (sig) {\n    var sig0 = sig.charCodeAt(0),\n        sig1 = sig.charCodeAt(1),\n        sig2 = sig.charCodeAt(2),\n        sig3 = sig.charCodeAt(3),\n        data = this.readData(4);\n    return sig0 === data[0] && sig1 === data[1] && sig2 === data[2] && sig3 === data[3];\n};\n/**\n * @see DataReader.readData\n */\nArrayReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    if(size === 0) {\n        return [];\n    }\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = ArrayReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/ArrayReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/DataReader.js":
/*!*****************************************************!*\
  !*** ./node_modules/jszip/lib/reader/DataReader.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\nfunction DataReader(data) {\n    this.data = data; // type : see implementation\n    this.length = data.length;\n    this.index = 0;\n    this.zero = 0;\n}\nDataReader.prototype = {\n    /**\n     * Check that the offset will not go too far.\n     * @param {string} offset the additional offset to check.\n     * @throws {Error} an Error if the offset is out of bounds.\n     */\n    checkOffset: function(offset) {\n        this.checkIndex(this.index + offset);\n    },\n    /**\n     * Check that the specified index will not be too far.\n     * @param {string} newIndex the index to check.\n     * @throws {Error} an Error if the index is out of bounds.\n     */\n    checkIndex: function(newIndex) {\n        if (this.length < this.zero + newIndex || newIndex < 0) {\n            throw new Error(\"End of data reached (data length = \" + this.length + \", asked index = \" + (newIndex) + \"). Corrupted zip ?\");\n        }\n    },\n    /**\n     * Change the index.\n     * @param {number} newIndex The new index.\n     * @throws {Error} if the new index is out of the data.\n     */\n    setIndex: function(newIndex) {\n        this.checkIndex(newIndex);\n        this.index = newIndex;\n    },\n    /**\n     * Skip the next n bytes.\n     * @param {number} n the number of bytes to skip.\n     * @throws {Error} if the new index is out of the data.\n     */\n    skip: function(n) {\n        this.setIndex(this.index + n);\n    },\n    /**\n     * Get the byte at the specified index.\n     * @param {number} i the index to use.\n     * @return {number} a byte.\n     */\n    byteAt: function() {\n        // see implementations\n    },\n    /**\n     * Get the next number with a given byte size.\n     * @param {number} size the number of bytes to read.\n     * @return {number} the corresponding number.\n     */\n    readInt: function(size) {\n        var result = 0,\n            i;\n        this.checkOffset(size);\n        for (i = this.index + size - 1; i >= this.index; i--) {\n            result = (result << 8) + this.byteAt(i);\n        }\n        this.index += size;\n        return result;\n    },\n    /**\n     * Get the next string with a given byte size.\n     * @param {number} size the number of bytes to read.\n     * @return {string} the corresponding string.\n     */\n    readString: function(size) {\n        return utils.transformTo(\"string\", this.readData(size));\n    },\n    /**\n     * Get raw data without conversion, <size> bytes.\n     * @param {number} size the number of bytes to read.\n     * @return {Object} the raw data, implementation specific.\n     */\n    readData: function() {\n        // see implementations\n    },\n    /**\n     * Find the last occurrence of a zip signature (4 bytes).\n     * @param {string} sig the signature to find.\n     * @return {number} the index of the last occurrence, -1 if not found.\n     */\n    lastIndexOfSignature: function() {\n        // see implementations\n    },\n    /**\n     * Read the signature (4 bytes) at the current position and compare it with sig.\n     * @param {string} sig the expected signature\n     * @return {boolean} true if the signature matches, false otherwise.\n     */\n    readAndCheckSignature: function() {\n        // see implementations\n    },\n    /**\n     * Get the next date.\n     * @return {Date} the date.\n     */\n    readDate: function() {\n        var dostime = this.readInt(4);\n        return new Date(Date.UTC(\n            ((dostime >> 25) & 0x7f) + 1980, // year\n            ((dostime >> 21) & 0x0f) - 1, // month\n            (dostime >> 16) & 0x1f, // day\n            (dostime >> 11) & 0x1f, // hour\n            (dostime >> 5) & 0x3f, // minute\n            (dostime & 0x1f) << 1)); // second\n    }\n};\nmodule.exports = DataReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/DataReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/NodeBufferReader.js":
/*!***********************************************************!*\
  !*** ./node_modules/jszip/lib/reader/NodeBufferReader.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar Uint8ArrayReader = __webpack_require__(/*! ./Uint8ArrayReader */ \"(ssr)/./node_modules/jszip/lib/reader/Uint8ArrayReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\nfunction NodeBufferReader(data) {\n    Uint8ArrayReader.call(this, data);\n}\nutils.inherits(NodeBufferReader, Uint8ArrayReader);\n\n/**\n * @see DataReader.readData\n */\nNodeBufferReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = NodeBufferReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3JlYWRlci9Ob2RlQnVmZmVyUmVhZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsdUJBQXVCLG1CQUFPLENBQUMscUZBQW9CO0FBQ25ELFlBQVksbUJBQU8sQ0FBQyx5REFBVTs7QUFFOUI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvcmVhZGVyL05vZGVCdWZmZXJSZWFkZXIuanM/MTE5MCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBVaW50OEFycmF5UmVhZGVyID0gcmVxdWlyZShcIi4vVWludDhBcnJheVJlYWRlclwiKTtcbnZhciB1dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcblxuZnVuY3Rpb24gTm9kZUJ1ZmZlclJlYWRlcihkYXRhKSB7XG4gICAgVWludDhBcnJheVJlYWRlci5jYWxsKHRoaXMsIGRhdGEpO1xufVxudXRpbHMuaW5oZXJpdHMoTm9kZUJ1ZmZlclJlYWRlciwgVWludDhBcnJheVJlYWRlcik7XG5cbi8qKlxuICogQHNlZSBEYXRhUmVhZGVyLnJlYWREYXRhXG4gKi9cbk5vZGVCdWZmZXJSZWFkZXIucHJvdG90eXBlLnJlYWREYXRhID0gZnVuY3Rpb24oc2l6ZSkge1xuICAgIHRoaXMuY2hlY2tPZmZzZXQoc2l6ZSk7XG4gICAgdmFyIHJlc3VsdCA9IHRoaXMuZGF0YS5zbGljZSh0aGlzLnplcm8gKyB0aGlzLmluZGV4LCB0aGlzLnplcm8gKyB0aGlzLmluZGV4ICsgc2l6ZSk7XG4gICAgdGhpcy5pbmRleCArPSBzaXplO1xuICAgIHJldHVybiByZXN1bHQ7XG59O1xubW9kdWxlLmV4cG9ydHMgPSBOb2RlQnVmZmVyUmVhZGVyO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/NodeBufferReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/StringReader.js":
/*!*******************************************************!*\
  !*** ./node_modules/jszip/lib/reader/StringReader.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar DataReader = __webpack_require__(/*! ./DataReader */ \"(ssr)/./node_modules/jszip/lib/reader/DataReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\nfunction StringReader(data) {\n    DataReader.call(this, data);\n}\nutils.inherits(StringReader, DataReader);\n/**\n * @see DataReader.byteAt\n */\nStringReader.prototype.byteAt = function(i) {\n    return this.data.charCodeAt(this.zero + i);\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nStringReader.prototype.lastIndexOfSignature = function(sig) {\n    return this.data.lastIndexOf(sig) - this.zero;\n};\n/**\n * @see DataReader.readAndCheckSignature\n */\nStringReader.prototype.readAndCheckSignature = function (sig) {\n    var data = this.readData(4);\n    return sig === data;\n};\n/**\n * @see DataReader.readData\n */\nStringReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    // this will work because the constructor applied the \"& 0xff\" mask.\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = StringReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/StringReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/Uint8ArrayReader.js":
/*!***********************************************************!*\
  !*** ./node_modules/jszip/lib/reader/Uint8ArrayReader.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar ArrayReader = __webpack_require__(/*! ./ArrayReader */ \"(ssr)/./node_modules/jszip/lib/reader/ArrayReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\nfunction Uint8ArrayReader(data) {\n    ArrayReader.call(this, data);\n}\nutils.inherits(Uint8ArrayReader, ArrayReader);\n/**\n * @see DataReader.readData\n */\nUint8ArrayReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    if(size === 0) {\n        // in IE10, when using subarray(idx, idx), we get the array [0x00] instead of [].\n        return new Uint8Array(0);\n    }\n    var result = this.data.subarray(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = Uint8ArrayReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3JlYWRlci9VaW50OEFycmF5UmVhZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2Isa0JBQWtCLG1CQUFPLENBQUMsMkVBQWU7QUFDekMsWUFBWSxtQkFBTyxDQUFDLHlEQUFVOztBQUU5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvcmVhZGVyL1VpbnQ4QXJyYXlSZWFkZXIuanM/MmQxNiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBBcnJheVJlYWRlciA9IHJlcXVpcmUoXCIuL0FycmF5UmVhZGVyXCIpO1xudmFyIHV0aWxzID0gcmVxdWlyZShcIi4uL3V0aWxzXCIpO1xuXG5mdW5jdGlvbiBVaW50OEFycmF5UmVhZGVyKGRhdGEpIHtcbiAgICBBcnJheVJlYWRlci5jYWxsKHRoaXMsIGRhdGEpO1xufVxudXRpbHMuaW5oZXJpdHMoVWludDhBcnJheVJlYWRlciwgQXJyYXlSZWFkZXIpO1xuLyoqXG4gKiBAc2VlIERhdGFSZWFkZXIucmVhZERhdGFcbiAqL1xuVWludDhBcnJheVJlYWRlci5wcm90b3R5cGUucmVhZERhdGEgPSBmdW5jdGlvbihzaXplKSB7XG4gICAgdGhpcy5jaGVja09mZnNldChzaXplKTtcbiAgICBpZihzaXplID09PSAwKSB7XG4gICAgICAgIC8vIGluIElFMTAsIHdoZW4gdXNpbmcgc3ViYXJyYXkoaWR4LCBpZHgpLCB3ZSBnZXQgdGhlIGFycmF5IFsweDAwXSBpbnN0ZWFkIG9mIFtdLlxuICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoMCk7XG4gICAgfVxuICAgIHZhciByZXN1bHQgPSB0aGlzLmRhdGEuc3ViYXJyYXkodGhpcy56ZXJvICsgdGhpcy5pbmRleCwgdGhpcy56ZXJvICsgdGhpcy5pbmRleCArIHNpemUpO1xuICAgIHRoaXMuaW5kZXggKz0gc2l6ZTtcbiAgICByZXR1cm4gcmVzdWx0O1xufTtcbm1vZHVsZS5leHBvcnRzID0gVWludDhBcnJheVJlYWRlcjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/Uint8ArrayReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/readerFor.js":
/*!****************************************************!*\
  !*** ./node_modules/jszip/lib/reader/readerFor.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ../support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar ArrayReader = __webpack_require__(/*! ./ArrayReader */ \"(ssr)/./node_modules/jszip/lib/reader/ArrayReader.js\");\nvar StringReader = __webpack_require__(/*! ./StringReader */ \"(ssr)/./node_modules/jszip/lib/reader/StringReader.js\");\nvar NodeBufferReader = __webpack_require__(/*! ./NodeBufferReader */ \"(ssr)/./node_modules/jszip/lib/reader/NodeBufferReader.js\");\nvar Uint8ArrayReader = __webpack_require__(/*! ./Uint8ArrayReader */ \"(ssr)/./node_modules/jszip/lib/reader/Uint8ArrayReader.js\");\n\n/**\n * Create a reader adapted to the data.\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data to read.\n * @return {DataReader} the data reader.\n */\nmodule.exports = function (data) {\n    var type = utils.getTypeOf(data);\n    utils.checkSupport(type);\n    if (type === \"string\" && !support.uint8array) {\n        return new StringReader(data);\n    }\n    if (type === \"nodebuffer\") {\n        return new NodeBufferReader(data);\n    }\n    if (support.uint8array) {\n        return new Uint8ArrayReader(utils.transformTo(\"uint8array\", data));\n    }\n    return new ArrayReader(utils.transformTo(\"array\", data));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3JlYWRlci9yZWFkZXJGb3IuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsWUFBWSxtQkFBTyxDQUFDLHlEQUFVO0FBQzlCLGNBQWMsbUJBQU8sQ0FBQyw2REFBWTtBQUNsQyxrQkFBa0IsbUJBQU8sQ0FBQywyRUFBZTtBQUN6QyxtQkFBbUIsbUJBQU8sQ0FBQyw2RUFBZ0I7QUFDM0MsdUJBQXVCLG1CQUFPLENBQUMscUZBQW9CO0FBQ25ELHVCQUF1QixtQkFBTyxDQUFDLHFGQUFvQjs7QUFFbkQ7QUFDQTtBQUNBLFdBQVcsc0NBQXNDO0FBQ2pELFlBQVksWUFBWTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvcmVhZGVyL3JlYWRlckZvci5qcz84OWU2Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG52YXIgdXRpbHMgPSByZXF1aXJlKFwiLi4vdXRpbHNcIik7XG52YXIgc3VwcG9ydCA9IHJlcXVpcmUoXCIuLi9zdXBwb3J0XCIpO1xudmFyIEFycmF5UmVhZGVyID0gcmVxdWlyZShcIi4vQXJyYXlSZWFkZXJcIik7XG52YXIgU3RyaW5nUmVhZGVyID0gcmVxdWlyZShcIi4vU3RyaW5nUmVhZGVyXCIpO1xudmFyIE5vZGVCdWZmZXJSZWFkZXIgPSByZXF1aXJlKFwiLi9Ob2RlQnVmZmVyUmVhZGVyXCIpO1xudmFyIFVpbnQ4QXJyYXlSZWFkZXIgPSByZXF1aXJlKFwiLi9VaW50OEFycmF5UmVhZGVyXCIpO1xuXG4vKipcbiAqIENyZWF0ZSBhIHJlYWRlciBhZGFwdGVkIHRvIHRoZSBkYXRhLlxuICogQHBhcmFtIHtTdHJpbmd8QXJyYXlCdWZmZXJ8VWludDhBcnJheXxCdWZmZXJ9IGRhdGEgdGhlIGRhdGEgdG8gcmVhZC5cbiAqIEByZXR1cm4ge0RhdGFSZWFkZXJ9IHRoZSBkYXRhIHJlYWRlci5cbiAqL1xubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoZGF0YSkge1xuICAgIHZhciB0eXBlID0gdXRpbHMuZ2V0VHlwZU9mKGRhdGEpO1xuICAgIHV0aWxzLmNoZWNrU3VwcG9ydCh0eXBlKTtcbiAgICBpZiAodHlwZSA9PT0gXCJzdHJpbmdcIiAmJiAhc3VwcG9ydC51aW50OGFycmF5KSB7XG4gICAgICAgIHJldHVybiBuZXcgU3RyaW5nUmVhZGVyKGRhdGEpO1xuICAgIH1cbiAgICBpZiAodHlwZSA9PT0gXCJub2RlYnVmZmVyXCIpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBOb2RlQnVmZmVyUmVhZGVyKGRhdGEpO1xuICAgIH1cbiAgICBpZiAoc3VwcG9ydC51aW50OGFycmF5KSB7XG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheVJlYWRlcih1dGlscy50cmFuc2Zvcm1UbyhcInVpbnQ4YXJyYXlcIiwgZGF0YSkpO1xuICAgIH1cbiAgICByZXR1cm4gbmV3IEFycmF5UmVhZGVyKHV0aWxzLnRyYW5zZm9ybVRvKFwiYXJyYXlcIiwgZGF0YSkpO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/readerFor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/signature.js":
/*!*********************************************!*\
  !*** ./node_modules/jszip/lib/signature.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.LOCAL_FILE_HEADER = \"PK\\x03\\x04\";\nexports.CENTRAL_FILE_HEADER = \"PK\\x01\\x02\";\nexports.CENTRAL_DIRECTORY_END = \"PK\\x05\\x06\";\nexports.ZIP64_CENTRAL_DIRECTORY_LOCATOR = \"PK\\x06\\x07\";\nexports.ZIP64_CENTRAL_DIRECTORY_END = \"PK\\x06\\x06\";\nexports.DATA_DESCRIPTOR = \"PK\\x07\\x08\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3NpZ25hdHVyZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLHlCQUF5QjtBQUN6QiwyQkFBMkI7QUFDM0IsNkJBQTZCO0FBQzdCLHVDQUF1QztBQUN2QyxtQ0FBbUM7QUFDbkMsdUJBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3NpZ25hdHVyZS5qcz8wNjkyIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuZXhwb3J0cy5MT0NBTF9GSUxFX0hFQURFUiA9IFwiUEtcXHgwM1xceDA0XCI7XG5leHBvcnRzLkNFTlRSQUxfRklMRV9IRUFERVIgPSBcIlBLXFx4MDFcXHgwMlwiO1xuZXhwb3J0cy5DRU5UUkFMX0RJUkVDVE9SWV9FTkQgPSBcIlBLXFx4MDVcXHgwNlwiO1xuZXhwb3J0cy5aSVA2NF9DRU5UUkFMX0RJUkVDVE9SWV9MT0NBVE9SID0gXCJQS1xceDA2XFx4MDdcIjtcbmV4cG9ydHMuWklQNjRfQ0VOVFJBTF9ESVJFQ1RPUllfRU5EID0gXCJQS1xceDA2XFx4MDZcIjtcbmV4cG9ydHMuREFUQV9ERVNDUklQVE9SID0gXCJQS1xceDA3XFx4MDhcIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/signature.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/ConvertWorker.js":
/*!********************************************************!*\
  !*** ./node_modules/jszip/lib/stream/ConvertWorker.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\n/**\n * A worker which convert chunks to a specified type.\n * @constructor\n * @param {String} destType the destination type.\n */\nfunction ConvertWorker(destType) {\n    GenericWorker.call(this, \"ConvertWorker to \" + destType);\n    this.destType = destType;\n}\nutils.inherits(ConvertWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nConvertWorker.prototype.processChunk = function (chunk) {\n    this.push({\n        data : utils.transformTo(this.destType, chunk.data),\n        meta : chunk.meta\n    });\n};\nmodule.exports = ConvertWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9Db252ZXJ0V29ya2VyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLG9CQUFvQixtQkFBTyxDQUFDLCtFQUFpQjtBQUM3QyxZQUFZLG1CQUFPLENBQUMseURBQVU7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2pzemlwL2xpYi9zdHJlYW0vQ29udmVydFdvcmtlci5qcz8xNWE5Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG52YXIgR2VuZXJpY1dvcmtlciA9IHJlcXVpcmUoXCIuL0dlbmVyaWNXb3JrZXJcIik7XG52YXIgdXRpbHMgPSByZXF1aXJlKFwiLi4vdXRpbHNcIik7XG5cbi8qKlxuICogQSB3b3JrZXIgd2hpY2ggY29udmVydCBjaHVua3MgdG8gYSBzcGVjaWZpZWQgdHlwZS5cbiAqIEBjb25zdHJ1Y3RvclxuICogQHBhcmFtIHtTdHJpbmd9IGRlc3RUeXBlIHRoZSBkZXN0aW5hdGlvbiB0eXBlLlxuICovXG5mdW5jdGlvbiBDb252ZXJ0V29ya2VyKGRlc3RUeXBlKSB7XG4gICAgR2VuZXJpY1dvcmtlci5jYWxsKHRoaXMsIFwiQ29udmVydFdvcmtlciB0byBcIiArIGRlc3RUeXBlKTtcbiAgICB0aGlzLmRlc3RUeXBlID0gZGVzdFR5cGU7XG59XG51dGlscy5pbmhlcml0cyhDb252ZXJ0V29ya2VyLCBHZW5lcmljV29ya2VyKTtcblxuLyoqXG4gKiBAc2VlIEdlbmVyaWNXb3JrZXIucHJvY2Vzc0NodW5rXG4gKi9cbkNvbnZlcnRXb3JrZXIucHJvdG90eXBlLnByb2Nlc3NDaHVuayA9IGZ1bmN0aW9uIChjaHVuaykge1xuICAgIHRoaXMucHVzaCh7XG4gICAgICAgIGRhdGEgOiB1dGlscy50cmFuc2Zvcm1Ubyh0aGlzLmRlc3RUeXBlLCBjaHVuay5kYXRhKSxcbiAgICAgICAgbWV0YSA6IGNodW5rLm1ldGFcbiAgICB9KTtcbn07XG5tb2R1bGUuZXhwb3J0cyA9IENvbnZlcnRXb3JrZXI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/ConvertWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/Crc32Probe.js":
/*!*****************************************************!*\
  !*** ./node_modules/jszip/lib/stream/Crc32Probe.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar crc32 = __webpack_require__(/*! ../crc32 */ \"(ssr)/./node_modules/jszip/lib/crc32.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n\n/**\n * A worker which calculate the crc32 of the data flowing through.\n * @constructor\n */\nfunction Crc32Probe() {\n    GenericWorker.call(this, \"Crc32Probe\");\n    this.withStreamInfo(\"crc32\", 0);\n}\nutils.inherits(Crc32Probe, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nCrc32Probe.prototype.processChunk = function (chunk) {\n    this.streamInfo.crc32 = crc32(chunk.data, this.streamInfo.crc32 || 0);\n    this.push(chunk);\n};\nmodule.exports = Crc32Probe;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9DcmMzMlByb2JlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLG9CQUFvQixtQkFBTyxDQUFDLCtFQUFpQjtBQUM3QyxZQUFZLG1CQUFPLENBQUMseURBQVU7QUFDOUIsWUFBWSxtQkFBTyxDQUFDLHlEQUFVOztBQUU5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2pzemlwL2xpYi9zdHJlYW0vQ3JjMzJQcm9iZS5qcz9lM2MzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG52YXIgR2VuZXJpY1dvcmtlciA9IHJlcXVpcmUoXCIuL0dlbmVyaWNXb3JrZXJcIik7XG52YXIgY3JjMzIgPSByZXF1aXJlKFwiLi4vY3JjMzJcIik7XG52YXIgdXRpbHMgPSByZXF1aXJlKFwiLi4vdXRpbHNcIik7XG5cbi8qKlxuICogQSB3b3JrZXIgd2hpY2ggY2FsY3VsYXRlIHRoZSBjcmMzMiBvZiB0aGUgZGF0YSBmbG93aW5nIHRocm91Z2guXG4gKiBAY29uc3RydWN0b3JcbiAqL1xuZnVuY3Rpb24gQ3JjMzJQcm9iZSgpIHtcbiAgICBHZW5lcmljV29ya2VyLmNhbGwodGhpcywgXCJDcmMzMlByb2JlXCIpO1xuICAgIHRoaXMud2l0aFN0cmVhbUluZm8oXCJjcmMzMlwiLCAwKTtcbn1cbnV0aWxzLmluaGVyaXRzKENyYzMyUHJvYmUsIEdlbmVyaWNXb3JrZXIpO1xuXG4vKipcbiAqIEBzZWUgR2VuZXJpY1dvcmtlci5wcm9jZXNzQ2h1bmtcbiAqL1xuQ3JjMzJQcm9iZS5wcm90b3R5cGUucHJvY2Vzc0NodW5rID0gZnVuY3Rpb24gKGNodW5rKSB7XG4gICAgdGhpcy5zdHJlYW1JbmZvLmNyYzMyID0gY3JjMzIoY2h1bmsuZGF0YSwgdGhpcy5zdHJlYW1JbmZvLmNyYzMyIHx8IDApO1xuICAgIHRoaXMucHVzaChjaHVuayk7XG59O1xubW9kdWxlLmV4cG9ydHMgPSBDcmMzMlByb2JlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/Crc32Probe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/DataLengthProbe.js":
/*!**********************************************************!*\
  !*** ./node_modules/jszip/lib/stream/DataLengthProbe.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * A worker which calculate the total length of the data flowing through.\n * @constructor\n * @param {String} propName the name used to expose the length\n */\nfunction DataLengthProbe(propName) {\n    GenericWorker.call(this, \"DataLengthProbe for \" + propName);\n    this.propName = propName;\n    this.withStreamInfo(propName, 0);\n}\nutils.inherits(DataLengthProbe, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nDataLengthProbe.prototype.processChunk = function (chunk) {\n    if(chunk) {\n        var length = this.streamInfo[this.propName] || 0;\n        this.streamInfo[this.propName] = length + chunk.data.length;\n    }\n    GenericWorker.prototype.processChunk.call(this, chunk);\n};\nmodule.exports = DataLengthProbe;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9EYXRhTGVuZ3RoUHJvYmUuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsWUFBWSxtQkFBTyxDQUFDLHlEQUFVO0FBQzlCLG9CQUFvQixtQkFBTyxDQUFDLCtFQUFpQjs7QUFFN0M7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvc3RyZWFtL0RhdGFMZW5ndGhQcm9iZS5qcz9mNTM0Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG52YXIgdXRpbHMgPSByZXF1aXJlKFwiLi4vdXRpbHNcIik7XG52YXIgR2VuZXJpY1dvcmtlciA9IHJlcXVpcmUoXCIuL0dlbmVyaWNXb3JrZXJcIik7XG5cbi8qKlxuICogQSB3b3JrZXIgd2hpY2ggY2FsY3VsYXRlIHRoZSB0b3RhbCBsZW5ndGggb2YgdGhlIGRhdGEgZmxvd2luZyB0aHJvdWdoLlxuICogQGNvbnN0cnVjdG9yXG4gKiBAcGFyYW0ge1N0cmluZ30gcHJvcE5hbWUgdGhlIG5hbWUgdXNlZCB0byBleHBvc2UgdGhlIGxlbmd0aFxuICovXG5mdW5jdGlvbiBEYXRhTGVuZ3RoUHJvYmUocHJvcE5hbWUpIHtcbiAgICBHZW5lcmljV29ya2VyLmNhbGwodGhpcywgXCJEYXRhTGVuZ3RoUHJvYmUgZm9yIFwiICsgcHJvcE5hbWUpO1xuICAgIHRoaXMucHJvcE5hbWUgPSBwcm9wTmFtZTtcbiAgICB0aGlzLndpdGhTdHJlYW1JbmZvKHByb3BOYW1lLCAwKTtcbn1cbnV0aWxzLmluaGVyaXRzKERhdGFMZW5ndGhQcm9iZSwgR2VuZXJpY1dvcmtlcik7XG5cbi8qKlxuICogQHNlZSBHZW5lcmljV29ya2VyLnByb2Nlc3NDaHVua1xuICovXG5EYXRhTGVuZ3RoUHJvYmUucHJvdG90eXBlLnByb2Nlc3NDaHVuayA9IGZ1bmN0aW9uIChjaHVuaykge1xuICAgIGlmKGNodW5rKSB7XG4gICAgICAgIHZhciBsZW5ndGggPSB0aGlzLnN0cmVhbUluZm9bdGhpcy5wcm9wTmFtZV0gfHwgMDtcbiAgICAgICAgdGhpcy5zdHJlYW1JbmZvW3RoaXMucHJvcE5hbWVdID0gbGVuZ3RoICsgY2h1bmsuZGF0YS5sZW5ndGg7XG4gICAgfVxuICAgIEdlbmVyaWNXb3JrZXIucHJvdG90eXBlLnByb2Nlc3NDaHVuay5jYWxsKHRoaXMsIGNodW5rKTtcbn07XG5tb2R1bGUuZXhwb3J0cyA9IERhdGFMZW5ndGhQcm9iZTtcblxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/DataLengthProbe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/DataWorker.js":
/*!*****************************************************!*\
  !*** ./node_modules/jszip/lib/stream/DataWorker.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\n// the size of the generated chunks\n// TODO expose this as a public variable\nvar DEFAULT_BLOCK_SIZE = 16 * 1024;\n\n/**\n * A worker that reads a content and emits chunks.\n * @constructor\n * @param {Promise} dataP the promise of the data to split\n */\nfunction DataWorker(dataP) {\n    GenericWorker.call(this, \"DataWorker\");\n    var self = this;\n    this.dataIsReady = false;\n    this.index = 0;\n    this.max = 0;\n    this.data = null;\n    this.type = \"\";\n\n    this._tickScheduled = false;\n\n    dataP.then(function (data) {\n        self.dataIsReady = true;\n        self.data = data;\n        self.max = data && data.length || 0;\n        self.type = utils.getTypeOf(data);\n        if(!self.isPaused) {\n            self._tickAndRepeat();\n        }\n    }, function (e) {\n        self.error(e);\n    });\n}\n\nutils.inherits(DataWorker, GenericWorker);\n\n/**\n * @see GenericWorker.cleanUp\n */\nDataWorker.prototype.cleanUp = function () {\n    GenericWorker.prototype.cleanUp.call(this);\n    this.data = null;\n};\n\n/**\n * @see GenericWorker.resume\n */\nDataWorker.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if (!this._tickScheduled && this.dataIsReady) {\n        this._tickScheduled = true;\n        utils.delay(this._tickAndRepeat, [], this);\n    }\n    return true;\n};\n\n/**\n * Trigger a tick a schedule an other call to this function.\n */\nDataWorker.prototype._tickAndRepeat = function() {\n    this._tickScheduled = false;\n    if(this.isPaused || this.isFinished) {\n        return;\n    }\n    this._tick();\n    if(!this.isFinished) {\n        utils.delay(this._tickAndRepeat, [], this);\n        this._tickScheduled = true;\n    }\n};\n\n/**\n * Read and push a chunk.\n */\nDataWorker.prototype._tick = function() {\n\n    if(this.isPaused || this.isFinished) {\n        return false;\n    }\n\n    var size = DEFAULT_BLOCK_SIZE;\n    var data = null, nextIndex = Math.min(this.max, this.index + size);\n    if (this.index >= this.max) {\n        // EOF\n        return this.end();\n    } else {\n        switch(this.type) {\n        case \"string\":\n            data = this.data.substring(this.index, nextIndex);\n            break;\n        case \"uint8array\":\n            data = this.data.subarray(this.index, nextIndex);\n            break;\n        case \"array\":\n        case \"nodebuffer\":\n            data = this.data.slice(this.index, nextIndex);\n            break;\n        }\n        this.index = nextIndex;\n        return this.push({\n            data : data,\n            meta : {\n                percent : this.max ? this.index / this.max * 100 : 0\n            }\n        });\n    }\n};\n\nmodule.exports = DataWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/DataWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js":
/*!********************************************************!*\
  !*** ./node_modules/jszip/lib/stream/GenericWorker.js ***!
  \********************************************************/
/***/ ((module) => {

eval("\n\n/**\n * A worker that does nothing but passing chunks to the next one. This is like\n * a nodejs stream but with some differences. On the good side :\n * - it works on IE 6-9 without any issue / polyfill\n * - it weights less than the full dependencies bundled with browserify\n * - it forwards errors (no need to declare an error handler EVERYWHERE)\n *\n * A chunk is an object with 2 attributes : `meta` and `data`. The former is an\n * object containing anything (`percent` for example), see each worker for more\n * details. The latter is the real data (String, Uint8Array, etc).\n *\n * @constructor\n * @param {String} name the name of the stream (mainly used for debugging purposes)\n */\nfunction GenericWorker(name) {\n    // the name of the worker\n    this.name = name || \"default\";\n    // an object containing metadata about the workers chain\n    this.streamInfo = {};\n    // an error which happened when the worker was paused\n    this.generatedError = null;\n    // an object containing metadata to be merged by this worker into the general metadata\n    this.extraStreamInfo = {};\n    // true if the stream is paused (and should not do anything), false otherwise\n    this.isPaused = true;\n    // true if the stream is finished (and should not do anything), false otherwise\n    this.isFinished = false;\n    // true if the stream is locked to prevent further structure updates (pipe), false otherwise\n    this.isLocked = false;\n    // the event listeners\n    this._listeners = {\n        \"data\":[],\n        \"end\":[],\n        \"error\":[]\n    };\n    // the previous worker, if any\n    this.previous = null;\n}\n\nGenericWorker.prototype = {\n    /**\n     * Push a chunk to the next workers.\n     * @param {Object} chunk the chunk to push\n     */\n    push : function (chunk) {\n        this.emit(\"data\", chunk);\n    },\n    /**\n     * End the stream.\n     * @return {Boolean} true if this call ended the worker, false otherwise.\n     */\n    end : function () {\n        if (this.isFinished) {\n            return false;\n        }\n\n        this.flush();\n        try {\n            this.emit(\"end\");\n            this.cleanUp();\n            this.isFinished = true;\n        } catch (e) {\n            this.emit(\"error\", e);\n        }\n        return true;\n    },\n    /**\n     * End the stream with an error.\n     * @param {Error} e the error which caused the premature end.\n     * @return {Boolean} true if this call ended the worker with an error, false otherwise.\n     */\n    error : function (e) {\n        if (this.isFinished) {\n            return false;\n        }\n\n        if(this.isPaused) {\n            this.generatedError = e;\n        } else {\n            this.isFinished = true;\n\n            this.emit(\"error\", e);\n\n            // in the workers chain exploded in the middle of the chain,\n            // the error event will go downward but we also need to notify\n            // workers upward that there has been an error.\n            if(this.previous) {\n                this.previous.error(e);\n            }\n\n            this.cleanUp();\n        }\n        return true;\n    },\n    /**\n     * Add a callback on an event.\n     * @param {String} name the name of the event (data, end, error)\n     * @param {Function} listener the function to call when the event is triggered\n     * @return {GenericWorker} the current object for chainability\n     */\n    on : function (name, listener) {\n        this._listeners[name].push(listener);\n        return this;\n    },\n    /**\n     * Clean any references when a worker is ending.\n     */\n    cleanUp : function () {\n        this.streamInfo = this.generatedError = this.extraStreamInfo = null;\n        this._listeners = [];\n    },\n    /**\n     * Trigger an event. This will call registered callback with the provided arg.\n     * @param {String} name the name of the event (data, end, error)\n     * @param {Object} arg the argument to call the callback with.\n     */\n    emit : function (name, arg) {\n        if (this._listeners[name]) {\n            for(var i = 0; i < this._listeners[name].length; i++) {\n                this._listeners[name][i].call(this, arg);\n            }\n        }\n    },\n    /**\n     * Chain a worker with an other.\n     * @param {Worker} next the worker receiving events from the current one.\n     * @return {worker} the next worker for chainability\n     */\n    pipe : function (next) {\n        return next.registerPrevious(this);\n    },\n    /**\n     * Same as `pipe` in the other direction.\n     * Using an API with `pipe(next)` is very easy.\n     * Implementing the API with the point of view of the next one registering\n     * a source is easier, see the ZipFileWorker.\n     * @param {Worker} previous the previous worker, sending events to this one\n     * @return {Worker} the current worker for chainability\n     */\n    registerPrevious : function (previous) {\n        if (this.isLocked) {\n            throw new Error(\"The stream '\" + this + \"' has already been used.\");\n        }\n\n        // sharing the streamInfo...\n        this.streamInfo = previous.streamInfo;\n        // ... and adding our own bits\n        this.mergeStreamInfo();\n        this.previous =  previous;\n        var self = this;\n        previous.on(\"data\", function (chunk) {\n            self.processChunk(chunk);\n        });\n        previous.on(\"end\", function () {\n            self.end();\n        });\n        previous.on(\"error\", function (e) {\n            self.error(e);\n        });\n        return this;\n    },\n    /**\n     * Pause the stream so it doesn't send events anymore.\n     * @return {Boolean} true if this call paused the worker, false otherwise.\n     */\n    pause : function () {\n        if(this.isPaused || this.isFinished) {\n            return false;\n        }\n        this.isPaused = true;\n\n        if(this.previous) {\n            this.previous.pause();\n        }\n        return true;\n    },\n    /**\n     * Resume a paused stream.\n     * @return {Boolean} true if this call resumed the worker, false otherwise.\n     */\n    resume : function () {\n        if(!this.isPaused || this.isFinished) {\n            return false;\n        }\n        this.isPaused = false;\n\n        // if true, the worker tried to resume but failed\n        var withError = false;\n        if(this.generatedError) {\n            this.error(this.generatedError);\n            withError = true;\n        }\n        if(this.previous) {\n            this.previous.resume();\n        }\n\n        return !withError;\n    },\n    /**\n     * Flush any remaining bytes as the stream is ending.\n     */\n    flush : function () {},\n    /**\n     * Process a chunk. This is usually the method overridden.\n     * @param {Object} chunk the chunk to process.\n     */\n    processChunk : function(chunk) {\n        this.push(chunk);\n    },\n    /**\n     * Add a key/value to be added in the workers chain streamInfo once activated.\n     * @param {String} key the key to use\n     * @param {Object} value the associated value\n     * @return {Worker} the current worker for chainability\n     */\n    withStreamInfo : function (key, value) {\n        this.extraStreamInfo[key] = value;\n        this.mergeStreamInfo();\n        return this;\n    },\n    /**\n     * Merge this worker's streamInfo into the chain's streamInfo.\n     */\n    mergeStreamInfo : function () {\n        for(var key in this.extraStreamInfo) {\n            if (!Object.prototype.hasOwnProperty.call(this.extraStreamInfo, key)) {\n                continue;\n            }\n            this.streamInfo[key] = this.extraStreamInfo[key];\n        }\n    },\n\n    /**\n     * Lock the stream to prevent further updates on the workers chain.\n     * After calling this method, all calls to pipe will fail.\n     */\n    lock: function () {\n        if (this.isLocked) {\n            throw new Error(\"The stream '\" + this + \"' has already been used.\");\n        }\n        this.isLocked = true;\n        if (this.previous) {\n            this.previous.lock();\n        }\n    },\n\n    /**\n     *\n     * Pretty print the workers chain.\n     */\n    toString : function () {\n        var me = \"Worker \" + this.name;\n        if (this.previous) {\n            return this.previous + \" -> \" + me;\n        } else {\n            return me;\n        }\n    }\n};\n\nmodule.exports = GenericWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/StreamHelper.js":
/*!*******************************************************!*\
  !*** ./node_modules/jszip/lib/stream/StreamHelper.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar ConvertWorker = __webpack_require__(/*! ./ConvertWorker */ \"(ssr)/./node_modules/jszip/lib/stream/ConvertWorker.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar base64 = __webpack_require__(/*! ../base64 */ \"(ssr)/./node_modules/jszip/lib/base64.js\");\nvar support = __webpack_require__(/*! ../support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar external = __webpack_require__(/*! ../external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\n\nvar NodejsStreamOutputAdapter = null;\nif (support.nodestream) {\n    try {\n        NodejsStreamOutputAdapter = __webpack_require__(/*! ../nodejs/NodejsStreamOutputAdapter */ \"(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js\");\n    } catch(e) {\n        // ignore\n    }\n}\n\n/**\n * Apply the final transformation of the data. If the user wants a Blob for\n * example, it's easier to work with an U8intArray and finally do the\n * ArrayBuffer/Blob conversion.\n * @param {String} type the name of the final type\n * @param {String|Uint8Array|Buffer} content the content to transform\n * @param {String} mimeType the mime type of the content, if applicable.\n * @return {String|Uint8Array|ArrayBuffer|Buffer|Blob} the content in the right format.\n */\nfunction transformZipOutput(type, content, mimeType) {\n    switch(type) {\n    case \"blob\" :\n        return utils.newBlob(utils.transformTo(\"arraybuffer\", content), mimeType);\n    case \"base64\" :\n        return base64.encode(content);\n    default :\n        return utils.transformTo(type, content);\n    }\n}\n\n/**\n * Concatenate an array of data of the given type.\n * @param {String} type the type of the data in the given array.\n * @param {Array} dataArray the array containing the data chunks to concatenate\n * @return {String|Uint8Array|Buffer} the concatenated data\n * @throws Error if the asked type is unsupported\n */\nfunction concat (type, dataArray) {\n    var i, index = 0, res = null, totalLength = 0;\n    for(i = 0; i < dataArray.length; i++) {\n        totalLength += dataArray[i].length;\n    }\n    switch(type) {\n    case \"string\":\n        return dataArray.join(\"\");\n    case \"array\":\n        return Array.prototype.concat.apply([], dataArray);\n    case \"uint8array\":\n        res = new Uint8Array(totalLength);\n        for(i = 0; i < dataArray.length; i++) {\n            res.set(dataArray[i], index);\n            index += dataArray[i].length;\n        }\n        return res;\n    case \"nodebuffer\":\n        return Buffer.concat(dataArray);\n    default:\n        throw new Error(\"concat : unsupported type '\"  + type + \"'\");\n    }\n}\n\n/**\n * Listen a StreamHelper, accumulate its content and concatenate it into a\n * complete block.\n * @param {StreamHelper} helper the helper to use.\n * @param {Function} updateCallback a callback called on each update. Called\n * with one arg :\n * - the metadata linked to the update received.\n * @return Promise the promise for the accumulation.\n */\nfunction accumulate(helper, updateCallback) {\n    return new external.Promise(function (resolve, reject){\n        var dataArray = [];\n        var chunkType = helper._internalType,\n            resultType = helper._outputType,\n            mimeType = helper._mimeType;\n        helper\n            .on(\"data\", function (data, meta) {\n                dataArray.push(data);\n                if(updateCallback) {\n                    updateCallback(meta);\n                }\n            })\n            .on(\"error\", function(err) {\n                dataArray = [];\n                reject(err);\n            })\n            .on(\"end\", function (){\n                try {\n                    var result = transformZipOutput(resultType, concat(chunkType, dataArray), mimeType);\n                    resolve(result);\n                } catch (e) {\n                    reject(e);\n                }\n                dataArray = [];\n            })\n            .resume();\n    });\n}\n\n/**\n * An helper to easily use workers outside of JSZip.\n * @constructor\n * @param {Worker} worker the worker to wrap\n * @param {String} outputType the type of data expected by the use\n * @param {String} mimeType the mime type of the content, if applicable.\n */\nfunction StreamHelper(worker, outputType, mimeType) {\n    var internalType = outputType;\n    switch(outputType) {\n    case \"blob\":\n    case \"arraybuffer\":\n        internalType = \"uint8array\";\n        break;\n    case \"base64\":\n        internalType = \"string\";\n        break;\n    }\n\n    try {\n        // the type used internally\n        this._internalType = internalType;\n        // the type used to output results\n        this._outputType = outputType;\n        // the mime type\n        this._mimeType = mimeType;\n        utils.checkSupport(internalType);\n        this._worker = worker.pipe(new ConvertWorker(internalType));\n        // the last workers can be rewired without issues but we need to\n        // prevent any updates on previous workers.\n        worker.lock();\n    } catch(e) {\n        this._worker = new GenericWorker(\"error\");\n        this._worker.error(e);\n    }\n}\n\nStreamHelper.prototype = {\n    /**\n     * Listen a StreamHelper, accumulate its content and concatenate it into a\n     * complete block.\n     * @param {Function} updateCb the update callback.\n     * @return Promise the promise for the accumulation.\n     */\n    accumulate : function (updateCb) {\n        return accumulate(this, updateCb);\n    },\n    /**\n     * Add a listener on an event triggered on a stream.\n     * @param {String} evt the name of the event\n     * @param {Function} fn the listener\n     * @return {StreamHelper} the current helper.\n     */\n    on : function (evt, fn) {\n        var self = this;\n\n        if(evt === \"data\") {\n            this._worker.on(evt, function (chunk) {\n                fn.call(self, chunk.data, chunk.meta);\n            });\n        } else {\n            this._worker.on(evt, function () {\n                utils.delay(fn, arguments, self);\n            });\n        }\n        return this;\n    },\n    /**\n     * Resume the flow of chunks.\n     * @return {StreamHelper} the current helper.\n     */\n    resume : function () {\n        utils.delay(this._worker.resume, [], this._worker);\n        return this;\n    },\n    /**\n     * Pause the flow of chunks.\n     * @return {StreamHelper} the current helper.\n     */\n    pause : function () {\n        this._worker.pause();\n        return this;\n    },\n    /**\n     * Return a nodejs stream for this helper.\n     * @param {Function} updateCb the update callback.\n     * @return {NodejsStreamOutputAdapter} the nodejs stream.\n     */\n    toNodejsStream : function (updateCb) {\n        utils.checkSupport(\"nodestream\");\n        if (this._outputType !== \"nodebuffer\") {\n            // an object stream containing blob/arraybuffer/uint8array/string\n            // is strange and I don't know if it would be useful.\n            // I you find this comment and have a good usecase, please open a\n            // bug report !\n            throw new Error(this._outputType + \" is not supported by this method\");\n        }\n\n        return new NodejsStreamOutputAdapter(this, {\n            objectMode : this._outputType !== \"nodebuffer\"\n        }, updateCb);\n    }\n};\n\n\nmodule.exports = StreamHelper;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/StreamHelper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/support.js":
/*!*******************************************!*\
  !*** ./node_modules/jszip/lib/support.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nexports.base64 = true;\nexports.array = true;\nexports.string = true;\nexports.arraybuffer = typeof ArrayBuffer !== \"undefined\" && typeof Uint8Array !== \"undefined\";\nexports.nodebuffer = typeof Buffer !== \"undefined\";\n// contains true if JSZip can read/generate Uint8Array, false otherwise.\nexports.uint8array = typeof Uint8Array !== \"undefined\";\n\nif (typeof ArrayBuffer === \"undefined\") {\n    exports.blob = false;\n}\nelse {\n    var buffer = new ArrayBuffer(0);\n    try {\n        exports.blob = new Blob([buffer], {\n            type: \"application/zip\"\n        }).size === 0;\n    }\n    catch (e) {\n        try {\n            var Builder = self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder;\n            var builder = new Builder();\n            builder.append(buffer);\n            exports.blob = builder.getBlob(\"application/zip\").size === 0;\n        }\n        catch (e) {\n            exports.blob = false;\n        }\n    }\n}\n\ntry {\n    exports.nodestream = !!(__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Readable);\n} catch(e) {\n    exports.nodestream = false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/support.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/utf8.js":
/*!****************************************!*\
  !*** ./node_modules/jszip/lib/utf8.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/jszip/lib/nodejsUtils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * The following functions come from pako, from pako/lib/utils/strings\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */\n\n// Table with utf8 lengths (calculated by first byte of sequence)\n// Note, that 5 & 6-byte values and some 4-byte values can not be represented in JS,\n// because max possible codepoint is 0x10ffff\nvar _utf8len = new Array(256);\nfor (var i=0; i<256; i++) {\n    _utf8len[i] = (i >= 252 ? 6 : i >= 248 ? 5 : i >= 240 ? 4 : i >= 224 ? 3 : i >= 192 ? 2 : 1);\n}\n_utf8len[254]=_utf8len[254]=1; // Invalid sequence start\n\n// convert string to array (typed, when possible)\nvar string2buf = function (str) {\n    var buf, c, c2, m_pos, i, str_len = str.length, buf_len = 0;\n\n    // count binary size\n    for (m_pos = 0; m_pos < str_len; m_pos++) {\n        c = str.charCodeAt(m_pos);\n        if ((c & 0xfc00) === 0xd800 && (m_pos+1 < str_len)) {\n            c2 = str.charCodeAt(m_pos+1);\n            if ((c2 & 0xfc00) === 0xdc00) {\n                c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n                m_pos++;\n            }\n        }\n        buf_len += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n    }\n\n    // allocate buffer\n    if (support.uint8array) {\n        buf = new Uint8Array(buf_len);\n    } else {\n        buf = new Array(buf_len);\n    }\n\n    // convert\n    for (i=0, m_pos = 0; i < buf_len; m_pos++) {\n        c = str.charCodeAt(m_pos);\n        if ((c & 0xfc00) === 0xd800 && (m_pos+1 < str_len)) {\n            c2 = str.charCodeAt(m_pos+1);\n            if ((c2 & 0xfc00) === 0xdc00) {\n                c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n                m_pos++;\n            }\n        }\n        if (c < 0x80) {\n            /* one byte */\n            buf[i++] = c;\n        } else if (c < 0x800) {\n            /* two bytes */\n            buf[i++] = 0xC0 | (c >>> 6);\n            buf[i++] = 0x80 | (c & 0x3f);\n        } else if (c < 0x10000) {\n            /* three bytes */\n            buf[i++] = 0xE0 | (c >>> 12);\n            buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n            buf[i++] = 0x80 | (c & 0x3f);\n        } else {\n            /* four bytes */\n            buf[i++] = 0xf0 | (c >>> 18);\n            buf[i++] = 0x80 | (c >>> 12 & 0x3f);\n            buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n            buf[i++] = 0x80 | (c & 0x3f);\n        }\n    }\n\n    return buf;\n};\n\n// Calculate max possible position in utf8 buffer,\n// that will not break sequence. If that's not possible\n// - (very small limits) return max size as is.\n//\n// buf[] - utf8 bytes array\n// max   - length limit (mandatory);\nvar utf8border = function(buf, max) {\n    var pos;\n\n    max = max || buf.length;\n    if (max > buf.length) { max = buf.length; }\n\n    // go back from last position, until start of sequence found\n    pos = max-1;\n    while (pos >= 0 && (buf[pos] & 0xC0) === 0x80) { pos--; }\n\n    // Fuckup - very small and broken sequence,\n    // return max, because we should return something anyway.\n    if (pos < 0) { return max; }\n\n    // If we came to start of buffer - that means vuffer is too small,\n    // return max too.\n    if (pos === 0) { return max; }\n\n    return (pos + _utf8len[buf[pos]] > max) ? pos : max;\n};\n\n// convert array to string\nvar buf2string = function (buf) {\n    var i, out, c, c_len;\n    var len = buf.length;\n\n    // Reserve max possible length (2 words per char)\n    // NB: by unknown reasons, Array is significantly faster for\n    //     String.fromCharCode.apply than Uint16Array.\n    var utf16buf = new Array(len*2);\n\n    for (out=0, i=0; i<len;) {\n        c = buf[i++];\n        // quick process ascii\n        if (c < 0x80) { utf16buf[out++] = c; continue; }\n\n        c_len = _utf8len[c];\n        // skip 5 & 6 byte codes\n        if (c_len > 4) { utf16buf[out++] = 0xfffd; i += c_len-1; continue; }\n\n        // apply mask on first byte\n        c &= c_len === 2 ? 0x1f : c_len === 3 ? 0x0f : 0x07;\n        // join the rest\n        while (c_len > 1 && i < len) {\n            c = (c << 6) | (buf[i++] & 0x3f);\n            c_len--;\n        }\n\n        // terminated by end of string?\n        if (c_len > 1) { utf16buf[out++] = 0xfffd; continue; }\n\n        if (c < 0x10000) {\n            utf16buf[out++] = c;\n        } else {\n            c -= 0x10000;\n            utf16buf[out++] = 0xd800 | ((c >> 10) & 0x3ff);\n            utf16buf[out++] = 0xdc00 | (c & 0x3ff);\n        }\n    }\n\n    // shrinkBuf(utf16buf, out)\n    if (utf16buf.length !== out) {\n        if(utf16buf.subarray) {\n            utf16buf = utf16buf.subarray(0, out);\n        } else {\n            utf16buf.length = out;\n        }\n    }\n\n    // return String.fromCharCode.apply(null, utf16buf);\n    return utils.applyFromCharCode(utf16buf);\n};\n\n\n// That's all for the pako functions.\n\n\n/**\n * Transform a javascript string into an array (typed if possible) of bytes,\n * UTF-8 encoded.\n * @param {String} str the string to encode\n * @return {Array|Uint8Array|Buffer} the UTF-8 encoded string.\n */\nexports.utf8encode = function utf8encode(str) {\n    if (support.nodebuffer) {\n        return nodejsUtils.newBufferFrom(str, \"utf-8\");\n    }\n\n    return string2buf(str);\n};\n\n\n/**\n * Transform a bytes array (or a representation) representing an UTF-8 encoded\n * string into a javascript string.\n * @param {Array|Uint8Array|Buffer} buf the data de decode\n * @return {String} the decoded string.\n */\nexports.utf8decode = function utf8decode(buf) {\n    if (support.nodebuffer) {\n        return utils.transformTo(\"nodebuffer\", buf).toString(\"utf-8\");\n    }\n\n    buf = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", buf);\n\n    return buf2string(buf);\n};\n\n/**\n * A worker to decode utf8 encoded binary chunks into string chunks.\n * @constructor\n */\nfunction Utf8DecodeWorker() {\n    GenericWorker.call(this, \"utf-8 decode\");\n    // the last bytes if a chunk didn't end with a complete codepoint.\n    this.leftOver = null;\n}\nutils.inherits(Utf8DecodeWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nUtf8DecodeWorker.prototype.processChunk = function (chunk) {\n\n    var data = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", chunk.data);\n\n    // 1st step, re-use what's left of the previous chunk\n    if (this.leftOver && this.leftOver.length) {\n        if(support.uint8array) {\n            var previousData = data;\n            data = new Uint8Array(previousData.length + this.leftOver.length);\n            data.set(this.leftOver, 0);\n            data.set(previousData, this.leftOver.length);\n        } else {\n            data = this.leftOver.concat(data);\n        }\n        this.leftOver = null;\n    }\n\n    var nextBoundary = utf8border(data);\n    var usableData = data;\n    if (nextBoundary !== data.length) {\n        if (support.uint8array) {\n            usableData = data.subarray(0, nextBoundary);\n            this.leftOver = data.subarray(nextBoundary, data.length);\n        } else {\n            usableData = data.slice(0, nextBoundary);\n            this.leftOver = data.slice(nextBoundary, data.length);\n        }\n    }\n\n    this.push({\n        data : exports.utf8decode(usableData),\n        meta : chunk.meta\n    });\n};\n\n/**\n * @see GenericWorker.flush\n */\nUtf8DecodeWorker.prototype.flush = function () {\n    if(this.leftOver && this.leftOver.length) {\n        this.push({\n            data : exports.utf8decode(this.leftOver),\n            meta : {}\n        });\n        this.leftOver = null;\n    }\n};\nexports.Utf8DecodeWorker = Utf8DecodeWorker;\n\n/**\n * A worker to endcode string chunks into utf8 encoded binary chunks.\n * @constructor\n */\nfunction Utf8EncodeWorker() {\n    GenericWorker.call(this, \"utf-8 encode\");\n}\nutils.inherits(Utf8EncodeWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nUtf8EncodeWorker.prototype.processChunk = function (chunk) {\n    this.push({\n        data : exports.utf8encode(chunk.data),\n        meta : chunk.meta\n    });\n};\nexports.Utf8EncodeWorker = Utf8EncodeWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/utf8.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/utils.js":
/*!*****************************************!*\
  !*** ./node_modules/jszip/lib/utils.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar base64 = __webpack_require__(/*! ./base64 */ \"(ssr)/./node_modules/jszip/lib/base64.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/jszip/lib/nodejsUtils.js\");\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\n__webpack_require__(/*! setimmediate */ \"(ssr)/./node_modules/next/dist/compiled/setimmediate/setImmediate.js\");\n\n\n/**\n * Convert a string that pass as a \"binary string\": it should represent a byte\n * array but may have > 255 char codes. Be sure to take only the first byte\n * and returns the byte array.\n * @param {String} str the string to transform.\n * @return {Array|Uint8Array} the string in a binary format.\n */\nfunction string2binary(str) {\n    var result = null;\n    if (support.uint8array) {\n        result = new Uint8Array(str.length);\n    } else {\n        result = new Array(str.length);\n    }\n    return stringToArrayLike(str, result);\n}\n\n/**\n * Create a new blob with the given content and the given type.\n * @param {String|ArrayBuffer} part the content to put in the blob. DO NOT use\n * an Uint8Array because the stock browser of android 4 won't accept it (it\n * will be silently converted to a string, \"[object Uint8Array]\").\n *\n * Use only ONE part to build the blob to avoid a memory leak in IE11 / Edge:\n * when a large amount of Array is used to create the Blob, the amount of\n * memory consumed is nearly 100 times the original data amount.\n *\n * @param {String} type the mime type of the blob.\n * @return {Blob} the created blob.\n */\nexports.newBlob = function(part, type) {\n    exports.checkSupport(\"blob\");\n\n    try {\n        // Blob constructor\n        return new Blob([part], {\n            type: type\n        });\n    }\n    catch (e) {\n\n        try {\n            // deprecated, browser only, old way\n            var Builder = self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder;\n            var builder = new Builder();\n            builder.append(part);\n            return builder.getBlob(type);\n        }\n        catch (e) {\n\n            // well, fuck ?!\n            throw new Error(\"Bug : can't construct the Blob.\");\n        }\n    }\n\n\n};\n/**\n * The identity function.\n * @param {Object} input the input.\n * @return {Object} the same input.\n */\nfunction identity(input) {\n    return input;\n}\n\n/**\n * Fill in an array with a string.\n * @param {String} str the string to use.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to fill in (will be mutated).\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated array.\n */\nfunction stringToArrayLike(str, array) {\n    for (var i = 0; i < str.length; ++i) {\n        array[i] = str.charCodeAt(i) & 0xFF;\n    }\n    return array;\n}\n\n/**\n * An helper for the function arrayLikeToString.\n * This contains static information and functions that\n * can be optimized by the browser JIT compiler.\n */\nvar arrayToStringHelper = {\n    /**\n     * Transform an array of int into a string, chunk by chunk.\n     * See the performances notes on arrayLikeToString.\n     * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n     * @param {String} type the type of the array.\n     * @param {Integer} chunk the chunk size.\n     * @return {String} the resulting string.\n     * @throws Error if the chunk is too big for the stack.\n     */\n    stringifyByChunk: function(array, type, chunk) {\n        var result = [], k = 0, len = array.length;\n        // shortcut\n        if (len <= chunk) {\n            return String.fromCharCode.apply(null, array);\n        }\n        while (k < len) {\n            if (type === \"array\" || type === \"nodebuffer\") {\n                result.push(String.fromCharCode.apply(null, array.slice(k, Math.min(k + chunk, len))));\n            }\n            else {\n                result.push(String.fromCharCode.apply(null, array.subarray(k, Math.min(k + chunk, len))));\n            }\n            k += chunk;\n        }\n        return result.join(\"\");\n    },\n    /**\n     * Call String.fromCharCode on every item in the array.\n     * This is the naive implementation, which generate A LOT of intermediate string.\n     * This should be used when everything else fail.\n     * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n     * @return {String} the result.\n     */\n    stringifyByChar: function(array){\n        var resultStr = \"\";\n        for(var i = 0; i < array.length; i++) {\n            resultStr += String.fromCharCode(array[i]);\n        }\n        return resultStr;\n    },\n    applyCanBeUsed : {\n        /**\n         * true if the browser accepts to use String.fromCharCode on Uint8Array\n         */\n        uint8array : (function () {\n            try {\n                return support.uint8array && String.fromCharCode.apply(null, new Uint8Array(1)).length === 1;\n            } catch (e) {\n                return false;\n            }\n        })(),\n        /**\n         * true if the browser accepts to use String.fromCharCode on nodejs Buffer.\n         */\n        nodebuffer : (function () {\n            try {\n                return support.nodebuffer && String.fromCharCode.apply(null, nodejsUtils.allocBuffer(1)).length === 1;\n            } catch (e) {\n                return false;\n            }\n        })()\n    }\n};\n\n/**\n * Transform an array-like object to a string.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n * @return {String} the result.\n */\nfunction arrayLikeToString(array) {\n    // Performances notes :\n    // --------------------\n    // String.fromCharCode.apply(null, array) is the fastest, see\n    // see http://jsperf.com/converting-a-uint8array-to-a-string/2\n    // but the stack is limited (and we can get huge arrays !).\n    //\n    // result += String.fromCharCode(array[i]); generate too many strings !\n    //\n    // This code is inspired by http://jsperf.com/arraybuffer-to-string-apply-performance/2\n    // TODO : we now have workers that split the work. Do we still need that ?\n    var chunk = 65536,\n        type = exports.getTypeOf(array),\n        canUseApply = true;\n    if (type === \"uint8array\") {\n        canUseApply = arrayToStringHelper.applyCanBeUsed.uint8array;\n    } else if (type === \"nodebuffer\") {\n        canUseApply = arrayToStringHelper.applyCanBeUsed.nodebuffer;\n    }\n\n    if (canUseApply) {\n        while (chunk > 1) {\n            try {\n                return arrayToStringHelper.stringifyByChunk(array, type, chunk);\n            } catch (e) {\n                chunk = Math.floor(chunk / 2);\n            }\n        }\n    }\n\n    // no apply or chunk error : slow and painful algorithm\n    // default browser on android 4.*\n    return arrayToStringHelper.stringifyByChar(array);\n}\n\nexports.applyFromCharCode = arrayLikeToString;\n\n\n/**\n * Copy the data from an array-like to an other array-like.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayFrom the origin array.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayTo the destination array which will be mutated.\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated destination array.\n */\nfunction arrayLikeToArrayLike(arrayFrom, arrayTo) {\n    for (var i = 0; i < arrayFrom.length; i++) {\n        arrayTo[i] = arrayFrom[i];\n    }\n    return arrayTo;\n}\n\n// a matrix containing functions to transform everything into everything.\nvar transform = {};\n\n// string to ?\ntransform[\"string\"] = {\n    \"string\": identity,\n    \"array\": function(input) {\n        return stringToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return transform[\"string\"][\"uint8array\"](input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return stringToArrayLike(input, new Uint8Array(input.length));\n    },\n    \"nodebuffer\": function(input) {\n        return stringToArrayLike(input, nodejsUtils.allocBuffer(input.length));\n    }\n};\n\n// array to ?\ntransform[\"array\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": identity,\n    \"arraybuffer\": function(input) {\n        return (new Uint8Array(input)).buffer;\n    },\n    \"uint8array\": function(input) {\n        return new Uint8Array(input);\n    },\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(input);\n    }\n};\n\n// arraybuffer to ?\ntransform[\"arraybuffer\"] = {\n    \"string\": function(input) {\n        return arrayLikeToString(new Uint8Array(input));\n    },\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(new Uint8Array(input), new Array(input.byteLength));\n    },\n    \"arraybuffer\": identity,\n    \"uint8array\": function(input) {\n        return new Uint8Array(input);\n    },\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(new Uint8Array(input));\n    }\n};\n\n// uint8array to ?\ntransform[\"uint8array\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return input.buffer;\n    },\n    \"uint8array\": identity,\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(input);\n    }\n};\n\n// nodebuffer to ?\ntransform[\"nodebuffer\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return transform[\"nodebuffer\"][\"uint8array\"](input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return arrayLikeToArrayLike(input, new Uint8Array(input.length));\n    },\n    \"nodebuffer\": identity\n};\n\n/**\n * Transform an input into any type.\n * The supported output type are : string, array, uint8array, arraybuffer, nodebuffer.\n * If no output type is specified, the unmodified input will be returned.\n * @param {String} outputType the output type.\n * @param {String|Array|ArrayBuffer|Uint8Array|Buffer} input the input to convert.\n * @throws {Error} an Error if the browser doesn't support the requested output type.\n */\nexports.transformTo = function(outputType, input) {\n    if (!input) {\n        // undefined, null, etc\n        // an empty string won't harm.\n        input = \"\";\n    }\n    if (!outputType) {\n        return input;\n    }\n    exports.checkSupport(outputType);\n    var inputType = exports.getTypeOf(input);\n    var result = transform[inputType][outputType](input);\n    return result;\n};\n\n/**\n * Resolve all relative path components, \".\" and \"..\", in a path. If these relative components\n * traverse above the root then the resulting path will only contain the final path component.\n *\n * All empty components, e.g. \"//\", are removed.\n * @param {string} path A path with / or \\ separators\n * @returns {string} The path with all relative path components resolved.\n */\nexports.resolve = function(path) {\n    var parts = path.split(\"/\");\n    var result = [];\n    for (var index = 0; index < parts.length; index++) {\n        var part = parts[index];\n        // Allow the first and last component to be empty for trailing slashes.\n        if (part === \".\" || (part === \"\" && index !== 0 && index !== parts.length - 1)) {\n            continue;\n        } else if (part === \"..\") {\n            result.pop();\n        } else {\n            result.push(part);\n        }\n    }\n    return result.join(\"/\");\n};\n\n/**\n * Return the type of the input.\n * The type will be in a format valid for JSZip.utils.transformTo : string, array, uint8array, arraybuffer.\n * @param {Object} input the input to identify.\n * @return {String} the (lowercase) type of the input.\n */\nexports.getTypeOf = function(input) {\n    if (typeof input === \"string\") {\n        return \"string\";\n    }\n    if (Object.prototype.toString.call(input) === \"[object Array]\") {\n        return \"array\";\n    }\n    if (support.nodebuffer && nodejsUtils.isBuffer(input)) {\n        return \"nodebuffer\";\n    }\n    if (support.uint8array && input instanceof Uint8Array) {\n        return \"uint8array\";\n    }\n    if (support.arraybuffer && input instanceof ArrayBuffer) {\n        return \"arraybuffer\";\n    }\n};\n\n/**\n * Throw an exception if the type is not supported.\n * @param {String} type the type to check.\n * @throws {Error} an Error if the browser doesn't support the requested type.\n */\nexports.checkSupport = function(type) {\n    var supported = support[type.toLowerCase()];\n    if (!supported) {\n        throw new Error(type + \" is not supported by this platform\");\n    }\n};\n\nexports.MAX_VALUE_16BITS = 65535;\nexports.MAX_VALUE_32BITS = -1; // well, \"\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\" is parsed as -1\n\n/**\n * Prettify a string read as binary.\n * @param {string} str the string to prettify.\n * @return {string} a pretty string.\n */\nexports.pretty = function(str) {\n    var res = \"\",\n        code, i;\n    for (i = 0; i < (str || \"\").length; i++) {\n        code = str.charCodeAt(i);\n        res += \"\\\\x\" + (code < 16 ? \"0\" : \"\") + code.toString(16).toUpperCase();\n    }\n    return res;\n};\n\n/**\n * Defer the call of a function.\n * @param {Function} callback the function to call asynchronously.\n * @param {Array} args the arguments to give to the callback.\n */\nexports.delay = function(callback, args, self) {\n    setImmediate(function () {\n        callback.apply(self || null, args || []);\n    });\n};\n\n/**\n * Extends a prototype with an other, without calling a constructor with\n * side effects. Inspired by nodejs' `utils.inherits`\n * @param {Function} ctor the constructor to augment\n * @param {Function} superCtor the parent constructor to use\n */\nexports.inherits = function (ctor, superCtor) {\n    var Obj = function() {};\n    Obj.prototype = superCtor.prototype;\n    ctor.prototype = new Obj();\n};\n\n/**\n * Merge the objects passed as parameters into a new one.\n * @private\n * @param {...Object} var_args All objects to merge.\n * @return {Object} a new object with the data of the others.\n */\nexports.extend = function() {\n    var result = {}, i, attr;\n    for (i = 0; i < arguments.length; i++) { // arguments is not enumerable in some browsers\n        for (attr in arguments[i]) {\n            if (Object.prototype.hasOwnProperty.call(arguments[i], attr) && typeof result[attr] === \"undefined\") {\n                result[attr] = arguments[i][attr];\n            }\n        }\n    }\n    return result;\n};\n\n/**\n * Transform arbitrary content into a Promise.\n * @param {String} name a name for the content being processed.\n * @param {Object} inputData the content to process.\n * @param {Boolean} isBinary true if the content is not an unicode string\n * @param {Boolean} isOptimizedBinaryString true if the string content only has one byte per character.\n * @param {Boolean} isBase64 true if the string content is encoded with base64.\n * @return {Promise} a promise in a format usable by JSZip.\n */\nexports.prepareContent = function(name, inputData, isBinary, isOptimizedBinaryString, isBase64) {\n\n    // if inputData is already a promise, this flatten it.\n    var promise = external.Promise.resolve(inputData).then(function(data) {\n\n\n        var isBlob = support.blob && (data instanceof Blob || [\"[object File]\", \"[object Blob]\"].indexOf(Object.prototype.toString.call(data)) !== -1);\n\n        if (isBlob && typeof FileReader !== \"undefined\") {\n            return new external.Promise(function (resolve, reject) {\n                var reader = new FileReader();\n\n                reader.onload = function(e) {\n                    resolve(e.target.result);\n                };\n                reader.onerror = function(e) {\n                    reject(e.target.error);\n                };\n                reader.readAsArrayBuffer(data);\n            });\n        } else {\n            return data;\n        }\n    });\n\n    return promise.then(function(data) {\n        var dataType = exports.getTypeOf(data);\n\n        if (!dataType) {\n            return external.Promise.reject(\n                new Error(\"Can't read the data of '\" + name + \"'. Is it \" +\n                          \"in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?\")\n            );\n        }\n        // special case : it's way easier to work with Uint8Array than with ArrayBuffer\n        if (dataType === \"arraybuffer\") {\n            data = exports.transformTo(\"uint8array\", data);\n        } else if (dataType === \"string\") {\n            if (isBase64) {\n                data = base64.decode(data);\n            }\n            else if (isBinary) {\n                // optimizedBinaryString === true means that the file has already been filtered with a 0xFF mask\n                if (isOptimizedBinaryString !== true) {\n                    // this is a string, not in a base64 format.\n                    // Be sure that this is a correct \"binary string\"\n                    data = string2binary(data);\n                }\n            }\n        }\n        return data;\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/zipEntries.js":
/*!**********************************************!*\
  !*** ./node_modules/jszip/lib/zipEntries.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar readerFor = __webpack_require__(/*! ./reader/readerFor */ \"(ssr)/./node_modules/jszip/lib/reader/readerFor.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar sig = __webpack_require__(/*! ./signature */ \"(ssr)/./node_modules/jszip/lib/signature.js\");\nvar ZipEntry = __webpack_require__(/*! ./zipEntry */ \"(ssr)/./node_modules/jszip/lib/zipEntry.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\n//  class ZipEntries {{{\n/**\n * All the entries in the zip file.\n * @constructor\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntries(loadOptions) {\n    this.files = [];\n    this.loadOptions = loadOptions;\n}\nZipEntries.prototype = {\n    /**\n     * Check that the reader is on the specified signature.\n     * @param {string} expectedSignature the expected signature.\n     * @throws {Error} if it is an other signature.\n     */\n    checkSignature: function(expectedSignature) {\n        if (!this.reader.readAndCheckSignature(expectedSignature)) {\n            this.reader.index -= 4;\n            var signature = this.reader.readString(4);\n            throw new Error(\"Corrupted zip or bug: unexpected signature \" + \"(\" + utils.pretty(signature) + \", expected \" + utils.pretty(expectedSignature) + \")\");\n        }\n    },\n    /**\n     * Check if the given signature is at the given index.\n     * @param {number} askedIndex the index to check.\n     * @param {string} expectedSignature the signature to expect.\n     * @return {boolean} true if the signature is here, false otherwise.\n     */\n    isSignature: function(askedIndex, expectedSignature) {\n        var currentIndex = this.reader.index;\n        this.reader.setIndex(askedIndex);\n        var signature = this.reader.readString(4);\n        var result = signature === expectedSignature;\n        this.reader.setIndex(currentIndex);\n        return result;\n    },\n    /**\n     * Read the end of the central directory.\n     */\n    readBlockEndOfCentral: function() {\n        this.diskNumber = this.reader.readInt(2);\n        this.diskWithCentralDirStart = this.reader.readInt(2);\n        this.centralDirRecordsOnThisDisk = this.reader.readInt(2);\n        this.centralDirRecords = this.reader.readInt(2);\n        this.centralDirSize = this.reader.readInt(4);\n        this.centralDirOffset = this.reader.readInt(4);\n\n        this.zipCommentLength = this.reader.readInt(2);\n        // warning : the encoding depends of the system locale\n        // On a linux machine with LANG=en_US.utf8, this field is utf8 encoded.\n        // On a windows machine, this field is encoded with the localized windows code page.\n        var zipComment = this.reader.readData(this.zipCommentLength);\n        var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n        // To get consistent behavior with the generation part, we will assume that\n        // this is utf8 encoded unless specified otherwise.\n        var decodeContent = utils.transformTo(decodeParamType, zipComment);\n        this.zipComment = this.loadOptions.decodeFileName(decodeContent);\n    },\n    /**\n     * Read the end of the Zip 64 central directory.\n     * Not merged with the method readEndOfCentral :\n     * The end of central can coexist with its Zip64 brother,\n     * I don't want to read the wrong number of bytes !\n     */\n    readBlockZip64EndOfCentral: function() {\n        this.zip64EndOfCentralSize = this.reader.readInt(8);\n        this.reader.skip(4);\n        // this.versionMadeBy = this.reader.readString(2);\n        // this.versionNeeded = this.reader.readInt(2);\n        this.diskNumber = this.reader.readInt(4);\n        this.diskWithCentralDirStart = this.reader.readInt(4);\n        this.centralDirRecordsOnThisDisk = this.reader.readInt(8);\n        this.centralDirRecords = this.reader.readInt(8);\n        this.centralDirSize = this.reader.readInt(8);\n        this.centralDirOffset = this.reader.readInt(8);\n\n        this.zip64ExtensibleData = {};\n        var extraDataSize = this.zip64EndOfCentralSize - 44,\n            index = 0,\n            extraFieldId,\n            extraFieldLength,\n            extraFieldValue;\n        while (index < extraDataSize) {\n            extraFieldId = this.reader.readInt(2);\n            extraFieldLength = this.reader.readInt(4);\n            extraFieldValue = this.reader.readData(extraFieldLength);\n            this.zip64ExtensibleData[extraFieldId] = {\n                id: extraFieldId,\n                length: extraFieldLength,\n                value: extraFieldValue\n            };\n        }\n    },\n    /**\n     * Read the end of the Zip 64 central directory locator.\n     */\n    readBlockZip64EndOfCentralLocator: function() {\n        this.diskWithZip64CentralDirStart = this.reader.readInt(4);\n        this.relativeOffsetEndOfZip64CentralDir = this.reader.readInt(8);\n        this.disksCount = this.reader.readInt(4);\n        if (this.disksCount > 1) {\n            throw new Error(\"Multi-volumes zip are not supported\");\n        }\n    },\n    /**\n     * Read the local files, based on the offset read in the central part.\n     */\n    readLocalFiles: function() {\n        var i, file;\n        for (i = 0; i < this.files.length; i++) {\n            file = this.files[i];\n            this.reader.setIndex(file.localHeaderOffset);\n            this.checkSignature(sig.LOCAL_FILE_HEADER);\n            file.readLocalPart(this.reader);\n            file.handleUTF8();\n            file.processAttributes();\n        }\n    },\n    /**\n     * Read the central directory.\n     */\n    readCentralDir: function() {\n        var file;\n\n        this.reader.setIndex(this.centralDirOffset);\n        while (this.reader.readAndCheckSignature(sig.CENTRAL_FILE_HEADER)) {\n            file = new ZipEntry({\n                zip64: this.zip64\n            }, this.loadOptions);\n            file.readCentralPart(this.reader);\n            this.files.push(file);\n        }\n\n        if (this.centralDirRecords !== this.files.length) {\n            if (this.centralDirRecords !== 0 && this.files.length === 0) {\n                // We expected some records but couldn't find ANY.\n                // This is really suspicious, as if something went wrong.\n                throw new Error(\"Corrupted zip or bug: expected \" + this.centralDirRecords + \" records in central dir, got \" + this.files.length);\n            } else {\n                // We found some records but not all.\n                // Something is wrong but we got something for the user: no error here.\n                // console.warn(\"expected\", this.centralDirRecords, \"records in central dir, got\", this.files.length);\n            }\n        }\n    },\n    /**\n     * Read the end of central directory.\n     */\n    readEndOfCentral: function() {\n        var offset = this.reader.lastIndexOfSignature(sig.CENTRAL_DIRECTORY_END);\n        if (offset < 0) {\n            // Check if the content is a truncated zip or complete garbage.\n            // A \"LOCAL_FILE_HEADER\" is not required at the beginning (auto\n            // extractible zip for example) but it can give a good hint.\n            // If an ajax request was used without responseType, we will also\n            // get unreadable data.\n            var isGarbage = !this.isSignature(0, sig.LOCAL_FILE_HEADER);\n\n            if (isGarbage) {\n                throw new Error(\"Can't find end of central directory : is this a zip file ? \" +\n                                \"If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html\");\n            } else {\n                throw new Error(\"Corrupted zip: can't find end of central directory\");\n            }\n\n        }\n        this.reader.setIndex(offset);\n        var endOfCentralDirOffset = offset;\n        this.checkSignature(sig.CENTRAL_DIRECTORY_END);\n        this.readBlockEndOfCentral();\n\n\n        /* extract from the zip spec :\n            4)  If one of the fields in the end of central directory\n                record is too small to hold required data, the field\n                should be set to -1 (0xFFFF or 0xFFFFFFFF) and the\n                ZIP64 format record should be created.\n            5)  The end of central directory record and the\n                Zip64 end of central directory locator record must\n                reside on the same disk when splitting or spanning\n                an archive.\n         */\n        if (this.diskNumber === utils.MAX_VALUE_16BITS || this.diskWithCentralDirStart === utils.MAX_VALUE_16BITS || this.centralDirRecordsOnThisDisk === utils.MAX_VALUE_16BITS || this.centralDirRecords === utils.MAX_VALUE_16BITS || this.centralDirSize === utils.MAX_VALUE_32BITS || this.centralDirOffset === utils.MAX_VALUE_32BITS) {\n            this.zip64 = true;\n\n            /*\n            Warning : the zip64 extension is supported, but ONLY if the 64bits integer read from\n            the zip file can fit into a 32bits integer. This cannot be solved : JavaScript represents\n            all numbers as 64-bit double precision IEEE 754 floating point numbers.\n            So, we have 53bits for integers and bitwise operations treat everything as 32bits.\n            see https://developer.mozilla.org/en-US/docs/JavaScript/Reference/Operators/Bitwise_Operators\n            and http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-262.pdf section 8.5\n            */\n\n            // should look for a zip64 EOCD locator\n            offset = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n            if (offset < 0) {\n                throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory locator\");\n            }\n            this.reader.setIndex(offset);\n            this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n            this.readBlockZip64EndOfCentralLocator();\n\n            // now the zip64 EOCD record\n            if (!this.isSignature(this.relativeOffsetEndOfZip64CentralDir, sig.ZIP64_CENTRAL_DIRECTORY_END)) {\n                // console.warn(\"ZIP64 end of central directory not where expected.\");\n                this.relativeOffsetEndOfZip64CentralDir = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n                if (this.relativeOffsetEndOfZip64CentralDir < 0) {\n                    throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory\");\n                }\n            }\n            this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir);\n            this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n            this.readBlockZip64EndOfCentral();\n        }\n\n        var expectedEndOfCentralDirOffset = this.centralDirOffset + this.centralDirSize;\n        if (this.zip64) {\n            expectedEndOfCentralDirOffset += 20; // end of central dir 64 locator\n            expectedEndOfCentralDirOffset += 12 /* should not include the leading 12 bytes */ + this.zip64EndOfCentralSize;\n        }\n\n        var extraBytes = endOfCentralDirOffset - expectedEndOfCentralDirOffset;\n\n        if (extraBytes > 0) {\n            // console.warn(extraBytes, \"extra bytes at beginning or within zipfile\");\n            if (this.isSignature(endOfCentralDirOffset, sig.CENTRAL_FILE_HEADER)) {\n                // The offsets seem wrong, but we have something at the specified offset.\n                // So… we keep it.\n            } else {\n                // the offset is wrong, update the \"zero\" of the reader\n                // this happens if data has been prepended (crx files for example)\n                this.reader.zero = extraBytes;\n            }\n        } else if (extraBytes < 0) {\n            throw new Error(\"Corrupted zip: missing \" + Math.abs(extraBytes) + \" bytes.\");\n        }\n    },\n    prepareReader: function(data) {\n        this.reader = readerFor(data);\n    },\n    /**\n     * Read a zip file and create ZipEntries.\n     * @param {String|ArrayBuffer|Uint8Array|Buffer} data the binary string representing a zip file.\n     */\n    load: function(data) {\n        this.prepareReader(data);\n        this.readEndOfCentral();\n        this.readCentralDir();\n        this.readLocalFiles();\n    }\n};\n// }}} end of ZipEntries\nmodule.exports = ZipEntries;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/zipEntries.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/zipEntry.js":
/*!********************************************!*\
  !*** ./node_modules/jszip/lib/zipEntry.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar readerFor = __webpack_require__(/*! ./reader/readerFor */ \"(ssr)/./node_modules/jszip/lib/reader/readerFor.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/./node_modules/jszip/lib/compressedObject.js\");\nvar crc32fn = __webpack_require__(/*! ./crc32 */ \"(ssr)/./node_modules/jszip/lib/crc32.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar compressions = __webpack_require__(/*! ./compressions */ \"(ssr)/./node_modules/jszip/lib/compressions.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\n\nvar MADE_BY_DOS = 0x00;\nvar MADE_BY_UNIX = 0x03;\n\n/**\n * Find a compression registered in JSZip.\n * @param {string} compressionMethod the method magic to find.\n * @return {Object|null} the JSZip compression object, null if none found.\n */\nvar findCompression = function(compressionMethod) {\n    for (var method in compressions) {\n        if (!Object.prototype.hasOwnProperty.call(compressions, method)) {\n            continue;\n        }\n        if (compressions[method].magic === compressionMethod) {\n            return compressions[method];\n        }\n    }\n    return null;\n};\n\n// class ZipEntry {{{\n/**\n * An entry in the zip file.\n * @constructor\n * @param {Object} options Options of the current file.\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntry(options, loadOptions) {\n    this.options = options;\n    this.loadOptions = loadOptions;\n}\nZipEntry.prototype = {\n    /**\n     * say if the file is encrypted.\n     * @return {boolean} true if the file is encrypted, false otherwise.\n     */\n    isEncrypted: function() {\n        // bit 1 is set\n        return (this.bitFlag & 0x0001) === 0x0001;\n    },\n    /**\n     * say if the file has utf-8 filename/comment.\n     * @return {boolean} true if the filename/comment is in utf-8, false otherwise.\n     */\n    useUTF8: function() {\n        // bit 11 is set\n        return (this.bitFlag & 0x0800) === 0x0800;\n    },\n    /**\n     * Read the local part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readLocalPart: function(reader) {\n        var compression, localExtraFieldsLength;\n\n        // we already know everything from the central dir !\n        // If the central dir data are false, we are doomed.\n        // On the bright side, the local part is scary  : zip64, data descriptors, both, etc.\n        // The less data we get here, the more reliable this should be.\n        // Let's skip the whole header and dash to the data !\n        reader.skip(22);\n        // in some zip created on windows, the filename stored in the central dir contains \\ instead of /.\n        // Strangely, the filename here is OK.\n        // I would love to treat these zip files as corrupted (see http://www.info-zip.org/FAQ.html#backslashes\n        // or APPNOTE#********, \"All slashes MUST be forward slashes '/'\") but there are a lot of bad zip generators...\n        // Search \"unzip mismatching \"local\" filename continuing with \"central\" filename version\" on\n        // the internet.\n        //\n        // I think I see the logic here : the central directory is used to display\n        // content and the local directory is used to extract the files. Mixing / and \\\n        // may be used to display \\ to windows users and use / when extracting the files.\n        // Unfortunately, this lead also to some issues : http://seclists.org/fulldisclosure/2009/Sep/394\n        this.fileNameLength = reader.readInt(2);\n        localExtraFieldsLength = reader.readInt(2); // can't be sure this will be the same as the central dir\n        // the fileName is stored as binary data, the handleUTF8 method will take care of the encoding.\n        this.fileName = reader.readData(this.fileNameLength);\n        reader.skip(localExtraFieldsLength);\n\n        if (this.compressedSize === -1 || this.uncompressedSize === -1) {\n            throw new Error(\"Bug or corrupted zip : didn't get enough information from the central directory \" + \"(compressedSize === -1 || uncompressedSize === -1)\");\n        }\n\n        compression = findCompression(this.compressionMethod);\n        if (compression === null) { // no compression found\n            throw new Error(\"Corrupted zip : compression \" + utils.pretty(this.compressionMethod) + \" unknown (inner file : \" + utils.transformTo(\"string\", this.fileName) + \")\");\n        }\n        this.decompressed = new CompressedObject(this.compressedSize, this.uncompressedSize, this.crc32, compression, reader.readData(this.compressedSize));\n    },\n\n    /**\n     * Read the central part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readCentralPart: function(reader) {\n        this.versionMadeBy = reader.readInt(2);\n        reader.skip(2);\n        // this.versionNeeded = reader.readInt(2);\n        this.bitFlag = reader.readInt(2);\n        this.compressionMethod = reader.readString(2);\n        this.date = reader.readDate();\n        this.crc32 = reader.readInt(4);\n        this.compressedSize = reader.readInt(4);\n        this.uncompressedSize = reader.readInt(4);\n        var fileNameLength = reader.readInt(2);\n        this.extraFieldsLength = reader.readInt(2);\n        this.fileCommentLength = reader.readInt(2);\n        this.diskNumberStart = reader.readInt(2);\n        this.internalFileAttributes = reader.readInt(2);\n        this.externalFileAttributes = reader.readInt(4);\n        this.localHeaderOffset = reader.readInt(4);\n\n        if (this.isEncrypted()) {\n            throw new Error(\"Encrypted zip are not supported\");\n        }\n\n        // will be read in the local part, see the comments there\n        reader.skip(fileNameLength);\n        this.readExtraFields(reader);\n        this.parseZIP64ExtraField(reader);\n        this.fileComment = reader.readData(this.fileCommentLength);\n    },\n\n    /**\n     * Parse the external file attributes and get the unix/dos permissions.\n     */\n    processAttributes: function () {\n        this.unixPermissions = null;\n        this.dosPermissions = null;\n        var madeBy = this.versionMadeBy >> 8;\n\n        // Check if we have the DOS directory flag set.\n        // We look for it in the DOS and UNIX permissions\n        // but some unknown platform could set it as a compatibility flag.\n        this.dir = this.externalFileAttributes & 0x0010 ? true : false;\n\n        if(madeBy === MADE_BY_DOS) {\n            // first 6 bits (0 to 5)\n            this.dosPermissions = this.externalFileAttributes & 0x3F;\n        }\n\n        if(madeBy === MADE_BY_UNIX) {\n            this.unixPermissions = (this.externalFileAttributes >> 16) & 0xFFFF;\n            // the octal permissions are in (this.unixPermissions & 0x01FF).toString(8);\n        }\n\n        // fail safe : if the name ends with a / it probably means a folder\n        if (!this.dir && this.fileNameStr.slice(-1) === \"/\") {\n            this.dir = true;\n        }\n    },\n\n    /**\n     * Parse the ZIP64 extra field and merge the info in the current ZipEntry.\n     * @param {DataReader} reader the reader to use.\n     */\n    parseZIP64ExtraField: function() {\n        if (!this.extraFields[0x0001]) {\n            return;\n        }\n\n        // should be something, preparing the extra reader\n        var extraReader = readerFor(this.extraFields[0x0001].value);\n\n        // I really hope that these 64bits integer can fit in 32 bits integer, because js\n        // won't let us have more.\n        if (this.uncompressedSize === utils.MAX_VALUE_32BITS) {\n            this.uncompressedSize = extraReader.readInt(8);\n        }\n        if (this.compressedSize === utils.MAX_VALUE_32BITS) {\n            this.compressedSize = extraReader.readInt(8);\n        }\n        if (this.localHeaderOffset === utils.MAX_VALUE_32BITS) {\n            this.localHeaderOffset = extraReader.readInt(8);\n        }\n        if (this.diskNumberStart === utils.MAX_VALUE_32BITS) {\n            this.diskNumberStart = extraReader.readInt(4);\n        }\n    },\n    /**\n     * Read the central part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readExtraFields: function(reader) {\n        var end = reader.index + this.extraFieldsLength,\n            extraFieldId,\n            extraFieldLength,\n            extraFieldValue;\n\n        if (!this.extraFields) {\n            this.extraFields = {};\n        }\n\n        while (reader.index + 4 < end) {\n            extraFieldId = reader.readInt(2);\n            extraFieldLength = reader.readInt(2);\n            extraFieldValue = reader.readData(extraFieldLength);\n\n            this.extraFields[extraFieldId] = {\n                id: extraFieldId,\n                length: extraFieldLength,\n                value: extraFieldValue\n            };\n        }\n\n        reader.setIndex(end);\n    },\n    /**\n     * Apply an UTF8 transformation if needed.\n     */\n    handleUTF8: function() {\n        var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n        if (this.useUTF8()) {\n            this.fileNameStr = utf8.utf8decode(this.fileName);\n            this.fileCommentStr = utf8.utf8decode(this.fileComment);\n        } else {\n            var upath = this.findExtraFieldUnicodePath();\n            if (upath !== null) {\n                this.fileNameStr = upath;\n            } else {\n                // ASCII text or unsupported code page\n                var fileNameByteArray =  utils.transformTo(decodeParamType, this.fileName);\n                this.fileNameStr = this.loadOptions.decodeFileName(fileNameByteArray);\n            }\n\n            var ucomment = this.findExtraFieldUnicodeComment();\n            if (ucomment !== null) {\n                this.fileCommentStr = ucomment;\n            } else {\n                // ASCII text or unsupported code page\n                var commentByteArray =  utils.transformTo(decodeParamType, this.fileComment);\n                this.fileCommentStr = this.loadOptions.decodeFileName(commentByteArray);\n            }\n        }\n    },\n\n    /**\n     * Find the unicode path declared in the extra field, if any.\n     * @return {String} the unicode path, null otherwise.\n     */\n    findExtraFieldUnicodePath: function() {\n        var upathField = this.extraFields[0x7075];\n        if (upathField) {\n            var extraReader = readerFor(upathField.value);\n\n            // wrong version\n            if (extraReader.readInt(1) !== 1) {\n                return null;\n            }\n\n            // the crc of the filename changed, this field is out of date.\n            if (crc32fn(this.fileName) !== extraReader.readInt(4)) {\n                return null;\n            }\n\n            return utf8.utf8decode(extraReader.readData(upathField.length - 5));\n        }\n        return null;\n    },\n\n    /**\n     * Find the unicode comment declared in the extra field, if any.\n     * @return {String} the unicode comment, null otherwise.\n     */\n    findExtraFieldUnicodeComment: function() {\n        var ucommentField = this.extraFields[0x6375];\n        if (ucommentField) {\n            var extraReader = readerFor(ucommentField.value);\n\n            // wrong version\n            if (extraReader.readInt(1) !== 1) {\n                return null;\n            }\n\n            // the crc of the comment changed, this field is out of date.\n            if (crc32fn(this.fileComment) !== extraReader.readInt(4)) {\n                return null;\n            }\n\n            return utf8.utf8decode(extraReader.readData(ucommentField.length - 5));\n        }\n        return null;\n    }\n};\nmodule.exports = ZipEntry;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/zipEntry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/zipObject.js":
/*!*********************************************!*\
  !*** ./node_modules/jszip/lib/zipObject.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar StreamHelper = __webpack_require__(/*! ./stream/StreamHelper */ \"(ssr)/./node_modules/jszip/lib/stream/StreamHelper.js\");\nvar DataWorker = __webpack_require__(/*! ./stream/DataWorker */ \"(ssr)/./node_modules/jszip/lib/stream/DataWorker.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/./node_modules/jszip/lib/compressedObject.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * A simple object representing a file in the zip file.\n * @constructor\n * @param {string} name the name of the file\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data\n * @param {Object} options the options of the file\n */\nvar ZipObject = function(name, data, options) {\n    this.name = name;\n    this.dir = options.dir;\n    this.date = options.date;\n    this.comment = options.comment;\n    this.unixPermissions = options.unixPermissions;\n    this.dosPermissions = options.dosPermissions;\n\n    this._data = data;\n    this._dataBinary = options.binary;\n    // keep only the compression\n    this.options = {\n        compression : options.compression,\n        compressionOptions : options.compressionOptions\n    };\n};\n\nZipObject.prototype = {\n    /**\n     * Create an internal stream for the content of this object.\n     * @param {String} type the type of each chunk.\n     * @return StreamHelper the stream.\n     */\n    internalStream: function (type) {\n        var result = null, outputType = \"string\";\n        try {\n            if (!type) {\n                throw new Error(\"No output type specified.\");\n            }\n            outputType = type.toLowerCase();\n            var askUnicodeString = outputType === \"string\" || outputType === \"text\";\n            if (outputType === \"binarystring\" || outputType === \"text\") {\n                outputType = \"string\";\n            }\n            result = this._decompressWorker();\n\n            var isUnicodeString = !this._dataBinary;\n\n            if (isUnicodeString && !askUnicodeString) {\n                result = result.pipe(new utf8.Utf8EncodeWorker());\n            }\n            if (!isUnicodeString && askUnicodeString) {\n                result = result.pipe(new utf8.Utf8DecodeWorker());\n            }\n        } catch (e) {\n            result = new GenericWorker(\"error\");\n            result.error(e);\n        }\n\n        return new StreamHelper(result, outputType, \"\");\n    },\n\n    /**\n     * Prepare the content in the asked type.\n     * @param {String} type the type of the result.\n     * @param {Function} onUpdate a function to call on each internal update.\n     * @return Promise the promise of the result.\n     */\n    async: function (type, onUpdate) {\n        return this.internalStream(type).accumulate(onUpdate);\n    },\n\n    /**\n     * Prepare the content as a nodejs stream.\n     * @param {String} type the type of each chunk.\n     * @param {Function} onUpdate a function to call on each internal update.\n     * @return Stream the stream.\n     */\n    nodeStream: function (type, onUpdate) {\n        return this.internalStream(type || \"nodebuffer\").toNodejsStream(onUpdate);\n    },\n\n    /**\n     * Return a worker for the compressed content.\n     * @private\n     * @param {Object} compression the compression object to use.\n     * @param {Object} compressionOptions the options to use when compressing.\n     * @return Worker the worker.\n     */\n    _compressWorker: function (compression, compressionOptions) {\n        if (\n            this._data instanceof CompressedObject &&\n            this._data.compression.magic === compression.magic\n        ) {\n            return this._data.getCompressedWorker();\n        } else {\n            var result = this._decompressWorker();\n            if(!this._dataBinary) {\n                result = result.pipe(new utf8.Utf8EncodeWorker());\n            }\n            return CompressedObject.createWorkerFrom(result, compression, compressionOptions);\n        }\n    },\n    /**\n     * Return a worker for the decompressed content.\n     * @private\n     * @return Worker the worker.\n     */\n    _decompressWorker : function () {\n        if (this._data instanceof CompressedObject) {\n            return this._data.getContentWorker();\n        } else if (this._data instanceof GenericWorker) {\n            return this._data;\n        } else {\n            return new DataWorker(this._data);\n        }\n    }\n};\n\nvar removedMethods = [\"asText\", \"asBinary\", \"asNodeBuffer\", \"asUint8Array\", \"asArrayBuffer\"];\nvar removedFn = function () {\n    throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n};\n\nfor(var i = 0; i < removedMethods.length; i++) {\n    ZipObject.prototype[removedMethods[i]] = removedFn;\n}\nmodule.exports = ZipObject;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/zipObject.js\n");

/***/ })

};
;