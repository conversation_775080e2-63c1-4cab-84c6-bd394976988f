"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/flairup";
exports.ids = ["vendor-chunks/flairup"];
exports.modules = {

/***/ "(ssr)/./node_modules/flairup/dist/esm/index.js":
/*!************************************************!*\
  !*** ./node_modules/flairup/dist/esm/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSheet: () => (/* binding */ createSheet),\n/* harmony export */   cx: () => (/* binding */ cx)\n/* harmony export */ });\n// src/utils/asArray.ts\nfunction asArray(v) {\n  return [].concat(v);\n}\n\n// src/utils/is.ts\nfunction isPsuedoSelector(selector) {\n  return selector.startsWith(\":\");\n}\nfunction isStyleCondition(selector) {\n  return isString(selector) && (selector === \"*\" || selector.length > 1 && \":>~.+*\".includes(selector.slice(0, 1)) || isImmediatePostcondition(selector));\n}\nfunction isValidProperty(property, value) {\n  return (isString(value) || typeof value === \"number\") && !isCssVariables(property) && !isPsuedoSelector(property) && !isMediaQuery(property);\n}\nfunction isMediaQuery(selector) {\n  return selector.startsWith(\"@media\");\n}\nfunction isDirectClass(selector) {\n  return selector === \".\";\n}\nfunction isCssVariables(selector) {\n  return selector === \"--\";\n}\nfunction isString(value) {\n  return value + \"\" === value;\n}\nfunction isImmediatePostcondition(value) {\n  return isString(value) && (value.startsWith(\"&\") || isPsuedoSelector(value));\n}\n\n// src/utils/joinTruthy.ts\nfunction joinTruthy(arr, delimiter = \"\") {\n  return arr.filter(Boolean).join(delimiter);\n}\n\n// src/utils/stableHash.ts\nfunction stableHash(prefix, seed) {\n  let hash = 0;\n  if (seed.length === 0)\n    return hash.toString();\n  for (let i = 0; i < seed.length; i++) {\n    const char = seed.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash;\n  }\n  return `${prefix ?? \"cl\"}_${hash.toString(36)}`;\n}\n\n// src/utils/stringManipulators.ts\nfunction handlePropertyValue(property, value) {\n  if (property === \"content\") {\n    return `\"${value}\"`;\n  }\n  return value;\n}\nfunction camelCaseToDash(str) {\n  return str.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase();\n}\nfunction joinedProperty(property, value) {\n  return `${property}:${value}`;\n}\nfunction toClass(str) {\n  return str ? `.${str}` : \"\";\n}\nfunction appendString(base, line) {\n  return base ? `${base}\n${line}` : line;\n}\n\n// src/Rule.ts\nvar Rule = class _Rule {\n  constructor(sheet, property, value, selector) {\n    this.sheet = sheet;\n    this.property = property;\n    this.value = value;\n    this.selector = selector;\n    this.property = property;\n    this.value = value;\n    this.joined = joinedProperty(property, value);\n    const joinedConditions = this.selector.preconditions.concat(\n      this.selector.postconditions\n    );\n    this.hash = this.selector.hasConditions ? this.selector.scopeClassName : stableHash(this.sheet.name, this.joined);\n    this.key = joinTruthy([this.joined, joinedConditions, this.hash]);\n  }\n  toString() {\n    let selectors = mergeSelectors(this.selector.preconditions, {\n      right: this.hash\n    });\n    selectors = mergeSelectors(this.selector.postconditions, {\n      left: selectors\n    });\n    return `${selectors} {${_Rule.genRule(this.property, this.value)}}`;\n  }\n  static genRule(property, value) {\n    const transformedProperty = camelCaseToDash(property);\n    return joinedProperty(\n      transformedProperty,\n      handlePropertyValue(property, value)\n    ) + \";\";\n  }\n};\nfunction mergeSelectors(selectors, { left = \"\", right = \"\" } = {}) {\n  const output = selectors.reduce((selectors2, current) => {\n    if (isPsuedoSelector(current)) {\n      return selectors2 + current;\n    }\n    if (isImmediatePostcondition(current)) {\n      return selectors2 + current.slice(1);\n    }\n    return joinTruthy([selectors2, current], \" \");\n  }, left);\n  return joinTruthy([output, toClass(right)], \" \");\n}\nvar Selector = class _Selector {\n  constructor(sheet, scopeName = null, {\n    preconditions,\n    postconditions\n  } = {}) {\n    this.sheet = sheet;\n    this.preconditions = [];\n    this.scopeClassName = null;\n    this.scopeName = null;\n    this.postconditions = [];\n    this.preconditions = preconditions ? asArray(preconditions) : [];\n    this.postconditions = postconditions ? asArray(postconditions) : [];\n    this.setScope(scopeName);\n  }\n  setScope(scopeName) {\n    if (!scopeName) {\n      return this;\n    }\n    if (!this.scopeClassName) {\n      this.scopeName = scopeName;\n      this.scopeClassName = stableHash(\n        this.sheet.name,\n        // adding the count guarantees uniqueness across style.create calls\n        scopeName + this.sheet.count\n      );\n    }\n    return this;\n  }\n  get hasConditions() {\n    return this.preconditions.length > 0 || this.postconditions.length > 0;\n  }\n  addScope(scopeName) {\n    return new _Selector(this.sheet, scopeName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions\n    });\n  }\n  addPrecondition(precondition) {\n    return new _Selector(this.sheet, this.scopeClassName, {\n      postconditions: this.postconditions,\n      preconditions: this.preconditions.concat(precondition)\n    });\n  }\n  addPostcondition(postcondition) {\n    return new _Selector(this.sheet, this.scopeClassName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions.concat(postcondition)\n    });\n  }\n  createRule(property, value) {\n    return new Rule(this.sheet, property, value, this);\n  }\n};\n\n// src/Sheet.ts\nvar Sheet = class {\n  constructor(name, rootNode) {\n    this.name = name;\n    this.rootNode = rootNode;\n    // Hash->css\n    this.storedStyles = {};\n    // styles->hash\n    this.storedClasses = {};\n    this.style = \"\";\n    this.count = 0;\n    this.id = `flairup-${name}`;\n    this.styleTag = this.createStyleTag();\n  }\n  getStyle() {\n    return this.style;\n  }\n  append(css) {\n    this.style = appendString(this.style, css);\n  }\n  apply() {\n    this.count++;\n    if (!this.styleTag) {\n      return;\n    }\n    this.styleTag.innerHTML = this.style;\n  }\n  isApplied() {\n    return !!this.styleTag;\n  }\n  createStyleTag() {\n    if (typeof document === \"undefined\" || this.isApplied() || // Explicitly disallow mounting to the DOM\n    this.rootNode === null) {\n      return this.styleTag;\n    }\n    const styleTag = document.createElement(\"style\");\n    styleTag.type = \"text/css\";\n    styleTag.id = this.id;\n    (this.rootNode ?? document.head).appendChild(styleTag);\n    return styleTag;\n  }\n  addRule(rule) {\n    const storedClass = this.storedClasses[rule.key];\n    if (isString(storedClass)) {\n      return storedClass;\n    }\n    this.storedClasses[rule.key] = rule.hash;\n    this.storedStyles[rule.hash] = [rule.property, rule.value];\n    this.append(rule.toString());\n    return rule.hash;\n  }\n};\n\n// src/utils/forIn.ts\nfunction forIn(obj, fn) {\n  for (const key in obj) {\n    fn(key.trim(), obj[key]);\n  }\n}\n\n// src/cx.ts\nfunction cx(...args) {\n  const classes = args.reduce((classes2, arg) => {\n    if (arg instanceof Set) {\n      classes2.push(...arg);\n    } else if (typeof arg === \"string\") {\n      classes2.push(arg);\n    } else if (Array.isArray(arg)) {\n      classes2.push(cx(...arg));\n    } else if (typeof arg === \"object\") {\n      Object.entries(arg).forEach(([key, value]) => {\n        if (value) {\n          classes2.push(key);\n        }\n      });\n    }\n    return classes2;\n  }, []);\n  return joinTruthy(classes, \" \").trim();\n}\n\n// src/index.ts\nfunction createSheet(name, rootNode) {\n  const sheet = new Sheet(name, rootNode);\n  return {\n    create,\n    getStyle: sheet.getStyle.bind(sheet),\n    isApplied: sheet.isApplied.bind(sheet)\n  };\n  function create(styles) {\n    const scopedStyles = {};\n    iteratePreconditions(sheet, styles, new Selector(sheet)).forEach(\n      ([scopeName, styles2, selector]) => {\n        iterateStyles(sheet, styles2, selector).forEach(\n          (className) => {\n            addScopedStyle(scopeName, className);\n          }\n        );\n      }\n    );\n    sheet.apply();\n    return scopedStyles;\n    function addScopedStyle(name2, className) {\n      scopedStyles[name2] = scopedStyles[name2] ?? /* @__PURE__ */ new Set();\n      scopedStyles[name2].add(className);\n    }\n  }\n}\nfunction iteratePreconditions(sheet, styles, selector) {\n  const output = [];\n  forIn(styles, (key, value) => {\n    if (isStyleCondition(key)) {\n      return iteratePreconditions(\n        sheet,\n        value,\n        selector.addPrecondition(key)\n      ).forEach((item) => output.push(item));\n    }\n    output.push([key, styles[key], selector.addScope(key)]);\n  });\n  return output;\n}\nfunction iterateStyles(sheet, styles, selector) {\n  const output = /* @__PURE__ */ new Set();\n  forIn(styles, (property, value) => {\n    let res = [];\n    if (isStyleCondition(property)) {\n      res = iterateStyles(\n        sheet,\n        value,\n        selector.addPostcondition(property)\n      );\n    } else if (isDirectClass(property)) {\n      res = asArray(value);\n    } else if (isMediaQuery(property)) {\n      res = handleMediaQuery(sheet, value, property, selector);\n    } else if (isCssVariables(property)) {\n      res = cssVariablesBlock(sheet, value, selector);\n    } else if (isValidProperty(property, value)) {\n      const rule = selector.createRule(property, value);\n      sheet.addRule(rule);\n      output.add(rule.hash);\n    }\n    return addEachClass(res, output);\n  });\n  return output;\n}\nfunction addEachClass(list, to) {\n  list.forEach((className) => to.add(className));\n  return to;\n}\nfunction cssVariablesBlock(sheet, styles, selector) {\n  const classes = /* @__PURE__ */ new Set();\n  const chunkRows = [];\n  forIn(styles, (property, value) => {\n    if (isValidProperty(property, value)) {\n      chunkRows.push(Rule.genRule(property, value));\n      return;\n    }\n    const res = iterateStyles(sheet, value ?? {}, selector);\n    addEachClass(res, classes);\n  });\n  if (!selector.scopeClassName) {\n    return classes;\n  }\n  if (chunkRows.length) {\n    const output = chunkRows.join(\" \");\n    sheet.append(\n      `${mergeSelectors(selector.preconditions, {\n        right: selector.scopeClassName\n      })} {${output}}`\n    );\n  }\n  classes.add(selector.scopeClassName);\n  return classes;\n}\nfunction handleMediaQuery(sheet, styles, mediaQuery, selector) {\n  sheet.append(mediaQuery + \" {\");\n  const output = iterateStyles(sheet, styles, selector);\n  sheet.append(\"}\");\n  return output;\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/flairup/dist/esm/index.js\n");

/***/ })

};
;