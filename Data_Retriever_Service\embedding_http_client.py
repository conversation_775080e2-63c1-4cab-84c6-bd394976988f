#!/usr/bin/env python3
"""
HTTP Client for communicating with the Data_Ingestion_Service for embedding functionality
"""

import logging
import requests
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from langchain_core.embeddings import Embeddings
from langchain_qdrant import FastEmbedSparse

logger = logging.getLogger(__name__)

from config import embedding_api_url

class SparseVector:
    """Simple sparse vector class that mimics FastEmbedSparse output format"""

    def __init__(self, indices: List[int], values: List[float]):
        self.indices = indices
        self.values = values

    @classmethod
    def from_dict(cls, sparse_dict: Dict[str, float]) -> 'SparseVector':
        """Convert dictionary format to SparseVector"""
        indices = []
        values = []
        for key, value in sparse_dict.items():
            if key.startswith("token_"):
                try:
                    index = int(key.replace("token_", ""))
                    indices.append(index)
                    values.append(value)
                except ValueError:
                    continue
        return cls(indices, values)


@dataclass
class EmbeddingConfig:
    """Configuration for embedding API client"""
    api_url: str = embedding_api_url
    timeout: int = 30
    retry_attempts: int = 3


class EmbeddingClient:
    """HTTP Client for communicating with the Data_Ingestion_Service Embedding API"""

    def __init__(self, config: EmbeddingConfig):
        self.config = config
        self.session = requests.Session()

    def _make_request(self, endpoint: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Make HTTP request to embedding API"""
        url = f"{self.config.api_url}/{endpoint}"

        for attempt in range(self.config.retry_attempts):
            try:
                response = self.session.post(
                    url,
                    json=payload,
                    timeout=self.config.timeout
                )
                response.raise_for_status()
                return response.json()
            except requests.exceptions.RequestException as e:
                logger.warning(f"Request attempt {attempt + 1} failed: {e}")
                if attempt == self.config.retry_attempts - 1:
                    raise

        raise Exception(f"Failed to connect to embedding API after {self.config.retry_attempts} attempts")

    def generate_dense_embeddings(self, texts: List[str], normalize: bool = True) -> List[List[float]]:
        """Generate dense embeddings via API"""
        payload = {
            "texts": texts,
            "embedding_type": "dense",
            "normalize_embeddings": normalize
        }

        response = self._make_request("embed", payload)
        return response["embeddings"]

    def generate_sparse_embeddings(self, texts: List[str]) -> List[Dict[str, float]]:
        """Generate sparse embeddings via API"""
        payload = {
            "texts": texts,
            "embedding_type": "sparse"
        }

        response = self._make_request("embed", payload)
        return response["sparse_embeddings"]

    def generate_sparse_vectors(self, texts: List[str]) -> List[SparseVector]:
        """Generate sparse embeddings as SparseVector objects for Qdrant compatibility"""
        sparse_dicts = self.generate_sparse_embeddings(texts)
        sparse_vectors = []
        for sparse_dict in sparse_dicts:
            sparse_vector = SparseVector.from_dict(sparse_dict)
            sparse_vectors.append(sparse_vector)
        return sparse_vectors

    def generate_hybrid_embeddings(self, texts: List[str], normalize: bool = True) -> tuple[List[List[float]], List[Dict[str, float]]]:
        """Generate hybrid embeddings (dense + sparse) via API"""
        payload = {
            "texts": texts,
            "embedding_type": "hybrid",
            "normalize_embeddings": normalize
        }

        response = self._make_request("embed", payload)
        return response["embeddings"], response["sparse_embeddings"]

    def health_check(self) -> Dict[str, Any]:
        """Check API health"""
        try:
            response = self.session.get(f"{self.config.api_url}/health", timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Health check failed: {e}")
            return {"status": "unhealthy", "error": str(e)}

    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        try:
            response = self.session.get(f"{self.config.api_url}/model-info", timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get model info: {e}")
            raise


class DenseEmbeddingWrapper(Embeddings):
    """Wrapper class that implements LangChain Embeddings interface but uses HTTP API client"""

    def __init__(self, api_client: EmbeddingClient, normalize: bool = True):
        self.api_client = api_client
        self.normalize = normalize

    def embed_query(self, text: str) -> List[float]:
        """Embed a single query text"""
        embeddings = self.api_client.generate_dense_embeddings([text], self.normalize)
        return embeddings[0] if embeddings else []

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed multiple documents"""
        return self.api_client.generate_dense_embeddings(texts, self.normalize)

    async def aembed_query(self, text: str) -> List[float]:
        """Async embed a single query text"""
        return self.embed_query(text)

    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """Async embed multiple documents"""
        return self.embed_documents(texts)


class SparseEmbeddingWrapper(FastEmbedSparse):
    """Wrapper class that implements FastEmbedSparse interface but uses HTTP API client"""

    def __init__(self, api_client: EmbeddingClient):
        # Don't call super().__init__() since we're not actually using FastEmbedSparse
        self.api_client = api_client
        # Set required attributes that FastEmbedSparse would have
        self.model_name = "api_sparse_wrapper"

    def embed_query(self, text: str) -> SparseVector:
        """Embed a single query text"""
        embeddings = self.api_client.generate_sparse_vectors([text])
        return embeddings[0] if embeddings else SparseVector([], [])

    def embed_documents(self, texts: List[str]) -> List[SparseVector]:
        """Embed multiple documents"""
        return self.api_client.generate_sparse_vectors(texts)

    async def aembed_query(self, text: str) -> SparseVector:
        """Async embed a single query text"""
        return self.embed_query(text)

    async def aembed_documents(self, texts: List[str]) -> List[SparseVector]:
        """Async embed multiple documents"""
        return self.embed_documents(texts)
