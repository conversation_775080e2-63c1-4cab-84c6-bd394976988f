"""
Add playground_type to user_spaces and extend knowledge_documents.document_type

This migration:
1. Adds playground_type column to user_spaces (default 'documents', NOT NULL, indexed)
2. Extends knowledge_documents.document_type to accept 'image' value

Run this migration manually:
    python Backend/migration/add_playground_type_migration.py
"""

import os
import sys
from pathlib import Path

# Add parent directory to path to import config
sys.path.insert(0, str(Path(__file__).parent.parent))

import asyncpg
import asyncio
from app.config import DATABASE_URL

async def run_migration():
    """Execute the migration"""
    print("Connecting to database...")
    # Convert SQLAlchemy-style URL to standard PostgreSQL DSN for asyncpg
    postgres_dsn = DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
    conn = await asyncpg.connect(postgres_dsn)
    
    try:
        print("\n=== Starting Migration: Add Playground Type Support ===\n")
        
        # Step 1: Add playground_type column to user_spaces
        print("Step 1: Adding playground_type column to user_spaces...")
        await conn.execute("""
            ALTER TABLE user_spaces 
            ADD COLUMN IF NOT EXISTS playground_type VARCHAR(20) NOT NULL DEFAULT 'documents';
        """)
        print("✓ Column added successfully")
        
        # Step 2: Create index on playground_type
        print("\nStep 2: Creating index on playground_type...")
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_user_spaces_playground_type 
            ON user_spaces(playground_type);
        """)
        print("✓ Index created successfully")
        
        # Step 3: Check if document_type uses ENUM or CHECK constraint
        print("\nStep 3: Checking document_type constraint type...")
        
        # Check for ENUM type
        enum_check = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM pg_type 
                WHERE typname = 'document_type_enum'
            );
        """)
        
        if enum_check:
            print("Found ENUM type for document_type, adding 'image' value...")
            # Add 'image' to ENUM if it doesn't exist
            await conn.execute("""
                DO $$ 
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM pg_enum 
                        WHERE enumlabel = 'image' 
                        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_type_enum')
                    ) THEN
                        ALTER TYPE document_type_enum ADD VALUE 'image';
                    END IF;
                END $$;
            """)
            print("✓ ENUM value 'image' added")
        else:
            # Check for CHECK constraint
            constraint_check = await conn.fetchval("""
                SELECT conname 
                FROM pg_constraint 
                WHERE conrelid = 'knowledge_documents'::regclass 
                AND contype = 'c' 
                AND conname LIKE '%document_type%'
                LIMIT 1;
            """)
            
            if constraint_check:
                print(f"Found CHECK constraint: {constraint_check}")
                print("Dropping old CHECK constraint...")
                await conn.execute(f"""
                    ALTER TABLE knowledge_documents 
                    DROP CONSTRAINT IF EXISTS {constraint_check};
                """)
                
                print("Creating new CHECK constraint with 'image' support...")
                await conn.execute("""
                    ALTER TABLE knowledge_documents 
                    ADD CONSTRAINT knowledge_documents_document_type_check 
                    CHECK (document_type IN ('pdf', 'text', 'docx', 'doc', 'ppt', 'pptx', 'xls', 'xlsx', 'image'));
                """)
                print("✓ CHECK constraint updated")
            else:
                print("No ENUM or CHECK constraint found on document_type")
                print("Adding CHECK constraint for document_type validation...")
                await conn.execute("""
                    ALTER TABLE knowledge_documents 
                    ADD CONSTRAINT knowledge_documents_document_type_check 
                    CHECK (document_type IN ('pdf', 'text', 'docx', 'doc', 'ppt', 'pptx', 'xls', 'xlsx', 'image'));
                """)
                print("✓ CHECK constraint created")
        
        # Step 4: Verify changes
        print("\nStep 4: Verifying migration...")
        
        # Check playground_type column
        playground_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'user_spaces' 
                AND column_name = 'playground_type'
            );
        """)
        
        if playground_exists:
            print("✓ playground_type column verified")
        else:
            raise Exception("playground_type column not found after migration!")
        
        # Check index
        index_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM pg_indexes 
                WHERE tablename = 'user_spaces' 
                AND indexname = 'idx_user_spaces_playground_type'
            );
        """)
        
        if index_exists:
            print("✓ playground_type index verified")
        else:
            raise Exception("playground_type index not found after migration!")
        
        print("\n=== Migration Completed Successfully ===\n")
        print("Summary:")
        print("- Added playground_type column to user_spaces (default: 'documents')")
        print("- Created index on playground_type")
        print("- Extended document_type to accept 'image' value")
        print("\nAll existing spaces default to 'documents' playground type.")
        print("New spaces can be created with 'images' playground type.")
        
    except Exception as e:
        print(f"\nMigration failed: {e}")
        raise
    finally:
        await conn.close()
        print("\nDatabase connection closed.")

if __name__ == "__main__":
    asyncio.run(run_migration())

