version: '3.8'

services:
  image-embedding-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: image-embedding-service
    ports:
      - "8019:8019"
    environment:
      - PORT=8019
      - HOST=0.0.0.0
      - MODEL_NAME=google/siglip-so400m-patch14-384
      - DEVICE=cuda
      - BATCH_SIZE=8
      - MAX_CAPTION_LENGTH=100
      - CACHE_DIR=/app/model_cache
      - LOG_LEVEL=INFO
    volumes:
      - ./model_cache:/app/model_cache
      - ./logs:/app/logs
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8019/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 180s

