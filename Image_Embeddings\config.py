import os
from dataclasses import dataclass
from dotenv import load_dotenv

load_dotenv()

@dataclass
class Config:
    """Configuration for Image Embedding Service"""
    
    port: int = int(os.getenv("PORT", 8019))
    host: str = os.getenv("HOST", "0.0.0.0")
    
    model_name: str = os.getenv(
        "MODEL_NAME", 
        "google/siglip-so400m-patch14-384"
    )
    
    device: str = os.getenv("DEVICE", "cuda")
    
    batch_size: int = int(os.getenv("BATCH_SIZE", 8))
    
    max_caption_length: int = int(os.getenv("MAX_CAPTION_LENGTH", 100))
    
    cache_dir: str = os.getenv("CACHE_DIR", "./model_cache")
    
    log_level: str = os.getenv("LOG_LEVEL", "INFO")


config = Config()

