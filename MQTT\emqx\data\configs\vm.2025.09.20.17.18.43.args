######################################################################
## Erlang VM Args
######################################################################

## NOTE:
##
## Arguments configured in this file might be overridden by configs from `emqx.conf`.
##
## Some basic VM arguments are to be configured in `emqx.conf`,
## such as `node.name` for `-name` and `node.cooke` for `-setcookie`.

## Sets the maximum number of simultaneously existing processes for this system.
## Preferably set in `emqx.conf`,
#+P 2097152

## Sets the maximum number of simultaneously existing ports for this system.
## Preferably set in `emqx.conf`,
#+Q 1048576

## Sets the maximum number of ETS tables
## Preferably set in `emqx.conf`,
#+e 262144

## Sets the maximum number of atoms the virtual machine can handle.
#+t 1048576

## Set how many times generational garbages collections can be done without
## forcing a fullsweep collection.
-env ERL_FULLSWEEP_AFTER 1000

## Heartbeat management; auto-restarts VM if it dies or becomes unresponsive
## (Disabled by default..use with caution!)
#-heart

## The shell is started in a restricted mode.
## In this mode, the shell evaluates a function call only if allowed.
## Prevent user from accidentally calling a function from the prompt that could harm a running system.
-stdlib restricted_shell emqx_restricted_shell

## Sets the distribution buffer busy limit (dist_buf_busy_limit).
## Preferably set in `emqx.conf`,
#+zdbbl 8192

## Sets default scheduler hint for port parallelism.
+spp true

## Sets the number of threads in async thread pool. Valid range is 0-1024.
## Increase the parameter if there are many simultaneous file I/O operations.
+A 4

## Sets the default heap size of processes to the size Size.
#+hms 233

## Sets the default binary virtual heap size of processes to the size Size.
#+hmbs 46422

## Sets the default maximum heap size of processes to the size Size.
## Defaults to 0, which means that no maximum heap size is used.
##For more information, see process_flag(max_heap_size, MaxHeapSize).
#+hmax 0

## Sets the default value for process flag message_queue_data. Defaults to on_heap.
#+hmqd on_heap | off_heap

## Sets the number of IO pollsets to use when polling for I/O.
#+IOp 1

## Sets the number of IO poll threads to use when polling for I/O.
## Increase this for the busy systems with many concurrent connection.
+IOt 4

## Sets the number of scheduler threads to create and scheduler threads to set online.
#+S 8:8

## Sets the number of dirty CPU scheduler threads to create and dirty CPU scheduler threads to set online.
#+SDcpu 8:8

## Sets the number of dirty I/O scheduler threads to create.
+SDio 8

## Suggested stack size, in kilowords, for scheduler threads.
#+sss 32

## Suggested stack size, in kilowords, for dirty CPU scheduler threads.
#+sssdcpu 40

## Suggested stack size, in kilowords, for dirty IO scheduler threads.
#+sssdio 40

## Sets scheduler bind type.
## Can be one of: u, ns, ts, ps, s, nnts, nnps, tnnps, db
#+sbt db

## Sets a user-defined CPU topology.
#+sct L0-3c0-3p0N0:L4-7c0-3p1N1

## Sets the mapping of warning messages for error_logger
#+W w

## Sets time warp mode: no_time_warp | single_time_warp | multi_time_warp
+C multi_time_warp

## Enable/disable Erlang monotonic time correction aginast OS.
## In some operating systems which cannot provide a stable monotonic time source,
## Erlang VM may exit with error log "monotonic time stepped backwards"
## In such cases, setting this option to 'false' can prevent the VM from restarting.
##
## NOTE!
## You typically never want to disable time correction.
## If time correction is disabled, Erlang monotonic time can warp forwards or stop,
## or even freeze for extended periods of time.
## There are then no guarantees that the frequency of the Erlang monotonic clock is accurate or stable.
+c true

## Prevents loading information about source filenames and line numbers.
#+L

## Specifies how long time (in milliseconds) to spend shutting down the system.
## See: http://erlang.org/doc/man/erl.html
-shutdown_time 30000

## Disable the code path caching feature to allow adding dynamic patch path using `-pa`.
-cache_boot_paths false

## patches dir
-pa "data/patches"

## Mnesia thresholds
-mnesia dump_log_write_threshold 5000
-mnesia dump_log_time_threshold 60000

## Disable os_mon's disksup by default
-os_mon start_disksup false

## Sets unicode as the printable character range when formatting binaries
+pc unicode

-pa "/opt/emqx/releases/5.8.7/consolidated"
-kernel prevent_overlapping_partitions false
-kernel net_ticktime 120
-env ERL_CRASH_DUMP_BYTES 104857600
-env ERL_CRASH_DUMP_SECONDS 30
-env ERL_CRASH_DUMP /opt/emqx/log/erl_crash.dump
+e 262144
+zdbbl 8192
+Q 1048576
-setcookie e8361c88f8713629b2e3db728d45bbfa529e331622efc5d801686ef344d94a67
+P 2097152
-name emqx@172.25.0.2
-mnesia dir '"/opt/emqx/data/mnesia/emqx@172.25.0.2"'
