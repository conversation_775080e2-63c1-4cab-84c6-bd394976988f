import mqtt from 'mqtt';
import { API_BASE_URL, getSecureToken } from './api';

interface MQTTConfig {
  userId: number;
  onMessage: (data: any) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: any) => void;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
}

interface MQTTConnectionInfo {
  token: string;
  expires_in: number;
  mqtt_host: string;
  mqtt_ws_port: number;
  mqtt_wss_port: number;
  topic_prefix: string;
}

export class MQTTManager {
  private client: mqtt.MqttClient | null = null;
  private config: MQTTConfig;
  private reconnectAttempts = 0;
  private allowReconnect = true;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private isIntentionalDisconnect = false;
  private messageQueue: any[] = [];
  private clientId: string;
  private connectionInfo: MQTTConnectionInfo | null = null;
  private tokenRefreshTimeout: NodeJS.Timeout | null = null;
  private presenceInterval: NodeJS.Timeout | null = null;
  
  constructor(config: MQTTConfig) {
    this.config = {
      maxReconnectAttempts: 10,
      reconnectDelay: 1000,
      ...config
    };
    
    this.clientId = this.getStoredClientId();
  }
  
  private getStoredClientId(): string {
    // Generate a persistent client ID for MQTT broker identification
    // Uses sessionStorage to maintain consistency across page refreshes
    const sessionKey = `mqtt_client_id_${this.config.userId}`;
    let stored = sessionStorage.getItem(sessionKey);

    if (!stored) {
      // Create unique client ID combining user ID, timestamp, and random string
      // This ensures no conflicts with other browser tabs/sessions
      stored = `user_${this.config.userId}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
      sessionStorage.setItem(sessionKey, stored);
    }

    return stored;
  }
  
  private async fetchBrokerToken(): Promise<MQTTConnectionInfo> {
    const token = getSecureToken();
    if (!token) {
      throw new Error('No authentication token available');
    }
    
    const response = await fetch(`${API_BASE_URL}/chat/mqtt-token`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch MQTT token: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  public async connect(): Promise<void> {
    if (this.client && this.client.connected) {
      return;
    }
    
    this.isIntentionalDisconnect = false;
    
    try {
      this.connectionInfo = await this.fetchBrokerToken();

      // Use WSS (secure WebSocket) through nginx proxy for HTTPS sites
      // This solves mixed content issues by proxying MQTT over HTTPS
      const isHttps = window.location.protocol === 'https:';
      const protocol = isHttps ? 'wss' : 'ws';
      
      let brokerUrl;
      if (isHttps) {
        // Use nginx proxy endpoint for secure connections
        brokerUrl = `${protocol}://${window.location.host}/mqtt`;
      } else {
        // Direct connection for non-HTTPS (development)
        const port = this.connectionInfo.mqtt_ws_port;
        brokerUrl = `${protocol}://${this.connectionInfo.mqtt_host}:${port}/mqtt`;
      }

      // Establish MQTT connection with broker-specific options
      this.client = mqtt.connect(brokerUrl, {
        clientId: this.clientId,              // Unique identifier for this client
        username: `user_${this.config.userId}`, // User identification for broker auth
        password: this.connectionInfo.token,  // JWT token from backend for authentication
        keepalive: 60,                        // Send ping every 60 seconds to keep connection alive
        clean: true,                          // Start with clean session (no retained messages)
        reconnectPeriod: 0,                   // Disable built-in reconnect (we handle it manually)
        connectTimeout: 30 * 1000,           // 30 second timeout for initial connection
        will: {                               // "Last will" message sent if client disconnects unexpectedly
          topic: `${this.connectionInfo.topic_prefix}/presence/${this.config.userId}`,
          payload: JSON.stringify({
            status: 'offline',
            last_seen: new Date().toISOString(),
            timestamp: new Date().toISOString()
          }),
          qos: 0,                            // At least once delivery guarantee
          retain: false                       // Don't retain the offline status message
        }
      });
      
      this.setupEventHandlers();
      
      this.scheduleTokenRefresh();
      
    } catch (error) {
      this.config.onError?.(error);
      this.scheduleReconnect();
    }
  }
  
  private setupEventHandlers(): void {
    if (!this.client) return;

    this.client.on('connect', () => {
      this.reconnectAttempts = 0;  // Reset reconnection counter on successful connect

      // Subscribe to personal event topic for receiving chat messages and notifications
      const eventTopic = `${this.connectionInfo!.topic_prefix}/chat/users/${this.config.userId}/events`;
      this.client!.subscribe(eventTopic, { qos: 1 }, (err) => {
        if (err) {
          this.config.onError?.(err);
        }
      });

      // Subscribe to presence topic to receive online/offline status of all users
      const presenceTopic = `${this.connectionInfo!.topic_prefix}/presence/+`;
      this.client!.subscribe(presenceTopic, { qos: 1 }, (err) => {
        if (err) {
          this.config.onError?.(err);
        }
      });

      // Immediately publish online status when connected
      this.publishPresence('online');

      // Set up periodic presence heartbeat every 30 seconds to maintain online status
      if (this.presenceInterval) clearInterval(this.presenceInterval);
      this.presenceInterval = setInterval(() => {
        this.publishPresence('online');
      }, 30000);  // 30 seconds matches backend expectations

      // Process any messages that were queued while disconnected
      this.processMessageQueue();

      // Notify application that connection is established
      this.config.onConnect?.();
    });
    
    this.client.on('message', (topic, payload) => {
      try {
        const data = JSON.parse(payload.toString());

        // Route presence messages (online/offline status updates)
        if (topic.includes('/presence/')) {
          const userId = parseInt(topic.split('/').pop() || '0');  // Extract user ID from topic
          this.config.onMessage({
            type: 'user_status',  // Normalize message type for application
            user_id: userId,
            ...data  // Include original presence data (status, timestamp, etc.)
          });
          return;  // Don't process further for presence messages
        }

        // Forward all other messages (chat, notifications) to application
        this.config.onMessage(data);

      } catch (error) {
        // Handle malformed messages gracefully
        this.config.onError?.(error);
      }
    });
    
    this.client.on('error', (error) => {
      this.config.onError?.(error);
      
      if (!this.isIntentionalDisconnect) {
        this.scheduleReconnect();
      }
      if (this.presenceInterval) { clearInterval(this.presenceInterval); this.presenceInterval = null; }
    });
    
    this.client.on('close', () => {
      this.config.onDisconnect?.();
      
      if (!this.isIntentionalDisconnect) {
        this.scheduleReconnect();
      }
      if (this.presenceInterval) { clearInterval(this.presenceInterval); this.presenceInterval = null; }
    });
    
    this.client.on('offline', () => {
      if (!this.isIntentionalDisconnect) {
        this.scheduleReconnect();
      }
    });
  }
  
  private scheduleReconnect(): void {
    if (!this.allowReconnect || this.reconnectTimeout) return;

    if (this.reconnectAttempts >= this.config.maxReconnectAttempts!) {
      this.config.onError?.(new Error('Max reconnection attempts reached'));
      return;
    }

    // Exponential backoff: delay increases as attempts increase (1s, 2s, 4s, 8s, 16s, 30s max)
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    this.reconnectAttempts++;

    // Schedule reconnection attempt after calculated delay
    this.reconnectTimeout = setTimeout(() => {
      this.reconnectTimeout = null;  // Clear timeout reference
      this.connect();  // Attempt reconnection
    }, delay);
  }
  
  private scheduleTokenRefresh(): void {
    if (!this.connectionInfo) return;

    // Schedule token refresh 5 minutes (300 seconds) before expiration
    // This ensures the token is refreshed before it becomes invalid
    const refreshIn = (this.connectionInfo.expires_in - 300) * 1000;  // Convert to milliseconds

    if (refreshIn > 0) {
      this.tokenRefreshTimeout = setTimeout(() => {
        this.refreshToken();
      }, refreshIn);
    }
  }
  
  private async refreshToken(): Promise<void> {
    try {
      const newConnectionInfo = await this.fetchBrokerToken();
      this.connectionInfo = newConnectionInfo;
      
      if (this.client && this.client.connected) {
        this.isIntentionalDisconnect = true;
        this.client.end(false, {}, () => {
          this.isIntentionalDisconnect = false;
          this.connect();
        });
      }
      
      this.scheduleTokenRefresh();

    } catch (error) {
      this.config.onError?.(error);
    }
  }
  
  public disconnect(): void {
    this.isIntentionalDisconnect = true;
    this.allowReconnect = false;
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    if (this.tokenRefreshTimeout) {
      clearTimeout(this.tokenRefreshTimeout);
      this.tokenRefreshTimeout = null;
    }
    if (this.presenceInterval) {
      clearInterval(this.presenceInterval);
      this.presenceInterval = null;
    }
    
    this.publishPresence('offline');
    
    if (this.client) {
      this.client.end(true);
      this.client = null;
    }
  }
  
  public publish(topic: string, payload: any, options: { qos?: 0 | 1 | 2, retain?: boolean } = {}): void {
    if (!this.client || !this.client.connected) {
      this.queueMessage({ topic, payload, options });
      return;
    }
    
    const message = typeof payload === 'string' ? payload : JSON.stringify(payload);
    
    this.client.publish(topic, message, {
      qos: options.qos || 1,
      retain: options.retain || false
    }, (error) => {
      if (error) {
        this.queueMessage({ topic, payload, options });
      }
    });
  }
  
  public publishCommand(command: any): void {
    if (!this.connectionInfo) {
      return;
    }

    // Publish to user's personal command topic for server-side processing
    const topic = `${this.connectionInfo.topic_prefix}/chat/cmd/${this.config.userId}`;

    // Add unique client message ID to prevent duplicate processing
    // Format: userId_timestamp_randomString (ensures uniqueness across sessions)
    const commandWithId = {
      ...command,
      client_msg_id: `${this.config.userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };

    // Use QoS 1 (at least once delivery) for reliable command delivery
    this.publish(topic, commandWithId, { qos: 1 });
  }
  
  public publishPresence(status: 'online' | 'offline'): void {
    if (!this.connectionInfo) return;
    
    const topic = `${this.connectionInfo.topic_prefix}/presence/${this.config.userId}`;
    const payload = {
      status,
      is_online: status === 'online',
      last_seen: new Date().toISOString(),
      timestamp: new Date().toISOString()
    };
    
    this.publish(topic, payload, { qos: 0, retain: false }); 
  }
  
  private queueMessage(message: any): void {
    // Store messages when client is disconnected, for retry after reconnection
    this.messageQueue.push(message);

    // Prevent unbounded queue growth - limit to 100 messages
    if (this.messageQueue.length > 100) {
      this.messageQueue.shift();  // Remove oldest message (FIFO)
    }
  }

  private processMessageQueue(): void {
    if (!this.client || !this.client.connected) return;

    // Send all queued messages after reconnection
    // This ensures no message loss during temporary disconnections
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.publish(message.topic, message.payload, message.options);
    }
  }
  
  public isConnected(): boolean {
    return this.client?.connected || false;
  }
  
  public getClientId(): string {
    return this.clientId;
  }
  
  public clearUserSession(): void {
    // Remove stored client ID from localStorage
    // This forces generation of a new client ID on next connection
    // Useful for troubleshooting connection issues or after logout
    localStorage.removeItem(`mqtt_client_id_${this.config.userId}`);
  }
}

export default MQTTManager;
