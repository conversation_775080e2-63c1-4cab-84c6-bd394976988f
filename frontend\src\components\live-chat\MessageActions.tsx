import React, { useState, useRef, useEffect } from 'react';
import { Message } from '@/contexts/Livechatcontext';

import { useNotification } from '@/contexts/NotificationContext';


interface MessageActionsProps {
  message: Message;
  isOwn: boolean;
  onEdit: (messageId: number, newContent: string) => void;
  onDeleteForEveryone: (messageId: number) => void;
  onDeleteForMe: (messageId: number) => void;
  onForward: (messageId: number) => void;
  iconClassName?: string;
}

export default function MessageActions({ message, isOwn, onEdit, onDeleteForEveryone, onDeleteForMe, onForward, iconClassName }: MessageActionsProps) {
  const [showMenu, setShowMenu] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);
  const menuRef = useRef<HTMLDivElement>(null);
  const editInputRef = useRef<HTMLInputElement>(null);
  const { addNotification } = useNotification();


  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (isEditing && editInputRef.current) {
      editInputRef.current.focus();
    }
  }, [isEditing]);

  const handleEdit = () => {
    setIsEditing(true);
    setShowMenu(false);
  };

  const handleSaveEdit = () => {
    if (editContent.trim() !== message.content) {
      onEdit(message.id, editContent.trim());
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditContent(message.content);
    }
  };

  if (isEditing) {
    return (
      <div className="flex items-center w-full">
        <input
          ref={editInputRef}
          type="text"
          value={editContent}
          onChange={(e) => setEditContent(e.target.value)}
          onKeyDown={handleKeyDown}
          className={`flex-1 px-2 py-1 rounded border ${
            isOwn ? 'bg-blue-500 text-white' : 'bg-gray-100'
          }`}
        />
        <button
          onClick={handleSaveEdit}
          className={`ml-2 text-xs ${isOwn ? 'text-blue-100' : 'text-gray-500'}`}
        >
          Save
        </button>
        <button
          onClick={() => {
            setIsEditing(false);
            setEditContent(message.content);
          }}
          className={`ml-2 text-xs ${isOwn ? 'text-blue-100' : 'text-gray-500'}`}
        >
          Cancel
        </button>
      </div>
    );
  }

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setShowMenu(!showMenu)}
        className={`p-2 mt-2 rounded-full hover:bg-opacity-20 ${
          isOwn ? 'hover:bg-blue-700' : 'hover:bg-gray-300'
        }`}
        style={{ color: '#374151' }}
      >
        <svg
          className={`w-4 h-4 ${iconClassName ? iconClassName : (isOwn ? 'text-blue-100' : 'text-gray-500')}`}
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" />
        </svg>
      </button>

      {showMenu && (
        <div
          className={`absolute z-10 w-32 py-2 mt-2 bg-white rounded-lg shadow-xl ${
            isOwn ? 'right-0' : 'left-0'
          }`}
        >
          {isOwn && (
            <button
              onClick={handleEdit}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            >
              Edit
            </button>
          )}
          {/* Delete options */}
          <button
            onClick={() => {
              onDeleteForMe(message.id);
              setShowMenu(false);
            }}
            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
          >
            Delete for me
          </button>
          {isOwn && (
            <button
              onClick={() => {
                onDeleteForEveryone(message.id);
                setShowMenu(false);
              }}
              className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100"
            >
              Delete for everyone
            </button>
          )}
          <button
            onClick={() => {

              navigator.clipboard.writeText(message.content || '').then(()=>{
                addNotification({
                  title: 'Copied',
                  message: 'Message copied to clipboard',
                  type: 'success'
                } as any);
              }).catch(()=>{});

              setShowMenu(false);
            }}
            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
          >
            Copy
          </button>
        </div>
      )}
    </div>
  );
} 