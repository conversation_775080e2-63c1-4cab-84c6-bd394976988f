"""
Performance Logging Utility

Provides detailed timing instrumentation to identify bottlenecks
in pipeline creation and processing.
"""

import time
import os
import logging
from contextlib import contextmanager
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class PerformanceTimer:
    """
    High-precision timer for measuring performance bottlenecks.
    """
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
        self.end_time = None
        self.sub_timers = {}
        self.total_time = 0
        
    def start(self):
        """Start the timer"""
        self.start_time = time.perf_counter()
        if os.getenv("ENABLE_PERF_LOGS", "0") == "1":
            logger.info(f"⏱️  TIMER START: {self.operation_name}")
        
    def stop(self):
        """Stop the timer and log results"""
        if self.start_time is None:
            logger.warning(f"Timer '{self.operation_name}' stopped without being started")
            return
            
        self.end_time = time.perf_counter()
        self.total_time = self.end_time - self.start_time
        
        if os.getenv("ENABLE_PERF_LOGS", "0") == "1":
            logger.info(f"⏱️  TIMER END: {self.operation_name} | Total: {self.total_time:.3f}s")
        
        # Log sub-timers if any
        if self.sub_timers and os.getenv("ENABLE_PERF_LOGS", "0") == "1":
            logger.info(f"⏱️  SUB-TIMERS for {self.operation_name}:")
            for name, duration in self.sub_timers.items():
                percentage = (duration / self.total_time) * 100 if self.total_time > 0 else 0
                logger.info(f"⏱️    - {name}: {duration:.3f}s ({percentage:.1f}%)")
        
        return self.total_time
    
    def add_sub_timer(self, name: str, duration: float):
        """Add a sub-timer measurement"""
        self.sub_timers[name] = duration
        if os.getenv("ENABLE_PERF_LOGS", "0") == "1":
            logger.debug(f"⏱️  SUB-TIMER: {self.operation_name}.{name} = {duration:.3f}s")

@contextmanager
def measure_time(operation_name: str, log_result: bool = True):
    """
    Context manager for measuring execution time.
    
    Usage:
        with measure_time("pipeline_creation") as timer:
            # do work
            timer.add_sub_timer("gemini_client", 1.2)
    """
    timer = PerformanceTimer(operation_name)
    timer.start()
    
    try:
        yield timer
    finally:
        if log_result:
            timer.stop()

class PipelinePerformanceProfiler:
    """
    Specialized profiler for pipeline operations.
    """
    
    @staticmethod
    def profile_ingestion_pipeline(func):
        """Decorator to profile ingestion pipeline creation"""
        def wrapper(*args, **kwargs):
            with measure_time("INGESTION_PIPELINE_CREATION") as timer:
                # Track component creation times
                start = time.perf_counter()
                result = func(*args, **kwargs)
                total = time.perf_counter() - start
                
                # Log detailed breakdown (gated)
                if os.getenv("ENABLE_PERF_LOGS", "0") == "1":
                    logger.info(f"🔍 INGESTION PROFILING COMPLETE:")
                    logger.info(f"🔍   Total Creation Time: {total:.3f}s")
                    logger.info(f"🔍   Components: Pipeline, Gemini, Qdrant, Embeddings")
                
                return result
        return wrapper
    
    @staticmethod
    def profile_retrieval_pipeline(func):
        """Decorator to profile retrieval pipeline creation"""
        def wrapper(*args, **kwargs):
            with measure_time("RETRIEVAL_PIPELINE_CREATION") as timer:
                start = time.perf_counter()
                result = func(*args, **kwargs)
                total = time.perf_counter() - start
                
                if os.getenv("ENABLE_PERF_LOGS", "0") == "1":
                    logger.info(f"🔍 RETRIEVAL PROFILING COMPLETE:")
                    logger.info(f"🔍   Total Creation Time: {total:.3f}s")
                    logger.info(f"🔍   Components: Enhanced Pipeline, Gemini, Qdrant, Embeddings, Graph")
                
                return result
        return wrapper

class ComponentTimer:
    """
    Timer for individual component initialization.
    """
    
    @staticmethod
    @contextmanager
    def time_component(component_name: str):
        """Time individual component creation"""
        start_time = time.perf_counter()
        if os.getenv("ENABLE_PERF_LOGS", "0") == "1":
            logger.info(f"🔧 COMPONENT START: {component_name}")
        
        try:
            yield
        finally:
            end_time = time.perf_counter()
            duration = end_time - start_time
            if os.getenv("ENABLE_PERF_LOGS", "0") == "1":
                logger.info(f"🔧 COMPONENT END: {component_name} | Time: {duration:.3f}s")

def log_memory_usage():
    """Log current memory usage"""
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_mb = process.memory_info().rss / 1024 / 1024
        cpu_percent = process.cpu_percent()
        
        if os.getenv("ENABLE_PERF_LOGS", "0") == "1":
            logger.info(f"💾 MEMORY: {memory_mb:.1f}MB | CPU: {cpu_percent:.1f}%")
        
    except ImportError:
        logger.debug("psutil not available for memory logging")

def log_request_metrics(request_id: str, operation: str, 
                       total_time: float, pipeline_time: float, 
                       processing_time: float):
    """
    Log comprehensive request metrics.
    """
    overhead_percentage = (pipeline_time / total_time) * 100 if total_time > 0 else 0
    
    if os.getenv("ENABLE_PERF_LOGS", "0") == "1":
        logger.info(f"📊 REQUEST METRICS [{request_id}]:")
        logger.info(f"📊   Operation: {operation}")
        logger.info(f"📊   Total Time: {total_time:.3f}s")
        logger.info(f"📊   Pipeline Creation: {pipeline_time:.3f}s ({overhead_percentage:.1f}%)")
        logger.info(f"📊   Actual Processing: {processing_time:.3f}s")
        logger.info(f"📊   Efficiency: {100-overhead_percentage:.1f}%")

# Pre-configured loggers for different operations
performance_logger = logging.getLogger("performance")
performance_logger.setLevel(logging.INFO)

# Add handler if not already added
if not performance_logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        '%(asctime)s - PERF - %(levelname)s - %(message)s'
    )
    handler.setFormatter(formatter)
    performance_logger.addHandler(handler)
