'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { <PERSON>Eye, FiEyeOff } from 'react-icons/fi';
import Link from 'next/link';
import { API_BASE_URL } from '@/lib/api';
import { MainLayout } from '@/components/layout';

// SharedWorker removed
const LoginWSWorker: null = null;

interface LoginForm {
  username: string;
  password: string;
}

interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  industry: string;
}

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  department: string;
  employee_id: string;
}

interface LoginResponse {
  access_token: string;
  token_type: string;
  user: User;
}

function LoginForm() {
  const [isLogin, setIsLogin] = useState(true);
  const [loginData, setLoginData] = useState<LoginForm>({
    username: '',
    password: ''
  });
  const [registerData, setRegisterData] = useState<RegisterForm>({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    industry: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [statusMessage, setStatusMessage] = useState('');
  const [success, setSuccess] = useState(false);
  // Tracks whether the password should be shown as plain text or obscured
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  
  useEffect(() => {
    // Check for status parameter in URL
    const status = searchParams.get('status');
    if (status === 'pending') {
      setStatusMessage('Your account registration is pending admin approval. You can sign in once it has been approved.');
      // auto-dismiss after 8 seconds
      setTimeout(() => setStatusMessage(''), 8000);
    }
  }, [searchParams]);

  const handleLoginInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLoginData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRegisterInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRegisterData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: loginData.username,
          password: loginData.password
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        
        // Handle specific error codes
        if (response.status === 403) {
          if (errorData.detail === "Account is pending approval") {
            throw new Error("Your account is pending approval. Please try again later.");
          } else if (errorData.detail === "Account access has been denied") {
            throw new Error("Your account access has been denied. Please contact an administrator.");
          }
        }
        
        throw new Error(errorData.detail || 'Login failed');
      }

      const data: LoginResponse = await response.json();
      
      // Store token and user data in localStorage
      localStorage.setItem('access_token', data.access_token);
      localStorage.setItem('user', JSON.stringify(data.user));

      // Redirect to chat page
      router.push('/chat');
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleRegisterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Validate passwords match
    if (registerData.password !== registerData.confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: registerData.username,
          email: registerData.email,
          password: registerData.password,
          industry: registerData.industry
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Registration failed');
      }

      // Registration successful
      setSuccess(true);
      
      // Clear form
      setRegisterData({
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        industry: ''
      });

      // Switch to login form and show success message
      setTimeout(() => {
        setIsLogin(true);
        setSuccess(false);
        setStatusMessage('Registration successful! Your account is pending approval.');
      }, 2000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout>
      <div className="min-h-full bg-gray-100 flex items-center justify-center px-4 py-24">
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-4xl font-bold text-black/70 mb-2">
              {isLogin ? 'Sign In' : 'Create Account'}
            </h1>
            <p className="text-black/70">
              {isLogin ? 'Welcome back to WorkplaceSLM' : 'Join WorkplaceSLM today'}
            </p>
          </div>

          {/* Form Toggle */}
          <div className="flex bg-white rounded-2xl shadow-sm">
            <button
              onClick={() => {
                setIsLogin(true);
                setError('');
                setSuccess(false);
              }}
              className={`flex-1 py-2 px-4 rounded-full text-sm font-medium transition-colors ${
                isLogin 
                  ? 'bg-blue-900 text-white' 
                  : 'text-gray-600 hover:text-blue-900'
              }`}
            >
              Sign In
            </button>
            <button
              onClick={() => {
                setIsLogin(false);
                setError('');
                setStatusMessage('');
              }}
              className={`flex-1 py-2 px-4 rounded-full text-sm font-medium transition-colors ${
                !isLogin 
                  ? 'bg-blue-900 text-white' 
                  : 'text-gray-600 hover:text-blue-900'
              }`}
            >
              Register
            </button>
          </div>

          {/* Forms */}
          <div className="backdrop-blur-md rounded-lg shadow-xl p-8 border border-white/20">
            {success && !isLogin && (
              <div className="bg-green-500/20 border border-green-500/50 text-green-700 px-4 py-3 rounded-md mb-4">
                Registration successful! Your account is pending approval. Switching to login...
              </div>
            )}
            
            {statusMessage && (
              <div className="bg-blue-500/20 border border-blue-500/50 text-blue-700 px-4 py-3 rounded-md mb-4">
                {statusMessage}
              </div>
            )}
            
            {error && (
              <div className="bg-red-500/20 border border-red-500/50 text-red-700 px-4 py-3 rounded-md mb-4">
                {error}
              </div>
            )}

            {isLogin ? (
              // Login Form
              <form onSubmit={handleLoginSubmit} className="space-y-6">
                <div>
                  <label htmlFor="username" className="block text-sm font-medium text-black/70 mb-2">
                    Username
                  </label>
                  <input
                    id="username"
                    name="username"
                    type="text"
                    required
                    value={loginData.username}
                    onChange={handleLoginInputChange}
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter your username"
                  />
                </div>

                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-black/70 mb-2">
                    Password
                  </label>
                  <div className="relative">
                    <input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      required
                      value={loginData.password}
                      onChange={handleLoginInputChange}
                      className="w-full px-3 py-2 pr-10 bg-white border border-gray-300 rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter your password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(prev => !prev)}
                      aria-label={showPassword ? 'Hide password' : 'Show password'}
                      className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-600 hover:text-gray-800 focus:outline-none"
                    >
                      {showPassword ? <FiEyeOff className="h-5 w-5" /> : <FiEye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-900 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Signing in...' : 'Sign in'}
                </button>
              </form>
            ) : (
              // Register Form
              <form onSubmit={handleRegisterSubmit} className="space-y-4">
                <div>
                  <label htmlFor="reg-username" className="block text-sm font-medium text-black/70 mb-2">
                    Username
                  </label>
                  <input
                    id="reg-username"
                    name="username"
                    type="text"
                    required
                    value={registerData.username}
                    onChange={handleRegisterInputChange}
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Choose a username"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-black/70 mb-2">
                    Email
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    required
                    value={registerData.email}
                    onChange={handleRegisterInputChange}
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter your email"
                  />
                </div>

                <div>
                  <label htmlFor="industry" className="block text-sm font-medium text-black/70 mb-2">
                    Industry
                  </label>
                  <input
                    id="industry"
                    name="industry"
                    type="text"
                    value={registerData.industry}
                    onChange={handleRegisterInputChange}
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Your industry (optional)"
                  />
                </div>

                <div>
                  <label htmlFor="reg-password" className="block text-sm font-medium text-black/70 mb-2">
                    Password
                  </label>
                  <div className="relative">
                    <input
                      id="reg-password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      required
                      value={registerData.password}
                      onChange={handleRegisterInputChange}
                      className="w-full px-3 py-2 pr-10 bg-white border border-gray-300 rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Create a password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(prev => !prev)}
                      aria-label={showPassword ? 'Hide password' : 'Show password'}
                      className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-600 hover:text-gray-800 focus:outline-none"
                    >
                      {showPassword ? <FiEyeOff className="h-5 w-5" /> : <FiEye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>

                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-black/70 mb-2">
                    Confirm Password
                  </label>
                  <div className="relative">
                    <input
                      id="confirmPassword"
                      name="confirmPassword"
                      type={showConfirmPassword ? 'text' : 'password'}
                      required
                      value={registerData.confirmPassword}
                      onChange={handleRegisterInputChange}
                      className="w-full px-3 py-2 pr-10 bg-white border border-gray-300 rounded-md text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Confirm your password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(prev => !prev)}
                      aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                      className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-600 hover:text-gray-800 focus:outline-none"
                    >
                      {showConfirmPassword ? <FiEyeOff className="h-5 w-5" /> : <FiEye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-900 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Registering...' : 'Create Account'}
                </button>
              </form>
            )}

            {/* Links
            <div className="mt-6 text-center">
              <Link 
                href="/about" 
                className="text-blue-900 hover:text-blue-700 text-sm font-medium"
              >
                Learn more about WorkplaceSLM
              </Link>
            </div> */}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <MainLayout>
        <div className="min-h-full bg-gray-100 flex items-center justify-center px-4 py-24">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-900 mx-auto mb-4"></div>
            <p className="text-black/70">Loading...</p>
          </div>
        </div>
      </MainLayout>
    }>
      <LoginForm />
    </Suspense>
  );
} 