from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.orm import relationship
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime
from ..database import Base

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), unique=True, nullable=False, index=True)
    email = Column(String(120), unique=True, nullable=False, index=True)
    password_hash = Column(String(200), nullable=False)
    industry = Column(String(50), nullable=True, index=True)
    role = Column(String(50), default='user', nullable=False)  # 'admin', 'user'
    status = Column(String(20), default='approved', nullable=False, index=True)  # 'pending', 'approved', 'rejected'
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    documents = relationship("Document", back_populates="uploader")
    feedback = relationship("QueryFeedback", foreign_keys="QueryFeedback.user_id", back_populates="user")
    spaces = relationship("UserSpace", back_populates="owner")
    created_tickets = relationship("Ticket", foreign_keys="Ticket.created_by", back_populates="creator")
    assigned_tickets = relationship("Ticket", foreign_keys="Ticket.assigned_to", back_populates="assignee")
    
    def set_password(self, password: str) -> None:
        """Hash and set password"""
        self.password_hash = pwd_context.hash(password)
        
    def verify_password(self, password: str) -> bool:
        """Verify password against hash"""
        return pwd_context.verify(password, self.password_hash)
        
    def is_admin(self) -> bool:
        """Check if user has admin role"""
        return self.role == 'admin'
        
    def is_approved(self) -> bool:
        """Check if user account is approved"""
        return self.status == 'approved'
        
    def is_pending(self) -> bool:
        """Check if user account is pending approval"""
        return self.status == 'pending'
        
    def is_rejected(self) -> bool:
        """Check if user account is rejected"""
        return self.status == 'rejected'
        
    def can_access_space(self, space_id: int) -> bool:
        """Check if user can access a specific space (for management/editing purposes)"""
        # All users (including admins) can only access their own spaces
        return any(space.id == space_id for space in self.spaces)
    
    def to_dict(self, include_sensitive: bool = False) -> dict:
        """Convert user to dictionary"""
        data = {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "industry": self.industry,
            "role": self.role,
            "status": self.status,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
        
        if include_sensitive:
            data["password_hash"] = self.password_hash
            
        return data
    
    @classmethod
    def get_default_users_data(cls):
        """
        Get default users data for seeding
        """
        return [
            # username, email, industry, role
            ('admin', '<EMAIL>', 'Legal', 'admin'),
            ('demo_user1', '<EMAIL>', 'demo1', 'user'),
            ('demo_user2', '<EMAIL>', 'demo2', 'user'),
            ('demo_user3', '<EMAIL>', 'demo3', 'user'),
            ('demo_user4', '<EMAIL>', 'demo4', 'user'),
            ('demo_user5', '<EMAIL>', 'demo5', 'user'),
            ('demo_user6', '<EMAIL>', 'demo6', 'user'),
            ('demo_user7', '<EMAIL>', 'demo7', 'user'),
            ('demo_user8', '<EMAIL>', 'demo8', 'user'),
            ('demo_user9', '<EMAIL>', 'demo9', 'user'),
            ('demo_user10', '<EMAIL>', 'demo10', 'user'),
            ('demo_user11', '<EMAIL>', 'demo11', 'user'),
            ('demo_user12', '<EMAIL>', 'demo12', 'user'),
            ('demo_user13', '<EMAIL>', 'demo13', 'user'),
            ('demo_user14', '<EMAIL>', 'demo14', 'user'),
            ('demo_user15', '<EMAIL>', 'demo15', 'user'),
            ('demo_user16', '<EMAIL>', 'demo16', 'user'),
            ('demo_user17', '<EMAIL>', 'demo17', 'user'),
            ('demo_user18', '<EMAIL>', 'demo18', 'user'),
            ('demo_user19', '<EMAIL>', 'demo19', 'user'),
            ('demo_user20', '<EMAIL>', 'demo20', 'user'),
            ('Mike.Callaghan', '<EMAIL>', 'Finance', 'user'),
            ('Brian.Bolon', '<EMAIL>', 'Finance', 'user'),
            ('ankit.goyanka', '<EMAIL>', 'Finance', 'admin'),
            ('anil.poonia', '<EMAIL>', 'Finance', 'admin'),
        ]
    
    def __repr__(self):
        return f"<User {self.username}>" 

# Ensure Ticket model is registered so string-based relationships resolve
from .ticket import Ticket  # noqa: E402,F401