'use client';

import React from 'react';
import { FiX, FiTrash2 } from 'react-icons/fi';

interface UserMetrics {
  ai_query_count: number;
  knowledge_docs_uploaded: number;
  knowledge_docs_indexed: number;
  feedback_submitted: number;
  feedback_helpful_count: number;
  feedback_avg_rating: number;
  ai_responses_shared: number;
  is_online: boolean;
  last_seen: string | null;
}

interface UserMetricsModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: {
    user_id: number;
    username: string;
    email: string;
    industry: string | null;
    role: string;
    created_at: string | null;
  };
  metrics: UserMetrics;
  canDelete?: boolean;
  onDelete?: () => void;
}

export default function UserMetricsModal({ isOpen, onClose, user, metrics, canDelete = false, onDelete }: UserMetricsModalProps) {
  const formatLastSeen = (lastSeenStr: string | null) => {
    if (!lastSeenStr) return 'Never';

    const lastSeen = new Date(lastSeenStr);
    const now = new Date();
    const diffInMs = now.getTime() - lastSeen.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInDays < 7) return `${diffInDays}d ago`;

    return lastSeen.toLocaleDateString();
  };

  const getOnlineStatusColor = () => {
    return metrics.is_online ? 'bg-green-400' : 'bg-gray-400';
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 8) return 'text-green-600';
    if (rating >= 6) return 'text-yellow-600';
    if (rating >= 4) return 'text-orange-600';
    return 'text-red-600';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Action buttons */}
            <div className="absolute top-0 right-0 pt-4 pr-4 flex items-center space-x-2">
              {canDelete && (
                <button
                  type="button"
                  onClick={onDelete}
                  className="bg-white rounded-md text-red-400 hover:text-red-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 p-1"
                  title="Delete user"
                >
                  <FiTrash2 className="h-5 w-5" />
                </button>
              )}
              <button
                type="button"
                onClick={onClose}
                className="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiX className="h-6 w-6" />
              </button>
            </div>

            {/* User Header */}
            <div className="flex items-center space-x-4 mb-6">
              <div className="relative">
                <div className="flex-shrink-0 h-16 w-16 bg-gradient-to-br from-blue-500 to-blue-900 rounded-full flex items-center justify-center shadow-md">
                  <span className="text-white font-bold text-2xl">
                    {user.username.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className={`absolute -bottom-1 -right-1 h-4 w-4 rounded-full border-2 border-white ${getOnlineStatusColor()}`}></div>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900">{user.username}</h3>
                <p className="text-sm text-gray-500">{user.email}</p>
                <div className="flex items-center gap-2 mt-1">
                  <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
                    {user.role === 'admin' ? 'Admin' : 'User'}
                  </span>
                  {user.industry && (
                    <span className="text-xs text-gray-500">• {user.industry}</span>
                  )}
                </div>
              </div>
            </div>

            {/* Account Info */}
            <div className="mb-6 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between text-sm mb-2">
                <span className="text-gray-600">Status:</span>
                <span className={`font-medium ${metrics.is_online ? 'text-green-600' : 'text-gray-500'}`}>
                  {metrics.is_online ? '● Online' : '● Offline'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm mb-2">
                <span className="text-gray-600">Last Seen:</span>
                <span className="font-medium text-gray-900">{formatLastSeen(metrics.last_seen)}</span>
              </div>
              {user.created_at && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Member Since:</span>
                  <span className="font-medium text-gray-900">
                    {new Date(user.created_at).toLocaleDateString()}
                  </span>
                </div>
              )}
            </div>

            {/* Detailed Metrics */}
            <div className="space-y-4">
              <h4 className="text-sm font-semibold text-gray-900 border-b pb-2">Detailed Analytics</h4>
              
              {/* Documents Breakdown */}
              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Knowledge Documents</span>
                  <span className="text-lg font-bold text-blue-700">{metrics.knowledge_docs_uploaded}</span>
                </div>
                <div className="flex items-center justify-between text-xs text-gray-600">
                  <span>Indexed:</span>
                  <span className="font-semibold">{metrics.knowledge_docs_indexed} / {metrics.knowledge_docs_uploaded}</span>
                </div>
                {metrics.knowledge_docs_uploaded > 0 && (
                  <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-900 h-2 rounded-full transition-all" 
                      style={{ width: `${Math.round((metrics.knowledge_docs_indexed / metrics.knowledge_docs_uploaded) * 100)}%` }}
                    ></div>
                  </div>
                )}
              </div>

              {/* Feedback Details */}
              {metrics.feedback_submitted > 0 && (
                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Feedback Activity</span>
                    <span className="text-lg font-bold text-green-700">{metrics.feedback_submitted}</span>
                  </div>
                  <div className="grid grid-cols-2 gap-3 mt-2">
                    <div>
                      <p className="text-xs text-gray-600">Helpful</p>
                      <p className="text-sm font-semibold text-green-700">{metrics.feedback_helpful_count}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-600">Helpful Rate</p>
                      <p className="text-sm font-semibold text-green-700">
                        {Math.round((metrics.feedback_helpful_count / metrics.feedback_submitted) * 100)}%
                      </p>
                    </div>
                  </div>
                  {metrics.feedback_avg_rating > 0 && (
                    <div className="mt-3 pt-3 border-t border-green-200">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-600">Average Rating:</span>
                        <span className={`text-sm font-bold ${getRatingColor(metrics.feedback_avg_rating)}`}>
                          {metrics.feedback_avg_rating.toFixed(1)} / 10
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              onClick={onClose}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-900 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
