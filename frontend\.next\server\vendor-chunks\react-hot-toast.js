"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hot-toast";
exports.ids = ["vendor-chunks/react-hot-toast"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hot-toast/dist/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/react-hot-toast/dist/index.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckmarkIcon: () => (/* binding */ L),\n/* harmony export */   ErrorIcon: () => (/* binding */ C),\n/* harmony export */   LoaderIcon: () => (/* binding */ F),\n/* harmony export */   ToastBar: () => (/* binding */ N),\n/* harmony export */   ToastIcon: () => (/* binding */ $),\n/* harmony export */   Toaster: () => (/* binding */ Fe),\n/* harmony export */   \"default\": () => (/* binding */ zt),\n/* harmony export */   resolveValue: () => (/* binding */ h),\n/* harmony export */   toast: () => (/* binding */ n),\n/* harmony export */   useToaster: () => (/* binding */ w),\n/* harmony export */   useToasterStore: () => (/* binding */ V)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var goober__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! goober */ \"(ssr)/./node_modules/goober/dist/goober.modern.js\");\n/* __next_internal_client_entry_do_not_use__ CheckmarkIcon,ErrorIcon,LoaderIcon,ToastBar,ToastIcon,Toaster,default,resolveValue,toast,useToaster,useToasterStore auto */ var Z = (e)=>typeof e == \"function\", h = (e, t)=>Z(e) ? e(t) : e;\nvar W = (()=>{\n    let e = 0;\n    return ()=>(++e).toString();\n})(), E = (()=>{\n    let e;\n    return ()=>{\n        if (e === void 0 && \"undefined\" < \"u\") {}\n        return e;\n    };\n})();\n\nvar re = 20, k = \"default\";\nvar H = (e, t)=>{\n    let { toastLimit: o } = e.settings;\n    switch(t.type){\n        case 0:\n            return {\n                ...e,\n                toasts: [\n                    t.toast,\n                    ...e.toasts\n                ].slice(0, o)\n            };\n        case 1:\n            return {\n                ...e,\n                toasts: e.toasts.map((r)=>r.id === t.toast.id ? {\n                        ...r,\n                        ...t.toast\n                    } : r)\n            };\n        case 2:\n            let { toast: s } = t;\n            return H(e, {\n                type: e.toasts.find((r)=>r.id === s.id) ? 1 : 0,\n                toast: s\n            });\n        case 3:\n            let { toastId: a } = t;\n            return {\n                ...e,\n                toasts: e.toasts.map((r)=>r.id === a || a === void 0 ? {\n                        ...r,\n                        dismissed: !0,\n                        visible: !1\n                    } : r)\n            };\n        case 4:\n            return t.toastId === void 0 ? {\n                ...e,\n                toasts: []\n            } : {\n                ...e,\n                toasts: e.toasts.filter((r)=>r.id !== t.toastId)\n            };\n        case 5:\n            return {\n                ...e,\n                pausedAt: t.time\n            };\n        case 6:\n            let i = t.time - (e.pausedAt || 0);\n            return {\n                ...e,\n                pausedAt: void 0,\n                toasts: e.toasts.map((r)=>({\n                        ...r,\n                        pauseDuration: r.pauseDuration + i\n                    }))\n            };\n    }\n}, v = [], j = {\n    toasts: [],\n    pausedAt: void 0,\n    settings: {\n        toastLimit: re\n    }\n}, f = {}, Y = (e, t = k)=>{\n    f[t] = H(f[t] || j, e), v.forEach(([o, s])=>{\n        o === t && s(f[t]);\n    });\n}, _ = (e)=>Object.keys(f).forEach((t)=>Y(e, t)), Q = (e)=>Object.keys(f).find((t)=>f[t].toasts.some((o)=>o.id === e)), S = (e = k)=>(t)=>{\n        Y(t, e);\n    }, se = {\n    blank: 4e3,\n    error: 4e3,\n    success: 2e3,\n    loading: 1 / 0,\n    custom: 4e3\n}, V = (e = {}, t = k)=>{\n    let [o, s] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(f[t] || j), a = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(f[t]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>(a.current !== f[t] && s(f[t]), v.push([\n            t,\n            s\n        ]), ()=>{\n            let r = v.findIndex(([l])=>l === t);\n            r > -1 && v.splice(r, 1);\n        }), [\n        t\n    ]);\n    let i = o.toasts.map((r)=>{\n        var l, g, T;\n        return {\n            ...e,\n            ...e[r.type],\n            ...r,\n            removeDelay: r.removeDelay || ((l = e[r.type]) == null ? void 0 : l.removeDelay) || (e == null ? void 0 : e.removeDelay),\n            duration: r.duration || ((g = e[r.type]) == null ? void 0 : g.duration) || (e == null ? void 0 : e.duration) || se[r.type],\n            style: {\n                ...e.style,\n                ...(T = e[r.type]) == null ? void 0 : T.style,\n                ...r.style\n            }\n        };\n    });\n    return {\n        ...o,\n        toasts: i\n    };\n};\nvar ie = (e, t = \"blank\", o)=>({\n        createdAt: Date.now(),\n        visible: !0,\n        dismissed: !1,\n        type: t,\n        ariaProps: {\n            role: \"status\",\n            \"aria-live\": \"polite\"\n        },\n        message: e,\n        pauseDuration: 0,\n        ...o,\n        id: (o == null ? void 0 : o.id) || W()\n    }), P = (e)=>(t, o)=>{\n        let s = ie(t, e, o);\n        return S(s.toasterId || Q(s.id))({\n            type: 2,\n            toast: s\n        }), s.id;\n    }, n = (e, t)=>P(\"blank\")(e, t);\nn.error = P(\"error\");\nn.success = P(\"success\");\nn.loading = P(\"loading\");\nn.custom = P(\"custom\");\nn.dismiss = (e, t)=>{\n    let o = {\n        type: 3,\n        toastId: e\n    };\n    t ? S(t)(o) : _(o);\n};\nn.dismissAll = (e)=>n.dismiss(void 0, e);\nn.remove = (e, t)=>{\n    let o = {\n        type: 4,\n        toastId: e\n    };\n    t ? S(t)(o) : _(o);\n};\nn.removeAll = (e)=>n.remove(void 0, e);\nn.promise = (e, t, o)=>{\n    let s = n.loading(t.loading, {\n        ...o,\n        ...o == null ? void 0 : o.loading\n    });\n    return typeof e == \"function\" && (e = e()), e.then((a)=>{\n        let i = t.success ? h(t.success, a) : void 0;\n        return i ? n.success(i, {\n            id: s,\n            ...o,\n            ...o == null ? void 0 : o.success\n        }) : n.dismiss(s), a;\n    }).catch((a)=>{\n        let i = t.error ? h(t.error, a) : void 0;\n        i ? n.error(i, {\n            id: s,\n            ...o,\n            ...o == null ? void 0 : o.error\n        }) : n.dismiss(s);\n    }), e;\n};\n\nvar ce = 1e3, w = (e, t = \"default\")=>{\n    let { toasts: o, pausedAt: s } = V(e, t), a = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Map).current, i = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((c, m = ce)=>{\n        if (a.has(c)) return;\n        let p = setTimeout(()=>{\n            a.delete(c), r({\n                type: 4,\n                toastId: c\n            });\n        }, m);\n        a.set(c, p);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (s) return;\n        let c = Date.now(), m = o.map((p)=>{\n            if (p.duration === 1 / 0) return;\n            let R = (p.duration || 0) + p.pauseDuration - (c - p.createdAt);\n            if (R < 0) {\n                p.visible && n.dismiss(p.id);\n                return;\n            }\n            return setTimeout(()=>n.dismiss(p.id, t), R);\n        });\n        return ()=>{\n            m.forEach((p)=>p && clearTimeout(p));\n        };\n    }, [\n        o,\n        s,\n        t\n    ]);\n    let r = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(S(t), [\n        t\n    ]), l = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        r({\n            type: 5,\n            time: Date.now()\n        });\n    }, [\n        r\n    ]), g = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((c, m)=>{\n        r({\n            type: 1,\n            toast: {\n                id: c,\n                height: m\n            }\n        });\n    }, [\n        r\n    ]), T = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        s && r({\n            type: 6,\n            time: Date.now()\n        });\n    }, [\n        s,\n        r\n    ]), d = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((c, m)=>{\n        let { reverseOrder: p = !1, gutter: R = 8, defaultPosition: z } = m || {}, O = o.filter((u)=>(u.position || z) === (c.position || z) && u.height), K = O.findIndex((u)=>u.id === c.id), B = O.filter((u, I)=>I < K && u.visible).length;\n        return O.filter((u)=>u.visible).slice(...p ? [\n            B + 1\n        ] : [\n            0,\n            B\n        ]).reduce((u, I)=>u + (I.height || 0) + R, 0);\n    }, [\n        o\n    ]);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        o.forEach((c)=>{\n            if (c.dismissed) i(c.id, c.removeDelay);\n            else {\n                let m = a.get(c.id);\n                m && (clearTimeout(m), a.delete(c.id));\n            }\n        });\n    }, [\n        o,\n        i\n    ]), {\n        toasts: o,\n        handlers: {\n            updateHeight: g,\n            startPause: l,\n            endPause: T,\n            calculateOffset: d\n        }\n    };\n};\n\n\n\n\n\nvar de = (0,goober__WEBPACK_IMPORTED_MODULE_1__.keyframes)`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}`, me = (0,goober__WEBPACK_IMPORTED_MODULE_1__.keyframes)`\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`, le = (0,goober__WEBPACK_IMPORTED_MODULE_1__.keyframes)`\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}`, C = (0,goober__WEBPACK_IMPORTED_MODULE_1__.styled)(\"div\")`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(e)=>e.primary || \"#ff4b4b\"};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${de} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: ${me} 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ${(e)=>e.secondary || \"#fff\"};\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: ${le} 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n`;\n\nvar Te = (0,goober__WEBPACK_IMPORTED_MODULE_1__.keyframes)`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`, F = (0,goober__WEBPACK_IMPORTED_MODULE_1__.styled)(\"div\")`\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ${(e)=>e.secondary || \"#e0e0e0\"};\n  border-right-color: ${(e)=>e.primary || \"#616161\"};\n  animation: ${Te} 1s linear infinite;\n`;\n\nvar ge = (0,goober__WEBPACK_IMPORTED_MODULE_1__.keyframes)`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}`, he = (0,goober__WEBPACK_IMPORTED_MODULE_1__.keyframes)`\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}`, L = (0,goober__WEBPACK_IMPORTED_MODULE_1__.styled)(\"div\")`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(e)=>e.primary || \"#61d345\"};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${ge} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: ${he} 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ${(e)=>e.secondary || \"#fff\"};\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n`;\nvar be = (0,goober__WEBPACK_IMPORTED_MODULE_1__.styled)(\"div\")`\n  position: absolute;\n`, Se = (0,goober__WEBPACK_IMPORTED_MODULE_1__.styled)(\"div\")`\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n`, Ae = (0,goober__WEBPACK_IMPORTED_MODULE_1__.keyframes)`\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`, Pe = (0,goober__WEBPACK_IMPORTED_MODULE_1__.styled)(\"div\")`\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: ${Ae} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n`, $ = ({ toast: e })=>{\n    let { icon: t, type: o, iconTheme: s } = e;\n    return t !== void 0 ? typeof t == \"string\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Pe, null, t) : t : o === \"blank\" ? null : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Se, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(F, {\n        ...s\n    }), o !== \"loading\" && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(be, null, o === \"error\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(C, {\n        ...s\n    }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(L, {\n        ...s\n    })));\n};\nvar Re = (e)=>`\n0% {transform: translate3d(0,${e * -200}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`, Ee = (e)=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${e * -150}%,-1px) scale(.6); opacity:0;}\n`, ve = \"0%{opacity:0;} 100%{opacity:1;}\", De = \"0%{opacity:1;} 100%{opacity:0;}\", Oe = (0,goober__WEBPACK_IMPORTED_MODULE_1__.styled)(\"div\")`\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n`, Ie = (0,goober__WEBPACK_IMPORTED_MODULE_1__.styled)(\"div\")`\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n`, ke = (e, t)=>{\n    let s = e.includes(\"top\") ? 1 : -1, [a, i] = E() ? [\n        ve,\n        De\n    ] : [\n        Re(s),\n        Ee(s)\n    ];\n    return {\n        animation: t ? `${(0,goober__WEBPACK_IMPORTED_MODULE_1__.keyframes)(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards` : `${(0,goober__WEBPACK_IMPORTED_MODULE_1__.keyframes)(i)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`\n    };\n}, N = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.memo(({ toast: e, position: t, style: o, children: s })=>{\n    let a = e.height ? ke(e.position || t || \"top-center\", e.visible) : {\n        opacity: 0\n    }, i = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement($, {\n        toast: e\n    }), r = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Ie, {\n        ...e.ariaProps\n    }, h(e.message, e));\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Oe, {\n        className: e.className,\n        style: {\n            ...a,\n            ...o,\n            ...e.style\n        }\n    }, typeof s == \"function\" ? s({\n        icon: i,\n        message: r\n    }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, i, r));\n});\n\n\n(0,goober__WEBPACK_IMPORTED_MODULE_1__.setup)(react__WEBPACK_IMPORTED_MODULE_0__.createElement);\nvar we = ({ id: e, className: t, style: o, onHeightUpdate: s, children: a })=>{\n    let i = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((r)=>{\n        if (r) {\n            let l = ()=>{\n                let g = r.getBoundingClientRect().height;\n                s(e, g);\n            };\n            l(), new MutationObserver(l).observe(r, {\n                subtree: !0,\n                childList: !0,\n                characterData: !0\n            });\n        }\n    }, [\n        e,\n        s\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: i,\n        className: t,\n        style: o\n    }, a);\n}, Me = (e, t)=>{\n    let o = e.includes(\"top\"), s = o ? {\n        top: 0\n    } : {\n        bottom: 0\n    }, a = e.includes(\"center\") ? {\n        justifyContent: \"center\"\n    } : e.includes(\"right\") ? {\n        justifyContent: \"flex-end\"\n    } : {};\n    return {\n        left: 0,\n        right: 0,\n        display: \"flex\",\n        position: \"absolute\",\n        transition: E() ? void 0 : \"all 230ms cubic-bezier(.21,1.02,.73,1)\",\n        transform: `translateY(${t * (o ? 1 : -1)}px)`,\n        ...s,\n        ...a\n    };\n}, Ce = (0,goober__WEBPACK_IMPORTED_MODULE_1__.css)`\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n`, D = 16, Fe = ({ reverseOrder: e, position: t = \"top-center\", toastOptions: o, gutter: s, children: a, toasterId: i, containerStyle: r, containerClassName: l })=>{\n    let { toasts: g, handlers: T } = w(o, i);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-rht-toaster\": i || \"\",\n        style: {\n            position: \"fixed\",\n            zIndex: 9999,\n            top: D,\n            left: D,\n            right: D,\n            bottom: D,\n            pointerEvents: \"none\",\n            ...r\n        },\n        className: l,\n        onMouseEnter: T.startPause,\n        onMouseLeave: T.endPause\n    }, g.map((d)=>{\n        let c = d.position || t, m = T.calculateOffset(d, {\n            reverseOrder: e,\n            gutter: s,\n            defaultPosition: t\n        }), p = Me(c, m);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(we, {\n            id: d.id,\n            key: d.id,\n            onHeightUpdate: T.updateHeight,\n            className: d.visible ? Ce : \"\",\n            style: p\n        }, d.type === \"custom\" ? h(d.message, d) : a ? a(d) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(N, {\n            toast: d,\n            position: c\n        }));\n    }));\n};\nvar zt = n;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hot-toast/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/react-hot-toast/dist/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/react-hot-toast/dist/index.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CheckmarkIcon: () => (/* binding */ e0),
/* harmony export */   ErrorIcon: () => (/* binding */ e1),
/* harmony export */   LoaderIcon: () => (/* binding */ e2),
/* harmony export */   ToastBar: () => (/* binding */ e3),
/* harmony export */   ToastIcon: () => (/* binding */ e4),
/* harmony export */   Toaster: () => (/* binding */ e5),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   resolveValue: () => (/* binding */ e6),
/* harmony export */   toast: () => (/* binding */ e7),
/* harmony export */   useToaster: () => (/* binding */ e8),
/* harmony export */   useToasterStore: () => (/* binding */ e9)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Predusk\Desktop\WORKPLACE_SLM\frontend\node_modules\react-hot-toast\dist\index.mjs#CheckmarkIcon`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Predusk\Desktop\WORKPLACE_SLM\frontend\node_modules\react-hot-toast\dist\index.mjs#ErrorIcon`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Predusk\Desktop\WORKPLACE_SLM\frontend\node_modules\react-hot-toast\dist\index.mjs#LoaderIcon`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Predusk\Desktop\WORKPLACE_SLM\frontend\node_modules\react-hot-toast\dist\index.mjs#ToastBar`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Predusk\Desktop\WORKPLACE_SLM\frontend\node_modules\react-hot-toast\dist\index.mjs#ToastIcon`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Predusk\Desktop\WORKPLACE_SLM\frontend\node_modules\react-hot-toast\dist\index.mjs#Toaster`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Predusk\Desktop\WORKPLACE_SLM\frontend\node_modules\react-hot-toast\dist\index.mjs#default`));

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Predusk\Desktop\WORKPLACE_SLM\frontend\node_modules\react-hot-toast\dist\index.mjs#resolveValue`);

const e7 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Predusk\Desktop\WORKPLACE_SLM\frontend\node_modules\react-hot-toast\dist\index.mjs#toast`);

const e8 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Predusk\Desktop\WORKPLACE_SLM\frontend\node_modules\react-hot-toast\dist\index.mjs#useToaster`);

const e9 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Predusk\Desktop\WORKPLACE_SLM\frontend\node_modules\react-hot-toast\dist\index.mjs#useToasterStore`);


/***/ })

};
;