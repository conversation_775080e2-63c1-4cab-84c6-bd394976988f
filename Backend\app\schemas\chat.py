from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class MessageCreate(BaseModel):
    content: str = Field(..., min_length=1, max_length=5000)
    message_type: str = Field(default="text", pattern="^(text|image|file|teamviewer_request|system)$")

class MessageResponse(BaseModel):
    id: int
    conversation_id: int
    sender_id: int
    sender_username: Optional[str]
    content: str
    message_type: str
    created_at: datetime
    is_read: bool
    is_edited: bool
    edited_at: Optional[datetime]

    class Config:
        from_attributes = True

class ConversationCreate(BaseModel):
    participant_id: int = Field(..., description="ID of the user to start conversation with")

class ConversationResponse(BaseModel):
    id: int
    participant1_id: int
    participant2_id: int
    participant1_username: Optional[str]
    participant2_username: Optional[str]
    created_at: datetime
    updated_at: datetime
    is_active: bool
    last_message: Optional[MessageResponse]

    class Config:
        from_attributes = True

class ConversationWithMessages(ConversationResponse):
    messages: List[MessageResponse] = []

class UserStatusResponse(BaseModel):
    user_id: int
    username: Optional[str]
    is_online: bool
    last_seen: datetime

    class Config:
        from_attributes = True

class ChatNotification(BaseModel):
    type: str  # 'new_message', 'user_online', 'user_offline', 'typing'
    conversation_id: Optional[int]
    message: Optional[MessageResponse]
    user_id: Optional[int]
    username: Optional[str]
    content: Optional[str]

class TypingIndicator(BaseModel):
    conversation_id: int
    is_typing: bool

class WebSocketMessage(BaseModel):
    type: str  # 'message', 'typing', 'read_receipt', 'join_conversation', 'group_message'
    conversation_id: Optional[int] = None
    group_id: Optional[int] = None
    content: Optional[str] = None
    message_id: Optional[int] = None
    is_typing: Optional[bool] = None
    # Allow clients to explicitly specify the type of message (text, image, file)
    message_type: Optional[str] = Field(default="text", pattern="^(text|image|file|teamviewer_request|system)$")

class OnlineUsersResponse(BaseModel):
    users: List[UserStatusResponse]
    total_count: int

class UnreadCountResponse(BaseModel):
    total_unread: int
    conversations: List[dict]  # conversation_id and unread_count pairs 

class MediaResponse(BaseModel):
    id: int
    message_id: int
    conversation_id: int
    uploader_id: Optional[int]
    media_type: str
    original_url: str
    thumb_url: Optional[str]
    size_bytes: Optional[int]
    width: Optional[int]
    height: Optional[int]
    created_at: datetime

    class Config:
        from_attributes = True 

class SharedLinkResponse(BaseModel):
    id: int
    message_id: int
    conversation_id: int
    url: str
    title: Optional[str]
    preview_img: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True 