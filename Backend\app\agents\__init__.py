from .general_agent import GeneralAgent
from .followup_agent import FollowUpSuggestionAgent
from .image_agent import image_vision_agent

# Single universal agent instance only
general_agent = GeneralAgent(
    use_web_search=False,
    use_document_search=False,
    use_deep_search=False,
)

# IMPORTANT: Reuse the SAME agent/model via composition to avoid creating a new agent/model
followup_agent = FollowUpSuggestionAgent(existing_base_agent=general_agent)

# Only expose the general agent in production
agent_instances = {
    "general": general_agent,
    "followup": followup_agent,
    "image": image_vision_agent,
}

__all__ = [
    "general_agent",
    "followup_agent",
    "agent_instances",
    "image_vision_agent",
]