[{ekka,
     [{cluster_discovery,{singleton,[]}},
      {proto_dist,inet_tcp},
      {cluster_name,emqxcl}]},
 {emqx_durable_session,[{poll_batch_size,100}]},
 {system_monitor,
     [{db_name,"postgres"},
      {db_password,<<"system_monitor_password">>},
      {db_username,"system_monitor"},
      {db_port,5432},
      {db_hostname,[]},
      {top_max_procs,1000000},
      {top_sample_interval,2000},
      {top_num_items,10}]},
 {emqx_durable_storage,
     [{egress_flush_interval,100},
      {egress_batch_bytes,infinity},
      {egress_batch_size,1000}]},
 {gen_rpc,
     [{ipv6_only,false},
      {insecure_auth_fallback_allowed,true},
      {socket_buffer,1048576},
      {socket_recbuf,1048576},
      {socket_sndbuf,1048576},
      {socket_keepalive_count,9},
      {socket_keepalive_interval,75},
      {socket_keepalive_idle,900},
      {call_receive_timeout,15000},
      {authentication_timeout,5000},
      {send_timeout,5000},
      {connect_timeout,5000},
      {port_discovery,manual},
      {max_batch_size,256},
      {driver,tcp},
      {socket_ip,{0,0,0,0}},
      {ssl_server_options,[{ciphers,[]},{versions,['tlsv1.3','tlsv1.2']}]},
      {ssl_client_options,[{ciphers,[]},{versions,['tlsv1.3','tlsv1.2']}]},
      {ssl_client_port,5369},
      {tcp_client_port,5369},
      {ssl_server_port,5369},
      {tcp_server_port,5369},
      {default_client_driver,tcp}]},
 {quicer,[{lb_mode,0}]},
 {mria,
     [{cluster_autoheal,true},
      {cluster_autoclean,86400000},
      {bootstrap_batch_size,500},
      {shard_transport,distr},
      {rlog_rpc_module,rpc},
      {node_role,core},
      {db_backend,rlog}]},
 {emqx_machine,
     [{custom_shard_transports,#{}},
      {applications,[]},
      {backtrace_depth,23},
      {global_gc_interval,900000}]},
 {emqx,
     [{data_dir,"data"},
      {cluster_hocon_file,"data/configs/cluster.hocon"},
      {local_override_conf_file,"data/configs/local-override.conf"},
      {cluster_override_conf_file,"data/configs/cluster-override.conf"},
      {config_files,["/opt/emqx/etc/emqx.conf"]}]},
 {kernel,
     [{error_logger,silent},
      {logger,
          [{handler,console,logger_std_h,
               #{config =>
                     #{type => standard_io,burst_limit_enable => true,
                       drop_mode_qlen => 3000,flush_qlen => 8000,
                       sync_mode_qlen => 100,
                       overload_kill_restart_after => 5000,
                       burst_limit_max_count => 10000,
                       burst_limit_window_time => 1000,
                       overload_kill_enable => true,
                       overload_kill_mem_size => 31457280,
                       overload_kill_qlen => 20000},
                 level => warning,
                 filters =>
                     [{drop_progress_reports,
                          {fun logger_filters:progress/2,stop}}],
                 formatter =>
                     {emqx_logger_textfmt,
                         #{time_offset => [],chars_limit => unlimited,
                           depth => 100,single_line => true,
                           template => ["[",level,"] ",msg,"\n"],
                           with_mfa => false,timestamp_format => auto,
                           payload_encode => text}}}}]},
      {logger_level,warning}]},
 {prometheus,
     [{collectors,
          [prometheus_boolean,prometheus_counter,prometheus_gauge,
           prometheus_histogram,prometheus_quantile_summary,
           prometheus_summary,emqx_prometheus,
           {'/prometheus/auth',emqx_prometheus_auth},
           {'/prometheus/data_integration',
               emqx_prometheus_data_integration}]}]}].
