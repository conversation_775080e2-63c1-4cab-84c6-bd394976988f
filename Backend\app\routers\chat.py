import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query, WebSocket, WebSocketDisconnect, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from sqlalchemy import select, or_, and_, func, desc, delete, update
from typing import List, Optional
from datetime import datetime, timedelta
import uuid
import os

from ..database import get_db
from ..models.user import User
# Exclude messages the requester has deleted for themselves
from ..models.chat import Conversation, Message, UserStatus
from ..models.deleted_message import DeletedMessage
from ..models.media import Media
from ..models.shared_link import SharedLink
from ..models.group_chat import GroupMessage, GroupMember, Group
from ..schemas.chat import (
    ConversationCreate, ConversationResponse, ConversationWithMessages,
    MessageCreate, MessageResponse, UserStatusResponse, OnlineUsersResponse,
    UnreadCountResponse, MediaResponse, SharedLinkResponse
)
from ..schemas.document_management import FileSelectionOption, UserSpaceFilesSelectionResponse

from ..auth.jwt_handler import get_user_from_token
from ..auth import get_current_user
from ..services.mqtt_service_client import mqtt_client
from ..services.websocket_session_cache import ws_session_cache
from ..services.minio_service import minio_service
from ..services.redis_service import redis_service, cached, invalidate_cache, invalidate_conversation_cache, invalidate_user_cache
from ..services.redis_logger import cached_get, cached_set
from ..services.cache_optimizer import cache_optimizer, cached_with_user, invalidate_user_specific_cache
from .. import config
import json
import io
from fastapi.responses import StreamingResponse
from jose import JWTError, jwt

router = APIRouter(prefix="/chat", tags=["Chat"])

# Configure logging for this module
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ---------------------------------------------------------------------------
# Conversation list & creation (restored)
# ---------------------------------------------------------------------------


@router.get("/conversations", response_model=List[ConversationResponse])
async def get_conversations(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Return all active one-to-one conversations for the logged-in user ordered by last update."""
    
    # Build cache key
    cache_key = f"{config.CACHE_PREFIX_CHAT}:conversations:{current_user.id}:{skip}:{limit}"

    # Try to get from cache with logging
    cached_data = await cached_get(cache_key)
    if cached_data:
        return cached_data

    result_query = await db.execute(
        select(Conversation)
        .options(
            joinedload(Conversation.participant1),
            joinedload(Conversation.participant2),
            joinedload(Conversation.messages),
        )
        .where(
            or_(
                Conversation.participant1_id == current_user.id,
                Conversation.participant2_id == current_user.id,
            ),
            Conversation.is_active == True,
        )
        .order_by(desc(Conversation.updated_at))
        .offset(skip)
        .limit(limit)
    )
    conversations = result_query.unique().scalars().all()

    result = [conv.to_dict() for conv in conversations]
    
    # Cache the result
    await cached_set(cache_key, result, ttl=config.CACHE_TTL_SHORT)
    
    return result


@router.post("/conversations", response_model=ConversationResponse, status_code=status.HTTP_201_CREATED)
async def create_conversation(
    conversation_data: ConversationCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Find an existing conversation with the given participant or create a new one."""

    # Validate participant
    result_query = await db.execute(
        select(User)
        .where(User.id == conversation_data.participant_id, User.is_active == True)
    )
    target_user = result_query.scalars().first()

    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    if target_user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot create conversation with yourself",
        )

    # look for existing convo (either direction)
    result_query = await db.execute(
        select(Conversation)
        .where(
            or_(
                and_(
                    Conversation.participant1_id == current_user.id,
                    Conversation.participant2_id == target_user.id,
                ),
                and_(
                    Conversation.participant1_id == target_user.id,
                    Conversation.participant2_id == current_user.id,
                ),
            )
        )
    )
    existing = result_query.scalars().first()

    if existing:
        # if it was archived/inactive reactivate it
        if not existing.is_active:
            existing.is_active = True
            existing.updated_at = datetime.utcnow()
            await db.commit()
        result = existing.to_dict()
        # Invalidate conversation list caches for both participants so it appears immediately
        try:
            await invalidate_user_cache(current_user.id)
            await invalidate_user_cache(target_user.id)
        except Exception as e:
            logger.warning(f"Conversation cache invalidation (existing) failed: {e}")
        # Publish conversation-created event to both participants for real-time UI update
        try:
            notify = {
                "type": "conversation_created",
                "conversation": result,
            }
            await mqtt_client.publish_event(current_user.id, notify)
            await mqtt_client.publish_event(target_user.id, notify)
        except Exception as e:
            logger.warning(f"MQTT publish failed for conversation_created (existing): {e}")
        return result

    # create new conversation (store smaller id first for uniqueness)
    new_conv = Conversation(
        participant1_id=min(current_user.id, target_user.id),
        participant2_id=max(current_user.id, target_user.id),
    )
    db.add(new_conv)
    await db.commit()
    await db.refresh(new_conv)

    # eager-load participants for response
    result_query = await db.execute(
        select(Conversation)
        .options(joinedload(Conversation.participant1), joinedload(Conversation.participant2))
        .where(Conversation.id == new_conv.id)
    )
    conv_with_rel = result_query.scalars().first()

    result = conv_with_rel.to_dict()
    # Invalidate conversation list caches for both participants
    try:
        await invalidate_user_cache(current_user.id)
        await invalidate_user_cache(target_user.id)
    except Exception as e:
        logger.warning(f"Conversation cache invalidation (create) failed: {e}")
    # Publish conversation-created event to both participants for real-time UI update
    try:
        notify = {
            "type": "conversation_created",
            "conversation": result,
        }
        await mqtt_client.publish_event(current_user.id, notify)
        await mqtt_client.publish_event(target_user.id, notify)
    except Exception as e:
        logger.warning(f"MQTT publish failed for conversation_created (create): {e}")
    return result

# ---------------------------------------------------------------------------
# Attachment Upload
# ---------------------------------------------------------------------------

from fastapi import UploadFile, File


@router.post("/attachments", status_code=status.HTTP_201_CREATED)
async def upload_attachment(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
):
    """Upload an attachment (image/file) to MinIO and return a public URL."""
    try:
        # Upload file to MinIO
        result = await minio_service.upload_file(
            file=file,
            bucket_type="chat_attachments",
            user_id=current_user.id,
            username=current_user.username  # Pass username for consistent bucket naming
        )
        
        if result["success"]:
            return {
                "filename": result["filename"],
                "size": result["file_size"],
                "content_type": result["content_type"],
                "minio_bucket": result["bucket_name"],
                "minio_object_key": result["object_key"]
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="File upload failed"
            )
            
    except Exception as e:
        logger.error(f"Error uploading attachment: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"File upload failed: {str(e)}"
        )

@router.get("/attachments/download")
async def download_attachment_by_key(
    bucket_name: str = Query(..., description="MinIO bucket name"),
    object_key: str = Query(..., description="MinIO object key"),
    token: Optional[str] = Query(None, description="JWT access token (optional if using Authorization header)"),
    request: Request = None,
    db: AsyncSession = Depends(get_db)
):
    """Download a chat attachment by bucket and object key."""
    # 1) Authenticate: allow token query param or Authorization header
    current_user: Optional[User] = None
    # Try token from query
    if not token:
        auth_header = request.headers.get("Authorization") if request else None
        if auth_header and auth_header.lower().startswith("bearer "):
            token = auth_header.split(" ", 1)[1].strip()
    if token:
        try:
            user_data = get_user_from_token(token)
            result_query = await db.execute(select(User).where(User.id == user_data["user_id"]))
            current_user = result_query.scalars().first()
            if not current_user:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found")
            if not current_user.is_active:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User account is disabled")
        except JWTError as e:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))
    else:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authentication token missing")
    result_query = await db.execute(select(Media).where(
        Media.minio_bucket == bucket_name,
        Media.minio_object_key == object_key
    ))
    media_item = result_query.scalars().first()
    # Set default filename and content type if not determined later
    filename: str = "file"
    content_type: str = "application/octet-stream"

    if media_item:
        # Check if current user is a participant in the conversation associated with the media
        result_query = await db.execute(select(Conversation).where(
            Conversation.id == media_item.conversation_id,
            or_(Conversation.participant1_id == current_user.id, Conversation.participant2_id == current_user.id)
        ))
        conv = result_query.scalars().first()
        if not conv:
            # Deny access if user is not part of the conversation
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied")
        
        # Use media item's filename and content type if available, otherwise use defaults
        filename = media_item.filename or filename
        content_type = media_item.content_type or content_type
    else:
        # If not a direct media item, check for group message attachment
        result_query = await db.execute(select(GroupMessage).where(
            GroupMessage.message_type.in_(["image", "file"]),
            GroupMessage.content.contains(object_key)
        ))
        gm = result_query.scalars().first()
        if not gm:
            # Raise 404 if no matching group message found
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Attachment not found")
        
        # Verify user is a member of the group
        result_query = await db.execute(select(GroupMember).where(
            GroupMember.group_id == gm.group_id,
            GroupMember.user_id == current_user.id
        ))
        membership = result_query.scalars().first()
        if not membership:
            # Deny access if user is not a group member
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied")
        
        try:
            # Try to extract filename and content type from group message content
            meta = json.loads(gm.content)
            filename = (meta.get("filename") or meta.get("name") or filename)
            content_type = meta.get("content_type") or content_type
        except Exception:
            # Silently ignore parsing errors, keeping default filename and content type
            pass

    # Download file from MinIO storage
    file_bytes = minio_service.download_file(bucket_name=bucket_name, object_key=object_key)
    
    # Sanitize filename to remove potentially problematic characters
    safe_filename = (filename or "file").replace("\r", "").replace("\n", "").replace(",", "_")
    
    # Set content disposition for inline file display
    content_disposition = f'inline; filename="{safe_filename}"'
    
    # Return file as a streaming response
    return StreamingResponse(
        io.BytesIO(file_bytes),
        media_type=content_type or "application/octet-stream",
        headers={"Content-Disposition": content_disposition}
    )


@router.get("/conversations/{conversation_id}", response_model=ConversationWithMessages)
async def get_conversation_with_messages(
    conversation_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get conversation details with messages"""
    
    # Verify user is part of the conversation
    result_query = await db.execute(
        select(Conversation).options(
            joinedload(Conversation.participant1),
            joinedload(Conversation.participant2)
        ).where(
            Conversation.id == conversation_id,
            or_(
                Conversation.participant1_id == current_user.id,
                Conversation.participant2_id == current_user.id
            )
        )
    )
    conversation = result_query.scalars().first()
    
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    
    # Get messages, skipping any the current user has deleted for themselves
    result_query = await db.execute(
        select(Message)
        .options(joinedload(Message.sender))
        .outerjoin(DeletedMessage, (
            (DeletedMessage.message_id == Message.id) & (DeletedMessage.user_id == current_user.id)
        ))
        .where(
            Message.conversation_id == conversation_id,
            DeletedMessage.id == None  # noqa: E711 – comparing to None is intentional
        )
        .order_by(desc(Message.created_at))
        .offset(skip)
        .limit(limit)
    )
    messages = result_query.scalars().all()
    
    # Reverse to show oldest first
    messages.reverse()
    
    conversation_dict = conversation.to_dict()
    conversation_dict['messages'] = [msg.to_dict() for msg in messages]
    
    return conversation_dict

@router.post("/conversations/{conversation_id}/messages", response_model=MessageResponse, status_code=status.HTTP_201_CREATED)
async def send_message(
    conversation_id: int,
    message_data: MessageCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Send a message in a conversation (REST endpoint, WebSocket is preferred for real-time)"""
    
    # Verify user is part of the conversation
    result_query = await db.execute(
        select(Conversation).where(
            Conversation.id == conversation_id,
            or_(
                Conversation.participant1_id == current_user.id,
                Conversation.participant2_id == current_user.id
            ),
            Conversation.is_active == True
        )
    )
    conversation = result_query.scalars().first()
    
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    
    # Create new message
    new_message = Message(
        conversation_id=conversation_id,
        sender_id=current_user.id,
        content=message_data.content,
        message_type=message_data.message_type
    )
    
    db.add(new_message)
    
    # Update conversation timestamp
    conversation.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(new_message)
    
    # Load with sender relationship
    result_query = await db.execute(
        select(Message).options(
            joinedload(Message.sender)
        ).where(Message.id == new_message.id)
    )
    message_with_sender = result_query.scalars().first()
    
    # Invalidate conversation caches and per-user conversation lists so ordering/visibility updates
    await invalidate_conversation_cache(conversation_id)
    try:
        other_id = conversation.participant1_id if conversation.participant2_id == current_user.id else conversation.participant2_id
        await invalidate_user_cache(current_user.id)
        await invalidate_user_cache(other_id)
    except Exception as e:
        logger.warning(f"Conversation/user cache invalidation after send failed: {e}")
    # Publish MQTT events to both participants including sender echo for real-time updates
    try:
        event = {
            "type": "new_message",
            "conversation_id": conversation_id,
            "message": message_with_sender.to_dict(),
        }
        await mqtt_client.publish_event(current_user.id, event)
        await mqtt_client.publish_event(other_id, event)
    except Exception as e:
        logger.warning(f"MQTT publish failed for new_message: {e}")
    
    return message_with_sender.to_dict()

@router.put("/conversations/{conversation_id}/read")
async def mark_conversation_as_read(
    conversation_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Mark all messages in a conversation as read"""
    
    # Verify user is part of the conversation
    result_query = await db.execute(
        select(Conversation).where(
            Conversation.id == conversation_id,
            or_(
                Conversation.participant1_id == current_user.id,
                Conversation.participant2_id == current_user.id
            )
        )
    )
    conversation = result_query.scalars().first()
    
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    
    # Mark messages as read (excluding own messages)
    stmt = (
        update(Message)
        .where(
            Message.conversation_id == conversation_id,
            Message.sender_id != current_user.id,
            Message.is_read == False
        )
        .values(is_read=True)
    )
    result = await db.execute(stmt)
    updated_count = result.rowcount
    
    await db.commit()
    # Notify the other participant about read receipt in realtime
    try:
        other_id = conversation.participant1_id if conversation.participant2_id == current_user.id else conversation.participant2_id
        event = {"type": "read_receipt", "conversation_id": conversation_id, "reader_id": current_user.id}
        await mqtt_client.publish_event(other_id, event)
    except Exception:
        pass
    
    return {"message": f"Marked {updated_count} messages as read"}

@router.post("/mqtt-token")
async def get_mqtt_broker_token(
    current_user: User = Depends(get_current_user)
):
    """Generate MQTT broker JWT token for authenticated user"""
    
    user_id = current_user.id
    # Define Access Control List (ACL) for MQTT broker permissions
    acl = [
        # Allow publishing to user-specific command topic
        {"permission": "allow", "action": "publish", "topic": f"{config.MQTT_TOPIC_PREFIX}/chat/cmd/{user_id}"},
        # Allow subscribing to user-specific event topic
        {"permission": "allow", "action": "subscribe", "topic": f"{config.MQTT_TOPIC_PREFIX}/chat/users/{user_id}/events"},
        # Allow publishing to user's presence topic
        {"permission": "allow", "action": "publish", "topic": f"{config.MQTT_TOPIC_PREFIX}/presence/{user_id}"},
        # Allow subscribing to all users' presence topics
        {"permission": "allow", "action": "subscribe", "topic": f"{config.MQTT_TOPIC_PREFIX}/presence/+"}
    ]
    
    # Prepare JWT payload with user details and access permissions
    payload = {
        "sub": f"user:{user_id}",  # Subject identifier
        "user_id": user_id,
        "username": current_user.username,
        "acl": acl,  # Access control list
        "exp": datetime.utcnow() + timedelta(minutes=config.MQTT_BROKER_JWT_EXPIRE_MINUTES),  # Expiration time
        "iat": datetime.utcnow()  # Issued at time
    }
    
    # Encode JWT with broker-specific secret and algorithm
    token = jwt.encode(payload, config.MQTT_BROKER_JWT_SECRET, algorithm=config.MQTT_BROKER_JWT_ALGORITHM)
    
    # Return token and connection details for MQTT broker
    return {
        "token": token,
        "expires_in": config.MQTT_BROKER_JWT_EXPIRE_MINUTES * 60,  # Convert minutes to seconds
        "mqtt_host": config.MQTT_HOST,
        "mqtt_ws_port": config.MQTT_WS_PORT,  # WebSocket port
        "mqtt_wss_port": config.MQTT_WSS_PORT,  # Secure WebSocket port
        "topic_prefix": config.MQTT_TOPIC_PREFIX
    }

@router.get("/users/online", response_model=OnlineUsersResponse)
async def get_online_users(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get list of online users"""
    
    # Use Redis-backed presence for cross-process scale
    online_user_ids = list(await ws_session_cache.get_online_users())
    logger.info(f"Presence via Redis: {len(online_user_ids)} users online")
    
    if online_user_ids:
        result_query = await db.execute(
            select(User).where(
                User.id.in_(online_user_ids),
                User.id != current_user.id,  # Exclude current user
                User.is_active == True
            )
        )
        users = result_query.scalars().all()
        
        user_statuses = []
        for user in users:
            user_statuses.append({
                'user_id': user.id,
                'username': user.username,
                'industry': user.industry,
                'is_online': True,
                'last_seen': datetime.utcnow().isoformat()
            })
    else:
        user_statuses = []
    
    return {
        'users': user_statuses,
        'total_count': len(user_statuses)
    }

@router.get("/users/all", response_model=List[UserStatusResponse])
async def get_all_users_with_status(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get all users with their online status"""
    
    # Get all active users except current user
    result_query = await db.execute(
        select(User, UserStatus).outerjoin(
            UserStatus, User.id == UserStatus.user_id
        ).where(
            User.id != current_user.id,
            User.is_active == True,
            User.status == 'approved'
        )
    )
    users_query = result_query.all()
    
    # Use Redis-backed presence
    online_user_ids = set(await ws_session_cache.get_online_users())
    logger.info(f"Presence via Redis (all): {len(online_user_ids)} users online")
    
    user_statuses = []
    for user, status in users_query:
        is_online = user.id in online_user_ids
        last_seen = status.last_seen if status else user.created_at
        
        user_statuses.append({
            'user_id': user.id,
            'username': user.username,
            'industry': user.industry,
            'is_online': is_online,
            'last_seen': last_seen.isoformat() if last_seen else None
        })
    
    # Sort by online status first, then by username
    user_statuses.sort(key=lambda x: (not x['is_online'], x['username']))
    
    return user_statuses

@router.get("/unread-messages", response_model=UnreadCountResponse)
async def get_unread_message_count(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get unread message count and messages for current user"""
    
    # Get conversations user is part of
    result_query = await db.execute(
        select(Conversation.id).where(
            or_(
                Conversation.participant1_id == current_user.id,
                Conversation.participant2_id == current_user.id
            ),
            Conversation.is_active == True
        )
    )
    user_conversations = result_query.scalars().all()
    logger.info(f"User conversations: {user_conversations}")
    
    conversation_ids = list(user_conversations)
    
    if not conversation_ids:
        return {'total_unread': 0, 'conversations': []}
    
    # Get unread count per conversation
    result_query = await db.execute(
        select(
            Message.conversation_id,
            func.count(Message.id).label('unread_count')
        ).where(
            Message.conversation_id.in_(conversation_ids),
            Message.sender_id != current_user.id,
            Message.is_read == False
        ).group_by(Message.conversation_id)
    )
    unread_by_conversation = result_query.all()
    
    total_unread = sum(count for _, count in unread_by_conversation)
    
    # Prepare conversations response with unread messages per conversation
    conversations = []
    for conv_id, count in unread_by_conversation:
        # Get unread messages for this specific conversation
        result_query = await db.execute(
            select(Message)
            .options(joinedload(Message.sender))
            .where(
                Message.conversation_id == conv_id,
                Message.sender_id != current_user.id,
                Message.is_read == False
            )
        )
        conv_unread_messages = result_query.scalars().all()
        
        # Convert to response format
        unread_messages_list = [msg.to_dict() for msg in conv_unread_messages]
        logger.info(f"Conversation {conv_id}: {len(unread_messages_list)} unread messages")
        
        conversations.append({
            'conversation_id': conv_id, 
            'unread_count': count,
            'unread_messages': unread_messages_list
        })
    
    return {
        'total_unread': total_unread,
        'conversations': conversations
    }

@router.delete("/conversations/{conversation_id}")
async def deactivate_conversation(
    conversation_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Deactivate a conversation (soft delete)"""
    
    result_query = await db.execute(
        select(Conversation).where(
            Conversation.id == conversation_id,
            or_(
                Conversation.participant1_id == current_user.id,
                Conversation.participant2_id == current_user.id
            )
        )
    )
    conversation = result_query.scalars().first()
    
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conversation not found"
        )
    
    conversation.is_active = False
    conversation.updated_at = datetime.utcnow()
    
    await db.commit()
    # Invalidate caches related to this conversation and participants
    await invalidate_conversation_cache(conversation_id)
    await invalidate_user_cache(conversation.participant1_id)
    await invalidate_user_cache(conversation.participant2_id)
    # Publish real-time event to both participants so tiles disappear immediately
    try:
        event = {"type": "conversation_deleted", "conversation_id": conversation_id}
        await mqtt_client.publish_event(conversation.participant1_id, event)
        await mqtt_client.publish_event(conversation.participant2_id, event)
    except Exception:
        pass
    
    return {"message": "Conversation deactivated successfully"}

# ---------------------------------------------------------------------------
# Clear chat history (hide all messages for current user)
# ---------------------------------------------------------------------------


@router.delete("/conversations/{conversation_id}/clear-history", status_code=status.HTTP_204_NO_CONTENT)
async def clear_conversation_history(
    conversation_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Hide all messages in a conversation for **this** user only (local clear history)."""

    # Verify user is part of the conversation
    result_query = await db.execute(
        select(Conversation).where(
            Conversation.id == conversation_id,
            or_(
                Conversation.participant1_id == current_user.id,
                Conversation.participant2_id == current_user.id,
            ),
        )
    )
    conversation = result_query.scalars().first()

    if not conversation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found")

    # Fetch all message ids in this conversation
    result_query = await db.execute(select(Message.id).where(Message.conversation_id == conversation_id))
    message_ids = result_query.scalars().all()

    # Insert deletion records where missing
    to_create = []
    for mid in message_ids:
        result_query = await db.execute(
            select(DeletedMessage).where(
                DeletedMessage.user_id == current_user.id,
                DeletedMessage.message_id == mid
            )
        )
        exists = result_query.scalars().first()
        if not exists:
            to_create.append(DeletedMessage(user_id=current_user.id, message_id=mid))

    if to_create:
        for obj in to_create:
            db.add(obj)
        await db.commit()

    return  # 204



@router.get("/history/{recipient_id}")
async def get_chat_history(
    recipient_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    try:
        # Get from database
        result_query = await db.execute(
            select(Message).join(Conversation).where(
                or_(
                    and_(
                        Conversation.participant1_id == current_user.id,
                        Conversation.participant2_id == recipient_id
                    ),
                    and_(
                        Conversation.participant1_id == recipient_id,
                        Conversation.participant2_id == current_user.id
                    )
                )
            ).order_by(Message.created_at.desc()).limit(50)
        )
        messages = result_query.scalars().all()

        # Format messages for response
        message_list = [{
            "id": msg.id,
            "sender_id": msg.sender_id,
            "content": msg.content,
            "timestamp": str(msg.created_at)
        } for msg in messages]

        return {"messages": message_list}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/conversations/{conversation_id}/media", response_model=List[MediaResponse])
async def get_conversation_media(
    conversation_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Return paginated list of media (images / files) shared in a conversation"""

    # verify membership
    result_query = await db.execute(
        select(Conversation).where(
            Conversation.id == conversation_id,
            or_(
                Conversation.participant1_id == current_user.id,
                Conversation.participant2_id == current_user.id,
            )
        )
    )
    conversation = result_query.scalars().first()

    if not conversation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found")

    result_query = await db.execute(
        select(Media)
        .where(Media.conversation_id == conversation_id)
        .order_by(desc(Media.created_at))
        .offset(skip)
        .limit(limit)
    )
    media_items = result_query.scalars().all()

    return [item.to_dict() for item in media_items]

# ---------------------------------------------
# Shared Links endpoints
# ---------------------------------------------

@router.get("/conversations/{conversation_id}/links", response_model=List[SharedLinkResponse])
async def get_conversation_links(
    conversation_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Return list of URLs shared in a conversation"""

    result_query = await db.execute(
        select(Conversation).where(
            Conversation.id == conversation_id,
            or_(
                Conversation.participant1_id == current_user.id,
                Conversation.participant2_id == current_user.id,
            )
        )
    )
    conversation = result_query.scalars().first()

    if not conversation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found")

    result_query = await db.execute(
        select(SharedLink)
        .where(SharedLink.conversation_id == conversation_id)
        .order_by(desc(SharedLink.created_at))
        .offset(skip)
        .limit(limit)
    )
    links = result_query.scalars().all()

    return [l.to_dict() for l in links]


# ---------------------------------------------
# Per-user media / links galleries
# ---------------------------------------------

@router.get("/users/{user_id}/media", response_model=List[MediaResponse])
async def get_user_media(
    user_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    if user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied")

    result_query = await db.execute(
        select(Media)
        .where(Media.uploader_id == user_id)
        .order_by(desc(Media.created_at))
        .offset(skip)
        .limit(limit)
    )
    items = result_query.scalars().all()
    return [i.to_dict() for i in items]


@router.get("/users/{user_id}/links", response_model=List[SharedLinkResponse])
async def get_user_links(
    user_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    if user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied")

    result_query = await db.execute(
        select(SharedLink)
        .join(Message, SharedLink.message_id == Message.id)
        .where(Message.sender_id == user_id)
        .order_by(desc(SharedLink.created_at))
        .offset(skip)
        .limit(limit)
    )
    items = result_query.scalars().all()
    return [i.to_dict() for i in items]

@router.get("/find-user/{username}")
async def find_user(username: str, db: AsyncSession = Depends(get_db)):
    result_query = await db.execute(select(User).where(User.username == username))
    user = result_query.scalars().first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return user.to_dict()
