#!/usr/bin/env python3
"""
Migration: Add Space-Group Linking Support

This migration adds:
1. space_id field to chat_groups table to link groups to shared spaces
2. Index for performance
3. Ensures backward compatibility with existing groups
"""

import logging
import os
import sys
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the parent directory to the path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config import DATABASE_URL

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def run_migration():
    """Run the space-group linking migration"""
    logger.info("Starting space-group linking migration...")
    
    with engine.connect() as connection:
        # Start transaction
        trans = connection.begin()
        
        try:
            # 1. Add space_id column to chat_groups table (nullable for existing groups)
            logger.info("Adding space_id column to chat_groups table...")
            connection.execute(text("""
                ALTER TABLE chat_groups 
                ADD COLUMN IF NOT EXISTS space_id INTEGER REFERENCES user_spaces(id) ON DELETE SET NULL
            """))
            
            # 2. Add index on space_id for performance
            logger.info("Adding index on space_id...")
            connection.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chat_groups_space_id 
                ON chat_groups(space_id)
            """))
            
            # 3. Add unique constraint to ensure one group per space
            logger.info("Adding unique constraint for space_id...")
            connection.execute(text("""
                CREATE UNIQUE INDEX IF NOT EXISTS idx_chat_groups_space_id_unique 
                ON chat_groups(space_id) 
                WHERE space_id IS NOT NULL
            """))
            
            # Commit transaction
            trans.commit()
            logger.info("Space-group linking migration completed successfully!")
            
        except Exception as e:
            # Rollback on error
            trans.rollback()
            logger.error(f"Migration failed: {e}")
            raise

def verify_migration():
    """Verify that the migration was applied correctly"""
    logger.info("Verifying migration...")
    
    with engine.connect() as connection:
        # Check if space_id column exists
        result = connection.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'chat_groups' AND column_name = 'space_id'
        """))
        
        if not result.fetchone():
            raise Exception("space_id column not found in chat_groups table")
        
        # Check if index exists
        result = connection.execute(text("""
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = 'chat_groups' AND indexname = 'idx_chat_groups_space_id'
        """))
        
        if not result.fetchone():
            logger.warning("Index idx_chat_groups_space_id not found")
        
        # Check if unique index exists
        result = connection.execute(text("""
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = 'chat_groups' AND indexname = 'idx_chat_groups_space_id_unique'
        """))
        
        if not result.fetchone():
            logger.warning("Unique index idx_chat_groups_space_id_unique not found")
        
        logger.info("Migration verification completed successfully!")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Space-Group Linking Migration")
    parser.add_argument("--verify", action="store_true", help="Verify the migration only (no changes)")
    args = parser.parse_args()
    
    try:
        if args.verify:
            verify_migration()
        else:
            run_migration()
            verify_migration()
    except Exception as e:
        logger.error(f"Migration operation failed: {e}")
        sys.exit(1)
