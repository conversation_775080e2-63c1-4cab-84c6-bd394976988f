import logging
from dataclasses import dataclass
import os
from dotenv import load_dotenv
load_dotenv()

query_llm = os.getenv("QUERY_LLM")
response_model = os.getenv("RESPONSE_MODEL")
embedding_api_url = os.getenv("EMBEDDING_API_URL")
reranking_service_url = os.getenv("RERANKING_SERVICE_URL")

@dataclass
class RetrieverConfig:
    """Configuration for the RAG pipeline

    Args:
        gemini_api_key (str): The API key for the Gemini API.
        qdrant_url (str): The URL for the Qdrant server.
        prefer_grpc (bool): Whether to use gRPC for the Qdrant server.
        grpc_port (int): The port for the gRPC server.
        qdrant_api_key (str): The API key for the Qdrant server.
        embedding_api_url (str): The URL for the external embedding service.
        reranking_service_url (str): The URL for the external reranking service.
        query_llm (str): The model for the query LLM.
        response_model (str): The model for the response.
        database_url (str): The database URL.
        image_embedding_service_url (str): The URL for the image embedding service (SigLIP-2).
    """
    gemini_api_key: str
    qdrant_url: str
    prefer_grpc: bool = True
    grpc_port: int = None
    qdrant_api_key: str = None
    embedding_api_url: str = embedding_api_url
    reranking_service_url: str = reranking_service_url
    query_llm: str = query_llm
    response_model: str = response_model
    database_url: str = None
    image_embedding_service_url: str = "http://**************:8019"
