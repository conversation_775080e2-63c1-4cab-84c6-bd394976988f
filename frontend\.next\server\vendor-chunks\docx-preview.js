"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/docx-preview";
exports.ids = ["vendor-chunks/docx-preview"];
exports.modules = {

/***/ "(ssr)/./node_modules/docx-preview/dist/docx-preview.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/docx-preview/dist/docx-preview.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultOptions: () => (/* binding */ defaultOptions),\n/* harmony export */   parseAsync: () => (/* binding */ parseAsync),\n/* harmony export */   renderAsync: () => (/* binding */ renderAsync),\n/* harmony export */   renderDocument: () => (/* binding */ renderDocument)\n/* harmony export */ });\n/* harmony import */ var jszip__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jszip */ \"(ssr)/./node_modules/jszip/lib/index.js\");\n/*\n * @license\n * docx-preview <https://github.com/VolodymyrBaydalka/docxjs>\n * Released under Apache License 2.0  <https://github.com/VolodymyrBaydalka/docxjs/blob/master/LICENSE>\n * Copyright Volodymyr Baydalka\n */\n\n\nvar RelationshipTypes;\n(function (RelationshipTypes) {\n    RelationshipTypes[\"OfficeDocument\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument\";\n    RelationshipTypes[\"FontTable\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable\";\n    RelationshipTypes[\"Image\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image\";\n    RelationshipTypes[\"Numbering\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering\";\n    RelationshipTypes[\"Styles\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles\";\n    RelationshipTypes[\"StylesWithEffects\"] = \"http://schemas.microsoft.com/office/2007/relationships/stylesWithEffects\";\n    RelationshipTypes[\"Theme\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme\";\n    RelationshipTypes[\"Settings\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings\";\n    RelationshipTypes[\"WebSettings\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/webSettings\";\n    RelationshipTypes[\"Hyperlink\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink\";\n    RelationshipTypes[\"Footnotes\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes\";\n    RelationshipTypes[\"Endnotes\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/endnotes\";\n    RelationshipTypes[\"Footer\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer\";\n    RelationshipTypes[\"Header\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/header\";\n    RelationshipTypes[\"ExtendedProperties\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties\";\n    RelationshipTypes[\"CoreProperties\"] = \"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties\";\n    RelationshipTypes[\"CustomProperties\"] = \"http://schemas.openxmlformats.org/package/2006/relationships/metadata/custom-properties\";\n    RelationshipTypes[\"Comments\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments\";\n    RelationshipTypes[\"CommentsExtended\"] = \"http://schemas.microsoft.com/office/2011/relationships/commentsExtended\";\n    RelationshipTypes[\"AltChunk\"] = \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/aFChunk\";\n})(RelationshipTypes || (RelationshipTypes = {}));\nfunction parseRelationships(root, xml) {\n    return xml.elements(root).map(e => ({\n        id: xml.attr(e, \"Id\"),\n        type: xml.attr(e, \"Type\"),\n        target: xml.attr(e, \"Target\"),\n        targetMode: xml.attr(e, \"TargetMode\")\n    }));\n}\n\nfunction escapeClassName(className) {\n    return className?.replace(/[ .]+/g, '-').replace(/[&]+/g, 'and').toLowerCase();\n}\nfunction encloseFontFamily(fontFamily) {\n    return /^[^\"'].*\\s.*[^\"']$/.test(fontFamily) ? `'${fontFamily}'` : fontFamily;\n}\nfunction splitPath(path) {\n    let si = path.lastIndexOf('/') + 1;\n    let folder = si == 0 ? \"\" : path.substring(0, si);\n    let fileName = si == 0 ? path : path.substring(si);\n    return [folder, fileName];\n}\nfunction resolvePath(path, base) {\n    try {\n        const prefix = \"http://docx/\";\n        const url = new URL(path, prefix + base).toString();\n        return url.substring(prefix.length);\n    }\n    catch {\n        return `${base}${path}`;\n    }\n}\nfunction keyBy(array, by) {\n    return array.reduce((a, x) => {\n        a[by(x)] = x;\n        return a;\n    }, {});\n}\nfunction blobToBase64(blob) {\n    return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onloadend = () => resolve(reader.result);\n        reader.onerror = () => reject();\n        reader.readAsDataURL(blob);\n    });\n}\nfunction isObject(item) {\n    return item && typeof item === 'object' && !Array.isArray(item);\n}\nfunction isString(item) {\n    return typeof item === 'string' || item instanceof String;\n}\nfunction mergeDeep(target, ...sources) {\n    if (!sources.length)\n        return target;\n    const source = sources.shift();\n    if (isObject(target) && isObject(source)) {\n        for (const key in source) {\n            if (isObject(source[key])) {\n                const val = target[key] ?? (target[key] = {});\n                mergeDeep(val, source[key]);\n            }\n            else {\n                target[key] = source[key];\n            }\n        }\n    }\n    return mergeDeep(target, ...sources);\n}\nfunction asArray(val) {\n    return Array.isArray(val) ? val : [val];\n}\nfunction clamp(val, min, max) {\n    return min > val ? min : (max < val ? max : val);\n}\n\nconst ns$1 = {\n    wordml: \"http://schemas.openxmlformats.org/wordprocessingml/2006/main\"};\nconst LengthUsage = {\n    Dxa: { mul: 0.05, unit: \"pt\" },\n    Emu: { mul: 1 / 12700, unit: \"pt\" },\n    FontSize: { mul: 0.5, unit: \"pt\" },\n    Border: { mul: 0.125, unit: \"pt\", min: 0.25, max: 12 },\n    Point: { mul: 1, unit: \"pt\" },\n    Percent: { mul: 0.02, unit: \"%\" }};\nfunction convertLength(val, usage = LengthUsage.Dxa) {\n    if (val == null || /.+(p[xt]|[%])$/.test(val)) {\n        return val;\n    }\n    var num = parseInt(val) * usage.mul;\n    if (usage.min && usage.max)\n        num = clamp(num, usage.min, usage.max);\n    return `${num.toFixed(2)}${usage.unit}`;\n}\nfunction convertBoolean(v, defaultValue = false) {\n    switch (v) {\n        case \"1\": return true;\n        case \"0\": return false;\n        case \"on\": return true;\n        case \"off\": return false;\n        case \"true\": return true;\n        case \"false\": return false;\n        default: return defaultValue;\n    }\n}\nfunction parseCommonProperty(elem, props, xml) {\n    if (elem.namespaceURI != ns$1.wordml)\n        return false;\n    switch (elem.localName) {\n        case \"color\":\n            props.color = xml.attr(elem, \"val\");\n            break;\n        case \"sz\":\n            props.fontSize = xml.lengthAttr(elem, \"val\", LengthUsage.FontSize);\n            break;\n        default:\n            return false;\n    }\n    return true;\n}\n\nfunction parseXmlString(xmlString, trimXmlDeclaration = false) {\n    if (trimXmlDeclaration)\n        xmlString = xmlString.replace(/<[?].*[?]>/, \"\");\n    xmlString = removeUTF8BOM(xmlString);\n    const result = new DOMParser().parseFromString(xmlString, \"application/xml\");\n    const errorText = hasXmlParserError(result);\n    if (errorText)\n        throw new Error(errorText);\n    return result;\n}\nfunction hasXmlParserError(doc) {\n    return doc.getElementsByTagName(\"parsererror\")[0]?.textContent;\n}\nfunction removeUTF8BOM(data) {\n    return data.charCodeAt(0) === 0xFEFF ? data.substring(1) : data;\n}\nfunction serializeXmlString(elem) {\n    return new XMLSerializer().serializeToString(elem);\n}\nclass XmlParser {\n    elements(elem, localName = null) {\n        const result = [];\n        for (let i = 0, l = elem.childNodes.length; i < l; i++) {\n            let c = elem.childNodes.item(i);\n            if (c.nodeType == Node.ELEMENT_NODE && (localName == null || c.localName == localName))\n                result.push(c);\n        }\n        return result;\n    }\n    element(elem, localName) {\n        for (let i = 0, l = elem.childNodes.length; i < l; i++) {\n            let c = elem.childNodes.item(i);\n            if (c.nodeType == 1 && c.localName == localName)\n                return c;\n        }\n        return null;\n    }\n    elementAttr(elem, localName, attrLocalName) {\n        var el = this.element(elem, localName);\n        return el ? this.attr(el, attrLocalName) : undefined;\n    }\n    attrs(elem) {\n        return Array.from(elem.attributes);\n    }\n    attr(elem, localName) {\n        for (let i = 0, l = elem.attributes.length; i < l; i++) {\n            let a = elem.attributes.item(i);\n            if (a.localName == localName)\n                return a.value;\n        }\n        return null;\n    }\n    intAttr(node, attrName, defaultValue = null) {\n        var val = this.attr(node, attrName);\n        return val ? parseInt(val) : defaultValue;\n    }\n    hexAttr(node, attrName, defaultValue = null) {\n        var val = this.attr(node, attrName);\n        return val ? parseInt(val, 16) : defaultValue;\n    }\n    floatAttr(node, attrName, defaultValue = null) {\n        var val = this.attr(node, attrName);\n        return val ? parseFloat(val) : defaultValue;\n    }\n    boolAttr(node, attrName, defaultValue = null) {\n        return convertBoolean(this.attr(node, attrName), defaultValue);\n    }\n    lengthAttr(node, attrName, usage = LengthUsage.Dxa) {\n        return convertLength(this.attr(node, attrName), usage);\n    }\n}\nconst globalXmlParser = new XmlParser();\n\nclass Part {\n    constructor(_package, path) {\n        this._package = _package;\n        this.path = path;\n    }\n    async load() {\n        this.rels = await this._package.loadRelationships(this.path);\n        const xmlText = await this._package.load(this.path);\n        const xmlDoc = this._package.parseXmlDocument(xmlText);\n        if (this._package.options.keepOrigin) {\n            this._xmlDocument = xmlDoc;\n        }\n        this.parseXml(xmlDoc.firstElementChild);\n    }\n    save() {\n        this._package.update(this.path, serializeXmlString(this._xmlDocument));\n    }\n    parseXml(root) {\n    }\n}\n\nconst embedFontTypeMap = {\n    embedRegular: 'regular',\n    embedBold: 'bold',\n    embedItalic: 'italic',\n    embedBoldItalic: 'boldItalic',\n};\nfunction parseFonts(root, xml) {\n    return xml.elements(root).map(el => parseFont(el, xml));\n}\nfunction parseFont(elem, xml) {\n    let result = {\n        name: xml.attr(elem, \"name\"),\n        embedFontRefs: []\n    };\n    for (let el of xml.elements(elem)) {\n        switch (el.localName) {\n            case \"family\":\n                result.family = xml.attr(el, \"val\");\n                break;\n            case \"altName\":\n                result.altName = xml.attr(el, \"val\");\n                break;\n            case \"embedRegular\":\n            case \"embedBold\":\n            case \"embedItalic\":\n            case \"embedBoldItalic\":\n                result.embedFontRefs.push(parseEmbedFontRef(el, xml));\n                break;\n        }\n    }\n    return result;\n}\nfunction parseEmbedFontRef(elem, xml) {\n    return {\n        id: xml.attr(elem, \"id\"),\n        key: xml.attr(elem, \"fontKey\"),\n        type: embedFontTypeMap[elem.localName]\n    };\n}\n\nclass FontTablePart extends Part {\n    parseXml(root) {\n        this.fonts = parseFonts(root, this._package.xmlParser);\n    }\n}\n\nclass OpenXmlPackage {\n    constructor(_zip, options) {\n        this._zip = _zip;\n        this.options = options;\n        this.xmlParser = new XmlParser();\n    }\n    get(path) {\n        const p = normalizePath(path);\n        return this._zip.files[p] ?? this._zip.files[p.replace(/\\//g, '\\\\')];\n    }\n    update(path, content) {\n        this._zip.file(path, content);\n    }\n    static async load(input, options) {\n        const zip = await jszip__WEBPACK_IMPORTED_MODULE_0__.loadAsync(input);\n        return new OpenXmlPackage(zip, options);\n    }\n    save(type = \"blob\") {\n        return this._zip.generateAsync({ type });\n    }\n    load(path, type = \"string\") {\n        return this.get(path)?.async(type) ?? Promise.resolve(null);\n    }\n    async loadRelationships(path = null) {\n        let relsPath = `_rels/.rels`;\n        if (path != null) {\n            const [f, fn] = splitPath(path);\n            relsPath = `${f}_rels/${fn}.rels`;\n        }\n        const txt = await this.load(relsPath);\n        return txt ? parseRelationships(this.parseXmlDocument(txt).firstElementChild, this.xmlParser) : null;\n    }\n    parseXmlDocument(txt) {\n        return parseXmlString(txt, this.options.trimXmlDeclaration);\n    }\n}\nfunction normalizePath(path) {\n    return path.startsWith('/') ? path.substr(1) : path;\n}\n\nclass DocumentPart extends Part {\n    constructor(pkg, path, parser) {\n        super(pkg, path);\n        this._documentParser = parser;\n    }\n    parseXml(root) {\n        this.body = this._documentParser.parseDocumentFile(root);\n    }\n}\n\nfunction parseBorder(elem, xml) {\n    return {\n        type: xml.attr(elem, \"val\"),\n        color: xml.attr(elem, \"color\"),\n        size: xml.lengthAttr(elem, \"sz\", LengthUsage.Border),\n        offset: xml.lengthAttr(elem, \"space\", LengthUsage.Point),\n        frame: xml.boolAttr(elem, 'frame'),\n        shadow: xml.boolAttr(elem, 'shadow')\n    };\n}\nfunction parseBorders(elem, xml) {\n    var result = {};\n    for (let e of xml.elements(elem)) {\n        switch (e.localName) {\n            case \"left\":\n                result.left = parseBorder(e, xml);\n                break;\n            case \"top\":\n                result.top = parseBorder(e, xml);\n                break;\n            case \"right\":\n                result.right = parseBorder(e, xml);\n                break;\n            case \"bottom\":\n                result.bottom = parseBorder(e, xml);\n                break;\n        }\n    }\n    return result;\n}\n\nvar SectionType;\n(function (SectionType) {\n    SectionType[\"Continuous\"] = \"continuous\";\n    SectionType[\"NextPage\"] = \"nextPage\";\n    SectionType[\"NextColumn\"] = \"nextColumn\";\n    SectionType[\"EvenPage\"] = \"evenPage\";\n    SectionType[\"OddPage\"] = \"oddPage\";\n})(SectionType || (SectionType = {}));\nfunction parseSectionProperties(elem, xml = globalXmlParser) {\n    var section = {};\n    for (let e of xml.elements(elem)) {\n        switch (e.localName) {\n            case \"pgSz\":\n                section.pageSize = {\n                    width: xml.lengthAttr(e, \"w\"),\n                    height: xml.lengthAttr(e, \"h\"),\n                    orientation: xml.attr(e, \"orient\")\n                };\n                break;\n            case \"type\":\n                section.type = xml.attr(e, \"val\");\n                break;\n            case \"pgMar\":\n                section.pageMargins = {\n                    left: xml.lengthAttr(e, \"left\"),\n                    right: xml.lengthAttr(e, \"right\"),\n                    top: xml.lengthAttr(e, \"top\"),\n                    bottom: xml.lengthAttr(e, \"bottom\"),\n                    header: xml.lengthAttr(e, \"header\"),\n                    footer: xml.lengthAttr(e, \"footer\"),\n                    gutter: xml.lengthAttr(e, \"gutter\"),\n                };\n                break;\n            case \"cols\":\n                section.columns = parseColumns(e, xml);\n                break;\n            case \"headerReference\":\n                (section.headerRefs ?? (section.headerRefs = [])).push(parseFooterHeaderReference(e, xml));\n                break;\n            case \"footerReference\":\n                (section.footerRefs ?? (section.footerRefs = [])).push(parseFooterHeaderReference(e, xml));\n                break;\n            case \"titlePg\":\n                section.titlePage = xml.boolAttr(e, \"val\", true);\n                break;\n            case \"pgBorders\":\n                section.pageBorders = parseBorders(e, xml);\n                break;\n            case \"pgNumType\":\n                section.pageNumber = parsePageNumber(e, xml);\n                break;\n        }\n    }\n    return section;\n}\nfunction parseColumns(elem, xml) {\n    return {\n        numberOfColumns: xml.intAttr(elem, \"num\"),\n        space: xml.lengthAttr(elem, \"space\"),\n        separator: xml.boolAttr(elem, \"sep\"),\n        equalWidth: xml.boolAttr(elem, \"equalWidth\", true),\n        columns: xml.elements(elem, \"col\")\n            .map(e => ({\n            width: xml.lengthAttr(e, \"w\"),\n            space: xml.lengthAttr(e, \"space\")\n        }))\n    };\n}\nfunction parsePageNumber(elem, xml) {\n    return {\n        chapSep: xml.attr(elem, \"chapSep\"),\n        chapStyle: xml.attr(elem, \"chapStyle\"),\n        format: xml.attr(elem, \"fmt\"),\n        start: xml.intAttr(elem, \"start\")\n    };\n}\nfunction parseFooterHeaderReference(elem, xml) {\n    return {\n        id: xml.attr(elem, \"id\"),\n        type: xml.attr(elem, \"type\"),\n    };\n}\n\nfunction parseLineSpacing(elem, xml) {\n    return {\n        before: xml.lengthAttr(elem, \"before\"),\n        after: xml.lengthAttr(elem, \"after\"),\n        line: xml.intAttr(elem, \"line\"),\n        lineRule: xml.attr(elem, \"lineRule\")\n    };\n}\n\nfunction parseRunProperties(elem, xml) {\n    let result = {};\n    for (let el of xml.elements(elem)) {\n        parseRunProperty(el, result, xml);\n    }\n    return result;\n}\nfunction parseRunProperty(elem, props, xml) {\n    if (parseCommonProperty(elem, props, xml))\n        return true;\n    return false;\n}\n\nfunction parseParagraphProperties(elem, xml) {\n    let result = {};\n    for (let el of xml.elements(elem)) {\n        parseParagraphProperty(el, result, xml);\n    }\n    return result;\n}\nfunction parseParagraphProperty(elem, props, xml) {\n    if (elem.namespaceURI != ns$1.wordml)\n        return false;\n    if (parseCommonProperty(elem, props, xml))\n        return true;\n    switch (elem.localName) {\n        case \"tabs\":\n            props.tabs = parseTabs(elem, xml);\n            break;\n        case \"sectPr\":\n            props.sectionProps = parseSectionProperties(elem, xml);\n            break;\n        case \"numPr\":\n            props.numbering = parseNumbering$1(elem, xml);\n            break;\n        case \"spacing\":\n            props.lineSpacing = parseLineSpacing(elem, xml);\n            return false;\n        case \"textAlignment\":\n            props.textAlignment = xml.attr(elem, \"val\");\n            return false;\n        case \"keepLines\":\n            props.keepLines = xml.boolAttr(elem, \"val\", true);\n            break;\n        case \"keepNext\":\n            props.keepNext = xml.boolAttr(elem, \"val\", true);\n            break;\n        case \"pageBreakBefore\":\n            props.pageBreakBefore = xml.boolAttr(elem, \"val\", true);\n            break;\n        case \"outlineLvl\":\n            props.outlineLevel = xml.intAttr(elem, \"val\");\n            break;\n        case \"pStyle\":\n            props.styleName = xml.attr(elem, \"val\");\n            break;\n        case \"rPr\":\n            props.runProps = parseRunProperties(elem, xml);\n            break;\n        default:\n            return false;\n    }\n    return true;\n}\nfunction parseTabs(elem, xml) {\n    return xml.elements(elem, \"tab\")\n        .map(e => ({\n        position: xml.lengthAttr(e, \"pos\"),\n        leader: xml.attr(e, \"leader\"),\n        style: xml.attr(e, \"val\")\n    }));\n}\nfunction parseNumbering$1(elem, xml) {\n    var result = {};\n    for (let e of xml.elements(elem)) {\n        switch (e.localName) {\n            case \"numId\":\n                result.id = xml.attr(e, \"val\");\n                break;\n            case \"ilvl\":\n                result.level = xml.intAttr(e, \"val\");\n                break;\n        }\n    }\n    return result;\n}\n\nfunction parseNumberingPart(elem, xml) {\n    let result = {\n        numberings: [],\n        abstractNumberings: [],\n        bulletPictures: []\n    };\n    for (let e of xml.elements(elem)) {\n        switch (e.localName) {\n            case \"num\":\n                result.numberings.push(parseNumbering(e, xml));\n                break;\n            case \"abstractNum\":\n                result.abstractNumberings.push(parseAbstractNumbering(e, xml));\n                break;\n            case \"numPicBullet\":\n                result.bulletPictures.push(parseNumberingBulletPicture(e, xml));\n                break;\n        }\n    }\n    return result;\n}\nfunction parseNumbering(elem, xml) {\n    let result = {\n        id: xml.attr(elem, 'numId'),\n        overrides: []\n    };\n    for (let e of xml.elements(elem)) {\n        switch (e.localName) {\n            case \"abstractNumId\":\n                result.abstractId = xml.attr(e, \"val\");\n                break;\n            case \"lvlOverride\":\n                result.overrides.push(parseNumberingLevelOverrride(e, xml));\n                break;\n        }\n    }\n    return result;\n}\nfunction parseAbstractNumbering(elem, xml) {\n    let result = {\n        id: xml.attr(elem, 'abstractNumId'),\n        levels: []\n    };\n    for (let e of xml.elements(elem)) {\n        switch (e.localName) {\n            case \"name\":\n                result.name = xml.attr(e, \"val\");\n                break;\n            case \"multiLevelType\":\n                result.multiLevelType = xml.attr(e, \"val\");\n                break;\n            case \"numStyleLink\":\n                result.numberingStyleLink = xml.attr(e, \"val\");\n                break;\n            case \"styleLink\":\n                result.styleLink = xml.attr(e, \"val\");\n                break;\n            case \"lvl\":\n                result.levels.push(parseNumberingLevel(e, xml));\n                break;\n        }\n    }\n    return result;\n}\nfunction parseNumberingLevel(elem, xml) {\n    let result = {\n        level: xml.intAttr(elem, 'ilvl')\n    };\n    for (let e of xml.elements(elem)) {\n        switch (e.localName) {\n            case \"start\":\n                result.start = xml.attr(e, \"val\");\n                break;\n            case \"lvlRestart\":\n                result.restart = xml.intAttr(e, \"val\");\n                break;\n            case \"numFmt\":\n                result.format = xml.attr(e, \"val\");\n                break;\n            case \"lvlText\":\n                result.text = xml.attr(e, \"val\");\n                break;\n            case \"lvlJc\":\n                result.justification = xml.attr(e, \"val\");\n                break;\n            case \"lvlPicBulletId\":\n                result.bulletPictureId = xml.attr(e, \"val\");\n                break;\n            case \"pStyle\":\n                result.paragraphStyle = xml.attr(e, \"val\");\n                break;\n            case \"pPr\":\n                result.paragraphProps = parseParagraphProperties(e, xml);\n                break;\n            case \"rPr\":\n                result.runProps = parseRunProperties(e, xml);\n                break;\n        }\n    }\n    return result;\n}\nfunction parseNumberingLevelOverrride(elem, xml) {\n    let result = {\n        level: xml.intAttr(elem, 'ilvl')\n    };\n    for (let e of xml.elements(elem)) {\n        switch (e.localName) {\n            case \"startOverride\":\n                result.start = xml.intAttr(e, \"val\");\n                break;\n            case \"lvl\":\n                result.numberingLevel = parseNumberingLevel(e, xml);\n                break;\n        }\n    }\n    return result;\n}\nfunction parseNumberingBulletPicture(elem, xml) {\n    var pict = xml.element(elem, \"pict\");\n    var shape = pict && xml.element(pict, \"shape\");\n    var imagedata = shape && xml.element(shape, \"imagedata\");\n    return imagedata ? {\n        id: xml.attr(elem, \"numPicBulletId\"),\n        referenceId: xml.attr(imagedata, \"id\"),\n        style: xml.attr(shape, \"style\")\n    } : null;\n}\n\nclass NumberingPart extends Part {\n    constructor(pkg, path, parser) {\n        super(pkg, path);\n        this._documentParser = parser;\n    }\n    parseXml(root) {\n        Object.assign(this, parseNumberingPart(root, this._package.xmlParser));\n        this.domNumberings = this._documentParser.parseNumberingFile(root);\n    }\n}\n\nclass StylesPart extends Part {\n    constructor(pkg, path, parser) {\n        super(pkg, path);\n        this._documentParser = parser;\n    }\n    parseXml(root) {\n        this.styles = this._documentParser.parseStylesFile(root);\n    }\n}\n\nvar DomType;\n(function (DomType) {\n    DomType[\"Document\"] = \"document\";\n    DomType[\"Paragraph\"] = \"paragraph\";\n    DomType[\"Run\"] = \"run\";\n    DomType[\"Break\"] = \"break\";\n    DomType[\"NoBreakHyphen\"] = \"noBreakHyphen\";\n    DomType[\"Table\"] = \"table\";\n    DomType[\"Row\"] = \"row\";\n    DomType[\"Cell\"] = \"cell\";\n    DomType[\"Hyperlink\"] = \"hyperlink\";\n    DomType[\"SmartTag\"] = \"smartTag\";\n    DomType[\"Drawing\"] = \"drawing\";\n    DomType[\"Image\"] = \"image\";\n    DomType[\"Text\"] = \"text\";\n    DomType[\"Tab\"] = \"tab\";\n    DomType[\"Symbol\"] = \"symbol\";\n    DomType[\"BookmarkStart\"] = \"bookmarkStart\";\n    DomType[\"BookmarkEnd\"] = \"bookmarkEnd\";\n    DomType[\"Footer\"] = \"footer\";\n    DomType[\"Header\"] = \"header\";\n    DomType[\"FootnoteReference\"] = \"footnoteReference\";\n    DomType[\"EndnoteReference\"] = \"endnoteReference\";\n    DomType[\"Footnote\"] = \"footnote\";\n    DomType[\"Endnote\"] = \"endnote\";\n    DomType[\"SimpleField\"] = \"simpleField\";\n    DomType[\"ComplexField\"] = \"complexField\";\n    DomType[\"Instruction\"] = \"instruction\";\n    DomType[\"VmlPicture\"] = \"vmlPicture\";\n    DomType[\"MmlMath\"] = \"mmlMath\";\n    DomType[\"MmlMathParagraph\"] = \"mmlMathParagraph\";\n    DomType[\"MmlFraction\"] = \"mmlFraction\";\n    DomType[\"MmlFunction\"] = \"mmlFunction\";\n    DomType[\"MmlFunctionName\"] = \"mmlFunctionName\";\n    DomType[\"MmlNumerator\"] = \"mmlNumerator\";\n    DomType[\"MmlDenominator\"] = \"mmlDenominator\";\n    DomType[\"MmlRadical\"] = \"mmlRadical\";\n    DomType[\"MmlBase\"] = \"mmlBase\";\n    DomType[\"MmlDegree\"] = \"mmlDegree\";\n    DomType[\"MmlSuperscript\"] = \"mmlSuperscript\";\n    DomType[\"MmlSubscript\"] = \"mmlSubscript\";\n    DomType[\"MmlPreSubSuper\"] = \"mmlPreSubSuper\";\n    DomType[\"MmlSubArgument\"] = \"mmlSubArgument\";\n    DomType[\"MmlSuperArgument\"] = \"mmlSuperArgument\";\n    DomType[\"MmlNary\"] = \"mmlNary\";\n    DomType[\"MmlDelimiter\"] = \"mmlDelimiter\";\n    DomType[\"MmlRun\"] = \"mmlRun\";\n    DomType[\"MmlEquationArray\"] = \"mmlEquationArray\";\n    DomType[\"MmlLimit\"] = \"mmlLimit\";\n    DomType[\"MmlLimitLower\"] = \"mmlLimitLower\";\n    DomType[\"MmlMatrix\"] = \"mmlMatrix\";\n    DomType[\"MmlMatrixRow\"] = \"mmlMatrixRow\";\n    DomType[\"MmlBox\"] = \"mmlBox\";\n    DomType[\"MmlBar\"] = \"mmlBar\";\n    DomType[\"MmlGroupChar\"] = \"mmlGroupChar\";\n    DomType[\"VmlElement\"] = \"vmlElement\";\n    DomType[\"Inserted\"] = \"inserted\";\n    DomType[\"Deleted\"] = \"deleted\";\n    DomType[\"DeletedText\"] = \"deletedText\";\n    DomType[\"Comment\"] = \"comment\";\n    DomType[\"CommentReference\"] = \"commentReference\";\n    DomType[\"CommentRangeStart\"] = \"commentRangeStart\";\n    DomType[\"CommentRangeEnd\"] = \"commentRangeEnd\";\n    DomType[\"AltChunk\"] = \"altChunk\";\n})(DomType || (DomType = {}));\nclass OpenXmlElementBase {\n    constructor() {\n        this.children = [];\n        this.cssStyle = {};\n    }\n}\n\nclass WmlHeader extends OpenXmlElementBase {\n    constructor() {\n        super(...arguments);\n        this.type = DomType.Header;\n    }\n}\nclass WmlFooter extends OpenXmlElementBase {\n    constructor() {\n        super(...arguments);\n        this.type = DomType.Footer;\n    }\n}\n\nclass BaseHeaderFooterPart extends Part {\n    constructor(pkg, path, parser) {\n        super(pkg, path);\n        this._documentParser = parser;\n    }\n    parseXml(root) {\n        this.rootElement = this.createRootElement();\n        this.rootElement.children = this._documentParser.parseBodyElements(root);\n    }\n}\nclass HeaderPart extends BaseHeaderFooterPart {\n    createRootElement() {\n        return new WmlHeader();\n    }\n}\nclass FooterPart extends BaseHeaderFooterPart {\n    createRootElement() {\n        return new WmlFooter();\n    }\n}\n\nfunction parseExtendedProps(root, xmlParser) {\n    const result = {};\n    for (let el of xmlParser.elements(root)) {\n        switch (el.localName) {\n            case \"Template\":\n                result.template = el.textContent;\n                break;\n            case \"Pages\":\n                result.pages = safeParseToInt(el.textContent);\n                break;\n            case \"Words\":\n                result.words = safeParseToInt(el.textContent);\n                break;\n            case \"Characters\":\n                result.characters = safeParseToInt(el.textContent);\n                break;\n            case \"Application\":\n                result.application = el.textContent;\n                break;\n            case \"Lines\":\n                result.lines = safeParseToInt(el.textContent);\n                break;\n            case \"Paragraphs\":\n                result.paragraphs = safeParseToInt(el.textContent);\n                break;\n            case \"Company\":\n                result.company = el.textContent;\n                break;\n            case \"AppVersion\":\n                result.appVersion = el.textContent;\n                break;\n        }\n    }\n    return result;\n}\nfunction safeParseToInt(value) {\n    if (typeof value === 'undefined')\n        return;\n    return parseInt(value);\n}\n\nclass ExtendedPropsPart extends Part {\n    parseXml(root) {\n        this.props = parseExtendedProps(root, this._package.xmlParser);\n    }\n}\n\nfunction parseCoreProps(root, xmlParser) {\n    const result = {};\n    for (let el of xmlParser.elements(root)) {\n        switch (el.localName) {\n            case \"title\":\n                result.title = el.textContent;\n                break;\n            case \"description\":\n                result.description = el.textContent;\n                break;\n            case \"subject\":\n                result.subject = el.textContent;\n                break;\n            case \"creator\":\n                result.creator = el.textContent;\n                break;\n            case \"keywords\":\n                result.keywords = el.textContent;\n                break;\n            case \"language\":\n                result.language = el.textContent;\n                break;\n            case \"lastModifiedBy\":\n                result.lastModifiedBy = el.textContent;\n                break;\n            case \"revision\":\n                el.textContent && (result.revision = parseInt(el.textContent));\n                break;\n        }\n    }\n    return result;\n}\n\nclass CorePropsPart extends Part {\n    parseXml(root) {\n        this.props = parseCoreProps(root, this._package.xmlParser);\n    }\n}\n\nclass DmlTheme {\n}\nfunction parseTheme(elem, xml) {\n    var result = new DmlTheme();\n    var themeElements = xml.element(elem, \"themeElements\");\n    for (let el of xml.elements(themeElements)) {\n        switch (el.localName) {\n            case \"clrScheme\":\n                result.colorScheme = parseColorScheme(el, xml);\n                break;\n            case \"fontScheme\":\n                result.fontScheme = parseFontScheme(el, xml);\n                break;\n        }\n    }\n    return result;\n}\nfunction parseColorScheme(elem, xml) {\n    var result = {\n        name: xml.attr(elem, \"name\"),\n        colors: {}\n    };\n    for (let el of xml.elements(elem)) {\n        var srgbClr = xml.element(el, \"srgbClr\");\n        var sysClr = xml.element(el, \"sysClr\");\n        if (srgbClr) {\n            result.colors[el.localName] = xml.attr(srgbClr, \"val\");\n        }\n        else if (sysClr) {\n            result.colors[el.localName] = xml.attr(sysClr, \"lastClr\");\n        }\n    }\n    return result;\n}\nfunction parseFontScheme(elem, xml) {\n    var result = {\n        name: xml.attr(elem, \"name\"),\n    };\n    for (let el of xml.elements(elem)) {\n        switch (el.localName) {\n            case \"majorFont\":\n                result.majorFont = parseFontInfo(el, xml);\n                break;\n            case \"minorFont\":\n                result.minorFont = parseFontInfo(el, xml);\n                break;\n        }\n    }\n    return result;\n}\nfunction parseFontInfo(elem, xml) {\n    return {\n        latinTypeface: xml.elementAttr(elem, \"latin\", \"typeface\"),\n        eaTypeface: xml.elementAttr(elem, \"ea\", \"typeface\"),\n        csTypeface: xml.elementAttr(elem, \"cs\", \"typeface\"),\n    };\n}\n\nclass ThemePart extends Part {\n    constructor(pkg, path) {\n        super(pkg, path);\n    }\n    parseXml(root) {\n        this.theme = parseTheme(root, this._package.xmlParser);\n    }\n}\n\nclass WmlBaseNote {\n}\nclass WmlFootnote extends WmlBaseNote {\n    constructor() {\n        super(...arguments);\n        this.type = DomType.Footnote;\n    }\n}\nclass WmlEndnote extends WmlBaseNote {\n    constructor() {\n        super(...arguments);\n        this.type = DomType.Endnote;\n    }\n}\n\nclass BaseNotePart extends Part {\n    constructor(pkg, path, parser) {\n        super(pkg, path);\n        this._documentParser = parser;\n    }\n}\nclass FootnotesPart extends BaseNotePart {\n    constructor(pkg, path, parser) {\n        super(pkg, path, parser);\n    }\n    parseXml(root) {\n        this.notes = this._documentParser.parseNotes(root, \"footnote\", WmlFootnote);\n    }\n}\nclass EndnotesPart extends BaseNotePart {\n    constructor(pkg, path, parser) {\n        super(pkg, path, parser);\n    }\n    parseXml(root) {\n        this.notes = this._documentParser.parseNotes(root, \"endnote\", WmlEndnote);\n    }\n}\n\nfunction parseSettings(elem, xml) {\n    var result = {};\n    for (let el of xml.elements(elem)) {\n        switch (el.localName) {\n            case \"defaultTabStop\":\n                result.defaultTabStop = xml.lengthAttr(el, \"val\");\n                break;\n            case \"footnotePr\":\n                result.footnoteProps = parseNoteProperties(el, xml);\n                break;\n            case \"endnotePr\":\n                result.endnoteProps = parseNoteProperties(el, xml);\n                break;\n            case \"autoHyphenation\":\n                result.autoHyphenation = xml.boolAttr(el, \"val\");\n                break;\n        }\n    }\n    return result;\n}\nfunction parseNoteProperties(elem, xml) {\n    var result = {\n        defaultNoteIds: []\n    };\n    for (let el of xml.elements(elem)) {\n        switch (el.localName) {\n            case \"numFmt\":\n                result.nummeringFormat = xml.attr(el, \"val\");\n                break;\n            case \"footnote\":\n            case \"endnote\":\n                result.defaultNoteIds.push(xml.attr(el, \"id\"));\n                break;\n        }\n    }\n    return result;\n}\n\nclass SettingsPart extends Part {\n    constructor(pkg, path) {\n        super(pkg, path);\n    }\n    parseXml(root) {\n        this.settings = parseSettings(root, this._package.xmlParser);\n    }\n}\n\nfunction parseCustomProps(root, xml) {\n    return xml.elements(root, \"property\").map(e => {\n        const firstChild = e.firstChild;\n        return {\n            formatId: xml.attr(e, \"fmtid\"),\n            name: xml.attr(e, \"name\"),\n            type: firstChild.nodeName,\n            value: firstChild.textContent\n        };\n    });\n}\n\nclass CustomPropsPart extends Part {\n    parseXml(root) {\n        this.props = parseCustomProps(root, this._package.xmlParser);\n    }\n}\n\nclass CommentsPart extends Part {\n    constructor(pkg, path, parser) {\n        super(pkg, path);\n        this._documentParser = parser;\n    }\n    parseXml(root) {\n        this.comments = this._documentParser.parseComments(root);\n        this.commentMap = keyBy(this.comments, x => x.id);\n    }\n}\n\nclass CommentsExtendedPart extends Part {\n    constructor(pkg, path) {\n        super(pkg, path);\n        this.comments = [];\n    }\n    parseXml(root) {\n        const xml = this._package.xmlParser;\n        for (let el of xml.elements(root, \"commentEx\")) {\n            this.comments.push({\n                paraId: xml.attr(el, 'paraId'),\n                paraIdParent: xml.attr(el, 'paraIdParent'),\n                done: xml.boolAttr(el, 'done')\n            });\n        }\n        this.commentMap = keyBy(this.comments, x => x.paraId);\n    }\n}\n\nconst topLevelRels = [\n    { type: RelationshipTypes.OfficeDocument, target: \"word/document.xml\" },\n    { type: RelationshipTypes.ExtendedProperties, target: \"docProps/app.xml\" },\n    { type: RelationshipTypes.CoreProperties, target: \"docProps/core.xml\" },\n    { type: RelationshipTypes.CustomProperties, target: \"docProps/custom.xml\" },\n];\nclass WordDocument {\n    constructor() {\n        this.parts = [];\n        this.partsMap = {};\n    }\n    static async load(blob, parser, options) {\n        var d = new WordDocument();\n        d._options = options;\n        d._parser = parser;\n        d._package = await OpenXmlPackage.load(blob, options);\n        d.rels = await d._package.loadRelationships();\n        await Promise.all(topLevelRels.map(rel => {\n            const r = d.rels.find(x => x.type === rel.type) ?? rel;\n            return d.loadRelationshipPart(r.target, r.type);\n        }));\n        return d;\n    }\n    save(type = \"blob\") {\n        return this._package.save(type);\n    }\n    async loadRelationshipPart(path, type) {\n        if (this.partsMap[path])\n            return this.partsMap[path];\n        if (!this._package.get(path))\n            return null;\n        let part = null;\n        switch (type) {\n            case RelationshipTypes.OfficeDocument:\n                this.documentPart = part = new DocumentPart(this._package, path, this._parser);\n                break;\n            case RelationshipTypes.FontTable:\n                this.fontTablePart = part = new FontTablePart(this._package, path);\n                break;\n            case RelationshipTypes.Numbering:\n                this.numberingPart = part = new NumberingPart(this._package, path, this._parser);\n                break;\n            case RelationshipTypes.Styles:\n                this.stylesPart = part = new StylesPart(this._package, path, this._parser);\n                break;\n            case RelationshipTypes.Theme:\n                this.themePart = part = new ThemePart(this._package, path);\n                break;\n            case RelationshipTypes.Footnotes:\n                this.footnotesPart = part = new FootnotesPart(this._package, path, this._parser);\n                break;\n            case RelationshipTypes.Endnotes:\n                this.endnotesPart = part = new EndnotesPart(this._package, path, this._parser);\n                break;\n            case RelationshipTypes.Footer:\n                part = new FooterPart(this._package, path, this._parser);\n                break;\n            case RelationshipTypes.Header:\n                part = new HeaderPart(this._package, path, this._parser);\n                break;\n            case RelationshipTypes.CoreProperties:\n                this.corePropsPart = part = new CorePropsPart(this._package, path);\n                break;\n            case RelationshipTypes.ExtendedProperties:\n                this.extendedPropsPart = part = new ExtendedPropsPart(this._package, path);\n                break;\n            case RelationshipTypes.CustomProperties:\n                part = new CustomPropsPart(this._package, path);\n                break;\n            case RelationshipTypes.Settings:\n                this.settingsPart = part = new SettingsPart(this._package, path);\n                break;\n            case RelationshipTypes.Comments:\n                this.commentsPart = part = new CommentsPart(this._package, path, this._parser);\n                break;\n            case RelationshipTypes.CommentsExtended:\n                this.commentsExtendedPart = part = new CommentsExtendedPart(this._package, path);\n                break;\n        }\n        if (part == null)\n            return Promise.resolve(null);\n        this.partsMap[path] = part;\n        this.parts.push(part);\n        await part.load();\n        if (part.rels?.length > 0) {\n            const [folder] = splitPath(part.path);\n            await Promise.all(part.rels.map(rel => this.loadRelationshipPart(resolvePath(rel.target, folder), rel.type)));\n        }\n        return part;\n    }\n    async loadDocumentImage(id, part) {\n        const x = await this.loadResource(part ?? this.documentPart, id, \"blob\");\n        return this.blobToURL(x);\n    }\n    async loadNumberingImage(id) {\n        const x = await this.loadResource(this.numberingPart, id, \"blob\");\n        return this.blobToURL(x);\n    }\n    async loadFont(id, key) {\n        const x = await this.loadResource(this.fontTablePart, id, \"uint8array\");\n        return x ? this.blobToURL(new Blob([deobfuscate(x, key)])) : x;\n    }\n    async loadAltChunk(id, part) {\n        return await this.loadResource(part ?? this.documentPart, id, \"string\");\n    }\n    blobToURL(blob) {\n        if (!blob)\n            return null;\n        if (this._options.useBase64URL) {\n            return blobToBase64(blob);\n        }\n        return URL.createObjectURL(blob);\n    }\n    findPartByRelId(id, basePart = null) {\n        var rel = (basePart.rels ?? this.rels).find(r => r.id == id);\n        const folder = basePart ? splitPath(basePart.path)[0] : '';\n        return rel ? this.partsMap[resolvePath(rel.target, folder)] : null;\n    }\n    getPathById(part, id) {\n        const rel = part.rels.find(x => x.id == id);\n        const [folder] = splitPath(part.path);\n        return rel ? resolvePath(rel.target, folder) : null;\n    }\n    loadResource(part, id, outputType) {\n        const path = this.getPathById(part, id);\n        return path ? this._package.load(path, outputType) : Promise.resolve(null);\n    }\n}\nfunction deobfuscate(data, guidKey) {\n    const len = 16;\n    const trimmed = guidKey.replace(/{|}|-/g, \"\");\n    const numbers = new Array(len);\n    for (let i = 0; i < len; i++)\n        numbers[len - i - 1] = parseInt(trimmed.substring(i * 2, i * 2 + 2), 16);\n    for (let i = 0; i < 32; i++)\n        data[i] = data[i] ^ numbers[i % len];\n    return data;\n}\n\nfunction parseBookmarkStart(elem, xml) {\n    return {\n        type: DomType.BookmarkStart,\n        id: xml.attr(elem, \"id\"),\n        name: xml.attr(elem, \"name\"),\n        colFirst: xml.intAttr(elem, \"colFirst\"),\n        colLast: xml.intAttr(elem, \"colLast\")\n    };\n}\nfunction parseBookmarkEnd(elem, xml) {\n    return {\n        type: DomType.BookmarkEnd,\n        id: xml.attr(elem, \"id\")\n    };\n}\n\nclass VmlElement extends OpenXmlElementBase {\n    constructor() {\n        super(...arguments);\n        this.type = DomType.VmlElement;\n        this.attrs = {};\n    }\n}\nfunction parseVmlElement(elem, parser) {\n    var result = new VmlElement();\n    switch (elem.localName) {\n        case \"rect\":\n            result.tagName = \"rect\";\n            Object.assign(result.attrs, { width: '100%', height: '100%' });\n            break;\n        case \"oval\":\n            result.tagName = \"ellipse\";\n            Object.assign(result.attrs, { cx: \"50%\", cy: \"50%\", rx: \"50%\", ry: \"50%\" });\n            break;\n        case \"line\":\n            result.tagName = \"line\";\n            break;\n        case \"shape\":\n            result.tagName = \"g\";\n            break;\n        case \"textbox\":\n            result.tagName = \"foreignObject\";\n            Object.assign(result.attrs, { width: '100%', height: '100%' });\n            break;\n        default:\n            return null;\n    }\n    for (const at of globalXmlParser.attrs(elem)) {\n        switch (at.localName) {\n            case \"style\":\n                result.cssStyleText = at.value;\n                break;\n            case \"fillcolor\":\n                result.attrs.fill = at.value;\n                break;\n            case \"from\":\n                const [x1, y1] = parsePoint(at.value);\n                Object.assign(result.attrs, { x1, y1 });\n                break;\n            case \"to\":\n                const [x2, y2] = parsePoint(at.value);\n                Object.assign(result.attrs, { x2, y2 });\n                break;\n        }\n    }\n    for (const el of globalXmlParser.elements(elem)) {\n        switch (el.localName) {\n            case \"stroke\":\n                Object.assign(result.attrs, parseStroke(el));\n                break;\n            case \"fill\":\n                Object.assign(result.attrs, parseFill());\n                break;\n            case \"imagedata\":\n                result.tagName = \"image\";\n                Object.assign(result.attrs, { width: '100%', height: '100%' });\n                result.imageHref = {\n                    id: globalXmlParser.attr(el, \"id\"),\n                    title: globalXmlParser.attr(el, \"title\"),\n                };\n                break;\n            case \"txbxContent\":\n                result.children.push(...parser.parseBodyElements(el));\n                break;\n            default:\n                const child = parseVmlElement(el, parser);\n                child && result.children.push(child);\n                break;\n        }\n    }\n    return result;\n}\nfunction parseStroke(el) {\n    return {\n        'stroke': globalXmlParser.attr(el, \"color\"),\n        'stroke-width': globalXmlParser.lengthAttr(el, \"weight\", LengthUsage.Emu) ?? '1px'\n    };\n}\nfunction parseFill(el) {\n    return {};\n}\nfunction parsePoint(val) {\n    return val.split(\",\");\n}\n\nclass WmlComment extends OpenXmlElementBase {\n    constructor() {\n        super(...arguments);\n        this.type = DomType.Comment;\n    }\n}\nclass WmlCommentReference extends OpenXmlElementBase {\n    constructor(id) {\n        super();\n        this.id = id;\n        this.type = DomType.CommentReference;\n    }\n}\nclass WmlCommentRangeStart extends OpenXmlElementBase {\n    constructor(id) {\n        super();\n        this.id = id;\n        this.type = DomType.CommentRangeStart;\n    }\n}\nclass WmlCommentRangeEnd extends OpenXmlElementBase {\n    constructor(id) {\n        super();\n        this.id = id;\n        this.type = DomType.CommentRangeEnd;\n    }\n}\n\nvar autos = {\n    shd: \"inherit\",\n    color: \"black\",\n    borderColor: \"black\",\n    highlight: \"transparent\"\n};\nconst supportedNamespaceURIs = [];\nconst mmlTagMap = {\n    \"oMath\": DomType.MmlMath,\n    \"oMathPara\": DomType.MmlMathParagraph,\n    \"f\": DomType.MmlFraction,\n    \"func\": DomType.MmlFunction,\n    \"fName\": DomType.MmlFunctionName,\n    \"num\": DomType.MmlNumerator,\n    \"den\": DomType.MmlDenominator,\n    \"rad\": DomType.MmlRadical,\n    \"deg\": DomType.MmlDegree,\n    \"e\": DomType.MmlBase,\n    \"sSup\": DomType.MmlSuperscript,\n    \"sSub\": DomType.MmlSubscript,\n    \"sPre\": DomType.MmlPreSubSuper,\n    \"sup\": DomType.MmlSuperArgument,\n    \"sub\": DomType.MmlSubArgument,\n    \"d\": DomType.MmlDelimiter,\n    \"nary\": DomType.MmlNary,\n    \"eqArr\": DomType.MmlEquationArray,\n    \"lim\": DomType.MmlLimit,\n    \"limLow\": DomType.MmlLimitLower,\n    \"m\": DomType.MmlMatrix,\n    \"mr\": DomType.MmlMatrixRow,\n    \"box\": DomType.MmlBox,\n    \"bar\": DomType.MmlBar,\n    \"groupChr\": DomType.MmlGroupChar\n};\nclass DocumentParser {\n    constructor(options) {\n        this.options = {\n            ignoreWidth: false,\n            debug: false,\n            ...options\n        };\n    }\n    parseNotes(xmlDoc, elemName, elemClass) {\n        var result = [];\n        for (let el of globalXmlParser.elements(xmlDoc, elemName)) {\n            const node = new elemClass();\n            node.id = globalXmlParser.attr(el, \"id\");\n            node.noteType = globalXmlParser.attr(el, \"type\");\n            node.children = this.parseBodyElements(el);\n            result.push(node);\n        }\n        return result;\n    }\n    parseComments(xmlDoc) {\n        var result = [];\n        for (let el of globalXmlParser.elements(xmlDoc, \"comment\")) {\n            const item = new WmlComment();\n            item.id = globalXmlParser.attr(el, \"id\");\n            item.author = globalXmlParser.attr(el, \"author\");\n            item.initials = globalXmlParser.attr(el, \"initials\");\n            item.date = globalXmlParser.attr(el, \"date\");\n            item.children = this.parseBodyElements(el);\n            result.push(item);\n        }\n        return result;\n    }\n    parseDocumentFile(xmlDoc) {\n        var xbody = globalXmlParser.element(xmlDoc, \"body\");\n        var background = globalXmlParser.element(xmlDoc, \"background\");\n        var sectPr = globalXmlParser.element(xbody, \"sectPr\");\n        return {\n            type: DomType.Document,\n            children: this.parseBodyElements(xbody),\n            props: sectPr ? parseSectionProperties(sectPr, globalXmlParser) : {},\n            cssStyle: background ? this.parseBackground(background) : {},\n        };\n    }\n    parseBackground(elem) {\n        var result = {};\n        var color = xmlUtil.colorAttr(elem, \"color\");\n        if (color) {\n            result[\"background-color\"] = color;\n        }\n        return result;\n    }\n    parseBodyElements(element) {\n        var children = [];\n        for (const elem of globalXmlParser.elements(element)) {\n            switch (elem.localName) {\n                case \"p\":\n                    children.push(this.parseParagraph(elem));\n                    break;\n                case \"altChunk\":\n                    children.push(this.parseAltChunk(elem));\n                    break;\n                case \"tbl\":\n                    children.push(this.parseTable(elem));\n                    break;\n                case \"sdt\":\n                    children.push(...this.parseSdt(elem, e => this.parseBodyElements(e)));\n                    break;\n            }\n        }\n        return children;\n    }\n    parseStylesFile(xstyles) {\n        var result = [];\n        for (const n of globalXmlParser.elements(xstyles)) {\n            switch (n.localName) {\n                case \"style\":\n                    result.push(this.parseStyle(n));\n                    break;\n                case \"docDefaults\":\n                    result.push(this.parseDefaultStyles(n));\n                    break;\n            }\n        }\n        return result;\n    }\n    parseDefaultStyles(node) {\n        var result = {\n            id: null,\n            name: null,\n            target: null,\n            basedOn: null,\n            styles: []\n        };\n        for (const c of globalXmlParser.elements(node)) {\n            switch (c.localName) {\n                case \"rPrDefault\":\n                    var rPr = globalXmlParser.element(c, \"rPr\");\n                    if (rPr)\n                        result.styles.push({\n                            target: \"span\",\n                            values: this.parseDefaultProperties(rPr, {})\n                        });\n                    break;\n                case \"pPrDefault\":\n                    var pPr = globalXmlParser.element(c, \"pPr\");\n                    if (pPr)\n                        result.styles.push({\n                            target: \"p\",\n                            values: this.parseDefaultProperties(pPr, {})\n                        });\n                    break;\n            }\n        }\n        return result;\n    }\n    parseStyle(node) {\n        var result = {\n            id: globalXmlParser.attr(node, \"styleId\"),\n            isDefault: globalXmlParser.boolAttr(node, \"default\"),\n            name: null,\n            target: null,\n            basedOn: null,\n            styles: [],\n            linked: null\n        };\n        switch (globalXmlParser.attr(node, \"type\")) {\n            case \"paragraph\":\n                result.target = \"p\";\n                break;\n            case \"table\":\n                result.target = \"table\";\n                break;\n            case \"character\":\n                result.target = \"span\";\n                break;\n        }\n        for (const n of globalXmlParser.elements(node)) {\n            switch (n.localName) {\n                case \"basedOn\":\n                    result.basedOn = globalXmlParser.attr(n, \"val\");\n                    break;\n                case \"name\":\n                    result.name = globalXmlParser.attr(n, \"val\");\n                    break;\n                case \"link\":\n                    result.linked = globalXmlParser.attr(n, \"val\");\n                    break;\n                case \"next\":\n                    result.next = globalXmlParser.attr(n, \"val\");\n                    break;\n                case \"aliases\":\n                    result.aliases = globalXmlParser.attr(n, \"val\").split(\",\");\n                    break;\n                case \"pPr\":\n                    result.styles.push({\n                        target: \"p\",\n                        values: this.parseDefaultProperties(n, {})\n                    });\n                    result.paragraphProps = parseParagraphProperties(n, globalXmlParser);\n                    break;\n                case \"rPr\":\n                    result.styles.push({\n                        target: \"span\",\n                        values: this.parseDefaultProperties(n, {})\n                    });\n                    result.runProps = parseRunProperties(n, globalXmlParser);\n                    break;\n                case \"tblPr\":\n                case \"tcPr\":\n                    result.styles.push({\n                        target: \"td\",\n                        values: this.parseDefaultProperties(n, {})\n                    });\n                    break;\n                case \"tblStylePr\":\n                    for (let s of this.parseTableStyle(n))\n                        result.styles.push(s);\n                    break;\n                case \"rsid\":\n                case \"qFormat\":\n                case \"hidden\":\n                case \"semiHidden\":\n                case \"unhideWhenUsed\":\n                case \"autoRedefine\":\n                case \"uiPriority\":\n                    break;\n                default:\n                    this.options.debug && console.warn(`DOCX: Unknown style element: ${n.localName}`);\n            }\n        }\n        return result;\n    }\n    parseTableStyle(node) {\n        var result = [];\n        var type = globalXmlParser.attr(node, \"type\");\n        var selector = \"\";\n        var modificator = \"\";\n        switch (type) {\n            case \"firstRow\":\n                modificator = \".first-row\";\n                selector = \"tr.first-row td\";\n                break;\n            case \"lastRow\":\n                modificator = \".last-row\";\n                selector = \"tr.last-row td\";\n                break;\n            case \"firstCol\":\n                modificator = \".first-col\";\n                selector = \"td.first-col\";\n                break;\n            case \"lastCol\":\n                modificator = \".last-col\";\n                selector = \"td.last-col\";\n                break;\n            case \"band1Vert\":\n                modificator = \":not(.no-vband)\";\n                selector = \"td.odd-col\";\n                break;\n            case \"band2Vert\":\n                modificator = \":not(.no-vband)\";\n                selector = \"td.even-col\";\n                break;\n            case \"band1Horz\":\n                modificator = \":not(.no-hband)\";\n                selector = \"tr.odd-row\";\n                break;\n            case \"band2Horz\":\n                modificator = \":not(.no-hband)\";\n                selector = \"tr.even-row\";\n                break;\n            default: return [];\n        }\n        for (const n of globalXmlParser.elements(node)) {\n            switch (n.localName) {\n                case \"pPr\":\n                    result.push({\n                        target: `${selector} p`,\n                        mod: modificator,\n                        values: this.parseDefaultProperties(n, {})\n                    });\n                    break;\n                case \"rPr\":\n                    result.push({\n                        target: `${selector} span`,\n                        mod: modificator,\n                        values: this.parseDefaultProperties(n, {})\n                    });\n                    break;\n                case \"tblPr\":\n                case \"tcPr\":\n                    result.push({\n                        target: selector,\n                        mod: modificator,\n                        values: this.parseDefaultProperties(n, {})\n                    });\n                    break;\n            }\n        }\n        return result;\n    }\n    parseNumberingFile(node) {\n        var result = [];\n        var mapping = {};\n        var bullets = [];\n        for (const n of globalXmlParser.elements(node)) {\n            switch (n.localName) {\n                case \"abstractNum\":\n                    this.parseAbstractNumbering(n, bullets)\n                        .forEach(x => result.push(x));\n                    break;\n                case \"numPicBullet\":\n                    bullets.push(this.parseNumberingPicBullet(n));\n                    break;\n                case \"num\":\n                    var numId = globalXmlParser.attr(n, \"numId\");\n                    var abstractNumId = globalXmlParser.elementAttr(n, \"abstractNumId\", \"val\");\n                    mapping[abstractNumId] = numId;\n                    break;\n            }\n        }\n        result.forEach(x => x.id = mapping[x.id]);\n        return result;\n    }\n    parseNumberingPicBullet(elem) {\n        var pict = globalXmlParser.element(elem, \"pict\");\n        var shape = pict && globalXmlParser.element(pict, \"shape\");\n        var imagedata = shape && globalXmlParser.element(shape, \"imagedata\");\n        return imagedata ? {\n            id: globalXmlParser.intAttr(elem, \"numPicBulletId\"),\n            src: globalXmlParser.attr(imagedata, \"id\"),\n            style: globalXmlParser.attr(shape, \"style\")\n        } : null;\n    }\n    parseAbstractNumbering(node, bullets) {\n        var result = [];\n        var id = globalXmlParser.attr(node, \"abstractNumId\");\n        for (const n of globalXmlParser.elements(node)) {\n            switch (n.localName) {\n                case \"lvl\":\n                    result.push(this.parseNumberingLevel(id, n, bullets));\n                    break;\n            }\n        }\n        return result;\n    }\n    parseNumberingLevel(id, node, bullets) {\n        var result = {\n            id: id,\n            level: globalXmlParser.intAttr(node, \"ilvl\"),\n            start: 1,\n            pStyleName: undefined,\n            pStyle: {},\n            rStyle: {},\n            suff: \"tab\"\n        };\n        for (const n of globalXmlParser.elements(node)) {\n            switch (n.localName) {\n                case \"start\":\n                    result.start = globalXmlParser.intAttr(n, \"val\");\n                    break;\n                case \"pPr\":\n                    this.parseDefaultProperties(n, result.pStyle);\n                    break;\n                case \"rPr\":\n                    this.parseDefaultProperties(n, result.rStyle);\n                    break;\n                case \"lvlPicBulletId\":\n                    var bulletId = globalXmlParser.intAttr(n, \"val\");\n                    result.bullet = bullets.find(x => x?.id == bulletId);\n                    break;\n                case \"lvlText\":\n                    result.levelText = globalXmlParser.attr(n, \"val\");\n                    break;\n                case \"pStyle\":\n                    result.pStyleName = globalXmlParser.attr(n, \"val\");\n                    break;\n                case \"numFmt\":\n                    result.format = globalXmlParser.attr(n, \"val\");\n                    break;\n                case \"suff\":\n                    result.suff = globalXmlParser.attr(n, \"val\");\n                    break;\n            }\n        }\n        return result;\n    }\n    parseSdt(node, parser) {\n        const sdtContent = globalXmlParser.element(node, \"sdtContent\");\n        return sdtContent ? parser(sdtContent) : [];\n    }\n    parseInserted(node, parentParser) {\n        return {\n            type: DomType.Inserted,\n            children: parentParser(node)?.children ?? []\n        };\n    }\n    parseDeleted(node, parentParser) {\n        return {\n            type: DomType.Deleted,\n            children: parentParser(node)?.children ?? []\n        };\n    }\n    parseAltChunk(node) {\n        return { type: DomType.AltChunk, children: [], id: globalXmlParser.attr(node, \"id\") };\n    }\n    parseParagraph(node) {\n        var result = { type: DomType.Paragraph, children: [] };\n        for (let el of globalXmlParser.elements(node)) {\n            switch (el.localName) {\n                case \"pPr\":\n                    this.parseParagraphProperties(el, result);\n                    break;\n                case \"r\":\n                    result.children.push(this.parseRun(el, result));\n                    break;\n                case \"hyperlink\":\n                    result.children.push(this.parseHyperlink(el, result));\n                    break;\n                case \"smartTag\":\n                    result.children.push(this.parseSmartTag(el, result));\n                    break;\n                case \"bookmarkStart\":\n                    result.children.push(parseBookmarkStart(el, globalXmlParser));\n                    break;\n                case \"bookmarkEnd\":\n                    result.children.push(parseBookmarkEnd(el, globalXmlParser));\n                    break;\n                case \"commentRangeStart\":\n                    result.children.push(new WmlCommentRangeStart(globalXmlParser.attr(el, \"id\")));\n                    break;\n                case \"commentRangeEnd\":\n                    result.children.push(new WmlCommentRangeEnd(globalXmlParser.attr(el, \"id\")));\n                    break;\n                case \"oMath\":\n                case \"oMathPara\":\n                    result.children.push(this.parseMathElement(el));\n                    break;\n                case \"sdt\":\n                    result.children.push(...this.parseSdt(el, e => this.parseParagraph(e).children));\n                    break;\n                case \"ins\":\n                    result.children.push(this.parseInserted(el, e => this.parseParagraph(e)));\n                    break;\n                case \"del\":\n                    result.children.push(this.parseDeleted(el, e => this.parseParagraph(e)));\n                    break;\n            }\n        }\n        return result;\n    }\n    parseParagraphProperties(elem, paragraph) {\n        this.parseDefaultProperties(elem, paragraph.cssStyle = {}, null, c => {\n            if (parseParagraphProperty(c, paragraph, globalXmlParser))\n                return true;\n            switch (c.localName) {\n                case \"pStyle\":\n                    paragraph.styleName = globalXmlParser.attr(c, \"val\");\n                    break;\n                case \"cnfStyle\":\n                    paragraph.className = values.classNameOfCnfStyle(c);\n                    break;\n                case \"framePr\":\n                    this.parseFrame(c, paragraph);\n                    break;\n                case \"rPr\":\n                    break;\n                default:\n                    return false;\n            }\n            return true;\n        });\n    }\n    parseFrame(node, paragraph) {\n        var dropCap = globalXmlParser.attr(node, \"dropCap\");\n        if (dropCap == \"drop\")\n            paragraph.cssStyle[\"float\"] = \"left\";\n    }\n    parseHyperlink(node, parent) {\n        var result = { type: DomType.Hyperlink, parent: parent, children: [] };\n        result.anchor = globalXmlParser.attr(node, \"anchor\");\n        result.id = globalXmlParser.attr(node, \"id\");\n        for (const c of globalXmlParser.elements(node)) {\n            switch (c.localName) {\n                case \"r\":\n                    result.children.push(this.parseRun(c, result));\n                    break;\n            }\n        }\n        return result;\n    }\n    parseSmartTag(node, parent) {\n        var result = { type: DomType.SmartTag, parent, children: [] };\n        var uri = globalXmlParser.attr(node, \"uri\");\n        var element = globalXmlParser.attr(node, \"element\");\n        if (uri)\n            result.uri = uri;\n        if (element)\n            result.element = element;\n        for (const c of globalXmlParser.elements(node)) {\n            switch (c.localName) {\n                case \"r\":\n                    result.children.push(this.parseRun(c, result));\n                    break;\n            }\n        }\n        return result;\n    }\n    parseRun(node, parent) {\n        var result = { type: DomType.Run, parent: parent, children: [] };\n        for (let c of globalXmlParser.elements(node)) {\n            c = this.checkAlternateContent(c);\n            switch (c.localName) {\n                case \"t\":\n                    result.children.push({\n                        type: DomType.Text,\n                        text: c.textContent\n                    });\n                    break;\n                case \"delText\":\n                    result.children.push({\n                        type: DomType.DeletedText,\n                        text: c.textContent\n                    });\n                    break;\n                case \"commentReference\":\n                    result.children.push(new WmlCommentReference(globalXmlParser.attr(c, \"id\")));\n                    break;\n                case \"fldSimple\":\n                    result.children.push({\n                        type: DomType.SimpleField,\n                        instruction: globalXmlParser.attr(c, \"instr\"),\n                        lock: globalXmlParser.boolAttr(c, \"lock\", false),\n                        dirty: globalXmlParser.boolAttr(c, \"dirty\", false)\n                    });\n                    break;\n                case \"instrText\":\n                    result.fieldRun = true;\n                    result.children.push({\n                        type: DomType.Instruction,\n                        text: c.textContent\n                    });\n                    break;\n                case \"fldChar\":\n                    result.fieldRun = true;\n                    result.children.push({\n                        type: DomType.ComplexField,\n                        charType: globalXmlParser.attr(c, \"fldCharType\"),\n                        lock: globalXmlParser.boolAttr(c, \"lock\", false),\n                        dirty: globalXmlParser.boolAttr(c, \"dirty\", false)\n                    });\n                    break;\n                case \"noBreakHyphen\":\n                    result.children.push({ type: DomType.NoBreakHyphen });\n                    break;\n                case \"br\":\n                    result.children.push({\n                        type: DomType.Break,\n                        break: globalXmlParser.attr(c, \"type\") || \"textWrapping\"\n                    });\n                    break;\n                case \"lastRenderedPageBreak\":\n                    result.children.push({\n                        type: DomType.Break,\n                        break: \"lastRenderedPageBreak\"\n                    });\n                    break;\n                case \"sym\":\n                    result.children.push({\n                        type: DomType.Symbol,\n                        font: encloseFontFamily(globalXmlParser.attr(c, \"font\")),\n                        char: globalXmlParser.attr(c, \"char\")\n                    });\n                    break;\n                case \"tab\":\n                    result.children.push({ type: DomType.Tab });\n                    break;\n                case \"footnoteReference\":\n                    result.children.push({\n                        type: DomType.FootnoteReference,\n                        id: globalXmlParser.attr(c, \"id\")\n                    });\n                    break;\n                case \"endnoteReference\":\n                    result.children.push({\n                        type: DomType.EndnoteReference,\n                        id: globalXmlParser.attr(c, \"id\")\n                    });\n                    break;\n                case \"drawing\":\n                    let d = this.parseDrawing(c);\n                    if (d)\n                        result.children = [d];\n                    break;\n                case \"pict\":\n                    result.children.push(this.parseVmlPicture(c));\n                    break;\n                case \"rPr\":\n                    this.parseRunProperties(c, result);\n                    break;\n            }\n        }\n        return result;\n    }\n    parseMathElement(elem) {\n        const propsTag = `${elem.localName}Pr`;\n        const result = { type: mmlTagMap[elem.localName], children: [] };\n        for (const el of globalXmlParser.elements(elem)) {\n            const childType = mmlTagMap[el.localName];\n            if (childType) {\n                result.children.push(this.parseMathElement(el));\n            }\n            else if (el.localName == \"r\") {\n                var run = this.parseRun(el);\n                run.type = DomType.MmlRun;\n                result.children.push(run);\n            }\n            else if (el.localName == propsTag) {\n                result.props = this.parseMathProperies(el);\n            }\n        }\n        return result;\n    }\n    parseMathProperies(elem) {\n        const result = {};\n        for (const el of globalXmlParser.elements(elem)) {\n            switch (el.localName) {\n                case \"chr\":\n                    result.char = globalXmlParser.attr(el, \"val\");\n                    break;\n                case \"vertJc\":\n                    result.verticalJustification = globalXmlParser.attr(el, \"val\");\n                    break;\n                case \"pos\":\n                    result.position = globalXmlParser.attr(el, \"val\");\n                    break;\n                case \"degHide\":\n                    result.hideDegree = globalXmlParser.boolAttr(el, \"val\");\n                    break;\n                case \"begChr\":\n                    result.beginChar = globalXmlParser.attr(el, \"val\");\n                    break;\n                case \"endChr\":\n                    result.endChar = globalXmlParser.attr(el, \"val\");\n                    break;\n            }\n        }\n        return result;\n    }\n    parseRunProperties(elem, run) {\n        this.parseDefaultProperties(elem, run.cssStyle = {}, null, c => {\n            switch (c.localName) {\n                case \"rStyle\":\n                    run.styleName = globalXmlParser.attr(c, \"val\");\n                    break;\n                case \"vertAlign\":\n                    run.verticalAlign = values.valueOfVertAlign(c, true);\n                    break;\n                default:\n                    return false;\n            }\n            return true;\n        });\n    }\n    parseVmlPicture(elem) {\n        const result = { type: DomType.VmlPicture, children: [] };\n        for (const el of globalXmlParser.elements(elem)) {\n            const child = parseVmlElement(el, this);\n            child && result.children.push(child);\n        }\n        return result;\n    }\n    checkAlternateContent(elem) {\n        if (elem.localName != 'AlternateContent')\n            return elem;\n        var choice = globalXmlParser.element(elem, \"Choice\");\n        if (choice) {\n            var requires = globalXmlParser.attr(choice, \"Requires\");\n            var namespaceURI = elem.lookupNamespaceURI(requires);\n            if (supportedNamespaceURIs.includes(namespaceURI))\n                return choice.firstElementChild;\n        }\n        return globalXmlParser.element(elem, \"Fallback\")?.firstElementChild;\n    }\n    parseDrawing(node) {\n        for (var n of globalXmlParser.elements(node)) {\n            switch (n.localName) {\n                case \"inline\":\n                case \"anchor\":\n                    return this.parseDrawingWrapper(n);\n            }\n        }\n    }\n    parseDrawingWrapper(node) {\n        var result = { type: DomType.Drawing, children: [], cssStyle: {} };\n        var isAnchor = node.localName == \"anchor\";\n        let wrapType = null;\n        let simplePos = globalXmlParser.boolAttr(node, \"simplePos\");\n        globalXmlParser.boolAttr(node, \"behindDoc\");\n        let posX = { relative: \"page\", align: \"left\", offset: \"0\" };\n        let posY = { relative: \"page\", align: \"top\", offset: \"0\" };\n        for (var n of globalXmlParser.elements(node)) {\n            switch (n.localName) {\n                case \"simplePos\":\n                    if (simplePos) {\n                        posX.offset = globalXmlParser.lengthAttr(n, \"x\", LengthUsage.Emu);\n                        posY.offset = globalXmlParser.lengthAttr(n, \"y\", LengthUsage.Emu);\n                    }\n                    break;\n                case \"extent\":\n                    result.cssStyle[\"width\"] = globalXmlParser.lengthAttr(n, \"cx\", LengthUsage.Emu);\n                    result.cssStyle[\"height\"] = globalXmlParser.lengthAttr(n, \"cy\", LengthUsage.Emu);\n                    break;\n                case \"positionH\":\n                case \"positionV\":\n                    if (!simplePos) {\n                        let pos = n.localName == \"positionH\" ? posX : posY;\n                        var alignNode = globalXmlParser.element(n, \"align\");\n                        var offsetNode = globalXmlParser.element(n, \"posOffset\");\n                        pos.relative = globalXmlParser.attr(n, \"relativeFrom\") ?? pos.relative;\n                        if (alignNode)\n                            pos.align = alignNode.textContent;\n                        if (offsetNode)\n                            pos.offset = convertLength(offsetNode.textContent, LengthUsage.Emu);\n                    }\n                    break;\n                case \"wrapTopAndBottom\":\n                    wrapType = \"wrapTopAndBottom\";\n                    break;\n                case \"wrapNone\":\n                    wrapType = \"wrapNone\";\n                    break;\n                case \"graphic\":\n                    var g = this.parseGraphic(n);\n                    if (g)\n                        result.children.push(g);\n                    break;\n            }\n        }\n        if (wrapType == \"wrapTopAndBottom\") {\n            result.cssStyle['display'] = 'block';\n            if (posX.align) {\n                result.cssStyle['text-align'] = posX.align;\n                result.cssStyle['width'] = \"100%\";\n            }\n        }\n        else if (wrapType == \"wrapNone\") {\n            result.cssStyle['display'] = 'block';\n            result.cssStyle['position'] = 'relative';\n            result.cssStyle[\"width\"] = \"0px\";\n            result.cssStyle[\"height\"] = \"0px\";\n            if (posX.offset)\n                result.cssStyle[\"left\"] = posX.offset;\n            if (posY.offset)\n                result.cssStyle[\"top\"] = posY.offset;\n        }\n        else if (isAnchor && (posX.align == 'left' || posX.align == 'right')) {\n            result.cssStyle[\"float\"] = posX.align;\n        }\n        return result;\n    }\n    parseGraphic(elem) {\n        var graphicData = globalXmlParser.element(elem, \"graphicData\");\n        for (let n of globalXmlParser.elements(graphicData)) {\n            switch (n.localName) {\n                case \"pic\":\n                    return this.parsePicture(n);\n            }\n        }\n        return null;\n    }\n    parsePicture(elem) {\n        var result = { type: DomType.Image, src: \"\", cssStyle: {} };\n        var blipFill = globalXmlParser.element(elem, \"blipFill\");\n        var blip = globalXmlParser.element(blipFill, \"blip\");\n        var srcRect = globalXmlParser.element(blipFill, \"srcRect\");\n        result.src = globalXmlParser.attr(blip, \"embed\");\n        if (srcRect) {\n            result.srcRect = [\n                globalXmlParser.intAttr(srcRect, \"l\", 0) / 100000,\n                globalXmlParser.intAttr(srcRect, \"t\", 0) / 100000,\n                globalXmlParser.intAttr(srcRect, \"r\", 0) / 100000,\n                globalXmlParser.intAttr(srcRect, \"b\", 0) / 100000,\n            ];\n        }\n        var spPr = globalXmlParser.element(elem, \"spPr\");\n        var xfrm = globalXmlParser.element(spPr, \"xfrm\");\n        result.cssStyle[\"position\"] = \"relative\";\n        if (xfrm) {\n            result.rotation = globalXmlParser.intAttr(xfrm, \"rot\", 0) / 60000;\n            for (var n of globalXmlParser.elements(xfrm)) {\n                switch (n.localName) {\n                    case \"ext\":\n                        result.cssStyle[\"width\"] = globalXmlParser.lengthAttr(n, \"cx\", LengthUsage.Emu);\n                        result.cssStyle[\"height\"] = globalXmlParser.lengthAttr(n, \"cy\", LengthUsage.Emu);\n                        break;\n                    case \"off\":\n                        result.cssStyle[\"left\"] = globalXmlParser.lengthAttr(n, \"x\", LengthUsage.Emu);\n                        result.cssStyle[\"top\"] = globalXmlParser.lengthAttr(n, \"y\", LengthUsage.Emu);\n                        break;\n                }\n            }\n        }\n        return result;\n    }\n    parseTable(node) {\n        var result = { type: DomType.Table, children: [] };\n        for (const c of globalXmlParser.elements(node)) {\n            switch (c.localName) {\n                case \"tr\":\n                    result.children.push(this.parseTableRow(c));\n                    break;\n                case \"tblGrid\":\n                    result.columns = this.parseTableColumns(c);\n                    break;\n                case \"tblPr\":\n                    this.parseTableProperties(c, result);\n                    break;\n            }\n        }\n        return result;\n    }\n    parseTableColumns(node) {\n        var result = [];\n        for (const n of globalXmlParser.elements(node)) {\n            switch (n.localName) {\n                case \"gridCol\":\n                    result.push({ width: globalXmlParser.lengthAttr(n, \"w\") });\n                    break;\n            }\n        }\n        return result;\n    }\n    parseTableProperties(elem, table) {\n        table.cssStyle = {};\n        table.cellStyle = {};\n        this.parseDefaultProperties(elem, table.cssStyle, table.cellStyle, c => {\n            switch (c.localName) {\n                case \"tblStyle\":\n                    table.styleName = globalXmlParser.attr(c, \"val\");\n                    break;\n                case \"tblLook\":\n                    table.className = values.classNameOftblLook(c);\n                    break;\n                case \"tblpPr\":\n                    this.parseTablePosition(c, table);\n                    break;\n                case \"tblStyleColBandSize\":\n                    table.colBandSize = globalXmlParser.intAttr(c, \"val\");\n                    break;\n                case \"tblStyleRowBandSize\":\n                    table.rowBandSize = globalXmlParser.intAttr(c, \"val\");\n                    break;\n                case \"hidden\":\n                    table.cssStyle[\"display\"] = \"none\";\n                    break;\n                default:\n                    return false;\n            }\n            return true;\n        });\n        switch (table.cssStyle[\"text-align\"]) {\n            case \"center\":\n                delete table.cssStyle[\"text-align\"];\n                table.cssStyle[\"margin-left\"] = \"auto\";\n                table.cssStyle[\"margin-right\"] = \"auto\";\n                break;\n            case \"right\":\n                delete table.cssStyle[\"text-align\"];\n                table.cssStyle[\"margin-left\"] = \"auto\";\n                break;\n        }\n    }\n    parseTablePosition(node, table) {\n        var topFromText = globalXmlParser.lengthAttr(node, \"topFromText\");\n        var bottomFromText = globalXmlParser.lengthAttr(node, \"bottomFromText\");\n        var rightFromText = globalXmlParser.lengthAttr(node, \"rightFromText\");\n        var leftFromText = globalXmlParser.lengthAttr(node, \"leftFromText\");\n        table.cssStyle[\"float\"] = 'left';\n        table.cssStyle[\"margin-bottom\"] = values.addSize(table.cssStyle[\"margin-bottom\"], bottomFromText);\n        table.cssStyle[\"margin-left\"] = values.addSize(table.cssStyle[\"margin-left\"], leftFromText);\n        table.cssStyle[\"margin-right\"] = values.addSize(table.cssStyle[\"margin-right\"], rightFromText);\n        table.cssStyle[\"margin-top\"] = values.addSize(table.cssStyle[\"margin-top\"], topFromText);\n    }\n    parseTableRow(node) {\n        var result = { type: DomType.Row, children: [] };\n        for (const c of globalXmlParser.elements(node)) {\n            switch (c.localName) {\n                case \"tc\":\n                    result.children.push(this.parseTableCell(c));\n                    break;\n                case \"trPr\":\n                case \"tblPrEx\":\n                    this.parseTableRowProperties(c, result);\n                    break;\n            }\n        }\n        return result;\n    }\n    parseTableRowProperties(elem, row) {\n        row.cssStyle = this.parseDefaultProperties(elem, {}, null, c => {\n            switch (c.localName) {\n                case \"cnfStyle\":\n                    row.className = values.classNameOfCnfStyle(c);\n                    break;\n                case \"tblHeader\":\n                    row.isHeader = globalXmlParser.boolAttr(c, \"val\");\n                    break;\n                case \"gridBefore\":\n                    row.gridBefore = globalXmlParser.intAttr(c, \"val\");\n                    break;\n                case \"gridAfter\":\n                    row.gridAfter = globalXmlParser.intAttr(c, \"val\");\n                    break;\n                default:\n                    return false;\n            }\n            return true;\n        });\n    }\n    parseTableCell(node) {\n        var result = { type: DomType.Cell, children: [] };\n        for (const c of globalXmlParser.elements(node)) {\n            switch (c.localName) {\n                case \"tbl\":\n                    result.children.push(this.parseTable(c));\n                    break;\n                case \"p\":\n                    result.children.push(this.parseParagraph(c));\n                    break;\n                case \"tcPr\":\n                    this.parseTableCellProperties(c, result);\n                    break;\n            }\n        }\n        return result;\n    }\n    parseTableCellProperties(elem, cell) {\n        cell.cssStyle = this.parseDefaultProperties(elem, {}, null, c => {\n            switch (c.localName) {\n                case \"gridSpan\":\n                    cell.span = globalXmlParser.intAttr(c, \"val\", null);\n                    break;\n                case \"vMerge\":\n                    cell.verticalMerge = globalXmlParser.attr(c, \"val\") ?? \"continue\";\n                    break;\n                case \"cnfStyle\":\n                    cell.className = values.classNameOfCnfStyle(c);\n                    break;\n                default:\n                    return false;\n            }\n            return true;\n        });\n        this.parseTableCellVerticalText(elem, cell);\n    }\n    parseTableCellVerticalText(elem, cell) {\n        const directionMap = {\n            \"btLr\": {\n                writingMode: \"vertical-rl\",\n                transform: \"rotate(180deg)\"\n            },\n            \"lrTb\": {\n                writingMode: \"vertical-lr\",\n                transform: \"none\"\n            },\n            \"tbRl\": {\n                writingMode: \"vertical-rl\",\n                transform: \"none\"\n            }\n        };\n        for (const c of globalXmlParser.elements(elem)) {\n            if (c.localName === \"textDirection\") {\n                const direction = globalXmlParser.attr(c, \"val\");\n                const style = directionMap[direction] || { writingMode: \"horizontal-tb\" };\n                cell.cssStyle[\"writing-mode\"] = style.writingMode;\n                cell.cssStyle[\"transform\"] = style.transform;\n            }\n        }\n    }\n    parseDefaultProperties(elem, style = null, childStyle = null, handler = null) {\n        style = style || {};\n        for (const c of globalXmlParser.elements(elem)) {\n            if (handler?.(c))\n                continue;\n            switch (c.localName) {\n                case \"jc\":\n                    style[\"text-align\"] = values.valueOfJc(c);\n                    break;\n                case \"textAlignment\":\n                    style[\"vertical-align\"] = values.valueOfTextAlignment(c);\n                    break;\n                case \"color\":\n                    style[\"color\"] = xmlUtil.colorAttr(c, \"val\", null, autos.color);\n                    break;\n                case \"sz\":\n                    style[\"font-size\"] = style[\"min-height\"] = globalXmlParser.lengthAttr(c, \"val\", LengthUsage.FontSize);\n                    break;\n                case \"shd\":\n                    style[\"background-color\"] = xmlUtil.colorAttr(c, \"fill\", null, autos.shd);\n                    break;\n                case \"highlight\":\n                    style[\"background-color\"] = xmlUtil.colorAttr(c, \"val\", null, autos.highlight);\n                    break;\n                case \"vertAlign\":\n                    break;\n                case \"position\":\n                    style.verticalAlign = globalXmlParser.lengthAttr(c, \"val\", LengthUsage.FontSize);\n                    break;\n                case \"tcW\":\n                    if (this.options.ignoreWidth)\n                        break;\n                case \"tblW\":\n                    style[\"width\"] = values.valueOfSize(c, \"w\");\n                    break;\n                case \"trHeight\":\n                    this.parseTrHeight(c, style);\n                    break;\n                case \"strike\":\n                    style[\"text-decoration\"] = globalXmlParser.boolAttr(c, \"val\", true) ? \"line-through\" : \"none\";\n                    break;\n                case \"b\":\n                    style[\"font-weight\"] = globalXmlParser.boolAttr(c, \"val\", true) ? \"bold\" : \"normal\";\n                    break;\n                case \"i\":\n                    style[\"font-style\"] = globalXmlParser.boolAttr(c, \"val\", true) ? \"italic\" : \"normal\";\n                    break;\n                case \"caps\":\n                    style[\"text-transform\"] = globalXmlParser.boolAttr(c, \"val\", true) ? \"uppercase\" : \"none\";\n                    break;\n                case \"smallCaps\":\n                    style[\"font-variant\"] = globalXmlParser.boolAttr(c, \"val\", true) ? \"small-caps\" : \"none\";\n                    break;\n                case \"u\":\n                    this.parseUnderline(c, style);\n                    break;\n                case \"ind\":\n                case \"tblInd\":\n                    this.parseIndentation(c, style);\n                    break;\n                case \"rFonts\":\n                    this.parseFont(c, style);\n                    break;\n                case \"tblBorders\":\n                    this.parseBorderProperties(c, childStyle || style);\n                    break;\n                case \"tblCellSpacing\":\n                    style[\"border-spacing\"] = values.valueOfMargin(c);\n                    style[\"border-collapse\"] = \"separate\";\n                    break;\n                case \"pBdr\":\n                    this.parseBorderProperties(c, style);\n                    break;\n                case \"bdr\":\n                    style[\"border\"] = values.valueOfBorder(c);\n                    break;\n                case \"tcBorders\":\n                    this.parseBorderProperties(c, style);\n                    break;\n                case \"vanish\":\n                    if (globalXmlParser.boolAttr(c, \"val\", true))\n                        style[\"display\"] = \"none\";\n                    break;\n                case \"kern\":\n                    break;\n                case \"noWrap\":\n                    break;\n                case \"tblCellMar\":\n                case \"tcMar\":\n                    this.parseMarginProperties(c, childStyle || style);\n                    break;\n                case \"tblLayout\":\n                    style[\"table-layout\"] = values.valueOfTblLayout(c);\n                    break;\n                case \"vAlign\":\n                    style[\"vertical-align\"] = values.valueOfTextAlignment(c);\n                    break;\n                case \"spacing\":\n                    if (elem.localName == \"pPr\")\n                        this.parseSpacing(c, style);\n                    break;\n                case \"wordWrap\":\n                    if (globalXmlParser.boolAttr(c, \"val\"))\n                        style[\"overflow-wrap\"] = \"break-word\";\n                    break;\n                case \"suppressAutoHyphens\":\n                    style[\"hyphens\"] = globalXmlParser.boolAttr(c, \"val\", true) ? \"none\" : \"auto\";\n                    break;\n                case \"lang\":\n                    style[\"$lang\"] = globalXmlParser.attr(c, \"val\");\n                    break;\n                case \"rtl\":\n                case \"bidi\":\n                    if (globalXmlParser.boolAttr(c, \"val\", true))\n                        style[\"direction\"] = \"rtl\";\n                    break;\n                case \"bCs\":\n                case \"iCs\":\n                case \"szCs\":\n                case \"tabs\":\n                case \"outlineLvl\":\n                case \"contextualSpacing\":\n                case \"tblStyleColBandSize\":\n                case \"tblStyleRowBandSize\":\n                case \"webHidden\":\n                case \"pageBreakBefore\":\n                case \"suppressLineNumbers\":\n                case \"keepLines\":\n                case \"keepNext\":\n                case \"widowControl\":\n                case \"bidi\":\n                case \"rtl\":\n                case \"noProof\":\n                    break;\n                default:\n                    if (this.options.debug)\n                        console.warn(`DOCX: Unknown document element: ${elem.localName}.${c.localName}`);\n                    break;\n            }\n        }\n        return style;\n    }\n    parseUnderline(node, style) {\n        var val = globalXmlParser.attr(node, \"val\");\n        if (val == null)\n            return;\n        switch (val) {\n            case \"dash\":\n            case \"dashDotDotHeavy\":\n            case \"dashDotHeavy\":\n            case \"dashedHeavy\":\n            case \"dashLong\":\n            case \"dashLongHeavy\":\n            case \"dotDash\":\n            case \"dotDotDash\":\n                style[\"text-decoration\"] = \"underline dashed\";\n                break;\n            case \"dotted\":\n            case \"dottedHeavy\":\n                style[\"text-decoration\"] = \"underline dotted\";\n                break;\n            case \"double\":\n                style[\"text-decoration\"] = \"underline double\";\n                break;\n            case \"single\":\n            case \"thick\":\n                style[\"text-decoration\"] = \"underline\";\n                break;\n            case \"wave\":\n            case \"wavyDouble\":\n            case \"wavyHeavy\":\n                style[\"text-decoration\"] = \"underline wavy\";\n                break;\n            case \"words\":\n                style[\"text-decoration\"] = \"underline\";\n                break;\n            case \"none\":\n                style[\"text-decoration\"] = \"none\";\n                break;\n        }\n        var col = xmlUtil.colorAttr(node, \"color\");\n        if (col)\n            style[\"text-decoration-color\"] = col;\n    }\n    parseFont(node, style) {\n        var ascii = globalXmlParser.attr(node, \"ascii\");\n        var asciiTheme = values.themeValue(node, \"asciiTheme\");\n        var eastAsia = globalXmlParser.attr(node, \"eastAsia\");\n        var fonts = [ascii, asciiTheme, eastAsia].filter(x => x).map(x => encloseFontFamily(x));\n        if (fonts.length > 0)\n            style[\"font-family\"] = [...new Set(fonts)].join(', ');\n    }\n    parseIndentation(node, style) {\n        var firstLine = globalXmlParser.lengthAttr(node, \"firstLine\");\n        var hanging = globalXmlParser.lengthAttr(node, \"hanging\");\n        var left = globalXmlParser.lengthAttr(node, \"left\");\n        var start = globalXmlParser.lengthAttr(node, \"start\");\n        var right = globalXmlParser.lengthAttr(node, \"right\");\n        var end = globalXmlParser.lengthAttr(node, \"end\");\n        if (firstLine)\n            style[\"text-indent\"] = firstLine;\n        if (hanging)\n            style[\"text-indent\"] = `-${hanging}`;\n        if (left || start)\n            style[\"margin-inline-start\"] = left || start;\n        if (right || end)\n            style[\"margin-inline-end\"] = right || end;\n    }\n    parseSpacing(node, style) {\n        var before = globalXmlParser.lengthAttr(node, \"before\");\n        var after = globalXmlParser.lengthAttr(node, \"after\");\n        var line = globalXmlParser.intAttr(node, \"line\", null);\n        var lineRule = globalXmlParser.attr(node, \"lineRule\");\n        if (before)\n            style[\"margin-top\"] = before;\n        if (after)\n            style[\"margin-bottom\"] = after;\n        if (line !== null) {\n            switch (lineRule) {\n                case \"auto\":\n                    style[\"line-height\"] = `${(line / 240).toFixed(2)}`;\n                    break;\n                case \"atLeast\":\n                    style[\"line-height\"] = `calc(100% + ${line / 20}pt)`;\n                    break;\n                default:\n                    style[\"line-height\"] = style[\"min-height\"] = `${line / 20}pt`;\n                    break;\n            }\n        }\n    }\n    parseMarginProperties(node, output) {\n        for (const c of globalXmlParser.elements(node)) {\n            switch (c.localName) {\n                case \"left\":\n                    output[\"padding-left\"] = values.valueOfMargin(c);\n                    break;\n                case \"right\":\n                    output[\"padding-right\"] = values.valueOfMargin(c);\n                    break;\n                case \"top\":\n                    output[\"padding-top\"] = values.valueOfMargin(c);\n                    break;\n                case \"bottom\":\n                    output[\"padding-bottom\"] = values.valueOfMargin(c);\n                    break;\n            }\n        }\n    }\n    parseTrHeight(node, output) {\n        switch (globalXmlParser.attr(node, \"hRule\")) {\n            case \"exact\":\n                output[\"height\"] = globalXmlParser.lengthAttr(node, \"val\");\n                break;\n            case \"atLeast\":\n            default:\n                output[\"height\"] = globalXmlParser.lengthAttr(node, \"val\");\n                break;\n        }\n    }\n    parseBorderProperties(node, output) {\n        for (const c of globalXmlParser.elements(node)) {\n            switch (c.localName) {\n                case \"start\":\n                case \"left\":\n                    output[\"border-left\"] = values.valueOfBorder(c);\n                    break;\n                case \"end\":\n                case \"right\":\n                    output[\"border-right\"] = values.valueOfBorder(c);\n                    break;\n                case \"top\":\n                    output[\"border-top\"] = values.valueOfBorder(c);\n                    break;\n                case \"bottom\":\n                    output[\"border-bottom\"] = values.valueOfBorder(c);\n                    break;\n            }\n        }\n    }\n}\nconst knownColors = ['black', 'blue', 'cyan', 'darkBlue', 'darkCyan', 'darkGray', 'darkGreen', 'darkMagenta', 'darkRed', 'darkYellow', 'green', 'lightGray', 'magenta', 'none', 'red', 'white', 'yellow'];\nclass xmlUtil {\n    static colorAttr(node, attrName, defValue = null, autoColor = 'black') {\n        var v = globalXmlParser.attr(node, attrName);\n        if (v) {\n            if (v == \"auto\") {\n                return autoColor;\n            }\n            else if (knownColors.includes(v)) {\n                return v;\n            }\n            return `#${v}`;\n        }\n        var themeColor = globalXmlParser.attr(node, \"themeColor\");\n        return themeColor ? `var(--docx-${themeColor}-color)` : defValue;\n    }\n}\nclass values {\n    static themeValue(c, attr) {\n        var val = globalXmlParser.attr(c, attr);\n        return val ? `var(--docx-${val}-font)` : null;\n    }\n    static valueOfSize(c, attr) {\n        var type = LengthUsage.Dxa;\n        switch (globalXmlParser.attr(c, \"type\")) {\n            case \"dxa\": break;\n            case \"pct\":\n                type = LengthUsage.Percent;\n                break;\n            case \"auto\": return \"auto\";\n        }\n        return globalXmlParser.lengthAttr(c, attr, type);\n    }\n    static valueOfMargin(c) {\n        return globalXmlParser.lengthAttr(c, \"w\");\n    }\n    static valueOfBorder(c) {\n        var type = values.parseBorderType(globalXmlParser.attr(c, \"val\"));\n        if (type == \"none\")\n            return \"none\";\n        var color = xmlUtil.colorAttr(c, \"color\");\n        var size = globalXmlParser.lengthAttr(c, \"sz\", LengthUsage.Border);\n        return `${size} ${type} ${color == \"auto\" ? autos.borderColor : color}`;\n    }\n    static parseBorderType(type) {\n        switch (type) {\n            case \"single\": return \"solid\";\n            case \"dashDotStroked\": return \"solid\";\n            case \"dashed\": return \"dashed\";\n            case \"dashSmallGap\": return \"dashed\";\n            case \"dotDash\": return \"dotted\";\n            case \"dotDotDash\": return \"dotted\";\n            case \"dotted\": return \"dotted\";\n            case \"double\": return \"double\";\n            case \"doubleWave\": return \"double\";\n            case \"inset\": return \"inset\";\n            case \"nil\": return \"none\";\n            case \"none\": return \"none\";\n            case \"outset\": return \"outset\";\n            case \"thick\": return \"solid\";\n            case \"thickThinLargeGap\": return \"solid\";\n            case \"thickThinMediumGap\": return \"solid\";\n            case \"thickThinSmallGap\": return \"solid\";\n            case \"thinThickLargeGap\": return \"solid\";\n            case \"thinThickMediumGap\": return \"solid\";\n            case \"thinThickSmallGap\": return \"solid\";\n            case \"thinThickThinLargeGap\": return \"solid\";\n            case \"thinThickThinMediumGap\": return \"solid\";\n            case \"thinThickThinSmallGap\": return \"solid\";\n            case \"threeDEmboss\": return \"solid\";\n            case \"threeDEngrave\": return \"solid\";\n            case \"triple\": return \"double\";\n            case \"wave\": return \"solid\";\n        }\n        return 'solid';\n    }\n    static valueOfTblLayout(c) {\n        var type = globalXmlParser.attr(c, \"val\");\n        return type == \"fixed\" ? \"fixed\" : \"auto\";\n    }\n    static classNameOfCnfStyle(c) {\n        const val = globalXmlParser.attr(c, \"val\");\n        const classes = [\n            'first-row', 'last-row', 'first-col', 'last-col',\n            'odd-col', 'even-col', 'odd-row', 'even-row',\n            'ne-cell', 'nw-cell', 'se-cell', 'sw-cell'\n        ];\n        return classes.filter((_, i) => val[i] == '1').join(' ');\n    }\n    static valueOfJc(c) {\n        var type = globalXmlParser.attr(c, \"val\");\n        switch (type) {\n            case \"start\":\n            case \"left\": return \"left\";\n            case \"center\": return \"center\";\n            case \"end\":\n            case \"right\": return \"right\";\n            case \"both\": return \"justify\";\n        }\n        return type;\n    }\n    static valueOfVertAlign(c, asTagName = false) {\n        var type = globalXmlParser.attr(c, \"val\");\n        switch (type) {\n            case \"subscript\": return \"sub\";\n            case \"superscript\": return asTagName ? \"sup\" : \"super\";\n        }\n        return asTagName ? null : type;\n    }\n    static valueOfTextAlignment(c) {\n        var type = globalXmlParser.attr(c, \"val\");\n        switch (type) {\n            case \"auto\":\n            case \"baseline\": return \"baseline\";\n            case \"top\": return \"top\";\n            case \"center\": return \"middle\";\n            case \"bottom\": return \"bottom\";\n        }\n        return type;\n    }\n    static addSize(a, b) {\n        if (a == null)\n            return b;\n        if (b == null)\n            return a;\n        return `calc(${a} + ${b})`;\n    }\n    static classNameOftblLook(c) {\n        const val = globalXmlParser.hexAttr(c, \"val\", 0);\n        let className = \"\";\n        if (globalXmlParser.boolAttr(c, \"firstRow\") || (val & 0x0020))\n            className += \" first-row\";\n        if (globalXmlParser.boolAttr(c, \"lastRow\") || (val & 0x0040))\n            className += \" last-row\";\n        if (globalXmlParser.boolAttr(c, \"firstColumn\") || (val & 0x0080))\n            className += \" first-col\";\n        if (globalXmlParser.boolAttr(c, \"lastColumn\") || (val & 0x0100))\n            className += \" last-col\";\n        if (globalXmlParser.boolAttr(c, \"noHBand\") || (val & 0x0200))\n            className += \" no-hband\";\n        if (globalXmlParser.boolAttr(c, \"noVBand\") || (val & 0x0400))\n            className += \" no-vband\";\n        return className.trim();\n    }\n}\n\nconst defaultTab = { pos: 0, leader: \"none\", style: \"left\" };\nconst maxTabs = 50;\nfunction computePixelToPoint(container = document.body) {\n    const temp = document.createElement(\"div\");\n    temp.style.width = '100pt';\n    container.appendChild(temp);\n    const result = 100 / temp.offsetWidth;\n    container.removeChild(temp);\n    return result;\n}\nfunction updateTabStop(elem, tabs, defaultTabSize, pixelToPoint = 72 / 96) {\n    const p = elem.closest(\"p\");\n    const ebb = elem.getBoundingClientRect();\n    const pbb = p.getBoundingClientRect();\n    const pcs = getComputedStyle(p);\n    const tabStops = tabs?.length > 0 ? tabs.map(t => ({\n        pos: lengthToPoint(t.position),\n        leader: t.leader,\n        style: t.style\n    })).sort((a, b) => a.pos - b.pos) : [defaultTab];\n    const lastTab = tabStops[tabStops.length - 1];\n    const pWidthPt = pbb.width * pixelToPoint;\n    const size = lengthToPoint(defaultTabSize);\n    let pos = lastTab.pos + size;\n    if (pos < pWidthPt) {\n        for (; pos < pWidthPt && tabStops.length < maxTabs; pos += size) {\n            tabStops.push({ ...defaultTab, pos: pos });\n        }\n    }\n    const marginLeft = parseFloat(pcs.marginLeft);\n    const pOffset = pbb.left + marginLeft;\n    const left = (ebb.left - pOffset) * pixelToPoint;\n    const tab = tabStops.find(t => t.style != \"clear\" && t.pos > left);\n    if (tab == null)\n        return;\n    let width = 1;\n    if (tab.style == \"right\" || tab.style == \"center\") {\n        const tabStops = Array.from(p.querySelectorAll(`.${elem.className}`));\n        const nextIdx = tabStops.indexOf(elem) + 1;\n        const range = document.createRange();\n        range.setStart(elem, 1);\n        if (nextIdx < tabStops.length) {\n            range.setEndBefore(tabStops[nextIdx]);\n        }\n        else {\n            range.setEndAfter(p);\n        }\n        const mul = tab.style == \"center\" ? 0.5 : 1;\n        const nextBB = range.getBoundingClientRect();\n        const offset = nextBB.left + mul * nextBB.width - (pbb.left - marginLeft);\n        width = tab.pos - offset * pixelToPoint;\n    }\n    else {\n        width = tab.pos - left;\n    }\n    elem.innerHTML = \"&nbsp;\";\n    elem.style.textDecoration = \"inherit\";\n    elem.style.wordSpacing = `${width.toFixed(0)}pt`;\n    switch (tab.leader) {\n        case \"dot\":\n        case \"middleDot\":\n            elem.style.textDecoration = \"underline\";\n            elem.style.textDecorationStyle = \"dotted\";\n            break;\n        case \"hyphen\":\n        case \"heavy\":\n        case \"underscore\":\n            elem.style.textDecoration = \"underline\";\n            break;\n    }\n}\nfunction lengthToPoint(length) {\n    return parseFloat(length);\n}\n\nconst ns = {\n    svg: \"http://www.w3.org/2000/svg\",\n    mathML: \"http://www.w3.org/1998/Math/MathML\"\n};\nclass HtmlRenderer {\n    constructor(htmlDocument) {\n        this.htmlDocument = htmlDocument;\n        this.className = \"docx\";\n        this.styleMap = {};\n        this.currentPart = null;\n        this.tableVerticalMerges = [];\n        this.currentVerticalMerge = null;\n        this.tableCellPositions = [];\n        this.currentCellPosition = null;\n        this.footnoteMap = {};\n        this.endnoteMap = {};\n        this.currentEndnoteIds = [];\n        this.usedHederFooterParts = [];\n        this.currentTabs = [];\n        this.commentMap = {};\n        this.tasks = [];\n        this.postRenderTasks = [];\n    }\n    async render(document, bodyContainer, styleContainer = null, options) {\n        this.document = document;\n        this.options = options;\n        this.className = options.className;\n        this.rootSelector = options.inWrapper ? `.${this.className}-wrapper` : ':root';\n        this.styleMap = null;\n        this.tasks = [];\n        if (this.options.renderComments && globalThis.Highlight) {\n            this.commentHighlight = new Highlight();\n        }\n        styleContainer = styleContainer || bodyContainer;\n        removeAllElements(styleContainer);\n        removeAllElements(bodyContainer);\n        styleContainer.appendChild(this.createComment(\"docxjs library predefined styles\"));\n        styleContainer.appendChild(this.renderDefaultStyle());\n        if (document.themePart) {\n            styleContainer.appendChild(this.createComment(\"docxjs document theme values\"));\n            this.renderTheme(document.themePart, styleContainer);\n        }\n        if (document.stylesPart != null) {\n            this.styleMap = this.processStyles(document.stylesPart.styles);\n            styleContainer.appendChild(this.createComment(\"docxjs document styles\"));\n            styleContainer.appendChild(this.renderStyles(document.stylesPart.styles));\n        }\n        if (document.numberingPart) {\n            this.prodessNumberings(document.numberingPart.domNumberings);\n            styleContainer.appendChild(this.createComment(\"docxjs document numbering styles\"));\n            styleContainer.appendChild(this.renderNumbering(document.numberingPart.domNumberings, styleContainer));\n        }\n        if (document.footnotesPart) {\n            this.footnoteMap = keyBy(document.footnotesPart.notes, x => x.id);\n        }\n        if (document.endnotesPart) {\n            this.endnoteMap = keyBy(document.endnotesPart.notes, x => x.id);\n        }\n        if (document.settingsPart) {\n            this.defaultTabSize = document.settingsPart.settings?.defaultTabStop;\n        }\n        if (!options.ignoreFonts && document.fontTablePart)\n            this.renderFontTable(document.fontTablePart, styleContainer);\n        var sectionElements = this.renderSections(document.documentPart.body);\n        if (this.options.inWrapper) {\n            bodyContainer.appendChild(this.renderWrapper(sectionElements));\n        }\n        else {\n            appendChildren(bodyContainer, sectionElements);\n        }\n        if (this.commentHighlight && options.renderComments) {\n            CSS.highlights.set(`${this.className}-comments`, this.commentHighlight);\n        }\n        this.postRenderTasks.forEach(t => t());\n        await Promise.allSettled(this.tasks);\n        this.refreshTabStops();\n    }\n    renderTheme(themePart, styleContainer) {\n        const variables = {};\n        const fontScheme = themePart.theme?.fontScheme;\n        if (fontScheme) {\n            if (fontScheme.majorFont) {\n                variables['--docx-majorHAnsi-font'] = fontScheme.majorFont.latinTypeface;\n            }\n            if (fontScheme.minorFont) {\n                variables['--docx-minorHAnsi-font'] = fontScheme.minorFont.latinTypeface;\n            }\n        }\n        const colorScheme = themePart.theme?.colorScheme;\n        if (colorScheme) {\n            for (let [k, v] of Object.entries(colorScheme.colors)) {\n                variables[`--docx-${k}-color`] = `#${v}`;\n            }\n        }\n        const cssText = this.styleToString(`.${this.className}`, variables);\n        styleContainer.appendChild(this.createStyleElement(cssText));\n    }\n    renderFontTable(fontsPart, styleContainer) {\n        for (let f of fontsPart.fonts) {\n            for (let ref of f.embedFontRefs) {\n                this.tasks.push(this.document.loadFont(ref.id, ref.key).then(fontData => {\n                    const cssValues = {\n                        'font-family': encloseFontFamily(f.name),\n                        'src': `url(${fontData})`\n                    };\n                    if (ref.type == \"bold\" || ref.type == \"boldItalic\") {\n                        cssValues['font-weight'] = 'bold';\n                    }\n                    if (ref.type == \"italic\" || ref.type == \"boldItalic\") {\n                        cssValues['font-style'] = 'italic';\n                    }\n                    const cssText = this.styleToString(\"@font-face\", cssValues);\n                    styleContainer.appendChild(this.createComment(`docxjs ${f.name} font`));\n                    styleContainer.appendChild(this.createStyleElement(cssText));\n                }));\n            }\n        }\n    }\n    processStyleName(className) {\n        return className ? `${this.className}_${escapeClassName(className)}` : this.className;\n    }\n    processStyles(styles) {\n        const stylesMap = keyBy(styles.filter(x => x.id != null), x => x.id);\n        for (const style of styles.filter(x => x.basedOn)) {\n            var baseStyle = stylesMap[style.basedOn];\n            if (baseStyle) {\n                style.paragraphProps = mergeDeep(style.paragraphProps, baseStyle.paragraphProps);\n                style.runProps = mergeDeep(style.runProps, baseStyle.runProps);\n                for (const baseValues of baseStyle.styles) {\n                    const styleValues = style.styles.find(x => x.target == baseValues.target);\n                    if (styleValues) {\n                        this.copyStyleProperties(baseValues.values, styleValues.values);\n                    }\n                    else {\n                        style.styles.push({ ...baseValues, values: { ...baseValues.values } });\n                    }\n                }\n            }\n            else if (this.options.debug)\n                console.warn(`Can't find base style ${style.basedOn}`);\n        }\n        for (let style of styles) {\n            style.cssName = this.processStyleName(style.id);\n        }\n        return stylesMap;\n    }\n    prodessNumberings(numberings) {\n        for (let num of numberings.filter(n => n.pStyleName)) {\n            const style = this.findStyle(num.pStyleName);\n            if (style?.paragraphProps?.numbering) {\n                style.paragraphProps.numbering.level = num.level;\n            }\n        }\n    }\n    processElement(element) {\n        if (element.children) {\n            for (var e of element.children) {\n                e.parent = element;\n                if (e.type == DomType.Table) {\n                    this.processTable(e);\n                }\n                else {\n                    this.processElement(e);\n                }\n            }\n        }\n    }\n    processTable(table) {\n        for (var r of table.children) {\n            for (var c of r.children) {\n                c.cssStyle = this.copyStyleProperties(table.cellStyle, c.cssStyle, [\n                    \"border-left\", \"border-right\", \"border-top\", \"border-bottom\",\n                    \"padding-left\", \"padding-right\", \"padding-top\", \"padding-bottom\"\n                ]);\n                this.processElement(c);\n            }\n        }\n    }\n    copyStyleProperties(input, output, attrs = null) {\n        if (!input)\n            return output;\n        if (output == null)\n            output = {};\n        if (attrs == null)\n            attrs = Object.getOwnPropertyNames(input);\n        for (var key of attrs) {\n            if (input.hasOwnProperty(key) && !output.hasOwnProperty(key))\n                output[key] = input[key];\n        }\n        return output;\n    }\n    createPageElement(className, props) {\n        var elem = this.createElement(\"section\", { className });\n        if (props) {\n            if (props.pageMargins) {\n                elem.style.paddingLeft = props.pageMargins.left;\n                elem.style.paddingRight = props.pageMargins.right;\n                elem.style.paddingTop = props.pageMargins.top;\n                elem.style.paddingBottom = props.pageMargins.bottom;\n            }\n            if (props.pageSize) {\n                if (!this.options.ignoreWidth)\n                    elem.style.width = props.pageSize.width;\n                if (!this.options.ignoreHeight)\n                    elem.style.minHeight = props.pageSize.height;\n            }\n        }\n        return elem;\n    }\n    createSectionContent(props) {\n        var elem = this.createElement(\"article\");\n        if (props.columns && props.columns.numberOfColumns) {\n            elem.style.columnCount = `${props.columns.numberOfColumns}`;\n            elem.style.columnGap = props.columns.space;\n            if (props.columns.separator) {\n                elem.style.columnRule = \"1px solid black\";\n            }\n        }\n        return elem;\n    }\n    renderSections(document) {\n        const result = [];\n        this.processElement(document);\n        const sections = this.splitBySection(document.children, document.props);\n        const pages = this.groupByPageBreaks(sections);\n        let prevProps = null;\n        for (let i = 0, l = pages.length; i < l; i++) {\n            this.currentFootnoteIds = [];\n            const section = pages[i][0];\n            let props = section.sectProps;\n            const pageElement = this.createPageElement(this.className, props);\n            this.renderStyleValues(document.cssStyle, pageElement);\n            this.options.renderHeaders && this.renderHeaderFooter(props.headerRefs, props, result.length, prevProps != props, pageElement);\n            for (const sect of pages[i]) {\n                var contentElement = this.createSectionContent(sect.sectProps);\n                this.renderElements(sect.elements, contentElement);\n                pageElement.appendChild(contentElement);\n                props = sect.sectProps;\n            }\n            if (this.options.renderFootnotes) {\n                this.renderNotes(this.currentFootnoteIds, this.footnoteMap, pageElement);\n            }\n            if (this.options.renderEndnotes && i == l - 1) {\n                this.renderNotes(this.currentEndnoteIds, this.endnoteMap, pageElement);\n            }\n            this.options.renderFooters && this.renderHeaderFooter(props.footerRefs, props, result.length, prevProps != props, pageElement);\n            result.push(pageElement);\n            prevProps = props;\n        }\n        return result;\n    }\n    renderHeaderFooter(refs, props, page, firstOfSection, into) {\n        if (!refs)\n            return;\n        var ref = (props.titlePage && firstOfSection ? refs.find(x => x.type == \"first\") : null)\n            ?? (page % 2 == 1 ? refs.find(x => x.type == \"even\") : null)\n            ?? refs.find(x => x.type == \"default\");\n        var part = ref && this.document.findPartByRelId(ref.id, this.document.documentPart);\n        if (part) {\n            this.currentPart = part;\n            if (!this.usedHederFooterParts.includes(part.path)) {\n                this.processElement(part.rootElement);\n                this.usedHederFooterParts.push(part.path);\n            }\n            const [el] = this.renderElements([part.rootElement], into);\n            if (props?.pageMargins) {\n                if (part.rootElement.type === DomType.Header) {\n                    el.style.marginTop = `calc(${props.pageMargins.header} - ${props.pageMargins.top})`;\n                    el.style.minHeight = `calc(${props.pageMargins.top} - ${props.pageMargins.header})`;\n                }\n                else if (part.rootElement.type === DomType.Footer) {\n                    el.style.marginBottom = `calc(${props.pageMargins.footer} - ${props.pageMargins.bottom})`;\n                    el.style.minHeight = `calc(${props.pageMargins.bottom} - ${props.pageMargins.footer})`;\n                }\n            }\n            this.currentPart = null;\n        }\n    }\n    isPageBreakElement(elem) {\n        if (elem.type != DomType.Break)\n            return false;\n        if (elem.break == \"lastRenderedPageBreak\")\n            return !this.options.ignoreLastRenderedPageBreak;\n        return elem.break == \"page\";\n    }\n    isPageBreakSection(prev, next) {\n        if (!prev)\n            return false;\n        if (!next)\n            return false;\n        return prev.pageSize?.orientation != next.pageSize?.orientation\n            || prev.pageSize?.width != next.pageSize?.width\n            || prev.pageSize?.height != next.pageSize?.height;\n    }\n    splitBySection(elements, defaultProps) {\n        var current = { sectProps: null, elements: [], pageBreak: false };\n        var result = [current];\n        for (let elem of elements) {\n            if (elem.type == DomType.Paragraph) {\n                const s = this.findStyle(elem.styleName);\n                if (s?.paragraphProps?.pageBreakBefore) {\n                    current.sectProps = sectProps;\n                    current.pageBreak = true;\n                    current = { sectProps: null, elements: [], pageBreak: false };\n                    result.push(current);\n                }\n            }\n            current.elements.push(elem);\n            if (elem.type == DomType.Paragraph) {\n                const p = elem;\n                var sectProps = p.sectionProps;\n                var pBreakIndex = -1;\n                var rBreakIndex = -1;\n                if (this.options.breakPages && p.children) {\n                    pBreakIndex = p.children.findIndex(r => {\n                        rBreakIndex = r.children?.findIndex(this.isPageBreakElement.bind(this)) ?? -1;\n                        return rBreakIndex != -1;\n                    });\n                }\n                if (sectProps || pBreakIndex != -1) {\n                    current.sectProps = sectProps;\n                    current.pageBreak = pBreakIndex != -1;\n                    current = { sectProps: null, elements: [], pageBreak: false };\n                    result.push(current);\n                }\n                if (pBreakIndex != -1) {\n                    let breakRun = p.children[pBreakIndex];\n                    let splitRun = rBreakIndex < breakRun.children.length - 1;\n                    if (pBreakIndex < p.children.length - 1 || splitRun) {\n                        var children = elem.children;\n                        var newParagraph = { ...elem, children: children.slice(pBreakIndex) };\n                        elem.children = children.slice(0, pBreakIndex);\n                        current.elements.push(newParagraph);\n                        if (splitRun) {\n                            let runChildren = breakRun.children;\n                            let newRun = { ...breakRun, children: runChildren.slice(0, rBreakIndex) };\n                            elem.children.push(newRun);\n                            breakRun.children = runChildren.slice(rBreakIndex);\n                        }\n                    }\n                }\n            }\n        }\n        let currentSectProps = null;\n        for (let i = result.length - 1; i >= 0; i--) {\n            if (result[i].sectProps == null) {\n                result[i].sectProps = currentSectProps ?? defaultProps;\n            }\n            else {\n                currentSectProps = result[i].sectProps;\n            }\n        }\n        return result;\n    }\n    groupByPageBreaks(sections) {\n        let current = [];\n        let prev;\n        const result = [current];\n        for (let s of sections) {\n            current.push(s);\n            if (this.options.ignoreLastRenderedPageBreak || s.pageBreak || this.isPageBreakSection(prev, s.sectProps))\n                result.push(current = []);\n            prev = s.sectProps;\n        }\n        return result.filter(x => x.length > 0);\n    }\n    renderWrapper(children) {\n        return this.createElement(\"div\", { className: `${this.className}-wrapper` }, children);\n    }\n    renderDefaultStyle() {\n        var c = this.className;\n        var wrapperStyle = `\r\n.${c}-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } \r\n.${c}-wrapper>section.${c} { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }`;\n        if (this.options.hideWrapperOnPrint) {\n            wrapperStyle = `@media not print { ${wrapperStyle} }`;\n        }\n        var styleText = `${wrapperStyle}\r\n.${c} { color: black; hyphens: auto; text-underline-position: from-font; }\r\nsection.${c} { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }\r\nsection.${c}>article { margin-bottom: auto; z-index: 1; }\r\nsection.${c}>footer { z-index: 1; }\r\n.${c} table { border-collapse: collapse; }\r\n.${c} table td, .${c} table th { vertical-align: top; }\r\n.${c} p { margin: 0pt; min-height: 1em; }\r\n.${c} span { white-space: pre-wrap; overflow-wrap: break-word; }\r\n.${c} a { color: inherit; text-decoration: inherit; }\r\n.${c} svg { fill: transparent; }\r\n`;\n        if (this.options.renderComments) {\n            styleText += `\r\n.${c}-comment-ref { cursor: default; }\r\n.${c}-comment-popover { display: none; z-index: 1000; padding: 0.5rem; background: white; position: absolute; box-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.25); width: 30ch; }\r\n.${c}-comment-ref:hover~.${c}-comment-popover { display: block; }\r\n.${c}-comment-author,.${c}-comment-date { font-size: 0.875rem; color: #888; }\r\n`;\n        }\n        return this.createStyleElement(styleText);\n    }\n    renderNumbering(numberings, styleContainer) {\n        var styleText = \"\";\n        var resetCounters = [];\n        for (var num of numberings) {\n            var selector = `p.${this.numberingClass(num.id, num.level)}`;\n            var listStyleType = \"none\";\n            if (num.bullet) {\n                let valiable = `--${this.className}-${num.bullet.src}`.toLowerCase();\n                styleText += this.styleToString(`${selector}:before`, {\n                    \"content\": \"' '\",\n                    \"display\": \"inline-block\",\n                    \"background\": `var(${valiable})`\n                }, num.bullet.style);\n                this.tasks.push(this.document.loadNumberingImage(num.bullet.src).then(data => {\n                    var text = `${this.rootSelector} { ${valiable}: url(${data}) }`;\n                    styleContainer.appendChild(this.createStyleElement(text));\n                }));\n            }\n            else if (num.levelText) {\n                let counter = this.numberingCounter(num.id, num.level);\n                const counterReset = counter + \" \" + (num.start - 1);\n                if (num.level > 0) {\n                    styleText += this.styleToString(`p.${this.numberingClass(num.id, num.level - 1)}`, {\n                        \"counter-set\": counterReset\n                    });\n                }\n                resetCounters.push(counterReset);\n                styleText += this.styleToString(`${selector}:before`, {\n                    \"content\": this.levelTextToContent(num.levelText, num.suff, num.id, this.numFormatToCssValue(num.format)),\n                    \"counter-increment\": counter,\n                    ...num.rStyle,\n                });\n            }\n            else {\n                listStyleType = this.numFormatToCssValue(num.format);\n            }\n            styleText += this.styleToString(selector, {\n                \"display\": \"list-item\",\n                \"list-style-position\": \"inside\",\n                \"list-style-type\": listStyleType,\n                ...num.pStyle\n            });\n        }\n        if (resetCounters.length > 0) {\n            styleText += this.styleToString(this.rootSelector, {\n                \"counter-reset\": resetCounters.join(\" \")\n            });\n        }\n        return this.createStyleElement(styleText);\n    }\n    renderStyles(styles) {\n        var styleText = \"\";\n        const stylesMap = this.styleMap;\n        const defautStyles = keyBy(styles.filter(s => s.isDefault), s => s.target);\n        for (const style of styles) {\n            var subStyles = style.styles;\n            if (style.linked) {\n                var linkedStyle = style.linked && stylesMap[style.linked];\n                if (linkedStyle)\n                    subStyles = subStyles.concat(linkedStyle.styles);\n                else if (this.options.debug)\n                    console.warn(`Can't find linked style ${style.linked}`);\n            }\n            for (const subStyle of subStyles) {\n                var selector = `${style.target ?? ''}.${style.cssName}`;\n                if (style.target != subStyle.target)\n                    selector += ` ${subStyle.target}`;\n                if (defautStyles[style.target] == style)\n                    selector = `.${this.className} ${style.target}, ` + selector;\n                styleText += this.styleToString(selector, subStyle.values);\n            }\n        }\n        return this.createStyleElement(styleText);\n    }\n    renderNotes(noteIds, notesMap, into) {\n        var notes = noteIds.map(id => notesMap[id]).filter(x => x);\n        if (notes.length > 0) {\n            var result = this.createElement(\"ol\", null, this.renderElements(notes));\n            into.appendChild(result);\n        }\n    }\n    renderElement(elem) {\n        switch (elem.type) {\n            case DomType.Paragraph:\n                return this.renderParagraph(elem);\n            case DomType.BookmarkStart:\n                return this.renderBookmarkStart(elem);\n            case DomType.BookmarkEnd:\n                return null;\n            case DomType.Run:\n                return this.renderRun(elem);\n            case DomType.Table:\n                return this.renderTable(elem);\n            case DomType.Row:\n                return this.renderTableRow(elem);\n            case DomType.Cell:\n                return this.renderTableCell(elem);\n            case DomType.Hyperlink:\n                return this.renderHyperlink(elem);\n            case DomType.SmartTag:\n                return this.renderSmartTag(elem);\n            case DomType.Drawing:\n                return this.renderDrawing(elem);\n            case DomType.Image:\n                return this.renderImage(elem);\n            case DomType.Text:\n                return this.renderText(elem);\n            case DomType.Text:\n                return this.renderText(elem);\n            case DomType.DeletedText:\n                return this.renderDeletedText(elem);\n            case DomType.Tab:\n                return this.renderTab(elem);\n            case DomType.Symbol:\n                return this.renderSymbol(elem);\n            case DomType.Break:\n                return this.renderBreak(elem);\n            case DomType.Footer:\n                return this.renderContainer(elem, \"footer\");\n            case DomType.Header:\n                return this.renderContainer(elem, \"header\");\n            case DomType.Footnote:\n            case DomType.Endnote:\n                return this.renderContainer(elem, \"li\");\n            case DomType.FootnoteReference:\n                return this.renderFootnoteReference(elem);\n            case DomType.EndnoteReference:\n                return this.renderEndnoteReference(elem);\n            case DomType.NoBreakHyphen:\n                return this.createElement(\"wbr\");\n            case DomType.VmlPicture:\n                return this.renderVmlPicture(elem);\n            case DomType.VmlElement:\n                return this.renderVmlElement(elem);\n            case DomType.MmlMath:\n                return this.renderContainerNS(elem, ns.mathML, \"math\", { xmlns: ns.mathML });\n            case DomType.MmlMathParagraph:\n                return this.renderContainer(elem, \"span\");\n            case DomType.MmlFraction:\n                return this.renderContainerNS(elem, ns.mathML, \"mfrac\");\n            case DomType.MmlBase:\n                return this.renderContainerNS(elem, ns.mathML, elem.parent.type == DomType.MmlMatrixRow ? \"mtd\" : \"mrow\");\n            case DomType.MmlNumerator:\n            case DomType.MmlDenominator:\n            case DomType.MmlFunction:\n            case DomType.MmlLimit:\n            case DomType.MmlBox:\n                return this.renderContainerNS(elem, ns.mathML, \"mrow\");\n            case DomType.MmlGroupChar:\n                return this.renderMmlGroupChar(elem);\n            case DomType.MmlLimitLower:\n                return this.renderContainerNS(elem, ns.mathML, \"munder\");\n            case DomType.MmlMatrix:\n                return this.renderContainerNS(elem, ns.mathML, \"mtable\");\n            case DomType.MmlMatrixRow:\n                return this.renderContainerNS(elem, ns.mathML, \"mtr\");\n            case DomType.MmlRadical:\n                return this.renderMmlRadical(elem);\n            case DomType.MmlSuperscript:\n                return this.renderContainerNS(elem, ns.mathML, \"msup\");\n            case DomType.MmlSubscript:\n                return this.renderContainerNS(elem, ns.mathML, \"msub\");\n            case DomType.MmlDegree:\n            case DomType.MmlSuperArgument:\n            case DomType.MmlSubArgument:\n                return this.renderContainerNS(elem, ns.mathML, \"mn\");\n            case DomType.MmlFunctionName:\n                return this.renderContainerNS(elem, ns.mathML, \"ms\");\n            case DomType.MmlDelimiter:\n                return this.renderMmlDelimiter(elem);\n            case DomType.MmlRun:\n                return this.renderMmlRun(elem);\n            case DomType.MmlNary:\n                return this.renderMmlNary(elem);\n            case DomType.MmlPreSubSuper:\n                return this.renderMmlPreSubSuper(elem);\n            case DomType.MmlBar:\n                return this.renderMmlBar(elem);\n            case DomType.MmlEquationArray:\n                return this.renderMllList(elem);\n            case DomType.Inserted:\n                return this.renderInserted(elem);\n            case DomType.Deleted:\n                return this.renderDeleted(elem);\n            case DomType.CommentRangeStart:\n                return this.renderCommentRangeStart(elem);\n            case DomType.CommentRangeEnd:\n                return this.renderCommentRangeEnd(elem);\n            case DomType.CommentReference:\n                return this.renderCommentReference(elem);\n            case DomType.AltChunk:\n                return this.renderAltChunk(elem);\n        }\n        return null;\n    }\n    renderElements(elems, into) {\n        if (elems == null)\n            return null;\n        var result = elems.flatMap(e => this.renderElement(e)).filter(e => e != null);\n        if (into)\n            appendChildren(into, result);\n        return result;\n    }\n    renderContainer(elem, tagName, props) {\n        return this.createElement(tagName, props, this.renderElements(elem.children));\n    }\n    renderContainerNS(elem, ns, tagName, props) {\n        return this.createElementNS(ns, tagName, props, this.renderElements(elem.children));\n    }\n    renderParagraph(elem) {\n        var result = this.renderContainer(elem, \"p\");\n        const style = this.findStyle(elem.styleName);\n        elem.tabs ?? (elem.tabs = style?.paragraphProps?.tabs);\n        this.renderClass(elem, result);\n        this.renderStyleValues(elem.cssStyle, result);\n        this.renderCommonProperties(result.style, elem);\n        const numbering = elem.numbering ?? style?.paragraphProps?.numbering;\n        if (numbering) {\n            result.classList.add(this.numberingClass(numbering.id, numbering.level));\n        }\n        return result;\n    }\n    renderRunProperties(style, props) {\n        this.renderCommonProperties(style, props);\n    }\n    renderCommonProperties(style, props) {\n        if (props == null)\n            return;\n        if (props.color) {\n            style[\"color\"] = props.color;\n        }\n        if (props.fontSize) {\n            style[\"font-size\"] = props.fontSize;\n        }\n    }\n    renderHyperlink(elem) {\n        var result = this.renderContainer(elem, \"a\");\n        this.renderStyleValues(elem.cssStyle, result);\n        let href = '';\n        if (elem.id) {\n            const rel = this.document.documentPart.rels.find(it => it.id == elem.id && it.targetMode === \"External\");\n            href = rel?.target ?? href;\n        }\n        if (elem.anchor) {\n            href += `#${elem.anchor}`;\n        }\n        result.href = href;\n        return result;\n    }\n    renderSmartTag(elem) {\n        return this.renderContainer(elem, \"span\");\n    }\n    renderCommentRangeStart(commentStart) {\n        if (!this.options.renderComments)\n            return null;\n        const rng = new Range();\n        this.commentHighlight?.add(rng);\n        const result = this.createComment(`start of comment #${commentStart.id}`);\n        this.later(() => rng.setStart(result, 0));\n        this.commentMap[commentStart.id] = rng;\n        return result;\n    }\n    renderCommentRangeEnd(commentEnd) {\n        if (!this.options.renderComments)\n            return null;\n        const rng = this.commentMap[commentEnd.id];\n        const result = this.createComment(`end of comment #${commentEnd.id}`);\n        this.later(() => rng?.setEnd(result, 0));\n        return result;\n    }\n    renderCommentReference(commentRef) {\n        if (!this.options.renderComments)\n            return null;\n        var comment = this.document.commentsPart?.commentMap[commentRef.id];\n        if (!comment)\n            return null;\n        const frg = new DocumentFragment();\n        const commentRefEl = this.createElement(\"span\", { className: `${this.className}-comment-ref` }, ['💬']);\n        const commentsContainerEl = this.createElement(\"div\", { className: `${this.className}-comment-popover` });\n        this.renderCommentContent(comment, commentsContainerEl);\n        frg.appendChild(this.createComment(`comment #${comment.id} by ${comment.author} on ${comment.date}`));\n        frg.appendChild(commentRefEl);\n        frg.appendChild(commentsContainerEl);\n        return frg;\n    }\n    renderAltChunk(elem) {\n        if (!this.options.renderAltChunks)\n            return null;\n        var result = this.createElement(\"iframe\");\n        this.tasks.push(this.document.loadAltChunk(elem.id, this.currentPart).then(x => {\n            result.srcdoc = x;\n        }));\n        return result;\n    }\n    renderCommentContent(comment, container) {\n        container.appendChild(this.createElement('div', { className: `${this.className}-comment-author` }, [comment.author]));\n        container.appendChild(this.createElement('div', { className: `${this.className}-comment-date` }, [new Date(comment.date).toLocaleString()]));\n        this.renderElements(comment.children, container);\n    }\n    renderDrawing(elem) {\n        var result = this.renderContainer(elem, \"div\");\n        result.style.display = \"inline-block\";\n        result.style.position = \"relative\";\n        result.style.textIndent = \"0px\";\n        this.renderStyleValues(elem.cssStyle, result);\n        return result;\n    }\n    renderImage(elem) {\n        let result = this.createElement(\"img\");\n        let transform = elem.cssStyle?.transform;\n        this.renderStyleValues(elem.cssStyle, result);\n        if (elem.srcRect && elem.srcRect.some(x => x != 0)) {\n            var [left, top, right, bottom] = elem.srcRect;\n            transform = `scale(${1 / (1 - left - right)}, ${1 / (1 - top - bottom)})`;\n            result.style['clip-path'] = `rect(${(100 * top).toFixed(2)}% ${(100 * (1 - right)).toFixed(2)}% ${(100 * (1 - bottom)).toFixed(2)}% ${(100 * left).toFixed(2)}%)`;\n        }\n        if (elem.rotation)\n            transform = `rotate(${elem.rotation}deg) ${transform ?? ''}`;\n        result.style.transform = transform?.trim();\n        if (this.document) {\n            this.tasks.push(this.document.loadDocumentImage(elem.src, this.currentPart).then(x => {\n                result.src = x;\n            }));\n        }\n        return result;\n    }\n    renderText(elem) {\n        return this.htmlDocument.createTextNode(elem.text);\n    }\n    renderDeletedText(elem) {\n        return this.options.renderChanges ? this.renderText(elem) : null;\n    }\n    renderBreak(elem) {\n        if (elem.break == \"textWrapping\") {\n            return this.createElement(\"br\");\n        }\n        return null;\n    }\n    renderInserted(elem) {\n        if (this.options.renderChanges)\n            return this.renderContainer(elem, \"ins\");\n        return this.renderElements(elem.children);\n    }\n    renderDeleted(elem) {\n        if (this.options.renderChanges)\n            return this.renderContainer(elem, \"del\");\n        return null;\n    }\n    renderSymbol(elem) {\n        var span = this.createElement(\"span\");\n        span.style.fontFamily = elem.font;\n        span.innerHTML = `&#x${elem.char};`;\n        return span;\n    }\n    renderFootnoteReference(elem) {\n        var result = this.createElement(\"sup\");\n        this.currentFootnoteIds.push(elem.id);\n        result.textContent = `${this.currentFootnoteIds.length}`;\n        return result;\n    }\n    renderEndnoteReference(elem) {\n        var result = this.createElement(\"sup\");\n        this.currentEndnoteIds.push(elem.id);\n        result.textContent = `${this.currentEndnoteIds.length}`;\n        return result;\n    }\n    renderTab(elem) {\n        var tabSpan = this.createElement(\"span\");\n        tabSpan.innerHTML = \"&emsp;\";\n        if (this.options.experimental) {\n            tabSpan.className = this.tabStopClass();\n            var stops = findParent(elem, DomType.Paragraph)?.tabs;\n            this.currentTabs.push({ stops, span: tabSpan });\n        }\n        return tabSpan;\n    }\n    renderBookmarkStart(elem) {\n        return this.createElement(\"span\", { id: elem.name });\n    }\n    renderRun(elem) {\n        if (elem.fieldRun)\n            return null;\n        const result = this.createElement(\"span\");\n        if (elem.id)\n            result.id = elem.id;\n        this.renderClass(elem, result);\n        this.renderStyleValues(elem.cssStyle, result);\n        if (elem.verticalAlign) {\n            const wrapper = this.createElement(elem.verticalAlign);\n            this.renderElements(elem.children, wrapper);\n            result.appendChild(wrapper);\n        }\n        else {\n            this.renderElements(elem.children, result);\n        }\n        return result;\n    }\n    renderTable(elem) {\n        let result = this.createElement(\"table\");\n        this.tableCellPositions.push(this.currentCellPosition);\n        this.tableVerticalMerges.push(this.currentVerticalMerge);\n        this.currentVerticalMerge = {};\n        this.currentCellPosition = { col: 0, row: 0 };\n        if (elem.columns)\n            result.appendChild(this.renderTableColumns(elem.columns));\n        this.renderClass(elem, result);\n        this.renderElements(elem.children, result);\n        this.renderStyleValues(elem.cssStyle, result);\n        this.currentVerticalMerge = this.tableVerticalMerges.pop();\n        this.currentCellPosition = this.tableCellPositions.pop();\n        return result;\n    }\n    renderTableColumns(columns) {\n        let result = this.createElement(\"colgroup\");\n        for (let col of columns) {\n            let colElem = this.createElement(\"col\");\n            if (col.width)\n                colElem.style.width = col.width;\n            result.appendChild(colElem);\n        }\n        return result;\n    }\n    renderTableRow(elem) {\n        let result = this.createElement(\"tr\");\n        this.currentCellPosition.col = 0;\n        if (elem.gridBefore)\n            result.appendChild(this.renderTableCellPlaceholder(elem.gridBefore));\n        this.renderClass(elem, result);\n        this.renderElements(elem.children, result);\n        this.renderStyleValues(elem.cssStyle, result);\n        if (elem.gridAfter)\n            result.appendChild(this.renderTableCellPlaceholder(elem.gridAfter));\n        this.currentCellPosition.row++;\n        return result;\n    }\n    renderTableCellPlaceholder(colSpan) {\n        const result = this.createElement(\"td\", { colSpan });\n        result.style['border'] = 'none';\n        return result;\n    }\n    renderTableCell(elem) {\n        let result = this.renderContainer(elem, \"td\");\n        const key = this.currentCellPosition.col;\n        if (elem.verticalMerge) {\n            if (elem.verticalMerge == \"restart\") {\n                this.currentVerticalMerge[key] = result;\n                result.rowSpan = 1;\n            }\n            else if (this.currentVerticalMerge[key]) {\n                this.currentVerticalMerge[key].rowSpan += 1;\n                result.style.display = \"none\";\n            }\n        }\n        else {\n            this.currentVerticalMerge[key] = null;\n        }\n        this.renderClass(elem, result);\n        this.renderStyleValues(elem.cssStyle, result);\n        if (elem.span)\n            result.colSpan = elem.span;\n        this.currentCellPosition.col += result.colSpan;\n        return result;\n    }\n    renderVmlPicture(elem) {\n        return this.renderContainer(elem, \"div\");\n    }\n    renderVmlElement(elem) {\n        var container = this.createSvgElement(\"svg\");\n        container.setAttribute(\"style\", elem.cssStyleText);\n        const result = this.renderVmlChildElement(elem);\n        if (elem.imageHref?.id) {\n            this.tasks.push(this.document?.loadDocumentImage(elem.imageHref.id, this.currentPart)\n                .then(x => result.setAttribute(\"href\", x)));\n        }\n        container.appendChild(result);\n        requestAnimationFrame(() => {\n            const bb = container.firstElementChild.getBBox();\n            container.setAttribute(\"width\", `${Math.ceil(bb.x + bb.width)}`);\n            container.setAttribute(\"height\", `${Math.ceil(bb.y + bb.height)}`);\n        });\n        return container;\n    }\n    renderVmlChildElement(elem) {\n        const result = this.createSvgElement(elem.tagName);\n        Object.entries(elem.attrs).forEach(([k, v]) => result.setAttribute(k, v));\n        for (let child of elem.children) {\n            if (child.type == DomType.VmlElement) {\n                result.appendChild(this.renderVmlChildElement(child));\n            }\n            else {\n                result.appendChild(...asArray(this.renderElement(child)));\n            }\n        }\n        return result;\n    }\n    renderMmlRadical(elem) {\n        const base = elem.children.find(el => el.type == DomType.MmlBase);\n        if (elem.props?.hideDegree) {\n            return this.createElementNS(ns.mathML, \"msqrt\", null, this.renderElements([base]));\n        }\n        const degree = elem.children.find(el => el.type == DomType.MmlDegree);\n        return this.createElementNS(ns.mathML, \"mroot\", null, this.renderElements([base, degree]));\n    }\n    renderMmlDelimiter(elem) {\n        const children = [];\n        children.push(this.createElementNS(ns.mathML, \"mo\", null, [elem.props.beginChar ?? '(']));\n        children.push(...this.renderElements(elem.children));\n        children.push(this.createElementNS(ns.mathML, \"mo\", null, [elem.props.endChar ?? ')']));\n        return this.createElementNS(ns.mathML, \"mrow\", null, children);\n    }\n    renderMmlNary(elem) {\n        const children = [];\n        const grouped = keyBy(elem.children, x => x.type);\n        const sup = grouped[DomType.MmlSuperArgument];\n        const sub = grouped[DomType.MmlSubArgument];\n        const supElem = sup ? this.createElementNS(ns.mathML, \"mo\", null, asArray(this.renderElement(sup))) : null;\n        const subElem = sub ? this.createElementNS(ns.mathML, \"mo\", null, asArray(this.renderElement(sub))) : null;\n        const charElem = this.createElementNS(ns.mathML, \"mo\", null, [elem.props?.char ?? '\\u222B']);\n        if (supElem || subElem) {\n            children.push(this.createElementNS(ns.mathML, \"munderover\", null, [charElem, subElem, supElem]));\n        }\n        else if (supElem) {\n            children.push(this.createElementNS(ns.mathML, \"mover\", null, [charElem, supElem]));\n        }\n        else if (subElem) {\n            children.push(this.createElementNS(ns.mathML, \"munder\", null, [charElem, subElem]));\n        }\n        else {\n            children.push(charElem);\n        }\n        children.push(...this.renderElements(grouped[DomType.MmlBase].children));\n        return this.createElementNS(ns.mathML, \"mrow\", null, children);\n    }\n    renderMmlPreSubSuper(elem) {\n        const children = [];\n        const grouped = keyBy(elem.children, x => x.type);\n        const sup = grouped[DomType.MmlSuperArgument];\n        const sub = grouped[DomType.MmlSubArgument];\n        const supElem = sup ? this.createElementNS(ns.mathML, \"mo\", null, asArray(this.renderElement(sup))) : null;\n        const subElem = sub ? this.createElementNS(ns.mathML, \"mo\", null, asArray(this.renderElement(sub))) : null;\n        const stubElem = this.createElementNS(ns.mathML, \"mo\", null);\n        children.push(this.createElementNS(ns.mathML, \"msubsup\", null, [stubElem, subElem, supElem]));\n        children.push(...this.renderElements(grouped[DomType.MmlBase].children));\n        return this.createElementNS(ns.mathML, \"mrow\", null, children);\n    }\n    renderMmlGroupChar(elem) {\n        const tagName = elem.props.verticalJustification === \"bot\" ? \"mover\" : \"munder\";\n        const result = this.renderContainerNS(elem, ns.mathML, tagName);\n        if (elem.props.char) {\n            result.appendChild(this.createElementNS(ns.mathML, \"mo\", null, [elem.props.char]));\n        }\n        return result;\n    }\n    renderMmlBar(elem) {\n        const result = this.renderContainerNS(elem, ns.mathML, \"mrow\");\n        switch (elem.props.position) {\n            case \"top\":\n                result.style.textDecoration = \"overline\";\n                break;\n            case \"bottom\":\n                result.style.textDecoration = \"underline\";\n                break;\n        }\n        return result;\n    }\n    renderMmlRun(elem) {\n        const result = this.createElementNS(ns.mathML, \"ms\", null, this.renderElements(elem.children));\n        this.renderClass(elem, result);\n        this.renderStyleValues(elem.cssStyle, result);\n        return result;\n    }\n    renderMllList(elem) {\n        const result = this.createElementNS(ns.mathML, \"mtable\");\n        this.renderClass(elem, result);\n        this.renderStyleValues(elem.cssStyle, result);\n        for (let child of this.renderElements(elem.children)) {\n            result.appendChild(this.createElementNS(ns.mathML, \"mtr\", null, [\n                this.createElementNS(ns.mathML, \"mtd\", null, [child])\n            ]));\n        }\n        return result;\n    }\n    renderStyleValues(style, ouput) {\n        for (let k in style) {\n            if (k.startsWith(\"$\")) {\n                ouput.setAttribute(k.slice(1), style[k]);\n            }\n            else {\n                ouput.style[k] = style[k];\n            }\n        }\n    }\n    renderClass(input, ouput) {\n        if (input.className)\n            ouput.className = input.className;\n        if (input.styleName)\n            ouput.classList.add(this.processStyleName(input.styleName));\n    }\n    findStyle(styleName) {\n        return styleName && this.styleMap?.[styleName];\n    }\n    numberingClass(id, lvl) {\n        return `${this.className}-num-${id}-${lvl}`;\n    }\n    tabStopClass() {\n        return `${this.className}-tab-stop`;\n    }\n    styleToString(selectors, values, cssText = null) {\n        let result = `${selectors} {\\r\\n`;\n        for (const key in values) {\n            if (key.startsWith('$'))\n                continue;\n            result += `  ${key}: ${values[key]};\\r\\n`;\n        }\n        if (cssText)\n            result += cssText;\n        return result + \"}\\r\\n\";\n    }\n    numberingCounter(id, lvl) {\n        return `${this.className}-num-${id}-${lvl}`;\n    }\n    levelTextToContent(text, suff, id, numformat) {\n        const suffMap = {\n            \"tab\": \"\\\\9\",\n            \"space\": \"\\\\a0\",\n        };\n        var result = text.replace(/%\\d*/g, s => {\n            let lvl = parseInt(s.substring(1), 10) - 1;\n            return `\"counter(${this.numberingCounter(id, lvl)}, ${numformat})\"`;\n        });\n        return `\"${result}${suffMap[suff] ?? \"\"}\"`;\n    }\n    numFormatToCssValue(format) {\n        var mapping = {\n            none: \"none\",\n            bullet: \"disc\",\n            decimal: \"decimal\",\n            lowerLetter: \"lower-alpha\",\n            upperLetter: \"upper-alpha\",\n            lowerRoman: \"lower-roman\",\n            upperRoman: \"upper-roman\",\n            decimalZero: \"decimal-leading-zero\",\n            aiueo: \"katakana\",\n            aiueoFullWidth: \"katakana\",\n            chineseCounting: \"simp-chinese-informal\",\n            chineseCountingThousand: \"simp-chinese-informal\",\n            chineseLegalSimplified: \"simp-chinese-formal\",\n            chosung: \"hangul-consonant\",\n            ideographDigital: \"cjk-ideographic\",\n            ideographTraditional: \"cjk-heavenly-stem\",\n            ideographLegalTraditional: \"trad-chinese-formal\",\n            ideographZodiac: \"cjk-earthly-branch\",\n            iroha: \"katakana-iroha\",\n            irohaFullWidth: \"katakana-iroha\",\n            japaneseCounting: \"japanese-informal\",\n            japaneseDigitalTenThousand: \"cjk-decimal\",\n            japaneseLegal: \"japanese-formal\",\n            thaiNumbers: \"thai\",\n            koreanCounting: \"korean-hangul-formal\",\n            koreanDigital: \"korean-hangul-formal\",\n            koreanDigital2: \"korean-hanja-informal\",\n            hebrew1: \"hebrew\",\n            hebrew2: \"hebrew\",\n            hindiNumbers: \"devanagari\",\n            ganada: \"hangul\",\n            taiwaneseCounting: \"cjk-ideographic\",\n            taiwaneseCountingThousand: \"cjk-ideographic\",\n            taiwaneseDigital: \"cjk-decimal\",\n        };\n        return mapping[format] ?? format;\n    }\n    refreshTabStops() {\n        if (!this.options.experimental)\n            return;\n        setTimeout(() => {\n            const pixelToPoint = computePixelToPoint();\n            for (let tab of this.currentTabs) {\n                updateTabStop(tab.span, tab.stops, this.defaultTabSize, pixelToPoint);\n            }\n        }, 500);\n    }\n    createElementNS(ns, tagName, props, children) {\n        var result = ns ? this.htmlDocument.createElementNS(ns, tagName) : this.htmlDocument.createElement(tagName);\n        Object.assign(result, props);\n        children && appendChildren(result, children);\n        return result;\n    }\n    createElement(tagName, props, children) {\n        return this.createElementNS(undefined, tagName, props, children);\n    }\n    createSvgElement(tagName, props, children) {\n        return this.createElementNS(ns.svg, tagName, props, children);\n    }\n    createStyleElement(cssText) {\n        return this.createElement(\"style\", { innerHTML: cssText });\n    }\n    createComment(text) {\n        return this.htmlDocument.createComment(text);\n    }\n    later(func) {\n        this.postRenderTasks.push(func);\n    }\n}\nfunction removeAllElements(elem) {\n    elem.innerHTML = '';\n}\nfunction appendChildren(elem, children) {\n    children.forEach(c => elem.appendChild(isString(c) ? document.createTextNode(c) : c));\n}\nfunction findParent(elem, type) {\n    var parent = elem.parent;\n    while (parent != null && parent.type != type)\n        parent = parent.parent;\n    return parent;\n}\n\nconst defaultOptions = {\n    ignoreHeight: false,\n    ignoreWidth: false,\n    ignoreFonts: false,\n    breakPages: true,\n    debug: false,\n    experimental: false,\n    className: \"docx\",\n    inWrapper: true,\n    hideWrapperOnPrint: false,\n    trimXmlDeclaration: true,\n    ignoreLastRenderedPageBreak: true,\n    renderHeaders: true,\n    renderFooters: true,\n    renderFootnotes: true,\n    renderEndnotes: true,\n    useBase64URL: false,\n    renderChanges: false,\n    renderComments: false,\n    renderAltChunks: true\n};\nfunction parseAsync(data, userOptions) {\n    const ops = { ...defaultOptions, ...userOptions };\n    return WordDocument.load(data, new DocumentParser(ops), ops);\n}\nasync function renderDocument(document, bodyContainer, styleContainer, userOptions) {\n    const ops = { ...defaultOptions, ...userOptions };\n    const renderer = new HtmlRenderer(window.document);\n    return await renderer.render(document, bodyContainer, styleContainer, ops);\n}\nasync function renderAsync(data, bodyContainer, styleContainer, userOptions) {\n    const doc = await parseAsync(data, userOptions);\n    await renderDocument(doc, bodyContainer, styleContainer, userOptions);\n    return doc;\n}\n\n\n//# sourceMappingURL=docx-preview.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/docx-preview/dist/docx-preview.mjs\n");

/***/ })

};
;