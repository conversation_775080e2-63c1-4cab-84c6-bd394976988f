import React, { useEffect, useRef, useState } from 'react';
import { Dialog } from '@headlessui/react';
import dynamic from 'next/dynamic';
import { API_BASE_URL, getSecureToken } from '@/lib/api';
// dynamic import for react-pdf viewer in modal
// @ts-ignore
const Document = dynamic(() => import('react-pdf').then(mod => mod.Document), { ssr: false });
// @ts-ignore
const Page = dynamic(() => import('react-pdf').then(mod => mod.Page), { ssr: false });
// pdf.js worker not needed because we use iframe viewer for PDFs

interface Props {
  open: boolean;
  url: string;
  type: string; // "image" | "file"
  onClose: () => void;
}

function getExt(url: string) {
  return url.split('?')[0].split('#')[0].split('.').pop()?.toLowerCase() || '';
}

const AttachmentModal: React.FC<Props> = ({ open, url, type, onClose }) => {
  const docxContainerRef = useRef<HTMLDivElement>(null);
  const [docxError, setDocxError] = useState<string | null>(null);
  // Derive download URL if MinIO metadata
  let meta: any = null;
  try { meta = JSON.parse(url); } catch {}
  const fileUrl = (() => {
    if (meta?.minio_bucket && meta?.minio_object_key) {
      const base = `${API_BASE_URL}/chat/attachments/download?bucket_name=${meta.minio_bucket}&object_key=${encodeURIComponent(
        meta.minio_object_key
      )}`;
      const token = getSecureToken();
      return token ? `${base}&token=${token}` : base;
    }
    return url;
  })();
  // Compute extension from resolved fileUrl
  const ext = getExt(fileUrl);

  // No PDF fetch logic needed now

  // Fetch & render DOCX when needed
  useEffect(() => {
    if (!open) return;
    if (ext !== 'docx') return;
    setDocxError(null);
    if (!docxContainerRef.current) return;
    docxContainerRef.current.innerHTML = '';
    (async () => {
      try {
        const resp = await fetch(fileUrl);
        if (!resp.ok) throw new Error('network');
        const blob = await resp.blob();
        const { default: docxPreview, renderAsync } = await import('docx-preview');
        await renderAsync(blob, docxContainerRef.current!, undefined, {
          className: 'docx',
          inWrapper: false,
        });
      } catch (err) {
        setDocxError('Failed to preview document');
      }
    })();
  }, [open, fileUrl]);

  const renderContent = () => {
    if (type === 'image') {
      const filename = fileUrl.split('?')[0].split('#')[0].split('/').pop() || 'file';
      return <img src={fileUrl} alt="full" className="max-h-[80vh] object-contain" />;
    }

    if (ext === 'pdf') {
      return <iframe src={fileUrl} className="w-full h-[80vh]" title="pdf-viewer" />;
    }

    if (['docx', 'doc', 'ppt', 'pptx', 'xls', 'xlsx'].includes(ext)) {
      // Use Office online viewer for Office documents
      return (
        <iframe
          src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`}
          className="w-full h-[80vh]"
          title={`${ext}-viewer`}
          frameBorder="0"
        />
      );
    }

    // Fallback – show in iframe
    return <iframe src={fileUrl} className="w-full h-[80vh]" title="file-viewer" />;
  };

  return (
    <Dialog open={open} onClose={onClose} className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 text-center">
        <div className="fixed inset-0 bg-black opacity-50" aria-hidden="true" />
        <span className="inline-block h-screen align-middle" aria-hidden="true">
          &#8203;
        </span>
        <div className="inline-block bg-white p-4 rounded-lg shadow-xl max-w-5xl w-full align-middle relative overflow-hidden">
          <button onClick={onClose} className="absolute top-2 right-2 text-gray-600 hover:text-gray-800">✕</button>
          {renderContent()}
        </div>
      </div>
    </Dialog>
  );
};

export default AttachmentModal; 