#!/usr/bin/env python3
"""
Data Ingestion Client for AISaarthi

Direct client for processing documents using the data ingestion pipeline.
Handles document processing, infrastructure setup, and department-specific configurations.
"""

import os
import sys
import tempfile
import hashlib
import logging
import traceback
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import asyncio

# Import existing pipeline components
from pipeline import MultimodalDataPipeline
from config import ProcessingConfig
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, SparseVectorParams, HnswConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataIngestionProcessor:
    """Handles document processing with infrastructure setup"""
    
    def __init__(self):
        logger.info("Data Ingestion Processor initialized")
    async def ensure_infrastructure(self, username: str, collection_name: str, 
                            qdrant_url: str, 
                            qdrant_api_key: Optional[str] = None,
                            prefer_grpc: bool = True, grpc_port: int = 6334) -> bool:
        """
        Ensure Qdrant collection exists for a user
        """
        try:
            # Initialize Qdrant client
            qdrant_client = QdrantClient(
                url=qdrant_url,
                api_key=qdrant_api_key,
                prefer_grpc=prefer_grpc,
                grpc_port=grpc_port
            )
            
            # Create Qdrant collection if it doesn't exist
            collections = qdrant_client.get_collections().collections
            collection_names = [c.name for c in collections]
            
            if collection_name not in collection_names:
                qdrant_client.create_collection(
                    collection_name=collection_name,
                    vectors_config={
                        "dense": VectorParams(
                            size=768,  # all-mpnet-base-v2 dimension
                            distance=Distance.DOT
                        )
                        },
                        sparse_vectors_config={
                            "sparse": SparseVectorParams()
                        },
                    
                        hnsw_config={
                            "m":32,
                            "ef_construct":250,
                            "full_scan_threshold":1000,
                        }
                )
                logger.info(f"Created Qdrant collection: {collection_name}")
            else:
                logger.info(f"Qdrant collection already exists: {collection_name}")
            
            # PostgreSQL schema is managed by backend's SQLAlchemy models via init_db.py
            logger.info("PostgreSQL schema managed by backend SQLAlchemy models")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to ensure infrastructure for {username}: {e}")
            logger.error(traceback.format_exc())
            return False
    
    # SQLite schema helper removed – handled via PostgreSQL above
    
    def validate_file_path(self, file_path: str) -> bool:
        """Validate that the file exists and is accessible"""
        try:
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                logger.error(f"File does not exist: {file_path}")
                return False
            
            if not file_path_obj.is_file():
                logger.error(f"Path is not a file: {file_path}")
                return False
            
            # Check if file is readable
            with open(file_path, 'rb') as f:
                f.read(1)  # Try to read first byte
            
            logger.info(f"File validated: {file_path}")
            return True
        except Exception as e:
            logger.error(f"File validation failed for {file_path}: {e}")
            return False

processor = DataIngestionProcessor()


async def upload_and_process_files(
    files: List[Any],  # File-like objects  
    username: str,
    collection_name: str,
    processing_config: Dict[str, Any],
    gemini_api_key: str,
    callback_url: Optional[str] = None,
    callback_token: Optional[str] = None,
    user_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    Upload files and process them through the data ingestion pipeline
    """
    import tempfile
    import shutil
    import time
    
    function_entry_time = time.perf_counter()
    # logger.info(f"🔄 FUNCTION_ENTRY_TIMING: upload_and_process_files() entered at {function_entry_time}")
    
    start_time = datetime.now()
    results = []
    
    try:
        logger.info(f"Processing {len(files)} uploaded files for user: {username}")
        
        # Ensure infrastructure exists
        infrastructure_ready = await processor.ensure_infrastructure(
            username=username,
            collection_name=collection_name,
            qdrant_url=processing_config.get("qdrant_url"),
            qdrant_api_key=processing_config.get("qdrant_api_key"),
            prefer_grpc=processing_config.get("prefer_grpc", True),
            grpc_port=processing_config.get("grpc_port")
        )
        
        if not infrastructure_ready:
            return {
                "success": False,
                "error": f"Failed to setup infrastructure for user: {username}",
                "processed_documents": 0,
                "failed_documents": len(files),
                "results": [],
                "total_processing_time": 0.0
            }
        
        # Create pipeline configuration
        pipeline_config = ProcessingConfig(
            gemini_api_key=gemini_api_key,
            qdrant_url=processing_config.get("qdrant_url"),
            prefer_grpc=processing_config.get("prefer_grpc", True),
            grpc_port=processing_config.get("grpc_port"),
            qdrant_api_key=processing_config.get("qdrant_api_key"),
            DATABASE_URL=processing_config.get("DATABASE_URL"),
            embedding_api_url=processing_config.get("embedding_api_url"),
            use_custom_splitter=processing_config.get("use_custom_splitter", False),
            use_unstructured=processing_config.get("use_unstructured", False),
            chunk_size=processing_config.get("chunk_size", 750),
            chunk_overlap=processing_config.get("chunk_overlap", 150),
            llm_model=processing_config.get("llm_model", "gemini-2.5-flash-lite"),
            vision_model=processing_config.get("vision_model", "gemini-2.5-flash")
        )
        import time
        
        # Process each uploaded file
        processed_count = 0
        failed_count = 0
    

        with tempfile.TemporaryDirectory() as temp_dir:

            for i, file in enumerate(files):
                doc_start_time = datetime.now()
                try:
                    # Handle only two source types: FastAPI UploadFile and MinIO stream dict
                    if hasattr(file, 'filename'):
                        # ----- Case A: FastAPI UploadFile -----
                        filename = file.filename
                        file_bytes = await file.read() if hasattr(file, 'read') else file.file.read()
                        tmp_path = Path(temp_dir) / filename
                        tmp_path.write_bytes(file_bytes)


                        space_id = file.get("space_id", 0)
                        if space_id == 0:
                            raise ValueError(f"Space ID is 0 for file {filename}")
                        logger.info(f"Space ID from file: {space_id}")
                        await pipeline.process_file(str(tmp_path), collection_name, space_id)

                    elif isinstance(file, dict) and file.get('source') == 'stream':
                        # ----- Case B: MinIO stream dict -----
                        filename = file.get('filename', f'file_{i+1}')
                        stream = file['stream']


                        space_id = file.get("space_id", 0)
                        if space_id == 0:
                            raise ValueError(f"Space ID is 0 for file {filename}")
                        logger.info(f"Space ID from file: {space_id}")
                        await pipeline.process_file(stream, collection_name, space_id)

                    else:
                        logger.error('Unsupported file object in upload_and_process_files')
                        raise ValueError('Unsupported file object')

                    processed_count += 1

                except Exception as e:
                    logger.error(f'Failed to process file {i+1}: {e}')
                    failed_count += 1
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()

        logger.info(f"File upload processing completed: {processed_count} success, {failed_count} failed, {total_time:.2f}s total")
        
        return {
            "success": failed_count == 0,
            "processed_documents": processed_count,
            "failed_documents": failed_count,
            "results": results,
            "total_processing_time": total_time
        }
        
    except Exception as e:
        logger.error(f"Error in upload_and_process_files: {e}")
        logger.error(traceback.format_exc())
        return {
            "success": False,
            "error": f"File upload processing failed: {str(e)}",
            "processed_documents": 0,
            "failed_documents": len(files),
            "results": [],
            "total_processing_time": 0.0
        }

def health_check() -> Dict[str, Any]:
    """Health check endpoint"""
    try:
        import requests

        # Check embedding service health
        embedding_healthy = False
        try:
            embedding_url = "http://**************:8015/health"
            response = requests.get(embedding_url, timeout=10)
            embedding_healthy = response.status_code == 200
        except Exception as e:
            logger.warning(f"Embedding service health check failed: {e}")

        # Check if pipeline can be created (always true since no global caching)
        models_loaded = True  # Models are loaded on-demand per request
        pipeline_ready = models_loaded

        # Overall status based on external services
        status = "healthy" if embedding_healthy else "partial"

        return {
            "status": status,
            "models_loaded": models_loaded,
            "pipeline_ready": pipeline_ready,
            "embedding_service": embedding_healthy
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "models_loaded": False,
            "pipeline_ready": False,
            "embedding_service": False
        } 