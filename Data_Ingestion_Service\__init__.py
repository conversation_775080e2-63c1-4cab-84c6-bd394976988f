import time
import logging

module_import_start = time.perf_counter()
# logger = logging.getLogger(__name__)
# logger.info(f"🔄 MODULE_IMPORT_TIMING: Data_Ingestion module import started at {module_import_start}")

from config import ProcessingConfig
# config_time = time.perf_counter()
# logger.info(f"🔄 MODULE_IMPORT_TIMING: config imported in {config_time - module_import_start:.3f}s")

from pipeline import MultimodalDataPipeline
# pipeline_time = time.perf_counter()
# logger.info(f"🔄 MODULE_IMPORT_TIMING: pipeline imported in {pipeline_time - config_time:.3f}s")

from embedding_client import EmbeddingClient, EmbeddingConfig, DenseEmbeddingWrapper, SparseEmbeddingWrapper
# embedding_time = time.perf_counter()
# logger.info(f"🔄 MODULE_IMPORT_TIMING: embedding_client imported in {embedding_time - pipeline_time:.3f}s")

# COMMENTED OUT: unstructured takes 12+ seconds to load
# from .unstructure import PDFContentExtractor
# unstructure_time = time.perf_counter()
# logger.info(f"🔄 MODULE_IMPORT_TIMING: unstructure imported in {unstructure_time - embedding_time:.3f}s")

from core import custom_markdown_splitter
# core_time = time.perf_counter()
# logger.info(f"🔄 MODULE_IMPORT_TIMING: core imported in {core_time - unstructure_time:.3f}s")

from data_ingestion_pipeline import DataIngestionProcessor, upload_and_process_files, health_check
# pipeline_module_time = time.perf_counter()
# logger.info(f"🔄 MODULE_IMPORT_TIMING: data_ingestion_pipeline imported in {pipeline_module_time - core_time:.3f}s")

# total_import_time = time.perf_counter() - module_import_start
# logger.info(f"🔄 MODULE_IMPORT_TIMING: Total Data_Ingestion module import time: {total_import_time:.3f}s")
__all__=[
    "ProcessingConfig",
    "MultimodalDataPipeline",
    "EmbeddingClient",
    "EmbeddingConfig",
    "DenseEmbeddingWrapper",
    "SparseEmbeddingWrapper",
    "custom_markdown_splitter",
    # "PDFContentExtractor",  # Commented out for performance
    "DataIngestionProcessor",
    "upload_and_process_files",
    "health_check"
]

__version__ = "0.1.0"
