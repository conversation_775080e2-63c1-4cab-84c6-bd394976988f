/**
 * AI Chat Components Module
 * 
 * This module contains all components related to the AI agent chat functionality.
 * It provides a centralized export point for all chat-related components.
 */

// Core chat components
export { default as ChatBox } from './ChatBox';
export { default as ChatInput } from './ChatInput';
export { default as ChatSidebar } from './ChatSidebar';
export { default as ChatPageContent } from './ChatPageContent';

// Chat management and history
export { default as ChatHistoryManager } from './ChatHistoryManager';
export { clearAgentChatHistory } from './ChatHistoryManager';

// Agent and prompt selection
export { default as AgentSelector } from './AgentSelector';
export { default as PromptGallery } from './PromptGallery';

// Feedback and interaction
export { default as FeedbackWidget } from './FeedbackWidget';

// Display components
export { default as SourcesDisplay } from './SourcesDisplay';
export { default as ReferenceParser } from './ReferenceParser';
export { default as SharedResponseDisplay } from './SharedResponseDisplay';

