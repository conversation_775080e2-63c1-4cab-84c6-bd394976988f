import os
import asyncpg
import logging

# DSN can be overridden via environment variable POSTGRES_DSN.
_POSTGRES_DSN = os.getenv("DATABASE_URL")
_logger = logging.getLogger(__name__)

async def connect(dsn: str | None = None):
    """Return a new asyncpg connection.
    A fresh connection is created on each call (no pooling by design).
    Caller must close it with ``await conn.close()``.
    """
    _dsn = dsn or _POSTGRES_DSN
    # Convert SQLAlchemy-style URL to standard PostgreSQL DSN for asyncpg
    # asyncpg doesn't understand the +asyncpg driver specification
    if _dsn and "postgresql+asyncpg://" in _dsn:
        _dsn = _dsn.replace("postgresql+asyncpg://", "postgresql://")
    _logger.info(f"asyncpg.connect using DSN: {_dsn}")
    return await asyncpg.connect(_dsn)

async def execute_many(conn, sql: str, records: list):
    """Execute many statements (bulk insert/update)."""
    await conn.executemany(sql, records)

async def fetch(conn, sql: str, *args):
    """Run a query and return rows list."""
    return await conn.fetch(sql, *args)
