'use client';

import React from 'react';
import Toast from './Toast';
import { useNotification, Notification } from '@/contexts';

const ToastContainer: React.FC = () => {
  const { state, removeNotification, handleNotificationClick } = useNotification();
  const { notifications } = state;

  if (notifications.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-4 max-h-screen overflow-hidden">
      {notifications.map((notification: Notification) => (
        <Toast
          key={notification.id}
          notification={notification}
          onClose={() => removeNotification(notification.id)}
          onClick={() => handleNotificationClick(notification)}
        />
      ))}
    </div>
  );
};

export default ToastContainer; 