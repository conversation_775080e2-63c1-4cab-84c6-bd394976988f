"""
OCR Processor Utility

Provides fast OCR capabilities using Tesseract for extracting text from images.
Optimized for document images with printed text (not handwriting).
"""

import logging
import pytesseract
import cv2
import numpy as np
from PIL import Image
import io
from typing import Optional, Dict, Any
import os

logger = logging.getLogger(__name__)


class OCRProcessor:
    """Fast OCR processor using Tesseract for document images"""

    def __init__(self, tesseract_cmd: Optional[str] = None):
        """
        Initialize OCR processor

        Args:
            tesseract_cmd: Path to tesseract executable (optional, uses system default)
        """
        # Set tesseract path for Windows if not explicitly provided
        if tesseract_cmd:
            pytesseract.pytesseract.tesseract_cmd = tesseract_cmd
        elif os.name == 'nt':  # Windows
            # Try to find tesseract in system PATH first
            import shutil
            tesseract_path = shutil.which('tesseract')
            
            # Common Windows installation paths
            possible_paths = []
            if tesseract_path:
                possible_paths.append(tesseract_path)
            
            possible_paths.extend([
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe',
                'tesseract'  # Fallback to PATH
            ])
            
            for path in possible_paths:
                try:
                    pytesseract.pytesseract.tesseract_cmd = path
                    pytesseract.get_tesseract_version()
                    logger.info(f"Found Tesseract at: {path}")
                    break
                except Exception:
                    continue

        # Test Tesseract availability
        try:
            version = pytesseract.get_tesseract_version()
            logger.info(f"Tesseract version: {version}")
        except Exception as e:
            logger.warning(f"Tesseract not available: {e}")

    def extract_text_from_image(self, image_bytes: bytes, lang: str = 'eng') -> Dict[str, Any]:
        """
        Extract text from image bytes

        Args:
            image_bytes: Raw image bytes
            lang: Language code for OCR (default: 'eng')

        Returns:
            Dict with 'text', 'confidence', and 'processing_time'
        """
        import time
        start_time = time.time()

        try:
            # Convert bytes to PIL Image
            image = Image.open(io.BytesIO(image_bytes))

            # Convert to RGB if necessary
            if image.mode not in ('L', 'RGB'):
                image = image.convert('RGB')

            # Convert PIL to numpy array for OpenCV preprocessing
            img_array = np.array(image)

            # Preprocessing for better OCR results
            processed_img = self._preprocess_image(img_array)

            # Extract text with confidence scores
            data = pytesseract.image_to_data(processed_img, lang=lang, output_type=pytesseract.Output.DICT)

            # Combine text pieces
            text_pieces = []
            confidences = []

            for i, conf in enumerate(data['conf']):
                if int(conf) > 0:  # Only include text with positive confidence
                    text_pieces.append(data['text'][i])
                    confidences.append(int(conf))

            extracted_text = ' '.join(text_pieces).strip()
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0

            processing_time = time.time() - start_time

            logger.info(f"OCR extracted {len(extracted_text)} characters with {avg_confidence:.1f}% confidence in {processing_time:.3f}s")

            return {
                'text': extracted_text,
                'confidence': avg_confidence,
                'processing_time': processing_time,
                'has_text': len(extracted_text.strip()) > 0
            }

        except Exception as e:
            logger.error(f"OCR processing failed: {e}")
            return {
                'text': '',
                'confidence': 0.0,
                'processing_time': time.time() - start_time,
                'has_text': False,
                'error': str(e)
            }

    def _preprocess_image(self, img_array: np.ndarray) -> np.ndarray:
        """
        Preprocess image for better OCR results

        Args:
            img_array: Input image as numpy array

        Returns:
            Preprocessed image
        """
        # Convert to grayscale if needed
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array

        # Apply slight blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)

        # Apply threshold to get binary image
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        return thresh

    def extract_text_from_base64(self, base64_string: str, lang: str = 'eng') -> Dict[str, Any]:
        """
        Extract text from base64 encoded image

        Args:
            base64_string: Base64 encoded image
            lang: Language code for OCR

        Returns:
            Dict with OCR results
        """
        import base64

        try:
            # Decode base64 to bytes
            image_bytes = base64.b64decode(base64_string)
            return self.extract_text_from_image(image_bytes, lang)
        except Exception as e:
            logger.error(f"Base64 decoding failed: {e}")
            return {
                'text': '',
                'confidence': 0.0,
                'processing_time': 0.0,
                'has_text': False,
                'error': str(e)
            }
