"""
Database migration: Create normalized tables for agent chat context snapshots.

This script creates two tables used to persist the exact context used for each
agent response, without duplicating Q/A pairs:

- `agent_chat_context_snapshot`: One row per assistant response (snapshot),
  storing meta such as mode, whether a summary was used, token statistics, and
  an optional `parent_context_id` to chain progressive summaries.

- `agent_chat_context_interactions`: A mapping table linking a snapshot to the
  specific prior `expert_chat_interactions` rows (each representing a Q/A pair)
  that were included in the raw tail window. The `position` column preserves
  ordered inclusion for efficient reads.

Design goals:
- Normalized storage: reference prior interactions by ID (no duplication).
- Idempotence and safety: existence checks and idempotent index creation allow
  safe re-runs.
- Environment safety: the `engine` is imported from `app.database`, which is
  configured via environment variables (e.g. `ENV`/`DATABASE_URL_{ENV}`), so the
  migration targets the database for the active environment only.

Run this script directly to apply the migration:

    python Backend/migration/add_context_snapshot_migration.py

It returns a zero exit code on success and a non-zero exit code on failure.
"""

import sys
import os

# Ensure Backend directory (containing the 'app' package) is on sys.path
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
BACKEND_DIR = os.path.dirname(CURRENT_DIR)
if BACKEND_DIR not in sys.path:
    sys.path.insert(0, BACKEND_DIR)

from sqlalchemy import text
from app.database import engine


def migrate_create_context_snapshot_tables() -> bool:
    """Create context snapshot tables and supporting indexes if absent.

    This function is designed to be idempotent and safe to re-run. It uses
    transactional DDL where appropriate and explicitly checks for the existence
    of the target tables and indexes before creating them.

    Returns:
        bool: True if the migration completed successfully (or if the tables
            already exist), False otherwise.
    """
    print("Starting migration: Creating agent chat context snapshot tables...")

    try:
        # Use a direct connection instead of a session to keep the footprint
        # minimal and to manage an explicit DDL transaction.
        with engine.connect() as connection:
            trans = connection.begin()
            try:
                # Check if agent_chat_context_snapshot table exists.
                # `to_regclass` returns NULL if the relation does not exist.
                result = connection.execute(
                    text("""
                    SELECT to_regclass('public.agent_chat_context_snapshot')
                """))
                snapshot_exists = result.scalar() is not None

                # Check if agent_chat_context_interactions table exists.
                result = connection.execute(
                    text("""
                    SELECT to_regclass('public.agent_chat_context_interactions')
                """))
                interactions_exists = result.scalar() is not None

                if snapshot_exists and interactions_exists:
                    # Nothing to do: both tables are present. Roll back the
                    # transaction since no DDL changes were applied and exit.
                    print(
                        "Agent_chat_context_snapshot and agent_chat_context_interactions tables already exist. Migration not needed."
                    )
                    trans.rollback()
                    return True

                # Create agent_chat_context_snapshot table with constraints and
                # essential metadata fields when absent.
                if not snapshot_exists:
                    print("Creating agent_chat_context_snapshot table...")
                    connection.execute(
                        text("""
                        CREATE TABLE agent_chat_context_snapshot (
                            id BIGSERIAL PRIMARY KEY,
                            interaction_id BIGINT NOT NULL UNIQUE REFERENCES expert_chat_interactions(id) ON DELETE CASCADE,
                            user_id INTEGER NOT NULL,
                            agent_type VARCHAR(64) NOT NULL,
                            chat_session_id VARCHAR(128) NOT NULL,
                            mode VARCHAR(16) NOT NULL CHECK (mode IN ('web','document','deep','basic')),
                            context_type VARCHAR(16) NOT NULL CHECK (context_type IN ('summary','history','none')),
                            summary_text TEXT NULL,
                            parent_context_id BIGINT NULL REFERENCES agent_chat_context_snapshot(id) ON DELETE SET NULL,
                            model_window_tokens INTEGER NOT NULL,
                            reserve_for_answer INTEGER NOT NULL,
                            instruction_tokens INTEGER NOT NULL,
                            input_tokens_used INTEGER NOT NULL,
                            instruction_prefix TEXT NULL,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        )
                    """))
                    print("Agent_chat_context_snapshot table created")

                    # Create helpful indexes for common query patterns:
                    # - Lookups by (user, agent_type, session)
                    # - Parent chaining follow-ups
                    # - Quick access by interaction_id (unique)
                    print(
                        "Creating indexes for agent_chat_context_snapshot...")
                    connection.execute(
                        text("""
                        CREATE INDEX idx_context_snapshot_user_agent_session 
                        ON agent_chat_context_snapshot (user_id, agent_type, chat_session_id)
                    """))
                    connection.execute(
                        text("""
                        CREATE INDEX idx_context_snapshot_parent 
                        ON agent_chat_context_snapshot (parent_context_id)
                    """))
                    connection.execute(
                        text("""
                        CREATE INDEX idx_context_snapshot_interaction 
                        ON agent_chat_context_snapshot (interaction_id)
                    """))
                    print("Indexes created for agent_chat_context_snapshot")

                # Create agent_chat_context_interactions mapping table when absent.
                if not interactions_exists:
                    print("Creating agent_chat_context_interactions table...")
                    connection.execute(
                        text("""
                        CREATE TABLE agent_chat_context_interactions (
                            context_id BIGINT NOT NULL REFERENCES agent_chat_context_snapshot(id) ON DELETE CASCADE,
                            interaction_id BIGINT NOT NULL REFERENCES expert_chat_interactions(id) ON DELETE CASCADE,
                            position SMALLINT NOT NULL,
                            PRIMARY KEY (context_id, interaction_id)
                        )
                    """))
                    print("Agent_chat_context_interactions table created")

                # Ensure indexes for mapping table using an idempotent DO block:
                # - Single-column index on interaction_id for reverse lookups
                # - Ordered composite index (context_id, position) to support
                #   ORDER BY queries directly from the index.
                print(
                    "Creating indexes for agent_chat_context_interactions...")
                connection.execute(
                    text("""
                    DO $$ BEGIN
                        IF NOT EXISTS (
                            SELECT 1 FROM pg_class c JOIN pg_namespace n ON n.oid = c.relnamespace
                            WHERE c.relkind = 'i' AND c.relname = 'idx_context_interactions_interaction'
                        ) THEN
                            CREATE INDEX idx_context_interactions_interaction 
                            ON agent_chat_context_interactions (interaction_id);
                        END IF;
                        IF NOT EXISTS (
                            SELECT 1 FROM pg_class c JOIN pg_namespace n ON n.oid = c.relnamespace
                            WHERE c.relkind = 'i' AND c.relname = 'idx_context_interactions_ordered'
                        ) THEN
                            CREATE INDEX idx_context_interactions_ordered
                            ON agent_chat_context_interactions (context_id, position);
                        END IF;
                    END $$;
                """))
                print("Indexes ensured for agent_chat_context_interactions")

                # Commit transactional DDL changes.
                trans.commit()
                print("Migration completed successfully!")
                return True
            except Exception as e:
                # Roll back the DDL transaction on any failure to leave the
                # database in a consistent state.
                trans.rollback()
                print(f"Error during migration: {e}")
                import traceback
                traceback.print_exc()
                return False
    except Exception as e:
        # Errors here indicate connection or engine setup problems (e.g. wrong
        # DATABASE_URL for the current environment). Surface the error and
        # return False so callers can handle it.
        print(f"Failed to connect to database: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # Allow the script to be executed directly for ad-hoc migrations.
    success = migrate_create_context_snapshot_tables()
    if success:
        print("\n Agent chat context snapshot tables ready!")
    else:
        print("\n Migration failed! Please check logs.")
        sys.exit(1)
