import React from 'react';
import { useChat } from '@/contexts';
import SearchToggle from '../ui/SearchToggle';

export default function DocumentSearchToggle() {
  const { useDocumentSearch, setUseDocumentSearch, selectedAgent } = useChat();

  if (!selectedAgent) {
    return null;
  }

  return (
    <SearchToggle
      enabled={useDocumentSearch}
      onToggle={() => setUseDocumentSearch(!useDocumentSearch)}
      label="Enable Document Search"
      variant="green"
    />
  );
} 