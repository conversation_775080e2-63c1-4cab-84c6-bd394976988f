'use client';

import React, { create<PERSON>ontext, useContext, useReducer, useEffect, useRef, ReactNode } from 'react';
import { useNotification } from './NotificationContext';
import { API_BASE_URL } from '@/lib/api';
import { MQTTManager } from '@/lib/mqttManager';

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  department: string;
  employee_id: string;
}

interface UserStatus {
  user_id: number;
  username: string;
  department: string;
  is_online: boolean;
  last_seen: string;
}

export interface Message {
  id: number;
  conversation_id: number;
  sender_id: number;
  sender_username: string;
  sender_department: string;
  content: string;
  message_type: string;
  created_at: string;
  is_read: boolean;
  is_edited: boolean;
  edited_at?: string;
}

interface Conversation {
  id: number;
  participant1_id: number;
  participant2_id: number;
  participant1_username: string;
  participant2_username: string;
  participant1_department: string;
  participant2_department: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  last_message?: Message;
  messages?: Message[];
  unread_count?: number;
}

interface TypingUser {
  user_id: number;
  username: string;
  conversation_id: number;
}

interface GroupMessage {
  id: number;
  group_id: number;
  sender_id: number;
  sender_username?: string;
  sender_department?: string;
  content: string;
  message_type: string;
  created_at: string;
  is_read: boolean;
  is_edited: boolean;
  edited_at?: string;
}

interface GroupMember {
  user_id: number;
  username?: string;
  department?: string;
  role: string;
  joined_at: string;
}

interface Group {
  id: number;
  name: string;
  creator_id: number;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  last_message?: GroupMessage;
  members?: GroupMember[];
  messages?: GroupMessage[];
}

interface ChatState {
  currentUser: User | null;
  conversations: Conversation[];
  activeConversation: Conversation | null;
  onlineUsers: UserStatus[];
  allUsers: UserStatus[];
  unreadCount: number;
  unreadMessages: Message[];
  typingUsers: TypingUser[];
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  groups: Group[];
  activeGroup: Group | null;
  ingestionProgress?: Record<string, Record<string, {
    document_id?: number;
    filename?: string;
    step: string;
    status: string;
    progress_pct?: number;
    message?: string;
    metrics?: any;
    updated_at: string;
  }>>;
}

type ChatAction =
  | { type: 'SET_CURRENT_USER'; payload: User | null }
  | { type: 'SET_CONVERSATIONS'; payload: Conversation[] }
  | { type: 'ADD_CONVERSATION'; payload: Conversation }
  | { type: 'UPDATE_CONVERSATION'; payload: Conversation }
  | { type: 'SET_ACTIVE_CONVERSATION'; payload: Conversation | null }
  | { type: 'ADD_MESSAGE'; payload: { conversationId: number; message: Message } }
  | { type: 'UPDATE_MESSAGE'; payload: { messageId: number; updates: Partial<Message> } }
  | { type: 'SET_ONLINE_USERS'; payload: UserStatus[] }
  | { type: 'SET_ALL_USERS'; payload: UserStatus[] }
  | { type: 'UPDATE_USER_STATUS'; payload: { userId: number; isOnline: boolean; lastSeen?: string } }
  | { type: 'SET_UNREAD_COUNT'; payload: number }
  | { type: 'SET_UNREAD_MESSAGES'; payload: Message[] }
  | { type: 'UPDATE_CONVERSATION_UNREAD_COUNT'; payload: { conversationId: number; unreadCount: number } }
  | { type: 'ADD_TYPING_USER'; payload: TypingUser }
  | { type: 'REMOVE_TYPING_USER'; payload: { userId: number; conversationId: number } }
  | { type: 'SET_CONNECTION_STATUS'; payload: { isConnected: boolean; isConnecting: boolean } }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_ERROR' }
  | { type: 'MARK_MESSAGES_READ'; payload: { conversationId: number; readerId: number } }
  | { type: 'SET_GROUPS'; payload: Group[] }
  | { type: 'ADD_GROUP'; payload: Group }
  | { type: 'UPDATE_GROUP'; payload: Group }
  | { type: 'SET_ACTIVE_GROUP'; payload: Group | null }
  | { type: 'ADD_GROUP_MESSAGE'; payload: { groupId: number; message: GroupMessage } }
  | { type: 'UPDATE_GROUP_MESSAGE'; payload: { groupId: number; messageId: number; updates: Partial<GroupMessage> } }
  | { type: 'DELETE_GROUP_MESSAGE'; payload: { groupId: number; messageId: number } }
  | { type: 'INGESTION_PROGRESS'; payload: { job_id: string; document_id?: number; filename?: string; step: string; status: string; progress_pct?: number; message?: string; metrics?: any } };

const initialState: ChatState = {
  currentUser: null,
  conversations: [],
  activeConversation: null,
  onlineUsers: [],
  allUsers: [],
  unreadCount: 0,
  unreadMessages: [],
  typingUsers: [],
  isConnected: false,
  isConnecting: false,
  error: null,
  groups: [],
  activeGroup: null,
  ingestionProgress: {}
};

function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'SET_CURRENT_USER':
      return { ...state, currentUser: action.payload };

    case 'SET_CONVERSATIONS':
      return { ...state, conversations: action.payload };

    case 'ADD_CONVERSATION':
      // Add new conversation to the beginning of the list, removing any existing one with same ID to avoid duplicates
      return {
        ...state,
        conversations: [action.payload, ...state.conversations.filter(c => c.id !== action.payload.id)]
      };

    case 'UPDATE_CONVERSATION':
      return {
        ...state,
        conversations: state.conversations.map(c =>
          c.id === action.payload.id ? action.payload : c
        ),
        activeConversation: state.activeConversation?.id === action.payload.id
          ? action.payload
          : state.activeConversation
      };

    case 'SET_ACTIVE_CONVERSATION':
      let conversation = action.payload;
      // Ensure the conversation has the most recent message as last_message for display purposes
      if (conversation && conversation.messages && conversation.messages.length > 0) {
        const sortedMessages = [...conversation.messages].sort((a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
        conversation = {
          ...conversation,
          last_message: sortedMessages[0]
        };

      }
      const updatedConversationsList = state.conversations.map(conv => {
        if (conv.id === conversation?.id) {
          return {
            ...conv,
            last_message: conversation.last_message
          };
        }
        return conv;
      });
      
      return { 
        ...state, 
        activeConversation: conversation,
        conversations: updatedConversationsList
      };

    case 'ADD_MESSAGE':
      const { conversationId, message } = action.payload;
      const currentTimestamp = new Date().toISOString();
      
      const lastMessage = {
        ...message,
        created_at: message.created_at || currentTimestamp
      };
  
      
      const updatedConversations = state.conversations.map(conv => {
        if (conv.id === conversationId) {
          return {
            ...conv,
            last_message: lastMessage,
            updated_at: currentTimestamp,
            messages: conv.messages ? [...conv.messages, message] : [message]
          };
        }
        return conv;
      });
      
      // Sort conversations by most recent activity (updated_at > last_message.created_at > created_at)
      updatedConversations.sort((a, b) => {
        const getTime = (c) => {
          const t = c?.updated_at || c?.last_message?.created_at || c?.created_at;
          return t ? new Date(t).getTime() : 0;
        };
        return getTime(b) - getTime(a);
      });

      const updatedActiveConversation = state.activeConversation?.id === conversationId
        ? {
            ...state.activeConversation,
            last_message: lastMessage, 
            updated_at: currentTimestamp, 
            messages: state.activeConversation.messages ? [...state.activeConversation.messages, message] : [message]
          }
        : state.activeConversation;

      return {
        ...state,
        conversations: updatedConversations,
        activeConversation: updatedActiveConversation
      };

    case 'SET_ONLINE_USERS':
      return { ...state, onlineUsers: action.payload };

    case 'SET_ALL_USERS':
      return { ...state, allUsers: action.payload };

    case 'UPDATE_USER_STATUS':
      const { userId, isOnline, lastSeen } = action.payload;
      {
        // Update or add user to allUsers array
        const existing = state.allUsers.find(u => u.user_id === userId);
        const updatedAllUsers = existing
          ? // Update existing user's status
            state.allUsers.map(u =>
              u.user_id === userId
                ? { ...u, is_online: isOnline, last_seen: lastSeen || u.last_seen }
                : u
            )
          : // Add new user to allUsers if not found
            [
              ...state.allUsers,
              {
                user_id: userId,
                username: 'Unknown', // Placeholder until full user data is loaded
                department: '',
                is_online: isOnline,
                last_seen: lastSeen || new Date().toISOString(),
              } as UserStatus,
            ];

        // Manage onlineUsers array based on status change
        const alreadyOnline = state.onlineUsers.some(u => u.user_id === userId);
        const updatedOnlineUsers = isOnline
          ? // Add to online users if coming online and not already there
            alreadyOnline
            ? state.onlineUsers
            : [
                ...state.onlineUsers,
                (existing || (updatedAllUsers.find(u => u.user_id === userId) as UserStatus)),
              ]
          : // Remove from online users if going offline
            state.onlineUsers.filter(u => u.user_id !== userId);

        return {
          ...state,
          onlineUsers: updatedOnlineUsers,
          allUsers: updatedAllUsers,
        };
      }

    case 'SET_UNREAD_COUNT':
      return { ...state, unreadCount: action.payload };
    
    case 'SET_UNREAD_MESSAGES':
      return { ...state, unreadMessages: action.payload };
      
    case 'UPDATE_CONVERSATION_UNREAD_COUNT':
      return {
        ...state,
        conversations: state.conversations.map(conversation => 
          conversation.id === action.payload.conversationId
            ? { ...conversation, unread_count: action.payload.unreadCount }
            : conversation
        )
      };

    case 'ADD_TYPING_USER':
      return {
        ...state,
        typingUsers: [
          ...state.typingUsers.filter(t => 
            !(t.user_id === action.payload.user_id && t.conversation_id === action.payload.conversation_id)
          ),
          action.payload
        ]
      };

    case 'REMOVE_TYPING_USER':
      return {
        ...state,
        typingUsers: state.typingUsers.filter(t =>
          !(t.user_id === action.payload.userId && t.conversation_id === action.payload.conversationId)
        )
      };

    case 'SET_CONNECTION_STATUS':
      return {
        ...state,
        isConnected: action.payload.isConnected,
        isConnecting: action.payload.isConnecting
      };

    case 'SET_ERROR':
      return { ...state, error: action.payload };

    case 'CLEAR_ERROR':
      return { ...state, error: null };

    case 'MARK_MESSAGES_READ':
      const { conversationId: convId, readerId } = action.payload;
      return {
        ...state,
        conversations: state.conversations.map(conv => {
          if (conv.id === convId) {
            return {
              ...conv,
              unread_count: 0, 
              messages: conv.messages ? conv.messages.map(msg =>
                msg.sender_id !== readerId ? { ...msg, is_read: true } : msg
              ) : []
            };
          }
          return conv;
        }),
        unreadCount: Math.max(0, state.unreadCount - (state.conversations.find(c => c.id === convId)?.unread_count || 0)),
        unreadMessages: state.unreadMessages.filter(msg => msg.conversation_id !== convId),
        activeConversation: state.activeConversation?.id === convId && state.activeConversation.messages
          ? {
              ...state.activeConversation,
              unread_count: 0, 
              messages: state.activeConversation.messages.map(msg =>
                msg.sender_id !== readerId ? { ...msg, is_read: true } : msg
              )
            }
          : state.activeConversation
      };

    case 'UPDATE_MESSAGE': {
      const { messageId, updates } = action.payload;

      const updateMsgs = (msgs?: Message[]) =>
        msgs?.map(m => (m.id === messageId ? { ...m, ...updates } : m));

      return {
        ...state,
        conversations: state.conversations.map(c => ({
          ...c,
          messages: updateMsgs(c.messages),
          last_message: c.last_message?.id === messageId ? { ...c.last_message, ...updates } : c.last_message,
        })),
        activeConversation: state.activeConversation
          ? {
              ...state.activeConversation,
              messages: updateMsgs(state.activeConversation.messages),
              last_message:
                state.activeConversation.last_message?.id === messageId
                  ? { ...state.activeConversation.last_message, ...updates }
                  : state.activeConversation.last_message,
            }
          : state.activeConversation,
      };
    }

    case 'SET_GROUPS':
      return { ...state, groups: action.payload };

    case 'ADD_GROUP':
      return { ...state, groups: [action.payload, ...state.groups.filter(g => g.id !== action.payload.id)] };

    case 'UPDATE_GROUP':
      return {
        ...state,
        groups: state.groups.map(g => (g.id === action.payload.id ? action.payload : g)),
        activeGroup: state.activeGroup?.id === action.payload.id ? action.payload : state.activeGroup
      };

    case 'SET_ACTIVE_GROUP':
      return { ...state, activeGroup: action.payload };

    case 'ADD_GROUP_MESSAGE': {
      const { groupId, message } = action.payload;
      const currentTimestamp = new Date().toISOString();
      
      const updatedGroups = state.groups.map(grp => {
        if (grp.id === groupId) {
          return {
            ...grp,
            last_message: message,
            updated_at: currentTimestamp, 
            messages: grp.messages ? [...grp.messages, message] : [message]
          };
        }
        return grp;
      });
      
      updatedGroups.sort((a, b) => {
        const getTime = (c) => {
          const t = c?.updated_at || c?.last_message?.created_at || c?.created_at;
          return t ? new Date(t).getTime() : 0;
        };
        return getTime(b) - getTime(a);
      });

      const updatedActiveGroup = state.activeGroup?.id === groupId
        ? {
            ...state.activeGroup,
            last_message: message,
            updated_at: currentTimestamp, 
            messages: state.activeGroup.messages ? [...state.activeGroup.messages, message] : [message]
          }
        : state.activeGroup;

      return { ...state, groups: updatedGroups, activeGroup: updatedActiveGroup };
    }

    case 'UPDATE_GROUP_MESSAGE': {
      const { groupId, messageId, updates } = action.payload;
      const updateFn = (msgs?: GroupMessage[]) => msgs?.map(m=>m.id===messageId?{...m,...updates}:m);
      return {
        ...state,
        groups: state.groups.map(g=>g.id===groupId?{...g,messages:updateFn(g.messages)}:g),
        activeGroup: state.activeGroup?.id===groupId?{...state.activeGroup,messages:updateFn(state.activeGroup.messages)}:state.activeGroup
      };
    }
    case 'DELETE_GROUP_MESSAGE': {
      const { groupId, messageId } = action.payload;
      const markDel = (msg: GroupMessage): GroupMessage =>
        msg.id === messageId ? { ...msg, message_type: 'deleted', content: '' } : msg;

      const updateGroup = (grp: Group): Group => {
        const updatedMessages = grp.messages ? grp.messages.map(markDel) : grp.messages;
        const updatedLast = grp.last_message && grp.last_message.id === messageId
          ? { ...grp.last_message, message_type: 'deleted', content: '' }
          : grp.last_message;
        return { ...grp, messages: updatedMessages, last_message: updatedLast };
      };

      return {
        ...state,
        groups: state.groups.map(g => (g.id === groupId ? updateGroup(g) : g)),
        activeGroup: state.activeGroup?.id === groupId ? updateGroup(state.activeGroup) : state.activeGroup,
      };
    }

    case 'INGESTION_PROGRESS': {
      const { job_id, document_id, filename, step, status, progress_pct, message, metrics } = action.payload;
      const jobMap = { ...(state.ingestionProgress || {}) };
      const docKey = String(document_id ?? filename ?? 'unknown');
      const docs = { ...(jobMap[job_id] || {}) };
      docs[docKey] = {
        document_id,
        filename,
        step,
        status,
        progress_pct,
        message,
        metrics,
        updated_at: new Date().toISOString(),
      };
      jobMap[job_id] = docs;
      return { ...state, ingestionProgress: jobMap };
    }

    default:
      return state;
  }
}

interface ChatContextType {
  state: ChatState;
  dispatch: React.Dispatch<ChatAction>;
  sendMessage: (conversationId: number, content: string, messageType?: string) => void;

  editMessage: (messageId: number, newContent: string) => void;
  deleteMessage: (messageId: number) => void;
  hideMessage: (messageId: number) => void;
  forwardMessage: (messageId: number, targetConversationId: number) => void;
  startTyping: (conversationId: number) => void;
  stopTyping: (conversationId: number) => void;
  markConversationAsRead: (conversationId: number) => void;
  createConversation: (participantId: number) => Promise<Conversation | null>;
  connectMQTT: () => void;
  disconnectMQTT: () => void;
  resetConnection: () => void;
  createNotificationFromMessage: (message: Message) => void;
  sendGroupMessage: (groupId: number, content: string, messageType?: string) => void;
  createGroup: (name: string, memberIds: number[]) => Promise<Group | null>;
  loadGroups: () => Promise<void>;
  renameGroup: (groupId: number, newName: string) => Promise<void>;
  addGroupMembers: (groupId: number, memberIds: number[]) => Promise<void>;
  leaveGroup: (groupId: number) => Promise<void>;
  editGroupMessage: (messageId:number, groupId:number, newContent:string)=>void;
  deleteGroupMessage: (messageId:number, groupId:number)=>void;
  hideGroupMessage: (messageId:number, groupId:number)=>void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

interface ChatProviderProps {
  children: ReactNode;
}

export function ChatProvider({ children }: ChatProviderProps) {
  const [state, dispatch] = useReducer(chatReducer, initialState);
  const mqttRef = useRef<MQTTManager | null>(null);
  // Track processed message IDs to prevent duplicate processing
  const seenMessageIdsRef = useRef<Set<number>>(new Set());
  const notificationContext = useNotification();
  // Control whether MQTT reconnection attempts are allowed
  const allowReconnectRef = useRef<boolean>(true);

  useEffect(() => {
    dispatch({ type: 'SET_CONNECTION_STATUS', payload: { isConnected: false, isConnecting: false } });
    dispatch({ type: 'SET_ERROR', payload: null });
  }, []); 

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        dispatch({ type: 'SET_CURRENT_USER', payload: user });
      } catch (err) {
      }
    }
  }, []);

  useEffect(() => {
    if (state.currentUser && !state.isConnected && !state.isConnecting && !mqttRef.current) {
      connectMQTT();
    }
  }, [state.currentUser]); 

  // Always refresh groups on provider mount and when live chat sidebar opens
  useEffect(() => {
    if (state.currentUser) {
      loadGroups();
    }
  }, [state.currentUser]);

  // Listen for live chat panel open event to force-refresh each time
  useEffect(() => {
    const handler = () => {
      if (state.currentUser) {
        loadGroups();
      }
    };
    window.addEventListener('open-live-chat', handler as EventListener);
    return () => window.removeEventListener('open-live-chat', handler as EventListener);
  }, [state.currentUser]);

  const getUserFromStateOrStorage = (): User | null => {
    if (state.currentUser) return state.currentUser;
    try {
      const raw = localStorage.getItem('user');
      return raw ? JSON.parse(raw) : null;
    } catch {
      return null;
    }
  };

  // Generate or retrieve a persistent session ID for WebSocket connections
  const getOrCreateSessionId = (userId: number): string => {
    const sidKey = `ws_session_${userId}`;
    let sessionId = localStorage.getItem(sidKey);
    if (!sessionId) {
      // Create new session ID with user ID, timestamp, and random component for uniqueness
      sessionId = `${userId}_${Date.now()}_${Math.random().toString(36).slice(2, 10)}`;
      try { localStorage.setItem(sidKey, sessionId); } catch {}
    }
    return sessionId;
  };

  // Note: WebSocket URL building is now handled in MQTTManager
  // The MQTT connection automatically uses WSS through nginx proxy for HTTPS sites

  const notify = (opts: any) => {
    try {
      notificationContext.addNotification(opts as any);
    } catch (err) {
    }
  };

  // Send MQTT command with connection validation and error handling
  const sendCommand = (payload: any): boolean => {
    if (!mqttRef.current || !mqttRef.current.isConnected()) {
      // Attempt reconnection if not connected
      dispatch({
        type: 'SET_ERROR',
        payload: 'Not connected to chat server. Attempting to reconnect...'
      });
      connectMQTT();
      return false; // Indicate command was not sent
    }
    
    try {
      mqttRef.current.publishCommand(payload);
      return true;
    } catch (err) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to send message. Please try again.' });
      return false;
    }
  };

  const connectMQTT = async () => {
    if (mqttRef.current && mqttRef.current.isConnected()) {
      return;
    }
    
    // Prevent multiple concurrent connection attempts
    if (!allowReconnectRef.current) return;
    if (state.isConnecting || state.isConnected) return;
    
    const user = getUserFromStateOrStorage();
    if (!user) {
      return;
    }
    
    dispatch({ type: 'SET_CONNECTION_STATUS', payload: { isConnected: false, isConnecting: true } });
    
    try {
      const seedOnlineUsers = async () => {
        try {
          const token = localStorage.getItem('access_token');
          const resp = await fetch(`${API_BASE_URL}/chat/users/online`, {
            headers: { 'Authorization': `Bearer ${token}` }
          });
          if (resp.ok) {
            const data = await resp.json();
            const users = Array.isArray(data?.users) ? data.users : [];
            users.forEach((u: any) => {
              dispatch({
                type: 'UPDATE_USER_STATUS',
                payload: {
                  userId: u.user_id,
                  isOnline: true,
                  lastSeen: u.last_seen || new Date().toISOString()
                }
              });
            });
          }
        } catch (e) {
        }
      };

      mqttRef.current = new MQTTManager({
        userId: user.id,
        onMessage: handleMQTTMessage,
        onConnect: () => {
          dispatch({ type: 'SET_CONNECTION_STATUS', payload: { isConnected: true, isConnecting: false } });
          dispatch({ type: 'CLEAR_ERROR' });
          seedOnlineUsers();
        },
        onDisconnect: () => {
          dispatch({ type: 'SET_CONNECTION_STATUS', payload: { isConnected: false, isConnecting: false } });
        },
        onError: (error: any) => {
          dispatch({ type: 'SET_CONNECTION_STATUS', payload: { isConnected: false, isConnecting: false } });
          dispatch({ type: 'SET_ERROR', payload: 'Connection failed. Retrying...' });
        }
      });
      
      await mqttRef.current.connect();
      
    } catch (error) {
      dispatch({ type: 'SET_CONNECTION_STATUS', payload: { isConnected: false, isConnecting: false } });
      dispatch({ type: 'SET_ERROR', payload: 'Failed to connect to chat server' });
    }
  };

  const disconnectMQTT = () => {
    if (mqttRef.current) {
      mqttRef.current.disconnect();
      mqttRef.current = null;
    }
    
    dispatch({ type: 'SET_CONNECTION_STATUS', payload: { isConnected: false, isConnecting: false } });
  };

  const handleMQTTMessage = (data: any) => {
    switch (data.type) {
      case 'new_message':
      case 'message':
        if (data.message && data.conversation_id) {
          // Check for duplicate messages using client-side deduplication
          const mid = Number(data.message.id);
          if (mid && seenMessageIdsRef.current.has(mid)) {
            break; // Skip processing duplicate messages
          }

          const enrichedMessage = {
            ...data.message,
            conversation_id: data.conversation_id,
          };

          // Check if conversation exists in local state
          const convExists = state.conversations.some(c => c.id === data.conversation_id);
          if (!convExists) {
            // Conversation not found locally, fetch it from server
            (async () => {
              try {
                const token = localStorage.getItem('access_token');
                const resp = await fetch(`${API_BASE_URL}/chat/conversations/${data.conversation_id}`, {
                  headers: { 'Authorization': `Bearer ${token}` }
                });
                if (resp.ok) {
                  const conversation = await resp.json();
                  dispatch({ type: 'ADD_CONVERSATION', payload: conversation });
                  if (mid) seenMessageIdsRef.current.add(mid);
                  dispatch({ type: 'ADD_MESSAGE', payload: { conversationId: data.conversation_id, message: enrichedMessage } });

                  const senderId = Number(enrichedMessage.sender_id);
                  const currentUserId = Number(state.currentUser?.id);
                  // Only show notifications for messages from other users
                  if (senderId !== currentUserId) {
                    // Don't show notifications if user is actively viewing this conversation
                    const isCurrentConversation = state.activeConversation?.id === data.conversation_id;
                    const isCurrentlyActive = document.visibilityState === 'visible';
                    if (!(isCurrentConversation && isCurrentlyActive)) {
                      const isTV = enrichedMessage.message_type === 'teamviewer_request';
                      notify({
                        title: isTV ? `TeamViewer request from ${enrichedMessage.sender_username}` : `New message from ${enrichedMessage.sender_username}`,
                        message: isTV ? 'Click to open the chat and respond.' : (enrichedMessage.content.length > 50 ? `${enrichedMessage.content.substring(0, 50)}...` : enrichedMessage.content),
                        type: 'info',
                        sender: { id: enrichedMessage.sender_id, username: enrichedMessage.sender_username },
                        conversationId: enrichedMessage.conversation_id,
                      });
                      try {
                        const audio = new Audio('/notification-sound.mp3');
                        audio.volume = 0.5;
                        audio.play().catch(()=>{});
                      } catch {}
                    }
                  }
                }
              } catch (e) {
              }
            })();
            break;
          }

          if (mid) seenMessageIdsRef.current.add(mid);
          dispatch({
            type: 'ADD_MESSAGE',
            payload: {
              conversationId: data.conversation_id,
              message: enrichedMessage,
            },
          });

          
          const senderId = Number(enrichedMessage.sender_id);
          const currentUserId = Number(state.currentUser?.id);
          
          if (senderId !== currentUserId) {
            const isCurrentConversation = state.activeConversation?.id === data.conversation_id;
            const isCurrentlyActive = document.visibilityState === 'visible';
            
            if (isCurrentConversation && isCurrentlyActive) {
              const readReceiptPayload = { type: 'read_receipt', conversation_id: data.conversation_id };
              sendCommand(readReceiptPayload);
            } else {
              const isTV = enrichedMessage.message_type === 'teamviewer_request';
              notify({
                title: isTV ? `TeamViewer request from ${enrichedMessage.sender_username}` : `New message from ${enrichedMessage.sender_username}`,
                message: isTV ? 'Click to open the chat and respond.' : (enrichedMessage.content.length > 50 ? `${enrichedMessage.content.substring(0, 50)}...` : enrichedMessage.content),
                type: 'info',
                sender: { id: enrichedMessage.sender_id, username: enrichedMessage.sender_username },
                conversationId: enrichedMessage.conversation_id,
              });
              
              try {
                const audio = new Audio('/notification-sound.mp3');
                audio.volume = 0.5;
                audio.play().catch(e => console.error('Error playing notification sound:', e));
              } catch (error) {
              }
            }
          }
        }
        break;

      case 'typing':
        if (!data.conversation_id || !data.user_id || !data.username) break;
        dispatch(
          data.is_typing
            ? {
                type: 'ADD_TYPING_USER',
                payload: {
                  user_id: data.user_id,
                  username: data.username,
                  conversation_id: data.conversation_id,
                },
              }
            : {
                type: 'REMOVE_TYPING_USER',
                payload: {
                  userId: data.user_id,
                  conversationId: data.conversation_id,
                },
              }
        );
        break;

      case 'read_receipt':
        if (data.conversation_id && data.reader_id) {
          dispatch({
            type: 'MARK_MESSAGES_READ',
            payload: {
              conversationId: data.conversation_id,
              readerId: data.reader_id
            }
          });
        }
        break;

      case 'error':
        dispatch({ type: 'SET_ERROR', payload: data.message || 'Unknown error' });
        break;

      case 'message_edited':
        if (!data.message_id || !data.new_content) break;
        dispatch({
          type: 'UPDATE_MESSAGE',
          payload: {
            messageId: data.message_id,
            updates: { content: data.new_content, is_edited: true }
          }
        });
        break;

      case 'message_deleted':
        if (!data.message_id) break;
        dispatch({
          type: 'UPDATE_MESSAGE',
          payload: {
            messageId: data.message_id,
            updates: { content: '', message_type: 'deleted' }
          }
        });
        break;

      case 'message_hidden':
        if (!data.message_id) break;
        dispatch({
          type: 'UPDATE_MESSAGE',
          payload: {
            messageId: data.message_id,
            updates: { content: '', message_type: 'deleted' }
          }
        });
        break;

      case 'group_message_hidden':
        if (!data.group_id || !data.message_id) break;
        dispatch({ type: 'DELETE_GROUP_MESSAGE', payload: { groupId: data.group_id, messageId: data.message_id } });
        break;

      case 'user_status':
        if (data.user_id) {
          // Handle different possible formats for online status from MQTT messages
          const isOnline = typeof data.is_online === 'boolean' ? data.is_online : (data.status === 'online');
          // Use provided timestamp or current time as fallback
          const lastSeen = data.last_seen || data.timestamp || new Date().toISOString();
          dispatch({
            type: 'UPDATE_USER_STATUS',
            payload: { userId: data.user_id, isOnline, lastSeen }
          });
        }
        break;

      case 'group_new_message':
        if (data.message && data.group_id) {
          // Deduplicate group messages using the same mechanism as 1:1 messages
          const gmid = Number(data.message.id);
          if (gmid && seenMessageIdsRef.current.has(gmid)) {
            break; // Skip duplicate group messages
          }

          // Check if message already exists in local group state
          const exists = state.groups.find(g=>g.id===data.group_id)?.messages?.some((m:any)=>m.id===data.message.id);
          if(!exists){
            if (gmid) seenMessageIdsRef.current.add(gmid);
            dispatch({
              type: 'ADD_GROUP_MESSAGE',
              payload: { groupId: data.group_id, message: data.message }
            });
          }

          // Show notifications only for messages from other users
          if (data.message.sender_id !== state.currentUser?.id) {
            notify({
              title: 'New group message',
              message: data.message.message_type==='text'? (data.message.content.length>50?data.message.content.substring(0,50)+'...':data.message.content): `Attachment in group`,
              type: 'info'
            });
          }
        }
        break;
      case 'group_invite':
        if (data.group) {
          dispatch({ type: 'ADD_GROUP', payload: data.group });
          const adder = data.adder_name ? ` by ${data.adder_name}` : '';
          notify({title:'Added to group',message:`You were added to ${data.group.name}${adder}`,type:'info'});
          try {
            // Prompt other panels (e.g., Knowledge Base) to refresh their space lists
            window.dispatchEvent(new CustomEvent('spaces-revalidate'));
          } catch {}
        }
        break;
      case 'group_update':
        if(data.group){
          dispatch({type:'UPDATE_GROUP',payload:data.group});
        }
        break;
      case 'group_member_removed':
        // If current user was removed from a group, drop it immediately from sidebar
        if (data && typeof data.group_id === 'number' && state.currentUser && data.user_id === state.currentUser.id) {
          dispatch({ type: 'SET_GROUPS', payload: state.groups.filter(g => g.id !== data.group_id) });
          if (state.activeGroup?.id === data.group_id) {
            dispatch({ type: 'SET_ACTIVE_GROUP', payload: null });
          }
          try {
            // Trigger space list refresh so linked shared space disappears as well
            window.dispatchEvent(new CustomEvent('spaces-revalidate'));
          } catch {}
        }
        break;
      case 'group_update_message':
        dispatch({type:'UPDATE_GROUP_MESSAGE',payload:{groupId:data.group_id,messageId:data.message.id,updates:data.message}});
        break;
      case 'group_delete_message':
        dispatch({type:'DELETE_GROUP_MESSAGE',payload:{groupId:data.group_id,messageId:data.message_id}});
        break;
      case 'group_member_added':
        notify({
          title: 'New member',
          message: `${data.adder_name} added ${data.added_names} to ${data.group_name}`,
          type: 'info'
        });
        break;

      case 'shared_ai_response':
        // Handle AI response shared by another user
        if (state.currentUser && data.shared_response && data.shared_by_user_id) {
          // Find existing conversation between current user and the sharer
          const existingConversation = state.conversations.find(conv =>
            (conv.participant1_id === data.shared_by_user_id && conv.participant2_id === state.currentUser!.id) ||
            (conv.participant1_id === state.currentUser!.id && conv.participant2_id === data.shared_by_user_id)
          );

          if (existingConversation) {
            const payload = {
              type: 'shared_ai_response',
              message_type: 'shared_ai_response',
              shared_response: data.shared_response,
              share_context: data.share_context,
              shared_by_username: data.shared_by_username,
              shared_by_user_id: data.shared_by_user_id,
              timestamp: data.timestamp || new Date().toISOString(),
            };

            const sharedMessage = {
              id: Date.now(),
              sender_id: data.shared_by_user_id,
              sender_username: data.shared_by_username || 'Unknown User',
              sender_department: 'System',
              content: JSON.stringify(payload),
              message_type: 'shared_ai_response',
              created_at: payload.timestamp,
              is_read: false,
              is_edited: false,
              edited_at: undefined,
              conversation_id: existingConversation.id
            } as any;

            dispatch({ type: 'ADD_MESSAGE', payload: { conversationId: existingConversation.id, message: sharedMessage } });
          } else {
            notify({
              title: 'Shared AI Response',
              message: `${data.shared_by_username || 'Someone'} shared an AI response: "${(data.shared_response?.query || '').toString().substring(0, 50)}..."`,
              type: 'info'
            });
          }
        }
        break;

      default:
    }
  };


  const sendMessage = (conversationId: number, content: string, messageType?: string) => {
    const payload = {
      type: 'message',
      conversation_id: conversationId,
      content: content.trim(),
      message_type: messageType || 'text'
    };
    sendCommand(payload);
  };

  const editMessage = (messageId: number, newContent: string) => {
    dispatch({
      type: 'UPDATE_MESSAGE',
      payload: {
        messageId: messageId,
        updates: { content: newContent, is_edited: true }
      }
    });

    const payload = { type: 'edit_message', message_id: messageId, content: newContent.trim() };
    if (!sendCommand(payload)) {
    }
  };

  const deleteMessage = (messageId: number) => {
    dispatch({
      type: 'UPDATE_MESSAGE',
      payload: {
        messageId: messageId,
        updates: { content: '', message_type: 'deleted' }
      }
    });

    const payload = { type: 'delete_message', message_id: messageId };
    sendCommand(payload);
  };

  const hideMessage = (messageId: number) => {
    dispatch({
      type: 'UPDATE_MESSAGE',
      payload: {
        messageId: messageId,
        updates: { content: '', message_type: 'deleted' }
      }
    });

    const payload = { type: 'hide_message', message_id: messageId };
    sendCommand(payload);
  };

  const forwardMessage = (messageId: number, targetConversationId: number) => {
    const payload = { type: 'forward_message', message_id: messageId, target_conversation_id: targetConversationId };
    sendCommand(payload);
  };

  const startTyping = (conversationId: number) => {
    const payload = { type: 'typing', conversation_id: conversationId, is_typing: true };
    sendCommand(payload);
  };

  const stopTyping = (conversationId: number) => {
    const payload = { type: 'typing', conversation_id: conversationId, is_typing: false };
    sendCommand(payload);
  };

  const markConversationAsRead = (conversationId: number) => {
    const payload = { type: 'read_receipt', conversation_id: conversationId };
    sendCommand(payload);
  };

  const createConversation = async (participantId: number): Promise<Conversation | null> => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/conversations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ participant_id: participantId })
      });

      if (!response.ok) {
        throw new Error('Failed to create conversation');
      }

      const conversation = await response.json();
      dispatch({ type: 'ADD_CONVERSATION', payload: conversation });
      return conversation;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to create conversation' });
      return null;
    }
  };

  

  const loadGroups = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/groups/`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (response.ok) {
        const groups = await response.json();
        dispatch({ type: 'SET_GROUPS', payload: groups });
      }
    } catch (error) {
    }
  };

  const createGroup = async (name: string, memberIds: number[]): Promise<Group | null> => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/groups/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ name, member_ids: memberIds })
      });
      if (response.ok) {
        const group = await response.json();
        dispatch({ type: 'ADD_GROUP', payload: group });
        return group;
      }
    } catch (error) {
    }
    return null;
  };

  const sendGroupMessage = (groupId: number, content: string, messageType: string = 'text') => {
    if (!state.currentUser) return;
    sendCommand({ type: 'group_message', group_id: groupId, content, message_type: messageType });
  };

  const renameGroup = async (groupId: number, newName: string) => {
    try {
      const token = localStorage.getItem('access_token');
      const resp = await fetch(`${API_BASE_URL}/chat/groups/${groupId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
        body: JSON.stringify({ name: newName })
      });
      if (resp.ok) {
        const grp = await resp.json();
        dispatch({ type: 'UPDATE_GROUP', payload: grp });
      }
    } catch (err) {
    }
  };

  const addGroupMembers = async (groupId: number, memberIds: number[]) => {
    try {
      const token = localStorage.getItem('access_token');
      const resp = await fetch(`${API_BASE_URL}/chat/groups/${groupId}/members`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
        body: JSON.stringify({ member_ids: memberIds })
      });
      if (resp.ok) {
        const grp = await resp.json();
        dispatch({ type: 'UPDATE_GROUP', payload: grp });
      }
    } catch (err) {
    }
  };

  const leaveGroup = async (groupId: number) => {
    try {
      const token = localStorage.getItem('access_token');
      const resp = await fetch(`${API_BASE_URL}/chat/groups/${groupId}/members/${state.currentUser?.id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (resp.ok || resp.status === 204) {
        dispatch({ type: 'SET_GROUPS', payload: state.groups.filter(g => g.id !== groupId) });
        if (state.activeGroup?.id === groupId) {
          dispatch({ type: 'SET_ACTIVE_GROUP', payload: null });
        }
      }
    } catch (err) {
    }
  };

  const editGroupMessage = (messageId:number, groupId:number, newContent:string) => {
    dispatch({type:'UPDATE_GROUP_MESSAGE',payload:{groupId,messageId,updates:{content:newContent,is_edited:true,edited_at:new Date().toISOString()}}});
    sendCommand({type:'group_edit_message',group_id:groupId,message_id:messageId,content:newContent});
  };
  const deleteGroupMessage = (messageId:number, groupId:number) => {
    dispatch({type:'DELETE_GROUP_MESSAGE',payload:{groupId,messageId}});
    sendCommand({type:'group_delete_message',group_id:groupId,message_id:messageId});
  };

  const hideGroupMessage = (messageId:number, groupId:number) => {
    dispatch({type:'DELETE_GROUP_MESSAGE',payload:{groupId,messageId}});
    sendCommand({type:'group_hide_message',group_id:groupId,message_id:messageId});
  }

  useEffect(() => {
    const handleBeforeUnload = () => {
      disconnectMQTT();
    };

    // Reconnect MQTT when user returns to tab after being away
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !state.isConnected && !state.isConnecting) {
        // Delay reconnection slightly to avoid rapid reconnect attempts
        setTimeout(() => {
          if (!mqttRef.current || !mqttRef.current.isConnected()) {
            connectMQTT();
          }
        }, 1000);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (mqttRef.current) {
        mqttRef.current.disconnect();
        mqttRef.current = null;
      }
    };
  }, []); 

  const value: ChatContextType = {
    state,
    dispatch,
    sendMessage,
    editMessage,
    deleteMessage,
    hideMessage,
    forwardMessage,
    startTyping,
    stopTyping,
    markConversationAsRead,
    createConversation,
    connectMQTT,
    disconnectMQTT,
    resetConnection: () => {
      // Force disconnect and reconnect with delay to reset MQTT connection state
      disconnectMQTT();
      setTimeout(() => connectMQTT(), 1000);
    },
    createNotificationFromMessage: () => {},
    sendGroupMessage,
    createGroup,
    loadGroups,
    renameGroup,
    addGroupMembers,
    leaveGroup,
    editGroupMessage,
    deleteGroupMessage,
    hideGroupMessage
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
}

export function useChat() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
} 