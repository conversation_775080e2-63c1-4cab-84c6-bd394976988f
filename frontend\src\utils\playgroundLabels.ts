/**
 * Playground Label Utility
 * 
 * Provides dynamic labels and terminology based on space playground type.
 * Used throughout the application to ensure consistent, context-aware UI text.
 */

export type PlaygroundType = 'documents' | 'images';

export interface PlaygroundLabels {
  viewAction: string;
  selectAction: string;
  uploadAction: string;
  uploadSingleAction: string;
  uploadBulkAction: string;
  addAction: string;
  itemName: string;
  itemNamePlural: string;
  spaceDescription: string;
  emptyStateMessage: string;
  uploadHint: string;
}

/**
 * Get appropriate labels for the given playground type
 */
export function getPlaygroundLabels(playgroundType: PlaygroundType): PlaygroundLabels {
  if (playgroundType === 'images') {
    return {
      viewAction: 'View Image',
      selectAction: 'Select Image',
      uploadAction: 'Upload Images',
      uploadSingleAction: 'Upload Image',
      uploadBulkAction: 'Bulk Upload Images',
      addAction: 'Add Image',
      itemName: 'Image',
      itemNamePlural: 'Images',
      spaceDescription: 'Image space for visual content',
      emptyStateMessage: 'No images in this space yet. Upload your first image to get started.',
      uploadHint: 'Supported: JPEG, PNG, WebP, HEIC, HEIF (max 10MB each)'
    };
  }
  
  // Default: documents
  return {
    viewAction: 'View Document',
    selectAction: 'Select Document',
    uploadAction: 'Upload Documents',
    uploadSingleAction: 'Upload Document',
    uploadBulkAction: 'Bulk Upload Documents',
    addAction: 'Add Document',
    itemName: 'Document',
    itemNamePlural: 'Documents',
    spaceDescription: 'Document space with AI-powered search',
    emptyStateMessage: 'No documents in this space yet. Upload or add a text document to get started.',
    uploadHint: 'Supported: PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX (max 10MB each)'
  };
}

/**
 * Get file accept attribute for the given playground type
 */
export function getPlaygroundAcceptAttribute(playgroundType: PlaygroundType): string {
  if (playgroundType === 'images') {
    return 'image/jpeg,image/png,image/webp,image/heic,image/heif';
  }
  
  // Default: documents
  return 'application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/msword,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,.pdf,.docx,.doc,.ppt,.pptx,.xls,.xlsx';
}

/**
 * Validate if a file is allowed for the given playground type
 */
export function isFileAllowedForPlayground(file: File, playgroundType: PlaygroundType): boolean {
  const fileType = file.type.toLowerCase();
  const fileName = file.name.toLowerCase();
  
  if (playgroundType === 'images') {
    const allowedImageTypes = [
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/heic',
      'image/heif'
    ];
    const allowedImageExts = ['.jpg', '.jpeg', '.png', '.webp', '.heic', '.heif'];
    
    return allowedImageTypes.includes(fileType) || 
           allowedImageExts.some(ext => fileName.endsWith(ext));
  }
  
  // Default: documents
  const allowedDocTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];
  const allowedDocExts = ['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx'];
  
  return allowedDocTypes.some(type => fileType.includes(type)) || 
         allowedDocExts.some(ext => fileName.endsWith(ext));
}

/**
 * Get maximum file size in bytes for the given playground type
 */
export function getMaxFileSizeBytes(playgroundType: PlaygroundType): number {
  // Both types have 10MB limit currently
  return 10 * 1024 * 1024; // 10MB
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
}

/**
 * Check if a space should show text document option
 */
export function canAddTextDocument(playgroundType: PlaygroundType): boolean {
  return playgroundType === 'documents';
}

/**
 * Get friendly playground type name for display
 */
export function getPlaygroundTypeName(playgroundType: PlaygroundType): string {
  return playgroundType === 'images' ? 'Image Space' : 'Document Space';
}

/**
 * Get icon name for playground type (for icon libraries)
 */
export function getPlaygroundIcon(playgroundType: PlaygroundType): string {
  return playgroundType === 'images' ? 'photo' : 'document-text';
}

