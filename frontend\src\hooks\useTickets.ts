import { useState, useEffect, useCallback } from 'react';
import { API_BASE_URL } from '@/lib/api';
import { Ticket, TicketsResponse, TicketStatus } from '@/types/ticket';

interface UseTicketsOptions {
  statusFilter?: 'all' | TicketStatus;
  page?: number;
  perPage?: number;
  isAdmin?: boolean;
}

interface UseTicketsReturn {
  tickets: Ticket[];
  loading: boolean;
  error: string;
  total: number;
  currentPage: number;
  hasAnyTickets: boolean | null;
  fetchTickets: () => Promise<void>;
  setCurrentPage: (page: number) => void;
}

/**
 * Custom hook for fetching and managing tickets
 * Handles both user tickets and admin ticket views
 */
export const useTickets = ({
  statusFilter = 'all',
  page = 1,
  perPage = 20,
  isAdmin = false
}: UseTicketsOptions = {}): UseTicketsReturn => {
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(page);
  const [hasAnyTickets, setHasAnyTickets] = useState<boolean | null>(null);

  const fetchTickets = useCallback(async () => {
    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('access_token');
      if (!token) throw new Error('Not authenticated');

      const params = new URLSearchParams();
      if (statusFilter !== 'all') {
        params.append('status_filter', statusFilter);
      }
      params.append('page', currentPage.toString());
      params.append('per_page', perPage.toString());

      // Different endpoints for admin vs user
      const endpoint = isAdmin
        ? `${API_BASE_URL}/tickets/?${params.toString()}`
        : `${API_BASE_URL}/tickets/my?${params.toString()}`;

      const response = await fetch(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch tickets');
      }

      const data: TicketsResponse = await response.json();
      setTickets(data.tickets);
      setTotal(data.total);

      // Track if user has any tickets at all (for non-admin views)
      if (!isAdmin) {
        setHasAnyTickets(prev => {
          if (prev === null) {
            if (statusFilter === 'all') {
              return data.total > 0;
            } else if (data.total > 0) {
              return true;
            }
          } else if (prev === true && statusFilter === 'all' && data.total === 0) {
            return false;
          }
          return prev;
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [statusFilter, currentPage, perPage, isAdmin]);

  useEffect(() => {
    fetchTickets();
  }, [fetchTickets]);

  return {
    tickets,
    loading,
    error,
    total,
    currentPage,
    hasAnyTickets,
    fetchTickets,
    setCurrentPage
  };
};
