from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class DocumentBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    department: str = Field(..., max_length=50)

class DocumentCreate(DocumentBase):
    file_name: str = Field(..., max_length=255)
    file_path: str = Field(..., max_length=500)
    file_size: Optional[int] = None
    file_type: Optional[str] = Field(None, max_length=100)

class DocumentUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    department: Optional[str] = Field(None, max_length=50)

class DocumentResponse(DocumentBase):
    id: int
    file_name: str
    file_path: str
    file_size: Optional[int]
    file_type: Optional[str]
    views: int
    downloads: int
    uploaded_by: int
    uploader_name: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True 