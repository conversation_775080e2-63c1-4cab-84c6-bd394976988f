"""
Lightweight token counting helpers built on tiktoken for budgeting with Gemini.

Why tiktoken here?
- Google Gemini does not provide a local/offline tokenizer. The only exact
  method to count tokens is via Google's server-side count_tokens.
- For fast, offline estimation during budget planning, we approximate using
  OpenAI's tiktoken encodings (e.g., cl100k_base). The caller should provide
  the encoding name from configuration to keep this flexible.

Caveats:
- Counts are approximate and may differ from Gemini's exact accounting.
- For strict limits, consider validating with server-side count_tokens.
"""

import logging
import tiktoken

logger = logging.getLogger(__name__)

# Global cache for encoding instances to avoid repeated initialization.
_encoding_cache: dict[str, tiktoken.Encoding] = {}

# Import application configuration for budgeting parameters.
from .. import config


def get_encoding(encoding_name: str) -> tiktoken.Encoding:
    """Return a cached tiktoken encoding instance.

    Args:
        encoding_name: Name of the tiktoken encoding (e.g., "cl100k_base").

    Returns:
        A `tiktoken.Encoding` instance associated with the given name.
    """
    if encoding_name not in _encoding_cache:
        _encoding_cache[encoding_name] = tiktoken.get_encoding(encoding_name)
    return _encoding_cache[encoding_name]


def count_tokens(text: str, encoding_name: str) -> int:
    """Count tokens for a single text using the given tiktoken encoding.

    This is an offline approximation for Gemini models. For exact counts, use
    Google's server-side API.

    Args:
        text: The input text to count.
        encoding_name: Name of the tiktoken encoding to use.

    Returns:
        Integer token count (approximate for Gemini).

    Raises:
        Exception: If tokenization fails.
    """
    if not text:
        return 0
    try:
        encoding = get_encoding(encoding_name)
        return len(encoding.encode(text))
    except Exception as e:
        logger.error("Token counting failed for text (len=%s): %s", len(text),
                     e)
        raise


def count_messages_tokens(messages: list[dict], encoding_name: str) -> int:
    """Count tokens for a list of role-tagged chat messages.

    We approximate a small, constant envelope overhead per message to account
    for role labels and separators when prompting.

    Args:
        messages: Sequence of {"role": str, "content": str} dicts.
        encoding_name: Name of the tiktoken encoding to use.

    Returns:
        Total token count (approximate) for all messages, including a small
        per-message overhead defined by TOKEN_MESSAGE_OVERHEAD.
    """
    if not messages:
        return 0

    total_tokens = 0
    encoding = get_encoding(encoding_name)

    for msg in messages:
        # Combine role and content to reflect a typical chat transcript line.
        role = msg.get("role", "user")
        content = msg.get("content", "")
        total_tokens += len(encoding.encode(
            f"{role}: {content}")) + config.MESSAGE_OVERHEAD_TOKENS

    return total_tokens
