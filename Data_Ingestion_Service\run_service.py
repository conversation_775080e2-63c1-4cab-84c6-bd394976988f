#!/usr/bin/env python3
"""
Data Ingestion Service Runner

Simple script to run the FastAPI data ingestion service.
"""

import os
import sys
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

if __name__ == "__main__":
    # Import and run the FastAPI app
    from main import app

    import uvicorn
    port = int(os.getenv("DATA_INGESTION_PORT", "8020"))
    host = os.getenv("HOST", "0.0.0.0")

    uvicorn.run(
        app,
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )
