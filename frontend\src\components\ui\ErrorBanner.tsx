import React from 'react';
import { FiX, FiAlertCircle } from 'react-icons/fi';

interface ErrorBannerProps {
  message: string;
  onDismiss?: () => void;
  className?: string;
}

export function ErrorBanner({ message, onDismiss, className = '' }: ErrorBannerProps) {
  return (
    <div className={`bg-red-50 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 flex items-start ${className}`}>
      <FiAlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
      <div className="flex-1">
        <p className="text-sm">{message}</p>
      </div>
      {onDismiss && (
        <button
          onClick={onDismiss}
          className="ml-3 flex-shrink-0 text-red-700 hover:text-red-900"
        >
          <FiX className="h-5 w-5" />
        </button>
      )}
    </div>
  );
}
