import requests
import logging
from typing import List, Dict, Any
import time

logger = logging.getLogger(__name__)


class ImageEmbeddingClient:
    """Client for Image Embedding Service (SigLIP-2)"""
    
    def __init__(self, service_url: str = "http://**************:8019", timeout: int = 60, max_retries: int = 3):
        """
        Initialize Image Embedding Client
        
        Args:
            service_url: URL of the image embedding service
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
        """
        self.service_url = service_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        logger.info(f"Initializing Image Embedding Client with URL: {self.service_url}")
        
        # Check service health on initialization
        try:
            health = self.health_check()
            if health.get('status') == 'healthy':
                logger.info(f"Connected to Image Embedding Service - Model: {health.get('model')}, Device: {health.get('device')}")
            else:
                logger.warning(f"Image Embedding Service health check returned: {health}")
        except Exception as e:
            logger.error(f"Failed to connect to Image Embedding Service: {e}")
            raise
    
    def health_check(self) -> Dict[str, Any]:
        """
        Check service health
        
        Returns:
            Health status dictionary
        """
        try:
            response = requests.get(f"{self.service_url}/health", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {"status": "error", "message": str(e)}
    
    def _make_request(self, endpoint: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make HTTP request with retry logic
        
        Args:
            endpoint: API endpoint
            payload: Request payload
            
        Returns:
            Response JSON
        """
        url = f"{self.service_url}{endpoint}"
        
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"Making request to {endpoint} (attempt {attempt + 1}/{self.max_retries})")
                response = requests.post(
                    url,
                    json=payload,
                    timeout=self.timeout
                )
                response.raise_for_status()
                return response.json()
                
            except requests.exceptions.Timeout:
                logger.warning(f"Request to {endpoint} timed out (attempt {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    raise
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Request to {endpoint} failed: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    raise
    
    def embed_images(self, images_base64: List[str]) -> Dict[str, Any]:
        """
        Generate embeddings for images
        
        Args:
            images_base64: List of base64 encoded images
            
        Returns:
            Dictionary with:
                - embeddings: List of embedding vectors (1152-dim)
                - dimension: Embedding dimension
                - processing_time: Time taken for processing
        """
        if not images_base64:
            logger.warning("Empty images list provided to embed_images")
            return {"embeddings": [], "dimension": 1152, "processing_time": 0.0}
        
        logger.info(f"Embedding {len(images_base64)} images")
        
        try:
            result = self._make_request("/embed", {"images": images_base64})
            logger.info(f"Successfully embedded {len(images_base64)} images in {result.get('processing_time', 0):.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error embedding images: {e}")
            raise
    
    def caption_images(self, images_base64: List[str], max_length: int = 100) -> Dict[str, Any]:
        """
        Generate captions for images
        
        Args:
            images_base64: List of base64 encoded images
            max_length: Maximum caption length
            
        Returns:
            Dictionary with:
                - captions: List of caption strings
                - processing_time: Time taken for processing
        """
        if not images_base64:
            logger.warning("Empty images list provided to caption_images")
            return {"captions": [], "processing_time": 0.0}
        
        logger.info(f"Generating captions for {len(images_base64)} images")
        
        try:
            result = self._make_request("/caption", {
                "images": images_base64,
                "max_length": max_length
            })
            logger.info(f"Successfully generated {len(result.get('captions', []))} captions in {result.get('processing_time', 0):.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error generating captions: {e}")
            raise
    
    def embed_text(self, texts: List[str]) -> Dict[str, Any]:
        """
        Generate embeddings for text queries (for image-text retrieval)
        
        Args:
            texts: List of text queries
            
        Returns:
            Dictionary with:
                - embeddings: List of embedding vectors (1152-dim)
                - dimension: Embedding dimension
                - processing_time: Time taken for processing
        """
        if not texts:
            logger.warning("Empty texts list provided to embed_text")
            return {"embeddings": [], "dimension": 1152, "processing_time": 0.0}
        
        logger.info(f"Embedding {len(texts)} text queries")
        
        try:
            result = self._make_request("/embed-text", texts)
            logger.info(f"Successfully embedded {len(texts)} texts in {result.get('processing_time', 0):.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error embedding text: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model
        
        Returns:
            Model information dictionary
        """
        try:
            response = requests.get(f"{self.service_url}/model-info", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error getting model info: {e}")
            return {"error": str(e)}

