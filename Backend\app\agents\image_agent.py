import asyncio
import base64
import io
import logging
from typing import Any, Dict, List, Optional

from PIL import Image
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from ..models.user import User
from ..models.user_space import UserSpace, SpaceType
from ..models.knowledge_base import KnowledgeDocument
from ..services.minio_service import minio_service
from ..services.space_permissions import SpacePermissions

logger = logging.getLogger(__name__)


class ImageVisionAgent:
    """Agent responsible for image-space query handling.

    This class encapsulates all business logic for selecting user images
    (potentially across multiple personal Image Spaces when explicit
    filenames are provided), preparing payloads, deciding inline vs Files
    API, calling the model, and shaping the response for the router.
    """

    async def analyze_images(
        self,
        query: str,
        space: UserSpace,
        space_id: int,
        allowed_files: Optional[List[str]],
        history: Optional[List[Dict]],
        current_user: User,
        db: AsyncSession,
        cancellation_event: asyncio.Event,
        agent_type: str,
        agent_name: str,
    ) -> Dict[str, Any]:
        # Import locally to avoid import weight on module load
        from ..config import (
            MAX_IMAGES_PER_QUERY,
            IMAGE_MAX_LONG_SIDE_PX,
            IMAGE_INLINE_MAX_SIZE_MB,
            GEMINI_API_KEY,
        )
        from google import genai
        from google.genai.types import GenerateContentConfig

        # 1) Resolve which images to use
        if allowed_files:
            # When explicit files are provided, allow images from ANY personal image space
            # the user can access (exclude shared). Preserve order from selection.
            result = await db.execute(
                select(KnowledgeDocument)
                .options(joinedload(KnowledgeDocument.space))
                .where(
                    KnowledgeDocument.document_type == "image",
                    KnowledgeDocument.filename.in_(allowed_files),
                )
                .order_by(KnowledgeDocument.created_at.desc())
            )
            query_images = result.scalars().all()

            order_index = {name: idx for idx, name in enumerate(allowed_files)}
            filtered_docs: List[KnowledgeDocument] = []
            for doc in query_images:
                sp = doc.space
                if not sp:
                    continue
                # Image playgrounds only (shared or personal). Permissions enforced below.
                if not sp.is_image_playground():
                    continue
                if not await SpacePermissions.can_query_documents(current_user, sp, db):
                    continue
                filtered_docs.append(doc)

            filtered_docs.sort(key=lambda d: order_index.get(d.filename, 1_000_000))
            images: List[KnowledgeDocument] = filtered_docs[:MAX_IMAGES_PER_QUERY]
        else:
            # Fallback: current space only (most recent first)
            result = await db.execute(
                select(KnowledgeDocument)
                .options(joinedload(KnowledgeDocument.space))
                .where(
                    KnowledgeDocument.space_id == space_id,
                    KnowledgeDocument.document_type == "image",
                )
                .order_by(KnowledgeDocument.created_at.desc())
                .limit(MAX_IMAGES_PER_QUERY)
            )
            images = result.scalars().all()

        if not images:
            return {
                "agent_name": agent_name,
                "query": query,
                "answer": "No images found in this space. Please upload images first.",
                "web_search_enabled": False,
                "document_search_enabled": False,
                "deep_search_enabled": False,
                "sources": [],
                "selected_files": [],
                "used_rag": False,
                "processing_stats": {"image_count": 0},
            }

        # 2) Download and process images
        processed_images: List[bytes] = []
        processed_mime_types: List[str] = []
        total_size_bytes = 0
        image_metadata: List[Dict[str, Any]] = []

        # Map PIL formats to IANA MIME types
        fmt_to_mime = {"JPEG": "image/jpeg", "PNG": "image/png", "WEBP": "image/webp"}

        for doc in images:
            try:
                # Download image
                image_bytes = minio_service.download_file(
                    bucket_name=doc.minio_bucket,
                    object_key=doc.minio_object_key,
                )
                current_mime = (doc.content_type or "image/jpeg").lower()

                # Resize if needed
                img = Image.open(io.BytesIO(image_bytes))
                if max(img.size) > IMAGE_MAX_LONG_SIDE_PX:
                    width, height = img.size
                    if width > height:
                        new_w = IMAGE_MAX_LONG_SIDE_PX
                        new_h = int(height * (IMAGE_MAX_LONG_SIDE_PX / width))
                    else:
                        new_h = IMAGE_MAX_LONG_SIDE_PX
                        new_w = int(width * (IMAGE_MAX_LONG_SIDE_PX / height))
                    img = img.resize((new_w, new_h), Image.Resampling.LANCZOS)

                    # Re-encode using original detected format if available
                    out = io.BytesIO()
                    img_format = (img.format or "JPEG").upper()
                    img.save(out, format=img_format, quality=85)
                    image_bytes = out.getvalue()
                    current_mime = fmt_to_mime.get(img_format, current_mime)

                processed_images.append(image_bytes)
                processed_mime_types.append(current_mime)
                total_size_bytes += len(image_bytes)
                image_metadata.append(
                    {
                        "document_id": doc.id,
                        "filename": doc.filename,
                        "title": doc.title,
                        "url": doc.file_url,
                        "content_type": doc.content_type,
                        "size_bytes": len(image_bytes),
                    }
                )
            except Exception as e:
                logger.error(f"Failed to process image {doc.id}: {e}")
                continue

        if not processed_images:
            return {
                "agent_name": agent_name,
                "query": query,
                "answer": "Failed to load images from storage. Please try again.",
                "web_search_enabled": False,
                "document_search_enabled": False,
                "deep_search_enabled": False,
                "sources": [],
                "selected_files": [],
                "used_rag": False,
                "processing_stats": {"image_count": 0, "error": "image_load_failed"},
            }

        # 3) Decide inline vs Files API
        total_size_mb = total_size_bytes / (1024 * 1024)
        use_files_api = total_size_mb >= IMAGE_INLINE_MAX_SIZE_MB

        # 4) Build prompt
        system_instruction = (
            "You are a helpful AI assistant analyzing images. "
            "Provide detailed, accurate responses based on the images provided."
        )

        formatted_history: List[Dict[str, Any]] = []
        if history:
            for msg in history:
                try:
                    raw_role = (msg.get("role") or "user").lower()
                except Exception:
                    raw_role = "user"
                # Gemini expects only roles: "user" and "model". Map assistant->model.
                role = "model" if raw_role == "assistant" else "user"
                formatted_history.append({"role": role, "parts": [{"text": msg.get("content", "")}]} )

        current_parts: List[Dict[str, Any]] = []
        current_parts.append({"text": query})

        if use_files_api:
            try:
                client = genai.Client(api_key=GEMINI_API_KEY)
                file_parts = []
                for idx, img_bytes in enumerate(processed_images):
                    upload = client.files.upload_bytes(
                        bytes=img_bytes,
                        mime_type=processed_mime_types[idx]
                        if idx < len(processed_mime_types)
                        else "image/jpeg",
                        display_name=images[idx].filename if idx < len(images) else f"image_{idx+1}.webp",
                    )
                    file_parts.append(
                        {
                            "file_data": {
                                "file_uri": upload.uri,
                                "mime_type": upload.mime_type
                                or (
                                    processed_mime_types[idx]
                                    if idx < len(processed_mime_types)
                                    else "image/jpeg"
                                ),
                            }
                        }
                    )

                current_parts.extend(file_parts)
            except Exception as e:
                logger.warning(f"Files API upload failed ({e}); falling back to inline data.")
                for idx, img_bytes in enumerate(processed_images):
                    mime_type = (
                        processed_mime_types[idx]
                        if idx < len(processed_mime_types)
                        else (images[idx].content_type if idx < len(images) else None)
                    ) or "image/jpeg"
                    current_parts.append(
                        {
                            "inline_data": {
                                "mime_type": mime_type,
                                "data": base64.b64encode(img_bytes).decode("utf-8"),
                            }
                        }
                    )
        else:
            for idx, img_bytes in enumerate(processed_images):
                mime_type = (
                    processed_mime_types[idx]
                    if idx < len(processed_mime_types)
                    else (images[idx].content_type if idx < len(images) else None)
                ) or "image/jpeg"
                current_parts.append(
                    {
                        "inline_data": {
                            "mime_type": mime_type,
                            "data": base64.b64encode(img_bytes).decode("utf-8"),
                        }
                    }
                )

        formatted_history.append({"role": "user", "parts": current_parts})

        # 5) Call the model
        try:
            client = genai.Client(api_key=GEMINI_API_KEY)
            response = client.models.generate_content(
                model="gemini-2.5-flash-lite",
                contents=formatted_history,
                config=GenerateContentConfig(
                    system_instruction=system_instruction
                ),
            )
            answer = response.text if response.text else "I couldn't generate a response. Please try again."
        except Exception as e:
            logger.error(f"Error calling Gemini for image query: {e}")
            answer = f"Error processing image query: {str(e)}"

        # 6) Build response
        return {
            "agent_name": agent_name,
            "query": query,
            "answer": answer,
            "web_search_enabled": False,
            "document_search_enabled": False,
            "deep_search_enabled": False,
            "sources": [
                {
                    "title": meta.get("title"),
                    "filename": meta.get("filename"),
                    "url": meta.get("url"),
                    "document_id": meta.get("document_id"),
                    "content_type": meta.get("content_type"),
                }
                for meta in image_metadata
            ],
            "selected_files": [meta["filename"] for meta in image_metadata],
            "used_rag": False,
            "processing_stats": {
                "image_count": len(processed_images),
                "total_size_mb": round(total_size_mb, 2),
                "used_files_api": use_files_api,
            },
        }


# Export a simple singleton to be used by the router
image_vision_agent = ImageVisionAgent()


