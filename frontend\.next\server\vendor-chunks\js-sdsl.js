"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/js-sdsl";
exports.ids = ["vendor-chunks/js-sdsl"];
exports.modules = {

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/ContainerBase/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/ContainerBase/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Base: () => (/* binding */ Base),\n/* harmony export */   Container: () => (/* binding */ Container),\n/* harmony export */   ContainerIterator: () => (/* binding */ ContainerIterator)\n/* harmony export */ });\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(n, t) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(n, t) {\n            n.__proto__ = t;\n        } || function(n, t) {\n            for (var r in t) if (Object.prototype.hasOwnProperty.call(t, r)) n[r] = t[r];\n        };\n        return extendStatics(n, t);\n    };\n    return function(n, t) {\n        if (typeof t !== \"function\" && t !== null) throw new TypeError(\"Class extends value \" + String(t) + \" is not a constructor or null\");\n        extendStatics(n, t);\n        function __() {\n            this.constructor = n;\n        }\n        n.prototype = t === null ? Object.create(t) : (__.prototype = t.prototype, new __);\n    };\n}();\n\nvar ContainerIterator = function() {\n    function ContainerIterator(n) {\n        if (n === void 0) {\n            n = 0;\n        }\n        this.iteratorType = n;\n    }\n    ContainerIterator.prototype.equals = function(n) {\n        return this.o === n.o;\n    };\n    return ContainerIterator;\n}();\n\n\n\nvar Base = function() {\n    function Base() {\n        this.M = 0;\n    }\n    Object.defineProperty(Base.prototype, \"length\", {\n        get: function() {\n            return this.M;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Base.prototype.size = function() {\n        return this.M;\n    };\n    Base.prototype.empty = function() {\n        return this.M === 0;\n    };\n    return Base;\n}();\n\n\n\nvar Container = function(n) {\n    __extends(Container, n);\n    function Container() {\n        return n !== null && n.apply(this, arguments) || this;\n    }\n    return Container;\n}(Base);\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/ContainerBase/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/HashContainer/Base/index.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/HashContainer/Base/index.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HashContainer: () => (/* binding */ HashContainer),\n/* harmony export */   HashContainerIterator: () => (/* binding */ HashContainerIterator)\n/* harmony export */ });\n/* harmony import */ var _ContainerBase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../ContainerBase */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/ContainerBase/index.js\");\n/* harmony import */ var _utils_checkObject__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../utils/checkObject */ \"(ssr)/./node_modules/js-sdsl/dist/esm/utils/checkObject.js\");\n/* harmony import */ var _utils_throwError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/throwError */ \"(ssr)/./node_modules/js-sdsl/dist/esm/utils/throwError.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(t, i) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(t, i) {\n            t.__proto__ = i;\n        } || function(t, i) {\n            for (var r in i) if (Object.prototype.hasOwnProperty.call(i, r)) t[r] = i[r];\n        };\n        return extendStatics(t, i);\n    };\n    return function(t, i) {\n        if (typeof i !== \"function\" && i !== null) throw new TypeError(\"Class extends value \" + String(i) + \" is not a constructor or null\");\n        extendStatics(t, i);\n        function __() {\n            this.constructor = t;\n        }\n        t.prototype = i === null ? Object.create(i) : (__.prototype = i.prototype, new __);\n    };\n}();\n\n\n\n\n\n\n\nvar HashContainerIterator = function(t) {\n    __extends(HashContainerIterator, t);\n    function HashContainerIterator(i, r, e) {\n        var n = t.call(this, e) || this;\n        n.o = i;\n        n.h = r;\n        if (n.iteratorType === 0) {\n            n.pre = function() {\n                if (this.o.L === this.h) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o = this.o.L;\n                return this;\n            };\n            n.next = function() {\n                if (this.o === this.h) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o = this.o.m;\n                return this;\n            };\n        } else {\n            n.pre = function() {\n                if (this.o.m === this.h) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o = this.o.m;\n                return this;\n            };\n            n.next = function() {\n                if (this.o === this.h) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o = this.o.L;\n                return this;\n            };\n        }\n        return n;\n    }\n    return HashContainerIterator;\n}(_ContainerBase__WEBPACK_IMPORTED_MODULE_1__.ContainerIterator);\n\n\n\nvar HashContainer = function(t) {\n    __extends(HashContainer, t);\n    function HashContainer() {\n        var i = t.call(this) || this;\n        i._ = [];\n        i.I = {};\n        i.HASH_TAG = Symbol(\"@@HASH_TAG\");\n        Object.setPrototypeOf(i.I, null);\n        i.h = {};\n        i.h.L = i.h.m = i.H = i.l = i.h;\n        return i;\n    }\n    HashContainer.prototype.G = function(t) {\n        var i = t.L, r = t.m;\n        i.m = r;\n        r.L = i;\n        if (t === this.H) {\n            this.H = r;\n        }\n        if (t === this.l) {\n            this.l = i;\n        }\n        this.M -= 1;\n    };\n    HashContainer.prototype.v = function(t, i, r) {\n        if (r === undefined) r = (0,_utils_checkObject__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(t);\n        var e;\n        if (r) {\n            var n = t[this.HASH_TAG];\n            if (n !== undefined) {\n                this._[n].p = i;\n                return this.M;\n            }\n            Object.defineProperty(t, this.HASH_TAG, {\n                value: this._.length,\n                configurable: true\n            });\n            e = {\n                u: t,\n                p: i,\n                L: this.l,\n                m: this.h\n            };\n            this._.push(e);\n        } else {\n            var s = this.I[t];\n            if (s) {\n                s.p = i;\n                return this.M;\n            }\n            e = {\n                u: t,\n                p: i,\n                L: this.l,\n                m: this.h\n            };\n            this.I[t] = e;\n        }\n        if (this.M === 0) {\n            this.H = e;\n            this.h.m = e;\n        } else {\n            this.l.m = e;\n        }\n        this.l = e;\n        this.h.L = e;\n        return ++this.M;\n    };\n    HashContainer.prototype.g = function(t, i) {\n        if (i === undefined) i = (0,_utils_checkObject__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(t);\n        if (i) {\n            var r = t[this.HASH_TAG];\n            if (r === undefined) return this.h;\n            return this._[r];\n        } else {\n            return this.I[t] || this.h;\n        }\n    };\n    HashContainer.prototype.clear = function() {\n        var t = this.HASH_TAG;\n        this._.forEach((function(i) {\n            delete i.u[t];\n        }));\n        this._ = [];\n        this.I = {};\n        Object.setPrototypeOf(this.I, null);\n        this.M = 0;\n        this.H = this.l = this.h.L = this.h.m = this.h;\n    };\n    HashContainer.prototype.eraseElementByKey = function(t, i) {\n        var r;\n        if (i === undefined) i = (0,_utils_checkObject__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(t);\n        if (i) {\n            var e = t[this.HASH_TAG];\n            if (e === undefined) return false;\n            delete t[this.HASH_TAG];\n            r = this._[e];\n            delete this._[e];\n        } else {\n            r = this.I[t];\n            if (r === undefined) return false;\n            delete this.I[t];\n        }\n        this.G(r);\n        return true;\n    };\n    HashContainer.prototype.eraseElementByIterator = function(t) {\n        var i = t.o;\n        if (i === this.h) {\n            (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n        }\n        this.G(i);\n        return t.next();\n    };\n    HashContainer.prototype.eraseElementByPos = function(t) {\n        if (t < 0 || t > this.M - 1) {\n            throw new RangeError;\n        }\n        var i = this.H;\n        while (t--) {\n            i = i.m;\n        }\n        this.G(i);\n        return this.M;\n    };\n    return HashContainer;\n}(_ContainerBase__WEBPACK_IMPORTED_MODULE_1__.Container);\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/HashContainer/Base/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/HashContainer/HashMap.js":
/*!**************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/HashContainer/HashMap.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Base */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/HashContainer/Base/index.js\");\n/* harmony import */ var _utils_checkObject__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/checkObject */ \"(ssr)/./node_modules/js-sdsl/dist/esm/utils/checkObject.js\");\n/* harmony import */ var _utils_throwError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/throwError */ \"(ssr)/./node_modules/js-sdsl/dist/esm/utils/throwError.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(t, r) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(t, r) {\n            t.__proto__ = r;\n        } || function(t, r) {\n            for (var n in r) if (Object.prototype.hasOwnProperty.call(r, n)) t[n] = r[n];\n        };\n        return extendStatics(t, r);\n    };\n    return function(t, r) {\n        if (typeof r !== \"function\" && r !== null) throw new TypeError(\"Class extends value \" + String(r) + \" is not a constructor or null\");\n        extendStatics(t, r);\n        function __() {\n            this.constructor = t;\n        }\n        t.prototype = r === null ? Object.create(r) : (__.prototype = r.prototype, new __);\n    };\n}();\n\nvar __generator = undefined && undefined.i || function(t, r) {\n    var n = {\n        label: 0,\n        sent: function() {\n            if (a[0] & 1) throw a[1];\n            return a[1];\n        },\n        trys: [],\n        ops: []\n    }, e, i, a, s;\n    return s = {\n        next: verb(0),\n        throw: verb(1),\n        return: verb(2)\n    }, typeof Symbol === \"function\" && (s[Symbol.iterator] = function() {\n        return this;\n    }), s;\n    function verb(t) {\n        return function(r) {\n            return step([ t, r ]);\n        };\n    }\n    function step(s) {\n        if (e) throw new TypeError(\"Generator is already executing.\");\n        while (n) try {\n            if (e = 1, i && (a = s[0] & 2 ? i[\"return\"] : s[0] ? i[\"throw\"] || ((a = i[\"return\"]) && a.call(i), \n            0) : i.next) && !(a = a.call(i, s[1])).done) return a;\n            if (i = 0, a) s = [ s[0] & 2, a.value ];\n            switch (s[0]) {\n              case 0:\n              case 1:\n                a = s;\n                break;\n\n              case 4:\n                n.label++;\n                return {\n                    value: s[1],\n                    done: false\n                };\n\n              case 5:\n                n.label++;\n                i = s[1];\n                s = [ 0 ];\n                continue;\n\n              case 7:\n                s = n.ops.pop();\n                n.trys.pop();\n                continue;\n\n              default:\n                if (!(a = n.trys, a = a.length > 0 && a[a.length - 1]) && (s[0] === 6 || s[0] === 2)) {\n                    n = 0;\n                    continue;\n                }\n                if (s[0] === 3 && (!a || s[1] > a[0] && s[1] < a[3])) {\n                    n.label = s[1];\n                    break;\n                }\n                if (s[0] === 6 && n.label < a[1]) {\n                    n.label = a[1];\n                    a = s;\n                    break;\n                }\n                if (a && n.label < a[2]) {\n                    n.label = a[2];\n                    n.ops.push(s);\n                    break;\n                }\n                if (a[2]) n.ops.pop();\n                n.trys.pop();\n                continue;\n            }\n            s = r.call(t, n);\n        } catch (t) {\n            s = [ 6, t ];\n            i = 0;\n        } finally {\n            e = a = 0;\n        }\n        if (s[0] & 5) throw s[1];\n        return {\n            value: s[0] ? s[1] : void 0,\n            done: true\n        };\n    }\n};\n\n\n\n\n\n\n\nvar HashMapIterator = function(t) {\n    __extends(HashMapIterator, t);\n    function HashMapIterator(r, n, e, i) {\n        var a = t.call(this, r, n, i) || this;\n        a.container = e;\n        return a;\n    }\n    Object.defineProperty(HashMapIterator.prototype, \"pointer\", {\n        get: function() {\n            if (this.o === this.h) {\n                (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n            }\n            var t = this;\n            return new Proxy([], {\n                get: function(r, n) {\n                    if (n === \"0\") return t.o.u; else if (n === \"1\") return t.o.p;\n                },\n                set: function(r, n, e) {\n                    if (n !== \"1\") {\n                        throw new TypeError(\"props must be 1\");\n                    }\n                    t.o.p = e;\n                    return true;\n                }\n            });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    HashMapIterator.prototype.copy = function() {\n        return new HashMapIterator(this.o, this.h, this.container, this.iteratorType);\n    };\n    return HashMapIterator;\n}(_Base__WEBPACK_IMPORTED_MODULE_1__.HashContainerIterator);\n\nvar HashMap = function(t) {\n    __extends(HashMap, t);\n    function HashMap(r) {\n        if (r === void 0) {\n            r = [];\n        }\n        var n = t.call(this) || this;\n        var e = n;\n        r.forEach((function(t) {\n            e.setElement(t[0], t[1]);\n        }));\n        return n;\n    }\n    HashMap.prototype.begin = function() {\n        return new HashMapIterator(this.H, this.h, this);\n    };\n    HashMap.prototype.end = function() {\n        return new HashMapIterator(this.h, this.h, this);\n    };\n    HashMap.prototype.rBegin = function() {\n        return new HashMapIterator(this.l, this.h, this, 1);\n    };\n    HashMap.prototype.rEnd = function() {\n        return new HashMapIterator(this.h, this.h, this, 1);\n    };\n    HashMap.prototype.front = function() {\n        if (this.M === 0) return;\n        return [ this.H.u, this.H.p ];\n    };\n    HashMap.prototype.back = function() {\n        if (this.M === 0) return;\n        return [ this.l.u, this.l.p ];\n    };\n    HashMap.prototype.setElement = function(t, r, n) {\n        return this.v(t, r, n);\n    };\n    HashMap.prototype.getElementByKey = function(t, r) {\n        if (r === undefined) r = (0,_utils_checkObject__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(t);\n        if (r) {\n            var n = t[this.HASH_TAG];\n            return n !== undefined ? this._[n].p : undefined;\n        }\n        var e = this.I[t];\n        return e ? e.p : undefined;\n    };\n    HashMap.prototype.getElementByPos = function(t) {\n        if (t < 0 || t > this.M - 1) {\n            throw new RangeError;\n        }\n        var r = this.H;\n        while (t--) {\n            r = r.m;\n        }\n        return [ r.u, r.p ];\n    };\n    HashMap.prototype.find = function(t, r) {\n        var n = this.g(t, r);\n        return new HashMapIterator(n, this.h, this);\n    };\n    HashMap.prototype.forEach = function(t) {\n        var r = 0;\n        var n = this.H;\n        while (n !== this.h) {\n            t([ n.u, n.p ], r++, this);\n            n = n.m;\n        }\n    };\n    HashMap.prototype[Symbol.iterator] = function() {\n        return function() {\n            var t;\n            return __generator(this, (function(r) {\n                switch (r.label) {\n                  case 0:\n                    t = this.H;\n                    r.label = 1;\n\n                  case 1:\n                    if (!(t !== this.h)) return [ 3, 3 ];\n                    return [ 4, [ t.u, t.p ] ];\n\n                  case 2:\n                    r.sent();\n                    t = t.m;\n                    return [ 3, 1 ];\n\n                  case 3:\n                    return [ 2 ];\n                }\n            }));\n        }.bind(this)();\n    };\n    return HashMap;\n}(_Base__WEBPACK_IMPORTED_MODULE_1__.HashContainer);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HashMap);\n//# sourceMappingURL=HashMap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/HashContainer/HashMap.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/HashContainer/HashSet.js":
/*!**************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/HashContainer/HashSet.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Base */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/HashContainer/Base/index.js\");\n/* harmony import */ var _utils_throwError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/throwError */ \"(ssr)/./node_modules/js-sdsl/dist/esm/utils/throwError.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(t, r) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(t, r) {\n            t.__proto__ = r;\n        } || function(t, r) {\n            for (var e in r) if (Object.prototype.hasOwnProperty.call(r, e)) t[e] = r[e];\n        };\n        return extendStatics(t, r);\n    };\n    return function(t, r) {\n        if (typeof r !== \"function\" && r !== null) throw new TypeError(\"Class extends value \" + String(r) + \" is not a constructor or null\");\n        extendStatics(t, r);\n        function __() {\n            this.constructor = t;\n        }\n        t.prototype = r === null ? Object.create(r) : (__.prototype = r.prototype, new __);\n    };\n}();\n\nvar __generator = undefined && undefined.i || function(t, r) {\n    var e = {\n        label: 0,\n        sent: function() {\n            if (s[0] & 1) throw s[1];\n            return s[1];\n        },\n        trys: [],\n        ops: []\n    }, n, i, s, a;\n    return a = {\n        next: verb(0),\n        throw: verb(1),\n        return: verb(2)\n    }, typeof Symbol === \"function\" && (a[Symbol.iterator] = function() {\n        return this;\n    }), a;\n    function verb(t) {\n        return function(r) {\n            return step([ t, r ]);\n        };\n    }\n    function step(a) {\n        if (n) throw new TypeError(\"Generator is already executing.\");\n        while (e) try {\n            if (n = 1, i && (s = a[0] & 2 ? i[\"return\"] : a[0] ? i[\"throw\"] || ((s = i[\"return\"]) && s.call(i), \n            0) : i.next) && !(s = s.call(i, a[1])).done) return s;\n            if (i = 0, s) a = [ a[0] & 2, s.value ];\n            switch (a[0]) {\n              case 0:\n              case 1:\n                s = a;\n                break;\n\n              case 4:\n                e.label++;\n                return {\n                    value: a[1],\n                    done: false\n                };\n\n              case 5:\n                e.label++;\n                i = a[1];\n                a = [ 0 ];\n                continue;\n\n              case 7:\n                a = e.ops.pop();\n                e.trys.pop();\n                continue;\n\n              default:\n                if (!(s = e.trys, s = s.length > 0 && s[s.length - 1]) && (a[0] === 6 || a[0] === 2)) {\n                    e = 0;\n                    continue;\n                }\n                if (a[0] === 3 && (!s || a[1] > s[0] && a[1] < s[3])) {\n                    e.label = a[1];\n                    break;\n                }\n                if (a[0] === 6 && e.label < s[1]) {\n                    e.label = s[1];\n                    s = a;\n                    break;\n                }\n                if (s && e.label < s[2]) {\n                    e.label = s[2];\n                    e.ops.push(a);\n                    break;\n                }\n                if (s[2]) e.ops.pop();\n                e.trys.pop();\n                continue;\n            }\n            a = r.call(t, e);\n        } catch (t) {\n            a = [ 6, t ];\n            i = 0;\n        } finally {\n            n = s = 0;\n        }\n        if (a[0] & 5) throw a[1];\n        return {\n            value: a[0] ? a[1] : void 0,\n            done: true\n        };\n    }\n};\n\n\n\n\n\nvar HashSetIterator = function(t) {\n    __extends(HashSetIterator, t);\n    function HashSetIterator(r, e, n, i) {\n        var s = t.call(this, r, e, i) || this;\n        s.container = n;\n        return s;\n    }\n    Object.defineProperty(HashSetIterator.prototype, \"pointer\", {\n        get: function() {\n            if (this.o === this.h) {\n                (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n            }\n            return this.o.u;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    HashSetIterator.prototype.copy = function() {\n        return new HashSetIterator(this.o, this.h, this.container, this.iteratorType);\n    };\n    return HashSetIterator;\n}(_Base__WEBPACK_IMPORTED_MODULE_1__.HashContainerIterator);\n\nvar HashSet = function(t) {\n    __extends(HashSet, t);\n    function HashSet(r) {\n        if (r === void 0) {\n            r = [];\n        }\n        var e = t.call(this) || this;\n        var n = e;\n        r.forEach((function(t) {\n            n.insert(t);\n        }));\n        return e;\n    }\n    HashSet.prototype.begin = function() {\n        return new HashSetIterator(this.H, this.h, this);\n    };\n    HashSet.prototype.end = function() {\n        return new HashSetIterator(this.h, this.h, this);\n    };\n    HashSet.prototype.rBegin = function() {\n        return new HashSetIterator(this.l, this.h, this, 1);\n    };\n    HashSet.prototype.rEnd = function() {\n        return new HashSetIterator(this.h, this.h, this, 1);\n    };\n    HashSet.prototype.front = function() {\n        return this.H.u;\n    };\n    HashSet.prototype.back = function() {\n        return this.l.u;\n    };\n    HashSet.prototype.insert = function(t, r) {\n        return this.v(t, undefined, r);\n    };\n    HashSet.prototype.getElementByPos = function(t) {\n        if (t < 0 || t > this.M - 1) {\n            throw new RangeError;\n        }\n        var r = this.H;\n        while (t--) {\n            r = r.m;\n        }\n        return r.u;\n    };\n    HashSet.prototype.find = function(t, r) {\n        var e = this.g(t, r);\n        return new HashSetIterator(e, this.h, this);\n    };\n    HashSet.prototype.forEach = function(t) {\n        var r = 0;\n        var e = this.H;\n        while (e !== this.h) {\n            t(e.u, r++, this);\n            e = e.m;\n        }\n    };\n    HashSet.prototype[Symbol.iterator] = function() {\n        return function() {\n            var t;\n            return __generator(this, (function(r) {\n                switch (r.label) {\n                  case 0:\n                    t = this.H;\n                    r.label = 1;\n\n                  case 1:\n                    if (!(t !== this.h)) return [ 3, 3 ];\n                    return [ 4, t.u ];\n\n                  case 2:\n                    r.sent();\n                    t = t.m;\n                    return [ 3, 1 ];\n\n                  case 3:\n                    return [ 2 ];\n                }\n            }));\n        }.bind(this)();\n    };\n    return HashSet;\n}(_Base__WEBPACK_IMPORTED_MODULE_1__.HashContainer);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HashSet);\n//# sourceMappingURL=HashSet.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/HashContainer/HashSet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/OtherContainer/PriorityQueue.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/OtherContainer/PriorityQueue.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ContainerBase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../ContainerBase */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/ContainerBase/index.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(i, r) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(i, r) {\n            i.__proto__ = r;\n        } || function(i, r) {\n            for (var t in r) if (Object.prototype.hasOwnProperty.call(r, t)) i[t] = r[t];\n        };\n        return extendStatics(i, r);\n    };\n    return function(i, r) {\n        if (typeof r !== \"function\" && r !== null) throw new TypeError(\"Class extends value \" + String(r) + \" is not a constructor or null\");\n        extendStatics(i, r);\n        function __() {\n            this.constructor = i;\n        }\n        i.prototype = r === null ? Object.create(r) : (__.prototype = r.prototype, new __);\n    };\n}();\n\nvar __read = undefined && undefined.q || function(i, r) {\n    var t = typeof Symbol === \"function\" && i[Symbol.iterator];\n    if (!t) return i;\n    var e = t.call(i), n, u = [], s;\n    try {\n        while ((r === void 0 || r-- > 0) && !(n = e.next()).done) u.push(n.value);\n    } catch (i) {\n        s = {\n            error: i\n        };\n    } finally {\n        try {\n            if (n && !n.done && (t = e[\"return\"])) t.call(e);\n        } finally {\n            if (s) throw s.error;\n        }\n    }\n    return u;\n};\n\nvar __spreadArray = undefined && undefined.D || function(i, r, t) {\n    if (t || arguments.length === 2) for (var e = 0, n = r.length, u; e < n; e++) {\n        if (u || !(e in r)) {\n            if (!u) u = Array.prototype.slice.call(r, 0, e);\n            u[e] = r[e];\n        }\n    }\n    return i.concat(u || Array.prototype.slice.call(r));\n};\n\n\n\nvar PriorityQueue = function(i) {\n    __extends(PriorityQueue, i);\n    function PriorityQueue(r, t, e) {\n        if (r === void 0) {\n            r = [];\n        }\n        if (t === void 0) {\n            t = function(i, r) {\n                if (i > r) return -1;\n                if (i < r) return 1;\n                return 0;\n            };\n        }\n        if (e === void 0) {\n            e = true;\n        }\n        var n = i.call(this) || this;\n        n.$ = t;\n        if (Array.isArray(r)) {\n            n.ii = e ? __spreadArray([], __read(r), false) : r;\n        } else {\n            n.ii = [];\n            var u = n;\n            r.forEach((function(i) {\n                u.ii.push(i);\n            }));\n        }\n        n.M = n.ii.length;\n        var s = n.M >> 1;\n        for (var o = n.M - 1 >> 1; o >= 0; --o) {\n            n.ri(o, s);\n        }\n        return n;\n    }\n    PriorityQueue.prototype.ti = function(i) {\n        var r = this.ii[i];\n        while (i > 0) {\n            var t = i - 1 >> 1;\n            var e = this.ii[t];\n            if (this.$(e, r) <= 0) break;\n            this.ii[i] = e;\n            i = t;\n        }\n        this.ii[i] = r;\n    };\n    PriorityQueue.prototype.ri = function(i, r) {\n        var t = this.ii[i];\n        while (i < r) {\n            var e = i << 1 | 1;\n            var n = e + 1;\n            var u = this.ii[e];\n            if (n < this.M && this.$(u, this.ii[n]) > 0) {\n                e = n;\n                u = this.ii[n];\n            }\n            if (this.$(u, t) >= 0) break;\n            this.ii[i] = u;\n            i = e;\n        }\n        this.ii[i] = t;\n    };\n    PriorityQueue.prototype.clear = function() {\n        this.M = 0;\n        this.ii.length = 0;\n    };\n    PriorityQueue.prototype.push = function(i) {\n        this.ii.push(i);\n        this.ti(this.M);\n        this.M += 1;\n    };\n    PriorityQueue.prototype.pop = function() {\n        if (this.M === 0) return;\n        var i = this.ii[0];\n        var r = this.ii.pop();\n        this.M -= 1;\n        if (this.M) {\n            this.ii[0] = r;\n            this.ri(0, this.M >> 1);\n        }\n        return i;\n    };\n    PriorityQueue.prototype.top = function() {\n        return this.ii[0];\n    };\n    PriorityQueue.prototype.find = function(i) {\n        return this.ii.indexOf(i) >= 0;\n    };\n    PriorityQueue.prototype.remove = function(i) {\n        var r = this.ii.indexOf(i);\n        if (r < 0) return false;\n        if (r === 0) {\n            this.pop();\n        } else if (r === this.M - 1) {\n            this.ii.pop();\n            this.M -= 1;\n        } else {\n            this.ii.splice(r, 1, this.ii.pop());\n            this.M -= 1;\n            this.ti(r);\n            this.ri(r, this.M >> 1);\n        }\n        return true;\n    };\n    PriorityQueue.prototype.updateItem = function(i) {\n        var r = this.ii.indexOf(i);\n        if (r < 0) return false;\n        this.ti(r);\n        this.ri(r, this.M >> 1);\n        return true;\n    };\n    PriorityQueue.prototype.toArray = function() {\n        return __spreadArray([], __read(this.ii), false);\n    };\n    return PriorityQueue;\n}(_ContainerBase__WEBPACK_IMPORTED_MODULE_0__.Base);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PriorityQueue);\n//# sourceMappingURL=PriorityQueue.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/OtherContainer/PriorityQueue.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/OtherContainer/Queue.js":
/*!*************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/OtherContainer/Queue.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ContainerBase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../ContainerBase */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/ContainerBase/index.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(t, i) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(t, i) {\n            t.__proto__ = i;\n        } || function(t, i) {\n            for (var n in i) if (Object.prototype.hasOwnProperty.call(i, n)) t[n] = i[n];\n        };\n        return extendStatics(t, i);\n    };\n    return function(t, i) {\n        if (typeof i !== \"function\" && i !== null) throw new TypeError(\"Class extends value \" + String(i) + \" is not a constructor or null\");\n        extendStatics(t, i);\n        function __() {\n            this.constructor = t;\n        }\n        t.prototype = i === null ? Object.create(i) : (__.prototype = i.prototype, new __);\n    };\n}();\n\n\n\nvar Queue = function(t) {\n    __extends(Queue, t);\n    function Queue(i) {\n        if (i === void 0) {\n            i = [];\n        }\n        var n = t.call(this) || this;\n        n.A = 0;\n        n.tt = [];\n        var e = n;\n        i.forEach((function(t) {\n            e.push(t);\n        }));\n        return n;\n    }\n    Queue.prototype.clear = function() {\n        this.tt = [];\n        this.M = this.A = 0;\n    };\n    Queue.prototype.push = function(t) {\n        var i = this.tt.length;\n        if (this.A / i > .5 && this.A + this.M >= i && i > 4096) {\n            var n = this.M;\n            for (var e = 0; e < n; ++e) {\n                this.tt[e] = this.tt[this.A + e];\n            }\n            this.A = 0;\n            this.tt[this.M] = t;\n        } else this.tt[this.A + this.M] = t;\n        return ++this.M;\n    };\n    Queue.prototype.pop = function() {\n        if (this.M === 0) return;\n        var t = this.tt[this.A++];\n        this.M -= 1;\n        return t;\n    };\n    Queue.prototype.front = function() {\n        if (this.M === 0) return;\n        return this.tt[this.A];\n    };\n    return Queue;\n}(_ContainerBase__WEBPACK_IMPORTED_MODULE_0__.Base);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Queue);\n//# sourceMappingURL=Queue.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/OtherContainer/Queue.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/OtherContainer/Stack.js":
/*!*************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/OtherContainer/Stack.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ContainerBase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../ContainerBase */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/ContainerBase/index.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(t, n) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(t, n) {\n            t.__proto__ = n;\n        } || function(t, n) {\n            for (var i in n) if (Object.prototype.hasOwnProperty.call(n, i)) t[i] = n[i];\n        };\n        return extendStatics(t, n);\n    };\n    return function(t, n) {\n        if (typeof n !== \"function\" && n !== null) throw new TypeError(\"Class extends value \" + String(n) + \" is not a constructor or null\");\n        extendStatics(t, n);\n        function __() {\n            this.constructor = t;\n        }\n        t.prototype = n === null ? Object.create(n) : (__.prototype = n.prototype, new __);\n    };\n}();\n\n\n\nvar Stack = function(t) {\n    __extends(Stack, t);\n    function Stack(n) {\n        if (n === void 0) {\n            n = [];\n        }\n        var i = t.call(this) || this;\n        i.nt = [];\n        var r = i;\n        n.forEach((function(t) {\n            r.push(t);\n        }));\n        return i;\n    }\n    Stack.prototype.clear = function() {\n        this.M = 0;\n        this.nt = [];\n    };\n    Stack.prototype.push = function(t) {\n        this.nt.push(t);\n        this.M += 1;\n        return this.M;\n    };\n    Stack.prototype.pop = function() {\n        if (this.M === 0) return;\n        this.M -= 1;\n        return this.nt.pop();\n    };\n    Stack.prototype.top = function() {\n        return this.nt[this.M - 1];\n    };\n    return Stack;\n}(_ContainerBase__WEBPACK_IMPORTED_MODULE_0__.Base);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Stack);\n//# sourceMappingURL=Stack.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/OtherContainer/Stack.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Base/RandomIterator.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Base/RandomIterator.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RandomIterator: () => (/* binding */ RandomIterator)\n/* harmony export */ });\n/* harmony import */ var _ContainerBase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../ContainerBase */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/ContainerBase/index.js\");\n/* harmony import */ var _utils_throwError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/throwError */ \"(ssr)/./node_modules/js-sdsl/dist/esm/utils/throwError.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(t, r) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(t, r) {\n            t.__proto__ = r;\n        } || function(t, r) {\n            for (var n in r) if (Object.prototype.hasOwnProperty.call(r, n)) t[n] = r[n];\n        };\n        return extendStatics(t, r);\n    };\n    return function(t, r) {\n        if (typeof r !== \"function\" && r !== null) throw new TypeError(\"Class extends value \" + String(r) + \" is not a constructor or null\");\n        extendStatics(t, r);\n        function __() {\n            this.constructor = t;\n        }\n        t.prototype = r === null ? Object.create(r) : (__.prototype = r.prototype, new __);\n    };\n}();\n\n\n\n\n\nvar RandomIterator = function(t) {\n    __extends(RandomIterator, t);\n    function RandomIterator(r, n) {\n        var o = t.call(this, n) || this;\n        o.o = r;\n        if (o.iteratorType === 0) {\n            o.pre = function() {\n                if (this.o === 0) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o -= 1;\n                return this;\n            };\n            o.next = function() {\n                if (this.o === this.container.size()) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o += 1;\n                return this;\n            };\n        } else {\n            o.pre = function() {\n                if (this.o === this.container.size() - 1) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o += 1;\n                return this;\n            };\n            o.next = function() {\n                if (this.o === -1) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o -= 1;\n                return this;\n            };\n        }\n        return o;\n    }\n    Object.defineProperty(RandomIterator.prototype, \"pointer\", {\n        get: function() {\n            return this.container.getElementByPos(this.o);\n        },\n        set: function(t) {\n            this.container.setElementByPos(this.o, t);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return RandomIterator;\n}(_ContainerBase__WEBPACK_IMPORTED_MODULE_1__.ContainerIterator);\n\n\n//# sourceMappingURL=RandomIterator.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Base/RandomIterator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Base/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Base/index.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ContainerBase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../ContainerBase */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/ContainerBase/index.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(n, t) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(n, t) {\n            n.__proto__ = t;\n        } || function(n, t) {\n            for (var e in t) if (Object.prototype.hasOwnProperty.call(t, e)) n[e] = t[e];\n        };\n        return extendStatics(n, t);\n    };\n    return function(n, t) {\n        if (typeof t !== \"function\" && t !== null) throw new TypeError(\"Class extends value \" + String(t) + \" is not a constructor or null\");\n        extendStatics(n, t);\n        function __() {\n            this.constructor = n;\n        }\n        n.prototype = t === null ? Object.create(t) : (__.prototype = t.prototype, new __);\n    };\n}();\n\n\n\nvar SequentialContainer = function(n) {\n    __extends(SequentialContainer, n);\n    function SequentialContainer() {\n        return n !== null && n.apply(this, arguments) || this;\n    }\n    return SequentialContainer;\n}(_ContainerBase__WEBPACK_IMPORTED_MODULE_0__.Container);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SequentialContainer);\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Base/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Deque.js":
/*!******************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Deque.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Base */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Base/index.js\");\n/* harmony import */ var _Base_RandomIterator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Base/RandomIterator */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Base/RandomIterator.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(t, i) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(t, i) {\n            t.__proto__ = i;\n        } || function(t, i) {\n            for (var r in i) if (Object.prototype.hasOwnProperty.call(i, r)) t[r] = i[r];\n        };\n        return extendStatics(t, i);\n    };\n    return function(t, i) {\n        if (typeof i !== \"function\" && i !== null) throw new TypeError(\"Class extends value \" + String(i) + \" is not a constructor or null\");\n        extendStatics(t, i);\n        function __() {\n            this.constructor = t;\n        }\n        t.prototype = i === null ? Object.create(i) : (__.prototype = i.prototype, new __);\n    };\n}();\n\nvar __generator = undefined && undefined.i || function(t, i) {\n    var r = {\n        label: 0,\n        sent: function() {\n            if (h[0] & 1) throw h[1];\n            return h[1];\n        },\n        trys: [],\n        ops: []\n    }, e, s, h, n;\n    return n = {\n        next: verb(0),\n        throw: verb(1),\n        return: verb(2)\n    }, typeof Symbol === \"function\" && (n[Symbol.iterator] = function() {\n        return this;\n    }), n;\n    function verb(t) {\n        return function(i) {\n            return step([ t, i ]);\n        };\n    }\n    function step(n) {\n        if (e) throw new TypeError(\"Generator is already executing.\");\n        while (r) try {\n            if (e = 1, s && (h = n[0] & 2 ? s[\"return\"] : n[0] ? s[\"throw\"] || ((h = s[\"return\"]) && h.call(s), \n            0) : s.next) && !(h = h.call(s, n[1])).done) return h;\n            if (s = 0, h) n = [ n[0] & 2, h.value ];\n            switch (n[0]) {\n              case 0:\n              case 1:\n                h = n;\n                break;\n\n              case 4:\n                r.label++;\n                return {\n                    value: n[1],\n                    done: false\n                };\n\n              case 5:\n                r.label++;\n                s = n[1];\n                n = [ 0 ];\n                continue;\n\n              case 7:\n                n = r.ops.pop();\n                r.trys.pop();\n                continue;\n\n              default:\n                if (!(h = r.trys, h = h.length > 0 && h[h.length - 1]) && (n[0] === 6 || n[0] === 2)) {\n                    r = 0;\n                    continue;\n                }\n                if (n[0] === 3 && (!h || n[1] > h[0] && n[1] < h[3])) {\n                    r.label = n[1];\n                    break;\n                }\n                if (n[0] === 6 && r.label < h[1]) {\n                    r.label = h[1];\n                    h = n;\n                    break;\n                }\n                if (h && r.label < h[2]) {\n                    r.label = h[2];\n                    r.ops.push(n);\n                    break;\n                }\n                if (h[2]) r.ops.pop();\n                r.trys.pop();\n                continue;\n            }\n            n = i.call(t, r);\n        } catch (t) {\n            n = [ 6, t ];\n            s = 0;\n        } finally {\n            e = h = 0;\n        }\n        if (n[0] & 5) throw n[1];\n        return {\n            value: n[0] ? n[1] : void 0,\n            done: true\n        };\n    }\n};\n\nvar __read = undefined && undefined.q || function(t, i) {\n    var r = typeof Symbol === \"function\" && t[Symbol.iterator];\n    if (!r) return t;\n    var e = r.call(t), s, h = [], n;\n    try {\n        while ((i === void 0 || i-- > 0) && !(s = e.next()).done) h.push(s.value);\n    } catch (t) {\n        n = {\n            error: t\n        };\n    } finally {\n        try {\n            if (s && !s.done && (r = e[\"return\"])) r.call(e);\n        } finally {\n            if (n) throw n.error;\n        }\n    }\n    return h;\n};\n\nvar __spreadArray = undefined && undefined.D || function(t, i, r) {\n    if (r || arguments.length === 2) for (var e = 0, s = i.length, h; e < s; e++) {\n        if (h || !(e in i)) {\n            if (!h) h = Array.prototype.slice.call(i, 0, e);\n            h[e] = i[e];\n        }\n    }\n    return t.concat(h || Array.prototype.slice.call(i));\n};\n\n\n\n\n\nvar DequeIterator = function(t) {\n    __extends(DequeIterator, t);\n    function DequeIterator(i, r, e) {\n        var s = t.call(this, i, e) || this;\n        s.container = r;\n        return s;\n    }\n    DequeIterator.prototype.copy = function() {\n        return new DequeIterator(this.o, this.container, this.iteratorType);\n    };\n    return DequeIterator;\n}(_Base_RandomIterator__WEBPACK_IMPORTED_MODULE_0__.RandomIterator);\n\nvar Deque = function(t) {\n    __extends(Deque, t);\n    function Deque(i, r) {\n        if (i === void 0) {\n            i = [];\n        }\n        if (r === void 0) {\n            r = 1 << 12;\n        }\n        var e = t.call(this) || this;\n        e.A = 0;\n        e.S = 0;\n        e.R = 0;\n        e.k = 0;\n        e.C = 0;\n        e.j = [];\n        var s = function() {\n            if (typeof i.length === \"number\") return i.length;\n            if (typeof i.size === \"number\") return i.size;\n            if (typeof i.size === \"function\") return i.size();\n            throw new TypeError(\"Cannot get the length or size of the container\");\n        }();\n        e.B = r;\n        e.C = Math.max(Math.ceil(s / e.B), 1);\n        for (var h = 0; h < e.C; ++h) {\n            e.j.push(new Array(e.B));\n        }\n        var n = Math.ceil(s / e.B);\n        e.A = e.R = (e.C >> 1) - (n >> 1);\n        e.S = e.k = e.B - s % e.B >> 1;\n        var u = e;\n        i.forEach((function(t) {\n            u.pushBack(t);\n        }));\n        return e;\n    }\n    Deque.prototype.O = function() {\n        var t = [];\n        var i = Math.max(this.C >> 1, 1);\n        for (var r = 0; r < i; ++r) {\n            t[r] = new Array(this.B);\n        }\n        for (var r = this.A; r < this.C; ++r) {\n            t[t.length] = this.j[r];\n        }\n        for (var r = 0; r < this.R; ++r) {\n            t[t.length] = this.j[r];\n        }\n        t[t.length] = __spreadArray([], __read(this.j[this.R]), false);\n        this.A = i;\n        this.R = t.length - 1;\n        for (var r = 0; r < i; ++r) {\n            t[t.length] = new Array(this.B);\n        }\n        this.j = t;\n        this.C = t.length;\n    };\n    Deque.prototype.T = function(t) {\n        var i = this.S + t + 1;\n        var r = i % this.B;\n        var e = r - 1;\n        var s = this.A + (i - r) / this.B;\n        if (r === 0) s -= 1;\n        s %= this.C;\n        if (e < 0) e += this.B;\n        return {\n            curNodeBucketIndex: s,\n            curNodePointerIndex: e\n        };\n    };\n    Deque.prototype.clear = function() {\n        this.j = [ new Array(this.B) ];\n        this.C = 1;\n        this.A = this.R = this.M = 0;\n        this.S = this.k = this.B >> 1;\n    };\n    Deque.prototype.begin = function() {\n        return new DequeIterator(0, this);\n    };\n    Deque.prototype.end = function() {\n        return new DequeIterator(this.M, this);\n    };\n    Deque.prototype.rBegin = function() {\n        return new DequeIterator(this.M - 1, this, 1);\n    };\n    Deque.prototype.rEnd = function() {\n        return new DequeIterator(-1, this, 1);\n    };\n    Deque.prototype.front = function() {\n        if (this.M === 0) return;\n        return this.j[this.A][this.S];\n    };\n    Deque.prototype.back = function() {\n        if (this.M === 0) return;\n        return this.j[this.R][this.k];\n    };\n    Deque.prototype.pushBack = function(t) {\n        if (this.M) {\n            if (this.k < this.B - 1) {\n                this.k += 1;\n            } else if (this.R < this.C - 1) {\n                this.R += 1;\n                this.k = 0;\n            } else {\n                this.R = 0;\n                this.k = 0;\n            }\n            if (this.R === this.A && this.k === this.S) this.O();\n        }\n        this.M += 1;\n        this.j[this.R][this.k] = t;\n        return this.M;\n    };\n    Deque.prototype.popBack = function() {\n        if (this.M === 0) return;\n        var t = this.j[this.R][this.k];\n        if (this.M !== 1) {\n            if (this.k > 0) {\n                this.k -= 1;\n            } else if (this.R > 0) {\n                this.R -= 1;\n                this.k = this.B - 1;\n            } else {\n                this.R = this.C - 1;\n                this.k = this.B - 1;\n            }\n        }\n        this.M -= 1;\n        return t;\n    };\n    Deque.prototype.pushFront = function(t) {\n        if (this.M) {\n            if (this.S > 0) {\n                this.S -= 1;\n            } else if (this.A > 0) {\n                this.A -= 1;\n                this.S = this.B - 1;\n            } else {\n                this.A = this.C - 1;\n                this.S = this.B - 1;\n            }\n            if (this.A === this.R && this.S === this.k) this.O();\n        }\n        this.M += 1;\n        this.j[this.A][this.S] = t;\n        return this.M;\n    };\n    Deque.prototype.popFront = function() {\n        if (this.M === 0) return;\n        var t = this.j[this.A][this.S];\n        if (this.M !== 1) {\n            if (this.S < this.B - 1) {\n                this.S += 1;\n            } else if (this.A < this.C - 1) {\n                this.A += 1;\n                this.S = 0;\n            } else {\n                this.A = 0;\n                this.S = 0;\n            }\n        }\n        this.M -= 1;\n        return t;\n    };\n    Deque.prototype.getElementByPos = function(t) {\n        if (t < 0 || t > this.M - 1) {\n            throw new RangeError;\n        }\n        var i = this.T(t), r = i.curNodeBucketIndex, e = i.curNodePointerIndex;\n        return this.j[r][e];\n    };\n    Deque.prototype.setElementByPos = function(t, i) {\n        if (t < 0 || t > this.M - 1) {\n            throw new RangeError;\n        }\n        var r = this.T(t), e = r.curNodeBucketIndex, s = r.curNodePointerIndex;\n        this.j[e][s] = i;\n    };\n    Deque.prototype.insert = function(t, i, r) {\n        if (r === void 0) {\n            r = 1;\n        }\n        if (t < 0 || t > this.M) {\n            throw new RangeError;\n        }\n        if (t === 0) {\n            while (r--) this.pushFront(i);\n        } else if (t === this.M) {\n            while (r--) this.pushBack(i);\n        } else {\n            var e = [];\n            for (var s = t; s < this.M; ++s) {\n                e.push(this.getElementByPos(s));\n            }\n            this.cut(t - 1);\n            for (var s = 0; s < r; ++s) this.pushBack(i);\n            for (var s = 0; s < e.length; ++s) this.pushBack(e[s]);\n        }\n        return this.M;\n    };\n    Deque.prototype.cut = function(t) {\n        if (t < 0) {\n            this.clear();\n            return 0;\n        }\n        var i = this.T(t), r = i.curNodeBucketIndex, e = i.curNodePointerIndex;\n        this.R = r;\n        this.k = e;\n        this.M = t + 1;\n        return this.M;\n    };\n    Deque.prototype.eraseElementByPos = function(t) {\n        if (t < 0 || t > this.M - 1) {\n            throw new RangeError;\n        }\n        if (t === 0) this.popFront(); else if (t === this.M - 1) this.popBack(); else {\n            var i = [];\n            for (var r = t + 1; r < this.M; ++r) {\n                i.push(this.getElementByPos(r));\n            }\n            this.cut(t);\n            this.popBack();\n            var e = this;\n            i.forEach((function(t) {\n                e.pushBack(t);\n            }));\n        }\n        return this.M;\n    };\n    Deque.prototype.eraseElementByValue = function(t) {\n        if (this.M === 0) return 0;\n        var i = [];\n        for (var r = 0; r < this.M; ++r) {\n            var e = this.getElementByPos(r);\n            if (e !== t) i.push(e);\n        }\n        var s = i.length;\n        for (var r = 0; r < s; ++r) this.setElementByPos(r, i[r]);\n        return this.cut(s - 1);\n    };\n    Deque.prototype.eraseElementByIterator = function(t) {\n        var i = t.o;\n        this.eraseElementByPos(i);\n        t = t.next();\n        return t;\n    };\n    Deque.prototype.find = function(t) {\n        for (var i = 0; i < this.M; ++i) {\n            if (this.getElementByPos(i) === t) {\n                return new DequeIterator(i, this);\n            }\n        }\n        return this.end();\n    };\n    Deque.prototype.reverse = function() {\n        var t = 0;\n        var i = this.M - 1;\n        while (t < i) {\n            var r = this.getElementByPos(t);\n            this.setElementByPos(t, this.getElementByPos(i));\n            this.setElementByPos(i, r);\n            t += 1;\n            i -= 1;\n        }\n    };\n    Deque.prototype.unique = function() {\n        if (this.M <= 1) {\n            return this.M;\n        }\n        var t = 1;\n        var i = this.getElementByPos(0);\n        for (var r = 1; r < this.M; ++r) {\n            var e = this.getElementByPos(r);\n            if (e !== i) {\n                i = e;\n                this.setElementByPos(t++, e);\n            }\n        }\n        while (this.M > t) this.popBack();\n        return this.M;\n    };\n    Deque.prototype.sort = function(t) {\n        var i = [];\n        for (var r = 0; r < this.M; ++r) {\n            i.push(this.getElementByPos(r));\n        }\n        i.sort(t);\n        for (var r = 0; r < this.M; ++r) this.setElementByPos(r, i[r]);\n    };\n    Deque.prototype.shrinkToFit = function() {\n        if (this.M === 0) return;\n        var t = [];\n        this.forEach((function(i) {\n            t.push(i);\n        }));\n        this.C = Math.max(Math.ceil(this.M / this.B), 1);\n        this.M = this.A = this.R = this.S = this.k = 0;\n        this.j = [];\n        for (var i = 0; i < this.C; ++i) {\n            this.j.push(new Array(this.B));\n        }\n        for (var i = 0; i < t.length; ++i) this.pushBack(t[i]);\n    };\n    Deque.prototype.forEach = function(t) {\n        for (var i = 0; i < this.M; ++i) {\n            t(this.getElementByPos(i), i, this);\n        }\n    };\n    Deque.prototype[Symbol.iterator] = function() {\n        return function() {\n            var t;\n            return __generator(this, (function(i) {\n                switch (i.label) {\n                  case 0:\n                    t = 0;\n                    i.label = 1;\n\n                  case 1:\n                    if (!(t < this.M)) return [ 3, 4 ];\n                    return [ 4, this.getElementByPos(t) ];\n\n                  case 2:\n                    i.sent();\n                    i.label = 3;\n\n                  case 3:\n                    ++t;\n                    return [ 3, 1 ];\n\n                  case 4:\n                    return [ 2 ];\n                }\n            }));\n        }.bind(this)();\n    };\n    return Deque;\n}(_Base__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Deque);\n//# sourceMappingURL=Deque.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Deque.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/LinkList.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/SequentialContainer/LinkList.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Base__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Base */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Base/index.js\");\n/* harmony import */ var _ContainerBase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ContainerBase */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/ContainerBase/index.js\");\n/* harmony import */ var _utils_throwError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/throwError */ \"(ssr)/./node_modules/js-sdsl/dist/esm/utils/throwError.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(t, i) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(t, i) {\n            t.__proto__ = i;\n        } || function(t, i) {\n            for (var r in i) if (Object.prototype.hasOwnProperty.call(i, r)) t[r] = i[r];\n        };\n        return extendStatics(t, i);\n    };\n    return function(t, i) {\n        if (typeof i !== \"function\" && i !== null) throw new TypeError(\"Class extends value \" + String(i) + \" is not a constructor or null\");\n        extendStatics(t, i);\n        function __() {\n            this.constructor = t;\n        }\n        t.prototype = i === null ? Object.create(i) : (__.prototype = i.prototype, new __);\n    };\n}();\n\nvar __generator = undefined && undefined.i || function(t, i) {\n    var r = {\n        label: 0,\n        sent: function() {\n            if (e[0] & 1) throw e[1];\n            return e[1];\n        },\n        trys: [],\n        ops: []\n    }, n, s, e, h;\n    return h = {\n        next: verb(0),\n        throw: verb(1),\n        return: verb(2)\n    }, typeof Symbol === \"function\" && (h[Symbol.iterator] = function() {\n        return this;\n    }), h;\n    function verb(t) {\n        return function(i) {\n            return step([ t, i ]);\n        };\n    }\n    function step(h) {\n        if (n) throw new TypeError(\"Generator is already executing.\");\n        while (r) try {\n            if (n = 1, s && (e = h[0] & 2 ? s[\"return\"] : h[0] ? s[\"throw\"] || ((e = s[\"return\"]) && e.call(s), \n            0) : s.next) && !(e = e.call(s, h[1])).done) return e;\n            if (s = 0, e) h = [ h[0] & 2, e.value ];\n            switch (h[0]) {\n              case 0:\n              case 1:\n                e = h;\n                break;\n\n              case 4:\n                r.label++;\n                return {\n                    value: h[1],\n                    done: false\n                };\n\n              case 5:\n                r.label++;\n                s = h[1];\n                h = [ 0 ];\n                continue;\n\n              case 7:\n                h = r.ops.pop();\n                r.trys.pop();\n                continue;\n\n              default:\n                if (!(e = r.trys, e = e.length > 0 && e[e.length - 1]) && (h[0] === 6 || h[0] === 2)) {\n                    r = 0;\n                    continue;\n                }\n                if (h[0] === 3 && (!e || h[1] > e[0] && h[1] < e[3])) {\n                    r.label = h[1];\n                    break;\n                }\n                if (h[0] === 6 && r.label < e[1]) {\n                    r.label = e[1];\n                    e = h;\n                    break;\n                }\n                if (e && r.label < e[2]) {\n                    r.label = e[2];\n                    r.ops.push(h);\n                    break;\n                }\n                if (e[2]) r.ops.pop();\n                r.trys.pop();\n                continue;\n            }\n            h = i.call(t, r);\n        } catch (t) {\n            h = [ 6, t ];\n            s = 0;\n        } finally {\n            n = e = 0;\n        }\n        if (h[0] & 5) throw h[1];\n        return {\n            value: h[0] ? h[1] : void 0,\n            done: true\n        };\n    }\n};\n\n\n\n\n\n\n\nvar LinkListIterator = function(t) {\n    __extends(LinkListIterator, t);\n    function LinkListIterator(i, r, n, s) {\n        var e = t.call(this, s) || this;\n        e.o = i;\n        e.h = r;\n        e.container = n;\n        if (e.iteratorType === 0) {\n            e.pre = function() {\n                if (this.o.L === this.h) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o = this.o.L;\n                return this;\n            };\n            e.next = function() {\n                if (this.o === this.h) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o = this.o.m;\n                return this;\n            };\n        } else {\n            e.pre = function() {\n                if (this.o.m === this.h) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o = this.o.m;\n                return this;\n            };\n            e.next = function() {\n                if (this.o === this.h) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o = this.o.L;\n                return this;\n            };\n        }\n        return e;\n    }\n    Object.defineProperty(LinkListIterator.prototype, \"pointer\", {\n        get: function() {\n            if (this.o === this.h) {\n                (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n            }\n            return this.o.p;\n        },\n        set: function(t) {\n            if (this.o === this.h) {\n                (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n            }\n            this.o.p = t;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    LinkListIterator.prototype.copy = function() {\n        return new LinkListIterator(this.o, this.h, this.container, this.iteratorType);\n    };\n    return LinkListIterator;\n}(_ContainerBase__WEBPACK_IMPORTED_MODULE_1__.ContainerIterator);\n\nvar LinkList = function(t) {\n    __extends(LinkList, t);\n    function LinkList(i) {\n        if (i === void 0) {\n            i = [];\n        }\n        var r = t.call(this) || this;\n        r.h = {};\n        r.H = r.l = r.h.L = r.h.m = r.h;\n        var n = r;\n        i.forEach((function(t) {\n            n.pushBack(t);\n        }));\n        return r;\n    }\n    LinkList.prototype.G = function(t) {\n        var i = t.L, r = t.m;\n        i.m = r;\n        r.L = i;\n        if (t === this.H) {\n            this.H = r;\n        }\n        if (t === this.l) {\n            this.l = i;\n        }\n        this.M -= 1;\n    };\n    LinkList.prototype.F = function(t, i) {\n        var r = i.m;\n        var n = {\n            p: t,\n            L: i,\n            m: r\n        };\n        i.m = n;\n        r.L = n;\n        if (i === this.h) {\n            this.H = n;\n        }\n        if (r === this.h) {\n            this.l = n;\n        }\n        this.M += 1;\n    };\n    LinkList.prototype.clear = function() {\n        this.M = 0;\n        this.H = this.l = this.h.L = this.h.m = this.h;\n    };\n    LinkList.prototype.begin = function() {\n        return new LinkListIterator(this.H, this.h, this);\n    };\n    LinkList.prototype.end = function() {\n        return new LinkListIterator(this.h, this.h, this);\n    };\n    LinkList.prototype.rBegin = function() {\n        return new LinkListIterator(this.l, this.h, this, 1);\n    };\n    LinkList.prototype.rEnd = function() {\n        return new LinkListIterator(this.h, this.h, this, 1);\n    };\n    LinkList.prototype.front = function() {\n        return this.H.p;\n    };\n    LinkList.prototype.back = function() {\n        return this.l.p;\n    };\n    LinkList.prototype.getElementByPos = function(t) {\n        if (t < 0 || t > this.M - 1) {\n            throw new RangeError;\n        }\n        var i = this.H;\n        while (t--) {\n            i = i.m;\n        }\n        return i.p;\n    };\n    LinkList.prototype.eraseElementByPos = function(t) {\n        if (t < 0 || t > this.M - 1) {\n            throw new RangeError;\n        }\n        var i = this.H;\n        while (t--) {\n            i = i.m;\n        }\n        this.G(i);\n        return this.M;\n    };\n    LinkList.prototype.eraseElementByValue = function(t) {\n        var i = this.H;\n        while (i !== this.h) {\n            if (i.p === t) {\n                this.G(i);\n            }\n            i = i.m;\n        }\n        return this.M;\n    };\n    LinkList.prototype.eraseElementByIterator = function(t) {\n        var i = t.o;\n        if (i === this.h) {\n            (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n        }\n        t = t.next();\n        this.G(i);\n        return t;\n    };\n    LinkList.prototype.pushBack = function(t) {\n        this.F(t, this.l);\n        return this.M;\n    };\n    LinkList.prototype.popBack = function() {\n        if (this.M === 0) return;\n        var t = this.l.p;\n        this.G(this.l);\n        return t;\n    };\n    LinkList.prototype.pushFront = function(t) {\n        this.F(t, this.h);\n        return this.M;\n    };\n    LinkList.prototype.popFront = function() {\n        if (this.M === 0) return;\n        var t = this.H.p;\n        this.G(this.H);\n        return t;\n    };\n    LinkList.prototype.setElementByPos = function(t, i) {\n        if (t < 0 || t > this.M - 1) {\n            throw new RangeError;\n        }\n        var r = this.H;\n        while (t--) {\n            r = r.m;\n        }\n        r.p = i;\n    };\n    LinkList.prototype.insert = function(t, i, r) {\n        if (r === void 0) {\n            r = 1;\n        }\n        if (t < 0 || t > this.M) {\n            throw new RangeError;\n        }\n        if (r <= 0) return this.M;\n        if (t === 0) {\n            while (r--) this.pushFront(i);\n        } else if (t === this.M) {\n            while (r--) this.pushBack(i);\n        } else {\n            var n = this.H;\n            for (var s = 1; s < t; ++s) {\n                n = n.m;\n            }\n            var e = n.m;\n            this.M += r;\n            while (r--) {\n                n.m = {\n                    p: i,\n                    L: n\n                };\n                n.m.L = n;\n                n = n.m;\n            }\n            n.m = e;\n            e.L = n;\n        }\n        return this.M;\n    };\n    LinkList.prototype.find = function(t) {\n        var i = this.H;\n        while (i !== this.h) {\n            if (i.p === t) {\n                return new LinkListIterator(i, this.h, this);\n            }\n            i = i.m;\n        }\n        return this.end();\n    };\n    LinkList.prototype.reverse = function() {\n        if (this.M <= 1) return;\n        var t = this.H;\n        var i = this.l;\n        var r = 0;\n        while (r << 1 < this.M) {\n            var n = t.p;\n            t.p = i.p;\n            i.p = n;\n            t = t.m;\n            i = i.L;\n            r += 1;\n        }\n    };\n    LinkList.prototype.unique = function() {\n        if (this.M <= 1) {\n            return this.M;\n        }\n        var t = this.H;\n        while (t !== this.h) {\n            var i = t;\n            while (i.m !== this.h && i.p === i.m.p) {\n                i = i.m;\n                this.M -= 1;\n            }\n            t.m = i.m;\n            t.m.L = t;\n            t = t.m;\n        }\n        return this.M;\n    };\n    LinkList.prototype.sort = function(t) {\n        if (this.M <= 1) return;\n        var i = [];\n        this.forEach((function(t) {\n            i.push(t);\n        }));\n        i.sort(t);\n        var r = this.H;\n        i.forEach((function(t) {\n            r.p = t;\n            r = r.m;\n        }));\n    };\n    LinkList.prototype.merge = function(t) {\n        var i = this;\n        if (this.M === 0) {\n            t.forEach((function(t) {\n                i.pushBack(t);\n            }));\n        } else {\n            var r = this.H;\n            t.forEach((function(t) {\n                while (r !== i.h && r.p <= t) {\n                    r = r.m;\n                }\n                i.F(t, r.L);\n            }));\n        }\n        return this.M;\n    };\n    LinkList.prototype.forEach = function(t) {\n        var i = this.H;\n        var r = 0;\n        while (i !== this.h) {\n            t(i.p, r++, this);\n            i = i.m;\n        }\n    };\n    LinkList.prototype[Symbol.iterator] = function() {\n        return function() {\n            var t;\n            return __generator(this, (function(i) {\n                switch (i.label) {\n                  case 0:\n                    if (this.M === 0) return [ 2 ];\n                    t = this.H;\n                    i.label = 1;\n\n                  case 1:\n                    if (!(t !== this.h)) return [ 3, 3 ];\n                    return [ 4, t.p ];\n\n                  case 2:\n                    i.sent();\n                    t = t.m;\n                    return [ 3, 1 ];\n\n                  case 3:\n                    return [ 2 ];\n                }\n            }));\n        }.bind(this)();\n    };\n    return LinkList;\n}(_Base__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LinkList);\n//# sourceMappingURL=LinkList.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/LinkList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Vector.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Vector.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Base */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Base/index.js\");\n/* harmony import */ var _Base_RandomIterator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Base/RandomIterator */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Base/RandomIterator.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(t, r) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(t, r) {\n            t.__proto__ = r;\n        } || function(t, r) {\n            for (var e in r) if (Object.prototype.hasOwnProperty.call(r, e)) t[e] = r[e];\n        };\n        return extendStatics(t, r);\n    };\n    return function(t, r) {\n        if (typeof r !== \"function\" && r !== null) throw new TypeError(\"Class extends value \" + String(r) + \" is not a constructor or null\");\n        extendStatics(t, r);\n        function __() {\n            this.constructor = t;\n        }\n        t.prototype = r === null ? Object.create(r) : (__.prototype = r.prototype, new __);\n    };\n}();\n\nvar __generator = undefined && undefined.i || function(t, r) {\n    var e = {\n        label: 0,\n        sent: function() {\n            if (o[0] & 1) throw o[1];\n            return o[1];\n        },\n        trys: [],\n        ops: []\n    }, n, i, o, u;\n    return u = {\n        next: verb(0),\n        throw: verb(1),\n        return: verb(2)\n    }, typeof Symbol === \"function\" && (u[Symbol.iterator] = function() {\n        return this;\n    }), u;\n    function verb(t) {\n        return function(r) {\n            return step([ t, r ]);\n        };\n    }\n    function step(u) {\n        if (n) throw new TypeError(\"Generator is already executing.\");\n        while (e) try {\n            if (n = 1, i && (o = u[0] & 2 ? i[\"return\"] : u[0] ? i[\"throw\"] || ((o = i[\"return\"]) && o.call(i), \n            0) : i.next) && !(o = o.call(i, u[1])).done) return o;\n            if (i = 0, o) u = [ u[0] & 2, o.value ];\n            switch (u[0]) {\n              case 0:\n              case 1:\n                o = u;\n                break;\n\n              case 4:\n                e.label++;\n                return {\n                    value: u[1],\n                    done: false\n                };\n\n              case 5:\n                e.label++;\n                i = u[1];\n                u = [ 0 ];\n                continue;\n\n              case 7:\n                u = e.ops.pop();\n                e.trys.pop();\n                continue;\n\n              default:\n                if (!(o = e.trys, o = o.length > 0 && o[o.length - 1]) && (u[0] === 6 || u[0] === 2)) {\n                    e = 0;\n                    continue;\n                }\n                if (u[0] === 3 && (!o || u[1] > o[0] && u[1] < o[3])) {\n                    e.label = u[1];\n                    break;\n                }\n                if (u[0] === 6 && e.label < o[1]) {\n                    e.label = o[1];\n                    o = u;\n                    break;\n                }\n                if (o && e.label < o[2]) {\n                    e.label = o[2];\n                    e.ops.push(u);\n                    break;\n                }\n                if (o[2]) e.ops.pop();\n                e.trys.pop();\n                continue;\n            }\n            u = r.call(t, e);\n        } catch (t) {\n            u = [ 6, t ];\n            i = 0;\n        } finally {\n            n = o = 0;\n        }\n        if (u[0] & 5) throw u[1];\n        return {\n            value: u[0] ? u[1] : void 0,\n            done: true\n        };\n    }\n};\n\nvar __read = undefined && undefined.q || function(t, r) {\n    var e = typeof Symbol === \"function\" && t[Symbol.iterator];\n    if (!e) return t;\n    var n = e.call(t), i, o = [], u;\n    try {\n        while ((r === void 0 || r-- > 0) && !(i = n.next()).done) o.push(i.value);\n    } catch (t) {\n        u = {\n            error: t\n        };\n    } finally {\n        try {\n            if (i && !i.done && (e = n[\"return\"])) e.call(n);\n        } finally {\n            if (u) throw u.error;\n        }\n    }\n    return o;\n};\n\nvar __spreadArray = undefined && undefined.D || function(t, r, e) {\n    if (e || arguments.length === 2) for (var n = 0, i = r.length, o; n < i; n++) {\n        if (o || !(n in r)) {\n            if (!o) o = Array.prototype.slice.call(r, 0, n);\n            o[n] = r[n];\n        }\n    }\n    return t.concat(o || Array.prototype.slice.call(r));\n};\n\nvar __values = undefined && undefined.V || function(t) {\n    var r = typeof Symbol === \"function\" && Symbol.iterator, e = r && t[r], n = 0;\n    if (e) return e.call(t);\n    if (t && typeof t.length === \"number\") return {\n        next: function() {\n            if (t && n >= t.length) t = void 0;\n            return {\n                value: t && t[n++],\n                done: !t\n            };\n        }\n    };\n    throw new TypeError(r ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\n\n\n\n\nvar VectorIterator = function(t) {\n    __extends(VectorIterator, t);\n    function VectorIterator(r, e, n) {\n        var i = t.call(this, r, n) || this;\n        i.container = e;\n        return i;\n    }\n    VectorIterator.prototype.copy = function() {\n        return new VectorIterator(this.o, this.container, this.iteratorType);\n    };\n    return VectorIterator;\n}(_Base_RandomIterator__WEBPACK_IMPORTED_MODULE_0__.RandomIterator);\n\nvar Vector = function(t) {\n    __extends(Vector, t);\n    function Vector(r, e) {\n        if (r === void 0) {\n            r = [];\n        }\n        if (e === void 0) {\n            e = true;\n        }\n        var n = t.call(this) || this;\n        if (Array.isArray(r)) {\n            n.J = e ? __spreadArray([], __read(r), false) : r;\n            n.M = r.length;\n        } else {\n            n.J = [];\n            var i = n;\n            r.forEach((function(t) {\n                i.pushBack(t);\n            }));\n        }\n        return n;\n    }\n    Vector.prototype.clear = function() {\n        this.M = 0;\n        this.J.length = 0;\n    };\n    Vector.prototype.begin = function() {\n        return new VectorIterator(0, this);\n    };\n    Vector.prototype.end = function() {\n        return new VectorIterator(this.M, this);\n    };\n    Vector.prototype.rBegin = function() {\n        return new VectorIterator(this.M - 1, this, 1);\n    };\n    Vector.prototype.rEnd = function() {\n        return new VectorIterator(-1, this, 1);\n    };\n    Vector.prototype.front = function() {\n        return this.J[0];\n    };\n    Vector.prototype.back = function() {\n        return this.J[this.M - 1];\n    };\n    Vector.prototype.getElementByPos = function(t) {\n        if (t < 0 || t > this.M - 1) {\n            throw new RangeError;\n        }\n        return this.J[t];\n    };\n    Vector.prototype.eraseElementByPos = function(t) {\n        if (t < 0 || t > this.M - 1) {\n            throw new RangeError;\n        }\n        this.J.splice(t, 1);\n        this.M -= 1;\n        return this.M;\n    };\n    Vector.prototype.eraseElementByValue = function(t) {\n        var r = 0;\n        for (var e = 0; e < this.M; ++e) {\n            if (this.J[e] !== t) {\n                this.J[r++] = this.J[e];\n            }\n        }\n        this.M = this.J.length = r;\n        return this.M;\n    };\n    Vector.prototype.eraseElementByIterator = function(t) {\n        var r = t.o;\n        t = t.next();\n        this.eraseElementByPos(r);\n        return t;\n    };\n    Vector.prototype.pushBack = function(t) {\n        this.J.push(t);\n        this.M += 1;\n        return this.M;\n    };\n    Vector.prototype.popBack = function() {\n        if (this.M === 0) return;\n        this.M -= 1;\n        return this.J.pop();\n    };\n    Vector.prototype.setElementByPos = function(t, r) {\n        if (t < 0 || t > this.M - 1) {\n            throw new RangeError;\n        }\n        this.J[t] = r;\n    };\n    Vector.prototype.insert = function(t, r, e) {\n        var n;\n        if (e === void 0) {\n            e = 1;\n        }\n        if (t < 0 || t > this.M) {\n            throw new RangeError;\n        }\n        (n = this.J).splice.apply(n, __spreadArray([ t, 0 ], __read(new Array(e).fill(r)), false));\n        this.M += e;\n        return this.M;\n    };\n    Vector.prototype.find = function(t) {\n        for (var r = 0; r < this.M; ++r) {\n            if (this.J[r] === t) {\n                return new VectorIterator(r, this);\n            }\n        }\n        return this.end();\n    };\n    Vector.prototype.reverse = function() {\n        this.J.reverse();\n    };\n    Vector.prototype.unique = function() {\n        var t = 1;\n        for (var r = 1; r < this.M; ++r) {\n            if (this.J[r] !== this.J[r - 1]) {\n                this.J[t++] = this.J[r];\n            }\n        }\n        this.M = this.J.length = t;\n        return this.M;\n    };\n    Vector.prototype.sort = function(t) {\n        this.J.sort(t);\n    };\n    Vector.prototype.forEach = function(t) {\n        for (var r = 0; r < this.M; ++r) {\n            t(this.J[r], r, this);\n        }\n    };\n    Vector.prototype[Symbol.iterator] = function() {\n        return function() {\n            return __generator(this, (function(t) {\n                switch (t.label) {\n                  case 0:\n                    return [ 5, __values(this.J) ];\n\n                  case 1:\n                    t.sent();\n                    return [ 2 ];\n                }\n            }));\n        }.bind(this)();\n    };\n    return Vector;\n}(_Base__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Vector);\n//# sourceMappingURL=Vector.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Vector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/TreeIterator.js":
/*!************************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/TreeIterator.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ContainerBase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../ContainerBase */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/ContainerBase/index.js\");\n/* harmony import */ var _utils_throwError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/throwError */ \"(ssr)/./node_modules/js-sdsl/dist/esm/utils/throwError.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(r, t) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(r, t) {\n            r.__proto__ = t;\n        } || function(r, t) {\n            for (var e in t) if (Object.prototype.hasOwnProperty.call(t, e)) r[e] = t[e];\n        };\n        return extendStatics(r, t);\n    };\n    return function(r, t) {\n        if (typeof t !== \"function\" && t !== null) throw new TypeError(\"Class extends value \" + String(t) + \" is not a constructor or null\");\n        extendStatics(r, t);\n        function __() {\n            this.constructor = r;\n        }\n        r.prototype = t === null ? Object.create(t) : (__.prototype = t.prototype, new __);\n    };\n}();\n\n\n\n\n\nvar TreeIterator = function(r) {\n    __extends(TreeIterator, r);\n    function TreeIterator(t, e, i) {\n        var n = r.call(this, i) || this;\n        n.o = t;\n        n.h = e;\n        if (n.iteratorType === 0) {\n            n.pre = function() {\n                if (this.o === this.h.K) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o = this.o.L();\n                return this;\n            };\n            n.next = function() {\n                if (this.o === this.h) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o = this.o.m();\n                return this;\n            };\n        } else {\n            n.pre = function() {\n                if (this.o === this.h.N) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o = this.o.m();\n                return this;\n            };\n            n.next = function() {\n                if (this.o === this.h) {\n                    (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n                }\n                this.o = this.o.L();\n                return this;\n            };\n        }\n        return n;\n    }\n    Object.defineProperty(TreeIterator.prototype, \"index\", {\n        get: function() {\n            var r = this.o;\n            var t = this.h.rr;\n            if (r === this.h) {\n                if (t) {\n                    return t.tr - 1;\n                }\n                return 0;\n            }\n            var e = 0;\n            if (r.K) {\n                e += r.K.tr;\n            }\n            while (r !== t) {\n                var i = r.rr;\n                if (r === i.N) {\n                    e += 1;\n                    if (i.K) {\n                        e += i.K.tr;\n                    }\n                }\n                r = i;\n            }\n            return e;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return TreeIterator;\n}(_ContainerBase__WEBPACK_IMPORTED_MODULE_1__.ContainerIterator);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TreeIterator);\n//# sourceMappingURL=TreeIterator.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/TreeIterator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/TreeNode.js":
/*!********************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/TreeNode.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeNode: () => (/* binding */ TreeNode),\n/* harmony export */   TreeNodeEnableIndex: () => (/* binding */ TreeNodeEnableIndex)\n/* harmony export */ });\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(e, n) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(e, n) {\n            e.__proto__ = n;\n        } || function(e, n) {\n            for (var t in n) if (Object.prototype.hasOwnProperty.call(n, t)) e[t] = n[t];\n        };\n        return extendStatics(e, n);\n    };\n    return function(e, n) {\n        if (typeof n !== \"function\" && n !== null) throw new TypeError(\"Class extends value \" + String(n) + \" is not a constructor or null\");\n        extendStatics(e, n);\n        function __() {\n            this.constructor = e;\n        }\n        e.prototype = n === null ? Object.create(n) : (__.prototype = n.prototype, new __);\n    };\n}();\n\nvar TreeNode = function() {\n    function TreeNode(e, n) {\n        this.ee = 1;\n        this.u = undefined;\n        this.p = undefined;\n        this.K = undefined;\n        this.N = undefined;\n        this.rr = undefined;\n        this.u = e;\n        this.p = n;\n    }\n    TreeNode.prototype.L = function() {\n        var e = this;\n        if (e.ee === 1 && e.rr.rr === e) {\n            e = e.N;\n        } else if (e.K) {\n            e = e.K;\n            while (e.N) {\n                e = e.N;\n            }\n        } else {\n            var n = e.rr;\n            while (n.K === e) {\n                e = n;\n                n = e.rr;\n            }\n            e = n;\n        }\n        return e;\n    };\n    TreeNode.prototype.m = function() {\n        var e = this;\n        if (e.N) {\n            e = e.N;\n            while (e.K) {\n                e = e.K;\n            }\n            return e;\n        } else {\n            var n = e.rr;\n            while (n.N === e) {\n                e = n;\n                n = e.rr;\n            }\n            if (e.N !== n) {\n                return n;\n            } else return e;\n        }\n    };\n    TreeNode.prototype.ne = function() {\n        var e = this.rr;\n        var n = this.N;\n        var t = n.K;\n        if (e.rr === this) e.rr = n; else if (e.K === this) e.K = n; else e.N = n;\n        n.rr = e;\n        n.K = this;\n        this.rr = n;\n        this.N = t;\n        if (t) t.rr = this;\n        return n;\n    };\n    TreeNode.prototype.te = function() {\n        var e = this.rr;\n        var n = this.K;\n        var t = n.N;\n        if (e.rr === this) e.rr = n; else if (e.K === this) e.K = n; else e.N = n;\n        n.rr = e;\n        n.N = this;\n        this.rr = n;\n        this.K = t;\n        if (t) t.rr = this;\n        return n;\n    };\n    return TreeNode;\n}();\n\n\n\nvar TreeNodeEnableIndex = function(e) {\n    __extends(TreeNodeEnableIndex, e);\n    function TreeNodeEnableIndex() {\n        var n = e !== null && e.apply(this, arguments) || this;\n        n.tr = 1;\n        return n;\n    }\n    TreeNodeEnableIndex.prototype.ne = function() {\n        var n = e.prototype.ne.call(this);\n        this.ie();\n        n.ie();\n        return n;\n    };\n    TreeNodeEnableIndex.prototype.te = function() {\n        var n = e.prototype.te.call(this);\n        this.ie();\n        n.ie();\n        return n;\n    };\n    TreeNodeEnableIndex.prototype.ie = function() {\n        this.tr = 1;\n        if (this.K) {\n            this.tr += this.K.tr;\n        }\n        if (this.N) {\n            this.tr += this.N.tr;\n        }\n    };\n    return TreeNodeEnableIndex;\n}(TreeNode);\n\n\n//# sourceMappingURL=TreeNode.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/TreeNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/index.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/index.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _TreeNode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TreeNode */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/TreeNode.js\");\n/* harmony import */ var _ContainerBase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../ContainerBase */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/ContainerBase/index.js\");\n/* harmony import */ var _utils_throwError__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/throwError */ \"(ssr)/./node_modules/js-sdsl/dist/esm/utils/throwError.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(e, r) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(e, r) {\n            e.__proto__ = r;\n        } || function(e, r) {\n            for (var i in r) if (Object.prototype.hasOwnProperty.call(r, i)) e[i] = r[i];\n        };\n        return extendStatics(e, r);\n    };\n    return function(e, r) {\n        if (typeof r !== \"function\" && r !== null) throw new TypeError(\"Class extends value \" + String(r) + \" is not a constructor or null\");\n        extendStatics(e, r);\n        function __() {\n            this.constructor = e;\n        }\n        e.prototype = r === null ? Object.create(r) : (__.prototype = r.prototype, new __);\n    };\n}();\n\nvar __read = undefined && undefined.q || function(e, r) {\n    var i = typeof Symbol === \"function\" && e[Symbol.iterator];\n    if (!i) return e;\n    var t = i.call(e), n, s = [], f;\n    try {\n        while ((r === void 0 || r-- > 0) && !(n = t.next()).done) s.push(n.value);\n    } catch (e) {\n        f = {\n            error: e\n        };\n    } finally {\n        try {\n            if (n && !n.done && (i = t[\"return\"])) i.call(t);\n        } finally {\n            if (f) throw f.error;\n        }\n    }\n    return s;\n};\n\nvar __values = undefined && undefined.V || function(e) {\n    var r = typeof Symbol === \"function\" && Symbol.iterator, i = r && e[r], t = 0;\n    if (i) return i.call(e);\n    if (e && typeof e.length === \"number\") return {\n        next: function() {\n            if (e && t >= e.length) e = void 0;\n            return {\n                value: e && e[t++],\n                done: !e\n            };\n        }\n    };\n    throw new TypeError(r ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\n\n\n\n\n\n\nvar TreeContainer = function(e) {\n    __extends(TreeContainer, e);\n    function TreeContainer(r, i) {\n        if (r === void 0) {\n            r = function(e, r) {\n                if (e < r) return -1;\n                if (e > r) return 1;\n                return 0;\n            };\n        }\n        if (i === void 0) {\n            i = false;\n        }\n        var t = e.call(this) || this;\n        t.W = undefined;\n        t.$ = r;\n        if (i) {\n            t.re = _TreeNode__WEBPACK_IMPORTED_MODULE_0__.TreeNodeEnableIndex;\n            t.v = function(e, r, i) {\n                var t = this.se(e, r, i);\n                if (t) {\n                    var n = t.rr;\n                    while (n !== this.h) {\n                        n.tr += 1;\n                        n = n.rr;\n                    }\n                    var s = this.fe(t);\n                    if (s) {\n                        var f = s, h = f.parentNode, u = f.grandParent, a = f.curNode;\n                        h.ie();\n                        u.ie();\n                        a.ie();\n                    }\n                }\n                return this.M;\n            };\n            t.G = function(e) {\n                var r = this.he(e);\n                while (r !== this.h) {\n                    r.tr -= 1;\n                    r = r.rr;\n                }\n            };\n        } else {\n            t.re = _TreeNode__WEBPACK_IMPORTED_MODULE_0__.TreeNode;\n            t.v = function(e, r, i) {\n                var t = this.se(e, r, i);\n                if (t) this.fe(t);\n                return this.M;\n            };\n            t.G = t.he;\n        }\n        t.h = new t.re;\n        return t;\n    }\n    TreeContainer.prototype.U = function(e, r) {\n        var i = this.h;\n        while (e) {\n            var t = this.$(e.u, r);\n            if (t < 0) {\n                e = e.N;\n            } else if (t > 0) {\n                i = e;\n                e = e.K;\n            } else return e;\n        }\n        return i;\n    };\n    TreeContainer.prototype.X = function(e, r) {\n        var i = this.h;\n        while (e) {\n            var t = this.$(e.u, r);\n            if (t <= 0) {\n                e = e.N;\n            } else {\n                i = e;\n                e = e.K;\n            }\n        }\n        return i;\n    };\n    TreeContainer.prototype.Y = function(e, r) {\n        var i = this.h;\n        while (e) {\n            var t = this.$(e.u, r);\n            if (t < 0) {\n                i = e;\n                e = e.N;\n            } else if (t > 0) {\n                e = e.K;\n            } else return e;\n        }\n        return i;\n    };\n    TreeContainer.prototype.Z = function(e, r) {\n        var i = this.h;\n        while (e) {\n            var t = this.$(e.u, r);\n            if (t < 0) {\n                i = e;\n                e = e.N;\n            } else {\n                e = e.K;\n            }\n        }\n        return i;\n    };\n    TreeContainer.prototype.ue = function(e) {\n        while (true) {\n            var r = e.rr;\n            if (r === this.h) return;\n            if (e.ee === 1) {\n                e.ee = 0;\n                return;\n            }\n            if (e === r.K) {\n                var i = r.N;\n                if (i.ee === 1) {\n                    i.ee = 0;\n                    r.ee = 1;\n                    if (r === this.W) {\n                        this.W = r.ne();\n                    } else r.ne();\n                } else {\n                    if (i.N && i.N.ee === 1) {\n                        i.ee = r.ee;\n                        r.ee = 0;\n                        i.N.ee = 0;\n                        if (r === this.W) {\n                            this.W = r.ne();\n                        } else r.ne();\n                        return;\n                    } else if (i.K && i.K.ee === 1) {\n                        i.ee = 1;\n                        i.K.ee = 0;\n                        i.te();\n                    } else {\n                        i.ee = 1;\n                        e = r;\n                    }\n                }\n            } else {\n                var i = r.K;\n                if (i.ee === 1) {\n                    i.ee = 0;\n                    r.ee = 1;\n                    if (r === this.W) {\n                        this.W = r.te();\n                    } else r.te();\n                } else {\n                    if (i.K && i.K.ee === 1) {\n                        i.ee = r.ee;\n                        r.ee = 0;\n                        i.K.ee = 0;\n                        if (r === this.W) {\n                            this.W = r.te();\n                        } else r.te();\n                        return;\n                    } else if (i.N && i.N.ee === 1) {\n                        i.ee = 1;\n                        i.N.ee = 0;\n                        i.ne();\n                    } else {\n                        i.ee = 1;\n                        e = r;\n                    }\n                }\n            }\n        }\n    };\n    TreeContainer.prototype.he = function(e) {\n        var r, i;\n        if (this.M === 1) {\n            this.clear();\n            return this.h;\n        }\n        var t = e;\n        while (t.K || t.N) {\n            if (t.N) {\n                t = t.N;\n                while (t.K) t = t.K;\n            } else {\n                t = t.K;\n            }\n            r = __read([ t.u, e.u ], 2), e.u = r[0], t.u = r[1];\n            i = __read([ t.p, e.p ], 2), e.p = i[0], t.p = i[1];\n            e = t;\n        }\n        if (this.h.K === t) {\n            this.h.K = t.rr;\n        } else if (this.h.N === t) {\n            this.h.N = t.rr;\n        }\n        this.ue(t);\n        var n = t.rr;\n        if (t === n.K) {\n            n.K = undefined;\n        } else n.N = undefined;\n        this.M -= 1;\n        this.W.ee = 0;\n        return n;\n    };\n    TreeContainer.prototype.ae = function(e, r) {\n        if (e === undefined) return false;\n        var i = this.ae(e.K, r);\n        if (i) return true;\n        if (r(e)) return true;\n        return this.ae(e.N, r);\n    };\n    TreeContainer.prototype.fe = function(e) {\n        while (true) {\n            var r = e.rr;\n            if (r.ee === 0) return;\n            var i = r.rr;\n            if (r === i.K) {\n                var t = i.N;\n                if (t && t.ee === 1) {\n                    t.ee = r.ee = 0;\n                    if (i === this.W) return;\n                    i.ee = 1;\n                    e = i;\n                    continue;\n                } else if (e === r.N) {\n                    e.ee = 0;\n                    if (e.K) e.K.rr = r;\n                    if (e.N) e.N.rr = i;\n                    r.N = e.K;\n                    i.K = e.N;\n                    e.K = r;\n                    e.N = i;\n                    if (i === this.W) {\n                        this.W = e;\n                        this.h.rr = e;\n                    } else {\n                        var n = i.rr;\n                        if (n.K === i) {\n                            n.K = e;\n                        } else n.N = e;\n                    }\n                    e.rr = i.rr;\n                    r.rr = e;\n                    i.rr = e;\n                    i.ee = 1;\n                    return {\n                        parentNode: r,\n                        grandParent: i,\n                        curNode: e\n                    };\n                } else {\n                    r.ee = 0;\n                    if (i === this.W) {\n                        this.W = i.te();\n                    } else i.te();\n                    i.ee = 1;\n                }\n            } else {\n                var t = i.K;\n                if (t && t.ee === 1) {\n                    t.ee = r.ee = 0;\n                    if (i === this.W) return;\n                    i.ee = 1;\n                    e = i;\n                    continue;\n                } else if (e === r.K) {\n                    e.ee = 0;\n                    if (e.K) e.K.rr = i;\n                    if (e.N) e.N.rr = r;\n                    i.N = e.K;\n                    r.K = e.N;\n                    e.K = i;\n                    e.N = r;\n                    if (i === this.W) {\n                        this.W = e;\n                        this.h.rr = e;\n                    } else {\n                        var n = i.rr;\n                        if (n.K === i) {\n                            n.K = e;\n                        } else n.N = e;\n                    }\n                    e.rr = i.rr;\n                    r.rr = e;\n                    i.rr = e;\n                    i.ee = 1;\n                    return {\n                        parentNode: r,\n                        grandParent: i,\n                        curNode: e\n                    };\n                } else {\n                    r.ee = 0;\n                    if (i === this.W) {\n                        this.W = i.ne();\n                    } else i.ne();\n                    i.ee = 1;\n                }\n            }\n            return;\n        }\n    };\n    TreeContainer.prototype.se = function(e, r, i) {\n        if (this.W === undefined) {\n            this.M += 1;\n            this.W = new this.re(e, r);\n            this.W.ee = 0;\n            this.W.rr = this.h;\n            this.h.rr = this.W;\n            this.h.K = this.W;\n            this.h.N = this.W;\n            return;\n        }\n        var t;\n        var n = this.h.K;\n        var s = this.$(n.u, e);\n        if (s === 0) {\n            n.p = r;\n            return;\n        } else if (s > 0) {\n            n.K = new this.re(e, r);\n            n.K.rr = n;\n            t = n.K;\n            this.h.K = t;\n        } else {\n            var f = this.h.N;\n            var h = this.$(f.u, e);\n            if (h === 0) {\n                f.p = r;\n                return;\n            } else if (h < 0) {\n                f.N = new this.re(e, r);\n                f.N.rr = f;\n                t = f.N;\n                this.h.N = t;\n            } else {\n                if (i !== undefined) {\n                    var u = i.o;\n                    if (u !== this.h) {\n                        var a = this.$(u.u, e);\n                        if (a === 0) {\n                            u.p = r;\n                            return;\n                        } else if (a > 0) {\n                            var o = u.L();\n                            var l = this.$(o.u, e);\n                            if (l === 0) {\n                                o.p = r;\n                                return;\n                            } else if (l < 0) {\n                                t = new this.re(e, r);\n                                if (o.N === undefined) {\n                                    o.N = t;\n                                    t.rr = o;\n                                } else {\n                                    u.K = t;\n                                    t.rr = u;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (t === undefined) {\n                    t = this.W;\n                    while (true) {\n                        var v = this.$(t.u, e);\n                        if (v > 0) {\n                            if (t.K === undefined) {\n                                t.K = new this.re(e, r);\n                                t.K.rr = t;\n                                t = t.K;\n                                break;\n                            }\n                            t = t.K;\n                        } else if (v < 0) {\n                            if (t.N === undefined) {\n                                t.N = new this.re(e, r);\n                                t.N.rr = t;\n                                t = t.N;\n                                break;\n                            }\n                            t = t.N;\n                        } else {\n                            t.p = r;\n                            return;\n                        }\n                    }\n                }\n            }\n        }\n        this.M += 1;\n        return t;\n    };\n    TreeContainer.prototype.g = function(e, r) {\n        while (e) {\n            var i = this.$(e.u, r);\n            if (i < 0) {\n                e = e.N;\n            } else if (i > 0) {\n                e = e.K;\n            } else return e;\n        }\n        return e || this.h;\n    };\n    TreeContainer.prototype.clear = function() {\n        this.M = 0;\n        this.W = undefined;\n        this.h.rr = undefined;\n        this.h.K = this.h.N = undefined;\n    };\n    TreeContainer.prototype.updateKeyByIterator = function(e, r) {\n        var i = e.o;\n        if (i === this.h) {\n            (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_1__.throwIteratorAccessError)();\n        }\n        if (this.M === 1) {\n            i.u = r;\n            return true;\n        }\n        if (i === this.h.K) {\n            if (this.$(i.m().u, r) > 0) {\n                i.u = r;\n                return true;\n            }\n            return false;\n        }\n        if (i === this.h.N) {\n            if (this.$(i.L().u, r) < 0) {\n                i.u = r;\n                return true;\n            }\n            return false;\n        }\n        var t = i.L().u;\n        if (this.$(t, r) >= 0) return false;\n        var n = i.m().u;\n        if (this.$(n, r) <= 0) return false;\n        i.u = r;\n        return true;\n    };\n    TreeContainer.prototype.eraseElementByPos = function(e) {\n        if (e < 0 || e > this.M - 1) {\n            throw new RangeError;\n        }\n        var r = 0;\n        var i = this;\n        this.ae(this.W, (function(t) {\n            if (e === r) {\n                i.G(t);\n                return true;\n            }\n            r += 1;\n            return false;\n        }));\n        return this.M;\n    };\n    TreeContainer.prototype.eraseElementByKey = function(e) {\n        if (this.M === 0) return false;\n        var r = this.g(this.W, e);\n        if (r === this.h) return false;\n        this.G(r);\n        return true;\n    };\n    TreeContainer.prototype.eraseElementByIterator = function(e) {\n        var r = e.o;\n        if (r === this.h) {\n            (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_1__.throwIteratorAccessError)();\n        }\n        var i = r.N === undefined;\n        var t = e.iteratorType === 0;\n        if (t) {\n            if (i) e.next();\n        } else {\n            if (!i || r.K === undefined) e.next();\n        }\n        this.G(r);\n        return e;\n    };\n    TreeContainer.prototype.forEach = function(e) {\n        var r, i;\n        var t = 0;\n        try {\n            for (var n = __values(this), s = n.next(); !s.done; s = n.next()) {\n                var f = s.value;\n                e(f, t++, this);\n            }\n        } catch (e) {\n            r = {\n                error: e\n            };\n        } finally {\n            try {\n                if (s && !s.done && (i = n.return)) i.call(n);\n            } finally {\n                if (r) throw r.error;\n            }\n        }\n    };\n    TreeContainer.prototype.getElementByPos = function(e) {\n        var r, i;\n        if (e < 0 || e > this.M - 1) {\n            throw new RangeError;\n        }\n        var t;\n        var n = 0;\n        try {\n            for (var s = __values(this), f = s.next(); !f.done; f = s.next()) {\n                var h = f.value;\n                if (n === e) {\n                    t = h;\n                    break;\n                }\n                n += 1;\n            }\n        } catch (e) {\n            r = {\n                error: e\n            };\n        } finally {\n            try {\n                if (f && !f.done && (i = s.return)) i.call(s);\n            } finally {\n                if (r) throw r.error;\n            }\n        }\n        return t;\n    };\n    TreeContainer.prototype.getHeight = function() {\n        if (this.M === 0) return 0;\n        var traversal = function(e) {\n            if (!e) return 0;\n            return Math.max(traversal(e.K), traversal(e.N)) + 1;\n        };\n        return traversal(this.W);\n    };\n    return TreeContainer;\n}(_ContainerBase__WEBPACK_IMPORTED_MODULE_2__.Container);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TreeContainer);\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/OrderedMap.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/TreeContainer/OrderedMap.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Base__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Base */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/index.js\");\n/* harmony import */ var _Base_TreeIterator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Base/TreeIterator */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/TreeIterator.js\");\n/* harmony import */ var _utils_throwError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/throwError */ \"(ssr)/./node_modules/js-sdsl/dist/esm/utils/throwError.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(r, e) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(r, e) {\n            r.__proto__ = e;\n        } || function(r, e) {\n            for (var t in e) if (Object.prototype.hasOwnProperty.call(e, t)) r[t] = e[t];\n        };\n        return extendStatics(r, e);\n    };\n    return function(r, e) {\n        if (typeof e !== \"function\" && e !== null) throw new TypeError(\"Class extends value \" + String(e) + \" is not a constructor or null\");\n        extendStatics(r, e);\n        function __() {\n            this.constructor = r;\n        }\n        r.prototype = e === null ? Object.create(e) : (__.prototype = e.prototype, new __);\n    };\n}();\n\nvar __generator = undefined && undefined.i || function(r, e) {\n    var t = {\n        label: 0,\n        sent: function() {\n            if (o[0] & 1) throw o[1];\n            return o[1];\n        },\n        trys: [],\n        ops: []\n    }, n, i, o, a;\n    return a = {\n        next: verb(0),\n        throw: verb(1),\n        return: verb(2)\n    }, typeof Symbol === \"function\" && (a[Symbol.iterator] = function() {\n        return this;\n    }), a;\n    function verb(r) {\n        return function(e) {\n            return step([ r, e ]);\n        };\n    }\n    function step(a) {\n        if (n) throw new TypeError(\"Generator is already executing.\");\n        while (t) try {\n            if (n = 1, i && (o = a[0] & 2 ? i[\"return\"] : a[0] ? i[\"throw\"] || ((o = i[\"return\"]) && o.call(i), \n            0) : i.next) && !(o = o.call(i, a[1])).done) return o;\n            if (i = 0, o) a = [ a[0] & 2, o.value ];\n            switch (a[0]) {\n              case 0:\n              case 1:\n                o = a;\n                break;\n\n              case 4:\n                t.label++;\n                return {\n                    value: a[1],\n                    done: false\n                };\n\n              case 5:\n                t.label++;\n                i = a[1];\n                a = [ 0 ];\n                continue;\n\n              case 7:\n                a = t.ops.pop();\n                t.trys.pop();\n                continue;\n\n              default:\n                if (!(o = t.trys, o = o.length > 0 && o[o.length - 1]) && (a[0] === 6 || a[0] === 2)) {\n                    t = 0;\n                    continue;\n                }\n                if (a[0] === 3 && (!o || a[1] > o[0] && a[1] < o[3])) {\n                    t.label = a[1];\n                    break;\n                }\n                if (a[0] === 6 && t.label < o[1]) {\n                    t.label = o[1];\n                    o = a;\n                    break;\n                }\n                if (o && t.label < o[2]) {\n                    t.label = o[2];\n                    t.ops.push(a);\n                    break;\n                }\n                if (o[2]) t.ops.pop();\n                t.trys.pop();\n                continue;\n            }\n            a = e.call(r, t);\n        } catch (r) {\n            a = [ 6, r ];\n            i = 0;\n        } finally {\n            n = o = 0;\n        }\n        if (a[0] & 5) throw a[1];\n        return {\n            value: a[0] ? a[1] : void 0,\n            done: true\n        };\n    }\n};\n\nvar __values = undefined && undefined.V || function(r) {\n    var e = typeof Symbol === \"function\" && Symbol.iterator, t = e && r[e], n = 0;\n    if (t) return t.call(r);\n    if (r && typeof r.length === \"number\") return {\n        next: function() {\n            if (r && n >= r.length) r = void 0;\n            return {\n                value: r && r[n++],\n                done: !r\n            };\n        }\n    };\n    throw new TypeError(e ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\n\n\n\n\n\n\nvar OrderedMapIterator = function(r) {\n    __extends(OrderedMapIterator, r);\n    function OrderedMapIterator(e, t, n, i) {\n        var o = r.call(this, e, t, i) || this;\n        o.container = n;\n        return o;\n    }\n    Object.defineProperty(OrderedMapIterator.prototype, \"pointer\", {\n        get: function() {\n            if (this.o === this.h) {\n                (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n            }\n            var r = this;\n            return new Proxy([], {\n                get: function(e, t) {\n                    if (t === \"0\") return r.o.u; else if (t === \"1\") return r.o.p;\n                },\n                set: function(e, t, n) {\n                    if (t !== \"1\") {\n                        throw new TypeError(\"props must be 1\");\n                    }\n                    r.o.p = n;\n                    return true;\n                }\n            });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OrderedMapIterator.prototype.copy = function() {\n        return new OrderedMapIterator(this.o, this.h, this.container, this.iteratorType);\n    };\n    return OrderedMapIterator;\n}(_Base_TreeIterator__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n\nvar OrderedMap = function(r) {\n    __extends(OrderedMap, r);\n    function OrderedMap(e, t, n) {\n        if (e === void 0) {\n            e = [];\n        }\n        var i = r.call(this, t, n) || this;\n        var o = i;\n        e.forEach((function(r) {\n            o.setElement(r[0], r[1]);\n        }));\n        return i;\n    }\n    OrderedMap.prototype.P = function(r) {\n        return __generator(this, (function(e) {\n            switch (e.label) {\n              case 0:\n                if (r === undefined) return [ 2 ];\n                return [ 5, __values(this.P(r.K)) ];\n\n              case 1:\n                e.sent();\n                return [ 4, [ r.u, r.p ] ];\n\n              case 2:\n                e.sent();\n                return [ 5, __values(this.P(r.N)) ];\n\n              case 3:\n                e.sent();\n                return [ 2 ];\n            }\n        }));\n    };\n    OrderedMap.prototype.begin = function() {\n        return new OrderedMapIterator(this.h.K || this.h, this.h, this);\n    };\n    OrderedMap.prototype.end = function() {\n        return new OrderedMapIterator(this.h, this.h, this);\n    };\n    OrderedMap.prototype.rBegin = function() {\n        return new OrderedMapIterator(this.h.N || this.h, this.h, this, 1);\n    };\n    OrderedMap.prototype.rEnd = function() {\n        return new OrderedMapIterator(this.h, this.h, this, 1);\n    };\n    OrderedMap.prototype.front = function() {\n        if (this.M === 0) return;\n        var r = this.h.K;\n        return [ r.u, r.p ];\n    };\n    OrderedMap.prototype.back = function() {\n        if (this.M === 0) return;\n        var r = this.h.N;\n        return [ r.u, r.p ];\n    };\n    OrderedMap.prototype.lowerBound = function(r) {\n        var e = this.U(this.W, r);\n        return new OrderedMapIterator(e, this.h, this);\n    };\n    OrderedMap.prototype.upperBound = function(r) {\n        var e = this.X(this.W, r);\n        return new OrderedMapIterator(e, this.h, this);\n    };\n    OrderedMap.prototype.reverseLowerBound = function(r) {\n        var e = this.Y(this.W, r);\n        return new OrderedMapIterator(e, this.h, this);\n    };\n    OrderedMap.prototype.reverseUpperBound = function(r) {\n        var e = this.Z(this.W, r);\n        return new OrderedMapIterator(e, this.h, this);\n    };\n    OrderedMap.prototype.setElement = function(r, e, t) {\n        return this.v(r, e, t);\n    };\n    OrderedMap.prototype.find = function(r) {\n        var e = this.g(this.W, r);\n        return new OrderedMapIterator(e, this.h, this);\n    };\n    OrderedMap.prototype.getElementByKey = function(r) {\n        var e = this.g(this.W, r);\n        return e.p;\n    };\n    OrderedMap.prototype.union = function(r) {\n        var e = this;\n        r.forEach((function(r) {\n            e.setElement(r[0], r[1]);\n        }));\n        return this.M;\n    };\n    OrderedMap.prototype[Symbol.iterator] = function() {\n        return this.P(this.W);\n    };\n    return OrderedMap;\n}(_Base__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OrderedMap);\n//# sourceMappingURL=OrderedMap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/OrderedMap.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/OrderedSet.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/container/TreeContainer/OrderedSet.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Base__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Base */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/index.js\");\n/* harmony import */ var _Base_TreeIterator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Base/TreeIterator */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/Base/TreeIterator.js\");\n/* harmony import */ var _utils_throwError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/throwError */ \"(ssr)/./node_modules/js-sdsl/dist/esm/utils/throwError.js\");\nvar __extends = undefined && undefined.t || function() {\n    var extendStatics = function(e, t) {\n        extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n        } instanceof Array && function(e, t) {\n            e.__proto__ = t;\n        } || function(e, t) {\n            for (var r in t) if (Object.prototype.hasOwnProperty.call(t, r)) e[r] = t[r];\n        };\n        return extendStatics(e, t);\n    };\n    return function(e, t) {\n        if (typeof t !== \"function\" && t !== null) throw new TypeError(\"Class extends value \" + String(t) + \" is not a constructor or null\");\n        extendStatics(e, t);\n        function __() {\n            this.constructor = e;\n        }\n        e.prototype = t === null ? Object.create(t) : (__.prototype = t.prototype, new __);\n    };\n}();\n\nvar __generator = undefined && undefined.i || function(e, t) {\n    var r = {\n        label: 0,\n        sent: function() {\n            if (o[0] & 1) throw o[1];\n            return o[1];\n        },\n        trys: [],\n        ops: []\n    }, n, i, o, u;\n    return u = {\n        next: verb(0),\n        throw: verb(1),\n        return: verb(2)\n    }, typeof Symbol === \"function\" && (u[Symbol.iterator] = function() {\n        return this;\n    }), u;\n    function verb(e) {\n        return function(t) {\n            return step([ e, t ]);\n        };\n    }\n    function step(u) {\n        if (n) throw new TypeError(\"Generator is already executing.\");\n        while (r) try {\n            if (n = 1, i && (o = u[0] & 2 ? i[\"return\"] : u[0] ? i[\"throw\"] || ((o = i[\"return\"]) && o.call(i), \n            0) : i.next) && !(o = o.call(i, u[1])).done) return o;\n            if (i = 0, o) u = [ u[0] & 2, o.value ];\n            switch (u[0]) {\n              case 0:\n              case 1:\n                o = u;\n                break;\n\n              case 4:\n                r.label++;\n                return {\n                    value: u[1],\n                    done: false\n                };\n\n              case 5:\n                r.label++;\n                i = u[1];\n                u = [ 0 ];\n                continue;\n\n              case 7:\n                u = r.ops.pop();\n                r.trys.pop();\n                continue;\n\n              default:\n                if (!(o = r.trys, o = o.length > 0 && o[o.length - 1]) && (u[0] === 6 || u[0] === 2)) {\n                    r = 0;\n                    continue;\n                }\n                if (u[0] === 3 && (!o || u[1] > o[0] && u[1] < o[3])) {\n                    r.label = u[1];\n                    break;\n                }\n                if (u[0] === 6 && r.label < o[1]) {\n                    r.label = o[1];\n                    o = u;\n                    break;\n                }\n                if (o && r.label < o[2]) {\n                    r.label = o[2];\n                    r.ops.push(u);\n                    break;\n                }\n                if (o[2]) r.ops.pop();\n                r.trys.pop();\n                continue;\n            }\n            u = t.call(e, r);\n        } catch (e) {\n            u = [ 6, e ];\n            i = 0;\n        } finally {\n            n = o = 0;\n        }\n        if (u[0] & 5) throw u[1];\n        return {\n            value: u[0] ? u[1] : void 0,\n            done: true\n        };\n    }\n};\n\nvar __values = undefined && undefined.V || function(e) {\n    var t = typeof Symbol === \"function\" && Symbol.iterator, r = t && e[t], n = 0;\n    if (r) return r.call(e);\n    if (e && typeof e.length === \"number\") return {\n        next: function() {\n            if (e && n >= e.length) e = void 0;\n            return {\n                value: e && e[n++],\n                done: !e\n            };\n        }\n    };\n    throw new TypeError(t ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\n\n\n\n\n\n\nvar OrderedSetIterator = function(e) {\n    __extends(OrderedSetIterator, e);\n    function OrderedSetIterator(t, r, n, i) {\n        var o = e.call(this, t, r, i) || this;\n        o.container = n;\n        return o;\n    }\n    Object.defineProperty(OrderedSetIterator.prototype, \"pointer\", {\n        get: function() {\n            if (this.o === this.h) {\n                (0,_utils_throwError__WEBPACK_IMPORTED_MODULE_0__.throwIteratorAccessError)();\n            }\n            return this.o.u;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OrderedSetIterator.prototype.copy = function() {\n        return new OrderedSetIterator(this.o, this.h, this.container, this.iteratorType);\n    };\n    return OrderedSetIterator;\n}(_Base_TreeIterator__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n\nvar OrderedSet = function(e) {\n    __extends(OrderedSet, e);\n    function OrderedSet(t, r, n) {\n        if (t === void 0) {\n            t = [];\n        }\n        var i = e.call(this, r, n) || this;\n        var o = i;\n        t.forEach((function(e) {\n            o.insert(e);\n        }));\n        return i;\n    }\n    OrderedSet.prototype.P = function(e) {\n        return __generator(this, (function(t) {\n            switch (t.label) {\n              case 0:\n                if (e === undefined) return [ 2 ];\n                return [ 5, __values(this.P(e.K)) ];\n\n              case 1:\n                t.sent();\n                return [ 4, e.u ];\n\n              case 2:\n                t.sent();\n                return [ 5, __values(this.P(e.N)) ];\n\n              case 3:\n                t.sent();\n                return [ 2 ];\n            }\n        }));\n    };\n    OrderedSet.prototype.begin = function() {\n        return new OrderedSetIterator(this.h.K || this.h, this.h, this);\n    };\n    OrderedSet.prototype.end = function() {\n        return new OrderedSetIterator(this.h, this.h, this);\n    };\n    OrderedSet.prototype.rBegin = function() {\n        return new OrderedSetIterator(this.h.N || this.h, this.h, this, 1);\n    };\n    OrderedSet.prototype.rEnd = function() {\n        return new OrderedSetIterator(this.h, this.h, this, 1);\n    };\n    OrderedSet.prototype.front = function() {\n        return this.h.K ? this.h.K.u : undefined;\n    };\n    OrderedSet.prototype.back = function() {\n        return this.h.N ? this.h.N.u : undefined;\n    };\n    OrderedSet.prototype.insert = function(e, t) {\n        return this.v(e, undefined, t);\n    };\n    OrderedSet.prototype.find = function(e) {\n        var t = this.g(this.W, e);\n        return new OrderedSetIterator(t, this.h, this);\n    };\n    OrderedSet.prototype.lowerBound = function(e) {\n        var t = this.U(this.W, e);\n        return new OrderedSetIterator(t, this.h, this);\n    };\n    OrderedSet.prototype.upperBound = function(e) {\n        var t = this.X(this.W, e);\n        return new OrderedSetIterator(t, this.h, this);\n    };\n    OrderedSet.prototype.reverseLowerBound = function(e) {\n        var t = this.Y(this.W, e);\n        return new OrderedSetIterator(t, this.h, this);\n    };\n    OrderedSet.prototype.reverseUpperBound = function(e) {\n        var t = this.Z(this.W, e);\n        return new OrderedSetIterator(t, this.h, this);\n    };\n    OrderedSet.prototype.union = function(e) {\n        var t = this;\n        e.forEach((function(e) {\n            t.insert(e);\n        }));\n        return this.M;\n    };\n    OrderedSet.prototype[Symbol.iterator] = function() {\n        return this.P(this.W);\n    };\n    return OrderedSet;\n}(_Base__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OrderedSet);\n//# sourceMappingURL=OrderedSet.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/OrderedSet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/index.js":
/*!************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Deque: () => (/* reexport safe */ _container_SequentialContainer_Deque__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   HashMap: () => (/* reexport safe */ _container_HashContainer_HashMap__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   HashSet: () => (/* reexport safe */ _container_HashContainer_HashSet__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   LinkList: () => (/* reexport safe */ _container_SequentialContainer_LinkList__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   OrderedMap: () => (/* reexport safe */ _container_TreeContainer_OrderedMap__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   OrderedSet: () => (/* reexport safe */ _container_TreeContainer_OrderedSet__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   PriorityQueue: () => (/* reexport safe */ _container_OtherContainer_PriorityQueue__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Queue: () => (/* reexport safe */ _container_OtherContainer_Queue__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Stack: () => (/* reexport safe */ _container_OtherContainer_Stack__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Vector: () => (/* reexport safe */ _container_SequentialContainer_Vector__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _container_OtherContainer_Stack__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./container/OtherContainer/Stack */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/OtherContainer/Stack.js\");\n/* harmony import */ var _container_OtherContainer_Queue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./container/OtherContainer/Queue */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/OtherContainer/Queue.js\");\n/* harmony import */ var _container_OtherContainer_PriorityQueue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./container/OtherContainer/PriorityQueue */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/OtherContainer/PriorityQueue.js\");\n/* harmony import */ var _container_SequentialContainer_Vector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./container/SequentialContainer/Vector */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Vector.js\");\n/* harmony import */ var _container_SequentialContainer_LinkList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./container/SequentialContainer/LinkList */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/LinkList.js\");\n/* harmony import */ var _container_SequentialContainer_Deque__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./container/SequentialContainer/Deque */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/SequentialContainer/Deque.js\");\n/* harmony import */ var _container_TreeContainer_OrderedSet__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./container/TreeContainer/OrderedSet */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/OrderedSet.js\");\n/* harmony import */ var _container_TreeContainer_OrderedMap__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./container/TreeContainer/OrderedMap */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/TreeContainer/OrderedMap.js\");\n/* harmony import */ var _container_HashContainer_HashSet__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./container/HashContainer/HashSet */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/HashContainer/HashSet.js\");\n/* harmony import */ var _container_HashContainer_HashMap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./container/HashContainer/HashMap */ \"(ssr)/./node_modules/js-sdsl/dist/esm/container/HashContainer/HashMap.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanMtc2RzbC9kaXN0L2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFvRTs7QUFFQTs7QUFFZ0I7O0FBRVQ7O0FBRUk7O0FBRU47O0FBRUk7O0FBRUE7O0FBRU47O0FBRUE7QUFDdkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qcy1zZHNsL2Rpc3QvZXNtL2luZGV4LmpzP2EwZTkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCBhcyBTdGFjayB9IGZyb20gXCIuL2NvbnRhaW5lci9PdGhlckNvbnRhaW5lci9TdGFja1wiO1xuXG5leHBvcnQgeyBkZWZhdWx0IGFzIFF1ZXVlIH0gZnJvbSBcIi4vY29udGFpbmVyL090aGVyQ29udGFpbmVyL1F1ZXVlXCI7XG5cbmV4cG9ydCB7IGRlZmF1bHQgYXMgUHJpb3JpdHlRdWV1ZSB9IGZyb20gXCIuL2NvbnRhaW5lci9PdGhlckNvbnRhaW5lci9Qcmlvcml0eVF1ZXVlXCI7XG5cbmV4cG9ydCB7IGRlZmF1bHQgYXMgVmVjdG9yIH0gZnJvbSBcIi4vY29udGFpbmVyL1NlcXVlbnRpYWxDb250YWluZXIvVmVjdG9yXCI7XG5cbmV4cG9ydCB7IGRlZmF1bHQgYXMgTGlua0xpc3QgfSBmcm9tIFwiLi9jb250YWluZXIvU2VxdWVudGlhbENvbnRhaW5lci9MaW5rTGlzdFwiO1xuXG5leHBvcnQgeyBkZWZhdWx0IGFzIERlcXVlIH0gZnJvbSBcIi4vY29udGFpbmVyL1NlcXVlbnRpYWxDb250YWluZXIvRGVxdWVcIjtcblxuZXhwb3J0IHsgZGVmYXVsdCBhcyBPcmRlcmVkU2V0IH0gZnJvbSBcIi4vY29udGFpbmVyL1RyZWVDb250YWluZXIvT3JkZXJlZFNldFwiO1xuXG5leHBvcnQgeyBkZWZhdWx0IGFzIE9yZGVyZWRNYXAgfSBmcm9tIFwiLi9jb250YWluZXIvVHJlZUNvbnRhaW5lci9PcmRlcmVkTWFwXCI7XG5cbmV4cG9ydCB7IGRlZmF1bHQgYXMgSGFzaFNldCB9IGZyb20gXCIuL2NvbnRhaW5lci9IYXNoQ29udGFpbmVyL0hhc2hTZXRcIjtcblxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIYXNoTWFwIH0gZnJvbSBcIi4vY29udGFpbmVyL0hhc2hDb250YWluZXIvSGFzaE1hcFwiO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/utils/checkObject.js":
/*!************************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/utils/checkObject.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ checkObject)\n/* harmony export */ });\nfunction checkObject(t) {\n    var e = typeof t;\n    return e === \"object\" && t !== null || e === \"function\";\n}\n//# sourceMappingURL=checkObject.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanMtc2RzbC9kaXN0L2VzbS91dGlscy9jaGVja09iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2pzLXNkc2wvZGlzdC9lc20vdXRpbHMvY2hlY2tPYmplY3QuanM/NGU0ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBjaGVja09iamVjdCh0KSB7XG4gICAgdmFyIGUgPSB0eXBlb2YgdDtcbiAgICByZXR1cm4gZSA9PT0gXCJvYmplY3RcIiAmJiB0ICE9PSBudWxsIHx8IGUgPT09IFwiZnVuY3Rpb25cIjtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNoZWNrT2JqZWN0LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/utils/checkObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/js-sdsl/dist/esm/utils/throwError.js":
/*!***********************************************************!*\
  !*** ./node_modules/js-sdsl/dist/esm/utils/throwError.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   throwIteratorAccessError: () => (/* binding */ throwIteratorAccessError)\n/* harmony export */ });\nfunction throwIteratorAccessError() {\n    throw new RangeError(\"Iterator access denied!\");\n}\n//# sourceMappingURL=throwError.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanMtc2RzbC9kaXN0L2VzbS91dGlscy90aHJvd0Vycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2pzLXNkc2wvZGlzdC9lc20vdXRpbHMvdGhyb3dFcnJvci5qcz8xYzk1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiB0aHJvd0l0ZXJhdG9yQWNjZXNzRXJyb3IoKSB7XG4gICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJJdGVyYXRvciBhY2Nlc3MgZGVuaWVkIVwiKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRocm93RXJyb3IuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-sdsl/dist/esm/utils/throwError.js\n");

/***/ })

};
;