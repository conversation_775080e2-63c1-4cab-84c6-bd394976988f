import React from 'react';
import { useAgents } from '@/hooks';

export default function AgentSelector() {
  const {
    agents,
    isLoading,
    error,
    selectedAgent,
    handleAgentChange
  } = useAgents();

  if (isLoading) {
    return (
      <div className="p-4 bg-gray-100 rounded-lg shadow-sm">
        <p className="text-gray-500 text-center">Loading agents...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 rounded-lg shadow-sm">
        <p className="text-red-500 text-center">{error}</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-white rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-4">Select an Assistant</h2>
      <div className="grid grid-cols-1 gap-3">
        {agents.map((agent) => (
          <button
            key={agent.id}
            onClick={() => handleAgentChange(agent)}
            className={`p-4 rounded-lg text-left transition-colors ${
              selectedAgent?.id === agent.id
                ? 'bg-blue-100 border-2 border-blue-500'
                : 'bg-gray-50 hover:bg-gray-100 border-2 border-transparent'
            }`}
          >
            <div className="flex items-center">
              <h3 className="font-medium text-lg">{agent.name}</h3>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
} 