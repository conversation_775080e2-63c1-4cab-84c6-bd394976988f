'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useChat } from '@/contexts/Livechatcontext';
import { API_BASE_URL, fetchUser, UserWithMetrics } from '@/lib/api';
import AttachmentButton from './AttachmentButton';
import AttachmentPreview from './AttachmentPreview';
import AttachmentModal from './AttachmentModal';
import EmojiPicker from './EmojiPicker';
import MessageActions from './MessageActions';
import GroupCreationModal from './GroupCreationModal';
import { Menu } from '@headlessui/react';
// @ts-ignore – heroicons has no TS types bundled, safe to ignore
import { DotsVerticalIcon, StarIcon, TrashIcon, ArchiveIcon } from '@heroicons/react/solid';
import SharedResponseDisplay from '../ai-chat/SharedResponseDisplay';

interface LiveChatSidebarProps {
  onSidebarToggle?: (isOpen: boolean) => void;
}



export default function LiveChatSidebar({ onSidebarToggle }: LiveChatSidebarProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [attachmentModal, setAttachmentModal] = useState<{url:string,type:string}|null>(null);
  const [hasSearched, setHasSearched] = useState(false);
  
  const [starredIds, setStarredIds] = useState<number[]>(() => {
    if (typeof window === 'undefined') return [];
    const data = localStorage.getItem('starred_conversations');
    return data ? JSON.parse(data) : [];
  });
  const [archivedIds, setArchivedIds] = useState<number[]>(() => {
    if (typeof window === 'undefined') return [];
    const data = localStorage.getItem('archived_conversations');
    return data ? JSON.parse(data) : [];
  });
  const [showArchived, setShowArchived] = useState(false);
  const [archivedGroupIds, setArchivedGroupIds] = useState<number[]>(() => {
    if (typeof window === 'undefined') return [];
    const data = localStorage.getItem('archived_groups');
    return data ? JSON.parse(data) : [];
  });
  
  // Notify parent component when sidebar visibility changes
  useEffect(() => {
    if (onSidebarToggle) {
      onSidebarToggle(isOpen);
    }
  }, [isOpen, onSidebarToggle]);
  // Close this sidebar when others open
  useEffect(() => {
    const handler = () => setIsOpen(false);
    window.addEventListener('close-live-chat', handler as EventListener);
    return () => window.removeEventListener('close-live-chat', handler as EventListener);
  }, []);

  const {
    state,
    dispatch,
    sendMessage,
    editMessage,
    deleteMessage,
    editGroupMessage,
    deleteGroupMessage,
    hideGroupMessage,
    startTyping,
    stopTyping,
    markConversationAsRead,
    createConversation,
    connectMQTT,
    hideMessage,
    sendGroupMessage,
    leaveGroup
  } = useChat();
  
  // Allow opening from a global custom event
  useEffect(() => {
    const handler = () => {
      // Reset active conversation when opening sidebar
      dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: null });
      dispatch({ type: 'SET_ACTIVE_GROUP', payload: null });
      setCurrentView('conversations'); // Always show conversations list first
      setIsOpen(true);
    };
    window.addEventListener('open-live-chat', handler as EventListener);
    return () => window.removeEventListener('open-live-chat', handler as EventListener);
  }, [dispatch]);

  const [showUserList, setShowUserList] = useState(false);
  const [showGroupModal, setShowGroupModal] = useState(false);
  const [messageInput, setMessageInput] = useState('');
  const [searchUser, setSearchUser] = useState('');
  const [user, setUser] = useState<UserWithMetrics | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const initialLoadDoneRef = useRef<boolean>(false);
  const [sortedConversations, setSortedConversations] = useState<any[]>([]);
  const [panelWidth, setPanelWidth] = useState<number>(380);
  const [isResizing, setIsResizing] = useState<boolean>(false);
  const [isDesktop, setIsDesktop] = useState<boolean>(false);
  // Track the current view state
  const [currentView, setCurrentView] = useState<'conversations' | 'chat'>('conversations');

  // Track viewport to use full width on mobile and resizable width on desktop
  useEffect(() => {
    const updateViewport = () => setIsDesktop(window.innerWidth >= 768);
    updateViewport();
    window.addEventListener('resize', updateViewport);
    return () => window.removeEventListener('resize', updateViewport);
  }, []);

  // Handle drag to resize
  useEffect(() => {
    if (!isResizing) return;
    const onMouseMove = (e: MouseEvent) => {
      // Since panel is fixed at left:0, width equals current X position
      const next = Math.min(900, Math.max(320, e.clientX));
      setPanelWidth(next);
    };
    const onMouseUp = () => {
      setIsResizing(false);
      document.body.style.cursor = '';
      (document.body.style as any).userSelect = '';
    };
    document.body.style.cursor = 'col-resize';
    (document.body.style as any).userSelect = 'none';
    window.addEventListener('mousemove', onMouseMove);
    window.addEventListener('mouseup', onMouseUp);
    return () => {
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', onMouseUp);
    };
  }, [isResizing]);

  // Auto-scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [state.activeConversation?.messages]);

  // Keep conversations ordered by most recent activity (updated_at or last_message)
  useEffect(() => {
    // Initialize with empty array if no conversations or groups
    if ((!state.conversations || state.conversations.length === 0) && (!state.groups || state.groups.length === 0)) {
      setSortedConversations([]);
      return;
    }
    
    // Combine conversations and groups into a single array
    const allItems = [
      ...(state.conversations || []),
      ...(state.groups || []).map(group => ({
        ...group,
        isGroup: true // Add a flag to identify groups
      }))
    ];
    
    // Sort all items by their timestamp
    allItems.sort((a: any, b: any) => {
      const getTime = (c: any) => {
        // Prioritize updated_at, then last_message.created_at, then created_at
        const t = c?.updated_at || c?.last_message?.created_at || c?.created_at;
        return t ? new Date(t).getTime() : 0;
      };
      return getTime(b) - getTime(a); // Sort in descending order (newest first)
    });
    
    
    setSortedConversations(allItems);
  }, [state.conversations, state.groups]);

  // Load initial data (only once)
  useEffect(() => {
    const initializeChat = async () => {
      // Skip if already initialized
      if (initialLoadDoneRef.current) {
        return;
      }
      
      // Set current user
      const userData = localStorage.getItem('user');
      if (userData) {
        try {
          const user = JSON.parse(userData);
          dispatch({ type: 'SET_CURRENT_USER', payload: user });
          
        } catch (error) {
          console.error('Error parsing user data:', error);
          return;
        }
      }

      // Load conversations and users only if not already loaded
      try {
        // First load conversations
        await loadConversations();
        
        // Then load unread counts to update the conversations
        await loadUnreadCount();
        
        // Finally load groups
        await loadGroups();
        
        initialLoadDoneRef.current = true;
      } catch (error) {
        console.error('Error loading initial data:', error);
      }
    };

    // Longer debounce to prevent duplicate initialization
    const initTimeout = setTimeout(() => {
      initializeChat();
    }, 500);

    return () => {
      clearTimeout(initTimeout);
    };
  }, []); // No dependencies to prevent re-runs

  // Removed duplicate WebSocket connect effect. ChatContext now owns connection lifecycle exclusively.

  // Load conversations
  const loadConversations = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/conversations`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const conversations = await response.json();
        dispatch({ type: 'SET_CONVERSATIONS', payload: conversations });
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
    }
  };

  // Load unread count and messages
  const loadUnreadCount = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/unread-messages`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        
        // Set unread count
        dispatch({ type: 'SET_UNREAD_COUNT', payload: data.total_unread });
        
        // Extract and set unread messages
        const allUnreadMessages = [];
        if (data.conversations && data.conversations.length > 0) {
          data.conversations.forEach(conv => {
            if (conv.unread_messages && conv.unread_messages.length > 0) {
              allUnreadMessages.push(...conv.unread_messages);
            }
          });
        }
        
        dispatch({ type: 'SET_UNREAD_MESSAGES', payload: allUnreadMessages });
        
        // Update conversations with unread counts
        if (data.conversations && data.conversations.length > 0) {
          // Update each conversation with its unread count individually
          data.conversations.forEach(conv => {
            dispatch({ 
              type: 'UPDATE_CONVERSATION_UNREAD_COUNT', 
              payload: { 
                conversationId: conv.conversation_id, 
                unreadCount: conv.unread_count 
              } 
            });
          });
        }
      }
    } catch (error) {
      console.error('Error loading unread count:', error);
    }
  };

  // Load conversation messages
  const loadConversationMessages = async (conversationId: number) => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/conversations/${conversationId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const conversation = await response.json();
        dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: conversation });
        
        // Mark as read
        markConversationAsRead(conversationId);
      }
    } catch (error) {
      console.error('Error loading conversation messages:', error);
    }
  };

  // Handle conversation selection
  const handleConversationSelect = (conversation: any) => {
    dispatch({ type: 'SET_ACTIVE_GROUP', payload: null });
    loadConversationMessages(conversation.id);
    setCurrentView('chat'); // Update view state to chat
    
    // Update the unread count for this conversation immediately
    if (conversation.unread_count && conversation.unread_count > 0) {
      // Reset unread count for this conversation
      dispatch({ 
        type: 'UPDATE_CONVERSATION_UNREAD_COUNT', 
        payload: { 
          conversationId: conversation.id, 
          unreadCount: 0 
        } 
      });
      
      // Also update the total unread count
      const newTotalUnread = Math.max(0, state.unreadCount - conversation.unread_count);
      dispatch({ type: 'SET_UNREAD_COUNT', payload: newTotalUnread });
      
      // Filter out messages from this conversation from unreadMessages
      const filteredMessages = state.unreadMessages.filter(msg => msg.conversation_id !== conversation.id);
      dispatch({ type: 'SET_UNREAD_MESSAGES', payload: filteredMessages });
    }
  };
  
  // Load groups
  const loadGroups = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/groups/`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (response.ok) {
        const groups = await response.json();
        dispatch({ type: 'SET_GROUPS', payload: groups });
      }
    } catch (error) {
      console.error('Error loading groups:', error);
    }
  };

  // Handle group selection
  const handleGroupSelect = async (group: any) => {
    // First clear any active conversation
    dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: null });
    setCurrentView('chat'); // Update view state to chat
    
    // Find the group in the state to get any existing messages
    const existingGroup = state.groups.find(g => g.id === group.id);
    const existingMessages = existingGroup?.messages || [];
    
    // Set as active group immediately with any existing messages to prevent flicker
    const initialGroup = { ...group, messages: existingMessages };
    dispatch({ type: 'SET_ACTIVE_GROUP', payload: initialGroup });
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/groups/${group.id}/messages`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const fetchedMessages = await response.json();
        // Merge fetched messages with any existing messages that might not be on the server yet
        // (like messages sent during this session)
        const existingMessageIds = new Set(existingMessages.map((msg: any) => msg.id).filter(Boolean));
        const newMessages = fetchedMessages.filter((msg: any) => !msg.id || !existingMessageIds.has(msg.id));
        const mergedMessages = [...existingMessages, ...newMessages];
        
        // Sort messages by created_at
        mergedMessages.sort((a: any, b: any) => 
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );
        
        // Update the group with merged messages
        const updatedGroup = { ...group, messages: mergedMessages };
        
        // Update the group in the state
        dispatch({ type: 'UPDATE_GROUP', payload: updatedGroup });
        dispatch({ type: 'SET_ACTIVE_GROUP', payload: updatedGroup });
      }
    } catch (error) {
      console.error('Error loading group messages:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load group messages' });
    }
  };


  // Handle user selection to start new chat
    const handleUserSelect = async (user: any) => { 
    const conversation = await createConversation(user.id);
    if (conversation) {
      loadConversationMessages(conversation.id);
    }
    setShowUserList(false);
  };

  // Handle emoji select
  const handleEmojiSelect = (emoji: string) => {
    setMessageInput(prev => prev + emoji);
  };

  // Handle message sending
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!messageInput.trim()) {
      return;
    }
    
    if (!state.activeConversation && !state.activeGroup) {
      dispatch({ type: 'SET_ERROR', payload: 'Please select a conversation first' });
      return;
    }

    if (!state.isConnected && !state.activeGroup) {
      dispatch({ type: 'SET_ERROR', payload: 'Not connected to chat server. Please try reconnecting.' });
      return;
    }

    try {
      // Determine message type
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const imageRegex = /(https?:\/\/.*\.(?:png|jpg|jpeg|gif|webp))/i;
      let msgType: string = 'text';
      if (imageRegex.test(messageInput.trim())) {
        msgType = 'image';
      } else if (urlRegex.test(messageInput.trim())) {
        msgType = 'file';
      }

      if (state.activeGroup) {
        sendGroupMessage(state.activeGroup.id, messageInput.trim(), msgType);
        setMessageInput('');
      } else if (state.activeConversation) {
        
        sendMessage(state.activeConversation.id, messageInput.trim(), msgType);
        setMessageInput('');
        
        // Stop typing indicator
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }
        stopTyping(state.activeConversation.id);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to send message. Please try again.' });
    }
  };
  const findUser = async () => {
    try {
      const userData = await fetchUser(searchUser);
      setUser(userData);
      setHasSearched(true); // Will be null if user doesn't exist
    } catch (error) {
      console.error('Error finding user:', error);
      setUser(null);
    }
  };
  // Handle typing
  const handleTyping = (value: string) => {
    setMessageInput(value);
    
    if (state.activeConversation && value.trim()) {
      startTyping(state.activeConversation.id);
      
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Set new timeout to stop typing
      typingTimeoutRef.current = setTimeout(() => {
        if (state.activeConversation) {
          stopTyping(state.activeConversation.id);
        }
      }, 3000);
    }
  };

  // Get other participant in conversation
  const getOtherParticipant = (conversation: any) => {
    if (!state.currentUser) return null;
    
    return conversation.participant1_id === state.currentUser.id 
      ? {
          id: conversation.participant2_id,
          username: conversation.participant2_username,
          department: conversation.participant2_department
        }
      : {
          id: conversation.participant1_id,
          username: conversation.participant1_username,
          department: conversation.participant1_department
        };
  };

  const isUserOnline = (userId: number) => {
    if (state.onlineUsers.some(u => u.user_id === userId)) return true;
    const status = state.allUsers.find(u => u.user_id === userId);
    return Boolean(status?.is_online);
  };

  // Get typing users for current conversation
  const getTypingUsers = () => {
    if (!state.activeConversation) return [];
    const activeId = state.activeConversation.id;
    return state.typingUsers.filter(t => t.conversation_id === activeId);
  };

  // Helper to format a date/time string in Indian Standard Time (IST)
  const formatISTTime = (dateInput: string | number | Date) => {
    let date: Date;

    if (typeof dateInput === 'string') {
      // Backend sends naive UTC timestamps (e.g., "2024-07-11T12:00:00.000000").
      // 1. Strip microseconds to ensure valid JS Date parsing.
      // 2. Append a 'Z' timezone designator when none is present to explicitly treat it as UTC.
      let isoString = dateInput;

      // Remove fractional seconds beyond milliseconds for compatibility (e.g., ".000000" → "").
      if (isoString.includes('.')) {
        isoString = isoString.split('.')[0];
      }

      // If the string lacks an explicit timezone offset, assume UTC.
      if (!/[Zz]|[+-]\d{2}:?\d{2}$/.test(isoString)) {
        isoString += 'Z';
      }

      date = new Date(isoString);
    } else {
      date = new Date(dateInput);
    }

    // Fallback in case of invalid date
    if (isNaN(date.getTime())) {
      return '';
    }

    return date.toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false, // 24-hour format; switch to true for 12-hour
      timeZone: 'Asia/Kolkata',
    });
  };

  // Star, archive, delete conversation functions
  const persistStarred = (ids: number[]) => {
    setStarredIds(ids);
    localStorage.setItem('starred_conversations', JSON.stringify(ids));
  };

  const persistArchived = (ids: number[]) => {
    setArchivedIds(ids);
    localStorage.setItem('archived_conversations', JSON.stringify(ids));
  };
  
  const persistArchivedGroups = (ids: number[]) => {
    setArchivedGroupIds(ids);
    localStorage.setItem('archived_groups', JSON.stringify(ids));
  };

  const toggleStar = (id: number) => {
    const updated = starredIds.includes(id)
      ? starredIds.filter(i => i !== id)
      : [...starredIds, id];
    persistStarred(updated);
  };

  const archiveConversation = (id: number) => {
    if (!archivedIds.includes(id)) {
      persistArchived([...archivedIds, id]);
    }
    if (state.activeConversation?.id === id) {
      dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: null });
    }
  };

  const unarchiveConversation = (id: number) => {
    const updated = archivedIds.filter(i => i !== id);
    persistArchived(updated);
  };

  const deleteConversation = async (id: number) => {
    const token = localStorage.getItem('access_token');

    // First optimistically update UI
    const filtered = state.conversations.filter(c => c.id !== id);
    dispatch({ type: 'SET_CONVERSATIONS', payload: filtered });
    if (state.activeConversation?.id === id) {
      dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: null });
    }

    try {
      const resp = await fetch(`${API_BASE_URL}/chat/conversations/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (!resp.ok) {
        // Revert UI on failure
        dispatch({ type: 'SET_CONVERSATIONS', payload: state.conversations });
        alert('Failed to delete conversation.');
      }
    } catch (err) {
      console.error('deleteConversation', err);
      // Revert UI on error
      dispatch({ type: 'SET_CONVERSATIONS', payload: state.conversations });
      alert('Network error while deleting conversation.');
    }
  };

  // Get last seen for user
  const getLastSeen = (userId: number) => {
    const status = state.allUsers.find(u => u.user_id === userId);
    if (status?.is_online) return 'Online';
    if (status?.last_seen) {
      const date = new Date(status.last_seen);
      return `Last seen ${formatISTTime(date)}`;
    }
    return 'Offline';
  };
  return (
    <>
      {/* Toggle Button - Hidden since buttons are now in ChatSidebar */}
      {/* <button
        onClick={() => {
          if (!isOpen) {
            // Close other sidebars before opening this one
            window.dispatchEvent(new Event('close-knowledge-base'));
            window.dispatchEvent(new Event('close-prompt-gallery'));
          }
          setIsOpen(!isOpen);
        }}
                       className={`hidden md:flex fixed top-44 left-6 z-39 p-2 rounded-full shadow-md transition-all duration-300 ${
          isOpen 
            ? 'bg-white text-blue-900 border-2 border-blue-900 shadow-lg' 
            : 'bg-blue-900 text-white hover:bg-blue-700 border-2 border-transparent'
        }`}
        aria-label={isOpen ? "Close Live Chat" : "Open Live Chat"}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      </button> */}

      {/* Live Chat Sidebar */}
      <div 
        className={`fixed top-[5rem] left-0 h-[calc(100%-5rem)] bg-white shadow-lg z-40 transition-all duration-300 ease-in-out overflow-hidden ${
          isOpen ? '' : 'w-0'
        }`}
        style={{ width: isOpen ? (isDesktop ? panelWidth : '100%') : 0 }}
      >
        <div className="h-full flex flex-col min-h-0">
          {/* Header */}
          <div className="bg-indigo-500 bg-gradient-to-r from-indigo-500 to-blue-500 text-white p-3 flex justify-between items-center">
            <h2 className="text-lg font-semibold flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Live Chat
            </h2>
            <button 
              onClick={() => setIsOpen(false)}
              className="text-white hover:text-gray-100 p-1 rounded-full hover:bg-indigo-400"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Sidebar Content */}
          <div className="flex-1 flex flex-col min-h-0 bg-gray-50">
            
                      {/* Conversations List */}
          {(currentView === 'conversations' || (!state.activeConversation && !state.activeGroup)) ? (
              <div className="flex-1 overflow-y-auto">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 shadow-sm">
                  <div className="p-3">
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium text-gray-800 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                        </svg>
                        <span className="mr-2 text-indigo-800">Conversations</span>
                        
                      </h3>
                      <div className="flex space-x-1.5">
                        <button
                          onClick={() => {
                            setShowUserList(true);
                            setSearchUser('');
                            setHasSearched(false);
                            setUser(null);
                          }}
                          className="px-2.5 py-1.5 bg-gradient-to-r from-blue-500 to-blue-900 text-white text-xs rounded-md hover:from-blue-900 hover:to-blue-700 flex items-center shadow-sm transition-all"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                          Chat
                        </button>
                        <button
                          onClick={() => setShowGroupModal(true)}
                          className="px-2.5 py-1.5 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white text-xs rounded-md hover:from-indigo-600 hover:to-indigo-700 flex items-center shadow-sm transition-all"
                        >
                           <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                          </svg>
                          Group
                        </button>
                        <button
                          onClick={() => setShowArchived(!showArchived)}
                          title={showArchived ? 'Show active' : 'Show archived'}
                          className="p-1.5 bg-white border border-gray-200 rounded-md hover:bg-gray-50 text-xs text-gray-600 shadow-sm transition-all"
                        >
                          {showArchived ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                {sortedConversations.length === 0 ? (
                  <div className="p-8 text-center">
                    <div className="mx-auto w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                    <h4 className="text-indigo-700 font-medium mb-1">No conversations yet</h4>
                    <p className="text-sm text-gray-500">Click the Chat button above to start a new conversation</p>
                  </div>
                ) : (
                  <>
                  {sortedConversations
                    .filter(c => !(c as any).isGroup && (showArchived ? archivedIds.includes(c.id) : !archivedIds.includes(c.id)))
                    .map((conversation) => {
                    const otherParticipant = getOtherParticipant(conversation);
                    const isOnline = otherParticipant ? isUserOnline(otherParticipant.id) : false;
                    const isActive = state.activeConversation?.id === conversation.id;
                    const hasUnread = conversation.unread_count !== undefined && conversation.unread_count !== null && conversation.unread_count > 0;
                    
                    return (
                      <div
                        key={conversation.id}
                        onClick={() => handleConversationSelect(conversation)}
                        className={`p-2.5 border-b border-gray-100 cursor-pointer hover:bg-blue-50/40 transition-all relative ${
                          isActive ? 'bg-blue-50 shadow-sm' : hasUnread ? 'bg-blue-50/30' : ''
                        }`}
                      > 
                        
                        <div className="flex items-center space-x-3">
                          <div className="relative">
                            <div className={`w-10 h-10 ${
                              isActive 
                                ? 'bg-gradient-to-br from-blue-500 to-blue-900' 
                                : 'bg-gradient-to-br from-blue-400 to-indigo-500'
                            } rounded-full flex items-center justify-center text-white font-medium text-sm shadow-sm ring-2 ring-white`}>
                              {otherParticipant?.username ? otherParticipant.username[0]?.toUpperCase() : '?'}
                            </div>
                            {isOnline && (
                              <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-white rounded-full shadow-sm animate-pulse"></div>
                            )}
                            {hasUnread && conversation.unread_count > 0 && (
                              <div className="absolute -top-1 -right-1 min-w-[18px] h-[18px] bg-blue-900 text-white text-xs flex items-center justify-center rounded-full font-medium shadow-sm ring-2 ring-white">
                                {conversation.unread_count}
                              </div>
                            )}
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex justify-between items-center">
                              <p className={`text-sm font-medium ${isActive ? 'text-blue-700' : hasUnread ? 'text-gray-900' : 'text-gray-800'} truncate flex items-center`}>
                                {otherParticipant?.username}
                              </p>
                              <div className="flex items-center space-x-1">
                                {conversation.last_message && (
                                  <span className="text-xs text-gray-400">
                                    {formatISTTime(conversation.last_message.created_at)}
                                  </span>
                                )}
                                <Menu as="div" className="relative inline-block text-left">
                                  <Menu.Button onClick={(e)=>e.stopPropagation()} className="p-1 rounded-full hover:bg-gray-200 focus:outline-none">
                                    <DotsVerticalIcon className="w-3.5 h-3.5 text-gray-500" />
                                  </Menu.Button>
                                  <Menu.Items className="origin-top-right absolute right-0 mt-1 w-36 rounded-md shadow-lg bg-white ring-1 ring-gray-100 focus:outline-none z-50">
                                    <div className="py-1">
                                      <Menu.Item>
                                        {({ active }) => (
                                          <button
                                            onClick={(e) => {e.stopPropagation(); toggleStar(conversation.id);}}
                                            className={`${active ? 'bg-gray-50' : ''} w-full flex items-center px-3 py-1.5 text-xs text-gray-700`}
                                          >
                                            <StarIcon className="w-3.5 h-3.5 mr-2 text-amber-400" />
                                            {starredIds.includes(conversation.id) ? 'Unstar' : 'Star'}
                                          </button>
                                        )}
                                      </Menu.Item>
                                      {showArchived ? (
                                        <Menu.Item>
                                          {({ active }) => (
                                            <button
                                              onClick={(e) => {e.stopPropagation(); unarchiveConversation(conversation.id);}}
                                              className={`${active ? 'bg-gray-50' : ''} w-full flex items-center px-3 py-1.5 text-xs text-gray-700`}
                                            >
                                              <ArchiveIcon className="w-3.5 h-3.5 mr-2 text-gray-500" />
                                              Unarchive
                                            </button>
                                          )}
                                        </Menu.Item>
                                      ) : (
                                        <Menu.Item>
                                          {({ active }) => (
                                            <button
                                              onClick={(e) => {e.stopPropagation(); archiveConversation(conversation.id);}}
                                              className={`${active ? 'bg-gray-50' : ''} w-full flex items-center px-3 py-1.5 text-xs text-gray-700`}
                                            >
                                              <ArchiveIcon className="w-3.5 h-3.5 mr-2 text-gray-500" />
                                              Archive
                                            </button>
                                          )}
                                        </Menu.Item>
                                      )}
                                      <Menu.Item>
                                        {({ active }) => (
                                          <button
                                            onClick={(e) => {e.stopPropagation(); deleteConversation(conversation.id);}}
                                            className={`${active ? 'bg-gray-50' : ''} w-full flex items-center px-3 py-1.5 text-xs text-red-500`}
                                          >
                                            <TrashIcon className="w-3.5 h-3.5 mr-2" />
                                            Delete
                                          </button>
                                        )}
                                      </Menu.Item>
                                    </div>
                                  </Menu.Items>
                                </Menu>
                              </div>
                            </div>
                            {otherParticipant?.department && (
                              <p className="text-xs text-gray-400 truncate">{otherParticipant.department}</p>
                            )}
                            {conversation.last_message && (
                              <p className="text-xs text-gray-500 truncate mt-1 flex items-center">
                                {conversation.last_message.sender_id === state.currentUser?.id && (
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                                  </svg>
                                )}
                                {conversation.last_message.message_type === 'text' 
                                  ? conversation.last_message.content
                                  : conversation.last_message.message_type === 'file' 
                                    ? 'File' 
                                    : conversation.last_message.message_type === 'image' 
                                      ? 'Image' 
                                      : 'Message'}
                              </p>
                            )}
                          </div>
                        </div>
                        {starredIds.includes(conversation.id) && (
                          <StarIcon className="w-3.5 h-3.5 text-amber-400 absolute bottom-2 right-2" />
                        )}
                      </div>
                    );
                  })}
                  
                  {/* Groups Section */}
                  {state.groups && state.groups.length > 0 && (
                    <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border-t border-b border-gray-200 shadow-sm mt-2">
                      <div className="p-3 flex items-center justify-between">
                        <h4 className="text-xs font-medium text-purple-800 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                          </svg>
                          <span>Group Conversations</span>
                        </h4>
                      </div>
                    </div>
                  )}
                  {sortedConversations
                    .filter(c => (c as any).isGroup && (showArchived ? archivedGroupIds.includes(c.id) : !archivedGroupIds.includes(c.id)))
                    .map(group => {
                    const isActive = state.activeGroup?.id === group.id;
                    const hasUnread = !!(group as any).unread_count && (group as any).unread_count > 0;
                    
                    return (
                      <div
                        key={`grp-${group.id}`}
                        onClick={() => handleGroupSelect(group)}
                        className={`p-2.5 border-b border-gray-100 cursor-pointer hover:bg-indigo-50/40 transition-all ${
                          isActive ? 'bg-indigo-50 shadow-sm' : hasUnread ? 'bg-indigo-50/30' : ''
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`w-10 h-10 ${
                            isActive 
                              ? 'bg-gradient-to-br from-purple-500 to-indigo-600' 
                              : 'bg-gradient-to-br from-purple-400 to-indigo-500'
                          } rounded-full flex items-center justify-center text-white font-medium text-sm shadow-sm ring-2 ring-white`}>
                            {group.name[0]?.toUpperCase()}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex justify-between items-center">
                              <p className={`text-sm font-medium ${isActive ? 'text-indigo-700' : 'text-gray-800'} truncate flex items-center`}>
                                {group.name}
                                {hasUnread && (
                                  <span className="ml-2 bg-indigo-500 text-white text-xs px-1.5 rounded-full">
                                    {(group as any).unread_count}
                                  </span>
                                )}
                              </p>
                              <div className="flex items-center space-x-1">
                                {group.last_message && (
                                  <span className="text-xs text-gray-400">{formatISTTime(group.last_message.created_at)}</span>
                                )}
                                <Menu as="div" className="relative inline-block text-left">
                                  <Menu.Button onClick={(e)=>e.stopPropagation()} className="p-1 rounded-full hover:bg-gray-200 focus:outline-none">
                                    <DotsVerticalIcon className="w-3.5 h-3.5 text-gray-500" />
                                  </Menu.Button>
                                  <Menu.Items className="origin-top-right absolute right-0 mt-1 w-36 rounded-md shadow-lg bg-white ring-1 ring-gray-100 focus:outline-none z-50">
                                    <div className="py-1">
                                      {archivedGroupIds.includes(group.id) ? (
                                        <Menu.Item>{({active}) => (
                                          <button 
                                            onClick={(e)=>{e.stopPropagation();persistArchivedGroups(archivedGroupIds.filter(i=>i!==group.id));}} 
                                            className={`${active?'bg-gray-50':''} w-full flex items-center px-3 py-1.5 text-xs text-gray-700`}
                                          >
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                            </svg>
                                            Unarchive
                                          </button>
                                        )}</Menu.Item>
                                      ) : (
                                        <Menu.Item>{({active}) => (
                                          <button 
                                            onClick={(e)=>{e.stopPropagation();persistArchivedGroups([...archivedGroupIds, group.id]);}} 
                                            className={`${active?'bg-gray-50':''} w-full flex items-center px-3 py-1.5 text-xs text-gray-700`}
                                          >
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                            </svg>
                                            Archive
                                          </button>
                                        )}</Menu.Item>
                                      )}
                                      <Menu.Item>{({active}) => (
                                        <button 
                                          onClick={(e)=>{e.stopPropagation();leaveGroup(group.id);}} 
                                          className={`${active?'bg-gray-50':''} w-full flex items-center px-3 py-1.5 text-xs text-red-500`}
                                        >
                                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                          </svg>
                                          Leave group
                                        </button>
                                      )}</Menu.Item>
                                    </div>
                                  </Menu.Items>
                                </Menu>
                              </div>
                            </div>
                            {group.last_message && (
                              <p className="text-xs text-gray-500 truncate mt-1 flex items-center">
                                {group.last_message.sender_username && (
                                  <span className="text-xs font-medium text-gray-600 mr-1">{group.last_message.sender_username.split(' ')[0]}:</span>
                                )}
                                {group.last_message.content}
                              </p>
                            )}
                            <p className="text-xs text-gray-400 mt-0.5 flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                              </svg>
                              {group.members?.length || 0} members
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  </>
                )}
              </div>
            ) : (
              <>
                {/* Chat Header */}
                <div className="p-3 border-b border-gray-200 bg-white">
                  <div className="flex items-center">
                    <button
                      onClick={() => {
                        dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: null });
                        dispatch({ type: 'SET_ACTIVE_GROUP', payload: null });
                        setCurrentView('conversations'); // Set view to conversations list
                      }}
                      className="mr-2 text-gray-500 hover:text-gray-700"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    
                    {state.activeGroup ? (
                      <>
                        <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
                          {state.activeGroup.name[0]?.toUpperCase()}
                        </div>
                        <div className="ml-2">
                          <p className="text-sm font-medium text-gray-900">
                            {state.activeGroup.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            Group chat • {state.activeGroup.members?.length || 0} members
                          </p>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="relative">
                          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
                            {getOtherParticipant(state.activeConversation)?.username[0]?.toUpperCase()}
                          </div>
                          {getOtherParticipant(state.activeConversation) && 
                            isUserOnline(getOtherParticipant(state.activeConversation)!.id) && (
                            <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-green-500 border-2 border-white rounded-full"></div>
                          )}
                        </div>
                        
                        <div className="ml-2">
                          <p className="text-sm font-medium text-gray-900">
                            {getOtherParticipant(state.activeConversation)?.username}
                          </p>
                          <p className="text-xs text-gray-500">
                            {getOtherParticipant(state.activeConversation)?.department}
                          </p>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-3 space-y-3 bg-gray-50">
                  {(state.activeGroup ? state.activeGroup.messages : state.activeConversation?.messages)?.map((message: any) => {
                    const isOwn = message.sender_id === state.currentUser?.id;
                    
                    // Dedicated rendering for system messages (centered, grey text)
                    if (message.message_type === 'system') {
                      return (
                        <div key={message.id} className="flex justify-center">
                          <p className="text-xs text-gray-500 py-1">{message.content}</p>
                        </div>
                      );
                    }

                    {
                      const bubbleClass =
                        message.message_type === 'shared_ai_response'
                          ? ''
                          : message.message_type === 'teamviewer_request'
                            ? 'rounded-2xl border border-blue-500 bg-blue-50 text-blue-900 px-4 py-2 shadow-sm'
                            : message.message_type === 'text'
                              ? `rounded-2xl px-4 py-2 shadow-md ${isOwn ? 'bg-blue-900 text-white' : 'bg-white text-gray-900 border border-gray-200'}`
                              : 'rounded-2xl p-2 bg-blue-100 text-gray-900 border border-blue-300 max-w-[280px] shadow';
                      return (
                        <div key={message.id} className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}>
                          <div className={`group relative max-w-[85%] rounded-lg ${bubbleClass}`}>
                          {message.message_type === 'deleted' ? (
                            <i className="text-xs text-gray-500">Message deleted</i>
                          ) : message.message_type === 'shared_ai_response' ? (
                            (() => {
                              let payload: any = null;
                              try { payload = JSON.parse(message.content || '{}'); } catch {}
                              const shared = payload?.shared_response;
                              return shared ? (
                                <SharedResponseDisplay
                                  sharedResponse={shared}
                                  shareContext={payload?.share_context}
                                  timestamp={payload?.timestamp || message.created_at}
                                  sharedByUsername={payload?.shared_by_username}
                                />
                              ) : (
                                <div className="bg-white border rounded p-2 text-xs text-gray-700">
                                  Invalid shared response
                                </div>
                              );
                            })()
                          ) : message.message_type === 'teamviewer_request' ? (
                            <div className="flex items-center space-x-2">
                              <span className="text-xs font-medium">TeamViewer session requested</span>
                            </div>
                          ) : message.message_type === 'text' ? (
                            <p className="text-xs break-all">
                              {message.content}
                              {message.is_edited && <span className="text-xs opacity-70"> (edited)</span>}
                            </p>
                          ) : (
                            <AttachmentPreview
                              url={message.content}
                              type={message.message_type}
                              onClick={() => setAttachmentModal({ url: message.content, type: message.message_type })}
                            />
                          )}
                          
                          {message.message_type !== 'shared_ai_response' && (
                            <div className="absolute -top-2 -right-2 opacity-90 group-hover:opacity-100">
                              <MessageActions
                                message={message}
                                isOwn={isOwn}
                                onEdit={(id, newC) => {
                                  const gid = state.activeGroup?.id ?? (message as any).group_id;
                                  if (gid) { editGroupMessage(id, gid, newC); } else { editMessage(id, newC); }
                                }}
                                onDeleteForEveryone={(id) => {
                                  const gid = state.activeGroup?.id ?? (message as any).group_id;
                                  if (gid) { deleteGroupMessage(id, gid); } else { deleteMessage(id); }
                                }}
                                onDeleteForMe={(id) => {
                                  const gid = state.activeGroup?.id ?? (message as any).group_id;
                                  if (gid) { hideGroupMessage(id, gid); } else { hideMessage(id); }
                                }}
                                onForward={() => {}}
                                iconClassName={`${message.message_type==='text' && isOwn ? 'text-white' : 'text-gray-700'}`}
                              />
                            </div>
                          )}
                          
                          <div className={`mt-1 text-right text-[11px] ${message.message_type==='text' && isOwn ? 'text-white/90' : 'text-gray-600'}`}> 
                            <span >
                              {formatISTTime(message.created_at)}
                            </span>
                            {isOwn && (
                              <span className="ml-1">
                                {message.is_read ? '✓✓' : '✓'}
                              </span>
                            )}
                          </div>
                          </div>
                        </div>
                      );
                    }
                  })}
                  
                  {/* Typing Indicator */}
                  {getTypingUsers().length > 0 && (
                    <div className="flex justify-start">
                      <div className="bg-gray-200 text-gray-900 px-3 py-2 rounded-lg">
                        <div className="flex items-center space-x-1">
                          <span className="text-xs">
                            Typing
                          </span>
                          <div className="flex space-x-1">
                            <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce"></div>
                            <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>

                {/* Message Input */}
                <div className="p-3 border-t border-gray-200 bg-white">
                  <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
                    <div className="flex space-x-1 shrink-0">
                      <AttachmentButton 
                        conversationId={state.activeConversation?.id ?? null}
                        groupId={state.activeGroup?.id ?? null}
                        isGroup={!!state.activeGroup}
                      />
                      <EmojiPicker 
                        onEmojiSelect={handleEmojiSelect}
                        buttonClassName="p-1 text-gray-600 hover:text-gray-800"
                      />
                    </div>
                    <input
                      type="text"
                      value={messageInput}
                      onChange={(e) => handleTyping(e.target.value)}
                      placeholder="Type a message..."
                      className="flex-1 min-w-0 px-2 py-1.5 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={!state.isConnected}
                    />
                    <button
                      type="submit"
                      disabled={!state.isConnected || !messageInput.trim()}
                      className="shrink-0 px-2 py-1.5 bg-blue-900 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Send
                    </button>
                  </form>
                </div>
              </>
            )}
          </div>
          {/* Resize handle - desktop only */}
          <div
            className="hidden md:block absolute top-0 right-0 h-full w-1.5 cursor-col-resize bg-transparent hover:bg-blue-200"
            onMouseDown={() => setIsResizing(true)}
            aria-label="Resize chat panel"
          />
        </div>
      </div>

      {/* Group Creation Modal */}
      <GroupCreationModal isOpen={showGroupModal} onClose={() => setShowGroupModal(false)} />
      {attachmentModal && (
        <AttachmentModal
          open={true}
          url={attachmentModal.url}
          type={attachmentModal.type}
          onClose={() => setAttachmentModal(null)}
        />
      )}
      
      {/* User List Modal */}
      {showUserList && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50 backdrop-blur-[2px]">
          <div className="bg-blue-50 p-4 rounded-lg shadow-lg w-[430px] border border-blue-100">
            <div className="flex justify-between items-center mb-4 border-b border-blue-100 pb-2">
            <h3 className="text-xl font-semibold text-blue-800">Start New Chat</h3>
              <button
                onClick={() => {
                  setShowUserList(false);
                  setSearchUser('');
                  setHasSearched(false);
                  setUser(null);
                }}
                className="text-blue-500 hover:text-blue-700 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Search */}
            <div className="mb-3">
              <div className="relative mb-2">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search users..."
                  value={searchUser}
                  onChange={(e) => {
                    setHasSearched(false);
                    setSearchUser(e.target.value);
                  }}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white"
                />
              </div>
              <button 
                onClick={() => findUser()} 
                className="mt-3 w-full bg-blue-900 hover:bg-blue-700 text-white px-4 py-2.5 rounded-lg font-medium transition-colors flex items-center justify-center"
              >
                <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                Search
              </button>
            </div>

            {/* Users List */}
            <div className="bg-white rounded-lg border border-gray-200 p-4 min-h-[100px] flex items-center justify-center">
              {/* Default state or when search hasn't been performed yet */}
              {(!searchUser || !hasSearched) && (
                <div className="text-center">
                  <div className="mx-auto w-14 h-14 bg-indigo-100 rounded-full flex items-center justify-center mb-2">
                    <svg className="w-7 h-7 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                  <p className="text-indigo-700 text-sm">Enter a username to find contacts</p>
                </div>
              )}
              
              {/* Search results */}
              {searchUser && hasSearched && (
                user ? (
                  <div
                    key={user.user_id}
                    onClick={() => handleUserSelect(user)}
                    className="flex items-center space-x-3 bg-blue-50 p-2 hover:bg-gray-300 rounded-lg cursor-pointer transition-colors w-full"
                  >
                    <div className="relative">
                      <div className="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white text-base font-medium">
                        {user.username[0]?.toUpperCase()}
                      </div>
                      {(user.is_active || user.metrics?.is_online) && (
                        <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                      )}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{user.username}</p>
                      <p className="text-xs text-gray-500 capitalize">{user.role}</p>
                    </div>
                    {(user.is_active || user.metrics?.is_online) && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full flex items-center">
                        <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1 animate-pulse"></span>
                        Online
                      </span>
                    )}
                  </div>
                ) : (
                  <div className="text-center w-full">
                    <svg className="h-8 w-8 mx-auto text-gray-300 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-gray-500 text-sm">No user found with username <span className="text-indigo-600 font-medium">"{searchUser}"</span></p>
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      )}

      {/* Error Toast */}
      {state.error && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className={`px-6 py-3 rounded-lg shadow-lg flex items-center space-x-3 ${
            state.error.includes('connect') || state.error.includes('server') 
              ? 'bg-red-500 text-white' 
              : 'bg-orange-500 text-white'
          }`}>
            <span className="flex-1 text-sm">{state.error}</span>
            <button
              onClick={() => dispatch({ type: 'SET_ERROR', payload: null })}
              className="flex-shrink-0 ml-4 text-white hover:text-gray-200"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </>
  );
}
