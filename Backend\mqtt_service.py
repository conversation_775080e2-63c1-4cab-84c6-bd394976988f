"""
MQTT Service - Standalone FastAPI Application

This module provides a standalone MQTT service that handles real-time chat functionality,
presence management, and event broadcasting. It runs as an independent FastAPI application
that communicates with the EMQX broker and provides HTTP APIs for other services.

The service acts as a bridge between REST API calls and MQTT event publishing,
enabling real-time features without embedding MQTT connections in the main application.

Key Features:
- MQTT broker connection with auto-reconnection
- Chat command processing and routing
- Presence updates and status management
- HTTP API for event publishing from other services
- Background task processing for async operations
- Windows event loop compatibility

Dependencies:
- FastAPI: Web framework for HTTP APIs
- aiomqtt: Async MQTT client library
- uvicorn: ASGI server
- pydantic: Data validation and serialization
"""

import asyncio
import sys
import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from app.services.mqtt_client import MQTTClientService
from app.services.redis_service import redis_service
from app import config

# Configure logging for the MQTT service
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Handle FastAPI application lifespan events (startup and shutdown).

    This is the modern way to handle application startup/shutdown in FastAPI,
    replacing the deprecated @app.on_event decorators. The context manager
    yields control to the application runtime and handles cleanup afterwards.

    Args:
        app: The FastAPI application instance (passed automatically by FastAPI)
    """
    global mqtt_service

    logger.info("Starting MQTT Service...")

    try:
        current_loop = asyncio.get_running_loop()
        logger.info(f"Using event loop: {current_loop.__class__.__name__}")
    except Exception:
        pass

    # Initialize Redis (required for presence/session caching)
    redis_connected = await redis_service.initialize_async()
    if redis_connected:
        logger.info("Redis service initialized successfully")
    else:
        logger.warning("Redis service initialization failed - presence features may be limited")

    try:
        # Initialize the MQTT client service and connect to broker
        mqtt_service = MQTTClientService()
        await mqtt_service.initialize()
        logger.info("MQTT service initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize MQTT service: {e}")
        # Continue startup even if MQTT fails - service can still provide API endpoints

    logger.info("MQTT Service startup completed")

    # Yield control to the FastAPI application - this is where the app runs
    yield

    logger.info("Shutting down MQTT Service...")

    if mqtt_service:
        try:
            # Gracefully disconnect from MQTT broker
            await mqtt_service.shutdown()
            logger.info("MQTT service shutdown completed")
        except Exception as e:
            logger.error(f"Error shutting down MQTT service: {e}")

    logger.info("MQTT Service shutdown completed")


# Create FastAPI application with lifespan event handler
app = FastAPI(
    title="MQTT Service",
    description="MQTT service for real-time chat and presence",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan  # Use modern lifespan events instead of deprecated @app.on_event
)

# Add CORS middleware for cross-origin requests from main API
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.MQTT_CORS_ORIGINS,
    allow_credentials=config.MQTT_CORS_ALLOW_CREDENTIALS,
    allow_methods=config.MQTT_CORS_ALLOW_METHODS,
    allow_headers=config.MQTT_CORS_ALLOW_HEADERS,
)

# Global variable to hold the MQTT service instance
# Initialized during startup, used throughout the application lifetime
mqtt_service: Optional[MQTTClientService] = None


class PublishEventRequest(BaseModel):
    """Request model for publishing events to a specific user."""
    user_id: int
    event_data: Dict[str, Any]


class PublishGroupEventRequest(BaseModel):
    """Request model for publishing events to all members of a group."""
    group_id: int
    event_data: Dict[str, Any]
    exclude_user_id: Optional[
        int] = None  # Optional: exclude this user from receiving the event


class ServiceStatusResponse(BaseModel):
    """Response model for service health and status endpoints."""
    status: str  # e.g., "online", "healthy", "degraded"
    mqtt_connected: bool  # Whether MQTT broker connection is active
    timestamp: str  # ISO format timestamp
    message: str  # Human-readable status message


@app.get("/", response_model=ServiceStatusResponse)
async def root():
    """
    Root endpoint providing basic service status.

    Returns:
        ServiceStatusResponse: Current service status and MQTT connection state
    """
    return ServiceStatusResponse(
        status="online",
        mqtt_connected=mqtt_service.is_connected if mqtt_service else False,
        timestamp=datetime.utcnow().isoformat(),
        message="MQTT Service is running"
    )


@app.get("/health", response_model=ServiceStatusResponse)
async def health_check():
    """
    Comprehensive health check endpoint for monitoring.

    Used by load balancers, health checkers, and monitoring systems
    to determine service availability and MQTT broker connectivity.

    Returns:
        ServiceStatusResponse: Health status with MQTT connection details
    """
    mqtt_connected = mqtt_service.is_connected if mqtt_service else False

    return ServiceStatusResponse(
        status="healthy" if mqtt_connected else "degraded",
        mqtt_connected=mqtt_connected,
        timestamp=datetime.utcnow().isoformat(),
        message="MQTT Service health check"
    )


@app.post("/api/v1/mqtt/publish-event")
async def publish_event(
    request: PublishEventRequest, background_tasks: BackgroundTasks
):
    """
    Publish an event to a specific user via MQTT.

    This endpoint allows other services (like the main API) to send real-time
    events to users without direct MQTT access. Uses background tasks to avoid
    blocking the HTTP response while processing the MQTT publish.

    Args:
        request: Contains user_id and event_data to publish
        background_tasks: FastAPI background task manager for async processing

    Returns:
        dict: Status response indicating event was queued

    Raises:
        HTTPException: If MQTT service is not initialized (503 Service Unavailable)
    """
    if not mqtt_service:
        raise HTTPException(status_code=503, detail="MQTT service not initialized")

    # Log warning if MQTT is disconnected but still accept the request
    # The service will attempt reconnection automatically
    if not mqtt_service.is_connected:
        logger.warning(f"MQTT not connected when publishing to user {request.user_id}")

    # Use background task to avoid blocking the API response
    # This ensures fast HTTP responses even if MQTT operations take time
    background_tasks.add_task(_publish_user_event, request.user_id, request.event_data)

    return {
        "status": "queued", "message": f"Event queued for user {request.user_id}",
        "mqtt_connected": mqtt_service.is_connected
    }


@app.post("/api/v1/mqtt/publish-group-event")
async def publish_group_event(
    request: PublishGroupEventRequest, background_tasks: BackgroundTasks
):
    """
    Publish an event to all members of a group via MQTT.

    Similar to publish_event but broadcasts to multiple users. Queries the database
    for group members and publishes to each user (optionally excluding one).

    Args:
        request: Contains group_id, event_data, and optional exclude_user_id
        background_tasks: FastAPI background task manager for async processing

    Returns:
        dict: Status response indicating event was queued

    Raises:
        HTTPException: If MQTT service is not initialized (503 Service Unavailable)
    """
    if not mqtt_service:
        raise HTTPException(status_code=503, detail="MQTT service not initialized")

    if not mqtt_service.is_connected:
        logger.warning(
            f"MQTT not connected when publishing to group {request.group_id}"
        )

    # Background task ensures non-blocking response
    background_tasks.add_task(
        _publish_group_event, request.group_id, request.event_data,
        request.exclude_user_id
    )

    return {
        "status": "queued", "message": f"Event queued for group {request.group_id}",
        "mqtt_connected": mqtt_service.is_connected
    }


@app.get("/api/v1/mqtt/status")
async def mqtt_status():
    """
    Get detailed MQTT service status for debugging and monitoring.

    Returns comprehensive status information including initialization state,
    connection status, and client ID. Used for operational monitoring.

    Returns:
        dict: Status information with the following keys:
            - initialized: bool - Whether the MQTT service was successfully initialized
            - connected: bool - Whether currently connected to MQTT broker
            - client_id: str or None - The MQTT client identifier
            - message: str - Human-readable status description
    """
    if not mqtt_service:
        return {
            "initialized": False, "connected": False, "client_id": None, "message":
            "MQTT service not initialized"
        }

    return {
        "initialized": True, "connected": mqtt_service.is_connected, "client_id":
        mqtt_service.client_id, "message": "MQTT service status"
    }


async def _publish_user_event(user_id: int, event_data: Dict[str, Any]):
    """
    Background task function to publish an event to a specific user.

    Executed asynchronously by FastAPI's background task system.
    This function runs in the background after the HTTP response is sent,
    ensuring the API remains responsive even if MQTT operations are slow.

    Args:
        user_id: Target user ID for the event
        event_data: Event payload dictionary to publish
    """
    try:
        if mqtt_service:
            await mqtt_service.publish_event(user_id, event_data)
    except Exception as e:
        logger.error(f"Failed to publish event to user {user_id}: {e}")


async def _publish_group_event(
    group_id: int, event_data: Dict[str, Any], exclude_user_id: Optional[int]
):
    """
    Background task function to publish an event to all group members.

    Queries the database for group members and publishes to each user
    (optionally excluding one user, typically the sender).

    Args:
        group_id: Target group ID
        event_data: Event payload dictionary to publish
        exclude_user_id: Optional user ID to exclude from publishing
    """
    try:
        if mqtt_service:
            await mqtt_service.publish_to_group(group_id, event_data, exclude_user_id)
    except Exception as e:
        logger.error(f"Failed to publish event to group {group_id}: {e}")


# Development server entry point
# Run this when executing the file directly (python mqtt_service.py)
if __name__ == "__main__":
    # Ensure Windows SelectorEventLoop is used for MQTT compatibility
    if sys.platform.startswith('win'):
        try:
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
        except AttributeError:
            pass

    # Create uvicorn config with explicit loop setting
    config = uvicorn.Config(
        app=app,
        host="127.0.0.1",
        port=8002,
        reload=False,
        log_level="info",
        loop="asyncio",  # Explicitly use asyncio loop
    )
    server = uvicorn.Server(config)

    # Create and set our own event loop with the correct policy
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        loop.run_until_complete(server.serve())
    finally:
        loop.close()
