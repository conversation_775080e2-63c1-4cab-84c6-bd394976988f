# MinIO Storage Structure Update

## New Simplified Structure

### Before (Complex Structure):
```
MinIO Buckets:
├── admin (bucket for admin users)
├── demo_user1 (bucket for regular users)
├── techsarthi-knowledge_documents
├── techsarthi-chat_attachments
└── Various other buckets based on roles/types
```

### After (Simplified Structure):
```
MinIO Buckets:
├── admin (bucket named after username)
│   ├── knowledge_documents/
│   │   └── 2025-01-18/
│   │       └── uuid_filename.ext
│   └── chat_attachments/
│       └── 2025-01-18/
│           └── uuid_filename.ext
├── ankitgoyanka (bucket named after username)
│   ├── knowledge_documents/
│   └── chat_attachments/
└── demo-user1 (bucket named after username)
    ├── knowledge_documents/
    └── chat_attachments/
```

## Key Changes Made

### 1. **Bucket Naming Strategy**
- **One bucket per user** based on their username
- Bucket names use lowercase, alphanumeric + hyphens only
- Same structure regardless of user role (admin or regular user)

### 2. **Folder Structure**
- Only **two folders** per user bucket:
  - `knowledge_documents/` - For all knowledge base files
  - `chat_attachments/` - For all chat-related files
- Files organized by date: `folder_name/YYYY-MM-DD/unique_filename`

### 3. **File Organization**
- Date-based subfolders for easy management
- Unique UUID prefixes to prevent filename conflicts
- Maintains original file extensions

### 4. **Automatic Folder Creation**
- When a new user bucket is created, both required folders are automatically initialized
- Marker files (`.folder`) ensure folders exist even when empty

## Benefits

1. **Simplified Management**: Clear, predictable structure
2. **User-Centric**: Each user has their own namespace
3. **Role-Agnostic**: Admin and regular users follow same structure
4. **Scalable**: Easy to add new users without complex bucket logic
5. **Maintainable**: Reduced complexity in bucket selection logic

## Implementation Details

### Updated Methods:
- `_get_bucket_name()`: Always returns username-based bucket
- `upload_file()`: Uses simplified folder structure
- `upload_binary_data()`: Uses simplified folder structure
- `_generate_object_key()`: Normalized to two folder types
- `ensure_user_space()`: Auto-creates required folders
- `list_user_collections()`: Returns only standard folders

### Supported File Types:
- `knowledge_documents` or `knowledge_document` → `knowledge_documents/`
- `chat_attachments` or `chat_attachment` → `chat_attachments/`
- Any other type → defaults to `chat_attachments/`

## Migration Notes

- Existing files in old structure will remain accessible
- New uploads will use the simplified structure
- Consider running a migration script if you need to reorganize existing files

## Usage Example

```python
# Upload a knowledge document for user 'admin'
result = await minio_service.upload_file(
    file=uploaded_file,
    bucket_type="knowledge_documents",
    user_id=1,
    username="admin"
)

# Result will be stored at:
# Bucket: admin
# Path: knowledge_documents/2025-01-18/uuid_document.pdf
```
