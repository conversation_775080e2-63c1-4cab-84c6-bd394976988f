from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import logging
from ..models.user import User
from .redis_service import redis_service
from .websocket_session_cache import ws_session_cache
from .. import config

logger = logging.getLogger(__name__)

class UserCleanupService:
    """Service for user account deletion while preserving all user data"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def delete_user_account_only(self, db: AsyncSession, user_id: int) -> bool:
        """
        Delete only the user account and clear sessions/cache, preserving all related data
        
        This will:
        - Delete the user record from the user table
        - Clear user sessions and cache
        - Keep all related data (documents, chats, notes, etc.)
        
        Args:
            db: Async database session
            user_id: ID of user to delete
            
        Returns:
            bool: True if deletion successful, False otherwise
        """
        try:
            # Get user first
            result = await db.execute(select(User).where(User.id == user_id))
            user = result.scalars().first()
            if not user:
                self.logger.warning(f"User {user_id} not found for deletion")
                return False
            
            username = user.username
            self.logger.info(f"Starting account deletion for user: {username} (ID: {user_id})")
            
            # 1. Clear user from cache and websocket sessions
            await self._cleanup_user_sessions(user_id)
            
            # 2. Delete only the user record (no related data)
            await db.delete(user)
            await db.commit()
            
            self.logger.info(f"Successfully deleted user account: {username} (ID: {user_id})")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting user account {user_id}: {str(e)}")
            await db.rollback()
            return False
    
    async def _cleanup_user_sessions(self, user_id: int):
        """Clean up user sessions from Redis and WebSocket cache"""
        try:
            # Clear user session from Redis
            session_key = f"{config.CACHE_PREFIX_USER}:session:{user_id}"
            await redis_service.delete(session_key)
            
            # Clear user-specific cache patterns
            await redis_service.delete_pattern(f"{config.CACHE_PREFIX_USER}:{user_id}:*")
            await redis_service.delete_pattern(f"{config.CACHE_PREFIX_CHAT}:*:{user_id}:*")
            
            # Clear WebSocket session if exists
            await ws_session_cache.delete_session(user_id)
            
            self.logger.info(f"Cleaned up sessions for user {user_id}")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up sessions for user {user_id}: {str(e)}")
    
    async def get_user_account_deletion_summary(self, db: AsyncSession, user_id: int) -> dict:
        """Get summary of user account that will be deleted (account only, preserving all data)"""
        try:
            result = await db.execute(select(User).where(User.id == user_id))
            user = result.scalars().first()
            if not user:
                return {}
            
            return {
                "user_id": user_id,
                "username": user.username,
                "email": user.email,
                "role": user.role,
                "status": user.status,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "deletion_type": "account_only",
                "data_preserved": "All user data (documents, chats, notes, etc.) will be preserved"
            }
            
        except Exception as e:
            self.logger.error(f"Error getting user account deletion summary for {user_id}: {str(e)}")
            return {}
    

# Create singleton instance
user_cleanup_service = UserCleanupService()
