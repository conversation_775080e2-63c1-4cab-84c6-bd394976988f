import logging
from typing import List, Dict, Any, Optional
import asyncpg
from qdrant_client.models import Filter, FieldCondition, MatchValue

from image_embedding_client import ImageEmbeddingClient

logger = logging.getLogger(__name__)


class ImageRetriever:
    """Image retrieval using SigLIP-2 embeddings"""
    
    def __init__(self, 
                 image_embedding_service_url: str,
                 qdrant_client,
                 database_url: str):
        """
        Initialize Image Retriever
        
        Args:
            image_embedding_service_url: URL of the SigLIP-2 service
            qdrant_client: Qdrant client instance
            database_url: PostgreSQL database URL for fetching image data
        """
        self.image_embedding_client = ImageEmbeddingClient(image_embedding_service_url)
        self.qdrant_client = qdrant_client
        self.database_url = database_url
        logger.info("Image Retriever initialized")
    
    async def fetch_image_from_pg(self, image_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch image data from PostgreSQL raw_documents table
        
        Args:
            image_id: Image document ID
            
        Returns:
            Dictionary with image data or None if not found
        """
        try:
            # Convert SQLAlchemy-style URL to standard PostgreSQL DSN for asyncpg
            postgres_dsn = self.database_url.replace("postgresql+asyncpg://", "postgresql://")
            conn = await asyncpg.connect(postgres_dsn)
            try:
                row = await conn.fetchrow(
                    "SELECT id, type, content, base64, source FROM raw_documents WHERE id = $1",
                    image_id
                )
                
                if row:
                    content = row['content'] or ''

                    # Parse OCR text from content if present
                    ocr_text = ''
                    ocr_confidence = 0.0

                    if 'OCR Text (confidence:' in content:
                        try:
                            # Extract OCR text and confidence from content
                            ocr_start = content.find('OCR Text (confidence:')
                            if ocr_start != -1:
                                ocr_end = content.find('):', ocr_start)
                                if ocr_end != -1:
                                    confidence_str = content[ocr_start + len('OCR Text (confidence:'):ocr_end]
                                    ocr_confidence = float(confidence_str.strip())

                                    # Extract OCR text content
                                    ocr_text_start = content.find('\n', ocr_end) + 1
                                    ocr_text_end = content.find('\n\nImage Info:', ocr_text_start)
                                    if ocr_text_end == -1:
                                        ocr_text_end = len(content)
                                    ocr_text = content[ocr_text_start:ocr_text_end].strip()
                        except Exception as e:
                            logger.warning(f"Failed to parse OCR data from content: {e}")

                    return {
                        'id': row['id'],
                        'type': row['type'],
                        'content': content,
                        'base64': row['base64'],
                        'source': row['source'],
                        'ocr_text': ocr_text,
                        'ocr_confidence': ocr_confidence
                    }
                else:
                    logger.warning(f"Image {image_id} not found in PostgreSQL")
                    return None
                    
            finally:
                await conn.close()
                
        except Exception as e:
            logger.error(f"Error fetching image from PostgreSQL: {e}")
            return None
    
    def retrieve_images_by_text(
        self, 
        query: str, 
        collection_name: str, 
        top_k: int = 5,
        fetch_base64: bool = False,
        source_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve images using text query with CLIP embeddings
        
        Args:
            query: Text query
            collection_name: Qdrant collection name
            top_k: Number of results to return
            fetch_base64: Whether to fetch base64 image data from PostgreSQL
            source_filter: Optional source filename to filter by
            
        Returns:
            List of retrieved images with metadata
        """
        try:
            logger.info(f"Retrieving images for query: '{query}' from collection: {collection_name}")
            
            # Generate text embedding
            text_embedding_result = self.image_embedding_client.embed_text([query])
            if not text_embedding_result.get('embeddings'):
                logger.warning("No embeddings generated for text query")
                return []
            
            query_embedding = text_embedding_result['embeddings'][0]
            
            # Build filter conditions
            filter_conditions = [
                FieldCondition(
                    key="type",
                    match=MatchValue(value="image")
                )
            ]
            
            # Add source filter if specified
            if source_filter:
                filter_conditions.append(
                    FieldCondition(
                        key="source",  # Image chunks store source directly, not in metadata
                        match=MatchValue(value=source_filter)
                    )
                )
            
            # Search in Qdrant using image vector
            search_results = self.qdrant_client.search(
                collection_name=collection_name,
                query_vector=("image", query_embedding),  # Named vector
                limit=top_k,
                with_payload=True,
                query_filter=Filter(must=filter_conditions)
            )
            
            logger.info(f"Found {len(search_results)} images matching query")
            
            # Prepare results
            retrieved_images = []
            for result in search_results:
                image_result = {
                    'id': result.payload.get('id'),
                    'score': result.score,
                    'page_number': result.payload.get('page_number'),
                    'image_index': result.payload.get('image_index'),
                    'caption': result.payload.get('caption'),
                    'bbox': result.payload.get('bbox'),
                    'width': result.payload.get('width'),
                    'height': result.payload.get('height'),
                    'format': result.payload.get('format'),
                    'source': result.payload.get('source'),
                    'ocr_text': result.payload.get('ocr_text', ''),
                    'ocr_confidence': result.payload.get('ocr_confidence', 0.0)
                }

                retrieved_images.append(image_result)
            
            return retrieved_images
            
        except Exception as e:
            logger.error(f"Error retrieving images: {e}")
            return []
    
    async def retrieve_images_with_base64(
        self, 
        query: str, 
        collection_name: str, 
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Retrieve images with base64 data included
        
        Args:
            query: Text query
            collection_name: Qdrant collection name
            top_k: Number of results to return
            
        Returns:
            List of retrieved images with base64 data
        """
        # Get initial results
        images = self.retrieve_images_by_text(query, collection_name, top_k, fetch_base64=False)
        
        # Fetch base64 data from PostgreSQL
        for image in images:
            image_data = await self.fetch_image_from_pg(image['id'])
            if image_data:
                image['base64'] = image_data.get('base64')
        
        return images

