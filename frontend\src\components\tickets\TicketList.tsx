import React from 'react';
import { Ticket } from '@/types/ticket';
import TicketCard from './TicketCard';
import { LoadingSpinner } from '@/components/ui';
import { TicketEmptyState } from './TicketEmptyState';

interface TicketListProps {
  tickets: Ticket[];
  loading: boolean;
  hasAnyTickets: boolean | null;
  activeFilter: any;
  onTicketClick: (ticket: Ticket) => void;
  onCreateTicket?: () => void;
  isAdmin?: boolean;
  emptyMessage?: string;
}

export function TicketList({ 
  tickets, 
  loading, 
  hasAnyTickets,
  activeFilter,
  onTicketClick, 
  onCreateTicket,
  isAdmin = false,
  emptyMessage 
}: TicketListProps) {
  if (loading) {
    return <LoadingSpinner message="Loading tickets..." />;
  }

  if (tickets.length === 0) {
    return (
      <TicketEmptyState 
        hasAnyTickets={hasAnyTickets}
        activeTab={activeFilter}
        onCreateTicket={onCreateTicket}
      />
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {tickets.map((ticket) => (
        <TicketCard
          key={ticket.id}
          ticket={ticket}
          onClick={onTicketClick}
          isAdmin={isAdmin}
        />
      ))}
    </div>
  );
}
