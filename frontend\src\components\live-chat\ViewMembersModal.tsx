import React from 'react';
import { Dialog } from '@headlessui/react';

interface ViewMembersModalProps {
  isOpen: boolean;
  onClose: () => void;
  group: any;
  isAdmin: boolean;
}

export default function ViewMembersModal({ isOpen, onClose, group, isAdmin }: ViewMembersModalProps) {
  return (
    <Dialog open={isOpen} onClose={onClose} className="fixed z-50 inset-0 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-black opacity-30" aria-hidden="true" />
        <div className="bg-white rounded-lg max-w-md w-full mx-auto p-6 relative z-10">
          <Dialog.Title className="text-lg font-medium text-gray-800 mb-4">Group Members</Dialog.Title>
          <div className="space-y-2 max-h-80 overflow-y-auto">
            {group.members?.map((m: any) => (
              <div key={m.user_id} className="flex justify-between items-center border-b py-1">
                <span>{m.username}</span>
                <span className="text-xs text-gray-500">{m.role === 'admin' ? 'Admin' : ''}</span>
              </div>
            ))}
          </div>
          <div className="text-right mt-4">
            <button onClick={onClose} className="px-4 py-2 text-sm rounded-md bg-blue-900 text-white">Close</button>
          </div>
        </div>
      </div>
    </Dialog>
  );
} 