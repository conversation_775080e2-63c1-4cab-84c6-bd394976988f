// =============================================================================
// GENERAL TYPES
// =============================================================================

export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  department: string;
  employee_id: string;
}

export interface Agent {
  id: string;
  name: string;
}

export interface Ticket {
  id: number;
  ticket_number: string;
  subject: string;
  description: string;
  category: string;
  priority: string;
  status: string;
  department: string;
  created_at: string;
  updated_at: string;
  user_id: number;
  username: string;
  responses: TicketResponse[];
}

export interface TicketResponse {
  id: number;
  content: string;
  response_type: string;
  created_at: string;
  created_by: number;
  creator_username: string;
}

// =============================================================================
// KNOWLEDGE BASE TYPES (Phase 1 Complete)
// =============================================================================
export * from './knowledge-base'; 