'use client';

import React from 'react';

interface SummaryStats {
  users: {
    total: number;
    active: number;
    pending: number;
    rejected?: number;
    approved: number;
    online: number;
  };
  ai_queries: {
    total: number;
    recent_week: number;
  };
  knowledge_docs: {
    total: number;
    indexed: number;
    recent_week: number;
  };
  feedback: {
    total: number;
    helpful: number;
    helpful_percentage: number;
  };
  shared_responses: {
    total: number;
  };
}

interface DashboardSummaryProps {
  stats: SummaryStats;
  loading?: boolean;
}

export default function DashboardSummary({ stats, loading = false }: DashboardSummaryProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const summaryCards = [
    {
      title: 'Active Users',
      // Show approved as total per requirement
      value: stats.users.approved,
      subtitle: `${stats.users.online} online, ${stats.users.pending} pending${
        typeof stats.users.rejected === 'number' ? `, ${stats.users.rejected} rejected` : ''
      }`,
      icon: '👥',
      color: 'blue',
      trend: stats.users.active > 0 ? `${stats.users.active} active` : null
    },
    {
      title: 'AI Queries',
      value: stats.ai_queries.total,
      subtitle: `${stats.ai_queries.recent_week} this week`,
      icon: '🤖',
      color: 'green',
      trend: stats.ai_queries.recent_week > 0 ? '+' + stats.ai_queries.recent_week : null
    },
    {
      title: 'Knowledge Docs',
      value: stats.knowledge_docs.total,
      subtitle: `${stats.knowledge_docs.indexed} indexed (${Math.round((stats.knowledge_docs.indexed / Math.max(stats.knowledge_docs.total, 1)) * 100)}%)`,
      icon: '📚',
      color: 'purple',
      trend: stats.knowledge_docs.recent_week > 0 ? '+' + stats.knowledge_docs.recent_week : null
    },
    {
      title: 'User Feedback',
      value: stats.feedback.total,
      subtitle: `${stats.feedback.helpful_percentage}% helpful`,
      icon: '💬',
      color: 'orange',
      trend: stats.feedback.helpful > 0 ? `${stats.feedback.helpful} helpful` : null
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-50 text-blue-900 border-blue-200',
      green: 'bg-green-50 text-green-600 border-green-200',
      purple: 'bg-purple-50 text-purple-600 border-purple-200',
      orange: 'bg-orange-50 text-orange-600 border-orange-200'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="mb-8">
      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
        {summaryCards.map((card, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-6">
            <div className="mb-4">{/* Removed trend badge and icon */}</div>
            
            <div className="mb-2">
              <div className="text-lg sm:text-2xl font-bold text-gray-900">{card.value.toLocaleString()}</div>
              <div className="text-xs sm:text-sm font-medium text-gray-600">{card.title}</div>
            </div>
            
            <div className="text-[10px] sm:text-xs text-gray-500">{card.subtitle}</div>
          </div>
        ))}
      </div>

      {/* Additional Stats Row */}
      <div className="mt-6 grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 gap-3 sm:gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4">
          <div>
            <div className="text-base sm:text-lg font-semibold text-gray-900">{stats.shared_responses.total}</div>
            <div className="text-xs sm:text-sm text-gray-600">Responses Shared</div>
          </div>
        </div>

        {/* Indexing Rate display - REMOVED */}

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4">
          <div>
            <div className="text-base sm:text-lg font-semibold text-gray-900">
              {Math.round((stats.users.online / Math.max(stats.users.approved, 1)) * 100)}%
            </div>
            <div className="text-xs sm:text-sm text-gray-600">Users Online</div>
          </div>
        </div>
      </div>
    </div>
  );
}
