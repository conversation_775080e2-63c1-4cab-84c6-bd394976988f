"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/worker-timers";
exports.ids = ["vendor-chunks/worker-timers"];
exports.modules = {

/***/ "(ssr)/./node_modules/worker-timers/build/es2019/factories/load-or-return-broker.js":
/*!************************************************************************************!*\
  !*** ./node_modules/worker-timers/build/es2019/factories/load-or-return-broker.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLoadOrReturnBroker: () => (/* binding */ createLoadOrReturnBroker)\n/* harmony export */ });\nconst createLoadOrReturnBroker = (loadBroker, worker) => {\n    let broker = null;\n    return () => {\n        if (broker !== null) {\n            return broker;\n        }\n        const blob = new Blob([worker], { type: 'application/javascript; charset=utf-8' });\n        const url = URL.createObjectURL(blob);\n        broker = loadBroker(url);\n        // Bug #1: Edge up until v18 didn't like the URL to be revoked directly.\n        setTimeout(() => URL.revokeObjectURL(url));\n        return broker;\n    };\n};\n//# sourceMappingURL=load-or-return-broker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd29ya2VyLXRpbWVycy9idWlsZC9lczIwMTkvZmFjdG9yaWVzL2xvYWQtb3ItcmV0dXJuLWJyb2tlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLCtCQUErQixnQkFBZ0I7QUFDekY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3dvcmtlci10aW1lcnMvYnVpbGQvZXMyMDE5L2ZhY3Rvcmllcy9sb2FkLW9yLXJldHVybi1icm9rZXIuanM/Nzk0MiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgY3JlYXRlTG9hZE9yUmV0dXJuQnJva2VyID0gKGxvYWRCcm9rZXIsIHdvcmtlcikgPT4ge1xuICAgIGxldCBicm9rZXIgPSBudWxsO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGlmIChicm9rZXIgIT09IG51bGwpIHtcbiAgICAgICAgICAgIHJldHVybiBicm9rZXI7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFt3b3JrZXJdLCB7IHR5cGU6ICdhcHBsaWNhdGlvbi9qYXZhc2NyaXB0OyBjaGFyc2V0PXV0Zi04JyB9KTtcbiAgICAgICAgY29uc3QgdXJsID0gVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTtcbiAgICAgICAgYnJva2VyID0gbG9hZEJyb2tlcih1cmwpO1xuICAgICAgICAvLyBCdWcgIzE6IEVkZ2UgdXAgdW50aWwgdjE4IGRpZG4ndCBsaWtlIHRoZSBVUkwgdG8gYmUgcmV2b2tlZCBkaXJlY3RseS5cbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiBVUkwucmV2b2tlT2JqZWN0VVJMKHVybCkpO1xuICAgICAgICByZXR1cm4gYnJva2VyO1xuICAgIH07XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9hZC1vci1yZXR1cm4tYnJva2VyLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/worker-timers/build/es2019/factories/load-or-return-broker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/worker-timers/build/es2019/module.js":
/*!***********************************************************!*\
  !*** ./node_modules/worker-timers/build/es2019/module.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearInterval: () => (/* binding */ clearInterval),\n/* harmony export */   clearTimeout: () => (/* binding */ clearTimeout),\n/* harmony export */   setInterval: () => (/* binding */ setInterval),\n/* harmony export */   setTimeout: () => (/* binding */ setTimeout)\n/* harmony export */ });\n/* harmony import */ var worker_timers_broker__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! worker-timers-broker */ \"(ssr)/./node_modules/worker-timers-broker/build/es2019/module.js\");\n/* harmony import */ var _factories_load_or_return_broker__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./factories/load-or-return-broker */ \"(ssr)/./node_modules/worker-timers/build/es2019/factories/load-or-return-broker.js\");\n/* harmony import */ var _worker_worker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./worker/worker */ \"(ssr)/./node_modules/worker-timers/build/es2019/worker/worker.js\");\n\n\n\nconst loadOrReturnBroker = (0,_factories_load_or_return_broker__WEBPACK_IMPORTED_MODULE_1__.createLoadOrReturnBroker)(worker_timers_broker__WEBPACK_IMPORTED_MODULE_0__.load, _worker_worker__WEBPACK_IMPORTED_MODULE_2__.worker);\nconst clearInterval = (timerId) => loadOrReturnBroker().clearInterval(timerId);\nconst clearTimeout = (timerId) => loadOrReturnBroker().clearTimeout(timerId);\nconst setInterval = (...args) => loadOrReturnBroker().setInterval(...args);\nconst setTimeout = (...args) => loadOrReturnBroker().setTimeout(...args);\n//# sourceMappingURL=module.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd29ya2VyLXRpbWVycy9idWlsZC9lczIwMTkvbW9kdWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNEM7QUFDaUM7QUFDcEM7QUFDekMsMkJBQTJCLDBGQUF3QixDQUFDLHNEQUFJLEVBQUUsa0RBQU07QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDUCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3dvcmtlci10aW1lcnMvYnVpbGQvZXMyMDE5L21vZHVsZS5qcz9jY2M3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGxvYWQgfSBmcm9tICd3b3JrZXItdGltZXJzLWJyb2tlcic7XG5pbXBvcnQgeyBjcmVhdGVMb2FkT3JSZXR1cm5Ccm9rZXIgfSBmcm9tICcuL2ZhY3Rvcmllcy9sb2FkLW9yLXJldHVybi1icm9rZXInO1xuaW1wb3J0IHsgd29ya2VyIH0gZnJvbSAnLi93b3JrZXIvd29ya2VyJztcbmNvbnN0IGxvYWRPclJldHVybkJyb2tlciA9IGNyZWF0ZUxvYWRPclJldHVybkJyb2tlcihsb2FkLCB3b3JrZXIpO1xuZXhwb3J0IGNvbnN0IGNsZWFySW50ZXJ2YWwgPSAodGltZXJJZCkgPT4gbG9hZE9yUmV0dXJuQnJva2VyKCkuY2xlYXJJbnRlcnZhbCh0aW1lcklkKTtcbmV4cG9ydCBjb25zdCBjbGVhclRpbWVvdXQgPSAodGltZXJJZCkgPT4gbG9hZE9yUmV0dXJuQnJva2VyKCkuY2xlYXJUaW1lb3V0KHRpbWVySWQpO1xuZXhwb3J0IGNvbnN0IHNldEludGVydmFsID0gKC4uLmFyZ3MpID0+IGxvYWRPclJldHVybkJyb2tlcigpLnNldEludGVydmFsKC4uLmFyZ3MpO1xuZXhwb3J0IGNvbnN0IHNldFRpbWVvdXQgPSAoLi4uYXJncykgPT4gbG9hZE9yUmV0dXJuQnJva2VyKCkuc2V0VGltZW91dCguLi5hcmdzKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1vZHVsZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/worker-timers/build/es2019/module.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/worker-timers/build/es2019/worker/worker.js":
/*!******************************************************************!*\
  !*** ./node_modules/worker-timers/build/es2019/worker/worker.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   worker: () => (/* binding */ worker)\n/* harmony export */ });\n// This is the minified and stringified code of the worker-timers-worker package.\nconst worker = `(()=>{var e={455:function(e,t){!function(e){\"use strict\";var t=function(e){return function(t){var r=e(t);return t.add(r),r}},r=function(e){return function(t,r){return e.set(t,r),r}},n=void 0===Number.MAX_SAFE_INTEGER?9007199254740991:Number.MAX_SAFE_INTEGER,o=536870912,s=2*o,a=function(e,t){return function(r){var a=t.get(r),i=void 0===a?r.size:a<s?a+1:0;if(!r.has(i))return e(r,i);if(r.size<o){for(;r.has(i);)i=Math.floor(Math.random()*s);return e(r,i)}if(r.size>n)throw new Error(\"Congratulations, you created a collection of unique numbers which uses all available integers!\");for(;r.has(i);)i=Math.floor(Math.random()*n);return e(r,i)}},i=new WeakMap,u=r(i),c=a(u,i),l=t(c);e.addUniqueNumber=l,e.generateUniqueNumber=c}(t)}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n].call(s.exports,s,s.exports,r),s.exports}(()=>{\"use strict\";const e=-32603,t=-32602,n=-32601,o=(e,t)=>Object.assign(new Error(e),{status:t}),s=t=>o('The handler of the method called \"'.concat(t,'\" returned an unexpected result.'),e),a=(t,r)=>async({data:{id:a,method:i,params:u}})=>{const c=r[i];try{if(void 0===c)throw(e=>o('The requested method called \"'.concat(e,'\" is not supported.'),n))(i);const r=void 0===u?c():c(u);if(void 0===r)throw(t=>o('The handler of the method called \"'.concat(t,'\" returned no required result.'),e))(i);const l=r instanceof Promise?await r:r;if(null===a){if(void 0!==l.result)throw s(i)}else{if(void 0===l.result)throw s(i);const{result:e,transferables:r=[]}=l;t.postMessage({id:a,result:e},r)}}catch(e){const{message:r,status:n=-32603}=e;t.postMessage({error:{code:n,message:r},id:a})}};var i=r(455);const u=new Map,c=(e,r,n)=>({...r,connect:({port:t})=>{t.start();const n=e(t,r),o=(0,i.generateUniqueNumber)(u);return u.set(o,()=>{n(),t.close(),u.delete(o)}),{result:o}},disconnect:({portId:e})=>{const r=u.get(e);if(void 0===r)throw(e=>o('The specified parameter called \"portId\" with the given value \"'.concat(e,'\" does not identify a port connected to this worker.'),t))(e);return r(),{result:null}},isSupported:async()=>{if(await new Promise(e=>{const t=new ArrayBuffer(0),{port1:r,port2:n}=new MessageChannel;r.onmessage=({data:t})=>e(null!==t),n.postMessage(t,[t])})){const e=n();return{result:e instanceof Promise?await e:e}}return{result:!1}}}),l=(e,t,r=()=>!0)=>{const n=c(l,t,r),o=a(e,n);return e.addEventListener(\"message\",o),()=>e.removeEventListener(\"message\",o)},d=(e,t)=>r=>{const n=t.get(r);if(void 0===n)return Promise.resolve(!1);const[o,s]=n;return e(o),t.delete(r),s(!1),Promise.resolve(!0)},f=(e,t,r,n)=>(o,s,a)=>{const i=o+s-t.timeOrigin,u=i-t.now();return new Promise(t=>{e.set(a,[r(n,u,i,e,t,a),t])})},m=new Map,h=d(globalThis.clearTimeout,m),p=new Map,v=d(globalThis.clearTimeout,p),w=((e,t)=>{const r=(n,o,s,a)=>{const i=n-e.now();i>0?o.set(a,[t(r,i,n,o,s,a),s]):(o.delete(a),s(!0))};return r})(performance,globalThis.setTimeout),g=f(m,performance,globalThis.setTimeout,w),T=f(p,performance,globalThis.setTimeout,w);l(self,{clear:async({timerId:e,timerType:t})=>({result:await(\"interval\"===t?h(e):v(e))}),set:async({delay:e,now:t,timerId:r,timerType:n})=>({result:await(\"interval\"===n?g:T)(e,t,r)})})})()})();`; // tslint:disable-line:max-line-length\n//# sourceMappingURL=worker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/worker-timers/build/es2019/worker/worker.js\n");

/***/ })

};
;