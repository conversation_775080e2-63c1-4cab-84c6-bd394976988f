"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-unique-numbers";
exports.ids = ["vendor-chunks/fast-unique-numbers"];
exports.modules = {

/***/ "(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/add-unique-number.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/fast-unique-numbers/build/es2019/factories/add-unique-number.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAddUniqueNumber: () => (/* binding */ createAddUniqueNumber)\n/* harmony export */ });\nconst createAddUniqueNumber = (generateUniqueNumber) => {\n    return (set) => {\n        const number = generateUniqueNumber(set);\n        set.add(number);\n        return number;\n    };\n};\n//# sourceMappingURL=add-unique-number.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC11bmlxdWUtbnVtYmVycy9idWlsZC9lczIwMTkvZmFjdG9yaWVzL2FkZC11bmlxdWUtbnVtYmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvZmFzdC11bmlxdWUtbnVtYmVycy9idWlsZC9lczIwMTkvZmFjdG9yaWVzL2FkZC11bmlxdWUtbnVtYmVyLmpzPzQ3NGUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGNyZWF0ZUFkZFVuaXF1ZU51bWJlciA9IChnZW5lcmF0ZVVuaXF1ZU51bWJlcikgPT4ge1xuICAgIHJldHVybiAoc2V0KSA9PiB7XG4gICAgICAgIGNvbnN0IG51bWJlciA9IGdlbmVyYXRlVW5pcXVlTnVtYmVyKHNldCk7XG4gICAgICAgIHNldC5hZGQobnVtYmVyKTtcbiAgICAgICAgcmV0dXJuIG51bWJlcjtcbiAgICB9O1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFkZC11bmlxdWUtbnVtYmVyLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/add-unique-number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/cache.js":
/*!**************************************************************************!*\
  !*** ./node_modules/fast-unique-numbers/build/es2019/factories/cache.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCache: () => (/* binding */ createCache)\n/* harmony export */ });\nconst createCache = (lastNumberWeakMap) => {\n    return (collection, nextNumber) => {\n        lastNumberWeakMap.set(collection, nextNumber);\n        return nextNumber;\n    };\n};\n//# sourceMappingURL=cache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC11bmlxdWUtbnVtYmVycy9idWlsZC9lczIwMTkvZmFjdG9yaWVzL2NhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2Zhc3QtdW5pcXVlLW51bWJlcnMvYnVpbGQvZXMyMDE5L2ZhY3Rvcmllcy9jYWNoZS5qcz9jNWU3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBjcmVhdGVDYWNoZSA9IChsYXN0TnVtYmVyV2Vha01hcCkgPT4ge1xuICAgIHJldHVybiAoY29sbGVjdGlvbiwgbmV4dE51bWJlcikgPT4ge1xuICAgICAgICBsYXN0TnVtYmVyV2Vha01hcC5zZXQoY29sbGVjdGlvbiwgbmV4dE51bWJlcik7XG4gICAgICAgIHJldHVybiBuZXh0TnVtYmVyO1xuICAgIH07XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2FjaGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/cache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/generate-unique-number.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/fast-unique-numbers/build/es2019/factories/generate-unique-number.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createGenerateUniqueNumber: () => (/* binding */ createGenerateUniqueNumber)\n/* harmony export */ });\n/*\n * The value of the constant Number.MAX_SAFE_INTEGER equals (2 ** 53 - 1) but it\n * is fairly new.\n */\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER === undefined ? 9007199254740991 : Number.MAX_SAFE_INTEGER;\nconst TWO_TO_THE_POWER_OF_TWENTY_NINE = 536870912;\nconst TWO_TO_THE_POWER_OF_THIRTY = TWO_TO_THE_POWER_OF_TWENTY_NINE * 2;\nconst createGenerateUniqueNumber = (cache, lastNumberWeakMap) => {\n    return (collection) => {\n        const lastNumber = lastNumberWeakMap.get(collection);\n        /*\n         * Let's try the cheapest algorithm first. It might fail to produce a new\n         * number, but it is so cheap that it is okay to take the risk. Just\n         * increase the last number by one or reset it to 0 if we reached the upper\n         * bound of SMIs (which stands for small integers). When the last number is\n         * unknown it is assumed that the collection contains zero based consecutive\n         * numbers.\n         */\n        let nextNumber = lastNumber === undefined ? collection.size : lastNumber < TWO_TO_THE_POWER_OF_THIRTY ? lastNumber + 1 : 0;\n        if (!collection.has(nextNumber)) {\n            return cache(collection, nextNumber);\n        }\n        /*\n         * If there are less than half of 2 ** 30 numbers stored in the collection,\n         * the chance to generate a new random number in the range from 0 to 2 ** 30\n         * is at least 50%. It's benifitial to use only SMIs because they perform\n         * much better in any environment based on V8.\n         */\n        if (collection.size < TWO_TO_THE_POWER_OF_TWENTY_NINE) {\n            while (collection.has(nextNumber)) {\n                nextNumber = Math.floor(Math.random() * TWO_TO_THE_POWER_OF_THIRTY);\n            }\n            return cache(collection, nextNumber);\n        }\n        // Quickly check if there is a theoretical chance to generate a new number.\n        if (collection.size > MAX_SAFE_INTEGER) {\n            throw new Error('Congratulations, you created a collection of unique numbers which uses all available integers!');\n        }\n        // Otherwise use the full scale of safely usable integers.\n        while (collection.has(nextNumber)) {\n            nextNumber = Math.floor(Math.random() * MAX_SAFE_INTEGER);\n        }\n        return cache(collection, nextNumber);\n    };\n};\n//# sourceMappingURL=generate-unique-number.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/generate-unique-number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-unique-numbers/build/es2019/module.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fast-unique-numbers/build/es2019/module.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addUniqueNumber: () => (/* binding */ addUniqueNumber),\n/* harmony export */   generateUniqueNumber: () => (/* binding */ generateUniqueNumber)\n/* harmony export */ });\n/* harmony import */ var _factories_add_unique_number__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./factories/add-unique-number */ \"(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/add-unique-number.js\");\n/* harmony import */ var _factories_cache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./factories/cache */ \"(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/cache.js\");\n/* harmony import */ var _factories_generate_unique_number__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./factories/generate-unique-number */ \"(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/generate-unique-number.js\");\n\n\n\nconst LAST_NUMBER_WEAK_MAP = new WeakMap();\nconst cache = (0,_factories_cache__WEBPACK_IMPORTED_MODULE_1__.createCache)(LAST_NUMBER_WEAK_MAP);\nconst generateUniqueNumber = (0,_factories_generate_unique_number__WEBPACK_IMPORTED_MODULE_2__.createGenerateUniqueNumber)(cache, LAST_NUMBER_WEAK_MAP);\nconst addUniqueNumber = (0,_factories_add_unique_number__WEBPACK_IMPORTED_MODULE_0__.createAddUniqueNumber)(generateUniqueNumber);\n\n//# sourceMappingURL=module.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC11bmlxdWUtbnVtYmVycy9idWlsZC9lczIwMTkvbW9kdWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXNFO0FBQ3RCO0FBQ2dDO0FBQ2hGO0FBQ0EsY0FBYyw2REFBVztBQUN6Qiw2QkFBNkIsNkZBQTBCO0FBQ3ZELHdCQUF3QixtRkFBcUI7QUFDSTtBQUNqRCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2Zhc3QtdW5pcXVlLW51bWJlcnMvYnVpbGQvZXMyMDE5L21vZHVsZS5qcz83NDQ4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUFkZFVuaXF1ZU51bWJlciB9IGZyb20gJy4vZmFjdG9yaWVzL2FkZC11bmlxdWUtbnVtYmVyJztcbmltcG9ydCB7IGNyZWF0ZUNhY2hlIH0gZnJvbSAnLi9mYWN0b3JpZXMvY2FjaGUnO1xuaW1wb3J0IHsgY3JlYXRlR2VuZXJhdGVVbmlxdWVOdW1iZXIgfSBmcm9tICcuL2ZhY3Rvcmllcy9nZW5lcmF0ZS11bmlxdWUtbnVtYmVyJztcbmNvbnN0IExBU1RfTlVNQkVSX1dFQUtfTUFQID0gbmV3IFdlYWtNYXAoKTtcbmNvbnN0IGNhY2hlID0gY3JlYXRlQ2FjaGUoTEFTVF9OVU1CRVJfV0VBS19NQVApO1xuY29uc3QgZ2VuZXJhdGVVbmlxdWVOdW1iZXIgPSBjcmVhdGVHZW5lcmF0ZVVuaXF1ZU51bWJlcihjYWNoZSwgTEFTVF9OVU1CRVJfV0VBS19NQVApO1xuY29uc3QgYWRkVW5pcXVlTnVtYmVyID0gY3JlYXRlQWRkVW5pcXVlTnVtYmVyKGdlbmVyYXRlVW5pcXVlTnVtYmVyKTtcbmV4cG9ydCB7IGFkZFVuaXF1ZU51bWJlciwgZ2VuZXJhdGVVbmlxdWVOdW1iZXIgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1vZHVsZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-unique-numbers/build/es2019/module.js\n");

/***/ })

};
;