"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_react-pdf_dist_esm_index_js"],{

/***/ "(app-pages-browser)/./node_modules/warning/warning.js":
/*!*****************************************!*\
  !*** ./node_modules/warning/warning.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\n/**\n * Similar to invariant but only logs a warning if the condition is not met.\n * This can be used to log issues in development environments in critical\n * paths. Removing the logging code for production environments will keep the\n * same logic and follow the same code paths.\n */\n\nvar __DEV__ = \"development\" !== 'production';\n\nvar warning = function() {};\n\nif (__DEV__) {\n  var printWarning = function printWarning(format, args) {\n    var len = arguments.length;\n    args = new Array(len > 1 ? len - 1 : 0);\n    for (var key = 1; key < len; key++) {\n      args[key - 1] = arguments[key];\n    }\n    var argIndex = 0;\n    var message = 'Warning: ' +\n      format.replace(/%s/g, function() {\n        return args[argIndex++];\n      });\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  }\n\n  warning = function(condition, format, args) {\n    var len = arguments.length;\n    args = new Array(len > 2 ? len - 2 : 0);\n    for (var key = 2; key < len; key++) {\n      args[key - 2] = arguments[key];\n    }\n    if (format === undefined) {\n      throw new Error(\n          '`warning(condition, format, ...args)` requires a warning ' +\n          'message argument'\n      );\n    }\n    if (!condition) {\n      printWarning.apply(null, [format].concat(args));\n    }\n  };\n}\n\nmodule.exports = warning;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/warning/warning.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs":
/*!*****************************************!*\
  !*** ./node_modules/clsx/dist/clsx.mjs ***!
  \*****************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: function() { return /* binding */ clsx; }\n/* harmony export */ });\nfunction r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}/* harmony default export */ __webpack_exports__[\"default\"] = (clsx);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9jbHN4L2Rpc3QvY2xzeC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGNBQWMsYUFBYSwrQ0FBK0MsZ0RBQWdELGVBQWUsUUFBUSxJQUFJLDBDQUEwQyx5Q0FBeUMsU0FBZ0IsZ0JBQWdCLHdDQUF3QyxJQUFJLG1EQUFtRCxTQUFTLCtEQUFlLElBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2Nsc3gvZGlzdC9jbHN4Lm1qcz9mZTRhIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHIoZSl7dmFyIHQsZixuPVwiXCI7aWYoXCJzdHJpbmdcIj09dHlwZW9mIGV8fFwibnVtYmVyXCI9PXR5cGVvZiBlKW4rPWU7ZWxzZSBpZihcIm9iamVjdFwiPT10eXBlb2YgZSlpZihBcnJheS5pc0FycmF5KGUpKXt2YXIgbz1lLmxlbmd0aDtmb3IodD0wO3Q8bzt0KyspZVt0XSYmKGY9cihlW3RdKSkmJihuJiYobis9XCIgXCIpLG4rPWYpfWVsc2UgZm9yKGYgaW4gZSllW2ZdJiYobiYmKG4rPVwiIFwiKSxuKz1mKTtyZXR1cm4gbn1leHBvcnQgZnVuY3Rpb24gY2xzeCgpe2Zvcih2YXIgZSx0LGY9MCxuPVwiXCIsbz1hcmd1bWVudHMubGVuZ3RoO2Y8bztmKyspKGU9YXJndW1lbnRzW2ZdKSYmKHQ9cihlKSkmJihuJiYobis9XCIgXCIpLG4rPXQpO3JldHVybiBufWV4cG9ydCBkZWZhdWx0IGNsc3g7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/make-cancellable-promise/dist/esm/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/make-cancellable-promise/dist/esm/index.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ makeCancellablePromise; }\n/* harmony export */ });\nfunction makeCancellablePromise(promise) {\n    var isCancelled = false;\n    var wrappedPromise = new Promise(function (resolve, reject) {\n        promise\n            .then(function (value) { return !isCancelled && resolve(value); })\n            .catch(function (error) { return !isCancelled && reject(error); });\n    });\n    return {\n        promise: wrappedPromise,\n        cancel: function () {\n            isCancelled = true;\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tYWtlLWNhbmNlbGxhYmxlLXByb21pc2UvZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLHdDQUF3QztBQUM3RSxzQ0FBc0MsdUNBQXVDO0FBQzdFLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tYWtlLWNhbmNlbGxhYmxlLXByb21pc2UvZGlzdC9lc20vaW5kZXguanM/MTBiNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtYWtlQ2FuY2VsbGFibGVQcm9taXNlKHByb21pc2UpIHtcbiAgICB2YXIgaXNDYW5jZWxsZWQgPSBmYWxzZTtcbiAgICB2YXIgd3JhcHBlZFByb21pc2UgPSBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7XG4gICAgICAgIHByb21pc2VcbiAgICAgICAgICAgIC50aGVuKGZ1bmN0aW9uICh2YWx1ZSkgeyByZXR1cm4gIWlzQ2FuY2VsbGVkICYmIHJlc29sdmUodmFsdWUpOyB9KVxuICAgICAgICAgICAgLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgeyByZXR1cm4gIWlzQ2FuY2VsbGVkICYmIHJlamVjdChlcnJvcik7IH0pO1xuICAgIH0pO1xuICAgIHJldHVybiB7XG4gICAgICAgIHByb21pc2U6IHdyYXBwZWRQcm9taXNlLFxuICAgICAgICBjYW5jZWw6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIGlzQ2FuY2VsbGVkID0gdHJ1ZTtcbiAgICAgICAgfSxcbiAgICB9O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/make-cancellable-promise/dist/esm/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/make-event-props/dist/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/make-event-props/dist/esm/index.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allEvents: function() { return /* binding */ allEvents; },\n/* harmony export */   animationEvents: function() { return /* binding */ animationEvents; },\n/* harmony export */   changeEvents: function() { return /* binding */ changeEvents; },\n/* harmony export */   clipboardEvents: function() { return /* binding */ clipboardEvents; },\n/* harmony export */   compositionEvents: function() { return /* binding */ compositionEvents; },\n/* harmony export */   \"default\": function() { return /* binding */ makeEventProps; },\n/* harmony export */   dragEvents: function() { return /* binding */ dragEvents; },\n/* harmony export */   focusEvents: function() { return /* binding */ focusEvents; },\n/* harmony export */   formEvents: function() { return /* binding */ formEvents; },\n/* harmony export */   imageEvents: function() { return /* binding */ imageEvents; },\n/* harmony export */   keyboardEvents: function() { return /* binding */ keyboardEvents; },\n/* harmony export */   mediaEvents: function() { return /* binding */ mediaEvents; },\n/* harmony export */   mouseEvents: function() { return /* binding */ mouseEvents; },\n/* harmony export */   otherEvents: function() { return /* binding */ otherEvents; },\n/* harmony export */   pointerEvents: function() { return /* binding */ pointerEvents; },\n/* harmony export */   selectionEvents: function() { return /* binding */ selectionEvents; },\n/* harmony export */   touchEvents: function() { return /* binding */ touchEvents; },\n/* harmony export */   transitionEvents: function() { return /* binding */ transitionEvents; },\n/* harmony export */   uiEvents: function() { return /* binding */ uiEvents; },\n/* harmony export */   wheelEvents: function() { return /* binding */ wheelEvents; }\n/* harmony export */ });\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n// As defined on the list of supported events: https://reactjs.org/docs/events.html\nvar clipboardEvents = ['onCopy', 'onCut', 'onPaste'];\nvar compositionEvents = [\n    'onCompositionEnd',\n    'onCompositionStart',\n    'onCompositionUpdate',\n];\nvar focusEvents = ['onFocus', 'onBlur'];\nvar formEvents = ['onInput', 'onInvalid', 'onReset', 'onSubmit'];\nvar imageEvents = ['onLoad', 'onError'];\nvar keyboardEvents = ['onKeyDown', 'onKeyPress', 'onKeyUp'];\nvar mediaEvents = [\n    'onAbort',\n    'onCanPlay',\n    'onCanPlayThrough',\n    'onDurationChange',\n    'onEmptied',\n    'onEncrypted',\n    'onEnded',\n    'onError',\n    'onLoadedData',\n    'onLoadedMetadata',\n    'onLoadStart',\n    'onPause',\n    'onPlay',\n    'onPlaying',\n    'onProgress',\n    'onRateChange',\n    'onSeeked',\n    'onSeeking',\n    'onStalled',\n    'onSuspend',\n    'onTimeUpdate',\n    'onVolumeChange',\n    'onWaiting',\n];\nvar mouseEvents = [\n    'onClick',\n    'onContextMenu',\n    'onDoubleClick',\n    'onMouseDown',\n    'onMouseEnter',\n    'onMouseLeave',\n    'onMouseMove',\n    'onMouseOut',\n    'onMouseOver',\n    'onMouseUp',\n];\nvar dragEvents = [\n    'onDrag',\n    'onDragEnd',\n    'onDragEnter',\n    'onDragExit',\n    'onDragLeave',\n    'onDragOver',\n    'onDragStart',\n    'onDrop',\n];\nvar selectionEvents = ['onSelect'];\nvar touchEvents = ['onTouchCancel', 'onTouchEnd', 'onTouchMove', 'onTouchStart'];\nvar pointerEvents = [\n    'onPointerDown',\n    'onPointerMove',\n    'onPointerUp',\n    'onPointerCancel',\n    'onGotPointerCapture',\n    'onLostPointerCapture',\n    'onPointerEnter',\n    'onPointerLeave',\n    'onPointerOver',\n    'onPointerOut',\n];\nvar uiEvents = ['onScroll'];\nvar wheelEvents = ['onWheel'];\nvar animationEvents = [\n    'onAnimationStart',\n    'onAnimationEnd',\n    'onAnimationIteration',\n];\nvar transitionEvents = ['onTransitionEnd'];\nvar otherEvents = ['onToggle'];\nvar changeEvents = ['onChange'];\nvar allEvents = __spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray([], clipboardEvents, true), compositionEvents, true), focusEvents, true), formEvents, true), imageEvents, true), keyboardEvents, true), mediaEvents, true), mouseEvents, true), dragEvents, true), selectionEvents, true), touchEvents, true), pointerEvents, true), uiEvents, true), wheelEvents, true), animationEvents, true), transitionEvents, true), changeEvents, true), otherEvents, true);\n/**\n * Returns an object with on-event callback props curried with provided args.\n * @param {Object} props Props passed to a component.\n * @param {Function=} getArgs A function that returns argument(s) on-event callbacks\n *   shall be curried with.\n */\nfunction makeEventProps(props, getArgs) {\n    var eventProps = {};\n    allEvents.forEach(function (eventName) {\n        var eventHandler = props[eventName];\n        if (!eventHandler) {\n            return;\n        }\n        if (getArgs) {\n            eventProps[eventName] = (function (event) {\n                return eventHandler(event, getArgs(eventName));\n            });\n        }\n        else {\n            eventProps[eventName] = eventHandler;\n        }\n    });\n    return eventProps;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/make-event-props/dist/esm/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/merge-refs/dist/esm/index.js":
/*!***************************************************!*\
  !*** ./node_modules/merge-refs/dist/esm/index.js ***!
  \***************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ mergeRefs; }\n/* harmony export */ });\n/**\n * A function that merges React refs into one.\n * Supports both functions and ref objects created using createRef() and useRef().\n *\n * Usage:\n * ```tsx\n * <div ref={mergeRefs(ref1, ref2, ref3)} />\n * ```\n *\n * @param {(React.Ref<T> | undefined)[]} inputRefs Array of refs\n * @returns {React.Ref<T> | React.RefCallback<T>} Merged refs\n */\nfunction mergeRefs() {\n    var inputRefs = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        inputRefs[_i] = arguments[_i];\n    }\n    var filteredInputRefs = inputRefs.filter(Boolean);\n    if (filteredInputRefs.length <= 1) {\n        var firstRef = filteredInputRefs[0];\n        return firstRef || null;\n    }\n    return function mergedRefs(ref) {\n        filteredInputRefs.forEach(function (inputRef) {\n            if (typeof inputRef === 'function') {\n                inputRef(ref);\n            }\n            else if (inputRef) {\n                inputRef.current = ref;\n            }\n        });\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZXJnZS1yZWZzL2Rpc3QvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQztBQUNBO0FBQ0EsV0FBVyw4QkFBOEI7QUFDekMsYUFBYSxxQ0FBcUM7QUFDbEQ7QUFDZTtBQUNmO0FBQ0EscUJBQXFCLHVCQUF1QjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbWVyZ2UtcmVmcy9kaXN0L2VzbS9pbmRleC5qcz8yM2NlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQSBmdW5jdGlvbiB0aGF0IG1lcmdlcyBSZWFjdCByZWZzIGludG8gb25lLlxuICogU3VwcG9ydHMgYm90aCBmdW5jdGlvbnMgYW5kIHJlZiBvYmplY3RzIGNyZWF0ZWQgdXNpbmcgY3JlYXRlUmVmKCkgYW5kIHVzZVJlZigpLlxuICpcbiAqIFVzYWdlOlxuICogYGBgdHN4XG4gKiA8ZGl2IHJlZj17bWVyZ2VSZWZzKHJlZjEsIHJlZjIsIHJlZjMpfSAvPlxuICogYGBgXG4gKlxuICogQHBhcmFtIHsoUmVhY3QuUmVmPFQ+IHwgdW5kZWZpbmVkKVtdfSBpbnB1dFJlZnMgQXJyYXkgb2YgcmVmc1xuICogQHJldHVybnMge1JlYWN0LlJlZjxUPiB8IFJlYWN0LlJlZkNhbGxiYWNrPFQ+fSBNZXJnZWQgcmVmc1xuICovXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtZXJnZVJlZnMoKSB7XG4gICAgdmFyIGlucHV0UmVmcyA9IFtdO1xuICAgIGZvciAodmFyIF9pID0gMDsgX2kgPCBhcmd1bWVudHMubGVuZ3RoOyBfaSsrKSB7XG4gICAgICAgIGlucHV0UmVmc1tfaV0gPSBhcmd1bWVudHNbX2ldO1xuICAgIH1cbiAgICB2YXIgZmlsdGVyZWRJbnB1dFJlZnMgPSBpbnB1dFJlZnMuZmlsdGVyKEJvb2xlYW4pO1xuICAgIGlmIChmaWx0ZXJlZElucHV0UmVmcy5sZW5ndGggPD0gMSkge1xuICAgICAgICB2YXIgZmlyc3RSZWYgPSBmaWx0ZXJlZElucHV0UmVmc1swXTtcbiAgICAgICAgcmV0dXJuIGZpcnN0UmVmIHx8IG51bGw7XG4gICAgfVxuICAgIHJldHVybiBmdW5jdGlvbiBtZXJnZWRSZWZzKHJlZikge1xuICAgICAgICBmaWx0ZXJlZElucHV0UmVmcy5mb3JFYWNoKGZ1bmN0aW9uIChpbnB1dFJlZikge1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBpbnB1dFJlZiA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgIGlucHV0UmVmKHJlZik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChpbnB1dFJlZikge1xuICAgICAgICAgICAgICAgIGlucHV0UmVmLmN1cnJlbnQgPSByZWY7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH07XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/merge-refs/dist/esm/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Document.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Document.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var make_event_props__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! make-event-props */ \"(app-pages-browser)/./node_modules/make-event-props/dist/esm/index.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! make-cancellable-promise */ \"(app-pages-browser)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(app-pages-browser)/./node_modules/warning/warning.js\");\n/* harmony import */ var dequal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dequal */ \"(app-pages-browser)/./node_modules/dequal/dist/index.mjs\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! pdfjs-dist */ \"(app-pages-browser)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _DocumentContext_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./DocumentContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/DocumentContext.js\");\n/* harmony import */ var _Message_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./Message.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Message.js\");\n/* harmony import */ var _LinkService_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./LinkService.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/LinkService.js\");\n/* harmony import */ var _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PasswordResponses.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/PasswordResponses.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./shared/utils.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst { PDFDataRangeTransport } = pdfjs_dist__WEBPACK_IMPORTED_MODULE_6__;\nconst defaultOnPassword = (callback, reason)=>{\n    switch(reason){\n        case _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"].NEED_PASSWORD:\n            {\n                const password = prompt(\"Enter the password to open this PDF file.\");\n                callback(password);\n                break;\n            }\n        case _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"].INCORRECT_PASSWORD:\n            {\n                const password = prompt(\"Invalid password. Please try again.\");\n                callback(password);\n                break;\n            }\n        default:\n    }\n};\nfunction isParameterObject(file) {\n    return typeof file === \"object\" && file !== null && (\"data\" in file || \"range\" in file || \"url\" in file);\n}\n/**\n * Loads a document passed using `file` prop.\n */ const Document = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s(function Document(_a, ref) {\n    _s();\n    var { children, className, error = \"Failed to load PDF file.\", externalLinkRel, externalLinkTarget, file, inputRef, imageResourcesPath, loading = \"Loading PDF…\", noData = \"No PDF file specified.\", onItemClick, onLoadError: onLoadErrorProps, onLoadProgress, onLoadSuccess: onLoadSuccessProps, onPassword = defaultOnPassword, onSourceError: onSourceErrorProps, onSourceSuccess: onSourceSuccessProps, options, renderMode, rotate } = _a, otherProps = __rest(_a, [\n        \"children\",\n        \"className\",\n        \"error\",\n        \"externalLinkRel\",\n        \"externalLinkTarget\",\n        \"file\",\n        \"inputRef\",\n        \"imageResourcesPath\",\n        \"loading\",\n        \"noData\",\n        \"onItemClick\",\n        \"onLoadError\",\n        \"onLoadProgress\",\n        \"onLoadSuccess\",\n        \"onPassword\",\n        \"onSourceError\",\n        \"onSourceSuccess\",\n        \"options\",\n        \"renderMode\",\n        \"rotate\"\n    ]);\n    const [sourceState, sourceDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { value: source, error: sourceError } = sourceState;\n    const [pdfState, pdfDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { value: pdf, error: pdfError } = pdfState;\n    const linkService = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new _LinkService_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]());\n    const pages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const prevFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(undefined);\n    const prevOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(undefined);\n    if (file && file !== prevFile.current && isParameterObject(file)) {\n        warning__WEBPACK_IMPORTED_MODULE_4__(!(0,dequal__WEBPACK_IMPORTED_MODULE_5__.dequal)(file, prevFile.current), 'File prop passed to <Document /> changed, but it\\'s equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to \"file\" prop.');\n        prevFile.current = file;\n    }\n    // Detect non-memoized changes in options prop\n    if (options && options !== prevOptions.current) {\n        warning__WEBPACK_IMPORTED_MODULE_4__(!(0,dequal__WEBPACK_IMPORTED_MODULE_5__.dequal)(options, prevOptions.current), 'Options prop passed to <Document /> changed, but it\\'s equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to \"options\" prop.');\n        prevOptions.current = options;\n    }\n    const viewer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        // Handling jumping to internal links target\n        scrollPageIntoView: (args)=>{\n            const { dest, pageNumber, pageIndex = pageNumber - 1 } = args;\n            // First, check if custom handling of onItemClick was provided\n            if (onItemClick) {\n                onItemClick({\n                    dest,\n                    pageIndex,\n                    pageNumber\n                });\n                return;\n            }\n            // If not, try to look for target page within the <Document>.\n            const page = pages.current[pageIndex];\n            if (page) {\n                // Scroll to the page automatically\n                page.scrollIntoView();\n                return;\n            }\n            warning__WEBPACK_IMPORTED_MODULE_4__(false, \"An internal link leading to page \".concat(pageNumber, \" was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>.\"));\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            linkService,\n            pages,\n            viewer\n        }), []);\n    /**\n     * Called when a document source is resolved correctly\n     */ function onSourceSuccess() {\n        if (onSourceSuccessProps) {\n            onSourceSuccessProps();\n        }\n    }\n    /**\n     * Called when a document source failed to be resolved correctly\n     */ function onSourceError() {\n        if (!sourceError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, sourceError.toString());\n        if (onSourceErrorProps) {\n            onSourceErrorProps(sourceError);\n        }\n    }\n    function resetSource() {\n        sourceDispatch({\n            type: \"RESET\"\n        });\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: See https://github.com/biomejs/biome/issues/3080\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(resetSource, [\n        file,\n        sourceDispatch\n    ]);\n    const findDocumentSource = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>__awaiter(this, void 0, void 0, function*() {\n            if (!file) {\n                return null;\n            }\n            // File is a string\n            if (typeof file === \"string\") {\n                if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isDataURI)(file)) {\n                    const fileByteString = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.dataURItoByteString)(file);\n                    return {\n                        data: fileByteString\n                    };\n                }\n                (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.displayCORSWarning)();\n                return {\n                    url: file\n                };\n            }\n            // File is PDFDataRangeTransport\n            if (file instanceof PDFDataRangeTransport) {\n                return {\n                    range: file\n                };\n            }\n            // File is an ArrayBuffer\n            if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isArrayBuffer)(file)) {\n                return {\n                    data: file\n                };\n            }\n            /**\n         * The cases below are browser-only.\n         * If you're running on a non-browser environment, these cases will be of no use.\n         */ if (_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isBrowser) {\n                // File is a Blob\n                if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isBlob)(file)) {\n                    const data = yield (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.loadFromFile)(file);\n                    return {\n                        data\n                    };\n                }\n            }\n            // At this point, file must be an object\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(typeof file === \"object\", \"Invalid parameter in file, need either Uint8Array, string or a parameter object\");\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(isParameterObject(file), \"Invalid parameter object: need either .data, .range or .url\");\n            // File .url is a string\n            if (\"url\" in file && typeof file.url === \"string\") {\n                if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isDataURI)(file.url)) {\n                    const { url } = file, otherParams = __rest(file, [\n                        \"url\"\n                    ]);\n                    const fileByteString = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.dataURItoByteString)(url);\n                    return Object.assign({\n                        data: fileByteString\n                    }, otherParams);\n                }\n                (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.displayCORSWarning)();\n            }\n            return file;\n        }), [\n        file\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(findDocumentSource());\n        cancellable.promise.then((nextSource)=>{\n            sourceDispatch({\n                type: \"RESOLVE\",\n                value: nextSource\n            });\n        }).catch((error)=>{\n            sourceDispatch({\n                type: \"REJECT\",\n                error\n            });\n        });\n        return ()=>{\n            (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.cancelRunningTask)(cancellable);\n        };\n    }, [\n        findDocumentSource,\n        sourceDispatch\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof source === \"undefined\") {\n            return;\n        }\n        if (source === false) {\n            onSourceError();\n            return;\n        }\n        onSourceSuccess();\n    }, [\n        source\n    ]);\n    /**\n     * Called when a document is read successfully\n     */ function onLoadSuccess() {\n        if (!pdf) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onLoadSuccessProps) {\n            onLoadSuccessProps(pdf);\n        }\n        pages.current = new Array(pdf.numPages);\n        linkService.current.setDocument(pdf);\n    }\n    /**\n     * Called when a document failed to read successfully\n     */ function onLoadError() {\n        if (!pdfError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, pdfError.toString());\n        if (onLoadErrorProps) {\n            onLoadErrorProps(pdfError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on source change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetDocument() {\n        pdfDispatch({\n            type: \"RESET\"\n        });\n    }, [\n        pdfDispatch,\n        source\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadDocument() {\n        if (!source) {\n            return;\n        }\n        const documentInitParams = options ? Object.assign(Object.assign({}, source), options) : source;\n        const destroyable = pdfjs_dist__WEBPACK_IMPORTED_MODULE_6__.getDocument(documentInitParams);\n        if (onLoadProgress) {\n            destroyable.onProgress = onLoadProgress;\n        }\n        if (onPassword) {\n            destroyable.onPassword = onPassword;\n        }\n        const loadingTask = destroyable;\n        const loadingPromise = loadingTask.promise.then((nextPdf)=>{\n            pdfDispatch({\n                type: \"RESOLVE\",\n                value: nextPdf\n            });\n        }).catch((error)=>{\n            if (loadingTask.destroyed) {\n                return;\n            }\n            pdfDispatch({\n                type: \"REJECT\",\n                error\n            });\n        });\n        return ()=>{\n            loadingPromise.finally(()=>loadingTask.destroy());\n        };\n    }, [\n        options,\n        pdfDispatch,\n        source\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof pdf === \"undefined\") {\n            return;\n        }\n        if (pdf === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [\n        pdf\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function setupLinkService() {\n        linkService.current.setViewer(viewer.current);\n        linkService.current.setExternalLinkRel(externalLinkRel);\n        linkService.current.setExternalLinkTarget(externalLinkTarget);\n    }, [\n        externalLinkRel,\n        externalLinkTarget\n    ]);\n    const registerPage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((pageIndex, ref)=>{\n        pages.current[pageIndex] = ref;\n    }, []);\n    const unregisterPage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((pageIndex)=>{\n        delete pages.current[pageIndex];\n    }, []);\n    const childContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            imageResourcesPath,\n            linkService: linkService.current,\n            onItemClick,\n            pdf,\n            registerPage,\n            renderMode,\n            rotate,\n            unregisterPage\n        }), [\n        imageResourcesPath,\n        onItemClick,\n        pdf,\n        registerPage,\n        renderMode,\n        rotate,\n        unregisterPage\n    ]);\n    const eventProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,make_event_props__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(otherProps, ()=>pdf), // biome-ignore lint/correctness/useExhaustiveDependencies: FIXME\n    [\n        otherProps,\n        pdf\n    ]);\n    function renderChildren() {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_DocumentContext_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"].Provider, {\n            value: childContext,\n            children: children\n        });\n    }\n    function renderContent() {\n        if (!file) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"no-data\",\n                children: typeof noData === \"function\" ? noData() : noData\n            });\n        }\n        if (pdf === undefined || pdf === null) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"loading\",\n                children: typeof loading === \"function\" ? loading() : loading\n            });\n        }\n        if (pdf === false) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"error\",\n                children: typeof error === \"function\" ? error() : error\n            });\n        }\n        return renderChildren();\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", Object.assign({\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\"react-pdf__Document\", className),\n        // Assertion is needed for React 18 compatibility\n        ref: inputRef,\n        style: {\n            [\"--scale-factor\"]: \"1\"\n        }\n    }, eventProps, {\n        children: renderContent()\n    }));\n}, \"iOH9mFORwmiAFx4uqtCRw/wWMiI=\", false, function() {\n    return [\n        _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    ];\n})), \"iOH9mFORwmiAFx4uqtCRw/wWMiI=\", false, function() {\n    return [\n        _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    ];\n});\n_c1 = Document;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Document);\nvar _c, _c1;\n$RefreshReg$(_c, \"Document$forwardRef\");\n$RefreshReg$(_c1, \"Document\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/Document.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/DocumentContext.js":
/*!************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/DocumentContext.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nconst documentContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n/* harmony default export */ __webpack_exports__[\"default\"] = (documentContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vRG9jdW1lbnRDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7OzZEQUNzQztBQUN0QyxNQUFNQyxnQ0FBa0JELG9EQUFhQSxDQUFDO0FBQ3RDLCtEQUFlQyxlQUFlQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vRG9jdW1lbnRDb250ZXh0LmpzPzEwMzAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuaW1wb3J0IHsgY3JlYXRlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcbmNvbnN0IGRvY3VtZW50Q29udGV4dCA9IGNyZWF0ZUNvbnRleHQobnVsbCk7XG5leHBvcnQgZGVmYXVsdCBkb2N1bWVudENvbnRleHQ7XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsImRvY3VtZW50Q29udGV4dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/DocumentContext.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/LinkService.js":
/*!********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/LinkService.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LinkService; }\n/* harmony export */ });\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* Copyright 2015 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst DEFAULT_LINK_REL = 'noopener noreferrer nofollow';\nclass LinkService {\n    constructor() {\n        this.externalLinkEnabled = true;\n        this.externalLinkRel = undefined;\n        this.externalLinkTarget = undefined;\n        this.isInPresentationMode = false;\n        this.pdfDocument = undefined;\n        this.pdfViewer = undefined;\n    }\n    setDocument(pdfDocument) {\n        this.pdfDocument = pdfDocument;\n    }\n    setViewer(pdfViewer) {\n        this.pdfViewer = pdfViewer;\n    }\n    setExternalLinkRel(externalLinkRel) {\n        this.externalLinkRel = externalLinkRel;\n    }\n    setExternalLinkTarget(externalLinkTarget) {\n        this.externalLinkTarget = externalLinkTarget;\n    }\n    setHistory() {\n        // Intentionally empty\n    }\n    get pagesCount() {\n        return this.pdfDocument ? this.pdfDocument.numPages : 0;\n    }\n    get page() {\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n        return this.pdfViewer.currentPageNumber || 0;\n    }\n    set page(value) {\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n        this.pdfViewer.currentPageNumber = value;\n    }\n    get rotation() {\n        return 0;\n    }\n    set rotation(_value) {\n        // Intentionally empty\n    }\n    goToDestination(dest) {\n        return new Promise((resolve) => {\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfDocument, 'PDF document not loaded.');\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(dest, 'Destination is not specified.');\n            if (typeof dest === 'string') {\n                this.pdfDocument.getDestination(dest).then(resolve);\n            }\n            else if (Array.isArray(dest)) {\n                resolve(dest);\n            }\n            else {\n                dest.then(resolve);\n            }\n        }).then((explicitDest) => {\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Array.isArray(explicitDest), `\"${explicitDest}\" is not a valid destination array.`);\n            const destRef = explicitDest[0];\n            new Promise((resolve) => {\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfDocument, 'PDF document not loaded.');\n                if (destRef instanceof Object) {\n                    this.pdfDocument\n                        .getPageIndex(destRef)\n                        .then((pageIndex) => {\n                        resolve(pageIndex);\n                    })\n                        .catch(() => {\n                        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(false, `\"${destRef}\" is not a valid page reference.`);\n                    });\n                }\n                else if (typeof destRef === 'number') {\n                    resolve(destRef);\n                }\n                else {\n                    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(false, `\"${destRef}\" is not a valid destination reference.`);\n                }\n            }).then((pageIndex) => {\n                const pageNumber = pageIndex + 1;\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(pageNumber >= 1 && pageNumber <= this.pagesCount, `\"${pageNumber}\" is not a valid page number.`);\n                this.pdfViewer.scrollPageIntoView({\n                    dest: explicitDest,\n                    pageIndex,\n                    pageNumber,\n                });\n            });\n        });\n    }\n    navigateTo(dest) {\n        this.goToDestination(dest);\n    }\n    goToPage(pageNumber) {\n        const pageIndex = pageNumber - 1;\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(pageNumber >= 1 && pageNumber <= this.pagesCount, `\"${pageNumber}\" is not a valid page number.`);\n        this.pdfViewer.scrollPageIntoView({\n            pageIndex,\n            pageNumber,\n        });\n    }\n    addLinkAttributes(link, url, newWindow) {\n        link.href = url;\n        link.rel = this.externalLinkRel || DEFAULT_LINK_REL;\n        link.target = newWindow ? '_blank' : this.externalLinkTarget || '';\n    }\n    getDestinationHash() {\n        return '#';\n    }\n    getAnchorUrl() {\n        return '#';\n    }\n    setHash() {\n        // Intentionally empty\n    }\n    executeNamedAction() {\n        // Intentionally empty\n    }\n    cachePageRef() {\n        // Intentionally empty\n    }\n    isPageVisible() {\n        return true;\n    }\n    isPageCached() {\n        return true;\n    }\n    executeSetOCGState() {\n        // Intentionally empty\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/LinkService.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Message.js":
/*!****************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Message.js ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Message; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n\nfunction Message({ children, type }) {\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: `react-pdf__message react-pdf__message--${type}`, children: children });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vTWVzc2FnZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDtBQUNqQyxtQkFBbUIsZ0JBQWdCO0FBQ2xELFdBQVcsc0RBQUksVUFBVSxxREFBcUQsS0FBSyx1QkFBdUI7QUFDMUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXBkZi9kaXN0L2VzbS9NZXNzYWdlLmpzPzhiNDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1lc3NhZ2UoeyBjaGlsZHJlbiwgdHlwZSB9KSB7XG4gICAgcmV0dXJuIF9qc3goXCJkaXZcIiwgeyBjbGFzc05hbWU6IGByZWFjdC1wZGZfX21lc3NhZ2UgcmVhY3QtcGRmX19tZXNzYWdlLS0ke3R5cGV9YCwgY2hpbGRyZW46IGNoaWxkcmVuIH0pO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/Message.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Outline.js":
/*!****************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Outline.js ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Outline; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! make-cancellable-promise */ \"(app-pages-browser)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var make_event_props__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! make-event-props */ \"(app-pages-browser)/./node_modules/make-event-props/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(app-pages-browser)/./node_modules/warning/warning.js\");\n/* harmony import */ var _OutlineContext_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./OutlineContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/OutlineContext.js\");\n/* harmony import */ var _OutlineItem_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./OutlineItem.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/OutlineItem.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./shared/utils.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* harmony import */ var _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./shared/hooks/useDocumentContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\nvar __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Displays an outline (table of contents).\n *\n * Should be placed inside `<Document />`. Alternatively, it can have `pdf` prop passed, which can be obtained from `<Document />`'s `onLoadSuccess` callback function.\n */ function Outline(props) {\n    _s();\n    const documentContext = (0,_shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const mergedProps = Object.assign(Object.assign({}, documentContext), props);\n    const { className, inputRef, onItemClick, onLoadError: onLoadErrorProps, onLoadSuccess: onLoadSuccessProps, pdf } = mergedProps, otherProps = __rest(mergedProps, [\n        \"className\",\n        \"inputRef\",\n        \"onItemClick\",\n        \"onLoadError\",\n        \"onLoadSuccess\",\n        \"pdf\"\n    ]);\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pdf, \"Attempted to load an outline, but no document was specified. Wrap <Outline /> in a <Document /> or pass explicit `pdf` prop.\");\n    const [outlineState, outlineDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const { value: outline, error: outlineError } = outlineState;\n    /**\n     * Called when an outline is read successfully\n     */ function onLoadSuccess() {\n        if (typeof outline === \"undefined\" || outline === false) {\n            return;\n        }\n        if (onLoadSuccessProps) {\n            onLoadSuccessProps(outline);\n        }\n    }\n    /**\n     * Called when an outline failed to read successfully\n     */ function onLoadError() {\n        if (!outlineError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, outlineError.toString());\n        if (onLoadErrorProps) {\n            onLoadErrorProps(outlineError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on pdf change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetOutline() {\n        outlineDispatch({\n            type: \"RESET\"\n        });\n    }, [\n        outlineDispatch,\n        pdf\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadOutline() {\n        if (!pdf) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(pdf.getOutline());\n        const runningTask = cancellable;\n        cancellable.promise.then((nextOutline)=>{\n            outlineDispatch({\n                type: \"RESOLVE\",\n                value: nextOutline\n            });\n        }).catch((error)=>{\n            outlineDispatch({\n                type: \"REJECT\",\n                error\n            });\n        });\n        return ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_8__.cancelRunningTask)(runningTask);\n    }, [\n        outlineDispatch,\n        pdf\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (outline === undefined) {\n            return;\n        }\n        if (outline === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [\n        outline\n    ]);\n    const childContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            onItemClick\n        }), [\n        onItemClick\n    ]);\n    const eventProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,make_event_props__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(otherProps, ()=>outline), // biome-ignore lint/correctness/useExhaustiveDependencies: FIXME\n    [\n        otherProps,\n        outline\n    ]);\n    if (!outline) {\n        return null;\n    }\n    function renderOutline() {\n        if (!outline) {\n            return null;\n        }\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"ul\", {\n            children: outline.map((item, itemIndex)=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_OutlineItem_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    item: item,\n                    pdf: pdf\n                }, typeof item.dest === \"string\" ? item.dest : itemIndex))\n        });\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", Object.assign({\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\"react-pdf__Outline\", className),\n        ref: inputRef\n    }, eventProps, {\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_OutlineContext_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Provider, {\n            value: childContext,\n            children: renderOutline()\n        })\n    }));\n}\n_s(Outline, \"xP0Yoc1JE/2W6qAZZO2uaqNe5Ko=\", false, function() {\n    return [\n        _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Outline;\nvar _c;\n$RefreshReg$(_c, \"Outline\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/Outline.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/OutlineContext.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/OutlineContext.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nconst outlineContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n/* harmony default export */ __webpack_exports__[\"default\"] = (outlineContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vT3V0bGluZUNvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7NkRBQ3NDO0FBQ3RDLE1BQU1DLCtCQUFpQkQsb0RBQWFBLENBQUM7QUFDckMsK0RBQWVDLGNBQWNBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXBkZi9kaXN0L2VzbS9PdXRsaW5lQ29udGV4dC5qcz8xYzA5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5jb25zdCBvdXRsaW5lQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQobnVsbCk7XG5leHBvcnQgZGVmYXVsdCBvdXRsaW5lQ29udGV4dDtcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0Iiwib3V0bGluZUNvbnRleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/OutlineContext.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/OutlineItem.js":
/*!********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/OutlineItem.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OutlineItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var _Ref_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Ref.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Ref.js\");\n/* harmony import */ var _shared_hooks_useCachedValue_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./shared/hooks/useCachedValue.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useCachedValue.js\");\n/* harmony import */ var _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/hooks/useDocumentContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\");\n/* harmony import */ var _shared_hooks_useOutlineContext_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./shared/hooks/useOutlineContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useOutlineContext.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\n\n\n\nfunction OutlineItem(props) {\n    const documentContext = (0,_shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const outlineContext = (0,_shared_hooks_useOutlineContext_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(outlineContext, 'Unable to find Outline context.');\n    const mergedProps = Object.assign(Object.assign(Object.assign({}, documentContext), outlineContext), props);\n    const { item, linkService, onItemClick, pdf } = mergedProps, otherProps = __rest(mergedProps, [\"item\", \"linkService\", \"onItemClick\", \"pdf\"]);\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(pdf, 'Attempted to load an outline, but no document was specified. Wrap <Outline /> in a <Document /> or pass explicit `pdf` prop.');\n    const getDestination = (0,_shared_hooks_useCachedValue_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(() => {\n        if (typeof item.dest === 'string') {\n            return pdf.getDestination(item.dest);\n        }\n        return item.dest;\n    });\n    const getPageIndex = (0,_shared_hooks_useCachedValue_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(() => __awaiter(this, void 0, void 0, function* () {\n        const destination = yield getDestination();\n        if (!destination) {\n            throw new Error('Destination not found.');\n        }\n        const [ref] = destination;\n        return pdf.getPageIndex(new _Ref_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"](ref));\n    }));\n    const getPageNumber = (0,_shared_hooks_useCachedValue_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(() => __awaiter(this, void 0, void 0, function* () {\n        const pageIndex = yield getPageIndex();\n        return pageIndex + 1;\n    }));\n    function onClick(event) {\n        event.preventDefault();\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(onItemClick || linkService, 'Either onItemClick callback or linkService must be defined in order to navigate to an outline item.');\n        if (onItemClick) {\n            Promise.all([getDestination(), getPageIndex(), getPageNumber()]).then(([dest, pageIndex, pageNumber]) => {\n                onItemClick({\n                    dest,\n                    pageIndex,\n                    pageNumber,\n                });\n            });\n        }\n        else if (linkService) {\n            linkService.goToDestination(item.dest);\n        }\n    }\n    function renderSubitems() {\n        if (!item.items || !item.items.length) {\n            return null;\n        }\n        const { items: subitems } = item;\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"ul\", { children: subitems.map((subitem, subitemIndex) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(OutlineItem, Object.assign({ item: subitem, pdf: pdf }, otherProps), typeof subitem.dest === 'string' ? subitem.dest : subitemIndex))) }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"li\", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", { href: \"#\", onClick: onClick, children: item.title }), renderSubitems()] }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/OutlineItem.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page.js":
/*!*************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page.js ***!
  \*************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! make-cancellable-promise */ \"(app-pages-browser)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var make_event_props__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! make-event-props */ \"(app-pages-browser)/./node_modules/make-event-props/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var merge_refs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! merge-refs */ \"(app-pages-browser)/./node_modules/merge-refs/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(app-pages-browser)/./node_modules/warning/warning.js\");\n/* harmony import */ var _PageContext_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./PageContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/PageContext.js\");\n/* harmony import */ var _Message_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./Message.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Message.js\");\n/* harmony import */ var _Page_Canvas_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Page/Canvas.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/Canvas.js\");\n/* harmony import */ var _Page_TextLayer_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./Page/TextLayer.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js\");\n/* harmony import */ var _Page_AnnotationLayer_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Page/AnnotationLayer.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./shared/utils.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* harmony import */ var _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./shared/hooks/useDocumentContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\nvar __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst defaultScale = 1;\n/**\n * Displays a page.\n *\n * Should be placed inside `<Document />`. Alternatively, it can have `pdf` prop passed, which can be obtained from `<Document />`'s `onLoadSuccess` callback function, however some advanced functions like linking between pages inside a document may not be working correctly.\n */ function Page(props) {\n    _s();\n    const documentContext = (0,_shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const mergedProps = Object.assign(Object.assign({}, documentContext), props);\n    const { _className = \"react-pdf__Page\", _enableRegisterUnregisterPage = true, canvasBackground, canvasRef, children, className, customRenderer: CustomRenderer, customTextRenderer, devicePixelRatio, error = \"Failed to load the page.\", height, inputRef, loading = \"Loading page…\", noData = \"No page specified.\", onGetAnnotationsError: onGetAnnotationsErrorProps, onGetAnnotationsSuccess: onGetAnnotationsSuccessProps, onGetStructTreeError: onGetStructTreeErrorProps, onGetStructTreeSuccess: onGetStructTreeSuccessProps, onGetTextError: onGetTextErrorProps, onGetTextSuccess: onGetTextSuccessProps, onLoadError: onLoadErrorProps, onLoadSuccess: onLoadSuccessProps, onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps, onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps, onRenderError: onRenderErrorProps, onRenderSuccess: onRenderSuccessProps, onRenderTextLayerError: onRenderTextLayerErrorProps, onRenderTextLayerSuccess: onRenderTextLayerSuccessProps, pageIndex: pageIndexProps, pageNumber: pageNumberProps, pdf, registerPage, renderAnnotationLayer: renderAnnotationLayerProps = true, renderForms = false, renderMode = \"canvas\", renderTextLayer: renderTextLayerProps = true, rotate: rotateProps, scale: scaleProps = defaultScale, unregisterPage, width } = mergedProps, otherProps = __rest(mergedProps, [\n        \"_className\",\n        \"_enableRegisterUnregisterPage\",\n        \"canvasBackground\",\n        \"canvasRef\",\n        \"children\",\n        \"className\",\n        \"customRenderer\",\n        \"customTextRenderer\",\n        \"devicePixelRatio\",\n        \"error\",\n        \"height\",\n        \"inputRef\",\n        \"loading\",\n        \"noData\",\n        \"onGetAnnotationsError\",\n        \"onGetAnnotationsSuccess\",\n        \"onGetStructTreeError\",\n        \"onGetStructTreeSuccess\",\n        \"onGetTextError\",\n        \"onGetTextSuccess\",\n        \"onLoadError\",\n        \"onLoadSuccess\",\n        \"onRenderAnnotationLayerError\",\n        \"onRenderAnnotationLayerSuccess\",\n        \"onRenderError\",\n        \"onRenderSuccess\",\n        \"onRenderTextLayerError\",\n        \"onRenderTextLayerSuccess\",\n        \"pageIndex\",\n        \"pageNumber\",\n        \"pdf\",\n        \"registerPage\",\n        \"renderAnnotationLayer\",\n        \"renderForms\",\n        \"renderMode\",\n        \"renderTextLayer\",\n        \"rotate\",\n        \"scale\",\n        \"unregisterPage\",\n        \"width\"\n    ]);\n    const [pageState, pageDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const { value: page, error: pageError } = pageState;\n    const pageElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pdf, \"Attempted to load a page, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.\");\n    const pageIndex = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageNumberProps) ? pageNumberProps - 1 : pageIndexProps !== null && pageIndexProps !== void 0 ? pageIndexProps : null;\n    const pageNumber = pageNumberProps !== null && pageNumberProps !== void 0 ? pageNumberProps : (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageIndexProps) ? pageIndexProps + 1 : null;\n    const rotate = rotateProps !== null && rotateProps !== void 0 ? rotateProps : page ? page.rotate : null;\n    const scale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!page) {\n            return null;\n        }\n        // Be default, we'll render page at 100% * scale width.\n        let pageScale = 1;\n        // Passing scale explicitly null would cause the page not to render\n        const scaleWithDefault = scaleProps !== null && scaleProps !== void 0 ? scaleProps : defaultScale;\n        // If width/height is defined, calculate the scale of the page so it could be of desired width.\n        if (width || height) {\n            const viewport = page.getViewport({\n                scale: 1,\n                rotation: rotate\n            });\n            if (width) {\n                pageScale = width / viewport.width;\n            } else if (height) {\n                pageScale = height / viewport.height;\n            }\n        }\n        return scaleWithDefault * pageScale;\n    }, [\n        height,\n        page,\n        rotate,\n        scaleProps,\n        width\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on pdf change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function hook() {\n        return ()=>{\n            if (!(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageIndex)) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            if (_enableRegisterUnregisterPage && unregisterPage) {\n                unregisterPage(pageIndex);\n            }\n        };\n    }, [\n        _enableRegisterUnregisterPage,\n        pdf,\n        pageIndex,\n        unregisterPage\n    ]);\n    /**\n     * Called when a page is loaded successfully\n     */ function onLoadSuccess() {\n        if (onLoadSuccessProps) {\n            if (!page || !scale) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            onLoadSuccessProps((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.makePageCallback)(page, scale));\n        }\n        if (_enableRegisterUnregisterPage && registerPage) {\n            if (!(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageIndex) || !pageElement.current) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            registerPage(pageIndex, pageElement.current);\n        }\n    }\n    /**\n     * Called when a page failed to load\n     */ function onLoadError() {\n        if (!pageError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, pageError.toString());\n        if (onLoadErrorProps) {\n            onLoadErrorProps(pageError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on pdf and pageIndex change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetPage() {\n        pageDispatch({\n            type: \"RESET\"\n        });\n    }, [\n        pageDispatch,\n        pdf,\n        pageIndex\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadPage() {\n        if (!pdf || !pageNumber) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(pdf.getPage(pageNumber));\n        const runningTask = cancellable;\n        cancellable.promise.then((nextPage)=>{\n            pageDispatch({\n                type: \"RESOLVE\",\n                value: nextPage\n            });\n        }).catch((error)=>{\n            pageDispatch({\n                type: \"REJECT\",\n                error\n            });\n        });\n        return ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.cancelRunningTask)(runningTask);\n    }, [\n        pageDispatch,\n        pdf,\n        pageNumber\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (page === undefined) {\n            return;\n        }\n        if (page === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [\n        page,\n        scale\n    ]);\n    const childContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>// Technically there cannot be page without pageIndex, pageNumber, rotate and scale, but TypeScript doesn't know that\n        page && (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageIndex) && pageNumber && (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(rotate) && (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(scale) ? {\n            _className,\n            canvasBackground,\n            customTextRenderer,\n            devicePixelRatio,\n            onGetAnnotationsError: onGetAnnotationsErrorProps,\n            onGetAnnotationsSuccess: onGetAnnotationsSuccessProps,\n            onGetStructTreeError: onGetStructTreeErrorProps,\n            onGetStructTreeSuccess: onGetStructTreeSuccessProps,\n            onGetTextError: onGetTextErrorProps,\n            onGetTextSuccess: onGetTextSuccessProps,\n            onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps,\n            onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps,\n            onRenderError: onRenderErrorProps,\n            onRenderSuccess: onRenderSuccessProps,\n            onRenderTextLayerError: onRenderTextLayerErrorProps,\n            onRenderTextLayerSuccess: onRenderTextLayerSuccessProps,\n            page,\n            pageIndex,\n            pageNumber,\n            renderForms,\n            renderTextLayer: renderTextLayerProps,\n            rotate,\n            scale\n        } : null, [\n        _className,\n        canvasBackground,\n        customTextRenderer,\n        devicePixelRatio,\n        onGetAnnotationsErrorProps,\n        onGetAnnotationsSuccessProps,\n        onGetStructTreeErrorProps,\n        onGetStructTreeSuccessProps,\n        onGetTextErrorProps,\n        onGetTextSuccessProps,\n        onRenderAnnotationLayerErrorProps,\n        onRenderAnnotationLayerSuccessProps,\n        onRenderErrorProps,\n        onRenderSuccessProps,\n        onRenderTextLayerErrorProps,\n        onRenderTextLayerSuccessProps,\n        page,\n        pageIndex,\n        pageNumber,\n        renderForms,\n        renderTextLayerProps,\n        rotate,\n        scale\n    ]);\n    const eventProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,make_event_props__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(otherProps, ()=>page ? scale ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.makePageCallback)(page, scale) : undefined : page), // biome-ignore lint/correctness/useExhaustiveDependencies: FIXME\n    [\n        otherProps,\n        page,\n        scale\n    ]);\n    const pageKey = \"\".concat(pageIndex, \"@\").concat(scale, \"/\").concat(rotate);\n    function renderMainLayer() {\n        switch(renderMode){\n            case \"custom\":\n                {\n                    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(CustomRenderer, 'renderMode was set to \"custom\", but no customRenderer was passed.');\n                    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CustomRenderer, {}, \"\".concat(pageKey, \"_custom\"));\n                }\n            case \"none\":\n                return null;\n            case \"canvas\":\n            default:\n                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Page_Canvas_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    canvasRef: canvasRef\n                }, \"\".concat(pageKey, \"_canvas\"));\n        }\n    }\n    function renderTextLayer() {\n        if (!renderTextLayerProps) {\n            return null;\n        }\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Page_TextLayer_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, \"\".concat(pageKey, \"_text\"));\n    }\n    function renderAnnotationLayer() {\n        if (!renderAnnotationLayerProps) {\n            return null;\n        }\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Page_AnnotationLayer_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, \"\".concat(pageKey, \"_annotations\"));\n    }\n    function renderChildren() {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_PageContext_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"].Provider, {\n            value: childContext,\n            children: [\n                renderMainLayer(),\n                renderTextLayer(),\n                renderAnnotationLayer(),\n                children\n            ]\n        });\n    }\n    function renderContent() {\n        if (!pageNumber) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"no-data\",\n                children: typeof noData === \"function\" ? noData() : noData\n            });\n        }\n        if (pdf === null || page === undefined || page === null) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"loading\",\n                children: typeof loading === \"function\" ? loading() : loading\n            });\n        }\n        if (pdf === false || page === false) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"error\",\n                children: typeof error === \"function\" ? error() : error\n            });\n        }\n        return renderChildren();\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", Object.assign({\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_className, className),\n        \"data-page-number\": pageNumber,\n        // Assertion is needed for React 18 compatibility\n        ref: (0,merge_refs__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(inputRef, pageElement),\n        style: {\n            [\"--scale-factor\"]: \"\".concat(scale),\n            backgroundColor: canvasBackground || \"white\",\n            position: \"relative\",\n            minWidth: \"min-content\",\n            minHeight: \"min-content\"\n        }\n    }, eventProps, {\n        children: renderContent()\n    }));\n}\n_s(Page, \"+eTnfXd1R1oht7EkcodM34zmLMI=\", false, function() {\n    return [\n        _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/PageContext.js":
/*!********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/PageContext.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nconst pageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n/* harmony default export */ __webpack_exports__[\"default\"] = (pageContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vUGFnZUNvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7NkRBQ3NDO0FBQ3RDLE1BQU1DLDRCQUFjRCxvREFBYUEsQ0FBQztBQUNsQywrREFBZUMsV0FBV0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1BhZ2VDb250ZXh0LmpzP2E0Y2UiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuaW1wb3J0IHsgY3JlYXRlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcbmNvbnN0IHBhZ2VDb250ZXh0ID0gY3JlYXRlQ29udGV4dChudWxsKTtcbmV4cG9ydCBkZWZhdWx0IHBhZ2VDb250ZXh0O1xuIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJwYWdlQ29udGV4dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/PageContext.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AnnotationLayer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! make-cancellable-promise */ \"(app-pages-browser)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(app-pages-browser)/./node_modules/warning/warning.js\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! pdfjs-dist */ \"(app-pages-browser)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/hooks/useDocumentContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../shared/hooks/useResolver.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../shared/utils.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction AnnotationLayer() {\n    _s();\n    const documentContext = (0,_shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pageContext, \"Unable to find Page context.\");\n    const mergedProps = Object.assign(Object.assign({}, documentContext), pageContext);\n    const { imageResourcesPath, linkService, onGetAnnotationsError: onGetAnnotationsErrorProps, onGetAnnotationsSuccess: onGetAnnotationsSuccessProps, onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps, onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps, page, pdf, renderForms, rotate, scale = 1 } = mergedProps;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pdf, \"Attempted to load page annotations, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.\");\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(page, \"Attempted to load page annotations, but no page was specified.\");\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(linkService, \"Attempted to load page annotations, but no linkService was specified.\");\n    const [annotationsState, annotationsDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { value: annotations, error: annotationsError } = annotationsState;\n    const layerElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    warning__WEBPACK_IMPORTED_MODULE_4__(Number.parseInt(window.getComputedStyle(document.body).getPropertyValue(\"--react-pdf-annotation-layer\"), 10) === 1, \"AnnotationLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-annotations\");\n    function onLoadSuccess() {\n        if (!annotations) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetAnnotationsSuccessProps) {\n            onGetAnnotationsSuccessProps(annotations);\n        }\n    }\n    function onLoadError() {\n        if (!annotationsError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, annotationsError.toString());\n        if (onGetAnnotationsErrorProps) {\n            onGetAnnotationsErrorProps(annotationsError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetAnnotations() {\n        annotationsDispatch({\n            type: \"RESET\"\n        });\n    }, [\n        annotationsDispatch,\n        page\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadAnnotations() {\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(page.getAnnotations());\n        const runningTask = cancellable;\n        cancellable.promise.then((nextAnnotations)=>{\n            annotationsDispatch({\n                type: \"RESOLVE\",\n                value: nextAnnotations\n            });\n        }).catch((error)=>{\n            annotationsDispatch({\n                type: \"REJECT\",\n                error\n            });\n        });\n        return ()=>{\n            (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.cancelRunningTask)(runningTask);\n        };\n    }, [\n        annotationsDispatch,\n        page\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (annotations === undefined) {\n            return;\n        }\n        if (annotations === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [\n        annotations\n    ]);\n    function onRenderSuccess() {\n        if (onRenderAnnotationLayerSuccessProps) {\n            onRenderAnnotationLayerSuccessProps();\n        }\n    }\n    function onRenderError(error) {\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, \"\".concat(error));\n        if (onRenderAnnotationLayerErrorProps) {\n            onRenderAnnotationLayerErrorProps(error);\n        }\n    }\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>page.getViewport({\n            scale,\n            rotation: rotate\n        }), [\n        page,\n        rotate,\n        scale\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function renderAnnotationLayer() {\n        if (!pdf || !page || !linkService || !annotations) {\n            return;\n        }\n        const { current: layer } = layerElement;\n        if (!layer) {\n            return;\n        }\n        const clonedViewport = viewport.clone({\n            dontFlip: true\n        });\n        const annotationLayerParameters = {\n            accessibilityManager: null,\n            annotationCanvasMap: null,\n            annotationEditorUIManager: null,\n            div: layer,\n            l10n: null,\n            page,\n            structTreeLayer: null,\n            viewport: clonedViewport\n        };\n        const renderParameters = {\n            annotations,\n            annotationStorage: pdf.annotationStorage,\n            div: layer,\n            imageResourcesPath,\n            linkService,\n            page,\n            renderForms,\n            viewport: clonedViewport\n        };\n        layer.innerHTML = \"\";\n        try {\n            new pdfjs_dist__WEBPACK_IMPORTED_MODULE_5__.AnnotationLayer(annotationLayerParameters).render(renderParameters);\n            // Intentional immediate callback\n            onRenderSuccess();\n        } catch (error) {\n            onRenderError(error);\n        }\n        return ()=>{\n        // TODO: Cancel running task?\n        };\n    }, [\n        annotations,\n        imageResourcesPath,\n        linkService,\n        page,\n        pdf,\n        renderForms,\n        viewport\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\"react-pdf__Page__annotations\", \"annotationLayer\"),\n        ref: layerElement\n    });\n}\n_s(AnnotationLayer, \"xGRgXRrXbMzusLtEcij0h5TUyxQ=\", false, function() {\n    return [\n        _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    ];\n});\n_c = AnnotationLayer;\nvar _c;\n$RefreshReg$(_c, \"AnnotationLayer\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/Canvas.js":
/*!********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/Canvas.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Canvas; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var merge_refs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! merge-refs */ \"(app-pages-browser)/./node_modules/merge-refs/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! warning */ \"(app-pages-browser)/./node_modules/warning/warning.js\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! pdfjs-dist */ \"(app-pages-browser)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _StructTree_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../StructTree.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/StructTree.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/utils.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ANNOTATION_MODE = pdfjs_dist__WEBPACK_IMPORTED_MODULE_4__.AnnotationMode;\nfunction Canvas(props) {\n    _s();\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(pageContext, \"Unable to find Page context.\");\n    const mergedProps = Object.assign(Object.assign({}, pageContext), props);\n    const { _className, canvasBackground, devicePixelRatio = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.getDevicePixelRatio)(), onRenderError: onRenderErrorProps, onRenderSuccess: onRenderSuccessProps, page, renderForms, renderTextLayer, rotate, scale } = mergedProps;\n    const { canvasRef } = props;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(page, \"Attempted to render page canvas, but no page was specified.\");\n    const canvasElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /**\n     * Called when a page is rendered successfully.\n     */ function onRenderSuccess() {\n        if (!page) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onRenderSuccessProps) {\n            onRenderSuccessProps((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.makePageCallback)(page, scale));\n        }\n    }\n    /**\n     * Called when a page fails to render.\n     */ function onRenderError(error) {\n        if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.isCancelException)(error)) {\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, error.toString());\n        if (onRenderErrorProps) {\n            onRenderErrorProps(error);\n        }\n    }\n    const renderViewport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>page.getViewport({\n            scale: scale * devicePixelRatio,\n            rotation: rotate\n        }), [\n        devicePixelRatio,\n        page,\n        rotate,\n        scale\n    ]);\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>page.getViewport({\n            scale,\n            rotation: rotate\n        }), [\n        page,\n        rotate,\n        scale\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function drawPageOnCanvas() {\n        if (!page) {\n            return;\n        }\n        // Ensures the canvas will be re-rendered from scratch. Otherwise all form data will stay.\n        page.cleanup();\n        const { current: canvas } = canvasElement;\n        if (!canvas) {\n            return;\n        }\n        canvas.width = renderViewport.width;\n        canvas.height = renderViewport.height;\n        canvas.style.width = \"\".concat(Math.floor(viewport.width), \"px\");\n        canvas.style.height = \"\".concat(Math.floor(viewport.height), \"px\");\n        canvas.style.visibility = \"hidden\";\n        const renderContext = {\n            annotationMode: renderForms ? ANNOTATION_MODE.ENABLE_FORMS : ANNOTATION_MODE.ENABLE,\n            canvasContext: canvas.getContext(\"2d\", {\n                alpha: false\n            }),\n            viewport: renderViewport\n        };\n        if (canvasBackground) {\n            renderContext.background = canvasBackground;\n        }\n        const cancellable = page.render(renderContext);\n        const runningTask = cancellable;\n        cancellable.promise.then(()=>{\n            canvas.style.visibility = \"\";\n            onRenderSuccess();\n        }).catch(onRenderError);\n        return ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.cancelRunningTask)(runningTask);\n    }, [\n        canvasBackground,\n        page,\n        renderForms,\n        renderViewport,\n        viewport\n    ]);\n    const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const { current: canvas } = canvasElement;\n        /**\n         * Zeroing the width and height cause most browsers to release graphics\n         * resources immediately, which can greatly reduce memory consumption.\n         */ if (canvas) {\n            canvas.width = 0;\n            canvas.height = 0;\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>cleanup, [\n        cleanup\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"canvas\", {\n        className: \"\".concat(_className, \"__canvas\"),\n        dir: \"ltr\",\n        ref: (0,merge_refs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(canvasRef, canvasElement),\n        style: {\n            display: \"block\",\n            userSelect: \"none\"\n        },\n        children: renderTextLayer ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_StructTree_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}) : null\n    });\n}\n_s(Canvas, \"yq8trrASu/syKKP2nPFDiuSi7Ys=\", false, function() {\n    return [\n        _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = Canvas;\nvar _c;\n$RefreshReg$(_c, \"Canvas\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/Canvas.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/TextLayer.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TextLayer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! make-cancellable-promise */ \"(app-pages-browser)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(app-pages-browser)/./node_modules/warning/warning.js\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! pdfjs-dist */ \"(app-pages-browser)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../shared/hooks/useResolver.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../shared/utils.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction isTextItem(item) {\n    return \"str\" in item;\n}\nfunction TextLayer() {\n    _s();\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pageContext, \"Unable to find Page context.\");\n    const { customTextRenderer, onGetTextError, onGetTextSuccess, onRenderTextLayerError, onRenderTextLayerSuccess, page, pageIndex, pageNumber, rotate, scale } = pageContext;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(page, \"Attempted to load page text content, but no page was specified.\");\n    const [textContentState, textContentDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const { value: textContent, error: textContentError } = textContentState;\n    const layerElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    warning__WEBPACK_IMPORTED_MODULE_4__(Number.parseInt(window.getComputedStyle(document.body).getPropertyValue(\"--react-pdf-text-layer\"), 10) === 1, \"TextLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-text-layer\");\n    /**\n     * Called when a page text content is read successfully\n     */ function onLoadSuccess() {\n        if (!textContent) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetTextSuccess) {\n            onGetTextSuccess(textContent);\n        }\n    }\n    /**\n     * Called when a page text content failed to read successfully\n     */ function onLoadError() {\n        if (!textContentError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, textContentError.toString());\n        if (onGetTextError) {\n            onGetTextError(textContentError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetTextContent() {\n        textContentDispatch({\n            type: \"RESET\"\n        });\n    }, [\n        page,\n        textContentDispatch\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadTextContent() {\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(page.getTextContent());\n        const runningTask = cancellable;\n        cancellable.promise.then((nextTextContent)=>{\n            textContentDispatch({\n                type: \"RESOLVE\",\n                value: nextTextContent\n            });\n        }).catch((error)=>{\n            textContentDispatch({\n                type: \"REJECT\",\n                error\n            });\n        });\n        return ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.cancelRunningTask)(runningTask);\n    }, [\n        page,\n        textContentDispatch\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (textContent === undefined) {\n            return;\n        }\n        if (textContent === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [\n        textContent\n    ]);\n    /**\n     * Called when a text layer is rendered successfully\n     */ const onRenderSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (onRenderTextLayerSuccess) {\n            onRenderTextLayerSuccess();\n        }\n    }, [\n        onRenderTextLayerSuccess\n    ]);\n    /**\n     * Called when a text layer failed to render successfully\n     */ const onRenderError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((error)=>{\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, error.toString());\n        if (onRenderTextLayerError) {\n            onRenderTextLayerError(error);\n        }\n    }, [\n        onRenderTextLayerError\n    ]);\n    function onMouseDown() {\n        const layer = layerElement.current;\n        if (!layer) {\n            return;\n        }\n        layer.classList.add(\"selecting\");\n    }\n    function onMouseUp() {\n        const layer = layerElement.current;\n        if (!layer) {\n            return;\n        }\n        layer.classList.remove(\"selecting\");\n    }\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>page.getViewport({\n            scale,\n            rotation: rotate\n        }), [\n        page,\n        rotate,\n        scale\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(function renderTextLayer() {\n        if (!page || !textContent) {\n            return;\n        }\n        const { current: layer } = layerElement;\n        if (!layer) {\n            return;\n        }\n        layer.innerHTML = \"\";\n        const textContentSource = page.streamTextContent({\n            includeMarkedContent: true\n        });\n        const parameters = {\n            container: layer,\n            textContentSource,\n            viewport\n        };\n        const cancellable = new pdfjs_dist__WEBPACK_IMPORTED_MODULE_5__.TextLayer(parameters);\n        const runningTask = cancellable;\n        cancellable.render().then(()=>{\n            const end = document.createElement(\"div\");\n            end.className = \"endOfContent\";\n            layer.append(end);\n            const layerChildren = layer.querySelectorAll('[role=\"presentation\"]');\n            if (customTextRenderer) {\n                let index = 0;\n                textContent.items.forEach((item, itemIndex)=>{\n                    if (!isTextItem(item)) {\n                        return;\n                    }\n                    const child = layerChildren[index];\n                    if (!child) {\n                        return;\n                    }\n                    const content = customTextRenderer(Object.assign({\n                        pageIndex,\n                        pageNumber,\n                        itemIndex\n                    }, item));\n                    child.innerHTML = content;\n                    index += item.str && item.hasEOL ? 2 : 1;\n                });\n            }\n            // Intentional immediate callback\n            onRenderSuccess();\n        }).catch(onRenderError);\n        return ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.cancelRunningTask)(runningTask);\n    }, [\n        customTextRenderer,\n        onRenderError,\n        onRenderSuccess,\n        page,\n        pageIndex,\n        pageNumber,\n        textContent,\n        viewport\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\"react-pdf__Page__textContent\", \"textLayer\"),\n        onMouseUp: onMouseUp,\n        onMouseDown: onMouseDown,\n        ref: layerElement\n    });\n}\n_s(TextLayer, \"e4W8YKokTiHAs3v+eLzTsnB/cXo=\", false, function() {\n    return [\n        _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    ];\n});\n_c = TextLayer;\nvar _c;\n$RefreshReg$(_c, \"TextLayer\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/PasswordResponses.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/PasswordResponses.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// As defined in https://github.com/mozilla/pdf.js/blob/d9fac3459609a807be6506fb3441b5da4b154d14/src/shared/util.js#L371-L374\nconst PasswordResponses = {\n    NEED_PASSWORD: 1,\n    INCORRECT_PASSWORD: 2,\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (PasswordResponses);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vUGFzc3dvcmRSZXNwb25zZXMuanMiLCJtYXBwaW5ncyI6IjtBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBZSxpQkFBaUIsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1Bhc3N3b3JkUmVzcG9uc2VzLmpzP2RhNDUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQXMgZGVmaW5lZCBpbiBodHRwczovL2dpdGh1Yi5jb20vbW96aWxsYS9wZGYuanMvYmxvYi9kOWZhYzM0NTk2MDlhODA3YmU2NTA2ZmIzNDQxYjVkYTRiMTU0ZDE0L3NyYy9zaGFyZWQvdXRpbC5qcyNMMzcxLUwzNzRcbmNvbnN0IFBhc3N3b3JkUmVzcG9uc2VzID0ge1xuICAgIE5FRURfUEFTU1dPUkQ6IDEsXG4gICAgSU5DT1JSRUNUX1BBU1NXT1JEOiAyLFxufTtcbmV4cG9ydCBkZWZhdWx0IFBhc3N3b3JkUmVzcG9uc2VzO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/PasswordResponses.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Ref.js":
/*!************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Ref.js ***!
  \************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Ref; }\n/* harmony export */ });\nclass Ref {\n    constructor({ num, gen }) {\n        this.num = num;\n        this.gen = gen;\n    }\n    toString() {\n        let str = `${this.num}R`;\n        if (this.gen !== 0) {\n            str += this.gen;\n        }\n        return str;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vUmVmLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmLGtCQUFrQixVQUFVO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLFNBQVM7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vUmVmLmpzP2Q2ODMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgY2xhc3MgUmVmIHtcbiAgICBjb25zdHJ1Y3Rvcih7IG51bSwgZ2VuIH0pIHtcbiAgICAgICAgdGhpcy5udW0gPSBudW07XG4gICAgICAgIHRoaXMuZ2VuID0gZ2VuO1xuICAgIH1cbiAgICB0b1N0cmluZygpIHtcbiAgICAgICAgbGV0IHN0ciA9IGAke3RoaXMubnVtfVJgO1xuICAgICAgICBpZiAodGhpcy5nZW4gIT09IDApIHtcbiAgICAgICAgICAgIHN0ciArPSB0aGlzLmdlbjtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gc3RyO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/Ref.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/StructTree.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/StructTree.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StructTree; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! make-cancellable-promise */ \"(app-pages-browser)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! warning */ \"(app-pages-browser)/./node_modules/warning/warning.js\");\n/* harmony import */ var _StructTreeItem_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StructTreeItem.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/StructTreeItem.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./shared/hooks/usePageContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./shared/utils.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n\n\n\n\n\n\n\n\n\nfunction StructTree() {\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const { onGetStructTreeError: onGetStructTreeErrorProps, onGetStructTreeSuccess: onGetStructTreeSuccessProps, } = pageContext;\n    const [structTreeState, structTreeDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { value: structTree, error: structTreeError } = structTreeState;\n    const { customTextRenderer, page } = pageContext;\n    function onLoadSuccess() {\n        if (!structTree) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetStructTreeSuccessProps) {\n            onGetStructTreeSuccessProps(structTree);\n        }\n    }\n    function onLoadError() {\n        if (!structTreeError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, structTreeError.toString());\n        if (onGetStructTreeErrorProps) {\n            onGetStructTreeErrorProps(structTreeError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetStructTree() {\n        structTreeDispatch({ type: 'RESET' });\n    }, [structTreeDispatch, page]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadStructTree() {\n        if (customTextRenderer) {\n            // TODO: Document why this is necessary\n            return;\n        }\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(page.getStructTree());\n        const runningTask = cancellable;\n        cancellable.promise\n            .then((nextStructTree) => {\n            structTreeDispatch({ type: 'RESOLVE', value: nextStructTree });\n        })\n            .catch((error) => {\n            structTreeDispatch({ type: 'REJECT', error });\n        });\n        return () => (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.cancelRunningTask)(runningTask);\n    }, [customTextRenderer, page, structTreeDispatch]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n        if (structTree === undefined) {\n            return;\n        }\n        if (structTree === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [structTree]);\n    if (!structTree) {\n        return null;\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_StructTreeItem_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"], { className: \"react-pdf__Page__structTree structTree\", node: structTree });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/StructTree.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/StructTreeItem.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/StructTreeItem.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StructTreeItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/structTreeUtils.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js\");\n\n\n\nfunction StructTreeItem({ className, node, }) {\n    const attributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => (0,_shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_2__.getAttributes)(node), [node]);\n    const children = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n        if (!(0,_shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isStructTreeNode)(node)) {\n            return null;\n        }\n        if ((0,_shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isStructTreeNodeWithOnlyContentChild)(node)) {\n            return null;\n        }\n        return node.children.map((child, index) => {\n            return (\n            // biome-ignore lint/suspicious/noArrayIndexKey: index is stable here\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StructTreeItem, { node: child }, index));\n        });\n    }, [node]);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", Object.assign({ className: className }, attributes, { children: children })));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vU3RydWN0VHJlZUl0ZW0uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFnRDtBQUNoQjtBQUNxRjtBQUN0RywwQkFBMEIsa0JBQWtCO0FBQzNELHVCQUF1Qiw4Q0FBTyxPQUFPLHlFQUFhO0FBQ2xELHFCQUFxQiw4Q0FBTztBQUM1QixhQUFhLDRFQUFnQjtBQUM3QjtBQUNBO0FBQ0EsWUFBWSxnR0FBb0M7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksc0RBQUksbUJBQW1CLGFBQWE7QUFDaEQsU0FBUztBQUNULEtBQUs7QUFDTCxZQUFZLHNEQUFJLHlCQUF5QixzQkFBc0IsZ0JBQWdCLG9CQUFvQjtBQUNuRyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1N0cnVjdFRyZWVJdGVtLmpzP2IxMmMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmltcG9ydCB7IHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBnZXRBdHRyaWJ1dGVzLCBpc1N0cnVjdFRyZWVOb2RlLCBpc1N0cnVjdFRyZWVOb2RlV2l0aE9ubHlDb250ZW50Q2hpbGQsIH0gZnJvbSAnLi9zaGFyZWQvc3RydWN0VHJlZVV0aWxzLmpzJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFN0cnVjdFRyZWVJdGVtKHsgY2xhc3NOYW1lLCBub2RlLCB9KSB7XG4gICAgY29uc3QgYXR0cmlidXRlcyA9IHVzZU1lbW8oKCkgPT4gZ2V0QXR0cmlidXRlcyhub2RlKSwgW25vZGVdKTtcbiAgICBjb25zdCBjaGlsZHJlbiA9IHVzZU1lbW8oKCkgPT4ge1xuICAgICAgICBpZiAoIWlzU3RydWN0VHJlZU5vZGUobm9kZSkpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGlmIChpc1N0cnVjdFRyZWVOb2RlV2l0aE9ubHlDb250ZW50Q2hpbGQobm9kZSkpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBub2RlLmNoaWxkcmVuLm1hcCgoY2hpbGQsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgLy8gYmlvbWUtaWdub3JlIGxpbnQvc3VzcGljaW91cy9ub0FycmF5SW5kZXhLZXk6IGluZGV4IGlzIHN0YWJsZSBoZXJlXG4gICAgICAgICAgICBfanN4KFN0cnVjdFRyZWVJdGVtLCB7IG5vZGU6IGNoaWxkIH0sIGluZGV4KSk7XG4gICAgICAgIH0pO1xuICAgIH0sIFtub2RlXSk7XG4gICAgcmV0dXJuIChfanN4KFwic3BhblwiLCBPYmplY3QuYXNzaWduKHsgY2xhc3NOYW1lOiBjbGFzc05hbWUgfSwgYXR0cmlidXRlcywgeyBjaGlsZHJlbjogY2hpbGRyZW4gfSkpKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/StructTreeItem.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Thumbnail.js":
/*!******************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Thumbnail.js ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Thumbnail; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var _Page_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Page.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./shared/utils.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* harmony import */ var _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./shared/hooks/useDocumentContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\nvar __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\n\n\n\n/**\n * Displays a thumbnail of a page. Does not render the annotation layer or the text layer. Does not register itself as a link target, so the user will not be scrolled to a Thumbnail component when clicked on an internal link (e.g. in Table of Contents). When clicked, attempts to navigate to the page clicked (similarly to a link in Outline).\n *\n * Should be placed inside `<Document />`. Alternatively, it can have `pdf` prop passed, which can be obtained from `<Document />`'s `onLoadSuccess` callback function.\n */ function Thumbnail(props) {\n    _s();\n    const documentContext = (0,_shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const mergedProps = Object.assign(Object.assign({}, documentContext), props);\n    const { className, linkService, onItemClick, pageIndex: pageIndexProps, pageNumber: pageNumberProps, pdf } = mergedProps;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(pdf, \"Attempted to load a thumbnail, but no document was specified. Wrap <Thumbnail /> in a <Document /> or pass explicit `pdf` prop.\");\n    const pageIndex = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_4__.isProvided)(pageNumberProps) ? pageNumberProps - 1 : pageIndexProps !== null && pageIndexProps !== void 0 ? pageIndexProps : null;\n    const pageNumber = pageNumberProps !== null && pageNumberProps !== void 0 ? pageNumberProps : (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_4__.isProvided)(pageIndexProps) ? pageIndexProps + 1 : null;\n    function onClick(event) {\n        event.preventDefault();\n        if (!(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_4__.isProvided)(pageIndex) || !pageNumber) {\n            return;\n        }\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(onItemClick || linkService, \"Either onItemClick callback or linkService must be defined in order to navigate to an outline item.\");\n        if (onItemClick) {\n            onItemClick({\n                pageIndex,\n                pageNumber\n            });\n        } else if (linkService) {\n            linkService.goToPage(pageNumber);\n        }\n    }\n    const { className: classNameProps, onItemClick: onItemClickProps } = props, pageProps = __rest(props, [\n        \"className\",\n        \"onItemClick\"\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"react-pdf__Thumbnail\", className),\n        href: pageNumber ? \"#\" : undefined,\n        onClick: onClick,\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Page_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"], Object.assign({}, pageProps, {\n            _className: \"react-pdf__Thumbnail__page\",\n            _enableRegisterUnregisterPage: false,\n            renderAnnotationLayer: false,\n            renderTextLayer: false\n        }))\n    });\n}\n_s(Thumbnail, \"LRrNpyT+ZVsooho0T3rocpjmKVM=\", false, function() {\n    return [\n        _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c = Thumbnail;\nvar _c;\n$RefreshReg$(_c, \"Thumbnail\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vVGh1bWJuYWlsLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQ0EsSUFBSUEsU0FBUyxTQUFLLElBQUksU0FBSSxDQUFDQSxNQUFNLElBQUssU0FBVUMsQ0FBQyxFQUFFQyxDQUFDO0lBQ2hELElBQUlDLElBQUksQ0FBQztJQUNULElBQUssSUFBSUMsS0FBS0gsRUFBRyxJQUFJSSxPQUFPQyxTQUFTLENBQUNDLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDUCxHQUFHRyxNQUFNRixFQUFFTyxPQUFPLENBQUNMLEtBQUssR0FDOUVELENBQUMsQ0FBQ0MsRUFBRSxHQUFHSCxDQUFDLENBQUNHLEVBQUU7SUFDZixJQUFJSCxLQUFLLFFBQVEsT0FBT0ksT0FBT0sscUJBQXFCLEtBQUssWUFDckQsSUFBSyxJQUFJQyxJQUFJLEdBQUdQLElBQUlDLE9BQU9LLHFCQUFxQixDQUFDVCxJQUFJVSxJQUFJUCxFQUFFUSxNQUFNLEVBQUVELElBQUs7UUFDcEUsSUFBSVQsRUFBRU8sT0FBTyxDQUFDTCxDQUFDLENBQUNPLEVBQUUsSUFBSSxLQUFLTixPQUFPQyxTQUFTLENBQUNPLG9CQUFvQixDQUFDTCxJQUFJLENBQUNQLEdBQUdHLENBQUMsQ0FBQ08sRUFBRSxHQUN6RVIsQ0FBQyxDQUFDQyxDQUFDLENBQUNPLEVBQUUsQ0FBQyxHQUFHVixDQUFDLENBQUNHLENBQUMsQ0FBQ08sRUFBRSxDQUFDO0lBQ3pCO0lBQ0osT0FBT1I7QUFDWDtBQUNnRDtBQUN4QjtBQUNlO0FBQ1Y7QUFDa0I7QUFDdUI7QUFDdEU7Ozs7Q0FJQyxHQUNjLFNBQVNrQixVQUFVQyxLQUFLOztJQUNuQyxNQUFNQyxrQkFBa0JILCtFQUFrQkE7SUFDMUMsTUFBTUksY0FBY25CLE9BQU9vQixNQUFNLENBQUNwQixPQUFPb0IsTUFBTSxDQUFDLENBQUMsR0FBR0Ysa0JBQWtCRDtJQUN0RSxNQUFNLEVBQUVJLFNBQVMsRUFBRUMsV0FBVyxFQUFFQyxXQUFXLEVBQUVDLFdBQVdDLGNBQWMsRUFBRUMsWUFBWUMsZUFBZSxFQUFFQyxHQUFHLEVBQUcsR0FBR1Q7SUFDOUdQLDBEQUFTQSxDQUFDZ0IsS0FBSztJQUNmLE1BQU1KLFlBQVlWLDREQUFVQSxDQUFDYSxtQkFBbUJBLGtCQUFrQixJQUFLRixtQkFBbUIsUUFBUUEsbUJBQW1CLEtBQUssSUFBSUEsaUJBQWlCO0lBQy9JLE1BQU1DLGFBQWFDLG9CQUFvQixRQUFRQSxvQkFBb0IsS0FBSyxJQUFJQSxrQkFBbUJiLDREQUFVQSxDQUFDVyxrQkFBa0JBLGlCQUFpQixJQUFJO0lBQ2pKLFNBQVNJLFFBQVFDLEtBQUs7UUFDbEJBLE1BQU1DLGNBQWM7UUFDcEIsSUFBSSxDQUFDakIsNERBQVVBLENBQUNVLGNBQWMsQ0FBQ0UsWUFBWTtZQUN2QztRQUNKO1FBQ0FkLDBEQUFTQSxDQUFDVyxlQUFlRCxhQUFhO1FBQ3RDLElBQUlDLGFBQWE7WUFDYkEsWUFBWTtnQkFDUkM7Z0JBQ0FFO1lBQ0o7UUFDSixPQUNLLElBQUlKLGFBQWE7WUFDbEJBLFlBQVlVLFFBQVEsQ0FBQ047UUFDekI7SUFDSjtJQUNBLE1BQU0sRUFBRUwsV0FBV1ksY0FBYyxFQUFFVixhQUFhVyxnQkFBZ0IsRUFBRSxHQUFHakIsT0FBT2tCLFlBQVl4QyxPQUFPc0IsT0FBTztRQUFDO1FBQWE7S0FBYztJQUNsSSxPQUFRUCxzREFBSUEsQ0FBQyxLQUFLO1FBQUVXLFdBQVdWLGdEQUFJQSxDQUFDLHdCQUF3QlU7UUFBWWUsTUFBTVYsYUFBYSxNQUFNVztRQUFXUixTQUFTQTtRQUFTUyxVQUFVNUIsc0RBQUlBLENBQUNHLGdEQUFJQSxFQUFFYixPQUFPb0IsTUFBTSxDQUFDLENBQUMsR0FBR2UsV0FBVztZQUFFSSxZQUFZO1lBQThCQywrQkFBK0I7WUFBT0MsdUJBQXVCO1lBQU9DLGlCQUFpQjtRQUFNO0lBQUk7QUFDL1Q7R0F6QndCMUI7O1FBQ0lELDJFQUFrQkE7OztLQUR0QkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXBkZi9kaXN0L2VzbS9UaHVtYm5haWwuanM/ZDhjNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG52YXIgX19yZXN0ID0gKHRoaXMgJiYgdGhpcy5fX3Jlc3QpIHx8IGZ1bmN0aW9uIChzLCBlKSB7XG4gICAgdmFyIHQgPSB7fTtcbiAgICBmb3IgKHZhciBwIGluIHMpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwocywgcCkgJiYgZS5pbmRleE9mKHApIDwgMClcbiAgICAgICAgdFtwXSA9IHNbcF07XG4gICAgaWYgKHMgIT0gbnVsbCAmJiB0eXBlb2YgT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyA9PT0gXCJmdW5jdGlvblwiKVxuICAgICAgICBmb3IgKHZhciBpID0gMCwgcCA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMocyk7IGkgPCBwLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBpZiAoZS5pbmRleE9mKHBbaV0pIDwgMCAmJiBPYmplY3QucHJvdG90eXBlLnByb3BlcnR5SXNFbnVtZXJhYmxlLmNhbGwocywgcFtpXSkpXG4gICAgICAgICAgICAgICAgdFtwW2ldXSA9IHNbcFtpXV07XG4gICAgICAgIH1cbiAgICByZXR1cm4gdDtcbn07XG5pbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuaW1wb3J0IGNsc3ggZnJvbSAnY2xzeCc7XG5pbXBvcnQgaW52YXJpYW50IGZyb20gJ3RpbnktaW52YXJpYW50JztcbmltcG9ydCBQYWdlIGZyb20gJy4vUGFnZS5qcyc7XG5pbXBvcnQgeyBpc1Byb3ZpZGVkIH0gZnJvbSAnLi9zaGFyZWQvdXRpbHMuanMnO1xuaW1wb3J0IHVzZURvY3VtZW50Q29udGV4dCBmcm9tICcuL3NoYXJlZC9ob29rcy91c2VEb2N1bWVudENvbnRleHQuanMnO1xuLyoqXG4gKiBEaXNwbGF5cyBhIHRodW1ibmFpbCBvZiBhIHBhZ2UuIERvZXMgbm90IHJlbmRlciB0aGUgYW5ub3RhdGlvbiBsYXllciBvciB0aGUgdGV4dCBsYXllci4gRG9lcyBub3QgcmVnaXN0ZXIgaXRzZWxmIGFzIGEgbGluayB0YXJnZXQsIHNvIHRoZSB1c2VyIHdpbGwgbm90IGJlIHNjcm9sbGVkIHRvIGEgVGh1bWJuYWlsIGNvbXBvbmVudCB3aGVuIGNsaWNrZWQgb24gYW4gaW50ZXJuYWwgbGluayAoZS5nLiBpbiBUYWJsZSBvZiBDb250ZW50cykuIFdoZW4gY2xpY2tlZCwgYXR0ZW1wdHMgdG8gbmF2aWdhdGUgdG8gdGhlIHBhZ2UgY2xpY2tlZCAoc2ltaWxhcmx5IHRvIGEgbGluayBpbiBPdXRsaW5lKS5cbiAqXG4gKiBTaG91bGQgYmUgcGxhY2VkIGluc2lkZSBgPERvY3VtZW50IC8+YC4gQWx0ZXJuYXRpdmVseSwgaXQgY2FuIGhhdmUgYHBkZmAgcHJvcCBwYXNzZWQsIHdoaWNoIGNhbiBiZSBvYnRhaW5lZCBmcm9tIGA8RG9jdW1lbnQgLz5gJ3MgYG9uTG9hZFN1Y2Nlc3NgIGNhbGxiYWNrIGZ1bmN0aW9uLlxuICovXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUaHVtYm5haWwocHJvcHMpIHtcbiAgICBjb25zdCBkb2N1bWVudENvbnRleHQgPSB1c2VEb2N1bWVudENvbnRleHQoKTtcbiAgICBjb25zdCBtZXJnZWRQcm9wcyA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgZG9jdW1lbnRDb250ZXh0KSwgcHJvcHMpO1xuICAgIGNvbnN0IHsgY2xhc3NOYW1lLCBsaW5rU2VydmljZSwgb25JdGVtQ2xpY2ssIHBhZ2VJbmRleDogcGFnZUluZGV4UHJvcHMsIHBhZ2VOdW1iZXI6IHBhZ2VOdW1iZXJQcm9wcywgcGRmLCB9ID0gbWVyZ2VkUHJvcHM7XG4gICAgaW52YXJpYW50KHBkZiwgJ0F0dGVtcHRlZCB0byBsb2FkIGEgdGh1bWJuYWlsLCBidXQgbm8gZG9jdW1lbnQgd2FzIHNwZWNpZmllZC4gV3JhcCA8VGh1bWJuYWlsIC8+IGluIGEgPERvY3VtZW50IC8+IG9yIHBhc3MgZXhwbGljaXQgYHBkZmAgcHJvcC4nKTtcbiAgICBjb25zdCBwYWdlSW5kZXggPSBpc1Byb3ZpZGVkKHBhZ2VOdW1iZXJQcm9wcykgPyBwYWdlTnVtYmVyUHJvcHMgLSAxIDogKHBhZ2VJbmRleFByb3BzICE9PSBudWxsICYmIHBhZ2VJbmRleFByb3BzICE9PSB2b2lkIDAgPyBwYWdlSW5kZXhQcm9wcyA6IG51bGwpO1xuICAgIGNvbnN0IHBhZ2VOdW1iZXIgPSBwYWdlTnVtYmVyUHJvcHMgIT09IG51bGwgJiYgcGFnZU51bWJlclByb3BzICE9PSB2b2lkIDAgPyBwYWdlTnVtYmVyUHJvcHMgOiAoaXNQcm92aWRlZChwYWdlSW5kZXhQcm9wcykgPyBwYWdlSW5kZXhQcm9wcyArIDEgOiBudWxsKTtcbiAgICBmdW5jdGlvbiBvbkNsaWNrKGV2ZW50KSB7XG4gICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgIGlmICghaXNQcm92aWRlZChwYWdlSW5kZXgpIHx8ICFwYWdlTnVtYmVyKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgaW52YXJpYW50KG9uSXRlbUNsaWNrIHx8IGxpbmtTZXJ2aWNlLCAnRWl0aGVyIG9uSXRlbUNsaWNrIGNhbGxiYWNrIG9yIGxpbmtTZXJ2aWNlIG11c3QgYmUgZGVmaW5lZCBpbiBvcmRlciB0byBuYXZpZ2F0ZSB0byBhbiBvdXRsaW5lIGl0ZW0uJyk7XG4gICAgICAgIGlmIChvbkl0ZW1DbGljaykge1xuICAgICAgICAgICAgb25JdGVtQ2xpY2soe1xuICAgICAgICAgICAgICAgIHBhZ2VJbmRleCxcbiAgICAgICAgICAgICAgICBwYWdlTnVtYmVyLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAobGlua1NlcnZpY2UpIHtcbiAgICAgICAgICAgIGxpbmtTZXJ2aWNlLmdvVG9QYWdlKHBhZ2VOdW1iZXIpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGNvbnN0IHsgY2xhc3NOYW1lOiBjbGFzc05hbWVQcm9wcywgb25JdGVtQ2xpY2s6IG9uSXRlbUNsaWNrUHJvcHMgfSA9IHByb3BzLCBwYWdlUHJvcHMgPSBfX3Jlc3QocHJvcHMsIFtcImNsYXNzTmFtZVwiLCBcIm9uSXRlbUNsaWNrXCJdKTtcbiAgICByZXR1cm4gKF9qc3goXCJhXCIsIHsgY2xhc3NOYW1lOiBjbHN4KCdyZWFjdC1wZGZfX1RodW1ibmFpbCcsIGNsYXNzTmFtZSksIGhyZWY6IHBhZ2VOdW1iZXIgPyAnIycgOiB1bmRlZmluZWQsIG9uQ2xpY2s6IG9uQ2xpY2ssIGNoaWxkcmVuOiBfanN4KFBhZ2UsIE9iamVjdC5hc3NpZ24oe30sIHBhZ2VQcm9wcywgeyBfY2xhc3NOYW1lOiBcInJlYWN0LXBkZl9fVGh1bWJuYWlsX19wYWdlXCIsIF9lbmFibGVSZWdpc3RlclVucmVnaXN0ZXJQYWdlOiBmYWxzZSwgcmVuZGVyQW5ub3RhdGlvbkxheWVyOiBmYWxzZSwgcmVuZGVyVGV4dExheWVyOiBmYWxzZSB9KSkgfSkpO1xufVxuIl0sIm5hbWVzIjpbIl9fcmVzdCIsInMiLCJlIiwidCIsInAiLCJPYmplY3QiLCJwcm90b3R5cGUiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJpbmRleE9mIiwiZ2V0T3duUHJvcGVydHlTeW1ib2xzIiwiaSIsImxlbmd0aCIsInByb3BlcnR5SXNFbnVtZXJhYmxlIiwianN4IiwiX2pzeCIsImNsc3giLCJpbnZhcmlhbnQiLCJQYWdlIiwiaXNQcm92aWRlZCIsInVzZURvY3VtZW50Q29udGV4dCIsIlRodW1ibmFpbCIsInByb3BzIiwiZG9jdW1lbnRDb250ZXh0IiwibWVyZ2VkUHJvcHMiLCJhc3NpZ24iLCJjbGFzc05hbWUiLCJsaW5rU2VydmljZSIsIm9uSXRlbUNsaWNrIiwicGFnZUluZGV4IiwicGFnZUluZGV4UHJvcHMiLCJwYWdlTnVtYmVyIiwicGFnZU51bWJlclByb3BzIiwicGRmIiwib25DbGljayIsImV2ZW50IiwicHJldmVudERlZmF1bHQiLCJnb1RvUGFnZSIsImNsYXNzTmFtZVByb3BzIiwib25JdGVtQ2xpY2tQcm9wcyIsInBhZ2VQcm9wcyIsImhyZWYiLCJ1bmRlZmluZWQiLCJjaGlsZHJlbiIsIl9jbGFzc05hbWUiLCJfZW5hYmxlUmVnaXN0ZXJVbnJlZ2lzdGVyUGFnZSIsInJlbmRlckFubm90YXRpb25MYXllciIsInJlbmRlclRleHRMYXllciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/Thumbnail.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/index.js":
/*!**************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Document: function() { return /* reexport safe */ _Document_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   Outline: function() { return /* reexport safe */ _Outline_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   Page: function() { return /* reexport safe */ _Page_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; },\n/* harmony export */   PasswordResponses: function() { return /* reexport safe */ _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]; },\n/* harmony export */   Thumbnail: function() { return /* reexport safe */ _Thumbnail_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]; },\n/* harmony export */   pdfjs: function() { return /* reexport module object */ pdfjs_dist__WEBPACK_IMPORTED_MODULE_0__; },\n/* harmony export */   useDocumentContext: function() { return /* reexport safe */ _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]; },\n/* harmony export */   useOutlineContext: function() { return /* reexport safe */ _shared_hooks_useOutlineContext_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]; },\n/* harmony export */   usePageContext: function() { return /* reexport safe */ _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pdfjs-dist */ \"(app-pages-browser)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _Document_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Document.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Document.js\");\n/* harmony import */ var _Outline_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Outline.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Outline.js\");\n/* harmony import */ var _Page_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Page.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page.js\");\n/* harmony import */ var _Thumbnail_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Thumbnail.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Thumbnail.js\");\n/* harmony import */ var _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./shared/hooks/useDocumentContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\");\n/* harmony import */ var _shared_hooks_useOutlineContext_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./shared/hooks/useOutlineContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useOutlineContext.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./shared/hooks/usePageContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./PasswordResponses.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/PasswordResponses.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./shared/utils.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n\n\n\n\n\n\n\n\n\n\n(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_1__.displayWorkerWarning)();\npdfjs_dist__WEBPACK_IMPORTED_MODULE_0__.GlobalWorkerOptions.workerSrc = 'pdf.worker.mjs';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFvQztBQUNDO0FBQ0Y7QUFDTjtBQUNVO0FBQytCO0FBQ0Y7QUFDTjtBQUNQO0FBQ0U7QUFDekQsc0VBQW9CO0FBQ3BCLDJEQUF5QjtBQUN1RyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL2luZGV4LmpzPzA4MzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgcGRmanMgZnJvbSAncGRmanMtZGlzdCc7XG5pbXBvcnQgRG9jdW1lbnQgZnJvbSAnLi9Eb2N1bWVudC5qcyc7XG5pbXBvcnQgT3V0bGluZSBmcm9tICcuL091dGxpbmUuanMnO1xuaW1wb3J0IFBhZ2UgZnJvbSAnLi9QYWdlLmpzJztcbmltcG9ydCBUaHVtYm5haWwgZnJvbSAnLi9UaHVtYm5haWwuanMnO1xuaW1wb3J0IHVzZURvY3VtZW50Q29udGV4dCBmcm9tICcuL3NoYXJlZC9ob29rcy91c2VEb2N1bWVudENvbnRleHQuanMnO1xuaW1wb3J0IHVzZU91dGxpbmVDb250ZXh0IGZyb20gJy4vc2hhcmVkL2hvb2tzL3VzZU91dGxpbmVDb250ZXh0LmpzJztcbmltcG9ydCB1c2VQYWdlQ29udGV4dCBmcm9tICcuL3NoYXJlZC9ob29rcy91c2VQYWdlQ29udGV4dC5qcyc7XG5pbXBvcnQgUGFzc3dvcmRSZXNwb25zZXMgZnJvbSAnLi9QYXNzd29yZFJlc3BvbnNlcy5qcyc7XG5pbXBvcnQgeyBkaXNwbGF5V29ya2VyV2FybmluZyB9IGZyb20gJy4vc2hhcmVkL3V0aWxzLmpzJztcbmRpc3BsYXlXb3JrZXJXYXJuaW5nKCk7XG5wZGZqcy5HbG9iYWxXb3JrZXJPcHRpb25zLndvcmtlclNyYyA9ICdwZGYud29ya2VyLm1qcyc7XG5leHBvcnQgeyBwZGZqcywgRG9jdW1lbnQsIE91dGxpbmUsIFBhZ2UsIFRodW1ibmFpbCwgdXNlRG9jdW1lbnRDb250ZXh0LCB1c2VPdXRsaW5lQ29udGV4dCwgdXNlUGFnZUNvbnRleHQsIFBhc3N3b3JkUmVzcG9uc2VzLCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADING_PATTERN: function() { return /* binding */ HEADING_PATTERN; },\n/* harmony export */   PDF_ROLE_TO_HTML_ROLE: function() { return /* binding */ PDF_ROLE_TO_HTML_ROLE; }\n/* harmony export */ });\n// From pdfjs-dist/lib/web/struct_tree_layer_builder.js\nconst PDF_ROLE_TO_HTML_ROLE = {\n    // Document level structure types\n    Document: null, // There's a \"document\" role, but it doesn't make sense here.\n    DocumentFragment: null,\n    // Grouping level structure types\n    Part: 'group',\n    Sect: 'group', // XXX: There's a \"section\" role, but it's abstract.\n    Div: 'group',\n    Aside: 'note',\n    NonStruct: 'none',\n    // Block level structure types\n    P: null,\n    // H<n>,\n    H: 'heading',\n    Title: null,\n    FENote: 'note',\n    // Sub-block level structure type\n    Sub: 'group',\n    // General inline level structure types\n    Lbl: null,\n    Span: null,\n    Em: null,\n    Strong: null,\n    Link: 'link',\n    Annot: 'note',\n    Form: 'form',\n    // Ruby and Warichu structure types\n    Ruby: null,\n    RB: null,\n    RT: null,\n    RP: null,\n    Warichu: null,\n    WT: null,\n    WP: null,\n    // List standard structure types\n    L: 'list',\n    LI: 'listitem',\n    LBody: null,\n    // Table standard structure types\n    Table: 'table',\n    TR: 'row',\n    TH: 'columnheader',\n    TD: 'cell',\n    THead: 'columnheader',\n    TBody: null,\n    TFoot: null,\n    // Standard structure type Caption\n    Caption: null,\n    // Standard structure type Figure\n    Figure: 'figure',\n    // Standard structure type Formula\n    Formula: null,\n    // standard structure type Artifact\n    Artifact: null,\n};\nconst HEADING_PATTERN = /^H(\\d+)$/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vc2hhcmVkL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanM/NzUzZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGcm9tIHBkZmpzLWRpc3QvbGliL3dlYi9zdHJ1Y3RfdHJlZV9sYXllcl9idWlsZGVyLmpzXG5leHBvcnQgY29uc3QgUERGX1JPTEVfVE9fSFRNTF9ST0xFID0ge1xuICAgIC8vIERvY3VtZW50IGxldmVsIHN0cnVjdHVyZSB0eXBlc1xuICAgIERvY3VtZW50OiBudWxsLCAvLyBUaGVyZSdzIGEgXCJkb2N1bWVudFwiIHJvbGUsIGJ1dCBpdCBkb2Vzbid0IG1ha2Ugc2Vuc2UgaGVyZS5cbiAgICBEb2N1bWVudEZyYWdtZW50OiBudWxsLFxuICAgIC8vIEdyb3VwaW5nIGxldmVsIHN0cnVjdHVyZSB0eXBlc1xuICAgIFBhcnQ6ICdncm91cCcsXG4gICAgU2VjdDogJ2dyb3VwJywgLy8gWFhYOiBUaGVyZSdzIGEgXCJzZWN0aW9uXCIgcm9sZSwgYnV0IGl0J3MgYWJzdHJhY3QuXG4gICAgRGl2OiAnZ3JvdXAnLFxuICAgIEFzaWRlOiAnbm90ZScsXG4gICAgTm9uU3RydWN0OiAnbm9uZScsXG4gICAgLy8gQmxvY2sgbGV2ZWwgc3RydWN0dXJlIHR5cGVzXG4gICAgUDogbnVsbCxcbiAgICAvLyBIPG4+LFxuICAgIEg6ICdoZWFkaW5nJyxcbiAgICBUaXRsZTogbnVsbCxcbiAgICBGRU5vdGU6ICdub3RlJyxcbiAgICAvLyBTdWItYmxvY2sgbGV2ZWwgc3RydWN0dXJlIHR5cGVcbiAgICBTdWI6ICdncm91cCcsXG4gICAgLy8gR2VuZXJhbCBpbmxpbmUgbGV2ZWwgc3RydWN0dXJlIHR5cGVzXG4gICAgTGJsOiBudWxsLFxuICAgIFNwYW46IG51bGwsXG4gICAgRW06IG51bGwsXG4gICAgU3Ryb25nOiBudWxsLFxuICAgIExpbms6ICdsaW5rJyxcbiAgICBBbm5vdDogJ25vdGUnLFxuICAgIEZvcm06ICdmb3JtJyxcbiAgICAvLyBSdWJ5IGFuZCBXYXJpY2h1IHN0cnVjdHVyZSB0eXBlc1xuICAgIFJ1Ynk6IG51bGwsXG4gICAgUkI6IG51bGwsXG4gICAgUlQ6IG51bGwsXG4gICAgUlA6IG51bGwsXG4gICAgV2FyaWNodTogbnVsbCxcbiAgICBXVDogbnVsbCxcbiAgICBXUDogbnVsbCxcbiAgICAvLyBMaXN0IHN0YW5kYXJkIHN0cnVjdHVyZSB0eXBlc1xuICAgIEw6ICdsaXN0JyxcbiAgICBMSTogJ2xpc3RpdGVtJyxcbiAgICBMQm9keTogbnVsbCxcbiAgICAvLyBUYWJsZSBzdGFuZGFyZCBzdHJ1Y3R1cmUgdHlwZXNcbiAgICBUYWJsZTogJ3RhYmxlJyxcbiAgICBUUjogJ3JvdycsXG4gICAgVEg6ICdjb2x1bW5oZWFkZXInLFxuICAgIFREOiAnY2VsbCcsXG4gICAgVEhlYWQ6ICdjb2x1bW5oZWFkZXInLFxuICAgIFRCb2R5OiBudWxsLFxuICAgIFRGb290OiBudWxsLFxuICAgIC8vIFN0YW5kYXJkIHN0cnVjdHVyZSB0eXBlIENhcHRpb25cbiAgICBDYXB0aW9uOiBudWxsLFxuICAgIC8vIFN0YW5kYXJkIHN0cnVjdHVyZSB0eXBlIEZpZ3VyZVxuICAgIEZpZ3VyZTogJ2ZpZ3VyZScsXG4gICAgLy8gU3RhbmRhcmQgc3RydWN0dXJlIHR5cGUgRm9ybXVsYVxuICAgIEZvcm11bGE6IG51bGwsXG4gICAgLy8gc3RhbmRhcmQgc3RydWN0dXJlIHR5cGUgQXJ0aWZhY3RcbiAgICBBcnRpZmFjdDogbnVsbCxcbn07XG5leHBvcnQgY29uc3QgSEVBRElOR19QQVRURVJOID0gL15IKFxcZCspJC87XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/constants.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useCachedValue.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/useCachedValue.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useCachedValue; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nfunction useCachedValue(getter) {\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n    const currentValue = ref.current;\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.isDefined)(currentValue)) {\n        return ()=>currentValue;\n    }\n    return ()=>{\n        const value = getter();\n        ref.current = value;\n        return value;\n    };\n}\n_s(useCachedValue, \"QMBuJFIdzLIeqBcFwhMf246mjOM=\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vc2hhcmVkL2hvb2tzL3VzZUNhY2hlZFZhbHVlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFDK0I7QUFDUztBQUN6QixTQUFTRSxlQUFlQyxNQUFNOztJQUN6QyxNQUFNQyxNQUFNSiw2Q0FBTUEsQ0FBQ0s7SUFDbkIsTUFBTUMsZUFBZUYsSUFBSUcsT0FBTztJQUNoQyxJQUFJTixvREFBU0EsQ0FBQ0ssZUFBZTtRQUN6QixPQUFPLElBQU1BO0lBQ2pCO0lBQ0EsT0FBTztRQUNILE1BQU1FLFFBQVFMO1FBQ2RDLElBQUlHLE9BQU8sR0FBR0M7UUFDZCxPQUFPQTtJQUNYO0FBQ0o7R0FYd0JOIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vc2hhcmVkL2hvb2tzL3VzZUNhY2hlZFZhbHVlLmpzPzZkZGMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuaW1wb3J0IHsgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgaXNEZWZpbmVkIH0gZnJvbSAnLi4vdXRpbHMuanMnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlQ2FjaGVkVmFsdWUoZ2V0dGVyKSB7XG4gICAgY29uc3QgcmVmID0gdXNlUmVmKHVuZGVmaW5lZCk7XG4gICAgY29uc3QgY3VycmVudFZhbHVlID0gcmVmLmN1cnJlbnQ7XG4gICAgaWYgKGlzRGVmaW5lZChjdXJyZW50VmFsdWUpKSB7XG4gICAgICAgIHJldHVybiAoKSA9PiBjdXJyZW50VmFsdWU7XG4gICAgfVxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGNvbnN0IHZhbHVlID0gZ2V0dGVyKCk7XG4gICAgICAgIHJlZi5jdXJyZW50ID0gdmFsdWU7XG4gICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9O1xufVxuIl0sIm5hbWVzIjpbInVzZVJlZiIsImlzRGVmaW5lZCIsInVzZUNhY2hlZFZhbHVlIiwiZ2V0dGVyIiwicmVmIiwidW5kZWZpbmVkIiwiY3VycmVudFZhbHVlIiwiY3VycmVudCIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useCachedValue.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useDocumentContext; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _DocumentContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../DocumentContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/DocumentContext.js\");\n\n\nfunction useDocumentContext() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_DocumentContext_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vc2hhcmVkL2hvb2tzL3VzZURvY3VtZW50Q29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUM7QUFDb0I7QUFDeEM7QUFDZixXQUFXLGlEQUFVLENBQUMsMkRBQWU7QUFDckMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXBkZi9kaXN0L2VzbS9zaGFyZWQvaG9va3MvdXNlRG9jdW1lbnRDb250ZXh0LmpzP2I5YTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBEb2N1bWVudENvbnRleHQgZnJvbSAnLi4vLi4vRG9jdW1lbnRDb250ZXh0LmpzJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZURvY3VtZW50Q29udGV4dCgpIHtcbiAgICByZXR1cm4gdXNlQ29udGV4dChEb2N1bWVudENvbnRleHQpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useOutlineContext.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/useOutlineContext.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useOutlineContext; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _OutlineContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../OutlineContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/OutlineContext.js\");\n\n\nfunction useOutlineContext() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_OutlineContext_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vc2hhcmVkL2hvb2tzL3VzZU91dGxpbmVDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFtQztBQUNrQjtBQUN0QztBQUNmLFdBQVcsaURBQVUsQ0FBQywwREFBYztBQUNwQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9ob29rcy91c2VPdXRsaW5lQ29udGV4dC5qcz8zOGNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgT3V0bGluZUNvbnRleHQgZnJvbSAnLi4vLi4vT3V0bGluZUNvbnRleHQuanMnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlT3V0bGluZUNvbnRleHQoKSB7XG4gICAgcmV0dXJuIHVzZUNvbnRleHQoT3V0bGluZUNvbnRleHQpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useOutlineContext.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ usePageContext; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _PageContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../PageContext.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/PageContext.js\");\n\n\nfunction usePageContext() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_PageContext_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vc2hhcmVkL2hvb2tzL3VzZVBhZ2VDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFtQztBQUNZO0FBQ2hDO0FBQ2YsV0FBVyxpREFBVSxDQUFDLHVEQUFXO0FBQ2pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vc2hhcmVkL2hvb2tzL3VzZVBhZ2VDb250ZXh0LmpzPzc1MzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBQYWdlQ29udGV4dCBmcm9tICcuLi8uLi9QYWdlQ29udGV4dC5qcyc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VQYWdlQ29udGV4dCgpIHtcbiAgICByZXR1cm4gdXNlQ29udGV4dChQYWdlQ29udGV4dCk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useResolver; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction reducer(state, action) {\n    switch (action.type) {\n        case 'RESOLVE':\n            return { value: action.value, error: undefined };\n        case 'REJECT':\n            return { value: false, error: action.error };\n        case 'RESET':\n            return { value: undefined, error: undefined };\n        default:\n            return state;\n    }\n}\nfunction useResolver() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)((reducer), { value: undefined, error: undefined });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vc2hhcmVkL2hvb2tzL3VzZVJlc29sdmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1DO0FBQ25DO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNlO0FBQ2YsV0FBVyxpREFBVSxjQUFjLG9DQUFvQztBQUN2RSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9ob29rcy91c2VSZXNvbHZlci5qcz83NmZkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVJlZHVjZXIgfSBmcm9tICdyZWFjdCc7XG5mdW5jdGlvbiByZWR1Y2VyKHN0YXRlLCBhY3Rpb24pIHtcbiAgICBzd2l0Y2ggKGFjdGlvbi50eXBlKSB7XG4gICAgICAgIGNhc2UgJ1JFU09MVkUnOlxuICAgICAgICAgICAgcmV0dXJuIHsgdmFsdWU6IGFjdGlvbi52YWx1ZSwgZXJyb3I6IHVuZGVmaW5lZCB9O1xuICAgICAgICBjYXNlICdSRUpFQ1QnOlxuICAgICAgICAgICAgcmV0dXJuIHsgdmFsdWU6IGZhbHNlLCBlcnJvcjogYWN0aW9uLmVycm9yIH07XG4gICAgICAgIGNhc2UgJ1JFU0VUJzpcbiAgICAgICAgICAgIHJldHVybiB7IHZhbHVlOiB1bmRlZmluZWQsIGVycm9yOiB1bmRlZmluZWQgfTtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJldHVybiBzdGF0ZTtcbiAgICB9XG59XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VSZXNvbHZlcigpIHtcbiAgICByZXR1cm4gdXNlUmVkdWNlcigocmVkdWNlciksIHsgdmFsdWU6IHVuZGVmaW5lZCwgZXJyb3I6IHVuZGVmaW5lZCB9KTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAttributes: function() { return /* binding */ getAttributes; },\n/* harmony export */   getBaseAttributes: function() { return /* binding */ getBaseAttributes; },\n/* harmony export */   getRoleAttributes: function() { return /* binding */ getRoleAttributes; },\n/* harmony export */   isPdfRole: function() { return /* binding */ isPdfRole; },\n/* harmony export */   isStructTreeNode: function() { return /* binding */ isStructTreeNode; },\n/* harmony export */   isStructTreeNodeWithOnlyContentChild: function() { return /* binding */ isStructTreeNodeWithOnlyContentChild; }\n/* harmony export */ });\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/constants.js\");\n\nfunction isPdfRole(role) {\n    return role in _constants_js__WEBPACK_IMPORTED_MODULE_0__.PDF_ROLE_TO_HTML_ROLE;\n}\nfunction isStructTreeNode(node) {\n    return 'children' in node;\n}\nfunction isStructTreeNodeWithOnlyContentChild(node) {\n    if (!isStructTreeNode(node)) {\n        return false;\n    }\n    return node.children.length === 1 && 0 in node.children && 'id' in node.children[0];\n}\nfunction getRoleAttributes(node) {\n    const attributes = {};\n    if (isStructTreeNode(node)) {\n        const { role } = node;\n        const matches = role.match(_constants_js__WEBPACK_IMPORTED_MODULE_0__.HEADING_PATTERN);\n        if (matches) {\n            attributes.role = 'heading';\n            attributes['aria-level'] = Number(matches[1]);\n        }\n        else if (isPdfRole(role)) {\n            const htmlRole = _constants_js__WEBPACK_IMPORTED_MODULE_0__.PDF_ROLE_TO_HTML_ROLE[role];\n            if (htmlRole) {\n                attributes.role = htmlRole;\n            }\n        }\n    }\n    return attributes;\n}\nfunction getBaseAttributes(node) {\n    const attributes = {};\n    if (isStructTreeNode(node)) {\n        if (node.alt !== undefined) {\n            attributes['aria-label'] = node.alt;\n        }\n        if (node.lang !== undefined) {\n            attributes.lang = node.lang;\n        }\n        if (isStructTreeNodeWithOnlyContentChild(node)) {\n            const [child] = node.children;\n            if (child) {\n                const childAttributes = getBaseAttributes(child);\n                return Object.assign(Object.assign({}, attributes), childAttributes);\n            }\n        }\n    }\n    else {\n        if ('id' in node) {\n            attributes['aria-owns'] = node.id;\n        }\n    }\n    return attributes;\n}\nfunction getAttributes(node) {\n    if (!node) {\n        return null;\n    }\n    return Object.assign(Object.assign({}, getRoleAttributes(node)), getBaseAttributes(node));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelRunningTask: function() { return /* binding */ cancelRunningTask; },\n/* harmony export */   dataURItoByteString: function() { return /* binding */ dataURItoByteString; },\n/* harmony export */   displayCORSWarning: function() { return /* binding */ displayCORSWarning; },\n/* harmony export */   displayWorkerWarning: function() { return /* binding */ displayWorkerWarning; },\n/* harmony export */   getDevicePixelRatio: function() { return /* binding */ getDevicePixelRatio; },\n/* harmony export */   isArrayBuffer: function() { return /* binding */ isArrayBuffer; },\n/* harmony export */   isBlob: function() { return /* binding */ isBlob; },\n/* harmony export */   isBrowser: function() { return /* binding */ isBrowser; },\n/* harmony export */   isCancelException: function() { return /* binding */ isCancelException; },\n/* harmony export */   isDataURI: function() { return /* binding */ isDataURI; },\n/* harmony export */   isDefined: function() { return /* binding */ isDefined; },\n/* harmony export */   isLocalFileSystem: function() { return /* binding */ isLocalFileSystem; },\n/* harmony export */   isProvided: function() { return /* binding */ isProvided; },\n/* harmony export */   isString: function() { return /* binding */ isString; },\n/* harmony export */   loadFromFile: function() { return /* binding */ loadFromFile; },\n/* harmony export */   makePageCallback: function() { return /* binding */ makePageCallback; }\n/* harmony export */ });\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tiny-invariant */ \"(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! warning */ \"(app-pages-browser)/./node_modules/warning/warning.js\");\n\n\n/**\n * Checks if we're running in a browser environment.\n */\nconst isBrowser = typeof window !== 'undefined';\n/**\n * Checks whether we're running from a local file system.\n */\nconst isLocalFileSystem = isBrowser && window.location.protocol === 'file:';\n/**\n * Checks whether a variable is defined.\n *\n * @param {*} variable Variable to check\n */\nfunction isDefined(variable) {\n    return typeof variable !== 'undefined';\n}\n/**\n * Checks whether a variable is defined and not null.\n *\n * @param {*} variable Variable to check\n */\nfunction isProvided(variable) {\n    return isDefined(variable) && variable !== null;\n}\n/**\n * Checks whether a variable provided is a string.\n *\n * @param {*} variable Variable to check\n */\nfunction isString(variable) {\n    return typeof variable === 'string';\n}\n/**\n * Checks whether a variable provided is an ArrayBuffer.\n *\n * @param {*} variable Variable to check\n */\nfunction isArrayBuffer(variable) {\n    return variable instanceof ArrayBuffer;\n}\n/**\n * Checks whether a variable provided is a Blob.\n *\n * @param {*} variable Variable to check\n */\nfunction isBlob(variable) {\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(isBrowser, 'isBlob can only be used in a browser environment');\n    return variable instanceof Blob;\n}\n/**\n * Checks whether a variable provided is a data URI.\n *\n * @param {*} variable String to check\n */\nfunction isDataURI(variable) {\n    return isString(variable) && /^data:/.test(variable);\n}\nfunction dataURItoByteString(dataURI) {\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(isDataURI(dataURI), 'Invalid data URI.');\n    const [headersString = '', dataString = ''] = dataURI.split(',');\n    const headers = headersString.split(';');\n    if (headers.indexOf('base64') !== -1) {\n        return atob(dataString);\n    }\n    return unescape(dataString);\n}\nfunction getDevicePixelRatio() {\n    return (isBrowser && window.devicePixelRatio) || 1;\n}\nconst allowFileAccessFromFilesTip = 'On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.';\nfunction displayCORSWarning() {\n    warning__WEBPACK_IMPORTED_MODULE_1__(!isLocalFileSystem, `Loading PDF as base64 strings/URLs may not work on protocols other than HTTP/HTTPS. ${allowFileAccessFromFilesTip}`);\n}\nfunction displayWorkerWarning() {\n    warning__WEBPACK_IMPORTED_MODULE_1__(!isLocalFileSystem, `Loading PDF.js worker may not work on protocols other than HTTP/HTTPS. ${allowFileAccessFromFilesTip}`);\n}\nfunction cancelRunningTask(runningTask) {\n    if (runningTask === null || runningTask === void 0 ? void 0 : runningTask.cancel)\n        runningTask.cancel();\n}\nfunction makePageCallback(page, scale) {\n    Object.defineProperty(page, 'width', {\n        get() {\n            return this.view[2] * scale;\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'height', {\n        get() {\n            return this.view[3] * scale;\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'originalWidth', {\n        get() {\n            return this.view[2];\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'originalHeight', {\n        get() {\n            return this.view[3];\n        },\n        configurable: true,\n    });\n    return page;\n}\nfunction isCancelException(error) {\n    return error.name === 'RenderingCancelledException';\n}\nfunction loadFromFile(file) {\n    return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = () => {\n            if (!reader.result) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            resolve(reader.result);\n        };\n        reader.onerror = (event) => {\n            if (!event.target) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            const { error } = event.target;\n            if (!error) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            switch (error.code) {\n                case error.NOT_FOUND_ERR:\n                    return reject(new Error('Error while reading a file: File not found.'));\n                case error.SECURITY_ERR:\n                    return reject(new Error('Error while reading a file: Security error.'));\n                case error.ABORT_ERR:\n                    return reject(new Error('Error while reading a file: Aborted.'));\n                default:\n                    return reject(new Error('Error while reading a file.'));\n            }\n        };\n        reader.readAsArrayBuffer(file);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js":
/*!****************************************************************!*\
  !*** ./node_modules/tiny-invariant/dist/esm/tiny-invariant.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ invariant; }\n/* harmony export */ });\nvar isProduction = \"development\" === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy90aW55LWludmFyaWFudC9kaXN0L2VzbS90aW55LWludmFyaWFudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsbUJBQW1CLGFBQW9CO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3RpbnktaW52YXJpYW50L2Rpc3QvZXNtL3RpbnktaW52YXJpYW50LmpzP2YyMjkiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGlzUHJvZHVjdGlvbiA9IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbic7XG52YXIgcHJlZml4ID0gJ0ludmFyaWFudCBmYWlsZWQnO1xuZnVuY3Rpb24gaW52YXJpYW50KGNvbmRpdGlvbiwgbWVzc2FnZSkge1xuICAgIGlmIChjb25kaXRpb24pIHtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBpZiAoaXNQcm9kdWN0aW9uKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihwcmVmaXgpO1xuICAgIH1cbiAgICB2YXIgcHJvdmlkZWQgPSB0eXBlb2YgbWVzc2FnZSA9PT0gJ2Z1bmN0aW9uJyA/IG1lc3NhZ2UoKSA6IG1lc3NhZ2U7XG4gICAgdmFyIHZhbHVlID0gcHJvdmlkZWQgPyBcIlwiLmNvbmNhdChwcmVmaXgsIFwiOiBcIikuY29uY2F0KHByb3ZpZGVkKSA6IHByZWZpeDtcbiAgICB0aHJvdyBuZXcgRXJyb3IodmFsdWUpO1xufVxuXG5leHBvcnQgeyBpbnZhcmlhbnQgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\n"));

/***/ })

}]);