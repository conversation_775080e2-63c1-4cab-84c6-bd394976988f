/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tmp";
exports.ids = ["vendor-chunks/tmp"];
exports.modules = {

/***/ "(ssr)/./node_modules/tmp/lib/tmp.js":
/*!*************************************!*\
  !*** ./node_modules/tmp/lib/tmp.js ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * Tmp\n *\n * Copyright (c) 2011-2017 KARASZI Istvan <<EMAIL>>\n *\n * MIT Licensed\n */\n\n/*\n * Module dependencies.\n */\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst os = __webpack_require__(/*! os */ \"os\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst _c = { fs: fs.constants, os: os.constants };\n\n/*\n * The working inner variables.\n */\nconst // the random characters to choose from\n  RANDOM_CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n  TEMPLATE_PATTERN = /XXXXXX/,\n  DEFAULT_TRIES = 3,\n  CREATE_FLAGS = (_c.O_CREAT || _c.fs.O_CREAT) | (_c.O_EXCL || _c.fs.O_EXCL) | (_c.O_RDWR || _c.fs.O_RDWR),\n  // constants are off on the windows platform and will not match the actual errno codes\n  IS_WIN32 = os.platform() === 'win32',\n  EBADF = _c.EBADF || _c.os.errno.EBADF,\n  ENOENT = _c.ENOENT || _c.os.errno.ENOENT,\n  DIR_MODE = 0o700 /* 448 */,\n  FILE_MODE = 0o600 /* 384 */,\n  EXIT = 'exit',\n  // this will hold the objects need to be removed on exit\n  _removeObjects = [],\n  // API change in fs.rmdirSync leads to error when passing in a second parameter, e.g. the callback\n  FN_RMDIR_SYNC = fs.rmdirSync.bind(fs);\n\nlet _gracefulCleanup = false;\n\n/**\n * Recursively remove a directory and its contents.\n *\n * @param {string} dirPath path of directory to remove\n * @param {Function} callback\n * @private\n */\nfunction rimraf(dirPath, callback) {\n  return fs.rm(dirPath, { recursive: true }, callback);\n}\n\n/**\n * Recursively remove a directory and its contents, synchronously.\n *\n * @param {string} dirPath path of directory to remove\n * @private\n */\nfunction FN_RIMRAF_SYNC(dirPath) {\n  return fs.rmSync(dirPath, { recursive: true });\n}\n\n/**\n * Gets a temporary file name.\n *\n * @param {(Options|tmpNameCallback)} options options or callback\n * @param {?tmpNameCallback} callback the callback function\n */\nfunction tmpName(options, callback) {\n  const args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  _assertAndSanitizeOptions(opts, function (err, sanitizedOptions) {\n    if (err) return cb(err);\n\n    let tries = sanitizedOptions.tries;\n    (function _getUniqueName() {\n      try {\n        const name = _generateTmpName(sanitizedOptions);\n\n        // check whether the path exists then retry if needed\n        fs.stat(name, function (err) {\n          /* istanbul ignore else */\n          if (!err) {\n            /* istanbul ignore else */\n            if (tries-- > 0) return _getUniqueName();\n\n            return cb(new Error('Could not get a unique tmp filename, max tries reached ' + name));\n          }\n\n          cb(null, name);\n        });\n      } catch (err) {\n        cb(err);\n      }\n    })();\n  });\n}\n\n/**\n * Synchronous version of tmpName.\n *\n * @param {Object} options\n * @returns {string} the generated random name\n * @throws {Error} if the options are invalid or could not generate a filename\n */\nfunction tmpNameSync(options) {\n  const args = _parseArguments(options),\n    opts = args[0];\n\n  const sanitizedOptions = _assertAndSanitizeOptionsSync(opts);\n\n  let tries = sanitizedOptions.tries;\n  do {\n    const name = _generateTmpName(sanitizedOptions);\n    try {\n      fs.statSync(name);\n    } catch (e) {\n      return name;\n    }\n  } while (tries-- > 0);\n\n  throw new Error('Could not get a unique tmp filename, max tries reached');\n}\n\n/**\n * Creates and opens a temporary file.\n *\n * @param {(Options|null|undefined|fileCallback)} options the config options or the callback function or null or undefined\n * @param {?fileCallback} callback\n */\nfunction file(options, callback) {\n  const args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  // gets a temporary filename\n  tmpName(opts, function _tmpNameCreated(err, name) {\n    /* istanbul ignore else */\n    if (err) return cb(err);\n\n    // create and open the file\n    fs.open(name, CREATE_FLAGS, opts.mode || FILE_MODE, function _fileCreated(err, fd) {\n      /* istanbu ignore else */\n      if (err) return cb(err);\n\n      if (opts.discardDescriptor) {\n        return fs.close(fd, function _discardCallback(possibleErr) {\n          // the chance of getting an error on close here is rather low and might occur in the most edgiest cases only\n          return cb(possibleErr, name, undefined, _prepareTmpFileRemoveCallback(name, -1, opts, false));\n        });\n      } else {\n        // detachDescriptor passes the descriptor whereas discardDescriptor closes it, either way, we no longer care\n        // about the descriptor\n        const discardOrDetachDescriptor = opts.discardDescriptor || opts.detachDescriptor;\n        cb(null, name, fd, _prepareTmpFileRemoveCallback(name, discardOrDetachDescriptor ? -1 : fd, opts, false));\n      }\n    });\n  });\n}\n\n/**\n * Synchronous version of file.\n *\n * @param {Options} options\n * @returns {FileSyncObject} object consists of name, fd and removeCallback\n * @throws {Error} if cannot create a file\n */\nfunction fileSync(options) {\n  const args = _parseArguments(options),\n    opts = args[0];\n\n  const discardOrDetachDescriptor = opts.discardDescriptor || opts.detachDescriptor;\n  const name = tmpNameSync(opts);\n  let fd = fs.openSync(name, CREATE_FLAGS, opts.mode || FILE_MODE);\n  /* istanbul ignore else */\n  if (opts.discardDescriptor) {\n    fs.closeSync(fd);\n    fd = undefined;\n  }\n\n  return {\n    name: name,\n    fd: fd,\n    removeCallback: _prepareTmpFileRemoveCallback(name, discardOrDetachDescriptor ? -1 : fd, opts, true)\n  };\n}\n\n/**\n * Creates a temporary directory.\n *\n * @param {(Options|dirCallback)} options the options or the callback function\n * @param {?dirCallback} callback\n */\nfunction dir(options, callback) {\n  const args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  // gets a temporary filename\n  tmpName(opts, function _tmpNameCreated(err, name) {\n    /* istanbul ignore else */\n    if (err) return cb(err);\n\n    // create the directory\n    fs.mkdir(name, opts.mode || DIR_MODE, function _dirCreated(err) {\n      /* istanbul ignore else */\n      if (err) return cb(err);\n\n      cb(null, name, _prepareTmpDirRemoveCallback(name, opts, false));\n    });\n  });\n}\n\n/**\n * Synchronous version of dir.\n *\n * @param {Options} options\n * @returns {DirSyncObject} object consists of name and removeCallback\n * @throws {Error} if it cannot create a directory\n */\nfunction dirSync(options) {\n  const args = _parseArguments(options),\n    opts = args[0];\n\n  const name = tmpNameSync(opts);\n  fs.mkdirSync(name, opts.mode || DIR_MODE);\n\n  return {\n    name: name,\n    removeCallback: _prepareTmpDirRemoveCallback(name, opts, true)\n  };\n}\n\n/**\n * Removes files asynchronously.\n *\n * @param {Object} fdPath\n * @param {Function} next\n * @private\n */\nfunction _removeFileAsync(fdPath, next) {\n  const _handler = function (err) {\n    if (err && !_isENOENT(err)) {\n      // reraise any unanticipated error\n      return next(err);\n    }\n    next();\n  };\n\n  if (0 <= fdPath[0])\n    fs.close(fdPath[0], function () {\n      fs.unlink(fdPath[1], _handler);\n    });\n  else fs.unlink(fdPath[1], _handler);\n}\n\n/**\n * Removes files synchronously.\n *\n * @param {Object} fdPath\n * @private\n */\nfunction _removeFileSync(fdPath) {\n  let rethrownException = null;\n  try {\n    if (0 <= fdPath[0]) fs.closeSync(fdPath[0]);\n  } catch (e) {\n    // reraise any unanticipated error\n    if (!_isEBADF(e) && !_isENOENT(e)) throw e;\n  } finally {\n    try {\n      fs.unlinkSync(fdPath[1]);\n    } catch (e) {\n      // reraise any unanticipated error\n      if (!_isENOENT(e)) rethrownException = e;\n    }\n  }\n  if (rethrownException !== null) {\n    throw rethrownException;\n  }\n}\n\n/**\n * Prepares the callback for removal of the temporary file.\n *\n * Returns either a sync callback or a async callback depending on whether\n * fileSync or file was called, which is expressed by the sync parameter.\n *\n * @param {string} name the path of the file\n * @param {number} fd file descriptor\n * @param {Object} opts\n * @param {boolean} sync\n * @returns {fileCallback | fileCallbackSync}\n * @private\n */\nfunction _prepareTmpFileRemoveCallback(name, fd, opts, sync) {\n  const removeCallbackSync = _prepareRemoveCallback(_removeFileSync, [fd, name], sync);\n  const removeCallback = _prepareRemoveCallback(_removeFileAsync, [fd, name], sync, removeCallbackSync);\n\n  if (!opts.keep) _removeObjects.unshift(removeCallbackSync);\n\n  return sync ? removeCallbackSync : removeCallback;\n}\n\n/**\n * Prepares the callback for removal of the temporary directory.\n *\n * Returns either a sync callback or a async callback depending on whether\n * tmpFileSync or tmpFile was called, which is expressed by the sync parameter.\n *\n * @param {string} name\n * @param {Object} opts\n * @param {boolean} sync\n * @returns {Function} the callback\n * @private\n */\nfunction _prepareTmpDirRemoveCallback(name, opts, sync) {\n  const removeFunction = opts.unsafeCleanup ? rimraf : fs.rmdir.bind(fs);\n  const removeFunctionSync = opts.unsafeCleanup ? FN_RIMRAF_SYNC : FN_RMDIR_SYNC;\n  const removeCallbackSync = _prepareRemoveCallback(removeFunctionSync, name, sync);\n  const removeCallback = _prepareRemoveCallback(removeFunction, name, sync, removeCallbackSync);\n  if (!opts.keep) _removeObjects.unshift(removeCallbackSync);\n\n  return sync ? removeCallbackSync : removeCallback;\n}\n\n/**\n * Creates a guarded function wrapping the removeFunction call.\n *\n * The cleanup callback is save to be called multiple times.\n * Subsequent invocations will be ignored.\n *\n * @param {Function} removeFunction\n * @param {string} fileOrDirName\n * @param {boolean} sync\n * @param {cleanupCallbackSync?} cleanupCallbackSync\n * @returns {cleanupCallback | cleanupCallbackSync}\n * @private\n */\nfunction _prepareRemoveCallback(removeFunction, fileOrDirName, sync, cleanupCallbackSync) {\n  let called = false;\n\n  // if sync is true, the next parameter will be ignored\n  return function _cleanupCallback(next) {\n    /* istanbul ignore else */\n    if (!called) {\n      // remove cleanupCallback from cache\n      const toRemove = cleanupCallbackSync || _cleanupCallback;\n      const index = _removeObjects.indexOf(toRemove);\n      /* istanbul ignore else */\n      if (index >= 0) _removeObjects.splice(index, 1);\n\n      called = true;\n      if (sync || removeFunction === FN_RMDIR_SYNC || removeFunction === FN_RIMRAF_SYNC) {\n        return removeFunction(fileOrDirName);\n      } else {\n        return removeFunction(fileOrDirName, next || function () {});\n      }\n    }\n  };\n}\n\n/**\n * The garbage collector.\n *\n * @private\n */\nfunction _garbageCollector() {\n  /* istanbul ignore else */\n  if (!_gracefulCleanup) return;\n\n  // the function being called removes itself from _removeObjects,\n  // loop until _removeObjects is empty\n  while (_removeObjects.length) {\n    try {\n      _removeObjects[0]();\n    } catch (e) {\n      // already removed?\n    }\n  }\n}\n\n/**\n * Random name generator based on crypto.\n * Adapted from http://blog.tompawlak.org/how-to-generate-random-values-nodejs-javascript\n *\n * @param {number} howMany\n * @returns {string} the generated random name\n * @private\n */\nfunction _randomChars(howMany) {\n  let value = [],\n    rnd = null;\n\n  // make sure that we do not fail because we ran out of entropy\n  try {\n    rnd = crypto.randomBytes(howMany);\n  } catch (e) {\n    rnd = crypto.pseudoRandomBytes(howMany);\n  }\n\n  for (let i = 0; i < howMany; i++) {\n    value.push(RANDOM_CHARS[rnd[i] % RANDOM_CHARS.length]);\n  }\n\n  return value.join('');\n}\n\n/**\n * Checks whether the `obj` parameter is defined or not.\n *\n * @param {Object} obj\n * @returns {boolean} true if the object is undefined\n * @private\n */\nfunction _isUndefined(obj) {\n  return typeof obj === 'undefined';\n}\n\n/**\n * Parses the function arguments.\n *\n * This function helps to have optional arguments.\n *\n * @param {(Options|null|undefined|Function)} options\n * @param {?Function} callback\n * @returns {Array} parsed arguments\n * @private\n */\nfunction _parseArguments(options, callback) {\n  /* istanbul ignore else */\n  if (typeof options === 'function') {\n    return [{}, options];\n  }\n\n  /* istanbul ignore else */\n  if (_isUndefined(options)) {\n    return [{}, callback];\n  }\n\n  // copy options so we do not leak the changes we make internally\n  const actualOptions = {};\n  for (const key of Object.getOwnPropertyNames(options)) {\n    actualOptions[key] = options[key];\n  }\n\n  return [actualOptions, callback];\n}\n\n/**\n * Resolve the specified path name in respect to tmpDir.\n *\n * The specified name might include relative path components, e.g. ../\n * so we need to resolve in order to be sure that is is located inside tmpDir\n *\n * @private\n */\nfunction _resolvePath(name, tmpDir, cb) {\n  const pathToResolve = path.isAbsolute(name) ? name : path.join(tmpDir, name);\n\n  fs.stat(pathToResolve, function (err) {\n    if (err) {\n      fs.realpath(path.dirname(pathToResolve), function (err, parentDir) {\n        if (err) return cb(err);\n\n        cb(null, path.join(parentDir, path.basename(pathToResolve)));\n      });\n    } else {\n      fs.realpath(pathToResolve, cb);\n    }\n  });\n}\n\n/**\n * Resolve the specified path name in respect to tmpDir.\n *\n * The specified name might include relative path components, e.g. ../\n * so we need to resolve in order to be sure that is is located inside tmpDir\n *\n * @private\n */\nfunction _resolvePathSync(name, tmpDir) {\n  const pathToResolve = path.isAbsolute(name) ? name : path.join(tmpDir, name);\n\n  try {\n    fs.statSync(pathToResolve);\n    return fs.realpathSync(pathToResolve);\n  } catch (_err) {\n    const parentDir = fs.realpathSync(path.dirname(pathToResolve));\n\n    return path.join(parentDir, path.basename(pathToResolve));\n  }\n}\n\n/**\n * Generates a new temporary name.\n *\n * @param {Object} opts\n * @returns {string} the new random name according to opts\n * @private\n */\nfunction _generateTmpName(opts) {\n  const tmpDir = opts.tmpdir;\n\n  /* istanbul ignore else */\n  if (!_isUndefined(opts.name)) {\n    return path.join(tmpDir, opts.dir, opts.name);\n  }\n\n  /* istanbul ignore else */\n  if (!_isUndefined(opts.template)) {\n    return path.join(tmpDir, opts.dir, opts.template).replace(TEMPLATE_PATTERN, _randomChars(6));\n  }\n\n  // prefix and postfix\n  const name = [\n    opts.prefix ? opts.prefix : 'tmp',\n    '-',\n    process.pid,\n    '-',\n    _randomChars(12),\n    opts.postfix ? '-' + opts.postfix : ''\n  ].join('');\n\n  return path.join(tmpDir, opts.dir, name);\n}\n\n/**\n * Asserts and sanitizes the basic options.\n *\n * @private\n */\nfunction _assertOptionsBase(options) {\n  if (!_isUndefined(options.name)) {\n    const name = options.name;\n\n    // assert that name is not absolute and does not contain a path\n    if (path.isAbsolute(name)) throw new Error(`name option must not contain an absolute path, found \"${name}\".`);\n\n    // must not fail on valid .<name> or ..<name> or similar such constructs\n    const basename = path.basename(name);\n    if (basename === '..' || basename === '.' || basename !== name)\n      throw new Error(`name option must not contain a path, found \"${name}\".`);\n  }\n\n  /* istanbul ignore else */\n  if (!_isUndefined(options.template) && !options.template.match(TEMPLATE_PATTERN)) {\n    throw new Error(`Invalid template, found \"${options.template}\".`);\n  }\n\n  /* istanbul ignore else */\n  if ((!_isUndefined(options.tries) && isNaN(options.tries)) || options.tries < 0) {\n    throw new Error(`Invalid tries, found \"${options.tries}\".`);\n  }\n\n  // if a name was specified we will try once\n  options.tries = _isUndefined(options.name) ? options.tries || DEFAULT_TRIES : 1;\n  options.keep = !!options.keep;\n  options.detachDescriptor = !!options.detachDescriptor;\n  options.discardDescriptor = !!options.discardDescriptor;\n  options.unsafeCleanup = !!options.unsafeCleanup;\n\n  // for completeness' sake only, also keep (multiple) blanks if the user, purportedly sane, requests us to\n  options.prefix = _isUndefined(options.prefix) ? '' : options.prefix;\n  options.postfix = _isUndefined(options.postfix) ? '' : options.postfix;\n}\n\n/**\n * Gets the relative directory to tmpDir.\n *\n * @private\n */\nfunction _getRelativePath(option, name, tmpDir, cb) {\n  if (_isUndefined(name)) return cb(null);\n\n  _resolvePath(name, tmpDir, function (err, resolvedPath) {\n    if (err) return cb(err);\n\n    const relativePath = path.relative(tmpDir, resolvedPath);\n\n    if (!resolvedPath.startsWith(tmpDir)) {\n      return cb(new Error(`${option} option must be relative to \"${tmpDir}\", found \"${relativePath}\".`));\n    }\n\n    cb(null, relativePath);\n  });\n}\n\n/**\n * Gets the relative path to tmpDir.\n *\n * @private\n */\nfunction _getRelativePathSync(option, name, tmpDir) {\n  if (_isUndefined(name)) return;\n\n  const resolvedPath = _resolvePathSync(name, tmpDir);\n  const relativePath = path.relative(tmpDir, resolvedPath);\n\n  if (!resolvedPath.startsWith(tmpDir)) {\n    throw new Error(`${option} option must be relative to \"${tmpDir}\", found \"${relativePath}\".`);\n  }\n\n  return relativePath;\n}\n\n/**\n * Asserts whether the specified options are valid, also sanitizes options and provides sane defaults for missing\n * options.\n *\n * @private\n */\nfunction _assertAndSanitizeOptions(options, cb) {\n  _getTmpDir(options, function (err, tmpDir) {\n    if (err) return cb(err);\n\n    options.tmpdir = tmpDir;\n\n    try {\n      _assertOptionsBase(options, tmpDir);\n    } catch (err) {\n      return cb(err);\n    }\n\n    // sanitize dir, also keep (multiple) blanks if the user, purportedly sane, requests us to\n    _getRelativePath('dir', options.dir, tmpDir, function (err, dir) {\n      if (err) return cb(err);\n\n      options.dir = _isUndefined(dir) ? '' : dir;\n\n      // sanitize further if template is relative to options.dir\n      _getRelativePath('template', options.template, tmpDir, function (err, template) {\n        if (err) return cb(err);\n\n        options.template = template;\n\n        cb(null, options);\n      });\n    });\n  });\n}\n\n/**\n * Asserts whether the specified options are valid, also sanitizes options and provides sane defaults for missing\n * options.\n *\n * @private\n */\nfunction _assertAndSanitizeOptionsSync(options) {\n  const tmpDir = (options.tmpdir = _getTmpDirSync(options));\n\n  _assertOptionsBase(options, tmpDir);\n\n  const dir = _getRelativePathSync('dir', options.dir, tmpDir);\n  options.dir = _isUndefined(dir) ? '' : dir;\n\n  options.template = _getRelativePathSync('template', options.template, tmpDir);\n\n  return options;\n}\n\n/**\n * Helper for testing against EBADF to compensate changes made to Node 7.x under Windows.\n *\n * @private\n */\nfunction _isEBADF(error) {\n  return _isExpectedError(error, -EBADF, 'EBADF');\n}\n\n/**\n * Helper for testing against ENOENT to compensate changes made to Node 7.x under Windows.\n *\n * @private\n */\nfunction _isENOENT(error) {\n  return _isExpectedError(error, -ENOENT, 'ENOENT');\n}\n\n/**\n * Helper to determine whether the expected error code matches the actual code and errno,\n * which will differ between the supported node versions.\n *\n * - Node >= 7.0:\n *   error.code {string}\n *   error.errno {number} any numerical value will be negated\n *\n * CAVEAT\n *\n * On windows, the errno for EBADF is -4083 but os.constants.errno.EBADF is different and we must assume that ENOENT\n * is no different here.\n *\n * @param {SystemError} error\n * @param {number} errno\n * @param {string} code\n * @private\n */\nfunction _isExpectedError(error, errno, code) {\n  return IS_WIN32 ? error.code === code : error.code === code && error.errno === errno;\n}\n\n/**\n * Sets the graceful cleanup.\n *\n * If graceful cleanup is set, tmp will remove all controlled temporary objects on process exit, otherwise the\n * temporary objects will remain in place, waiting to be cleaned up on system restart or otherwise scheduled temporary\n * object removals.\n */\nfunction setGracefulCleanup() {\n  _gracefulCleanup = true;\n}\n\n/**\n * Returns the currently configured tmp dir from os.tmpdir().\n *\n * @private\n */\nfunction _getTmpDir(options, cb) {\n  return fs.realpath((options && options.tmpdir) || os.tmpdir(), cb);\n}\n\n/**\n * Returns the currently configured tmp dir from os.tmpdir().\n *\n * @private\n */\nfunction _getTmpDirSync(options) {\n  return fs.realpathSync((options && options.tmpdir) || os.tmpdir());\n}\n\n// Install process exit listener\nprocess.addListener(EXIT, _garbageCollector);\n\n/**\n * Configuration options.\n *\n * @typedef {Object} Options\n * @property {?boolean} keep the temporary object (file or dir) will not be garbage collected\n * @property {?number} tries the number of tries before give up the name generation\n * @property (?int) mode the access mode, defaults are 0o700 for directories and 0o600 for files\n * @property {?string} template the \"mkstemp\" like filename template\n * @property {?string} name fixed name relative to tmpdir or the specified dir option\n * @property {?string} dir tmp directory relative to the root tmp directory in use\n * @property {?string} prefix prefix for the generated name\n * @property {?string} postfix postfix for the generated name\n * @property {?string} tmpdir the root tmp directory which overrides the os tmpdir\n * @property {?boolean} unsafeCleanup recursively removes the created temporary directory, even when it's not empty\n * @property {?boolean} detachDescriptor detaches the file descriptor, caller is responsible for closing the file, tmp will no longer try closing the file during garbage collection\n * @property {?boolean} discardDescriptor discards the file descriptor (closes file, fd is -1), tmp will no longer try closing the file during garbage collection\n */\n\n/**\n * @typedef {Object} FileSyncObject\n * @property {string} name the name of the file\n * @property {string} fd the file descriptor or -1 if the fd has been discarded\n * @property {fileCallback} removeCallback the callback function to remove the file\n */\n\n/**\n * @typedef {Object} DirSyncObject\n * @property {string} name the name of the directory\n * @property {fileCallback} removeCallback the callback function to remove the directory\n */\n\n/**\n * @callback tmpNameCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n */\n\n/**\n * @callback fileCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {number} fd the file descriptor or -1 if the fd had been discarded\n * @param {cleanupCallback} fn the cleanup callback function\n */\n\n/**\n * @callback fileCallbackSync\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {number} fd the file descriptor or -1 if the fd had been discarded\n * @param {cleanupCallbackSync} fn the cleanup callback function\n */\n\n/**\n * @callback dirCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {cleanupCallback} fn the cleanup callback function\n */\n\n/**\n * @callback dirCallbackSync\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {cleanupCallbackSync} fn the cleanup callback function\n */\n\n/**\n * Removes the temporary created file or directory.\n *\n * @callback cleanupCallback\n * @param {simpleCallback} [next] function to call whenever the tmp object needs to be removed\n */\n\n/**\n * Removes the temporary created file or directory.\n *\n * @callback cleanupCallbackSync\n */\n\n/**\n * Callback function for function composition.\n * @see {@link https://github.com/raszi/node-tmp/issues/57|raszi/node-tmp#57}\n *\n * @callback simpleCallback\n */\n\n// exporting all the needed methods\n\n// evaluate _getTmpDir() lazily, mainly for simplifying testing but it also will\n// allow users to reconfigure the temporary directory\nObject.defineProperty(module.exports, \"tmpdir\", ({\n  enumerable: true,\n  configurable: false,\n  get: function () {\n    return _getTmpDirSync();\n  }\n}));\n\nmodule.exports.dir = dir;\nmodule.exports.dirSync = dirSync;\n\nmodule.exports.file = file;\nmodule.exports.fileSync = fileSync;\n\nmodule.exports.tmpName = tmpName;\nmodule.exports.tmpNameSync = tmpNameSync;\n\nmodule.exports.setGracefulCleanup = setGracefulCleanup;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tmp/lib/tmp.js\n");

/***/ })

};
;