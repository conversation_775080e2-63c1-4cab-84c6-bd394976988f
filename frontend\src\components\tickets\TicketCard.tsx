'use client';

import React from 'react';
import { 
  Ticket, 
  TICKET_PRIORITIES, 
  TICKET_CATEGORIES, 
  TICKET_STATUSES,
  TicketPriority,
  TicketCategory,
  TicketStatus
} from '@/types/ticket';

interface TicketCardProps {
  ticket: Ticket;
  onClick: (ticket: Ticket) => void;
  isAdmin?: boolean;
}

export default function TicketCard({ ticket, onClick, isAdmin = false }: TicketCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <div
      className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-pointer hover:bg-gray-50 hover:shadow-md transition-all"
      onClick={() => onClick(ticket)}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-gray-900 truncate mb-1">
            {ticket.title}
          </h3>
          <p className="text-xs text-gray-500 truncate">
            {ticket.description.substring(0, 60)}...
          </p>
        </div>
        <div className="flex-shrink-0 ml-2">
          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
            TICKET_STATUSES[ticket.status as TicketStatus]?.color || 'bg-gray-100 text-gray-800'
          }`}>
            {TICKET_STATUSES[ticket.status as TicketStatus]?.label || ticket.status}
          </span>
        </div>
      </div>

      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${
            TICKET_PRIORITIES[ticket.priority as TicketPriority]?.color || 'bg-gray-100 text-gray-800'
          }`}>
            {TICKET_PRIORITIES[ticket.priority as TicketPriority]?.label || ticket.priority}
          </span>
          <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${
            TICKET_CATEGORIES[ticket.category as TicketCategory]?.color || 'bg-gray-100 text-gray-800'
          }`}>
            {TICKET_CATEGORIES[ticket.category as TicketCategory]?.label || ticket.category}
          </span>
        </div>
        <div className="text-gray-500">
          {formatDate(ticket.created_at)}
        </div>
      </div>

      {isAdmin && ticket.creator_username && (
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-center">
            <div className="flex-shrink-0 h-6 w-6 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-900 font-medium text-xs">
                {ticket.creator_username.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="ml-2">
              <div className="text-xs font-medium text-gray-900">
                {ticket.creator_username}
              </div>
              <div className="text-xs text-gray-500 truncate max-w-32">
                {ticket.creator_email}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
