from sqlalchemy import <PERSON>umn, Integer, String, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum as PyEnum
from ..database import Base


class SpaceRole(PyEnum):
    """Role of a user within a shared space.

    Values:
    - admin: Space owner/admin with full control over the shared space
    - member: Regular member with limited permissions
    """

    admin = "admin"
    member = "member"


class SpaceMembership(Base):
    """Association table between users and shared spaces with a role.

    A membership row asserts that `user_id` participates in space `space_id`
    with the specified `role`. The (user_id, space_id) pair is unique.
    """

    __tablename__ = "space_memberships"

    # Primary identifier for the membership
    id = Column(Integer, primary_key=True, index=True)

    # Foreign keys
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    space_id = Column(Integer, ForeignKey('user_spaces.id'), nullable=False, index=True)

    # Role in the space; defaults to member
    role = Column(
        String(20), nullable=False, default=SpaceRole.member.value, index=True
    )

    # Auditing fields
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)

    # ORM relationships
    user = relationship("User", foreign_keys=[user_id])  # the member
    space = relationship("UserSpace", back_populates="memberships")  # the space
    added_by = relationship("User", foreign_keys=[created_by])  # who added the member

    # Enforce 1 membership per (user, space)
    __table_args__ = (
        UniqueConstraint('user_id', 'space_id', name='unique_user_space_membership'),
    )

    def to_dict(self) -> dict:
        """Serialize membership for API responses.

        Includes denormalized usernames for UX while keeping the payload compact.
        """

        return {
            'id': self.id,
            'user_id': self.user_id,
            'space_id': self.space_id,
            'role': self.role,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'created_by': self.created_by,
            'user_username': self.user.username if self.user else None,
            'added_by_username': self.added_by.username if self.added_by else None,
        }

    def __repr__(self):
        return f'<SpaceMembership user_id={self.user_id} space_id={self.space_id} role={self.role}>'
