from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, update
from sqlalchemy.orm import joinedload, selectinload
from typing import List, Optional
from datetime import datetime

from ..database import get_db
from ..models.ticket import Ticket
from ..models.ticket_attachment import TicketAttachment
from ..models.user import User
from ..schemas.ticket import (
    TicketCreate, TicketUpdate, TicketResponse,
    TicketSummary, TicketListResponse, TicketAttachmentResponse
)
from ..schemas.ticket import TicketResponse as TicketResponseSchema
from ..auth.dependencies import get_current_user, require_admin
from ..services.redis_service import redis_service, cached
from ..services.minio_service import minio_service
from .. import config

router = APIRouter(prefix="/tickets", tags=["Tickets"])

# Valid categories and priorities
VALID_CATEGORIES = ['bug', 'feature', 'support', 'other']
VALID_PRIORITIES = ['low', 'medium', 'high', 'urgent']
VALID_STATUSES = ['open', 'in_progress', 'resolved', 'closed']

@router.post("/", response_model=TicketResponse)
async def create_ticket(
    ticket_data: TicketCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new ticket (any authenticated user)"""
    try:
        # Validate category
        if ticket_data.category not in VALID_CATEGORIES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid category. Must be one of: {', '.join(VALID_CATEGORIES)}"
            )

        # Validate priority
        if ticket_data.priority not in VALID_PRIORITIES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid priority. Must be one of: {', '.join(VALID_PRIORITIES)}"
            )

        # Create ticket
        ticket = Ticket(
            title=ticket_data.title,
            description=ticket_data.description,
            category=ticket_data.category,
            priority=ticket_data.priority,
            created_by=current_user.id
        )

        db.add(ticket)
        await db.commit()
        await db.refresh(ticket)

        # Load creator information
        ticket.creator_username = current_user.username
        ticket.creator_email = current_user.email

        return ticket

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create ticket: {str(e)}"
        )

@router.post("/with-attachments", response_model=TicketResponse)
async def create_ticket_with_attachments(
    title: str = Form(...),
    description: str = Form(...),
    category: str = Form(...),
    priority: str = Form("medium"),
    files: List[UploadFile] = File(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new ticket with optional file attachments (FormData)"""
    try:
        # Validate category
        if category not in VALID_CATEGORIES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid category. Must be one of: {', '.join(VALID_CATEGORIES)}"
            )

        # Validate priority
        if priority not in VALID_PRIORITIES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid priority. Must be one of: {', '.join(VALID_PRIORITIES)}"
            )

        # Create ticket
        ticket = Ticket(
            title=title,
            description=description,
            category=category,
            priority=priority,
            created_by=current_user.id
        )

        db.add(ticket)
        await db.commit()
        await db.refresh(ticket)

        # Handle file attachments if provided
        attachment_records = []
        if files and len(files) > 0:
            for file in files:
                if file.filename:
                    # Upload file to MinIO
                    upload_result = await minio_service.upload_file(
                        file=file,
                        bucket_type="support_tickets",
                        user_id=current_user.id,
                        username=current_user.username,
                        collection=f"ticket_{ticket.id}"
                    )

                    if upload_result["success"]:
                        # Create attachment record
                        attachment = TicketAttachment(
                            ticket_id=ticket.id,
                            filename=upload_result["object_key"].split('/')[-1],
                            original_filename=file.filename,
                            file_size=upload_result["file_size"],
                            content_type=upload_result["content_type"],
                            minio_bucket=upload_result["bucket_name"],
                            minio_object_key=upload_result["object_key"],
                            uploaded_by=current_user.id
                        )
                        db.add(attachment)
                        attachment_records.append(attachment)

            await db.commit()
            
            # Refresh all attachments to get their IDs
            for attachment in attachment_records:
                await db.refresh(attachment)

        # Reload ticket with all relationships using eager loading
        stmt = select(Ticket)\
                  .options(
                      joinedload(Ticket.creator), 
                      joinedload(Ticket.assignee), 
                      selectinload(Ticket.attachments).joinedload(TicketAttachment.uploader)
                  )\
                  .where(Ticket.id == ticket.id)
        
        result = await db.execute(stmt)
        ticket = result.scalars().first()

        # Add user information to ticket and attachments
        if ticket.creator:
            ticket.creator_username = ticket.creator.username
            ticket.creator_email = ticket.creator.email
        if ticket.assignee:
            ticket.assignee_username = ticket.assignee.username
        
        # Add uploader info to attachments
        if ticket.attachments:
            for attachment in ticket.attachments:
                if attachment.uploader:
                    attachment.uploader_username = attachment.uploader.username

        return ticket

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create ticket: {str(e)}"
        )

@router.get("/my", response_model=TicketListResponse)
async def get_my_tickets(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=50),
    status_filter: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get tickets created by current user"""
    try:
        stmt = select(Ticket).where(Ticket.created_by == current_user.id)

        # Apply status filter if provided
        if status_filter and status_filter in VALID_STATUSES:
            stmt = stmt.where(Ticket.status == status_filter)

        # Get total count (apply same filters as main query)
        count_stmt = select(func.count(Ticket.id)).where(Ticket.created_by == current_user.id)

        # Apply status filter to count if provided
        if status_filter and status_filter in VALID_STATUSES:
            count_stmt = count_stmt.where(Ticket.status == status_filter)

        total_result = await db.execute(count_stmt)
        total = total_result.scalar()

        # Apply pagination and eager loading
        stmt = stmt.order_by(Ticket.created_at.desc())\
                   .offset((page - 1) * per_page)\
                   .limit(per_page)\
                   .options(joinedload(Ticket.creator), joinedload(Ticket.assignee), selectinload(Ticket.attachments).joinedload(TicketAttachment.uploader))

        result = await db.execute(stmt)
        tickets = result.scalars().unique().all()

        # Add user information to tickets and attachments
        for ticket in tickets:
            if ticket.creator:
                ticket.creator_username = ticket.creator.username
                ticket.creator_email = ticket.creator.email
            if ticket.assignee:
                ticket.assignee_username = ticket.assignee.username
            # Add uploader info to attachments
            if ticket.attachments:
                for attachment in ticket.attachments:
                    if attachment.uploader:
                        attachment.uploader_username = attachment.uploader.username

        return TicketListResponse(
            tickets=tickets,
            total=total,
            page=page,
            per_page=per_page
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch tickets: {str(e)}"
        )

@router.get("/summary", response_model=TicketSummary)
@cached(
    prefix=f"{config.CACHE_PREFIX_TICKETS}:summary",
    ttl=config.CACHE_TTL_SHORT  # 5 minutes cache
)
async def get_ticket_summary(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get ticket summary statistics (All tickets for admins, own tickets for regular users)"""
    try:
        # Build base statement based on user role
        if current_user.is_admin():
            # Admin can see all tickets
            base_stmt = select(Ticket)
        else:
            # Regular users see only their own tickets
            base_stmt = select(Ticket).where(Ticket.created_by == current_user.id)
        
        # Get counts for each status
        total = await db.scalar(select(func.count()).select_from(base_stmt.subquery()))
        
        open_count = await db.scalar(select(func.count()).select_from(
            base_stmt.where(Ticket.status == 'open').subquery()
        ))
        in_progress_count = await db.scalar(select(func.count()).select_from(
            base_stmt.where(Ticket.status == 'in_progress').subquery()
        ))
        resolved_count = await db.scalar(select(func.count()).select_from(
            base_stmt.where(Ticket.status == 'resolved').subquery()
        ))
        closed_count = await db.scalar(select(func.count()).select_from(
            base_stmt.where(Ticket.status == 'closed').subquery()
        ))

        return TicketSummary(
            total=total,
            open=open_count,
            in_progress=in_progress_count,
            resolved=resolved_count,
            closed=closed_count
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch ticket summary: {str(e)}"
        )

@router.get("/", response_model=TicketListResponse)
async def get_all_tickets(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=50),
    status_filter: Optional[str] = None,
    category_filter: Optional[str] = None,
    priority_filter: Optional[str] = None,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """Get all tickets (Admin only)"""
    try:
        stmt = select(Ticket)

        # Apply filters
        if status_filter and status_filter in VALID_STATUSES:
            stmt = stmt.where(Ticket.status == status_filter)
        if category_filter and category_filter in VALID_CATEGORIES:
            stmt = stmt.where(Ticket.category == category_filter)
        if priority_filter and priority_filter in VALID_PRIORITIES:
            stmt = stmt.where(Ticket.priority == priority_filter)

        # Get total count
        count_stmt = select(func.count(Ticket.id))
        if status_filter and status_filter in VALID_STATUSES:
            count_stmt = count_stmt.where(Ticket.status == status_filter)
        if category_filter and category_filter in VALID_CATEGORIES:
            count_stmt = count_stmt.where(Ticket.category == category_filter)
        if priority_filter and priority_filter in VALID_PRIORITIES:
            count_stmt = count_stmt.where(Ticket.priority == priority_filter)

        total = await db.scalar(count_stmt)

        # Apply pagination and eager loading
        stmt = stmt.order_by(Ticket.created_at.desc())\
                   .offset((page - 1) * per_page)\
                   .limit(per_page)\
                   .options(joinedload(Ticket.creator), joinedload(Ticket.assignee), selectinload(Ticket.attachments).joinedload(TicketAttachment.uploader))

        result = await db.execute(stmt)
        tickets = result.scalars().unique().all()

        # Add user information to tickets and attachments
        for ticket in tickets:
            if ticket.creator:
                ticket.creator_username = ticket.creator.username
                ticket.creator_email = ticket.creator.email
            if ticket.assignee:
                ticket.assignee_username = ticket.assignee.username
            # Add uploader info to attachments
            if ticket.attachments:
                for attachment in ticket.attachments:
                    if attachment.uploader:
                        attachment.uploader_username = attachment.uploader.username

        return TicketListResponse(
            tickets=tickets,
            total=total,
            page=page,
            per_page=per_page
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch tickets: {str(e)}"
        )

@router.put("/{ticket_id}", response_model=TicketResponse)
async def update_ticket(
    ticket_id: int,
    ticket_update: TicketUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update a ticket"""
    try:
        stmt = select(Ticket)\
                  .options(joinedload(Ticket.creator), joinedload(Ticket.assignee), selectinload(Ticket.attachments).joinedload(TicketAttachment.uploader))\
                  .where(Ticket.id == ticket_id)
        
        result = await db.execute(stmt)
        ticket = result.scalars().first()

        if not ticket:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Ticket not found"
            )

        # Check permissions: users can only update their own tickets' basic info,
        # admins can update everything including assignment and status
        if not current_user.is_admin() and ticket.created_by != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only update your own tickets"
            )

        # Validate inputs
        if ticket_update.category and ticket_update.category not in VALID_CATEGORIES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid category. Must be one of: {', '.join(VALID_CATEGORIES)}"
            )

        if ticket_update.priority and ticket_update.priority not in VALID_PRIORITIES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid priority. Must be one of: {', '.join(VALID_PRIORITIES)}"
            )

        if ticket_update.status and ticket_update.status not in VALID_STATUSES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid status. Must be one of: {', '.join(VALID_STATUSES)}"
            )

        # Users can only update basic info, not admin fields
        if not current_user.is_admin():
            # Remove admin-only fields from update
            ticket_update.assigned_to = None
            ticket_update.admin_notes = None
            ticket_update.status = None

        # Store original status before applying updates
        original_status = ticket.status

        # Apply updates
        update_data = ticket_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(ticket, field, value)

        # Set resolved_at if status changed to resolved
        if ticket_update.status == 'resolved' and original_status != 'resolved':
            ticket.resolved_at = datetime.utcnow()
        elif ticket_update.status and ticket_update.status != 'resolved':
            ticket.resolved_at = None

        await db.commit()
        await db.refresh(ticket)

        # Add user information
        if ticket.creator:
            ticket.creator_username = ticket.creator.username
            ticket.creator_email = ticket.creator.email
        if ticket.assignee:
            ticket.assignee_username = ticket.assignee.username
        # Add uploader info to attachments
        if ticket.attachments:
            for attachment in ticket.attachments:
                if attachment.uploader:
                    attachment.uploader_username = attachment.uploader.username

        return ticket

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update ticket: {str(e)}"
        )

@router.get("/attachments/{attachment_id}/download")
async def download_ticket_attachment(
    attachment_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Download a ticket attachment"""
    try:
        # Get the attachment
        stmt = select(TicketAttachment)\
                  .options(joinedload(TicketAttachment.ticket))\
                  .where(TicketAttachment.id == attachment_id)
        
        result = await db.execute(stmt)
        attachment = result.scalars().first()

        if not attachment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Attachment not found"
            )

        # Check permissions: user must be the ticket creator or an admin
        if not current_user.is_admin() and attachment.ticket.created_by != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only download attachments from your own tickets"
            )

        # Download file from MinIO
        file_data = minio_service.download_file(
            bucket_name=attachment.minio_bucket,
            object_key=attachment.minio_object_key
        )

        if not file_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found in storage"
            )

        from fastapi.responses import StreamingResponse
        import io

        # Return file as streaming response
        return StreamingResponse(
            io.BytesIO(file_data),
            media_type=attachment.content_type,
            headers={
                "Content-Disposition": f'attachment; filename="{attachment.original_filename}"'
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download attachment: {str(e)}"
        )
