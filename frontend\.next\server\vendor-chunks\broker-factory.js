"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/broker-factory";
exports.ids = ["vendor-chunks/broker-factory"];
exports.modules = {

/***/ "(ssr)/./node_modules/broker-factory/build/es2019/guards/message-port.js":
/*!*************************************************************************!*\
  !*** ./node_modules/broker-factory/build/es2019/guards/message-port.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isMessagePort: () => (/* binding */ isMessagePort)\n/* harmony export */ });\nconst isMessagePort = (sender) => {\n    return typeof sender.start === 'function';\n};\n//# sourceMappingURL=message-port.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L2d1YXJkcy9tZXNzYWdlLXBvcnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L2d1YXJkcy9tZXNzYWdlLXBvcnQuanM/N2YyOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgaXNNZXNzYWdlUG9ydCA9IChzZW5kZXIpID0+IHtcbiAgICByZXR1cm4gdHlwZW9mIHNlbmRlci5zdGFydCA9PT0gJ2Z1bmN0aW9uJztcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tZXNzYWdlLXBvcnQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/broker-factory/build/es2019/guards/message-port.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/broker-factory/build/es2019/helpers/extend-broker-implementation.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/broker-factory/build/es2019/helpers/extend-broker-implementation.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extendBrokerImplementation: () => (/* binding */ extendBrokerImplementation)\n/* harmony export */ });\n/* harmony import */ var _port_map__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./port-map */ \"(ssr)/./node_modules/broker-factory/build/es2019/helpers/port-map.js\");\n\nconst extendBrokerImplementation = (partialBrokerImplementation) => ({\n    ...partialBrokerImplementation,\n    connect: ({ call }) => {\n        return async () => {\n            const { port1, port2 } = new MessageChannel();\n            const portId = await call('connect', { port: port1 }, [port1]);\n            _port_map__WEBPACK_IMPORTED_MODULE_0__.PORT_MAP.set(port2, portId);\n            return port2;\n        };\n    },\n    disconnect: ({ call }) => {\n        return async (port) => {\n            const portId = _port_map__WEBPACK_IMPORTED_MODULE_0__.PORT_MAP.get(port);\n            if (portId === undefined) {\n                throw new Error('The given port is not connected.');\n            }\n            await call('disconnect', { portId });\n        };\n    },\n    isSupported: ({ call }) => {\n        return () => call('isSupported');\n    }\n});\n//# sourceMappingURL=extend-broker-implementation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L2hlbHBlcnMvZXh0ZW5kLWJyb2tlci1pbXBsZW1lbnRhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzQztBQUMvQjtBQUNQO0FBQ0EsZ0JBQWdCLE1BQU07QUFDdEI7QUFDQSxvQkFBb0IsZUFBZTtBQUNuQyxtREFBbUQsYUFBYTtBQUNoRSxZQUFZLCtDQUFRO0FBQ3BCO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsbUJBQW1CLE1BQU07QUFDekI7QUFDQSwyQkFBMkIsK0NBQVE7QUFDbkM7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLFFBQVE7QUFDL0M7QUFDQSxLQUFLO0FBQ0wsb0JBQW9CLE1BQU07QUFDMUI7QUFDQTtBQUNBLENBQUM7QUFDRCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2Jyb2tlci1mYWN0b3J5L2J1aWxkL2VzMjAxOS9oZWxwZXJzL2V4dGVuZC1icm9rZXItaW1wbGVtZW50YXRpb24uanM/MTI0NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQT1JUX01BUCB9IGZyb20gJy4vcG9ydC1tYXAnO1xuZXhwb3J0IGNvbnN0IGV4dGVuZEJyb2tlckltcGxlbWVudGF0aW9uID0gKHBhcnRpYWxCcm9rZXJJbXBsZW1lbnRhdGlvbikgPT4gKHtcbiAgICAuLi5wYXJ0aWFsQnJva2VySW1wbGVtZW50YXRpb24sXG4gICAgY29ubmVjdDogKHsgY2FsbCB9KSA9PiB7XG4gICAgICAgIHJldHVybiBhc3luYyAoKSA9PiB7XG4gICAgICAgICAgICBjb25zdCB7IHBvcnQxLCBwb3J0MiB9ID0gbmV3IE1lc3NhZ2VDaGFubmVsKCk7XG4gICAgICAgICAgICBjb25zdCBwb3J0SWQgPSBhd2FpdCBjYWxsKCdjb25uZWN0JywgeyBwb3J0OiBwb3J0MSB9LCBbcG9ydDFdKTtcbiAgICAgICAgICAgIFBPUlRfTUFQLnNldChwb3J0MiwgcG9ydElkKTtcbiAgICAgICAgICAgIHJldHVybiBwb3J0MjtcbiAgICAgICAgfTtcbiAgICB9LFxuICAgIGRpc2Nvbm5lY3Q6ICh7IGNhbGwgfSkgPT4ge1xuICAgICAgICByZXR1cm4gYXN5bmMgKHBvcnQpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHBvcnRJZCA9IFBPUlRfTUFQLmdldChwb3J0KTtcbiAgICAgICAgICAgIGlmIChwb3J0SWQgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignVGhlIGdpdmVuIHBvcnQgaXMgbm90IGNvbm5lY3RlZC4nKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGF3YWl0IGNhbGwoJ2Rpc2Nvbm5lY3QnLCB7IHBvcnRJZCB9KTtcbiAgICAgICAgfTtcbiAgICB9LFxuICAgIGlzU3VwcG9ydGVkOiAoeyBjYWxsIH0pID0+IHtcbiAgICAgICAgcmV0dXJuICgpID0+IGNhbGwoJ2lzU3VwcG9ydGVkJyk7XG4gICAgfVxufSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1leHRlbmQtYnJva2VyLWltcGxlbWVudGF0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/broker-factory/build/es2019/helpers/extend-broker-implementation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/broker-factory/build/es2019/helpers/port-map.js":
/*!**********************************************************************!*\
  !*** ./node_modules/broker-factory/build/es2019/helpers/port-map.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PORT_MAP: () => (/* binding */ PORT_MAP)\n/* harmony export */ });\nconst PORT_MAP = new WeakMap();\n//# sourceMappingURL=port-map.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L2hlbHBlcnMvcG9ydC1tYXAuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9icm9rZXItZmFjdG9yeS9idWlsZC9lczIwMTkvaGVscGVycy9wb3J0LW1hcC5qcz8xYzVkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBQT1JUX01BUCA9IG5ldyBXZWFrTWFwKCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wb3J0LW1hcC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/broker-factory/build/es2019/helpers/port-map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/broker-factory/build/es2019/interfaces/broker-actions.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/broker-factory/build/es2019/interfaces/broker-actions.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=broker-actions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L2ludGVyZmFjZXMvYnJva2VyLWFjdGlvbnMuanMiLCJtYXBwaW5ncyI6IjtBQUFVO0FBQ1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9icm9rZXItZmFjdG9yeS9idWlsZC9lczIwMTkvaW50ZXJmYWNlcy9icm9rZXItYWN0aW9ucy5qcz9kNTBhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWJyb2tlci1hY3Rpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/broker-factory/build/es2019/interfaces/broker-actions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/broker-factory/build/es2019/interfaces/broker-definition.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/broker-factory/build/es2019/interfaces/broker-definition.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=broker-definition.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L2ludGVyZmFjZXMvYnJva2VyLWRlZmluaXRpb24uanMiLCJtYXBwaW5ncyI6IjtBQUFVO0FBQ1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9icm9rZXItZmFjdG9yeS9idWlsZC9lczIwMTkvaW50ZXJmYWNlcy9icm9rZXItZGVmaW5pdGlvbi5qcz81Yzk4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWJyb2tlci1kZWZpbml0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/broker-factory/build/es2019/interfaces/broker-definition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/broker-factory/build/es2019/interfaces/default-broker-definition.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/broker-factory/build/es2019/interfaces/default-broker-definition.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=default-broker-definition.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L2ludGVyZmFjZXMvZGVmYXVsdC1icm9rZXItZGVmaW5pdGlvbi5qcyIsIm1hcHBpbmdzIjoiO0FBQVU7QUFDViIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2Jyb2tlci1mYWN0b3J5L2J1aWxkL2VzMjAxOS9pbnRlcmZhY2VzL2RlZmF1bHQtYnJva2VyLWRlZmluaXRpb24uanM/OWVhNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kZWZhdWx0LWJyb2tlci1kZWZpbml0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/broker-factory/build/es2019/interfaces/default-broker-definition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/broker-factory/build/es2019/interfaces/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/broker-factory/build/es2019/interfaces/index.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _broker_actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./broker-actions */ \"(ssr)/./node_modules/broker-factory/build/es2019/interfaces/broker-actions.js\");\n/* harmony import */ var _broker_definition__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./broker-definition */ \"(ssr)/./node_modules/broker-factory/build/es2019/interfaces/broker-definition.js\");\n/* harmony import */ var _default_broker_definition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./default-broker-definition */ \"(ssr)/./node_modules/broker-factory/build/es2019/interfaces/default-broker-definition.js\");\n/* harmony import */ var _worker_event__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./worker-event */ \"(ssr)/./node_modules/broker-factory/build/es2019/interfaces/worker-event.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L2ludGVyZmFjZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBaUM7QUFDRztBQUNRO0FBQ2I7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9icm9rZXItZmFjdG9yeS9idWlsZC9lczIwMTkvaW50ZXJmYWNlcy9pbmRleC5qcz9lNTEzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vYnJva2VyLWFjdGlvbnMnO1xuZXhwb3J0ICogZnJvbSAnLi9icm9rZXItZGVmaW5pdGlvbic7XG5leHBvcnQgKiBmcm9tICcuL2RlZmF1bHQtYnJva2VyLWRlZmluaXRpb24nO1xuZXhwb3J0ICogZnJvbSAnLi93b3JrZXItZXZlbnQnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/broker-factory/build/es2019/interfaces/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/broker-factory/build/es2019/interfaces/worker-event.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/broker-factory/build/es2019/interfaces/worker-event.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=worker-event.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L2ludGVyZmFjZXMvd29ya2VyLWV2ZW50LmpzIiwibWFwcGluZ3MiOiI7QUFBVTtBQUNWIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L2ludGVyZmFjZXMvd29ya2VyLWV2ZW50LmpzPzZiZjMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d29ya2VyLWV2ZW50LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/broker-factory/build/es2019/interfaces/worker-event.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/broker-factory/build/es2019/module.js":
/*!************************************************************!*\
  !*** ./node_modules/broker-factory/build/es2019/module.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBroker: () => (/* binding */ createBroker)\n/* harmony export */ });\n/* harmony import */ var fast_unique_numbers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fast-unique-numbers */ \"(ssr)/./node_modules/fast-unique-numbers/build/es2019/module.js\");\n/* harmony import */ var _guards_message_port__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./guards/message-port */ \"(ssr)/./node_modules/broker-factory/build/es2019/guards/message-port.js\");\n/* harmony import */ var _helpers_extend_broker_implementation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers/extend-broker-implementation */ \"(ssr)/./node_modules/broker-factory/build/es2019/helpers/extend-broker-implementation.js\");\n/* harmony import */ var _interfaces_index__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./interfaces/index */ \"(ssr)/./node_modules/broker-factory/build/es2019/interfaces/index.js\");\n/* harmony import */ var _types_index__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./types/index */ \"(ssr)/./node_modules/broker-factory/build/es2019/types/index.js\");\n\n\n\n/*\n * @todo Explicitly referencing the barrel file seems to be necessary when enabling the\n * isolatedModules compiler option.\n */\n\n\nconst ONGOING_REQUESTS = new WeakMap();\nconst createOrGetOngoingRequests = (sender) => {\n    if (ONGOING_REQUESTS.has(sender)) {\n        // @todo TypeScript needs to be convinced that has() works as expected.\n        return ONGOING_REQUESTS.get(sender);\n    }\n    const ongoingRequests = new Map();\n    ONGOING_REQUESTS.set(sender, ongoingRequests);\n    return ongoingRequests;\n};\nconst createBroker = (brokerImplementation) => {\n    const fullBrokerImplementation = (0,_helpers_extend_broker_implementation__WEBPACK_IMPORTED_MODULE_2__.extendBrokerImplementation)(brokerImplementation);\n    return (sender) => {\n        const ongoingRequests = createOrGetOngoingRequests(sender);\n        sender.addEventListener('message', (({ data: message }) => {\n            const { id } = message;\n            if (id !== null && ongoingRequests.has(id)) {\n                const { reject, resolve } = ongoingRequests.get(id);\n                ongoingRequests.delete(id);\n                if (message.error === undefined) {\n                    resolve(message.result);\n                }\n                else {\n                    reject(new Error(message.error.message));\n                }\n            }\n        }));\n        if ((0,_guards_message_port__WEBPACK_IMPORTED_MODULE_1__.isMessagePort)(sender)) {\n            sender.start();\n        }\n        const call = (method, params = null, transferables = []) => {\n            return new Promise((resolve, reject) => {\n                const id = (0,fast_unique_numbers__WEBPACK_IMPORTED_MODULE_0__.generateUniqueNumber)(ongoingRequests);\n                ongoingRequests.set(id, { reject, resolve });\n                if (params === null) {\n                    sender.postMessage({ id, method }, transferables);\n                }\n                else {\n                    sender.postMessage({ id, method, params }, transferables);\n                }\n            });\n        };\n        const notify = (method, params, transferables = []) => {\n            sender.postMessage({ id: null, method, params }, transferables);\n        };\n        let functions = {};\n        for (const [key, handler] of Object.entries(fullBrokerImplementation)) {\n            functions = { ...functions, [key]: handler({ call, notify }) };\n        }\n        return { ...functions };\n    };\n};\n//# sourceMappingURL=module.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/broker-factory/build/es2019/module.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/broker-factory/build/es2019/types/broker-implementation.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/broker-factory/build/es2019/types/broker-implementation.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=broker-implementation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L3R5cGVzL2Jyb2tlci1pbXBsZW1lbnRhdGlvbi5qcyIsIm1hcHBpbmdzIjoiO0FBQVU7QUFDViIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2Jyb2tlci1mYWN0b3J5L2J1aWxkL2VzMjAxOS90eXBlcy9icm9rZXItaW1wbGVtZW50YXRpb24uanM/OTgyMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4vLyMgc291cmNlTWFwcGluZ1VSTD1icm9rZXItaW1wbGVtZW50YXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/broker-factory/build/es2019/types/broker-implementation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/broker-factory/build/es2019/types/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/broker-factory/build/es2019/types/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _broker_implementation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./broker-implementation */ \"(ssr)/./node_modules/broker-factory/build/es2019/types/broker-implementation.js\");\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L3R5cGVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7O0FBQXdDO0FBQ3hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvYnJva2VyLWZhY3RvcnkvYnVpbGQvZXMyMDE5L3R5cGVzL2luZGV4LmpzPzA3NDMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9icm9rZXItaW1wbGVtZW50YXRpb24nO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/broker-factory/build/es2019/types/index.js\n");

/***/ })

};
;