 'use client';

import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { MainLayout } from '@/components/layout';
import Link from 'next/link';

export default function WhatsNewPage() {
  const [sections, setSections] = useState<Array<{ 
    id: string; 
    content: string; 
    version: string; 
    date: string; 
    highlights: number;
  }>>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const loadMarkdown = async () => {
      try {
        const files = ['/0.0.6.md', '/0.0.5.md', '/0.0.4.md', '/0.0.3.md', '/0.0.2.md']; // newest first
        const results = await Promise.all(
          files.map(async (path) => {
            try {
              const res = await fetch(path);
              if (!res.ok) return null;
              const text = await res.text();
              
              // Extract version and date from filename and content
              const version = path.replace('/', '').replace('.md', '');
              const dateMatch = text.match(/Release date: (.+)/);
              const date = dateMatch ? dateMatch[1] : '';
              
              // Count features (lines starting with -)
              const highlights = (text.match(/^-/gm) || []).length;
              
              return { 
                id: path.replace('/', ''), 
                content: text, 
                version,
                date,
                highlights
              };
            } catch (e) {
              console.error(`Failed to load ${path}`, e);
              return null;
            }
          })
        );
        setSections(results.filter(Boolean) as Array<{ 
          id: string; 
          content: string; 
          version: string; 
          date: string; 
          highlights: number;
        }>);
      } catch (error) {
        console.error('Error loading markdown:', error);
      } finally {
        setLoading(false);
      }
    };

    loadMarkdown();
  }, []);

  if (loading) {
    return (
      <MainLayout>
        <div className="bg-gray-50 min-h-screen">
          <div className="mx-auto max-w-7xl px-6 lg:px-8 py-16">
            <div className="flex justify-center items-center min-h-[400px]">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-900"></div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="bg-gray-50 min-h-screen">
        {/* Hero Section */}
        <section className="text-black py-6">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 className="text-xl sm:text-2xl font-semibold text-gray-900">
              What's New in WorkplaceSLM
            </h1>
            <p className="text-sm text-gray-500 mt-1">Latest updates and improvements</p>
          </div>
        </section>

        {/* Release Notes */}
        <section className="pb-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="space-y-6">
              {sections.map((section, idx) => (
                <div key={section.id} className="relative">
                  {/* Timeline line */}
                  {idx < sections.length - 1 && (
                    <div className="absolute left-4 top-16 bottom-0 w-px bg-gray-200 hidden sm:block"></div>
                  )}
                  
                  <div className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                    {/* Header */}
                    <div className="px-4 sm:px-6 py-4 border-b border-gray-100">
                      <div className="flex items-center justify-between gap-4">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-8 h-8 bg-blue-900 text-white rounded-full text-sm font-medium">
                            {idx + 1}
                          </div>
                          <div>
                            <h3 className="text-base font-semibold text-gray-900">v{section.version}</h3>
                            <p className="text-xs text-gray-500">{section.date}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="inline-flex items-center px-2.5 py-1 rounded-md bg-blue-50 text-blue-700 text-xs font-medium">
                            {section.highlights} updates
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="px-4 sm:px-6 py-5">
                      {(() => {
                        // Parse the content to extract heading, subtitle, and features
                        const contentAfterDate = section.content.split('Release date:')[1]?.trim() || section.content;
                        const lines = contentAfterDate.split('\n').filter(line => line.trim());
                        
                        let heading = '';
                        let subtitle = '';
                        let featuresStartIndex = 0;
                        
                        // Find the heading (starts with ##)
                        const headingIndex = lines.findIndex(line => line.startsWith('## '));
                        if (headingIndex !== -1) {
                          heading = lines[headingIndex].replace('## ', '');
                          
                          // Find subtitle (next non-empty line after heading)
                          if (headingIndex + 1 < lines.length && lines[headingIndex + 1].trim()) {
                            subtitle = lines[headingIndex + 1].trim();
                            featuresStartIndex = headingIndex + 2;
                          } else {
                            featuresStartIndex = headingIndex + 1;
                          }
                        }
                        
                        // Get features content (everything after heading and subtitle)
                        const featuresContent = lines.slice(featuresStartIndex).join('\n');
                        
                        return (
                          <div>
                            {/* Heading */}
                            {heading && (
                              <h4 className="text-base font-medium text-gray-900 mb-2">
                                {heading}
                              </h4>
                            )}
                            
                            {/* Subtitle as quote */}
                            {subtitle && (
                              <blockquote className="pl-3 py-1.5 mb-4 border-l-2 border-blue-300 bg-blue-50/50 rounded-r">
                                <p className="text-sm text-gray-600 italic">{subtitle}</p>
                              </blockquote>
                            )}
                            
                            {/* Features */}
                            {featuresContent && (
                              <ReactMarkdown
                                remarkPlugins={[remarkGfm]}
                                components={{
                                  p: ({ children }) => (
                                    <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                                      {children}
                                    </p>
                                  ),
                                  ul: ({ children }) => (
                                    <div className="space-y-2">
                                      {children}
                                    </div>
                                  ),
                                  li: ({ children }) => {
                                    return (
                                      <div className="flex items-start gap-2.5 p-3 rounded-lg bg-gray-50 hover:bg-blue-50/50 transition-colors">
                                        <div className="flex-shrink-0 w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
                                          <svg className="w-3 h-3 text-blue-900" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                          </svg>
                                        </div>
                                        <div className="flex-1 text-sm text-gray-700 leading-relaxed">
                                          {children}
                                        </div>
                                      </div>
                                    );
                                  },
                                  strong: ({ children }) => (
                                    <strong className="font-semibold text-gray-900">{children}</strong>
                                  ),
                                  em: ({ children }) => (
                                    <em className="italic text-gray-600">{children}</em>
                                  ),
                                }}
                              >
                                {featuresContent}
                              </ReactMarkdown>
                            )}
                          </div>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Feedback Section */}
        <section className="py-8 bg-white border-t border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Your Feedback Shapes Our Roadmap
              </h3>
              <p className="text-sm text-gray-600 max-w-2xl mx-auto">
                We're constantly improving WorkplaceSLM based on your needs. Share what's missing or what could be better!
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* Sales Contact */}
              <div className="bg-blue-50/50 rounded-lg p-4 border border-blue-100">
                <div className="flex items-center gap-2.5 mb-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-blue-900" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  </div>
                  <h4 className="text-sm font-medium text-gray-900">Contact Sales</h4>
                </div>
                <p className="text-xs text-gray-600 mb-3">
                  Questions about features, pricing, or new capabilities?
                </p>
                <a 
                  href="mailto:<EMAIL>" 
                  className="inline-flex items-center gap-1.5 text-xs text-blue-900 hover:text-blue-700 font-medium"
                >
                  <span><EMAIL></span>
                  <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>

              {/* Support Tickets */}
              <div className="bg-green-50/50 rounded-lg p-4 border border-green-100">
                <div className="flex items-center gap-2.5 mb-3">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h4 className="text-sm font-medium text-gray-900">Get Support</h4>
                </div>
                <p className="text-xs text-gray-600 mb-3">
                  Need help with a technical issue or have a bug to report?
                </p>
                <p className="text-xs text-green-700 font-medium">
                  Create a support ticket from your dashboard
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </MainLayout>
  );
}