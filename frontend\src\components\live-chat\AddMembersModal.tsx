import React, { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { useChat } from '@/contexts/Livechatcontext';

interface AddMembersModalProps {
  isOpen: boolean;
  onClose: () => void;
  groupId: number;
}

export default function AddMembersModal({ isOpen, onClose, groupId }: AddMembersModalProps) {
  const { state, addGroupMembers } = useChat();
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [submitting, setSubmitting] = useState(false);

  const toggleSelect = (id: number) => {
    setSelectedIds(prev => (prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id]));
  };

  const handleAdd = async () => {
    if (selectedIds.length === 0) return;
    setSubmitting(true);
    await addGroupMembers(groupId, selectedIds);
    setSubmitting(false);
    setSelectedIds([]);
    onClose();
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="fixed z-50 inset-0 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-black opacity-30" aria-hidden="true" />
        <div className="bg-white rounded-lg max-w-md w-full mx-auto p-6 relative z-10">
          <Dialog.Title className="text-lg font-medium text-gray-800 mb-4">Add Members</Dialog.Title>
          <div className="space-y-4">
            <div className="max-h-60 overflow-y-auto border rounded-md p-2 divide-y divide-gray-100">
              {state.allUsers.map(u => (
                <div key={u.user_id} className="flex items-center py-1">
                  <input
                    type="checkbox"
                    className="mr-2"
                    checked={selectedIds.includes(u.user_id)}
                    onChange={() => toggleSelect(u.user_id)}
                  />
                  <span>{u.username}</span>
                </div>
              ))}
            </div>
            <div className="flex justify-end space-x-2">
              <button onClick={onClose} className="px-4 py-2 text-sm rounded-md bg-gray-200 hover:bg-gray-300">Cancel</button>
              <button
                disabled={submitting || selectedIds.length === 0}
                onClick={handleAdd}
                className="px-4 py-2 text-sm rounded-md bg-blue-900 text-white hover:bg-blue-700 disabled:opacity-50"
              >
                {submitting ? 'Adding...' : 'Add'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
} 