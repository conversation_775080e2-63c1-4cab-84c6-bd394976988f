/**
 * Component for displaying note sources
 */

'use client';

import React from 'react';

interface NoteSourcesListProps {
  sourceLinks: string[];
}

export default function NoteSourcesList({ sourceLinks }: NoteSourcesListProps) {
  return (
    <div>
      <label className="block text-sm text-gray-700 mb-1">Sources</label>
      <div className="mb-3 rounded-lg text-sm max-h-24 overflow-y-auto border border-gray-200 bg-gray-50">
        {sourceLinks.length > 0 ? (
          <ul className="space-y-1 p-2">
            {sourceLinks.map((url) => (
              <li 
                key={url} 
                className="flex items-center justify-between gap-3 rounded-md border border-gray-200 bg-white p-2"
              >
                <div className="text-xs text-gray-600 break-all flex-1">{url}</div>
                <a
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-900 hover:text-blue-800 text-xs flex items-center gap-1"
                  title="Visit"
                >
                  Visit
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    className="h-3.5 w-3.5" 
                    viewBox="0 0 20 20" 
                    fill="currentColor"
                  >
                    <path d="M12.293 2.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414L9.414 16H5a1 1 0 01-1-1v-4.414l8.293-8.293z" />
                    <path d="M11 3l6 6" />
                  </svg>
                </a>
              </li>
            ))}
          </ul>
        ) : (
          <div className="p-3 text-xs text-gray-600">No sources defined.</div>
        )}
      </div>
    </div>
  );
}
