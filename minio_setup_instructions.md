# MinIO Setup Instructions for GPU VM

These instructions will help you set up a MinIO service on your GPU VM (43.230.202.228) with user-specific spaces and collections.

## 1. Deploy Min<PERSON> with Docker Compose

1. Copy the `minio-docker-compose.yml` file to your GPU VM:

```bash
scp minio-docker-compose.yml username@43.230.202.228:~/
```

2. SSH into your GPU VM:

```bash
ssh username@43.230.202.228
```

3. Create a directory for MinIO data (optional):

```bash
mkdir -p ~/minio-data
```

4. Start the MinIO service:

```bash
docker-compose -f minio-docker-compose.yml up -d
```

5. Verify that <PERSON><PERSON> is running:

```bash
docker ps
```

## 2. Access MinIO Console

1. Open a web browser and navigate to:
   - http://43.230.202.228:9001

2. Log in with the credentials:
   - Username: `miniouser`
   - Password: `miniopassword`

## 3. Integrate with Your Application

1. Copy the `user_minio_service.py` file to your project:

```bash
scp user_minio_service.py username@43.230.202.228:~/your-project/
```

2. Import and use the service in your FastAPI application:

```python
from user_minio_service import user_minio_service

# In your FastAPI endpoint
@app.post("/upload")
async def upload_file(
    file: UploadFile,
    user_id: int,
    collection: str,
    current_user: User = Depends(get_current_user)
):
    # Validate that the current_user can access this user_id
    if current_user.id != user_id and not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # Upload file to user's collection
    result = await user_minio_service.upload_file(file, user_id, collection)
    return result

@app.get("/collections")
async def list_collections(
    user_id: int,
    current_user: User = Depends(get_current_user)
):
    # Validate that the current_user can access this user_id
    if current_user.id != user_id and not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # List collections for the user
    collections = user_minio_service.list_collections(user_id)
    return {"user_id": user_id, "collections": collections}

@app.get("/files")
async def list_files(
    user_id: int,
    collection: str,
    current_user: User = Depends(get_current_user)
):
    # Validate that the current_user can access this user_id
    if current_user.id != user_id and not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # List files in the user's collection
    files = user_minio_service.list_files(user_id, collection)
    return {"user_id": user_id, "collection": collection, "files": files}
```

## 4. User Space and Collection Structure

The MinIO service is organized as follows:

1. Each user gets their own bucket named `user-{user_id}`
2. Within each user's bucket, files are organized by collections
3. Each collection is a directory within the user's bucket
4. Files within a collection are organized by date

Example structure:
```
user-123/
  ├── documents/
  │   ├── 2023/05/20/
  │   │   ├── uuid_document1.pdf
  │   │   └── uuid_document2.docx
  │   └── 2023/05/21/
  │       └── uuid_document3.pdf
  ├── images/
  │   └── 2023/05/20/
  │       ├── uuid_image1.jpg
  │       └── uuid_image2.png
  └── projects/
      └── 2023/05/20/
          └── uuid_project1.zip
```

## 5. Security Considerations

1. Update the MinIO credentials in the Docker Compose file and `user_minio_service.py` for production use
2. Consider enabling HTTPS for production
3. Implement proper access controls in your application
4. Set up regular backups of your MinIO data

## 6. Troubleshooting

1. If MinIO is not accessible, check:
   - Firewall settings (ports 9000 and 9001 should be open)
   - Docker container status (`docker ps`)
   - Docker logs (`docker logs minio-docker-compose_minio_1`)

2. If uploads fail, check:
   - Disk space on the VM
   - MinIO service health (`user_minio_service.health_check()`)
   - Network connectivity between your application and MinIO
