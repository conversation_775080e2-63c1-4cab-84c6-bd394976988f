import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';

interface ReferenceParserProps {
  content: string;
  customRenderers?: any;
}

const ReferenceParser: React.FC<ReferenceParserProps> = ({ content, customRenderers }) => {
  const enhancedRenderers = {
    ...customRenderers,
    h1: ({ node, ...props }: any) => (
      <h1 className="text-2xl font-bold mt-6 mb-4 text-gray-900 border-b border-gray-200 pb-2" {...props} />
    ),
    h2: ({ node, ...props }: any) => (
      <h2 className="text-xl font-bold mt-5 mb-3 text-gray-900" {...props} />
    ),
    h3: ({ node, ...props }: any) => (
      <h3 className="text-lg font-semibold mt-4 mb-2 text-gray-900" {...props} />
    ),
    h4: ({ node, ...props }: any) => (
      <h4 className="text-base font-semibold mt-3 mb-2 text-gray-800" {...props} />
    ),
    p: ({ node, ...props }: any) => (
      <p className="mb-3 leading-relaxed text-gray-800" {...props} />
    ),
    ul: ({ node, ...props }: any) => (
      <ul className="mb-3 ml-5 space-y-1 list-disc marker:text-gray-500" {...props} />
    ),
    ol: ({ node, ...props }: any) => (
      <ol className="mb-3 ml-5 space-y-1 list-decimal marker:text-gray-500" {...props} />
    ),
    li: ({ node, ...props }: any) => (
      <li className="leading-relaxed text-gray-800 pl-1" {...props} />
    ),
    blockquote: ({ node, ...props }: any) => (
      <blockquote className="border-l-4 border-gray-300 pl-4 py-2 my-3 italic text-gray-700 bg-gray-50" {...props} />
    ),
    code: ({ node, inline, className, children, ...props }: any) => {
      if (inline) {
        return (
          <code className="px-1.5 py-0.5 bg-gray-100 text-red-600 rounded text-sm font-mono" {...props}>
            {children}
          </code>
        );
      }
      return (
        <code className={`block p-3 bg-gray-900 text-gray-100 rounded-lg overflow-x-auto text-sm font-mono my-3 ${className || ''}`} {...props}>
          {children}
        </code>
      );
    },
    pre: ({ node, ...props }: any) => (
      <pre className="my-3 overflow-x-auto" {...props} />
    ),
    table: ({ node, ...props }: any) => (
      <div className="overflow-x-auto my-4">
        <table className="min-w-full divide-y divide-gray-300 border border-gray-300" {...props} />
      </div>
    ),
    thead: ({ node, ...props }: any) => (
      <thead className="bg-gray-100" {...props} />
    ),
    tbody: ({ node, ...props }: any) => (
      <tbody className="divide-y divide-gray-200 bg-white" {...props} />
    ),
    tr: ({ node, ...props }: any) => (
      <tr {...props} />
    ),
    th: ({ node, ...props }: any) => (
      <th className="px-4 py-2 text-left text-sm font-semibold text-gray-900 border-r border-gray-300 last:border-r-0" {...props} />
    ),
    td: ({ node, ...props }: any) => (
      <td className="px-4 py-2 text-sm text-gray-800 border-r border-gray-200 last:border-r-0" {...props} />
    ),
    strong: ({ node, ...props }: any) => (
      <strong className="font-semibold text-gray-900" {...props} />
    ),
    em: ({ node, ...props }: any) => (
      <em className="italic text-gray-700" {...props} />
    ),
    hr: ({ node, ...props }: any) => (
      <hr className="my-6 border-t border-gray-300" {...props} />
    ),
    a: ({ node, ...props }: any) => (
      <a
        {...props}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 hover:text-blue-800 underline"
      />
    ),
  };
  // Function to parse and render content with inline reference buttons
  const renderContentWithReferences = (text: string) => {
    // Simple regex to match reference patterns
    const referenceRegex = /Reference:\s*(?:"([^"]+),?"\s*([^(]+?)\s*\(([^)]+)\)|"([^"]+)"\s*\(([^)]+)\)|([^(]+?)\s*\(([^)]+)\)|(https?:\/\/[^\s]+))/gi;
    
    const parts = [];
    let lastIndex = 0;
    let keyIndex = 0;

    // Find all reference matches
    let match;
    while ((match = referenceRegex.exec(text)) !== null) {
      // Add text before the reference
      if (match.index > lastIndex) {
        const beforeText = text.slice(lastIndex, match.index);
        parts.push(
          <span key={`text-${keyIndex++}`}>
            <ReactMarkdown components={enhancedRenderers} remarkPlugins={[remarkGfm, remarkBreaks]}>
              {beforeText}
            </ReactMarkdown>
          </span>
        );
      }

      // Extract title and URL from the match
      let title = '';
      let url = '';

      if (match[3]) {
        // Pattern: "Title," Source (URL)
        title = match[2]?.trim() || match[1]?.replace(/,$/, '').trim() || '';
        url = match[3].trim();
      } else if (match[5]) {
        // Pattern: "Title" (URL)
        title = match[4]?.trim() || '';
        url = match[5].trim();
      } else if (match[7]) {
        // Pattern: Source (URL)
        title = match[6]?.trim() || '';
        url = match[7].trim();
      } else if (match[8]) {
        // Pattern: Just URL
        url = match[8].trim();
        try {
          const domain = new URL(url).hostname.replace('www.', '');
          title = domain;
        } catch {
          title = 'Link';
        }
      }

      // Add the reference button
      if (url) {
        parts.push(
          <a
            key={`ref-${keyIndex++}`}
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-1 px-2 py-0.5 mx-1 text-xs bg-gray-100 text-gray-700 border border-gray-200 rounded-full hover:bg-gray-200 hover:border-gray-300 transition-colors"
            title={title}
          >
            <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
            <span className="truncate max-w-20">
              {title.length > 15 ? `${title.substring(0, 15)}...` : title}
            </span>
          </a>
        );
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text after all references
    if (lastIndex < text.length) {
      const remainingText = text.slice(lastIndex);
      parts.push(
        <span key={`text-${keyIndex++}`}>
          <ReactMarkdown components={enhancedRenderers} remarkPlugins={[remarkGfm, remarkBreaks]}>
            {remainingText}
          </ReactMarkdown>
        </span>
      );
    }

    return parts;
  };

  // Check if content has references
  const hasReferences = /Reference:\s*/.test(content);
  
  if (!hasReferences) {
    // No references, render normal markdown
    return (
      <div className="prose prose-sm max-w-none">
        <ReactMarkdown components={enhancedRenderers} remarkPlugins={[remarkGfm, remarkBreaks]}>
          {content}
        </ReactMarkdown>
      </div>
    );
  }

  // Render with inline references
  const contentParts = renderContentWithReferences(content);
  
  return (
    <div className="prose prose-sm max-w-none">
      {contentParts}
    </div>
  );
};

export default ReferenceParser;
