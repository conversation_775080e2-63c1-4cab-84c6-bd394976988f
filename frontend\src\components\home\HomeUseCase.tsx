
import { motion, useScroll, useTransform } from "framer-motion";
import { useRef } from "react";
import Link from "next/link";

export default function UseCases() {

  const containerRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  // Circles animate from bottom with scroll - one by one
  const getCircleY = (index: number) => {
    const start = 0.1 + (index * 0.1);
    const end = start + 0.15;
    return useTransform(scrollYProgress, [start, end], [300, 0]);
  };

  const getCircleOpacity = (index: number) => {
    const start = 0.1 + (index * 0.1);
    const end = start + 0.15;
    return useTransform(scrollYProgress, [start, end], [0, index === 0 || index === 2 ? 0.7 : 1]);
  };

  // Text items animate from bottom with scroll
  const getTextY = (index: number) => {
    const start = 0.2 + (index * 0.1);
    const end = start + 0.15;
    return useTransform(scrollYProgress, [start, end], [300, 0]);
  };

  const getTextOpacity = (index: number) => {
    const start = 0.2 + (index * 0.1);
    const end = start + 0.15;
    return useTransform(scrollYProgress, [start, end], [0, 1]);
  };


  return (
    <>
      <section className="w-full bg-gray-100 py-12 px-8 sm:px-12 lg:px-16">
        <div className="w-full">
          {/* Scroll container - provides scroll space for animations */}
          <div ref={containerRef} className="relative w-full mb-12 min-h-[1200px]">
            {/* Sticky wrapper - keeps ALL content visible during scroll animation */}
            <div className="sticky top-24 w-full bg-gray-100 pb-4">
              {/* Heading - part of sticky content */}
              <div className="w-full mb-4">
                <h2 className="text-5xl sm:text-6xl font-bold text-black text-left">
                  Use Cases
                </h2>
              </div>

              {/* Horizontal line below heading */}
              <div className="w-full h-1 bg-gray-300 mb-6"></div>

              {/* Circles - animate from bottom on scroll */}
              <div className="relative w-full mb-16 sm:mb-0">
                <div className="flex items-center justify-start lg:absolute lg:top-0 lg:left-0 pointer-events-none">
                  {[0, 1, 2, 3].map((index) => (
                    <motion.div
                      key={index}
                      style={{
                        y: getCircleY(index),
                        opacity: getCircleOpacity(index)
                      }}
                      className={`w-14 h-14 sm:w-16 sm:h-16 lg:w-20 lg:h-20 rounded-full -ml-3 first:ml-0 ${index === 0 ? "bg-purple-800" :
                        index === 1 ? "bg-purple-600" :
                          index === 2 ? "bg-purple-500" :
                            "bg-purple-300"
                        }`}
                    />
                  ))}
                </div>
              </div>

              {/* Text items - animate from bottom on scroll */}
              <div className="flex justify-start lg:justify-end w-full">
                <div className="grid gap-4 sm:gap-5 md:gap-6 lg:gap-8 w-full lg:max-w-3xl lg:pr-4 xl:pr-8">
                  {["Students", "Researchers", "Professionals", "Lawyers"].map(
                    (item, idx) => (
                      <motion.div
                        key={idx}
                        style={{
                          opacity: getTextOpacity(idx),
                          y: getTextY(idx)
                        }}
                        className="flex flex-col items-start"
                      >
                        <p className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-6xl text-black font-light text-left">
                          {item}
                        </p>
                        {idx < 3 && (
                          <div className="border-t border-gray-300 w-full mt-3 sm:mt-4"></div>
                        )}
                      </motion.div>
                    )
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Button - bottom right (outside scroll container) */}
          <div className="w-full flex justify-end mt-8 ">
            <Link href="/faq">
              <button className="group bg-purple-700/90 text-white px-10 py-4 rounded-full hover:bg-white/20 hover:text-black border border-purple-700 hover:border-black transition-all duration-300 flex items-center gap-2 mr-12">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-6 h-6 -ml-3 transition-transform duration-300 group-hover:rotate-90"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                  />
                </svg>
                All Features
              </button>
            </Link>
          </div>
        </div>
      </section>

    </>
  )
}
