/**
 * Knowledge Base API functions
 * Handles all API calls related to spaces, documents, and knowledge management
 */

import { API_BASE_URL, getSecureToken } from '@/lib/api';
import type {
  UserSpace,
  KnowledgeDocument,
  BulkUploadResponse,
  IndexingResponse,
  CreateSpaceRequest,
  SpaceMembership,
  BasicUser
} from '@/types/knowledge-base';

/**
 * Generic API call helper with authentication
 */
export const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('access_token');
  const baseUrl = API_BASE_URL;
  const fullUrl = `${baseUrl}${endpoint}`;

  const response = await fetch(fullUrl, {
    ...options,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });

  return response.json();
};

/**
 * Delete a space by ID
 */
export const deleteSpace = async (spaceId: number) => {
  const data = await apiCall(`/spaces/${spaceId}`, {
    method: 'DELETE',
  });
  return data;
};

/**
 * Create a new text document
 * Enhanced version with better error handling
 */
export const createTextDocument = async (docData: {
  title: string;
  description?: string;
  space_id: number;
  document_type: string;
  content: string;
}): Promise<KnowledgeDocument> => {
  const token = localStorage.getItem('access_token');
  if (!token) throw new Error('Unauthorized');
  
  const response = await fetch(`${API_BASE_URL}/knowledge`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(docData),
  });
  
  if (!response.ok) {
    const errorText = await response.text().catch(() => '');
    throw new Error(`Create text document failed: ${response.status} ${errorText}`);
  }
  
  return response.json();
};

/**
 * Upload a single document with duplicate detection
 * Enhanced version with recursive Promise handling for duplicate confirmation
 */
export const uploadDocument = async (
  file: File,
  title: string,
  description: string,
  spaceId: number,
  setSingleDuplicateState?: (state: { indexed: boolean; resolve: (allow: boolean) => void } | null) => void
) => {
  const token = localStorage.getItem('access_token');
  const formData = new FormData();
  formData.append('file', file);
  formData.append('title', title);
  formData.append('description', description || '');
  formData.append('space_id', spaceId.toString());

  try {
    const doUpload = async (): Promise<any> => {
      const response = await fetch(`${API_BASE_URL}/knowledge/upload/single`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Upload error response:', response.status, errorText);
        throw new Error(`Upload failed: ${response.status} ${errorText}`);
      }
      
      const data = await response.json();
      const res = { ...data, "status": "success" }

      if (data.warning === 'duplicate' && setSingleDuplicateState) {
        return new Promise((resolve) => {
          setSingleDuplicateState({
            indexed: !!data.indexed,
            resolve: (allow: boolean) => {
              if (!allow) {
                resolve({ cancelled: true, ...data });
              } else {
                formData.set('allow_duplicate', 'true');
                doUpload().then(resolve).catch(resolve);
              }
            }
          });
        });
      }

      return res;
    };

    return doUpload();
  } catch (error) {
    console.error('Upload document error:', error);
    throw error;
  }
};

/**
 * Retry bulk upload with specific allow_duplicate setting
 * Used internally for handling duplicate confirmation flows
 */
export const retryBulkUpload = async (
  files: File[],
  spaceId: number,
  allowDuplicate: boolean
): Promise<BulkUploadResponse> => {
  const token = localStorage.getItem('access_token');
  const formData = new FormData();
  files.forEach(file => formData.append('files', file));
  formData.append('space_id', spaceId.toString());
  formData.append('allow_duplicate', allowDuplicate.toString());

  const response = await fetch(`${API_BASE_URL}/knowledge/upload/bulk`, {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` },
    body: formData,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Retry bulk upload failed: ${response.status} ${errorText}`);
  }

  return await response.json();
};

/**
 * Bulk upload multiple documents with duplicate detection
 * Enhanced version with Promise-based duplicate handling
 */
export const bulkUploadDocuments = async (
  files: File[],
  spaceId: number,
  setDuplicateModalState?: (state: {
    files: File[];
    info: any[];
    spaceId: number;
    initialUploaded: number;
    resolve: (value: any) => void;
  } | null) => void,
  setSpaceDocuments?: (updater: (prev: Map<number, KnowledgeDocument[]>) => Map<number, KnowledgeDocument[]>) => void
): Promise<BulkUploadResponse> => {
  const token = localStorage.getItem('access_token');
  const formData = new FormData();
  files.forEach(file => formData.append('files', file));
  formData.append('space_id', spaceId.toString());
  formData.append('allow_duplicate', 'false');

  try {
    const response = await fetch(`${API_BASE_URL}/knowledge/upload/bulk`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Bulk upload error response:', response.status, errorText);
      throw new Error(`Bulk upload failed: ${response.status} ${errorText}`);
    }

    const data = await response.json();

    // If duplicate files are detected, show the confirmation modal to the user
    if (data.duplicate_files && data.duplicate_files.length > 0) {
      // still merge the successfully uploaded (non-duplicate) docs into local state now
      if (Array.isArray(data.uploaded_documents) && data.uploaded_documents.length > 0 && setSpaceDocuments) {
        setSpaceDocuments((prev) => {
          const map = new Map(prev);
          const existing = map.get(spaceId) || [];
          const merged: KnowledgeDocument[] = [...(data.uploaded_documents as KnowledgeDocument[]), ...existing];
          map.set(spaceId, merged);
          return map;
        });
      }
      
      if (setDuplicateModalState) {
        return new Promise(async (resolve) => {
          const initialUploadedCount = Array.isArray(data.uploaded_documents) ? data.uploaded_documents.length : data.uploaded_count;
          setDuplicateModalState({ files, info: data.duplicate_files, spaceId, initialUploaded: initialUploadedCount, resolve });
        });
      }
    }

    return data;
  } catch (error) {
    console.error('Bulk upload error:', error);
    throw error;
  }
};

/**
 * Get a specific document by ID
 */
export const getDocument = async (documentId: number): Promise<KnowledgeDocument> => {
  const token = localStorage.getItem('access_token');
  const response = await fetch(`${API_BASE_URL}/knowledge/${documentId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
  const data = await response.json();
  return data;
};

/**
 * Delete a document by ID
 */
export const deleteDocument = async (documentId: number): Promise<void> => {
  const token = localStorage.getItem('access_token');
  const resp = await fetch(`${API_BASE_URL}/knowledge/${documentId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!resp.ok) {
    const errorText = await resp.text();
    throw new Error(`Delete failed: ${resp.status} ${errorText}`);
  }
};

/**
 * Download document attachment (for preview or download)
 * Returns blob that can be used to create object URL or trigger download
 */
export const downloadDocumentAttachment = async (documentId: number): Promise<Blob> => {
  const token = getSecureToken();
  const url = `${API_BASE_URL}/knowledge/${documentId}/download_attachment?token=${token || ''}`;
  
  const response = await fetch(url, {
    headers: token ? { Authorization: `Bearer ${token}` } : {},
  });
  
  if (!response.ok) {
    throw new Error(`Failed to download attachment: ${response.status}`);
  }
  
  return await response.blob();
};

/**
 * Index a document or all documents in a space
 * @param documentId - Document ID to index
 * @param spaceId - Space ID (required for indexing)
 */
export const indexDocument = async (documentId: number, spaceId: number): Promise<IndexingResponse> => {
  if (!spaceId || Number.isNaN(spaceId)) {
    console.error('No space selected for indexing document');
    throw new Error('Space ID is required for indexing');
  }

  const result = await apiCall(
    `/knowledge/space/${spaceId}/index`,
    { method: 'POST', body: JSON.stringify({ document_ids: [documentId] }) }
  );

  return result;
};

/**
 * Index all documents in a space
 */
export const indexAllDocuments = async (spaceId: number): Promise<IndexingResponse> => {
  const token = localStorage.getItem('access_token');
  const response = await fetch(`${API_BASE_URL}/knowledge/space/${spaceId}/index`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ document_ids: null }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Indexing failed: ${response.status} ${errorText}`);
  }

  return await response.json();
};

/**
 * Fetch all spaces with optional filtering by type
 */
export const fetchAllSpaces = async (space_type?: 'personal' | 'shared'): Promise<UserSpace[] | undefined> => {
  const token = localStorage.getItem('access_token');
  if (!token) return;
  
  const url = space_type 
    ? `${API_BASE_URL}/spaces/?space_type=${space_type}`
    : `${API_BASE_URL}/spaces/`;
    
  const res = await fetch(url, {
    headers: { Authorization: `Bearer ${token}` }
  });
  
  if (res.ok) {
    const data = await res.json();
    return data;
  }
};

/**
 * Create a new space
 */
export const createSpace = async (spaceData: CreateSpaceRequest): Promise<UserSpace> => {
  const token = localStorage.getItem('access_token');
  if (!token) throw new Error('No access token');
  
  const res = await fetch(`${API_BASE_URL}/spaces/`, {
    method: 'POST',
    headers: { 
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(spaceData)
  });
  
  if (!res.ok) {
    throw new Error(`Failed to create space: ${res.statusText}`);
  }
  
  return res.json();
};

/**
 * Get space details with members
 */
export const getSpaceWithMembers = async (spaceId: number) => {
  const token = localStorage.getItem('access_token');
  if (!token) throw new Error('No access token');
  
  const res = await fetch(`${API_BASE_URL}/spaces/${spaceId}`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  
  if (!res.ok) {
    throw new Error(`Failed to get space: ${res.statusText}`);
  }
  
  return res.json();
};

/**
 * Add members to a space
 */
export const addMembersToSpace = async (
  spaceId: number,
  userIds: number[],
  role: 'admin' | 'member' = 'member'
): Promise<SpaceMembership[]> => {
  const token = localStorage.getItem('access_token');
  if (!token) throw new Error('No access token');
  
  const res = await fetch(`${API_BASE_URL}/spaces/${spaceId}/members`, {
    method: 'POST',
    headers: { 
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ user_ids: userIds, role })
  });
  
  if (!res.ok) {
    throw new Error(`Failed to add members: ${res.statusText}`);
  }
  
  return res.json();
};

/**
 * Remove members from a space
 */
export const removeMembersFromSpace = async (spaceId: number, userIds: number[]) => {
  const token = localStorage.getItem('access_token');
  if (!token) throw new Error('No access token');
  
  const res = await fetch(`${API_BASE_URL}/spaces/${spaceId}/members`, {
    method: 'DELETE',
    headers: { 
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ user_ids: userIds })
  });
  
  if (!res.ok) {
    throw new Error(`Failed to remove members: ${res.statusText}`);
  }
  
  return res.json();
};

/**
 * Fetch all users (for selecting space members)
 */
export const fetchAllUsers = async (): Promise<BasicUser[]> => {
  const token = localStorage.getItem('access_token');
  if (!token) throw new Error('No access token');

  const res = await fetch(`${API_BASE_URL}/chat/users/all`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  
  if (!res.ok) {
    throw new Error(`Failed to fetch users: ${res.statusText}`);
  }
  
  return res.json();
};

/**
 * Fetch all documents in a space
 */
export const fetchSpaceDocuments = async (spaceId: number): Promise<KnowledgeDocument[]> => {
  const token = localStorage.getItem('access_token');
  if (!token) throw Object.assign(new Error('Unauthorized'), { status: 401 });

  const res = await fetch(`${API_BASE_URL}/knowledge/space/${spaceId}/documents`, {
    headers: { Authorization: `Bearer ${token}` }
  });

  if (!res.ok) {
    const error = new Error(`Failed to fetch documents for space ${spaceId}: ${res.status}`);
    (error as any).status = res.status;
    throw error;
  }

  return res.json();
};

