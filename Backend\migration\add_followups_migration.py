#!/usr/bin/env python3
"""
Database migration to add suggested_followups field to expert_chat_interactions table
"""

import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.config import DATABASE_URL

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_add_followups_field():
    """Add suggested_followups JSON field to expert_chat_interactions table"""
    if not DATABASE_URL:
        logger.error("DATABASE_URL not found in environment variables")
        return False

    try:
        logger.info(f"Using DATABASE_URL for migration: {DATABASE_URL}")
        engine = create_engine(DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()

        logger.info("Starting migration: Adding suggested_followups to expert_chat_interactions")

        # Check if column already exists
        check_column_sql = """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'expert_chat_interactions' 
        AND column_name = 'suggested_followups'
        """

        result = db.execute(text(check_column_sql)).fetchone()
        if result:
            logger.info("suggested_followups column already exists, applying backfill and default if needed")
            # Backfill existing rows with empty array and set default
            db.execute(text("""
            UPDATE expert_chat_interactions
            SET suggested_followups = '[]'::jsonb
            WHERE suggested_followups IS NULL
            """))
            db.execute(text("""
            ALTER TABLE expert_chat_interactions
            ALTER COLUMN suggested_followups SET DEFAULT '[]'::jsonb
            """))
            db.commit()
            logger.info("✅ Backfill and default setting completed successfully!")
            return True

        # Add the suggested_followups column
        alter_sql = """
        ALTER TABLE expert_chat_interactions 
        ADD COLUMN suggested_followups JSONB NULL
        """
        db.execute(text(alter_sql))
        db.commit()

        # Backfill existing rows with empty array and set default
        logger.info("Backfilling null suggested_followups with empty arrays and setting default")
        db.execute(text("""
        UPDATE expert_chat_interactions
        SET suggested_followups = '[]'::jsonb
        WHERE suggested_followups IS NULL
        """))
        db.execute(text("""
        ALTER TABLE expert_chat_interactions
        ALTER COLUMN suggested_followups SET DEFAULT '[]'::jsonb
        """))
        db.commit()

        logger.info("✅ Successfully added suggested_followups column to expert_chat_interactions table")

        # Verify the column was added
        verify_sql = """
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'expert_chat_interactions' 
        AND column_name = 'suggested_followups'
        """
        result = db.execute(text(verify_sql)).fetchone()
        if result:
            logger.info(f"✅ Verification successful: suggested_followups column added with type {result[1]}")
        else:
            logger.error("❌ Verification failed: suggested_followups column not found after migration")
            return False

        return True

    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        if 'db' in locals():
            db.rollback()
        return False
    finally:
        if 'db' in locals():
            db.close()

if __name__ == "__main__":
    print("🔄 Starting database migration...")
    print("Adding suggested_followups field to expert_chat_interactions table")
    success = migrate_add_followups_field()
    if success:
        print("✅ Migration completed successfully!")
    else:
        print("❌ Migration failed. Please check the logs above.")
        exit(1)


