import React, { useState, useEffect, useRef } from 'react';
import { API_BASE_URL, getSecureToken } from '@/lib/api';
import dynamic from 'next/dynamic';
// We removed react-pdf from preview to avoid CORS issues; keep import for tree-shaking.

import { pdfjs } from 'react-pdf';
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

// Lazy load docx-preview only when needed to keep bundle small
// @ts-ignore
const loadDocxPreview = () => import('docx-preview').then(mod => mod.renderAsync);

// Lazy load pptx-preview only when needed
// @ts-ignore
const loadPptxPreview = () => import('pptx-preview').then(mod => mod.renderPptx || mod.default);

// Lazy load xlsx-preview only when needed
// @ts-ignore
const loadXlsxPreview = () => import('xlsx-preview').then(mod => mod.previewXlsxFile);

interface Props {
  url: string;
  type: string; // message.message_type ("image" | "file")
  onClick: () => void;
}

function getExt(url: string): string {
  return url.split('?')[0].split('#')[0].split('.').pop()?.toLowerCase() || '';
}

// Helper: prettify filename by removing leading UUID_ and decoding URI-encoding
function prettifyFilename(input: string): string {
  try {
    const decoded = decodeURIComponent(input);
    // UUID v4 pattern followed by underscore
    return decoded.replace(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}_/i, '');
  } catch {
    // Fallback – just return input on decode error
    return input;
  }
}

export default function AttachmentPreview({ url, type, onClick }: Props) {
  const [docxRendered, setDocxRendered] = useState(false);
  const [pptxRendered, setPptxRendered] = useState(false);
  const [xlsxRendered, setXlsxRendered] = useState(false);
  const [showFullName, setShowFullName] = useState(false);

  let meta: any = null;
  try { meta = JSON.parse(url); } catch {}
  const fileUrl = (() => {
    if (meta?.minio_bucket && meta?.minio_object_key) {
      const base = `${API_BASE_URL}/chat/attachments/download?bucket_name=${meta.minio_bucket}&object_key=${encodeURIComponent(
        meta.minio_object_key
      )}`;
      const token = getSecureToken();
      return token ? `${base}&token=${token}` : base;
    }
    if (meta?.url) {
      return meta.url as string;
    }
    return url;
  })();

  const basename = fileUrl.split('?')[0].split('#')[0].split('/').pop() || 'file';
  const rawName = meta?.filename || meta?.name || prettifyFilename(basename);
  const displayName = showFullName ? rawName : (rawName.length>30 ? rawName.slice(0,27)+'...' : rawName);
  const toggle = rawName.length>30 ? (
    <button onClick={e=>{e.stopPropagation();setShowFullName(!showFullName);}} className="text-[10px] text-gray-300 underline mt-0.5">
      {showFullName?'See less':'See more'}
    </button>) : null;
  const docxRef = useRef<HTMLDivElement>(null);
  const pptxRef = useRef<HTMLDivElement>(null);
  const xlsxRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (type !== 'file') return;
    const ext = getExt(fileUrl);
    if (ext !== 'docx') return;
    if (docxRendered) return;
    if (!docxRef.current) return;

    (async () => {
      try {
        const renderAsync = await loadDocxPreview();
        const resp = await fetch(fileUrl);
        if (!resp.ok) throw new Error('network');
        const blob = await resp.blob();
        await renderAsync(blob, docxRef.current!, undefined, {
          className: 'docx-preview',
          inWrapper: false,
        });
        setDocxRendered(true);
      } catch {
        // ignore, fallback icon will show
      }
    })();
  }, [url, type]);

  // PPT/PPTX preview rendering
  useEffect(() => {
    if (type !== 'file') return;
    const ext = getExt(fileUrl);
    if (!['ppt', 'pptx'].includes(ext)) return;
    if (pptxRendered) return;
    if (!pptxRef.current) return;

    (async () => {
      try {
        const renderPptx = await loadPptxPreview();
        const resp = await fetch(fileUrl);
        if (!resp.ok) throw new Error('network');
        const blob = await resp.blob();

        // Clear any existing content
        pptxRef.current!.innerHTML = '';

        // Render PPT using the renderPptx function
        if (typeof renderPptx === 'function') {
          await renderPptx(blob, pptxRef.current, {});
          setPptxRendered(true);
        }
      } catch (error) {
        console.warn('PPT preview failed:', error);
        // ignore, fallback icon will show
      }
    })();
  }, [url, type]);

  // XLS/XLSX preview rendering
  useEffect(() => {
    if (type !== 'file') return;
    const ext = getExt(fileUrl);
    if (!['xls', 'xlsx'].includes(ext)) return;
    if (xlsxRendered) return;
    if (!xlsxRef.current) return;

    (async () => {
      try {
        const xlsxModule = await loadXlsxPreview();
        const resp = await fetch(fileUrl);
        if (!resp.ok) throw new Error('network');
        const blob = await resp.blob();
        // Try different possible API patterns
        if (typeof xlsxModule === 'function') {
          await xlsxModule(blob, xlsxRef.current, {
            maxRows: 10,
            maxCols: 10
          });
        } else if (xlsxModule.previewXlsxFile) {
          await xlsxModule.previewXlsxFile(blob, xlsxRef.current, {
            maxRows: 10,
            maxCols: 10
          });
        } else if (xlsxModule.default && typeof xlsxModule.default === 'function') {
          await xlsxModule.default(blob, xlsxRef.current, {
            maxRows: 10,
            maxCols: 10
          });
        }
        setXlsxRendered(true);
      } catch {
        // ignore, fallback icon will show
      }
    })();
  }, [url, type]);

  // image thumbnail directly
  if (type === 'image') {
    const filename = fileUrl.split('?')[0].split('#')[0].split('/').pop() || 'file';
    return (
      <div className="flex flex-col items-center max-w-[260px]">
        <img
          src={fileUrl}
          onClick={onClick}
          alt="attachment"
          className="max-w-full max-h-64 object-contain cursor-pointer rounded-md hover:opacity-90"
        />
        <p className="text-xs mt-1 break-all text-center text-gray-700 max-w-[220px]">{displayName}</p>
        {toggle}
      </div>
    );
  }

  // Generic file – decide based on extension
  const ext = getExt(fileUrl);
  const filename=rawName;

  // PDF – render first page thumbnail using react-pdf
  if (ext === 'pdf') {
    // Show placeholder PDF icon – cross-origin preview often fails
    return (
      <div className="flex flex-col items-center cursor-pointer max-w-[220px]" onClick={onClick}>
        <div className="w-32 h-40 flex flex-col items-center justify-center bg-red-50 text-red-700 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" className="w-10 h-10 mb-2" fill="currentColor" viewBox="0 0 24 24">
            <path d="M6 2a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6H6zm7 1.5L18.5 9H13V3.5z" />
          </svg>
          <span className="text-xs font-medium">PDF</span>
        </div>
        <p className="text-xs mt-1 break-all text-center text-gray-700 max-w-[180px]">{displayName}</p>
        {toggle}
      </div>
    );
  }

  // DOCX → show Word icon
  if (ext === 'docx') {
    return (
      <div className="flex flex-col items-center cursor-pointer max-w-[220px]" onClick={onClick}>
        <div className="w-36 h-48 bg-blue-50 flex flex-col items-center justify-center overflow-hidden rounded-lg">
          {docxRendered ? (
            <div ref={docxRef} className="scale-75 origin-top-left" />
          ) : (
            <div className="flex flex-col items-center text-blue-700">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-10 h-10 mb-1"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M6 2a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6H6zm7 1.5L18.5 9H13V3.5z" />
              </svg>
              <span className="text-xs font-medium">WORD</span>
            </div>
          )}
        </div>
        <p className="text-xs mt-1 break-all text-center text-gray-700 max-w-[180px]">{displayName}</p>
        {toggle}
      </div>
    );
  }

  // PPT/PPTX → show PowerPoint icon with preview
  if (['ppt', 'pptx'].includes(ext)) {
    return (
      <div className="flex flex-col items-center cursor-pointer max-w-[220px]" onClick={onClick}>
        <div className="w-36 h-48 bg-orange-50 flex flex-col items-center justify-center overflow-auto rounded-lg">
          {pptxRendered ? (
            <div ref={pptxRef} className="w-full h-full overflow-auto" />
          ) : (
            <div className="flex flex-col items-center text-orange-700">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-10 h-10 mb-1"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M6 2a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6H6zm7 1.5L18.5 9H13V3.5z" />
              </svg>
              <span className="text-xs font-medium">POWERPOINT</span>
            </div>
          )}
        </div>
        <p className="text-xs mt-1 break-all text-center text-gray-700 max-w-[180px]">{displayName}</p>
        {toggle}
      </div>
    );
  }

  // XLS/XLSX → show Excel icon with preview
  if (['xls', 'xlsx'].includes(ext)) {
    return (
      <div className="flex flex-col items-center cursor-pointer max-w-[220px]" onClick={onClick}>
        <div className="w-36 h-48 bg-green-50 flex flex-col items-center justify-center overflow-hidden rounded-lg">
          {xlsxRendered ? (
            <div ref={xlsxRef} className="scale-75 origin-top-left max-w-full max-h-full overflow-auto" />
          ) : (
            <div className="flex flex-col items-center text-green-700">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-10 h-10 mb-1"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M6 2a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6H6zm7 1.5L18.5 9H13V3.5z" />
              </svg>
              <span className="text-xs font-medium">EXCEL</span>
            </div>
          )}
        </div>
        <p className="text-xs mt-1 break-all text-center text-gray-700 max-w-[180px]">{displayName}</p>
        {toggle}
      </div>
    );
  }

  // Fallback – generic file icon
  return (
    <div
      onClick={onClick}
      className="w-32 h-40 flex flex-col items-center justify-center bg-gray-100 text-gray-600 cursor-pointer rounded-lg"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="w-10 h-10 mb-2"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        strokeWidth={1.5}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z"
        />
      </svg>
      <span className="text-xs font-medium">FILE</span>
      <p className="text-xs mt-1 break-all text-center text-gray-700 max-w-[180px]">{displayName}</p>
      {toggle}
    </div>
  );
} 