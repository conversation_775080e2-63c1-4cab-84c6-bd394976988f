"""Pydantic schemas for user spaces and memberships.

These models define request/response contracts for the user spaces feature,
including personal/shared spaces and membership management APIs.
"""

from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum


class SpaceTypeEnum(str, Enum):
    """Type of space a user owns or participates in.

    - personal: Owner-only space
    - shared: Space with additional members and roles
    """
    personal = "personal"
    shared = "shared"


class SpaceRoleEnum(str, Enum):
    """Role of a user within a shared space."""
    admin = "admin"
    member = "member"


class PlaygroundTypeEnum(str, Enum):
    """Type of playground content in a space.
    
    - documents: Space contains documents (PDFs, DOCX, etc.) with RAG indexing
    - images: Space contains images (JPEG, PNG, WebP, HEIC) without indexing
    """
    documents = "documents"
    images = "images"


class UserSpaceBase(BaseModel):
    """Base fields common to all space payloads."""
    name: str = Field(
        ..., min_length=1, max_length=100, description="Name of the space"
    )
    description: Optional[str] = Field(
        None, description="Brief description of the space"
    )
    space_type: SpaceTypeEnum = Field(
        SpaceTypeEnum.personal, description="Type of space (personal or shared)"
    )
    playground_type: PlaygroundTypeEnum = Field(
        PlaygroundTypeEnum.documents, description="Type of playground content (documents or images)"
    )


class UserSpaceCreate(UserSpaceBase):
    """Payload to create a user space (personal or shared)."""
    create_group: bool = Field(
        False, description="Whether to create a linked group chat for shared spaces"
    )

    class Config:
        # Allow population from SQLAlchemy model attributes (ORM mode replacement)
        from_attributes = True


class UserSpaceUpdate(BaseModel):
    """Partial update payload for space metadata and active status."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None)
    is_active: Optional[bool] = Field(None)

    class Config:
        # Allow population from SQLAlchemy model attributes (ORM mode replacement)
        from_attributes = True
        # Reject unknown fields defensively (e.g., playground_type)
        extra = 'forbid'


class UserSpaceResponse(UserSpaceBase):
    """Response model for a space, enriched with owner, counts, and flags."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    is_active: bool
    owner_id: int
    owner_username: Optional[str]
    collection_name: str
    document_count: int
    member_count: int
    is_shared: bool
    is_personal: bool
    linked_group_id: Optional[int] = Field(None, description="ID of the linked group chat if any")

    class Config:
        # Allow population from SQLAlchemy model attributes (ORM mode replacement)
        from_attributes = True


class SpaceMembershipBase(BaseModel):
    """Base fields for a space membership row."""
    user_id: int
    role: SpaceRoleEnum = Field(SpaceRoleEnum.member, description="Role in the space")


class SpaceMembershipCreate(SpaceMembershipBase):
    """Payload to create a membership mapping a user to a space."""
    pass


class SpaceMembershipUpdate(BaseModel):
    """Payload to update membership role within a shared space."""
    role: Optional[SpaceRoleEnum] = Field(None, description="New role for the member")


class SpaceMembershipResponse(SpaceMembershipBase):
    """Response model representing a space membership with denormalized usernames."""
    id: int
    space_id: int
    created_at: datetime
    created_by: int
    user_username: Optional[str]
    added_by_username: Optional[str]

    class Config:
        # Allow population from SQLAlchemy model attributes (ORM mode replacement)
        from_attributes = True


class AddMembersRequest(BaseModel):
    """Request to add multiple users to a shared space with a default role."""
    user_ids: List[int] = Field(..., min_items=1, description="List of user IDs to add")
    role: SpaceRoleEnum = Field(
        SpaceRoleEnum.member, description="Role to assign to all users"
    )


class RemoveMembersRequest(BaseModel):
    """Request to remove one or more users from a shared space."""
    user_ids: List[int] = Field(
        ..., min_items=1, description="List of user IDs to remove"
    )


class UserSpaceWithMembers(UserSpaceResponse):
    """Response model for a space including its resolved member list.

    Also includes `user_role` which reflects the current user's effective role
    in the space (derived at request time; not stored on the space record).
    """
    members: List[SpaceMembershipResponse] = Field(default_factory=list)
    user_role: Optional[str] = Field(
        None, description="Current user's role in this space"
    )
