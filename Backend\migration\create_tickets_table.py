#!/usr/bin/env python3
"""
Database migration script to create the tickets table if it does not exist
"""

import sys
import os

# Ensure Backend directory (containing the 'app' package) is on sys.path
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
BACKEND_DIR = os.path.dirname(CURRENT_DIR)
if BACKEND_DIR not in sys.path:
    sys.path.insert(0, BACKEND_DIR)

from sqlalchemy import text
from app.database import engine


def migrate_create_tickets_table():
    print("Starting migration: Creating tickets table if missing...")

    try:
        with engine.connect() as connection:
            trans = connection.begin()
            try:
                # Check if table exists
                result = connection.execute(text("""
                    SELECT to_regclass('public.tickets')
                """))
                exists = result.scalar() is not None
                if exists:
                    print("✅ tickets table already exists. Migration not needed.")
                    trans.rollback()
                    return True

                # Create table
                print("Creating tickets table...")
                connection.execute(text("""
                    CREATE TABLE tickets (
                        id SERIAL PRIMARY KEY,
                        title VARCHAR(200) NOT NULL,
                        description TEXT NOT NULL,
                        category VARCHAR(50) NOT NULL,  -- 'bug', 'feature', 'support', 'other'
                        priority VARCHAR(20) NOT NULL DEFAULT 'medium',  -- 'low', 'medium', 'high', 'urgent'
                        status VARCHAR(20) NOT NULL DEFAULT 'open',  -- 'open', 'in_progress', 'resolved', 'closed'
                        created_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                        assigned_to INTEGER NULL REFERENCES users(id) ON DELETE SET NULL,
                        created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
                        updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
                        resolved_at TIMESTAMP WITHOUT TIME ZONE NULL,
                        admin_notes TEXT NULL
                    )
                """))

                # Create indexes
                print("Creating indexes...")
                connection.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_tickets_created_by ON tickets (created_by);
                """))
                connection.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_tickets_assigned_to ON tickets (assigned_to);
                """))
                connection.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_tickets_status ON tickets (status);
                """))
                connection.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_tickets_category ON tickets (category);
                """))
                connection.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_tickets_priority ON tickets (priority);
                """))
                connection.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_tickets_created_at ON tickets (created_at);
                """))

                trans.commit()
                print("✅ Migration completed successfully!")
                return True
            except Exception as e:
                trans.rollback()
                print(f"❌ Error during migration: {e}")
                return False
    except Exception as e:
        print(f"❌ Failed to connect to database: {e}")
        return False


if __name__ == "__main__":
    success = migrate_create_tickets_table()
    if success:
        print("\n🎉 Tickets table ready!")
    else:
        print("\n💥 Migration failed! Please check logs.")
        sys.exit(1)