'use client';

import React from 'react';
import Link from 'next/link';
import { MainLayout } from '@/components/layout';
import Image from 'next/image';

export default function AboutPage() {
  return (
    <MainLayout>
      <div className="w-full min-h-screen max-w-8xl mx-auto px-6 lg:px-12 py-16 relative">
        {/* Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-[30%_70%] gap-8 lg:gap-12 relative">
          {/* Left Column - About Us Heading */}
          <div className="space-y-6 border-r border-black">
            <h1 className="text-5xl lg:text-7xl text-purple-600 mb-28 text-right pr-12">
              About<br />Us
            </h1>
            <p className="text-3xl text-orange-500 text-left pl-8">
              15+ years of transforming processes into intelligent partnerships.
            </p>
          </div>

          {/* Vertical Divider */}
          <div className="hidden lg:block absolute left-[calc(30%+3rem)] top-16 bottom-16 w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent" />

          {/* Right Column - Content */}
          <div className="space-y-8 relative">
            {/* Video Background Banner */}
            <div className="rounded-2xl overflow-hidden relative h-[160px] sm:h-[210px]">
              {/* Background Video */}
              <video
                autoPlay
                loop
                muted
                playsInline
                className="absolute inset-0 w-full h-full object-cover"
              >
                <source src="/images/bottom-bg.mp4" type="video/mp4" />
              </video>

              {/* Dark overlay */}
              <div className="absolute inset-0 bg-black/50" />

              {/* Content */}
              <div className="relative z-10 p-6 lg:p-10 flex items-start justify-start h-full">
                <h2 className="text-3xl lg:text-4xl font-bold text-white leading-tight text-left">
                  Empowering Businesses Through<br />
                  Human Intelligence and AI Innovation
                </h2>
              </div>
            </div>

            {/* Three Info Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <InfoCard>
                With over 15 years of operational excellence, ProcessVenue has been at the forefront of helping enterprises and global brands streamline operations, reduce costs, and enhance productivity through a process-driven technology-first approach.
              </InfoCard>
              
              <InfoCard>
                Our deep industry expertise and commitment to continuous innovation led to creation of Workplace SLM - an AI powered document assistant designed to make organizational knowledge accessible and secure.
              </InfoCard>
              
              <InfoCard>
                By combining human insight with advanced AI capabilities, we're building tools that empower businesses to make faster while maintaining trust, transparency and data integrity. From data management and customer support to AI-integrated business solutions, we create customized workflows that evolve with your organization's goals.
              </InfoCard>
            </div>

            {/* Company Description & Excellence Statement */}
            <div className="space-y-6">
              <p className="text-2xl text-center text-base text-purple-600 leading-relaxed">
                With <span className="text-purple-600 font-semibold">global operations</span>, <span className="text-purple-600 font-semibold">24/7 support</span>, and <span className="text-purple-600 font-semibold">proven excellence</span>,<br />
                we bring reliability and agility together under one roof.
              </p>

              {/* Founder Section */}
              <div className="grid grid-cols-1 md:grid-cols-[300px_1fr] gap-6 items-start bg-gray-50 rounded-2xl p-6">
                <div className="mx-auto md:mx-0">
                  <Image
                    src="/images/founder.jpg"
                    alt="Ankit Goyanka"
                    width={280}
                    height={350}
                    className="rounded-lg object-cover"
                  />
                </div>
                <div className="space-y-3">
                  <p className="text-gray-700 leading-relaxed">
                    Behind this mission is <span className="font-semibold">Ankit Goyanka</span>, a visionary entrepreneur whose leadership combines innovation with hands-on experience. Having built and scaled multiple successful ventures including one acquired by a major Silicon Valley firm — Ankit continues to steer ProcessVenue toward a future where humans and AI work in harmony to deliver smarter, faster, and more meaningful results.
                  </p>
                </div>
              </div>

              {/* CTA */}
              <div className="text-center pt-4">
                <Link
                  href="https://processvenue.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 text-purple-600 hover:text-purple-700 font-semibold text-lg transition-colors group"
                >
                  Learn More About ProcessVenue
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={2}
                    stroke="currentColor"
                    className="w-5 h-5 transition-transform group-hover:translate-x-1"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
                    />
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

function InfoCard({ children }: { children: React.ReactNode }) {
  return (
    <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
      <div className="mb-4 h-24 bg-gray-100 rounded-lg flex items-center justify-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="w-12 h-12 text-gray-400"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M2.25 13.5h3.86a2.25 2.25 0 0 1 2.012 1.244l.256.512a2.25 2.25 0 0 0 2.013 1.244h3.218a2.25 2.25 0 0 0 2.013-1.244l.256-.512a2.25 2.25 0 0 1 2.013-1.244h3.859m-19.5.338V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18v-4.162c0-.224-.034-.447-.1-.661L19.24 5.338a2.25 2.25 0 0 0-2.15-1.588H6.911a2.25 2.25 0 0 0-2.15 1.588L2.35 13.177a2.25 2.25 0 0 0-.1.661Z"
          />
        </svg>
      </div>
      <p className="text-gray-700 text-sm leading-relaxed">{children}</p>
    </div>
  );
}
