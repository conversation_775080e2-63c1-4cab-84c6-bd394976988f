from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from ..database import get_db
from ..models.user import User
from ..auth import require_admin
from ..services.redis_service import invalidate_cache, invalidate_admin_cache
from ..services.user_cleanup_service import user_cleanup_service
from .. import config

router = APIRouter(prefix="/user-deletion", tags=["User Deletion"])

@router.delete("/{user_id}/delete")
@invalidate_cache(f"{config.CACHE_PREFIX_ADMIN}:*")
async def delete_user_account(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete only the user account while preserving all associated data (admin only)
    This removes only:
    - User credentials and profile from user table
    - Cache and session data
    
    This preserves:
    - User spaces and documents
    - Chat history and interactions
    - Notes and feedback
    - Files in MinIO storage
    """
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Prevent deletion of admin accounts or self
    if user.role == "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Cannot delete admin accounts"
        )
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Cannot delete your own account"
        )
    
    # Get user account summary before deletion
    user_summary = await user_cleanup_service.get_user_account_deletion_summary(db, user_id)
    
    # Perform account-only deletion
    deletion_success = await user_cleanup_service.delete_user_account_only(db, user_id)
    
    if not deletion_success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user account"
        )
    
    # Invalidate admin cache
    await invalidate_admin_cache()
    
    return {
        "message": f"User account '{user_summary.get('username', user_id)}' has been deleted. All user data has been preserved.",
        "deleted_data": user_summary
    }

@router.get("/{user_id}/delete-preview")
async def get_user_deletion_preview(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    Get preview of what will be deleted for a user (admin only)
    This endpoint shows that only the user account will be deleted, preserving all data
    """
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Prevent preview for admin accounts
    if user.role == "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Cannot delete admin accounts"
        )
    
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Cannot delete your own account"
        )
    
    # Get user account deletion summary
    user_summary = await user_cleanup_service.get_user_account_deletion_summary(db, user_id)
    
    return {
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "role": user.role,
            "status": user.status,
            "created_at": user.created_at.isoformat() if user.created_at else None
        },
        "data_to_delete": user_summary,
        "warning": "This will delete only the user account. All user data (documents, chats, notes, etc.) will be preserved."
    }
