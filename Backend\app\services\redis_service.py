"""
Redis Service Module for Caching and Session Management
Production-ready implementation with connection pooling, error handling, and monitoring
"""

import json
import logging
import pickle
import asyncio
from typing import Any, Optional, Union, List, Dict, Callable
from functools import wraps
from datetime import datetime, timedelta
import hashlib

import redis
from redis import asyncio as aioredis
from redis.exceptions import RedisError, ConnectionError, TimeoutError
from redis.asyncio.connection import ConnectionPool

from .. import config
from fastapi.encoders import jsonable_encoder

# Configure logging
logger = logging.getLogger(__name__)


class RedisService:
    """
    Production-ready Redis service with async support, connection pooling,
    and comprehensive error handling
    """
    
    def __init__(self):
        self._sync_client: Optional[redis.Redis] = None
        self._async_client: Optional[aioredis.Redis] = None
        self._async_pool: Optional[ConnectionPool] = None
        self._is_connected: bool = False
        self._connection_retries: int = 3
        self._retry_delay: int = 1
        
    async def initialize_async(self) -> bool:
        """Initialize async Redis connection with connection pooling"""
        if self._is_connected:
            logger.debug("Redis client already initialized, skipping")
            return True

        try:
            # Create connection pool for better resource management
            self._async_pool = aioredis.ConnectionPool(
                host=config.REDIS_HOST,
                port=config.REDIS_PORT,
                db=config.REDIS_DB,
                password=config.REDIS_PASSWORD,
                decode_responses=config.REDIS_DECODE_RESPONSES,
                max_connections=config.REDIS_MAX_CONNECTIONS,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30,
            )
            
            self._async_client = aioredis.Redis(connection_pool=self._async_pool)
            
            # Test connection
            await self._async_client.ping()
            self._is_connected = True
            logger.info(f"Successfully connected to Redis at {config.REDIS_HOST}:{config.REDIS_PORT}")
            return True
            
        except (ConnectionError, TimeoutError) as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self._is_connected = False
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to Redis: {e}")
            self._is_connected = False
            return False
    
    def initialize_sync(self) -> bool:
        """Initialize synchronous Redis connection"""
        if self._sync_client is not None:
            logger.debug("Redis sync client already initialized, skipping")
            return True

        try:
            self._sync_client = redis.Redis(
                host=config.REDIS_HOST,
                port=config.REDIS_PORT,
                db=config.REDIS_DB,
                password=config.REDIS_PASSWORD,
                decode_responses=config.REDIS_DECODE_RESPONSES,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
            )
            
            # Test connection
            self._sync_client.ping()
            logger.info(f"Sync Redis client connected to {config.REDIS_HOST}:{config.REDIS_PORT}")
            return True
            
        except (ConnectionError, TimeoutError) as e:
            logger.error(f"Failed to connect sync Redis client: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error in sync Redis connection: {e}")
            return False
    
    async def close(self):
        """Close Redis connections gracefully"""
        try:
            if self._async_client:
                await self._async_client.close()
            if self._async_pool:
                await self._async_pool.disconnect()
            if self._sync_client:
                self._sync_client.close()
            self._is_connected = False
            logger.info("Redis connections closed successfully")
        except Exception as e:
            logger.error(f"Error closing Redis connections: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check Redis connection health and stats"""
        try:
            if not self._async_client:
                return {"status": "disconnected", "error": "Client not initialized"}
            
            # Ping Redis
            await self._async_client.ping()
            
            # Get Redis info
            info = await self._async_client.info()
            
            return {
                "status": "healthy",
                "connected": True,
                "server": f"{config.REDIS_HOST}:{config.REDIS_PORT}",
                "clients_connected": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "N/A"),
                "uptime_days": info.get("uptime_in_days", 0),
                "db_keys": await self._async_client.dbsize(),
            }
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return {
                "status": "unhealthy",
                "connected": False,
                "error": str(e)
            }
    
    # Core Cache Operations
    async def get(self, key: str, default: Any = None) -> Any:
        """Get value from cache with automatic deserialization"""
        try:
            if not self._async_client:
                logger.warning("Redis client not initialized, returning default")
                return default
            
            value = await self._async_client.get(key)
            if value is None:
                return default
            
            # Try to deserialize JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                # Return as string if not JSON
                return value
                
        except RedisError as e:
            logger.error(f"Redis GET error for key {key}: {e}")
            return default
        except Exception as e:
            logger.error(f"Unexpected error getting key {key}: {e}")
            return default
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        nx: bool = False,
        xx: bool = False,
        keepttl: bool = False,
    ) -> bool:
        """
        Set value in cache with automatic serialization
        
        Args:
            key: Cache key
            value: Value to cache (will be JSON serialized if not string)
            ttl: Time to live in seconds
            nx: Only set if key doesn't exist
            xx: Only set if key exists
        """
        try:
            if not self._async_client:
                logger.warning("Redis client not initialized")
                return False
            
            # Serialize value
            if isinstance(value, (dict, list, tuple)):
                value = json.dumps(value)
            elif not isinstance(value, str):
                value = str(value)
            
            # Set with options
            result = await self._async_client.set(
                key,
                value,
                ex=ttl,
                nx=nx,
                xx=xx,
                keepttl=keepttl,
            )
            
            return bool(result)
            
        except RedisError as e:
            logger.error(f"Redis SET error for key {key}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error setting key {key}: {e}")
            return False
    
    async def delete(self, *keys: str) -> int:
        """Delete one or more keys from cache"""
        try:
            if not self._async_client or not keys:
                return 0
            
            return await self._async_client.delete(*keys)
            
        except RedisError as e:
            logger.error(f"Redis DELETE error for keys {keys}: {e}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error deleting keys {keys}: {e}")
            return 0
    
    async def exists(self, *keys: str) -> int:
        """Check if keys exist"""
        try:
            if not self._async_client:
                return 0
            
            return await self._async_client.exists(*keys)
            
        except RedisError as e:
            logger.error(f"Redis EXISTS error for keys {keys}: {e}")
            return 0
    
    async def expire(self, key: str, ttl: int) -> bool:
        """Set TTL for existing key"""
        try:
            if not self._async_client:
                return False
            
            return await self._async_client.expire(key, ttl)
            
        except RedisError as e:
            logger.error(f"Redis EXPIRE error for key {key}: {e}")
            return False
    
    # Batch Operations
    async def mget(self, keys: List[str]) -> List[Any]:
        """Get multiple values at once"""
        try:
            if not self._async_client or not keys:
                return []
            
            values = await self._async_client.mget(keys)
            
            # Deserialize values
            result = []
            for value in values:
                if value is None:
                    result.append(None)
                else:
                    try:
                        result.append(json.loads(value))
                    except (json.JSONDecodeError, TypeError):
                        result.append(value)
            
            return result
            
        except RedisError as e:
            logger.error(f"Redis MGET error: {e}")
            return []
    
    async def mset(self, mapping: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Set multiple values at once"""
        try:
            if not self._async_client or not mapping:
                return False
            
            # Serialize values
            serialized = {}
            for key, value in mapping.items():
                if isinstance(value, (dict, list, tuple)):
                    serialized[key] = json.dumps(value)
                elif not isinstance(value, str):
                    serialized[key] = str(value)
                else:
                    serialized[key] = value
            
            # Use pipeline for atomic operation with TTL
            if ttl:
                pipe = self._async_client.pipeline()
                pipe.mset(serialized)
                for key in serialized.keys():
                    pipe.expire(key, ttl)
                await pipe.execute()
            else:
                await self._async_client.mset(serialized)
            
            return True
            
        except RedisError as e:
            logger.error(f"Redis MSET error: {e}")
            return False
    
    # Pattern-based Operations
    async def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern"""
        try:
            if not self._async_client:
                return 0
            
            # Use SCAN for production-safe iteration
            deleted = 0
            async for key in self._async_client.scan_iter(match=pattern, count=100):
                await self._async_client.delete(key)
                deleted += 1
            
            return deleted
            
        except RedisError as e:
            logger.error(f"Redis DELETE pattern error for {pattern}: {e}")
            return 0
    
    async def get_keys(self, pattern: str = "*", count: int = 100) -> List[str]:
        """Get keys matching pattern (use carefully in production)"""
        try:
            if not self._async_client:
                return []
            
            keys = []
            async for key in self._async_client.scan_iter(match=pattern, count=count):
                keys.append(key)
                if len(keys) >= count:
                    break
            
            return keys
            
        except RedisError as e:
            logger.error(f"Redis KEYS error for pattern {pattern}: {e}")
            return []
    
    # Hash Operations (for structured data)
    async def hset(self, name: str, key: str, value: Any) -> int:
        """Set field in hash"""
        try:
            if not self._async_client:
                return 0
            
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            
            return await self._async_client.hset(name, key, value)
            
        except RedisError as e:
            logger.error(f"Redis HSET error: {e}")
            return 0
    
    async def hget(self, name: str, key: str) -> Any:
        """Get field from hash"""
        try:
            if not self._async_client:
                return None
            
            value = await self._async_client.hget(name, key)
            if value:
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    return value
            return None
            
        except RedisError as e:
            logger.error(f"Redis HGET error: {e}")
            return None
    
    async def hgetall(self, name: str) -> Dict[str, Any]:
        """Get all fields from hash"""
        try:
            if not self._async_client:
                return {}
            
            data = await self._async_client.hgetall(name)
            
            # Deserialize values
            result = {}
            for key, value in data.items():
                try:
                    result[key] = json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    result[key] = value
            
            return result
            
        except RedisError as e:
            logger.error(f"Redis HGETALL error: {e}")
            return {}
    
    # List Operations (for queues/timelines)
    async def lpush(self, key: str, *values: Any) -> int:
        """Push values to list head"""
        try:
            if not self._async_client:
                return 0
            
            serialized = []
            for value in values:
                if isinstance(value, (dict, list)):
                    serialized.append(json.dumps(value))
                else:
                    serialized.append(str(value))
            
            return await self._async_client.lpush(key, *serialized)
            
        except RedisError as e:
            logger.error(f"Redis LPUSH error: {e}")
            return 0
    
    async def lrange(self, key: str, start: int = 0, stop: int = -1) -> List[Any]:
        """Get list range"""
        try:
            if not self._async_client:
                return []
            
            values = await self._async_client.lrange(key, start, stop)
            
            # Deserialize values
            result = []
            for value in values:
                try:
                    result.append(json.loads(value))
                except (json.JSONDecodeError, TypeError):
                    result.append(value)
            
            return result
            
        except RedisError as e:
            logger.error(f"Redis LRANGE error: {e}")
            return []
    
    async def ltrim(self, key: str, start: int, stop: int) -> bool:
        """Trim list to specified range"""
        try:
            if not self._async_client:
                return False
            
            return await self._async_client.ltrim(key, start, stop)
            
        except RedisError as e:
            logger.error(f"Redis LTRIM error: {e}")
            return False
    
    # Increment/Decrement Operations
    async def incr(self, key: str, amount: int = 1) -> int:
        """Increment counter"""
        try:
            if not self._async_client:
                return 0
            
            if amount == 1:
                return await self._async_client.incr(key)
            else:
                return await self._async_client.incrby(key, amount)
                
        except RedisError as e:
            logger.error(f"Redis INCR error: {e}")
            return 0
    
    async def decr(self, key: str, amount: int = 1) -> int:
        """Decrement counter"""
        try:
            if not self._async_client:
                return 0
            
            if amount == 1:
                return await self._async_client.decr(key)
            else:
                return await self._async_client.decrby(key, amount)
                
        except RedisError as e:
            logger.error(f"Redis DECR error: {e}")
            return 0
    
    # Set Operations
    async def sadd(self, key: str, *values: Any) -> int:
        """Add members to set"""
        try:
            if not self._async_client or not values:
                return 0
            
            return await self._async_client.sadd(key, *values)
            
        except RedisError as e:
            logger.error(f"Redis SADD error: {e}")
            return 0
    
    async def srem(self, key: str, *values: Any) -> int:
        """Remove members from set"""
        try:
            if not self._async_client or not values:
                return 0
            
            return await self._async_client.srem(key, *values)
            
        except RedisError as e:
            logger.error(f"Redis SREM error: {e}")
            return 0
    
    async def smembers(self, key: str) -> set:
        """Get all set members"""
        try:
            if not self._async_client:
                return set()
            
            return await self._async_client.smembers(key)
            
        except RedisError as e:
            logger.error(f"Redis SMEMBERS error: {e}")
            return set()
    
    async def sismember(self, key: str, value: Any) -> bool:
        """Check if value is in set"""
        try:
            if not self._async_client:
                return False
            
            return await self._async_client.sismember(key, value)
            
        except RedisError as e:
            logger.error(f"Redis SISMEMBER error: {e}")
            return False


# Create singleton instance
redis_service = RedisService()


# Caching Decorators
def cache_key_builder(prefix: str, *args, **kwargs) -> str:
    """Build cache key from function arguments"""
    key_parts = [prefix]
    
    # Add positional arguments
    for arg in args:
        if hasattr(arg, 'id'):
            key_parts.append(str(arg.id))
        elif isinstance(arg, (str, int, float, bool)):
            key_parts.append(str(arg))
        else:
            # Hash complex objects
            key_parts.append(hashlib.md5(str(arg).encode()).hexdigest()[:8])
    
    # Add keyword arguments
    for k, v in sorted(kwargs.items()):
        if hasattr(v, 'id'):
            key_parts.append(f"{k}:{v.id}")
        elif isinstance(v, (str, int, float, bool)):
            key_parts.append(f"{k}:{v}")
        else:
            key_parts.append(f"{k}:{hashlib.md5(str(v).encode()).hexdigest()[:8]}")
    
    return ":".join(key_parts)


def cached(
    prefix: str = "cache",
    ttl: int = 3600,
    key_builder: Optional[Callable] = None,
    skip_cache: bool = False
):
    """
    Async cache decorator for FastAPI endpoints
    
    Args:
        prefix: Cache key prefix
        ttl: Time to live in seconds
        key_builder: Custom key builder function
        skip_cache: Skip caching (useful for debugging)
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if skip_cache:
                return await func(*args, **kwargs)
            
            # Build cache key
            if key_builder:
                cache_key = key_builder(*args, **kwargs)
            else:
                # Filter out non-serializable arguments
                filtered_args = []
                filtered_kwargs = {}
                
                for arg in args:
                    if not hasattr(arg, '__dict__') or hasattr(arg, 'id'):
                        filtered_args.append(arg)
                
                for k, v in kwargs.items():
                    if not hasattr(v, '__dict__') or hasattr(v, 'id'):
                        filtered_kwargs[k] = v
                
                cache_key = cache_key_builder(prefix, *filtered_args, **filtered_kwargs)
            
            # Try to get from cache
            cached_value = await redis_service.get(cache_key)
            if cached_value is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_value
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Cache result (JSON-serializable)
            if result is not None:
                try:
                    serializable = jsonable_encoder(result)
                except Exception:
                    # Fallback to string conversion if encoding fails
                    serializable = result
                await redis_service.set(cache_key, serializable, ttl=ttl)
                logger.debug(f"Cached result for key: {cache_key}")
            
            return result
        
        return wrapper
    return decorator


def invalidate_cache(pattern: str):
    """
    Decorator to invalidate cache entries matching pattern after function execution
    
    Args:
        pattern: Redis key pattern to invalidate (e.g., "user:*")
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Execute function
            result = await func(*args, **kwargs)
            
            # Invalidate cache
            deleted = await redis_service.delete_pattern(pattern)
            if deleted > 0:
                logger.debug(f"Invalidated {deleted} cache entries matching pattern: {pattern}")
            
            return result
        
        return wrapper
    return decorator


# Cache invalidation helpers
async def invalidate_user_cache(user_id: int):
    """Invalidate all cache entries for a specific user"""
    patterns = [
        f"{config.CACHE_PREFIX_USER}:{user_id}:*",
        # Chat caches keyed by user (e.g., conversations, recent, queries)
        f"{config.CACHE_PREFIX_CHAT}:*:{user_id}:*",
        f"{config.CACHE_PREFIX_CHAT}:*:{user_id}",
        # Group list caches scoped by user
        f"{config.CACHE_PREFIX_GROUP}:user_groups:*:current_user:{user_id}*",
        f"{config.CACHE_PREFIX_GROUP}:*:members:{user_id}",
    ]
    
    total_deleted = 0
    for pattern in patterns:
        deleted = await redis_service.delete_pattern(pattern)
        total_deleted += deleted
    
    logger.info(f"Invalidated {total_deleted} cache entries for user {user_id}")
    return total_deleted


async def invalidate_conversation_cache(conversation_id: int):
    """Invalidate cache for a specific conversation"""
    pattern = f"{config.CACHE_PREFIX_CHAT}:conversation:{conversation_id}:*"
    deleted = await redis_service.delete_pattern(pattern)
    logger.info(f"Invalidated {deleted} cache entries for conversation {conversation_id}")
    return deleted


async def invalidate_group_cache(group_id: int):
    base = config.CACHE_PREFIX_GROUP
    # Define multiple cache key patterns to comprehensively clear group-related caches
    patterns = [
        f"{base}:{group_id}:*",  # Group-specific metadata and details
        f"{base}:messages:{group_id}:*",  # Group messages 
        f"{base}:messages:*:{group_id}:*",  # Messages related to this group across different contexts
        f"{base}:messages:*{group_id}*",  # Catch-all pattern for any message-related group caches
    ]
    total_deleted = 0
    for pattern in patterns:
        total_deleted += await redis_service.delete_pattern(pattern)
    logger.info(f"Invalidated {total_deleted} cache entries for group {group_id}")
    return total_deleted


async def invalidate_knowledge_cache(
    space_id: Optional[int] = None,
    document_ids: Optional[List[int]] = None,
    user_id: Optional[int] = None,
):
    """Invalidate knowledge base cache.
    When document_ids are provided, clear per-document caches (for all users by default).
    When space_id is provided, clear space-scoped caches for that space.
    If neither is provided, clear all knowledge caches (use sparingly).
    """
    patterns: List[str] = []
    # Space-level caches (document lists per space, potentially user-scoped)
    if space_id is not None:
        patterns.append(f"{config.CACHE_PREFIX_KNOWLEDGE}:space:{space_id}:*")
    # Document-level caches (per-document details cached per user)
    if document_ids:
        for doc_id in document_ids:
            if user_id is not None:
                patterns.append(f"{config.CACHE_PREFIX_KNOWLEDGE}:document:{doc_id}:user:{user_id}")
                patterns.append(f"{config.CACHE_PREFIX_KNOWLEDGE}:document:{doc_id}:user:{user_id}:*")
            else:
                patterns.append(f"{config.CACHE_PREFIX_KNOWLEDGE}:document:{doc_id}:user:*")
    # Fallback: if nothing specific specified, clear all knowledge caches
    if not patterns:
        patterns.append(f"{config.CACHE_PREFIX_KNOWLEDGE}:*")

    total_deleted = 0
    for pattern in patterns:
        total_deleted += await redis_service.delete_pattern(pattern)
    logger.info(f"Invalidated {total_deleted} knowledge cache entries")
    return total_deleted


async def invalidate_admin_cache():
    """Invalidate admin dashboard cache"""
    pattern = f"{config.CACHE_PREFIX_ADMIN}:*"
    deleted = await redis_service.delete_pattern(pattern)
    logger.info(f"Invalidated {deleted} admin cache entries")
    return deleted


async def invalidate_account_requests_cache():
    """Invalidate only account requests cache for immediate updates"""
    patterns = [
        f"{config.CACHE_PREFIX_ADMIN}:account_requests:*",
        f"{config.CACHE_PREFIX_ADMIN}:account_counts:*"
    ]
    
    total_deleted = 0
    for pattern in patterns:
        deleted = await redis_service.delete_pattern(pattern)
        total_deleted += deleted
    
    logger.info(f"Invalidated {total_deleted} account request cache entries")
    return total_deleted


async def invalidate_notes_cache(user_id: Optional[int] = None, note_id: Optional[int] = None):
    """Invalidate cache entries related to notes.
    If user_id is provided, clears that user's notes list cache.
    If note_id is provided, also clears that user's note detail cache for the note.
    If user_id is not provided, clears all notes cache (use sparingly).
    """
    base_prefix = config.CACHE_PREFIX_NOTES
    patterns: List[str] = []

    if user_id is None:
        patterns.append(f"{base_prefix}:*")
    else:
        # List caches for this user (any search/page/size)
        patterns.append(f"{base_prefix}:list:user:{user_id}:*")
        if note_id is not None:
            # Specific note detail cache for this user
            patterns.append(f"{base_prefix}:note:user:{user_id}:id:{note_id}")
            patterns.append(f"{base_prefix}:note:user:{user_id}:id:{note_id}:*")

    total_deleted = 0
    for pattern in patterns:
        total_deleted += await redis_service.delete_pattern(pattern)

    logger.info(f"Invalidated {total_deleted} notes cache entries")
    return total_deleted