from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>teger, Foreign<PERSON>ey, DateTime, Index
from datetime import datetime

from ..database import Base


class DeletedMessage(Base):
    """Record of a direct message that an individual user has deleted only for themselves."""

    __tablename__ = "deleted_messages"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    message_id = Column(Integer, ForeignKey("messages.id"), nullable=False)
    deleted_at = Column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        # Ensure one record per user/message pair
        Index("idx_deleted_user_msg", "user_id", "message_id", unique=True),
    )


class DeletedGroupMessage(Base):
    """Record of a group message that a user removed only from their own timeline."""

    __tablename__ = "deleted_group_messages"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    group_message_id = Column(Integer, <PERSON><PERSON><PERSON>("chat_group_messages.id"), nullable=False)
    deleted_at = Column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index("idx_deleted_user_groupmsg", "user_id", "group_message_id", unique=True),
    ) 