'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode, useEffect } from 'react';
import { Ticket, TicketStatus, TicketCreationData, TicketSummary } from '@/types/ticket';
import { useTickets, useTicketActions, useTicketSummary } from '@/hooks';

interface TicketContextValue {
  // Ticket State
  tickets: Ticket[];
  loading: boolean;
  error: string;
  total: number;
  currentPage: number;
  perPage: number;
  hasAnyTickets: boolean | null;
  
  // Selected Ticket
  selectedTicket: Ticket | null;
  selectTicket: (ticket: Ticket | null) => void;
  
  // Filters
  activeFilter: 'all' | TicketStatus;
  setActiveFilter: (filter: 'all' | TicketStatus) => void;
  
  // Modal States
  isCreateModalOpen: boolean;
  isViewModalOpen: boolean;
  openCreateModal: () => void;
  closeCreateModal: () => void;
  openViewModal: (ticket: Ticket) => void;
  closeViewModal: () => void;
  
  // CRUD Operations
  createTicket: (data: TicketCreationData) => Promise<void>;
  updateTicketStatus: (ticketId: number, status: string) => Promise<void>;
  updateTicketNotes: (ticketId: number, notes: string) => Promise<void>;
  refreshTickets: () => Promise<void>;
  
  // Admin Features
  summary: TicketSummary | null;
  refreshSummary: () => Promise<void>;
  
  // Pagination
  setPage: (page: number) => void;
  
  // Action States
  isSubmitting: boolean;
  actionError: string | null;
  clearActionError: () => void;
}

const TicketContext = createContext<TicketContextValue | null>(null);

interface TicketProviderProps {
  children: ReactNode;
  isAdmin?: boolean;
  initialFilter?: 'all' | TicketStatus;
}

export function TicketProvider({ 
  children, 
  isAdmin = false,
  initialFilter = 'all'
}: TicketProviderProps) {
  // Filter and Page State
  const [activeFilter, setActiveFilter] = useState<'all' | TicketStatus>(initialFilter);
  const [currentPage, setCurrentPage] = useState(1);
  
  // Modal State
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  
  // Refresh trigger for manual refreshes
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  
  // Hooks
  const ticketsHook = useTickets({
    statusFilter: activeFilter,
    page: currentPage,
    perPage: 20,
    isAdmin
  });

  const actionsHook = useTicketActions();
  const summaryHook = isAdmin ? useTicketSummary() : null;
  
  // Refresh on trigger change
  useEffect(() => {
    if (refreshTrigger > 0) {
      ticketsHook.fetchTickets();
      if (isAdmin && summaryHook) {
        summaryHook.fetchSummary();
      }
    }
  }, [refreshTrigger, isAdmin, summaryHook]);
  
  // Modal Handlers
  const openCreateModal = useCallback(() => {
    setIsCreateModalOpen(true);
  }, []);
  
  const closeCreateModal = useCallback(() => {
    setIsCreateModalOpen(false);
  }, []);
  
  const openViewModal = useCallback((ticket: Ticket) => {
    setSelectedTicket(ticket);
    setIsViewModalOpen(true);
  }, []);
  
  const closeViewModal = useCallback(() => {
    setIsViewModalOpen(false);
    // Delay clearing selected ticket for smooth animation
    setTimeout(() => setSelectedTicket(null), 300);
  }, []);
  
  // Ticket Selection
  const selectTicket = useCallback((ticket: Ticket | null) => {
    setSelectedTicket(ticket);
  }, []);
  
  // Filter Change Handler
  const handleFilterChange = useCallback((filter: 'all' | TicketStatus) => {
    setActiveFilter(filter);
    setCurrentPage(1);
    ticketsHook.setCurrentPage(1);
  }, [ticketsHook]);
  
  // Page Change Handler
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
    ticketsHook.setCurrentPage(page);
  }, [ticketsHook]);
  
  // Enhanced CRUD Operations with refresh
  const createTicket = useCallback(async (data: TicketCreationData) => {
    await actionsHook.createTicket(data);
    setRefreshTrigger(prev => prev + 1);
    closeCreateModal();
  }, [actionsHook, closeCreateModal]);
  
  const updateTicketStatus = useCallback(async (ticketId: number, status: string) => {
    await actionsHook.updateTicketStatus(ticketId, status);
    setRefreshTrigger(prev => prev + 1);
    closeViewModal();
  }, [actionsHook, closeViewModal]);
  
  const updateTicketNotes = useCallback(async (ticketId: number, notes: string) => {
    await actionsHook.updateTicketNotes(ticketId, notes);
    setRefreshTrigger(prev => prev + 1);
  }, [actionsHook]);
  
  const refreshTickets = useCallback(async () => {
    setRefreshTrigger(prev => prev + 1);
  }, []);
  
  const refreshSummary = useCallback(async () => {
    if (summaryHook) {
      await summaryHook.fetchSummary();
    }
  }, [summaryHook]);
  
  const value: TicketContextValue = {
    // Ticket State
    tickets: ticketsHook.tickets,
    loading: ticketsHook.loading,
    error: ticketsHook.error,
    total: ticketsHook.total,
    currentPage,
    perPage: 20,
    hasAnyTickets: ticketsHook.hasAnyTickets,
    
    // Selected Ticket
    selectedTicket,
    selectTicket,
    
    // Filters
    activeFilter,
    setActiveFilter: handleFilterChange,
    
    // Modal States
    isCreateModalOpen,
    isViewModalOpen,
    openCreateModal,
    closeCreateModal,
    openViewModal,
    closeViewModal,
    
    // CRUD Operations
    createTicket,
    updateTicketStatus,
    updateTicketNotes,
    refreshTickets,
    
    // Admin Features
    summary: summaryHook?.summary || null,
    refreshSummary,
    
    // Pagination
    setPage: handlePageChange,
    
    // Action States
    isSubmitting: actionsHook.isSubmitting,
    actionError: actionsHook.error,
    clearActionError: actionsHook.clearError,
  };
  
  return (
    <TicketContext.Provider value={value}>
      {children}
    </TicketContext.Provider>
  );
}

/**
 * Custom hook to use the Ticket Context
 * Throws an error if used outside of TicketProvider
 */
export function useTicketContext() {
  const context = useContext(TicketContext);
  
  if (!context) {
    throw new Error('useTicketContext must be used within a TicketProvider');
  }
  
  return context;
}

/**
 * Optional: Separate hook for admin-only features
 * Provides better type safety and clearer intent
 */
export function useTicketAdmin() {
  const context = useTicketContext();
  
  return {
    summary: context.summary,
    refreshSummary: context.refreshSummary,
    updateTicketStatus: context.updateTicketStatus,
    updateTicketNotes: context.updateTicketNotes,
  };
}
