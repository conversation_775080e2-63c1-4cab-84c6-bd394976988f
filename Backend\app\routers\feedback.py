from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_, and_
from sqlalchemy.orm import joinedload
from typing import List, Optional

from ..database import get_db
from ..auth.dependencies import get_current_user
from ..models.user import User
from ..schemas.feedback import (
    FeedbackCreate, FeedbackResponse, ResponseShare, SharedResponseCreate,
    SharedResponseResponse, FeedbackStats, ExpertChatInteractionResponse
)
from ..services.feedback_service import FeedbackService, ResponseSharingService

router = APIRouter(
    prefix="/feedback",
    tags=["feedback"],
    responses={404: {"description": "Not found"}},
)

from ..services.mqtt_service_client import mqtt_client

@router.post("/submit", response_model=FeedbackResponse, status_code=status.HTTP_201_CREATED)
async def submit_feedback(
    feedback_data: Feedback<PERSON>reate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Submit detailed feedback for an AI response with rating and reasons"""
    try:
        # Additional validation for rating alignment (redundant with Pydantic but defensive)
        if feedback_data.is_helpful and not (6 <= feedback_data.rating <= 10):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="For helpful feedback, rating must be between 6-10"
            )
        elif not feedback_data.is_helpful and not (1 <= feedback_data.rating <= 5):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="For not helpful feedback, rating must be between 1-5"
            )
        
        feedback = await FeedbackService.create_feedback(
            db=db,
            feedback_data=feedback_data,
            user_id=current_user.id
        )
        return feedback
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error submitting feedback: {str(e)}"
        )

@router.get("/my-feedback", response_model=List[FeedbackResponse])
async def get_my_feedback(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get all feedback submitted by the current user"""
    feedback_list = await FeedbackService.get_user_feedback(db=db, user_id=current_user.id)
    return feedback_list

@router.get("/stats", response_model=FeedbackStats)
async def get_feedback_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get comprehensive feedback statistics (admin only for now)"""
    # Add role checking here if needed
    return await FeedbackService.get_feedback_stats(db=db)

@router.post("/share-response", response_model=SharedResponseResponse, status_code=status.HTTP_201_CREATED)
async def share_ai_response(
    share_data: ResponseShare,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Share an AI response with a user or group via live chat"""
    try:
        # Import models needed for conversation handling
        from ..models.chat import Conversation, Message
        from ..models.group_chat import Group, GroupMessage
        from datetime import datetime
        import json
        
        # Create the sharing record
        shared_response = await ResponseSharingService.share_response(
            db=db,
            share_data=share_data,
            shared_by_user_id=current_user.id
        )
        
        # Get the interaction for formatting
        from ..models.feedback import ExpertChatInteraction
        if not shared_response.interaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Expert chat interaction not found"
            )
        
        # Format the message for chat
        chat_message = ResponseSharingService.format_shared_response_for_chat(
            interaction=shared_response.interaction,
            share_context=share_data.share_context,
            shared_by_user_id=current_user.id,
            shared_by_username=current_user.username
        )
        
        # Save the shared response as a persistent message
        if share_data.share_to_type == "user":
            # Find or create conversation between users
            result = await db.execute(select(User).where(
                User.id == share_data.share_to_id,
                User.is_active == True
            ))
            target_user = result.scalars().first()
            
            if not target_user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Target user not found"
                )
            
            # Look for existing conversation (either direction)
            result = await db.execute(select(Conversation).where(
                or_(
                    and_(
                        Conversation.participant1_id == current_user.id,
                        Conversation.participant2_id == share_data.share_to_id,
                    ),
                    and_(
                        Conversation.participant1_id == share_data.share_to_id,
                        Conversation.participant2_id == current_user.id,
                    ),
                ),
                Conversation.is_active == True
            ))
            conversation = result.scalars().first()
            
            # Create conversation if it doesn't exist
            if not conversation:
                conversation = Conversation(
                    participant1_id=min(current_user.id, share_data.share_to_id),
                    participant2_id=max(current_user.id, share_data.share_to_id),
                )
                db.add(conversation)
                await db.commit()
                await db.refresh(conversation)
            
            # Create message with shared response content
            shared_message_content = json.dumps(chat_message)
            new_message = Message(
                conversation_id=conversation.id,
                sender_id=current_user.id,
                content=shared_message_content,
                message_type="shared_ai_response"
            )
            
            db.add(new_message)
            
            # Update conversation timestamp
            conversation.updated_at = datetime.utcnow()
            await db.commit()
            await db.refresh(new_message)
            
            # Send via MQTT
            await mqtt_client.publish_event(
                user_id=share_data.share_to_id,
                event_data=chat_message
            )
            
        elif share_data.share_to_type == "group":
            # Verify group exists and user has access
            result = await db.execute(
                select(Group).options(joinedload(Group.members)).where(Group.id == share_data.share_to_id)
            )
            group = result.unique().scalars().first()
            if not group:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Group not found"
                )
            
            # Create group message with shared response content
            shared_message_content = json.dumps(chat_message)
            new_group_message = GroupMessage(
                group_id=share_data.share_to_id,
                sender_id=current_user.id,
                content=shared_message_content,
                message_type="shared_ai_response"
            )
            
            db.add(new_group_message)
            
            # Update group timestamp
            group.updated_at = datetime.utcnow()
            await db.commit()
            await db.refresh(new_group_message)
            
            # Send to group chat via MQTT
            for member in group.members:
                await mqtt_client.publish_event(
                    user_id=member.user_id,
                    event_data=chat_message
                )
        
        return shared_response.to_dict()
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error sharing response: {str(e)}"
        )

@router.get("/shared-responses", response_model=List[SharedResponseResponse])
async def get_shared_responses(
    shared_to_type: Optional[str] = Query(None, description="Filter by share type (user/group)"),
    shared_to_id: Optional[int] = Query(None, description="Filter by target ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get shared responses (by current user or shared to current user)"""
    
    # Get responses shared by current user
    shared_by_user = await ResponseSharingService.get_shared_responses(
        db=db,
        user_id=current_user.id
    )
    
    # Get responses shared to current user (if filtering)
    shared_to_user = []
    if shared_to_type == "user":
        shared_to_user = await ResponseSharingService.get_shared_responses(
            db=db,
            shared_to_id=current_user.id,
            shared_to_type="user"
        )
    
    # Combine and return
    all_shared = shared_by_user + shared_to_user
    return [response.to_dict() for response in all_shared]

@router.post("/export-chat", status_code=status.HTTP_200_OK)
async def export_chat_history(
    chat_data: List[dict],
    format: str = Query("json", regex="^(json|csv|pdf)$", description="Export format: json, csv, or pdf"),
    current_user: User = Depends(get_current_user),
):
    """Export current AI chat session in various formats"""
    try:
        from datetime import datetime, timezone
        import json
        import csv
        import io
        from fastapi.responses import Response
        
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        filename_base = f"ai_chat_export_{current_user.username}_{timestamp}"
        
        if format == "json":
            # Export as JSON
            export_data = {
                "user": {
                    "username": current_user.username,
                    "industry": current_user.industry
                },
                "export_timestamp": datetime.now(timezone.utc).isoformat(),
                "total_messages": len(chat_data),
                "chat_history": chat_data
            }
            
            content = json.dumps(export_data, indent=2, ensure_ascii=False)
            
            return Response(
                content=content,
                media_type="application/json",
                headers={"Content-Disposition": f"attachment; filename={filename_base}.json"}
            )
            
        elif format == "csv":
            # Export as CSV
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header
            writer.writerow([
                "Message_Index", "Role", "Content", "Timestamp", "Agent_Type", 
                "Web_Search", "Document_Search", "Deep_Search", "Response_ID", "Cached"
            ])
            
            # Write data
            for idx, message in enumerate(chat_data):
                writer.writerow([
                    idx + 1,
                    message.get("role", ""),
                    message.get("content", "").replace("\n", " ").replace("\r", ""),
                    message.get("timestamp", ""),
                    message.get("agent_type", ""),
                    message.get("webSearchEnabled", False),
                    message.get("documentSearchEnabled", False),
                    message.get("deepSearchEnabled", False),
                    message.get("responseId", ""),
                    message.get("cached", False)
                ])
            
            content = output.getvalue()
            output.close()
            
            return Response(
                content=content,
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename={filename_base}.csv"}
            )
            
        elif format == "pdf":
            # For PDF export, we'll create a simple text-based PDF
            try:
                from reportlab.lib.pagesizes import letter
                from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib.units import inch
                import io
                
                buffer = io.BytesIO()
                doc = SimpleDocTemplate(buffer, pagesize=letter, topMargin=1*inch)
                styles = getSampleStyleSheet()
                
                # Custom styles
                title_style = ParagraphStyle(
                    'CustomTitle',
                    parent=styles['Heading1'],
                    fontSize=16,
                    spaceAfter=20
                )
                
                header_style = ParagraphStyle(
                    'CustomHeader',
                    parent=styles['Heading2'],
                    fontSize=12,
                    spaceAfter=10
                )
                
                body_style = ParagraphStyle(
                    'CustomBody',
                    parent=styles['Normal'],
                    fontSize=10,
                    spaceAfter=10
                )
                
                elements = []
                
                # Title
                elements.append(Paragraph(f"AI Chat History Export", title_style))
                elements.append(Paragraph(f"User: {current_user.username}", header_style))
                elements.append(Paragraph(f"Export Date: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}", body_style))
                elements.append(Paragraph(f"Total Messages: {len(chat_data)}", body_style))
                elements.append(Spacer(1, 20))
                
                # Chat messages
                for idx, message in enumerate(chat_data):
                    role = message.get("role", "").title()
                    content = message.get("content", "")
                    
                    # Clean content for PDF
                    content = content.replace("<", "&lt;").replace(">", "&gt;")
                    
                    elements.append(Paragraph(f"<b>Message {idx + 1} - {role}:</b>", header_style))
                    elements.append(Paragraph(content, body_style))
                    
                    if message.get("role") == "assistant":
                        metadata = []
                        if message.get("webSearchEnabled"):
                            metadata.append("Web Search")
                        if message.get("documentSearchEnabled"):
                            metadata.append("Document Search")
                        if message.get("deepSearchEnabled"):
                            metadata.append("Deep Search")
                        if message.get("cached"):
                            metadata.append("Cached Response")
                        
                        if metadata:
                            elements.append(Paragraph(f"<i>Features: {', '.join(metadata)}</i>", body_style))
                    
                    elements.append(Spacer(1, 10))
                
                doc.build(elements)
                pdf_content = buffer.getvalue()
                buffer.close()
                
                return Response(
                    content=pdf_content,
                    media_type="application/pdf",
                    headers={"Content-Disposition": f"attachment; filename={filename_base}.pdf"}
                )
                
            except ImportError:
                raise HTTPException(
                    status_code=status.HTTP_501_NOT_IMPLEMENTED,
                    detail="PDF export requires reportlab package. Please install it: pip install reportlab"
                )
                
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error exporting chat: {str(e)}"
        )

@router.get("/export-history")
async def export_user_ai_history(
    format: str = Query("json", regex="^(json|csv)$", description="Export format: json or csv"),
    limit: int = Query(50, description="Number of recent AI interactions to export"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Export user's historical AI interactions from database"""
    try:
        from datetime import datetime, timezone
        import json
        import csv
        import io
        from fastapi.responses import Response
        from ..models.feedback import ExpertChatInteraction
        
        # Get user's recent expert chat interactions
        interactions = ExpertChatInteraction.get_user_interactions(db, current_user.id, limit)
        
        # Build export data
        export_items = []
        for interaction in interactions:
            export_items.append({
                "timestamp": interaction.created_at.isoformat() if interaction.created_at else None,
                "query": interaction.query,
                "response": interaction.response,
                "agent_type": interaction.agent_type,
                "feedback_helpful": interaction.feedback,
                "web_search_enabled": False,  # Not tracked in simplified model
                "document_search_enabled": False,  # Not tracked in simplified model
                "deep_search_enabled": False,  # Not tracked in simplified model
                "sources_count": 0,  # Not tracked in simplified model
                "interaction_id": interaction.id,
                "response_id": interaction.response_id
            })
        
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        filename_base = f"ai_history_{current_user.username}_{timestamp}"
        
        if format == "json":
            export_data = {
                "user": {
                    "username": current_user.username,
                    "industry": current_user.industry
                },
                "export_timestamp": datetime.now(timezone.utc).isoformat(),
                "total_interactions": len(export_items),
                "interactions": export_items
            }
            
            content = json.dumps(export_data, indent=2, ensure_ascii=False)
            
            return Response(
                content=content,
                media_type="application/json",
                headers={"Content-Disposition": f"attachment; filename={filename_base}.json"}
            )
            
        elif format == "csv":
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header
            writer.writerow([
                "Timestamp", "Query", "Response", "Agent_Type", "Feedback_Helpful",
                "Web_Search", "Document_Search", "Deep_Search", "Sources_Count", "Interaction_ID", "Response_ID"
            ])
            
            # Write data
            for item in export_items:
                writer.writerow([
                    item["timestamp"],
                    item["query"],
                    item["response"].replace("\n", " ").replace("\r", ""),
                    item["agent_type"],
                    item["feedback_helpful"],
                    item["web_search_enabled"],
                    item["document_search_enabled"],
                    item["deep_search_enabled"],
                    item["sources_count"],
                    item["interaction_id"],
                    item["response_id"]
                ])
            
            content = output.getvalue()
            output.close()
            
            return Response(
                content=content,
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename={filename_base}.csv"}
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error exporting history: {str(e)}"
        ) 