"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lru-cache";
exports.ids = ["vendor-chunks/lru-cache"];
exports.modules = {

/***/ "(ssr)/./node_modules/lru-cache/dist/commonjs/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/lru-cache/dist/commonjs/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/**\n * @module LRUCache\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.LRUCache = void 0;\nconst perf = typeof performance === 'object' &&\n    performance &&\n    typeof performance.now === 'function'\n    ? performance\n    : Date;\nconst warned = new Set();\n/* c8 ignore start */\nconst PROCESS = (typeof process === 'object' && !!process ? process : {});\n/* c8 ignore start */\nconst emitWarning = (msg, type, code, fn) => {\n    typeof PROCESS.emitWarning === 'function'\n        ? PROCESS.emitWarning(msg, type, code, fn)\n        : console.error(`[${code}] ${type}: ${msg}`);\n};\nlet AC = globalThis.AbortController;\nlet AS = globalThis.AbortSignal;\n/* c8 ignore start */\nif (typeof AC === 'undefined') {\n    //@ts-ignore\n    AS = class AbortSignal {\n        onabort;\n        _onabort = [];\n        reason;\n        aborted = false;\n        addEventListener(_, fn) {\n            this._onabort.push(fn);\n        }\n    };\n    //@ts-ignore\n    AC = class AbortController {\n        constructor() {\n            warnACPolyfill();\n        }\n        signal = new AS();\n        abort(reason) {\n            if (this.signal.aborted)\n                return;\n            //@ts-ignore\n            this.signal.reason = reason;\n            //@ts-ignore\n            this.signal.aborted = true;\n            //@ts-ignore\n            for (const fn of this.signal._onabort) {\n                fn(reason);\n            }\n            this.signal.onabort?.(reason);\n        }\n    };\n    let printACPolyfillWarning = PROCESS.env?.LRU_CACHE_IGNORE_AC_WARNING !== '1';\n    const warnACPolyfill = () => {\n        if (!printACPolyfillWarning)\n            return;\n        printACPolyfillWarning = false;\n        emitWarning('AbortController is not defined. If using lru-cache in ' +\n            'node 14, load an AbortController polyfill from the ' +\n            '`node-abort-controller` package. A minimal polyfill is ' +\n            'provided for use by LRUCache.fetch(), but it should not be ' +\n            'relied upon in other contexts (eg, passing it to other APIs that ' +\n            'use AbortController/AbortSignal might have undesirable effects). ' +\n            'You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.', 'NO_ABORT_CONTROLLER', 'ENOTSUP', warnACPolyfill);\n    };\n}\n/* c8 ignore stop */\nconst shouldWarn = (code) => !warned.has(code);\nconst TYPE = Symbol('type');\nconst isPosInt = (n) => n && n === Math.floor(n) && n > 0 && isFinite(n);\n/* c8 ignore start */\n// This is a little bit ridiculous, tbh.\n// The maximum array length is 2^32-1 or thereabouts on most JS impls.\n// And well before that point, you're caching the entire world, I mean,\n// that's ~32GB of just integers for the next/prev links, plus whatever\n// else to hold that many keys and values.  Just filling the memory with\n// zeroes at init time is brutal when you get that big.\n// But why not be complete?\n// Maybe in the future, these limits will have expanded.\nconst getUintArray = (max) => !isPosInt(max)\n    ? null\n    : max <= Math.pow(2, 8)\n        ? Uint8Array\n        : max <= Math.pow(2, 16)\n            ? Uint16Array\n            : max <= Math.pow(2, 32)\n                ? Uint32Array\n                : max <= Number.MAX_SAFE_INTEGER\n                    ? ZeroArray\n                    : null;\n/* c8 ignore stop */\nclass ZeroArray extends Array {\n    constructor(size) {\n        super(size);\n        this.fill(0);\n    }\n}\nclass Stack {\n    heap;\n    length;\n    // private constructor\n    static #constructing = false;\n    static create(max) {\n        const HeapCls = getUintArray(max);\n        if (!HeapCls)\n            return [];\n        Stack.#constructing = true;\n        const s = new Stack(max, HeapCls);\n        Stack.#constructing = false;\n        return s;\n    }\n    constructor(max, HeapCls) {\n        /* c8 ignore start */\n        if (!Stack.#constructing) {\n            throw new TypeError('instantiate Stack using Stack.create(n)');\n        }\n        /* c8 ignore stop */\n        this.heap = new HeapCls(max);\n        this.length = 0;\n    }\n    push(n) {\n        this.heap[this.length++] = n;\n    }\n    pop() {\n        return this.heap[--this.length];\n    }\n}\n/**\n * Default export, the thing you're using this module to get.\n *\n * The `K` and `V` types define the key and value types, respectively. The\n * optional `FC` type defines the type of the `context` object passed to\n * `cache.fetch()` and `cache.memo()`.\n *\n * Keys and values **must not** be `null` or `undefined`.\n *\n * All properties from the options object (with the exception of `max`,\n * `maxSize`, `fetchMethod`, `memoMethod`, `dispose` and `disposeAfter`) are\n * added as normal public members. (The listed options are read-only getters.)\n *\n * Changing any of these will alter the defaults for subsequent method calls.\n */\nclass LRUCache {\n    // options that cannot be changed without disaster\n    #max;\n    #maxSize;\n    #dispose;\n    #disposeAfter;\n    #fetchMethod;\n    #memoMethod;\n    /**\n     * {@link LRUCache.OptionsBase.ttl}\n     */\n    ttl;\n    /**\n     * {@link LRUCache.OptionsBase.ttlResolution}\n     */\n    ttlResolution;\n    /**\n     * {@link LRUCache.OptionsBase.ttlAutopurge}\n     */\n    ttlAutopurge;\n    /**\n     * {@link LRUCache.OptionsBase.updateAgeOnGet}\n     */\n    updateAgeOnGet;\n    /**\n     * {@link LRUCache.OptionsBase.updateAgeOnHas}\n     */\n    updateAgeOnHas;\n    /**\n     * {@link LRUCache.OptionsBase.allowStale}\n     */\n    allowStale;\n    /**\n     * {@link LRUCache.OptionsBase.noDisposeOnSet}\n     */\n    noDisposeOnSet;\n    /**\n     * {@link LRUCache.OptionsBase.noUpdateTTL}\n     */\n    noUpdateTTL;\n    /**\n     * {@link LRUCache.OptionsBase.maxEntrySize}\n     */\n    maxEntrySize;\n    /**\n     * {@link LRUCache.OptionsBase.sizeCalculation}\n     */\n    sizeCalculation;\n    /**\n     * {@link LRUCache.OptionsBase.noDeleteOnFetchRejection}\n     */\n    noDeleteOnFetchRejection;\n    /**\n     * {@link LRUCache.OptionsBase.noDeleteOnStaleGet}\n     */\n    noDeleteOnStaleGet;\n    /**\n     * {@link LRUCache.OptionsBase.allowStaleOnFetchAbort}\n     */\n    allowStaleOnFetchAbort;\n    /**\n     * {@link LRUCache.OptionsBase.allowStaleOnFetchRejection}\n     */\n    allowStaleOnFetchRejection;\n    /**\n     * {@link LRUCache.OptionsBase.ignoreFetchAbort}\n     */\n    ignoreFetchAbort;\n    // computed properties\n    #size;\n    #calculatedSize;\n    #keyMap;\n    #keyList;\n    #valList;\n    #next;\n    #prev;\n    #head;\n    #tail;\n    #free;\n    #disposed;\n    #sizes;\n    #starts;\n    #ttls;\n    #hasDispose;\n    #hasFetchMethod;\n    #hasDisposeAfter;\n    /**\n     * Do not call this method unless you need to inspect the\n     * inner workings of the cache.  If anything returned by this\n     * object is modified in any way, strange breakage may occur.\n     *\n     * These fields are private for a reason!\n     *\n     * @internal\n     */\n    static unsafeExposeInternals(c) {\n        return {\n            // properties\n            starts: c.#starts,\n            ttls: c.#ttls,\n            sizes: c.#sizes,\n            keyMap: c.#keyMap,\n            keyList: c.#keyList,\n            valList: c.#valList,\n            next: c.#next,\n            prev: c.#prev,\n            get head() {\n                return c.#head;\n            },\n            get tail() {\n                return c.#tail;\n            },\n            free: c.#free,\n            // methods\n            isBackgroundFetch: (p) => c.#isBackgroundFetch(p),\n            backgroundFetch: (k, index, options, context) => c.#backgroundFetch(k, index, options, context),\n            moveToTail: (index) => c.#moveToTail(index),\n            indexes: (options) => c.#indexes(options),\n            rindexes: (options) => c.#rindexes(options),\n            isStale: (index) => c.#isStale(index),\n        };\n    }\n    // Protected read-only members\n    /**\n     * {@link LRUCache.OptionsBase.max} (read-only)\n     */\n    get max() {\n        return this.#max;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.maxSize} (read-only)\n     */\n    get maxSize() {\n        return this.#maxSize;\n    }\n    /**\n     * The total computed size of items in the cache (read-only)\n     */\n    get calculatedSize() {\n        return this.#calculatedSize;\n    }\n    /**\n     * The number of items stored in the cache (read-only)\n     */\n    get size() {\n        return this.#size;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.fetchMethod} (read-only)\n     */\n    get fetchMethod() {\n        return this.#fetchMethod;\n    }\n    get memoMethod() {\n        return this.#memoMethod;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.dispose} (read-only)\n     */\n    get dispose() {\n        return this.#dispose;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.disposeAfter} (read-only)\n     */\n    get disposeAfter() {\n        return this.#disposeAfter;\n    }\n    constructor(options) {\n        const { max = 0, ttl, ttlResolution = 1, ttlAutopurge, updateAgeOnGet, updateAgeOnHas, allowStale, dispose, disposeAfter, noDisposeOnSet, noUpdateTTL, maxSize = 0, maxEntrySize = 0, sizeCalculation, fetchMethod, memoMethod, noDeleteOnFetchRejection, noDeleteOnStaleGet, allowStaleOnFetchRejection, allowStaleOnFetchAbort, ignoreFetchAbort, } = options;\n        if (max !== 0 && !isPosInt(max)) {\n            throw new TypeError('max option must be a nonnegative integer');\n        }\n        const UintArray = max ? getUintArray(max) : Array;\n        if (!UintArray) {\n            throw new Error('invalid max value: ' + max);\n        }\n        this.#max = max;\n        this.#maxSize = maxSize;\n        this.maxEntrySize = maxEntrySize || this.#maxSize;\n        this.sizeCalculation = sizeCalculation;\n        if (this.sizeCalculation) {\n            if (!this.#maxSize && !this.maxEntrySize) {\n                throw new TypeError('cannot set sizeCalculation without setting maxSize or maxEntrySize');\n            }\n            if (typeof this.sizeCalculation !== 'function') {\n                throw new TypeError('sizeCalculation set to non-function');\n            }\n        }\n        if (memoMethod !== undefined &&\n            typeof memoMethod !== 'function') {\n            throw new TypeError('memoMethod must be a function if defined');\n        }\n        this.#memoMethod = memoMethod;\n        if (fetchMethod !== undefined &&\n            typeof fetchMethod !== 'function') {\n            throw new TypeError('fetchMethod must be a function if specified');\n        }\n        this.#fetchMethod = fetchMethod;\n        this.#hasFetchMethod = !!fetchMethod;\n        this.#keyMap = new Map();\n        this.#keyList = new Array(max).fill(undefined);\n        this.#valList = new Array(max).fill(undefined);\n        this.#next = new UintArray(max);\n        this.#prev = new UintArray(max);\n        this.#head = 0;\n        this.#tail = 0;\n        this.#free = Stack.create(max);\n        this.#size = 0;\n        this.#calculatedSize = 0;\n        if (typeof dispose === 'function') {\n            this.#dispose = dispose;\n        }\n        if (typeof disposeAfter === 'function') {\n            this.#disposeAfter = disposeAfter;\n            this.#disposed = [];\n        }\n        else {\n            this.#disposeAfter = undefined;\n            this.#disposed = undefined;\n        }\n        this.#hasDispose = !!this.#dispose;\n        this.#hasDisposeAfter = !!this.#disposeAfter;\n        this.noDisposeOnSet = !!noDisposeOnSet;\n        this.noUpdateTTL = !!noUpdateTTL;\n        this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection;\n        this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection;\n        this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort;\n        this.ignoreFetchAbort = !!ignoreFetchAbort;\n        // NB: maxEntrySize is set to maxSize if it's set\n        if (this.maxEntrySize !== 0) {\n            if (this.#maxSize !== 0) {\n                if (!isPosInt(this.#maxSize)) {\n                    throw new TypeError('maxSize must be a positive integer if specified');\n                }\n            }\n            if (!isPosInt(this.maxEntrySize)) {\n                throw new TypeError('maxEntrySize must be a positive integer if specified');\n            }\n            this.#initializeSizeTracking();\n        }\n        this.allowStale = !!allowStale;\n        this.noDeleteOnStaleGet = !!noDeleteOnStaleGet;\n        this.updateAgeOnGet = !!updateAgeOnGet;\n        this.updateAgeOnHas = !!updateAgeOnHas;\n        this.ttlResolution =\n            isPosInt(ttlResolution) || ttlResolution === 0\n                ? ttlResolution\n                : 1;\n        this.ttlAutopurge = !!ttlAutopurge;\n        this.ttl = ttl || 0;\n        if (this.ttl) {\n            if (!isPosInt(this.ttl)) {\n                throw new TypeError('ttl must be a positive integer if specified');\n            }\n            this.#initializeTTLTracking();\n        }\n        // do not allow completely unbounded caches\n        if (this.#max === 0 && this.ttl === 0 && this.#maxSize === 0) {\n            throw new TypeError('At least one of max, maxSize, or ttl is required');\n        }\n        if (!this.ttlAutopurge && !this.#max && !this.#maxSize) {\n            const code = 'LRU_CACHE_UNBOUNDED';\n            if (shouldWarn(code)) {\n                warned.add(code);\n                const msg = 'TTL caching without ttlAutopurge, max, or maxSize can ' +\n                    'result in unbounded memory consumption.';\n                emitWarning(msg, 'UnboundedCacheWarning', code, LRUCache);\n            }\n        }\n    }\n    /**\n     * Return the number of ms left in the item's TTL. If item is not in cache,\n     * returns `0`. Returns `Infinity` if item is in cache without a defined TTL.\n     */\n    getRemainingTTL(key) {\n        return this.#keyMap.has(key) ? Infinity : 0;\n    }\n    #initializeTTLTracking() {\n        const ttls = new ZeroArray(this.#max);\n        const starts = new ZeroArray(this.#max);\n        this.#ttls = ttls;\n        this.#starts = starts;\n        this.#setItemTTL = (index, ttl, start = perf.now()) => {\n            starts[index] = ttl !== 0 ? start : 0;\n            ttls[index] = ttl;\n            if (ttl !== 0 && this.ttlAutopurge) {\n                const t = setTimeout(() => {\n                    if (this.#isStale(index)) {\n                        this.#delete(this.#keyList[index], 'expire');\n                    }\n                }, ttl + 1);\n                // unref() not supported on all platforms\n                /* c8 ignore start */\n                if (t.unref) {\n                    t.unref();\n                }\n                /* c8 ignore stop */\n            }\n        };\n        this.#updateItemAge = index => {\n            starts[index] = ttls[index] !== 0 ? perf.now() : 0;\n        };\n        this.#statusTTL = (status, index) => {\n            if (ttls[index]) {\n                const ttl = ttls[index];\n                const start = starts[index];\n                /* c8 ignore next */\n                if (!ttl || !start)\n                    return;\n                status.ttl = ttl;\n                status.start = start;\n                status.now = cachedNow || getNow();\n                const age = status.now - start;\n                status.remainingTTL = ttl - age;\n            }\n        };\n        // debounce calls to perf.now() to 1s so we're not hitting\n        // that costly call repeatedly.\n        let cachedNow = 0;\n        const getNow = () => {\n            const n = perf.now();\n            if (this.ttlResolution > 0) {\n                cachedNow = n;\n                const t = setTimeout(() => (cachedNow = 0), this.ttlResolution);\n                // not available on all platforms\n                /* c8 ignore start */\n                if (t.unref) {\n                    t.unref();\n                }\n                /* c8 ignore stop */\n            }\n            return n;\n        };\n        this.getRemainingTTL = key => {\n            const index = this.#keyMap.get(key);\n            if (index === undefined) {\n                return 0;\n            }\n            const ttl = ttls[index];\n            const start = starts[index];\n            if (!ttl || !start) {\n                return Infinity;\n            }\n            const age = (cachedNow || getNow()) - start;\n            return ttl - age;\n        };\n        this.#isStale = index => {\n            const s = starts[index];\n            const t = ttls[index];\n            return !!t && !!s && (cachedNow || getNow()) - s > t;\n        };\n    }\n    // conditionally set private methods related to TTL\n    #updateItemAge = () => { };\n    #statusTTL = () => { };\n    #setItemTTL = () => { };\n    /* c8 ignore stop */\n    #isStale = () => false;\n    #initializeSizeTracking() {\n        const sizes = new ZeroArray(this.#max);\n        this.#calculatedSize = 0;\n        this.#sizes = sizes;\n        this.#removeItemSize = index => {\n            this.#calculatedSize -= sizes[index];\n            sizes[index] = 0;\n        };\n        this.#requireSize = (k, v, size, sizeCalculation) => {\n            // provisionally accept background fetches.\n            // actual value size will be checked when they return.\n            if (this.#isBackgroundFetch(v)) {\n                return 0;\n            }\n            if (!isPosInt(size)) {\n                if (sizeCalculation) {\n                    if (typeof sizeCalculation !== 'function') {\n                        throw new TypeError('sizeCalculation must be a function');\n                    }\n                    size = sizeCalculation(v, k);\n                    if (!isPosInt(size)) {\n                        throw new TypeError('sizeCalculation return invalid (expect positive integer)');\n                    }\n                }\n                else {\n                    throw new TypeError('invalid size value (must be positive integer). ' +\n                        'When maxSize or maxEntrySize is used, sizeCalculation ' +\n                        'or size must be set.');\n                }\n            }\n            return size;\n        };\n        this.#addItemSize = (index, size, status) => {\n            sizes[index] = size;\n            if (this.#maxSize) {\n                const maxSize = this.#maxSize - sizes[index];\n                while (this.#calculatedSize > maxSize) {\n                    this.#evict(true);\n                }\n            }\n            this.#calculatedSize += sizes[index];\n            if (status) {\n                status.entrySize = size;\n                status.totalCalculatedSize = this.#calculatedSize;\n            }\n        };\n    }\n    #removeItemSize = _i => { };\n    #addItemSize = (_i, _s, _st) => { };\n    #requireSize = (_k, _v, size, sizeCalculation) => {\n        if (size || sizeCalculation) {\n            throw new TypeError('cannot set size without setting maxSize or maxEntrySize on cache');\n        }\n        return 0;\n    };\n    *#indexes({ allowStale = this.allowStale } = {}) {\n        if (this.#size) {\n            for (let i = this.#tail; true;) {\n                if (!this.#isValidIndex(i)) {\n                    break;\n                }\n                if (allowStale || !this.#isStale(i)) {\n                    yield i;\n                }\n                if (i === this.#head) {\n                    break;\n                }\n                else {\n                    i = this.#prev[i];\n                }\n            }\n        }\n    }\n    *#rindexes({ allowStale = this.allowStale } = {}) {\n        if (this.#size) {\n            for (let i = this.#head; true;) {\n                if (!this.#isValidIndex(i)) {\n                    break;\n                }\n                if (allowStale || !this.#isStale(i)) {\n                    yield i;\n                }\n                if (i === this.#tail) {\n                    break;\n                }\n                else {\n                    i = this.#next[i];\n                }\n            }\n        }\n    }\n    #isValidIndex(index) {\n        return (index !== undefined &&\n            this.#keyMap.get(this.#keyList[index]) === index);\n    }\n    /**\n     * Return a generator yielding `[key, value]` pairs,\n     * in order from most recently used to least recently used.\n     */\n    *entries() {\n        for (const i of this.#indexes()) {\n            if (this.#valList[i] !== undefined &&\n                this.#keyList[i] !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield [this.#keyList[i], this.#valList[i]];\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.entries}\n     *\n     * Return a generator yielding `[key, value]` pairs,\n     * in order from least recently used to most recently used.\n     */\n    *rentries() {\n        for (const i of this.#rindexes()) {\n            if (this.#valList[i] !== undefined &&\n                this.#keyList[i] !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield [this.#keyList[i], this.#valList[i]];\n            }\n        }\n    }\n    /**\n     * Return a generator yielding the keys in the cache,\n     * in order from most recently used to least recently used.\n     */\n    *keys() {\n        for (const i of this.#indexes()) {\n            const k = this.#keyList[i];\n            if (k !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield k;\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.keys}\n     *\n     * Return a generator yielding the keys in the cache,\n     * in order from least recently used to most recently used.\n     */\n    *rkeys() {\n        for (const i of this.#rindexes()) {\n            const k = this.#keyList[i];\n            if (k !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield k;\n            }\n        }\n    }\n    /**\n     * Return a generator yielding the values in the cache,\n     * in order from most recently used to least recently used.\n     */\n    *values() {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            if (v !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield this.#valList[i];\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.values}\n     *\n     * Return a generator yielding the values in the cache,\n     * in order from least recently used to most recently used.\n     */\n    *rvalues() {\n        for (const i of this.#rindexes()) {\n            const v = this.#valList[i];\n            if (v !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield this.#valList[i];\n            }\n        }\n    }\n    /**\n     * Iterating over the cache itself yields the same results as\n     * {@link LRUCache.entries}\n     */\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n    /**\n     * A String value that is used in the creation of the default string\n     * description of an object. Called by the built-in method\n     * `Object.prototype.toString`.\n     */\n    [Symbol.toStringTag] = 'LRUCache';\n    /**\n     * Find a value for which the supplied fn method returns a truthy value,\n     * similar to `Array.find()`. fn is called as `fn(value, key, cache)`.\n     */\n    find(fn, getOptions = {}) {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            if (fn(value, this.#keyList[i], this)) {\n                return this.get(this.#keyList[i], getOptions);\n            }\n        }\n    }\n    /**\n     * Call the supplied function on each item in the cache, in order from most\n     * recently used to least recently used.\n     *\n     * `fn` is called as `fn(value, key, cache)`.\n     *\n     * If `thisp` is provided, function will be called in the `this`-context of\n     * the provided object, or the cache if no `thisp` object is provided.\n     *\n     * Does not update age or recenty of use, or iterate over stale values.\n     */\n    forEach(fn, thisp = this) {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            fn.call(thisp, value, this.#keyList[i], this);\n        }\n    }\n    /**\n     * The same as {@link LRUCache.forEach} but items are iterated over in\n     * reverse order.  (ie, less recently used items are iterated over first.)\n     */\n    rforEach(fn, thisp = this) {\n        for (const i of this.#rindexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            fn.call(thisp, value, this.#keyList[i], this);\n        }\n    }\n    /**\n     * Delete any stale entries. Returns true if anything was removed,\n     * false otherwise.\n     */\n    purgeStale() {\n        let deleted = false;\n        for (const i of this.#rindexes({ allowStale: true })) {\n            if (this.#isStale(i)) {\n                this.#delete(this.#keyList[i], 'expire');\n                deleted = true;\n            }\n        }\n        return deleted;\n    }\n    /**\n     * Get the extended info about a given entry, to get its value, size, and\n     * TTL info simultaneously. Returns `undefined` if the key is not present.\n     *\n     * Unlike {@link LRUCache#dump}, which is designed to be portable and survive\n     * serialization, the `start` value is always the current timestamp, and the\n     * `ttl` is a calculated remaining time to live (negative if expired).\n     *\n     * Always returns stale values, if their info is found in the cache, so be\n     * sure to check for expirations (ie, a negative {@link LRUCache.Entry#ttl})\n     * if relevant.\n     */\n    info(key) {\n        const i = this.#keyMap.get(key);\n        if (i === undefined)\n            return undefined;\n        const v = this.#valList[i];\n        const value = this.#isBackgroundFetch(v)\n            ? v.__staleWhileFetching\n            : v;\n        if (value === undefined)\n            return undefined;\n        const entry = { value };\n        if (this.#ttls && this.#starts) {\n            const ttl = this.#ttls[i];\n            const start = this.#starts[i];\n            if (ttl && start) {\n                const remain = ttl - (perf.now() - start);\n                entry.ttl = remain;\n                entry.start = Date.now();\n            }\n        }\n        if (this.#sizes) {\n            entry.size = this.#sizes[i];\n        }\n        return entry;\n    }\n    /**\n     * Return an array of [key, {@link LRUCache.Entry}] tuples which can be\n     * passed to {@link LRLUCache#load}.\n     *\n     * The `start` fields are calculated relative to a portable `Date.now()`\n     * timestamp, even if `performance.now()` is available.\n     *\n     * Stale entries are always included in the `dump`, even if\n     * {@link LRUCache.OptionsBase.allowStale} is false.\n     *\n     * Note: this returns an actual array, not a generator, so it can be more\n     * easily passed around.\n     */\n    dump() {\n        const arr = [];\n        for (const i of this.#indexes({ allowStale: true })) {\n            const key = this.#keyList[i];\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined || key === undefined)\n                continue;\n            const entry = { value };\n            if (this.#ttls && this.#starts) {\n                entry.ttl = this.#ttls[i];\n                // always dump the start relative to a portable timestamp\n                // it's ok for this to be a bit slow, it's a rare operation.\n                const age = perf.now() - this.#starts[i];\n                entry.start = Math.floor(Date.now() - age);\n            }\n            if (this.#sizes) {\n                entry.size = this.#sizes[i];\n            }\n            arr.unshift([key, entry]);\n        }\n        return arr;\n    }\n    /**\n     * Reset the cache and load in the items in entries in the order listed.\n     *\n     * The shape of the resulting cache may be different if the same options are\n     * not used in both caches.\n     *\n     * The `start` fields are assumed to be calculated relative to a portable\n     * `Date.now()` timestamp, even if `performance.now()` is available.\n     */\n    load(arr) {\n        this.clear();\n        for (const [key, entry] of arr) {\n            if (entry.start) {\n                // entry.start is a portable timestamp, but we may be using\n                // node's performance.now(), so calculate the offset, so that\n                // we get the intended remaining TTL, no matter how long it's\n                // been on ice.\n                //\n                // it's ok for this to be a bit slow, it's a rare operation.\n                const age = Date.now() - entry.start;\n                entry.start = perf.now() - age;\n            }\n            this.set(key, entry.value, entry);\n        }\n    }\n    /**\n     * Add a value to the cache.\n     *\n     * Note: if `undefined` is specified as a value, this is an alias for\n     * {@link LRUCache#delete}\n     *\n     * Fields on the {@link LRUCache.SetOptions} options param will override\n     * their corresponding values in the constructor options for the scope\n     * of this single `set()` operation.\n     *\n     * If `start` is provided, then that will set the effective start\n     * time for the TTL calculation. Note that this must be a previous\n     * value of `performance.now()` if supported, or a previous value of\n     * `Date.now()` if not.\n     *\n     * Options object may also include `size`, which will prevent\n     * calling the `sizeCalculation` function and just use the specified\n     * number if it is a positive integer, and `noDisposeOnSet` which\n     * will prevent calling a `dispose` function in the case of\n     * overwrites.\n     *\n     * If the `size` (or return value of `sizeCalculation`) for a given\n     * entry is greater than `maxEntrySize`, then the item will not be\n     * added to the cache.\n     *\n     * Will update the recency of the entry.\n     *\n     * If the value is `undefined`, then this is an alias for\n     * `cache.delete(key)`. `undefined` is never stored in the cache.\n     */\n    set(k, v, setOptions = {}) {\n        if (v === undefined) {\n            this.delete(k);\n            return this;\n        }\n        const { ttl = this.ttl, start, noDisposeOnSet = this.noDisposeOnSet, sizeCalculation = this.sizeCalculation, status, } = setOptions;\n        let { noUpdateTTL = this.noUpdateTTL } = setOptions;\n        const size = this.#requireSize(k, v, setOptions.size || 0, sizeCalculation);\n        // if the item doesn't fit, don't do anything\n        // NB: maxEntrySize set to maxSize by default\n        if (this.maxEntrySize && size > this.maxEntrySize) {\n            if (status) {\n                status.set = 'miss';\n                status.maxEntrySizeExceeded = true;\n            }\n            // have to delete, in case something is there already.\n            this.#delete(k, 'set');\n            return this;\n        }\n        let index = this.#size === 0 ? undefined : this.#keyMap.get(k);\n        if (index === undefined) {\n            // addition\n            index = (this.#size === 0\n                ? this.#tail\n                : this.#free.length !== 0\n                    ? this.#free.pop()\n                    : this.#size === this.#max\n                        ? this.#evict(false)\n                        : this.#size);\n            this.#keyList[index] = k;\n            this.#valList[index] = v;\n            this.#keyMap.set(k, index);\n            this.#next[this.#tail] = index;\n            this.#prev[index] = this.#tail;\n            this.#tail = index;\n            this.#size++;\n            this.#addItemSize(index, size, status);\n            if (status)\n                status.set = 'add';\n            noUpdateTTL = false;\n        }\n        else {\n            // update\n            this.#moveToTail(index);\n            const oldVal = this.#valList[index];\n            if (v !== oldVal) {\n                if (this.#hasFetchMethod && this.#isBackgroundFetch(oldVal)) {\n                    oldVal.__abortController.abort(new Error('replaced'));\n                    const { __staleWhileFetching: s } = oldVal;\n                    if (s !== undefined && !noDisposeOnSet) {\n                        if (this.#hasDispose) {\n                            this.#dispose?.(s, k, 'set');\n                        }\n                        if (this.#hasDisposeAfter) {\n                            this.#disposed?.push([s, k, 'set']);\n                        }\n                    }\n                }\n                else if (!noDisposeOnSet) {\n                    if (this.#hasDispose) {\n                        this.#dispose?.(oldVal, k, 'set');\n                    }\n                    if (this.#hasDisposeAfter) {\n                        this.#disposed?.push([oldVal, k, 'set']);\n                    }\n                }\n                this.#removeItemSize(index);\n                this.#addItemSize(index, size, status);\n                this.#valList[index] = v;\n                if (status) {\n                    status.set = 'replace';\n                    const oldValue = oldVal && this.#isBackgroundFetch(oldVal)\n                        ? oldVal.__staleWhileFetching\n                        : oldVal;\n                    if (oldValue !== undefined)\n                        status.oldValue = oldValue;\n                }\n            }\n            else if (status) {\n                status.set = 'update';\n            }\n        }\n        if (ttl !== 0 && !this.#ttls) {\n            this.#initializeTTLTracking();\n        }\n        if (this.#ttls) {\n            if (!noUpdateTTL) {\n                this.#setItemTTL(index, ttl, start);\n            }\n            if (status)\n                this.#statusTTL(status, index);\n        }\n        if (!noDisposeOnSet && this.#hasDisposeAfter && this.#disposed) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n        return this;\n    }\n    /**\n     * Evict the least recently used item, returning its value or\n     * `undefined` if cache is empty.\n     */\n    pop() {\n        try {\n            while (this.#size) {\n                const val = this.#valList[this.#head];\n                this.#evict(true);\n                if (this.#isBackgroundFetch(val)) {\n                    if (val.__staleWhileFetching) {\n                        return val.__staleWhileFetching;\n                    }\n                }\n                else if (val !== undefined) {\n                    return val;\n                }\n            }\n        }\n        finally {\n            if (this.#hasDisposeAfter && this.#disposed) {\n                const dt = this.#disposed;\n                let task;\n                while ((task = dt?.shift())) {\n                    this.#disposeAfter?.(...task);\n                }\n            }\n        }\n    }\n    #evict(free) {\n        const head = this.#head;\n        const k = this.#keyList[head];\n        const v = this.#valList[head];\n        if (this.#hasFetchMethod && this.#isBackgroundFetch(v)) {\n            v.__abortController.abort(new Error('evicted'));\n        }\n        else if (this.#hasDispose || this.#hasDisposeAfter) {\n            if (this.#hasDispose) {\n                this.#dispose?.(v, k, 'evict');\n            }\n            if (this.#hasDisposeAfter) {\n                this.#disposed?.push([v, k, 'evict']);\n            }\n        }\n        this.#removeItemSize(head);\n        // if we aren't about to use the index, then null these out\n        if (free) {\n            this.#keyList[head] = undefined;\n            this.#valList[head] = undefined;\n            this.#free.push(head);\n        }\n        if (this.#size === 1) {\n            this.#head = this.#tail = 0;\n            this.#free.length = 0;\n        }\n        else {\n            this.#head = this.#next[head];\n        }\n        this.#keyMap.delete(k);\n        this.#size--;\n        return head;\n    }\n    /**\n     * Check if a key is in the cache, without updating the recency of use.\n     * Will return false if the item is stale, even though it is technically\n     * in the cache.\n     *\n     * Check if a key is in the cache, without updating the recency of\n     * use. Age is updated if {@link LRUCache.OptionsBase.updateAgeOnHas} is set\n     * to `true` in either the options or the constructor.\n     *\n     * Will return `false` if the item is stale, even though it is technically in\n     * the cache. The difference can be determined (if it matters) by using a\n     * `status` argument, and inspecting the `has` field.\n     *\n     * Will not update item age unless\n     * {@link LRUCache.OptionsBase.updateAgeOnHas} is set.\n     */\n    has(k, hasOptions = {}) {\n        const { updateAgeOnHas = this.updateAgeOnHas, status } = hasOptions;\n        const index = this.#keyMap.get(k);\n        if (index !== undefined) {\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v) &&\n                v.__staleWhileFetching === undefined) {\n                return false;\n            }\n            if (!this.#isStale(index)) {\n                if (updateAgeOnHas) {\n                    this.#updateItemAge(index);\n                }\n                if (status) {\n                    status.has = 'hit';\n                    this.#statusTTL(status, index);\n                }\n                return true;\n            }\n            else if (status) {\n                status.has = 'stale';\n                this.#statusTTL(status, index);\n            }\n        }\n        else if (status) {\n            status.has = 'miss';\n        }\n        return false;\n    }\n    /**\n     * Like {@link LRUCache#get} but doesn't update recency or delete stale\n     * items.\n     *\n     * Returns `undefined` if the item is stale, unless\n     * {@link LRUCache.OptionsBase.allowStale} is set.\n     */\n    peek(k, peekOptions = {}) {\n        const { allowStale = this.allowStale } = peekOptions;\n        const index = this.#keyMap.get(k);\n        if (index === undefined ||\n            (!allowStale && this.#isStale(index))) {\n            return;\n        }\n        const v = this.#valList[index];\n        // either stale and allowed, or forcing a refresh of non-stale value\n        return this.#isBackgroundFetch(v) ? v.__staleWhileFetching : v;\n    }\n    #backgroundFetch(k, index, options, context) {\n        const v = index === undefined ? undefined : this.#valList[index];\n        if (this.#isBackgroundFetch(v)) {\n            return v;\n        }\n        const ac = new AC();\n        const { signal } = options;\n        // when/if our AC signals, then stop listening to theirs.\n        signal?.addEventListener('abort', () => ac.abort(signal.reason), {\n            signal: ac.signal,\n        });\n        const fetchOpts = {\n            signal: ac.signal,\n            options,\n            context,\n        };\n        const cb = (v, updateCache = false) => {\n            const { aborted } = ac.signal;\n            const ignoreAbort = options.ignoreFetchAbort && v !== undefined;\n            if (options.status) {\n                if (aborted && !updateCache) {\n                    options.status.fetchAborted = true;\n                    options.status.fetchError = ac.signal.reason;\n                    if (ignoreAbort)\n                        options.status.fetchAbortIgnored = true;\n                }\n                else {\n                    options.status.fetchResolved = true;\n                }\n            }\n            if (aborted && !ignoreAbort && !updateCache) {\n                return fetchFail(ac.signal.reason);\n            }\n            // either we didn't abort, and are still here, or we did, and ignored\n            const bf = p;\n            if (this.#valList[index] === p) {\n                if (v === undefined) {\n                    if (bf.__staleWhileFetching) {\n                        this.#valList[index] = bf.__staleWhileFetching;\n                    }\n                    else {\n                        this.#delete(k, 'fetch');\n                    }\n                }\n                else {\n                    if (options.status)\n                        options.status.fetchUpdated = true;\n                    this.set(k, v, fetchOpts.options);\n                }\n            }\n            return v;\n        };\n        const eb = (er) => {\n            if (options.status) {\n                options.status.fetchRejected = true;\n                options.status.fetchError = er;\n            }\n            return fetchFail(er);\n        };\n        const fetchFail = (er) => {\n            const { aborted } = ac.signal;\n            const allowStaleAborted = aborted && options.allowStaleOnFetchAbort;\n            const allowStale = allowStaleAborted || options.allowStaleOnFetchRejection;\n            const noDelete = allowStale || options.noDeleteOnFetchRejection;\n            const bf = p;\n            if (this.#valList[index] === p) {\n                // if we allow stale on fetch rejections, then we need to ensure that\n                // the stale value is not removed from the cache when the fetch fails.\n                const del = !noDelete || bf.__staleWhileFetching === undefined;\n                if (del) {\n                    this.#delete(k, 'fetch');\n                }\n                else if (!allowStaleAborted) {\n                    // still replace the *promise* with the stale value,\n                    // since we are done with the promise at this point.\n                    // leave it untouched if we're still waiting for an\n                    // aborted background fetch that hasn't yet returned.\n                    this.#valList[index] = bf.__staleWhileFetching;\n                }\n            }\n            if (allowStale) {\n                if (options.status && bf.__staleWhileFetching !== undefined) {\n                    options.status.returnedStale = true;\n                }\n                return bf.__staleWhileFetching;\n            }\n            else if (bf.__returned === bf) {\n                throw er;\n            }\n        };\n        const pcall = (res, rej) => {\n            const fmp = this.#fetchMethod?.(k, v, fetchOpts);\n            if (fmp && fmp instanceof Promise) {\n                fmp.then(v => res(v === undefined ? undefined : v), rej);\n            }\n            // ignored, we go until we finish, regardless.\n            // defer check until we are actually aborting,\n            // so fetchMethod can override.\n            ac.signal.addEventListener('abort', () => {\n                if (!options.ignoreFetchAbort ||\n                    options.allowStaleOnFetchAbort) {\n                    res(undefined);\n                    // when it eventually resolves, update the cache.\n                    if (options.allowStaleOnFetchAbort) {\n                        res = v => cb(v, true);\n                    }\n                }\n            });\n        };\n        if (options.status)\n            options.status.fetchDispatched = true;\n        const p = new Promise(pcall).then(cb, eb);\n        const bf = Object.assign(p, {\n            __abortController: ac,\n            __staleWhileFetching: v,\n            __returned: undefined,\n        });\n        if (index === undefined) {\n            // internal, don't expose status.\n            this.set(k, bf, { ...fetchOpts.options, status: undefined });\n            index = this.#keyMap.get(k);\n        }\n        else {\n            this.#valList[index] = bf;\n        }\n        return bf;\n    }\n    #isBackgroundFetch(p) {\n        if (!this.#hasFetchMethod)\n            return false;\n        const b = p;\n        return (!!b &&\n            b instanceof Promise &&\n            b.hasOwnProperty('__staleWhileFetching') &&\n            b.__abortController instanceof AC);\n    }\n    async fetch(k, fetchOptions = {}) {\n        const { \n        // get options\n        allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, \n        // set options\n        ttl = this.ttl, noDisposeOnSet = this.noDisposeOnSet, size = 0, sizeCalculation = this.sizeCalculation, noUpdateTTL = this.noUpdateTTL, \n        // fetch exclusive options\n        noDeleteOnFetchRejection = this.noDeleteOnFetchRejection, allowStaleOnFetchRejection = this.allowStaleOnFetchRejection, ignoreFetchAbort = this.ignoreFetchAbort, allowStaleOnFetchAbort = this.allowStaleOnFetchAbort, context, forceRefresh = false, status, signal, } = fetchOptions;\n        if (!this.#hasFetchMethod) {\n            if (status)\n                status.fetch = 'get';\n            return this.get(k, {\n                allowStale,\n                updateAgeOnGet,\n                noDeleteOnStaleGet,\n                status,\n            });\n        }\n        const options = {\n            allowStale,\n            updateAgeOnGet,\n            noDeleteOnStaleGet,\n            ttl,\n            noDisposeOnSet,\n            size,\n            sizeCalculation,\n            noUpdateTTL,\n            noDeleteOnFetchRejection,\n            allowStaleOnFetchRejection,\n            allowStaleOnFetchAbort,\n            ignoreFetchAbort,\n            status,\n            signal,\n        };\n        let index = this.#keyMap.get(k);\n        if (index === undefined) {\n            if (status)\n                status.fetch = 'miss';\n            const p = this.#backgroundFetch(k, index, options, context);\n            return (p.__returned = p);\n        }\n        else {\n            // in cache, maybe already fetching\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v)) {\n                const stale = allowStale && v.__staleWhileFetching !== undefined;\n                if (status) {\n                    status.fetch = 'inflight';\n                    if (stale)\n                        status.returnedStale = true;\n                }\n                return stale ? v.__staleWhileFetching : (v.__returned = v);\n            }\n            // if we force a refresh, that means do NOT serve the cached value,\n            // unless we are already in the process of refreshing the cache.\n            const isStale = this.#isStale(index);\n            if (!forceRefresh && !isStale) {\n                if (status)\n                    status.fetch = 'hit';\n                this.#moveToTail(index);\n                if (updateAgeOnGet) {\n                    this.#updateItemAge(index);\n                }\n                if (status)\n                    this.#statusTTL(status, index);\n                return v;\n            }\n            // ok, it is stale or a forced refresh, and not already fetching.\n            // refresh the cache.\n            const p = this.#backgroundFetch(k, index, options, context);\n            const hasStale = p.__staleWhileFetching !== undefined;\n            const staleVal = hasStale && allowStale;\n            if (status) {\n                status.fetch = isStale ? 'stale' : 'refresh';\n                if (staleVal && isStale)\n                    status.returnedStale = true;\n            }\n            return staleVal ? p.__staleWhileFetching : (p.__returned = p);\n        }\n    }\n    async forceFetch(k, fetchOptions = {}) {\n        const v = await this.fetch(k, fetchOptions);\n        if (v === undefined)\n            throw new Error('fetch() returned undefined');\n        return v;\n    }\n    memo(k, memoOptions = {}) {\n        const memoMethod = this.#memoMethod;\n        if (!memoMethod) {\n            throw new Error('no memoMethod provided to constructor');\n        }\n        const { context, forceRefresh, ...options } = memoOptions;\n        const v = this.get(k, options);\n        if (!forceRefresh && v !== undefined)\n            return v;\n        const vv = memoMethod(k, v, {\n            options,\n            context,\n        });\n        this.set(k, vv, options);\n        return vv;\n    }\n    /**\n     * Return a value from the cache. Will update the recency of the cache\n     * entry found.\n     *\n     * If the key is not found, get() will return `undefined`.\n     */\n    get(k, getOptions = {}) {\n        const { allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, status, } = getOptions;\n        const index = this.#keyMap.get(k);\n        if (index !== undefined) {\n            const value = this.#valList[index];\n            const fetching = this.#isBackgroundFetch(value);\n            if (status)\n                this.#statusTTL(status, index);\n            if (this.#isStale(index)) {\n                if (status)\n                    status.get = 'stale';\n                // delete only if not an in-flight background fetch\n                if (!fetching) {\n                    if (!noDeleteOnStaleGet) {\n                        this.#delete(k, 'expire');\n                    }\n                    if (status && allowStale)\n                        status.returnedStale = true;\n                    return allowStale ? value : undefined;\n                }\n                else {\n                    if (status &&\n                        allowStale &&\n                        value.__staleWhileFetching !== undefined) {\n                        status.returnedStale = true;\n                    }\n                    return allowStale ? value.__staleWhileFetching : undefined;\n                }\n            }\n            else {\n                if (status)\n                    status.get = 'hit';\n                // if we're currently fetching it, we don't actually have it yet\n                // it's not stale, which means this isn't a staleWhileRefetching.\n                // If it's not stale, and fetching, AND has a __staleWhileFetching\n                // value, then that means the user fetched with {forceRefresh:true},\n                // so it's safe to return that value.\n                if (fetching) {\n                    return value.__staleWhileFetching;\n                }\n                this.#moveToTail(index);\n                if (updateAgeOnGet) {\n                    this.#updateItemAge(index);\n                }\n                return value;\n            }\n        }\n        else if (status) {\n            status.get = 'miss';\n        }\n    }\n    #connect(p, n) {\n        this.#prev[n] = p;\n        this.#next[p] = n;\n    }\n    #moveToTail(index) {\n        // if tail already, nothing to do\n        // if head, move head to next[index]\n        // else\n        //   move next[prev[index]] to next[index] (head has no prev)\n        //   move prev[next[index]] to prev[index]\n        // prev[index] = tail\n        // next[tail] = index\n        // tail = index\n        if (index !== this.#tail) {\n            if (index === this.#head) {\n                this.#head = this.#next[index];\n            }\n            else {\n                this.#connect(this.#prev[index], this.#next[index]);\n            }\n            this.#connect(this.#tail, index);\n            this.#tail = index;\n        }\n    }\n    /**\n     * Deletes a key out of the cache.\n     *\n     * Returns true if the key was deleted, false otherwise.\n     */\n    delete(k) {\n        return this.#delete(k, 'delete');\n    }\n    #delete(k, reason) {\n        let deleted = false;\n        if (this.#size !== 0) {\n            const index = this.#keyMap.get(k);\n            if (index !== undefined) {\n                deleted = true;\n                if (this.#size === 1) {\n                    this.#clear(reason);\n                }\n                else {\n                    this.#removeItemSize(index);\n                    const v = this.#valList[index];\n                    if (this.#isBackgroundFetch(v)) {\n                        v.__abortController.abort(new Error('deleted'));\n                    }\n                    else if (this.#hasDispose || this.#hasDisposeAfter) {\n                        if (this.#hasDispose) {\n                            this.#dispose?.(v, k, reason);\n                        }\n                        if (this.#hasDisposeAfter) {\n                            this.#disposed?.push([v, k, reason]);\n                        }\n                    }\n                    this.#keyMap.delete(k);\n                    this.#keyList[index] = undefined;\n                    this.#valList[index] = undefined;\n                    if (index === this.#tail) {\n                        this.#tail = this.#prev[index];\n                    }\n                    else if (index === this.#head) {\n                        this.#head = this.#next[index];\n                    }\n                    else {\n                        const pi = this.#prev[index];\n                        this.#next[pi] = this.#next[index];\n                        const ni = this.#next[index];\n                        this.#prev[ni] = this.#prev[index];\n                    }\n                    this.#size--;\n                    this.#free.push(index);\n                }\n            }\n        }\n        if (this.#hasDisposeAfter && this.#disposed?.length) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n        return deleted;\n    }\n    /**\n     * Clear the cache entirely, throwing away all values.\n     */\n    clear() {\n        return this.#clear('delete');\n    }\n    #clear(reason) {\n        for (const index of this.#rindexes({ allowStale: true })) {\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v)) {\n                v.__abortController.abort(new Error('deleted'));\n            }\n            else {\n                const k = this.#keyList[index];\n                if (this.#hasDispose) {\n                    this.#dispose?.(v, k, reason);\n                }\n                if (this.#hasDisposeAfter) {\n                    this.#disposed?.push([v, k, reason]);\n                }\n            }\n        }\n        this.#keyMap.clear();\n        this.#valList.fill(undefined);\n        this.#keyList.fill(undefined);\n        if (this.#ttls && this.#starts) {\n            this.#ttls.fill(0);\n            this.#starts.fill(0);\n        }\n        if (this.#sizes) {\n            this.#sizes.fill(0);\n        }\n        this.#head = 0;\n        this.#tail = 0;\n        this.#free.length = 0;\n        this.#calculatedSize = 0;\n        this.#size = 0;\n        if (this.#hasDisposeAfter && this.#disposed) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n    }\n}\nexports.LRUCache = LRUCache;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lru-cache/dist/commonjs/index.js\n");

/***/ })

};
;