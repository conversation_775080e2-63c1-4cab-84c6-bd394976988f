import React, { useState, useEffect, useRef, use } from 'react';
import { MdOutlineRefresh } from 'react-icons/md';
import { useChat } from '@/contexts';
import { fetchSpaceDocuments, fetchAllSpaces } from '@/api';
import { LoadingSpinner } from './ui/LoadingSpinner';
import { getPlaygroundLabels } from '@/utils/playgroundLabels';
import type { FileInfo } from '@/types/knowledge-base';

export interface FileSelectorProps {
  isOpen: boolean;
  onClose: () => void;
}


export default function FileSelector({ isOpen, onClose }: FileSelectorProps) {
  /* --------------------------------------------------
   * Context & state
   * --------------------------------------------------*/
  const {
    selectedFiles: chatSelectedFiles,
    setSelectedFiles: setChatSelectedFiles,
    setUseDocumentSearch,
    setCurrentSearchSpaceId,
  } = useChat();

  const [selectedSpaceId, setSelectedSpaceId] = useState(Number(0)); // 0 = All
  const [spaceDocuments, setSpaceDocuments] = useState<FileInfo[]>([]);
  const [loadingFiles, setLoadingFiles] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [spaces, setSpaces] = useState<any[]>([]);
  // Global bucket: Store document_id -> FileInfo mapping for selected files from ALL spaces
  const [selectedFiles, setSelectedFiles] = useState<Map<number, FileInfo>>(new Map());

  // Load spaces helper (reused on mount, open, and manual refresh)
  const loadSpaces = async () => {
    try {
      const data = await fetchAllSpaces();
      if (data) {
        setSpaces(data);
        // If there are no spaces or the previously selected space no longer exists,
        // reset the selection to 0 so we don't fetch documents for a stale space id
        const exists = data.some((s: any) => s.id === selectedSpaceId);
        if (data.length === 0 || (!exists && selectedSpaceId !== 0)) {
          setSelectedSpaceId(0);
        }
      } else {
        setSpaces([]);
        if (selectedSpaceId !== 0) setSelectedSpaceId(0);
      }
      return data || [];
    } catch (error) {
      console.error('Error fetching spaces:', error);
      return [] as any[];
    }
  };

  // Load once on mount
  useEffect(() => {
    loadSpaces();
  }, []);

  // Also refresh spaces whenever dialog is opened
  useEffect(() => {
    if (isOpen) {
      (async () => {
        const latest = await loadSpaces();
        const exists = latest.some((s: any) => s.id === selectedSpaceId);
        if (selectedSpaceId > 0 && exists) {
          await loadDocuments();
        }
      })();
    }
  }, [isOpen]);

  // Initialize FileSelector with existing ChatContext selections when dialog opens
  useEffect(() => {
    if (isOpen && chatSelectedFiles.length > 0) {
      //console.log('Initializing FileSelector with existing ChatContext selections:', chatSelectedFiles);
      // Note: We'll need to match filenames to FileInfo objects when documents are loaded
      // This is handled in the loadDocuments effect below
    }
  }, [isOpen, chatSelectedFiles]);

  const loadDocuments = async () => {
    setLoadingFiles(true);
    try {
      if(selectedSpaceId){
        const data = await fetchSpaceDocuments(selectedSpaceId);
        if (data) {
          setSpaceDocuments(data);
          
          // Initialize selections from ChatContext if FileSelector is opening with existing selections
          if (isOpen && chatSelectedFiles.length > 0) {
            const newSelectedFiles = new Map<number, FileInfo>();
            
            // Match ChatContext filenames to FileInfo objects
            data.forEach(doc => {
              if (chatSelectedFiles.includes(doc.filename)) {
                newSelectedFiles.set(doc.id, doc);
              }
            });
            
            if (newSelectedFiles.size > 0) {
              setSelectedFiles(prev => {
                const merged = new Map(prev);
                newSelectedFiles.forEach((fileInfo, id) => {
                  merged.set(id, fileInfo);
                });
                return merged;
              });
              //console.log('Initialized FileSelector with existing selections:', newSelectedFiles.size, 'files');
            }
          }
        }
      }
      else{
          //console.log("space id is null")
      }
    } catch (error) {
      console.error('Error fetching space documents:', error);
    } finally {
      setLoadingFiles(false);
    }
  };
  
  // Manual refresh: spaces list and current space documents
  const refreshAll = async () => {
    setIsRefreshing(true);
    try {
      const latest = await loadSpaces();
      const stillExists = latest.some((s: any) => s.id === selectedSpaceId);
      if (selectedSpaceId > 0 && stillExists) {
        await loadDocuments();
      }
    } finally {
      setIsRefreshing(false);
    }
  };
   
  useEffect(() => {
    if (selectedSpaceId > 0) {
      loadDocuments();
    } else {
      setSpaceDocuments([]);
      setLoadingFiles(false);
    }
  }, [selectedSpaceId]);

  /* --------------------------------------------------
   * Helpers
   * --------------------------------------------------*/
  const getSpaceById = (id?: number) => spaces.find((s) => s.id === id);
  const isSharedSpaceId = (id?: number) => (getSpaceById(id)?.space_type === 'shared');

  // Enforce selection rules:
  // - If any file from a shared space is selected, user cannot select documents from any other space
  // - Personal spaces can be mixed across multiple personal spaces, but never with any shared selection
  // - Cannot mix playground types (documents vs images)
  const canAddFileToSelection = (file: FileInfo, current: Map<number, FileInfo>): boolean => {
    const fileSpaceId = file.space_id;
    const fileSpace = getSpaceById(fileSpaceId);
    const newIsShared = isSharedSpaceId(fileSpaceId);
    const newPlaygroundType = fileSpace?.playground_type || 'documents';

    // If file is already selected, removal is always allowed (handled elsewhere)

    // Gather current selection spaces
    const currentFiles = Array.from(current.values());
    if (currentFiles.length === 0) return true; // nothing selected yet

    const currentHasShared = currentFiles.some(f => isSharedSpaceId(f.space_id));
    const currentSpaceIds = new Set(currentFiles.map(f => f.space_id));
    
    // Check playground type compatibility
    const currentPlaygroundTypes = new Set(
      currentFiles.map(f => getSpaceById(f.space_id)?.playground_type || 'documents')
    );
    
    // Cannot mix playground types
    if (currentPlaygroundTypes.size > 0 && !currentPlaygroundTypes.has(newPlaygroundType)) {
      return false;
    }

    if (newIsShared) {
      // When selecting from a shared space: allowed only if all current selections are from the same space
      return currentSpaceIds.size === 1 && currentSpaceIds.has(fileSpaceId);
    }

    // When selecting from personal space: allowed only if there is no shared selection already
    return !currentHasShared;
  };
  const handleFileToggle = (file: FileInfo) => {
    // Determine the playground type for the file's space
    const fileSpace = getSpaceById(file.space_id);
    const filePlaygroundType = fileSpace?.playground_type || 'documents';
    const isDocFile = filePlaygroundType === 'documents';

    // Only enforce indexing requirement for document spaces
    if (isDocFile && !file.indexed) {
      alert('Please index this document before selecting it for search.');
      return;
    }

    setSelectedFiles(prev => {
      const newMap = new Map(prev);
      
      if (newMap.has(file.id)) {
        // Remove from global bucket if already selected
        newMap.delete(file.id);
        //console.log(`Removed document ${file.id} (${file.title || file.filename}) from global bucket`);
      } else {
        // Enforce cross-space selection rules before adding
        if (!canAddFileToSelection(file, prev)) {
          const fileSpace = getSpaceById(file.space_id);
          const filePlaygroundType = fileSpace?.playground_type || 'documents';
          const isShared = isSharedSpaceId(file.space_id);
          
          // Check what type of conflict this is
          const currentFiles = Array.from(prev.values());
          const currentPlaygroundTypes = new Set(
            currentFiles.map(f => getSpaceById(f.space_id)?.playground_type || 'documents')
          );
          
          if (currentPlaygroundTypes.size > 0 && !currentPlaygroundTypes.has(filePlaygroundType)) {
            // Playground type conflict
            const currentType = Array.from(currentPlaygroundTypes)[0];
            const currentLabels = getPlaygroundLabels(currentType as 'documents' | 'images');
            const newLabels = getPlaygroundLabels(filePlaygroundType as 'documents' | 'images');
            alert(`You cannot mix ${newLabels.itemNamePlural.toLowerCase()} with ${currentLabels.itemNamePlural.toLowerCase()}. Please deselect current selections first.`);
          } else if (isShared) {
            // Shared space conflict
            alert('You cannot mix documents from a shared space with any other space. Deselect current selections from other spaces first.');
          } else {
            // Personal vs shared conflict
            alert('You cannot mix personal documents with documents already selected from a shared space. Deselect shared selections first.');
          }
          return prev;
        }
        // Add to global bucket if not selected
        newMap.set(file.id, file);
        //console.log(`Added document ${file.id} (${file.title || file.filename}) to global bucket`);
      }
      
      return newMap;
    });
  };

  // Utility functions to access selected files
  const getAllSelectedFiles = (): FileInfo[] => {
    return Array.from(selectedFiles.values());
  };

  const getAllSelectedFileIds = (): number[] => {
    return Array.from(selectedFiles.keys());
  };

  const getSelectedFileById = (id: number): FileInfo | undefined => {
    return selectedFiles.get(id);
  };
  const handleSelectAll = () => {
    const currentSpace = getSpaceById(selectedSpaceId);
    const isDocSpace = (currentSpace?.playground_type || 'documents') === 'documents';
    const selectableDocs = isDocSpace ? spaceDocuments.filter(doc => doc.indexed) : spaceDocuments;
    const selectableIds = selectableDocs.map(doc => doc.id);
    const currentSelectedIds = getAllSelectedFileIds();
    
    // Check if all indexed documents are already selected
    const allIndexedSelected = selectableIds.every(id => currentSelectedIds.includes(id));
    
    if (allIndexedSelected) {
      // Deselect all indexed documents
      setSelectedFiles(prev => {
        const newMap = new Map(prev);
        selectableIds.forEach(id => newMap.delete(id));
        return newMap;
      });
    } else {
      // Before selecting all, enforce cross-space rules
      const currentSpace = getSpaceById(selectedSpaceId);
      const currentPlaygroundType = currentSpace?.playground_type || 'documents';
      const currentIsShared = isSharedSpaceId(selectedSpaceId);
      const currentValues = Array.from(selectedFiles.values());
      const hasSharedAlready = currentValues.some(f => isSharedSpaceId(f.space_id));
      
      // Check playground type compatibility
      const currentPlaygroundTypes = new Set(
        currentValues.map(f => getSpaceById(f.space_id)?.playground_type || 'documents')
      );
      
      if (currentPlaygroundTypes.size > 0 && !currentPlaygroundTypes.has(currentPlaygroundType)) {
        // Playground type conflict
        const existingType = Array.from(currentPlaygroundTypes)[0];
        const existingLabels = getPlaygroundLabels(existingType as 'documents' | 'images');
        const currentLabels = getPlaygroundLabels(currentPlaygroundType as 'documents' | 'images');
        alert(`You cannot select ${currentLabels.itemNamePlural.toLowerCase()} while you have ${existingLabels.itemNamePlural.toLowerCase()} selected. Deselect them first.`);
        return;
      }
      
      if (currentIsShared) {
        // Selecting from a shared space: must either be selecting within the same space or have nothing selected
        const conflict = currentValues.some(f => f.space_id !== selectedSpaceId);
        if (currentValues.length > 0 && conflict) {
          alert('You cannot select documents from this shared space while you have selections from another space. Deselect them first.');
          return;
        }
      } else {
        // Selecting from a personal space: cannot have any shared selections already
        if (hasSharedAlready) {
          alert('You cannot add personal documents while shared-space documents are selected. Deselect shared selections first.');
          return;
        }
      }

      // Select all indexed documents
      setSelectedFiles(prev => {
        const newMap = new Map(prev);
        selectableDocs.forEach(doc => newMap.set(doc.id, doc));
        return newMap;
      });
    }
  };

  const close = () => {
    // Convert global bucket to format expected by ChatContext
    const selectedFilesList = getAllSelectedFiles();
    
    if (selectedFilesList.length > 0) {
      // Convert FileInfo objects to filename strings for ChatContext
      const filenames = selectedFilesList.map(file => file.filename);
      
      // Update ChatContext with selected files and space context
      setChatSelectedFiles(filenames);
      setCurrentSearchSpaceId(selectedSpaceId > 0 ? selectedSpaceId : null);
      
      // Enable document search since files are selected
      setUseDocumentSearch(true);
    
    } else {
      // No files selected, clear ChatContext
      setChatSelectedFiles([]);
      //console.log('No files selected - cleared ChatContext');
    }
    
    onClose();
  };

  /* --------------------------------------------------
   * Custom space dropdown
   * --------------------------------------------------*/
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [spaceMenuOpen, setSpaceMenuOpen] = useState(false);

  useEffect(() => {
    const handler = (e: MouseEvent) => {
      if (spaceMenuOpen && dropdownRef.current && !dropdownRef.current.contains(e.target as Node)) {
        setSpaceMenuOpen(false);
      }
    };
    document.addEventListener('mousedown', handler);
    return () => document.removeEventListener('mousedown', handler);
  }, [spaceMenuOpen]);


  /* --------------------------------------------------
   * Render
   * --------------------------------------------------*/
  return (
    (isOpen && <div
      className="fixed inset-0 z-50 flex items-start justify-center pt-20 bg-black bg-opacity-30"
      // onClick={close}
    >
      <form 
        // onSubmit={(e) => e.preventDefault()} 
        className="bg-white rounded-xl shadow-2xl w-full max-w-lg mx-4 border border-gray-200"
        // onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-sky-100 rounded-t-xl border-b border-sky-200">
          <div>
            {(() => {
              const currentSpace = getSpaceById(selectedSpaceId);
              const type = (currentSpace?.playground_type || 'documents') as 'documents' | 'images';
              const labels = getPlaygroundLabels(type);
              const title = selectedSpaceId === 0 ? 'Select' : `Select ${labels.itemNamePlural}`;
              return (
                <>
                  <h3 className="text-base font-medium text-sky-800">{title}</h3>
                  <p className="text-xs text-sky-600 mt-1">Global Bucket: {selectedFiles.size} selected</p>
                </>
              );
            })()}
          </div>
          <div className="flex items-center gap-2">
              <button
                  type="button"
                  onClick={()=> refreshAll()}               
                  className="text-sky-600 hover:text-sky-800 rounded p-0.5 hover:bg-sky-200"
                  aria-label="Refresh files"
                  disabled={loadingFiles || isRefreshing}
                >
                  <MdOutlineRefresh
                    className={`h-6 w-6 transition-transform ${(loadingFiles || isRefreshing) ? 'animate-spin-fast' : ''}`}
                  />
                </button>
              <button
              onClick={close}
              className="text-sky-600 hover:text-sky-800 rounded p-0.5 hover:bg-sky-200"
              aria-label="Close selector"
            >
              <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 8.586l4.95-4.95a1 1 0 111.414 1.415L11.414 10l4.95 4.95a1 1 0 01-1.414 1.415L10 11.414l-4.95 4.95A1 1 0 013.636 14.95L8.586 10l-4.95-4.95A1 1 0 115.05 3.636L10 8.586z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>

        {/* Space selector */}
         {spaces.length > 0 && (
          <div className="relative p-2 border-b border-gray-100 space-y-1" ref={dropdownRef}>
            <span className="text-sm font-medium text-gray-700">Space</span>
            <button
              type="button"
              onClick={() => setSpaceMenuOpen((o) => !o)}
              className="w-full flex items-center justify-between text-sm border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-2 focus:ring-sky-500"
            >
              <span className="truncate text-gray-800">
                {selectedSpaceId === 0 ? 'Select a space' : spaces.find((s) => s.id === selectedSpaceId)?.name}
              </span>
              <svg
                className={`h-4 w-4 transition-transform ${spaceMenuOpen ? 'rotate-180' : ''}`}
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.25 8.29a.75.75 0 01-.02-1.08z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
            {spaceMenuOpen && (
              <ul className="relative inset-x-0 z-10 mt-1 w-full max-h-56 overflow-auto bg-white border border-gray-200 rounded-md shadow-lg">
                <li>
                  <button
                    type="button"
                    onClick={() => {
                       
                       setSpaceMenuOpen(false);
                    
                    }}
                    className={`block w-full text-left px-4 py-2 text-sm hover:bg-sky-100 ${selectedSpaceId === 0 ? 'bg-sky-50 text-sky-700' : 'text-gray-700'}`}
                  >
                    Select a space
                  </button>
                </li>
                {spaces.map((sp) => (
                  <li key={sp.id}>
                    <button
                      type="button"
                      onClick={() => {
                       
                        setSelectedSpaceId(sp.id);
                        // printfiles(sp.id);
                        setSpaceMenuOpen(false);
                      
                      }}
                      className={`block w-full text-left px-4 py-2 text-sm hover:bg-sky-100 ${selectedSpaceId === sp.id ? 'bg-sky-50 text-sky-700' : 'text-gray-700'}`}
                    >
                      <div className="flex justify-between items-center">
                        <span>{sp.name}</span>
                        <span className="text-xs text-gray-500">
                          {sp.playground_type === 'images' ? 'Images' : 'Documents'}
                        </span>
                      </div>
                    </button>
                  </li>
                ))}
              </ul>
            )}
          </div>
        )} 

        {/* File list */} 
        <div className="overflow-y-auto max-h-80">
          {spaceDocuments.length > 0 && (
            <div className="sticky top-0 z-10 bg-white p-2 border-b border-gray-100 flex justify-between items-center">
              <button
                type="button"
                onClick={() => handleSelectAll()}
                className="text-xs font-medium text-sky-600 hover:text-sky-800"
              >
                {(() => {
                  const currentSpace = getSpaceById(selectedSpaceId);
                  const isDocSpace = (currentSpace?.playground_type || 'documents') === 'documents';
                  const selectableDocs = isDocSpace ? spaceDocuments.filter(doc => doc.indexed) : spaceDocuments;
                  const allSelected = selectableDocs.every(doc => selectedFiles.has(doc.id));
                  if (allSelected) return 'Deselect All';
                  return isDocSpace ? 'Select All Indexed' : 'Select All';
                })()}
              </button>
              
              <span className="text-xs text-gray-500">
                Total selected: {selectedFiles.size}
              </span>
            </div>
          )}

          {loadingFiles ? (
            <div className="p-8 text-center">
              <LoadingSpinner size="sm" message="Loading documents..." />
            </div>
          ) : selectedSpaceId === 0 ? (
            <div className="p-3 text-sm text-gray-500 text-center">Please select a space to view documents</div>
          ) : spaceDocuments.length === 0 ? (
            <div className="p-3 text-sm text-gray-500 text-center">No documents found in this space</div>
          ) : (
            spaceDocuments.map((file) => (
              <label
                 key={file.id}
                className={`group flex items-start gap-3 px-4 py-3 transition border-b last:border-0 ${(() => {
                  const space = getSpaceById(file.space_id);
                  const isDocSpace = (space?.playground_type || 'documents') === 'documents';
                  return isDocSpace && !file.indexed ? 'opacity-60 cursor-not-allowed' : 'hover:bg-sky-50 cursor-pointer odd:bg-gray-50';
                })()}`}
              >
                <input
                  type="checkbox"
                  checked={selectedFiles.has(file.id)}
                  onChange={() => handleFileToggle(file)}
                  disabled={(() => {
                    const space = getSpaceById(file.space_id);
                    const isDocSpace = (space?.playground_type || 'documents') === 'documents';
                    return isDocSpace && !file.indexed;
                  })()}
                  className="accent-sky-600 focus:ring-0 h-4 w-4 mt-1"
                />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate text-gray-800 group-hover:text-sky-800">
                    {file.title || file.filename}
                  </div>
                  {(() => {
                    const space = getSpaceById(file.space_id);
                    const isDocSpace = (space?.playground_type || 'documents') === 'documents';
                    if (isDocSpace && !file.indexed) {
                      return <div className="text-xs text-orange-600">Not indexed - Please index this document first</div>;
                    }
                    return null;
                  })()}
                  </div>
              </label>
            ))
          )}
        </div>

        {/* Footer */}
        <div className="p-4 bg-white rounded-b-xl flex justify-end items-center border-t border-gray-200">
          <button
            onClick={close}
            className="px-5 py-2 text-sm font-medium text-white bg-sky-600 rounded hover:bg-sky-700 active:scale-95 transition-all"
          >
            {(() => {
              const currentSpace = getSpaceById(selectedSpaceId);
              const type = (currentSpace?.playground_type || 'documents') as 'documents' | 'images';
              const labels = getPlaygroundLabels(type);
              return selectedFiles.size > 0 ? `Use ${selectedFiles.size} ${labels.itemNamePlural}` : 'Done';
            })()}
          </button>
        </div>
      </form>
    </div>)
  );
}