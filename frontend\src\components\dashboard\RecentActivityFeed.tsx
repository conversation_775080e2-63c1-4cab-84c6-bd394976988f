'use client';

import React, { useEffect, useRef, useState } from 'react';
import { ActivityFeedResponse, ActivityItem, getMyActivity } from '@/lib/api';

interface Props {
  refreshTrigger?: number;
}

export default function RecentActivityFeed({ refreshTrigger }: Props) {
  const [items, setItems] = useState<ActivityItem[]>([]);
  const [nextOffset, setNextOffset] = useState<number | null>(0);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'note' | 'feedback'>('all');
  const scrollRef = useRef<HTMLDivElement>(null);

  const load = async (offset = 0, append = false) => {
    try {
      if (append) setLoadingMore(true); else setLoading(true);
      const res: ActivityFeedResponse = await getMyActivity({ limit: 20, offset });
      setItems((prev) => (append ? [...prev, ...res.items] : res.items));
      setNextOffset(res.next_offset ?? null);
    } catch (e: any) {
      setError(e?.message || 'Failed to load activity');
    } finally {
      if (append) setLoadingMore(false); else setLoading(false);
    }
  };

  useEffect(() => {
    load(0, false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshTrigger]);

  // Reset internal scroll when switching tabs to avoid window scroll jumps
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = 0;
    }
  }, [activeTab]);

  return (
    <>
      <h3 className="text-lg font-semibold text-gray-900 mt-6 mb-3">Recent Activity</h3>
      <div className="mb-2 border-b border-gray-200">
        <nav className="-mb-px flex space-x-6" aria-label="Tabs">
          {(['all', 'note', 'feedback'] as const).map((tab) => (
            <button
              key={tab}
              className={`whitespace-nowrap pb-2 px-1 border-b-2 text-sm font-medium ${
                activeTab === tab
                  ? 'border-blue-900 text-blue-900'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab(tab)}
            >
              {tab === 'all' ? 'All' : tab === 'note' ? 'Notes' : 'Feedbacks'}
            </button>
          ))}
        </nav>
      </div>
      <div className="bg-white rounded-xl shadow-sm border p-3">
        {loading && <div className="text-center text-gray-500 mb-2">Loading activity…</div>}
        {error && <div className="text-red-600">{error}</div>}
        {!loading && items.filter((it) => (activeTab === 'all' ? (it.type === 'note' || it.type === 'feedback') : it.type === activeTab)).length === 0 && (
          <div className="text-gray-500">No recent activity.</div>
        )}
        <div ref={scrollRef}>
          <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {items
              .filter((it) => (activeTab === 'all' ? (it.type === 'note' || it.type === 'feedback') : it.type === activeTab))
              .map((it) => (
              <li
                key={`${it.type}-${it.id}`}
                className={
                  `rounded-lg p-3 border-l-4 ` +
                  (it.type === 'note'
                    ? 'border-amber-400 bg-amber-50/50'
                    : it.type === 'feedback'
                    ? 'border-emerald-400 bg-emerald-50/50'
                    : 'border-gray-300 bg-gray-50')
                }
              >
                <div className="flex items-center justify-between mb-1">
                  <div className="text-xs text-gray-500">{new Date(it.created_at).toLocaleString()}</div>
                  <span className={`text-[10px] px-2 py-0.5 rounded-full font-medium ` +
                    (it.type === 'note'
                      ? 'bg-amber-100 text-amber-800'
                      : it.type === 'feedback'
                        ? 'bg-emerald-100 text-emerald-800'
                        : 'bg-gray-100 text-gray-700')
                  }>
                    {it.type === 'note' ? 'Note' : it.type === 'feedback' ? 'Feedback' : 'Other'}
                  </span>
                </div>
                {it.type === 'note' && (
                  <div className="text-sm text-gray-900 truncate"><span className="font-medium">Note {it.action}:</span> {it.title}</div>
                )}
                {it.type === 'feedback' && (
                  <div className="text-sm text-gray-900 truncate"><span className="font-medium">Feedback {it.action}:</span> {it.is_helpful ? 'Helpful' : 'Not helpful'}{typeof it.rating === 'number' ? ` (${it.rating}/10)` : ''}</div>
                )}
              </li>
            ))}
          </ul>
        </div>
        {nextOffset !== null && (
          <div className="mt-3 text-right">
            <button
              disabled={loadingMore}
              onClick={() => load(nextOffset || 0, true)}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm"
            >
              {loadingMore ? 'Loading…' : 'Load more'}
            </button>
          </div>
        )}
      </div>
    </>
  );
}


