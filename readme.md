### Technical Whitepaper: Workplace SLM


- The system is a multimodal AI assistant with:
  - Knowledge spaces and documents (PDF/DOCX/Text) ingested to Qdrant.
  - Hybrid RAG (dense + sparse) with reranking and raw-chunk grounding.
  - Live chat (1:1 and groups), attachments, presence, read-receipts, notifications.
  - Deep research workflow with tool use and explainable reasoning.
- Architecture emphasizes stateless pipelines, shared connection pools, and separation of heavy compute (embeddings, rerank, ingestion) into external microservices, with Redis for caching and EMQX MQTT for realtime.

---

### High-Level Architecture

- **Frontend (Next.js + Tailwind)**: Pages under `frontend/src/app`, UI components under `frontend/src/components`, realtime via `MQTTManager`, chat context in `contexts/Livechatcontext.tsx`.
- **Backend (FastAPI monolith)**: Entry `Backend/app/main.py`. Routers grouped by domains under `app/routers/*`. Services under `app/services/*`. Data retrieval under `app/Data_Retriever/*`. Agents in `app/agents/*`.
- **Data Ingestion Service**: `Data_Ingestion_Service` (FastAPI). Converts uploads, chunks, embeds via Embedding API, and writes to Qdrant.
- **External GPU Services**: Embedding API, Reranking API; configured via env.
- **Infra**: Postgres (SQLAlchemy ORM), Qdrant (vectors), MinIO (object storage), Redis (cache), EMQX (MQTT broker).

Data flow: Upload → MinIO + DB row → Ingestion Service → Qdrant vectors + raw_documents table → RAG query → rerank → raw chunk fetch → answer → FE renders sources.

---

### Backend Deep Dive

#### App Entrypoint and Routing

- `Backend/app/main.py`: configures CORS, validation handler, and mounts routers under `API_PREFIX`. Routers include `auth`, `agent`, `chat`, `knowledge_base`, `document_management`, `group_chat`, `notes`, `feedback`, `dashboard`, `ticket`, `account_requests`, `user_deletion`.

Code reference:
```139:153:Backend/app/main.py
# Include routers
app.include_router(auth_router, prefix=settings.API_PREFIX)
app.include_router(agent_router, prefix=settings.API_PREFIX)
app.include_router(user_space_router, prefix=settings.API_PREFIX)
app.include_router(knowledge_router, prefix=settings.API_PREFIX)
app.include_router(chat_router, prefix=settings.API_PREFIX)
app.include_router(document_router, prefix=settings.API_PREFIX)
app.include_router(group_chat_router, prefix=settings.API_PREFIX)
app.include_router(notes_router, prefix=settings.API_PREFIX)
app.include_router(feedback.router, prefix=settings.API_PREFIX)
app.include_router(account_requests_router, prefix=settings.API_PREFIX)
app.include_router(user_deletion_router, prefix=settings.API_PREFIX)
app.include_router(dashboard_router, prefix=settings.API_PREFIX)
app.include_router(ticket_router, prefix=settings.API_PREFIX)
```

- Startup initializes Redis to enable caches and presence storage.

#### Configuration

- `Backend/app/config.py` uses `.env` with environment suffixing:
  - **Core**: `ENVIRONMENT`, `API_PREFIX`, `PROJECT_NAME`
  - **Auth**: `SECRET_KEY`, `ALGORITHM`, `ACCESS_TOKEN_EXPIRE_MINUTES`
  - **Databases**: `DATABASE_URL_{ENV}`
  - **Qdrant**: `QDRANT_URL_{ENV}`, `GRPC_PORT_{ENV}`, `QDRANT_API_KEY_{Prod/Dev}`
  - **MinIO**: `MINIO_ENDPOINT`, `MINIO_PUBLIC_ENDPOINT`, `MINIO_ACCESS_KEY`, `MINIO_SECRET_KEY`, `MINIO_BUCKET_{Prod/Dev}`
  - **CORS**: defaults are permissive for dev; restrict in prod
  - **External Services**: `DATA_INGESTION_SERVICE_URL`, embedding/reranking URLs (via ExternalServicesManager defaults)
  - **MQTT**: host/ports, broker JWT config, topic prefix (read by chat router and MQTT service)
  - **Redis**: host/port/db and cache prefixes/TTLs used throughout services

Recommendation: document all env keys in a `README Ops` for each environment and enforce via CI check.

#### Persistence

- **ORM**: `Backend/app/database.py` sets SQLAlchemy engine, `SessionLocal`, and `Base`.
- **Core models**: `app/models` includes users, spaces, conversations/messages, knowledge documents, media, group chat, notes, tickets, feedback, raw documents. Notable:
  - `User`: password hashing with passlib, roles `admin/user`, status `pending/approved/rejected`, `is_active`.
  - `KnowledgeDocument`: stores metadata, MinIO object keys, content (when text), `indexed` flags, and relationships to spaces.
  - `RawDocument`: holds chunk content or images (base64) for grounding.
  - `Conversation`, `Message`: with soft-delete map in `DeletedMessage` for per-user clear history.
- **MinIO**: `services/minio_service.py`:
  - Ensures environment main bucket exists; creates user-space folders `knowledge_documents` and `chat_attachments`.
  - Uploads binary or `UploadFile` to user-scoped paths. Generates signed URLs using API endpoint (9000) with optional public endpoint rewriting. Validates content type.
  - Download and delete capabilities.

- **Redis**: `services/redis_service.py`:
  - `RedisService` with connection pools, async/sync clients, JSON serialization, pipelines, and scanning deletes.
  - Decorators `cached` and `invalidate_cache` for endpoint-level caching; scoped invalidation helpers for user, conversation, knowledge, notes, admin.

#### Authentication, Authorization, and Permissions

- JWT:
  - `auth/jwt_handler.py` encodes/decodes HS256 tokens with `exp`.
  - Payload stores `sub`, `user_id`, `role`, `industry`.

Code reference:
```7:19:Backend/app/auth/jwt_handler.py
def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    ...
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
```

- FastAPI dependencies:
  - `auth/dependencies.py` `get_current_user` validates via `HTTPBearer`, decodes token, loads user from DB, enforces `is_active`, supports role-based guards with `require_role`, `require_admin`.
- Chat download endpoints accept `token` query param or `Authorization` header, decode with `get_user_from_token`, and enforce resource-level permissions (user must be conversation participant or group member).

#### Routers and API Surface (selected highlights)

- **Agent** (`routers/agent.py`)
  - `GET /agents`: list available agents (currently `general`).
  - `POST /agents/query`: supports `use_web_search`, `use_document_search`, `use_deep_search`, `allowed_files`, `space_id`, optional `chat_history`. Stores interaction in DB and Redis, invalidates admin caches, updates sessions cache, and returns answer + sources + flags.
  - `POST /agents/cancel`: cancels a running query by session id.
  - `GET /agents/chat-history/{agent_type}`: retrieves chat history (Redis first, DB fallback).
  - `GET/PUT /agents/chat-sessions/*`: list/rename chat sessions (Redis-sourced title overrides).
  - `POST /agents/followups`: generates follow-up suggestions from latest Q/A with context and stores against the interaction.

- **Chat** (`routers/chat.py`)
  - Conversations CRUD, messages, read receipts, unread counts.
  - `POST /chat/attachments`: uploads to MinIO `chat_attachments`.
  - `GET /chat/attachments/download`: streams a file back with auth and content disposition.
  - `GET /chat/mqtt-token`: returns broker JWT & connection info for FE client; ACL restricts publish/subscribe topics to user-specific.

Code reference:
```497:537:Backend/app/routers/chat.py
@router.post("/mqtt-token")
async def get_mqtt_broker_token(
    current_user: User = Depends(get_current_user)
):
    ...
    payload = {
        "sub": f"user:{user_id}",
        "user_id": user_id,
        "username": current_user.username,
        "acl": acl,
        "exp": datetime.utcnow() + timedelta(minutes=config.MQTT_BROKER_JWT_EXPIRE_MINUTES),
        "iat": datetime.utcnow()
    }
    token = jwt.encode(payload, config.MQTT_BROKER_JWT_SECRET, algorithm=config.MQTT_BROKER_JWT_ALGORITHM)
    return { "token": token, "expires_in": ..., "mqtt_host": ..., "mqtt_ws_port": ..., "mqtt_wss_port": ..., "topic_prefix": ... }
```

- **Knowledge Base** (`routers/knowledge_base.py`)
  - `POST /knowledge/` create text doc (stores content and MinIO object).
  - `POST /knowledge/upload/single|bulk` upload PDF/DOCX/PPT/XLS etc. Deduplicates titles with space-aware suffixes for idempotence.
  - `GET /knowledge/{id}` detail (cached per user); `download` (inline) and `download_attachment`.
  - `GET /knowledge/space/{space_id}/documents` list with search & pagination (Redis cached).
  - `POST /knowledge/space/{space_id}/index` triggers indexing via external ingestion service; marks processed docs as indexed and invalidates caches.

- **Document Management** (`routers/document_management.py`)
  - Bulk index with optional department filter; adapts docs (MinIO-backed vs text) into ingestion payload; invalidates caches.

- Other routers manage users/spaces, groups, feedback, dashboard metrics, tickets, account requests, and user deletion.

#### Services and Internal Integrations

- **External Services Manager** (`services/external_services.py`):
  - Normalizes interactions to ingestion, retrieval, embedding, and reranking services.
  - Ingestion: streams MinIO objects directly to ingestion service via multipart form; passes processing config (chunk sizes, unstructured parsing on DOCX).
  - Retrieval: builds `retriever_config` and `query_params`, calls `Data_Retriever.query_documents`.
  - Health checks for external services.
- **Stateless Pipeline & Connection Pools**
  - `services/connection_pools.py`: pooled clients for Gemini (google.genai), Qdrant, Embedding HTTP client, HTTP sessions, and Redis.
  - `services/stateless_pipeline_factory.py`: wraps ingestion/retrieval pipelines to use pooled resources and avoid memory growth.

---

### Retrieval and RAG Pipeline

- **Primary entry**: `app/Data_Retriever/retrieval_pipeline.py` → creates fresh `EnhancedMultiModalRAGPipeline` per request (stateless).
- **Pipeline**: `EnhancedMultiModalRAGPipeline` (`enhanced_pipeline.py`)
  - Uses Gemini for:
    - Query transform for retrieval robustness.
    - Optional multi-query generation (up to 4).
    - Final grounded answer generation.
  - **Hybrid Retrieval**:
    - Dense embeddings (normalize option) + sparse (FastEmbedSparse-style) via `EmbeddingClient` (HTTP).
    - `QdrantVectorStore` configured with `vector_name="dense"`, `sparse_vector_name="sparse"`, `RetrievalMode.HYBRID`, `Distance.DOT`.
  - **Filtering**:
    - If single file selected, applies direct Qdrant `Filter` on `metadata.source`.
    - Supports additional `extra_filter` (e.g., `user_id`) converted to `FieldCondition`s.
  - **Reranking**:
    - HTTP call to a reranking service; converts LangChain docs ↔ payload preserving `confidence_score`.
    - Applies file filtering after reranking for multi-file selection; logs alternative matching strategies (filename normalization, fuzzy overlap).
  - **Grounding**:
    - Fetches raw chunks by IDs from Postgres `raw_documents` via asyncpg in a background thread; builds `raw_docs` and image data URLs for final context.
  - **Response**:
    - Assembles a minimal but precise system prompt; includes document metadata (title/summary) if single-file target; injects chat history if provided.
    - Returns `final_response`, `context_used`, `sources_used`, and doc IDs for provenance.

Code reference:
```1054:1063:Backend/app/Data_Retriever/enhanced_pipeline.py
self.current_vector_store = QdrantVectorStore(
    client=self.qdrant_client,
    collection_name=collection_name,
    embedding=self.embeddings,
    sparse_embedding=self.sparse_embeddings,
    retrieval_mode=RetrievalMode.HYBRID,
    vector_name="dense",
    sparse_vector_name="sparse",
    distance=Distance.DOT,
)
```

- **State graph** (LangGraph):
  - Nodes: `query_transform → (multi_query_generation?) → hybrid_retrieval → rerank_documents → fetch_raw_docs → generate_response`.
  - State schema `RagState` in `state.py` enumerates fields persisted across nodes.

- **Embedding Client**: `EmbeddingClient` wraps HTTP:
  - `embed` endpoint for dense/sparse/hybrid; sparse vectors adapted into indices/values compatible with Qdrant.

#### Cancellation Semantics

- The `agents/base.py` implements cancellation-aware execution for LLM and external calls with short polling; cleans up tool hooks; interrupts background threads swiftly to avoid hung queries or resource leaks.

---

### Data Ingestion Service

- FastAPI app under `Data_Ingestion_Service`:
  - Lifespan context wires any warmup; CORS open for development; `main.py` runs on configurable port.
- `pipeline.py` `MultimodalDataPipeline`:
  - Chunks content with recursive splitter and Markdown-aware paths; builds hybrid embeddings; sets up Qdrant collection schema; pushes documents.
  - Supports multiple formats (text/PDF/DOCX), images (base64), and hybrid vector modes.

- Endpoints (representative):
  - `POST /ingest`: multipart files + form `username`, `collection_name`, `gemini_api_key`, embedding service URL, Qdrant config, chunk params; returns processed/failed counts and task id.
  - `GET /health`: returns service health.

- Backend `external_services.ingest_documents` streams MinIO file contents directly (no temp file write), preserving content type.

---

### AI Agents and Deep Research

- **Agents**: `app/agents/base.py` builds an `agno.agent.Agent` using Gemini with `web_search`, `document_search`, `deep_search` modes toggled per request.

- **Behavior**:
  - Basic chat: uses chat summary when history is long; otherwise raw history.
  - Web search: executes registered tools (e.g., Google/Tavily/Wikipedia/Calculator), hook captures raw tool results and formats consistent sources for FE.
  - Knowledge search: delegates to RAG pipeline; collects doc IDs and pulls raw chunks from DB for better FE presentation (title/snippet).
  - Deep research: orchestrates specialized deep research tool; exposes `reasoning`, `progress_messages`, and formatted sources.

- **Follow-ups**: `routers/agent.py` stores expert interactions and generates suggestions via a dedicated followup agent, writing back to DB.

---

### Realtime & Presence

- **Broker**: EMQX. Topics (examples):
  - Events per user: `${topic_prefix}/chat/users/{userId}/events`
  - Presence per user: `${topic_prefix}/presence/{userId}`
  - Commands per user: `${topic_prefix}/chat/cmd/{userId}`

- **MQTT Microservice**: `Backend/mqtt_service.py`
  - aiomqtt client with reconnection; subscribes to command/presence topics; exposes HTTP endpoints to publish events (user and group). Adds CORS for the main API.
  - Uses broker JWTs on connect and auto-refresh.

- **Backend-to-MQTT**: `services/mqtt_service_client.py`
  - HTTP client to the microservice: `publish_event`, `publish_to_group`, `health_check` with 5s timeout and error resilience.

- **Frontend MQTT**:
  - `lib/mqttManager.ts`: connects (WS/WSS), subscribes to event/presence topics, publishes presence heartbeats every 30s, queues messages offline, reconnects with backoff.
  - `contexts/Livechatcontext.tsx`: manages chat state, loads groups on login, sends commands via `MQTTManager`, handles errors and notifications.

---

### Frontend (Next.js) Deep Dive

- **Pages**: Under `src/app/*` for login, dashboard, chat, activity, admin panels.
- **Components**:
  - Chat stack: `ChatInterface`, `ChatSidebar`, `ChatInput`, `MessageActions`, media/link drawers, modals for group management.
  - Knowledge stack: `KnowledgeBaseSidebar`, `FileSelector`, `DocumentSearchToggle`, `SourcesDisplay` (renders RAG provenance and confidence).
  - Notifications: `NotificationBadge`, `ToastContainer`.
  - Layout: `MainLayout`, `Header`, `Footer`.
- **Contexts**:
  - `ChatContext` (under `lib/ChatContext.tsx`) and `Livechatcontext.tsx` for realtime state and UI updates.
  - `NotificationContext.tsx` for toasts.
- **Hooks**:
  - `useSecureAuth.ts` for guarding routes with backend tokens.
  - `useBlobUrl.ts` for streaming object URLs.

- **Design hooks** for Designers:
  - Visualize source cards with:
    - Document title, filename, content snippet, optional page number, confidence score.
  - States to represent:
    - MQTT connecting/connected/disconnected; offline queue indicator.
    - Upload progress and size validations (enforce 10MB cap).
    - Duplicates flow (warning vs confirm duplicate upload).
    - Indexing status badges (indexed/not-indexed).
    - Read receipts and presence (green dot, last seen).
  - Accessibility: ensure toasts are ARIA-live; keyboard navigation in modals; high contrast mode; media previews with alt text.

---

### Caching Strategy and Invalidation

- **Read caches**:
  - Conversations lists, knowledge lists per space/user, per-document detail per user.
  - Chat sessions lists and custom titles cached per user/agent.
- **Invalidation**:
  - Conversation and user caches invalidated on message send, conversation create/delete.
  - Knowledge caches invalidated per space and per-document on upload/update/delete/index.
  - Admin dashboard caches invalidated on agent query writes.
- **Redis Patterns**: use SCAN-based delete with prefixes (`CACHE_PREFIX_*`). TTLs tuned per domain: short for chat lists; longer for knowledge lists; very long for chat session titles.

---

### Error Handling and Failure Modes

- **Retrieval**:
  - If reranking fails → fall back to top-K retrieved docs.
  - If raw chunk IDs missing in DB → degrade to reranked docs’ own content.
  - If embedding service health != 200 → pipeline proceeds with warnings; ensure FE displays “Sources unavailable” gracefully.
- **Ingestion**:
  - Partial successes: counts reported; caller marks only processed docs as indexed; failed docs listed.
  - DOCX unstructured extraction toggled only when needed.
- **Realtime**:
  - MQTT microservice down → HTTP publish returns false; FE still shows REST updates on reload; presence degrades.
  - Frontend reconnects with backoff; presence heartbeats resume on connect.
- **Auth**:
  - Expired tokens → 401 with message; FE should redirect to login and clear local storage.

---

### Security, Privacy, Compliance

- **Auth**: HS256 JWTs with `exp`; server-side DB user presence check; `is_active` gating; `require_admin` protection for admin tasks.
- **Authorization**:
  - Knowledge doc access: enforced by `SpacePermissions.can_view_space`; download streams only if user has rights; MinIO keys never exposed without auth.
  - Chat attachments: only participants can download; group attachments restricted to group members.
- **Transport**:
  - Require HTTPS and WSS in prod; configure CORS to allowed domains; EMQX behind TLS termination.
- **Secrets**:
  - Store in environment secret stores; never commit. Rotate `SECRET_KEY` and `MQTT_BROKER_JWT_SECRET`.
- **PII**:
  - Avoid logging user content; redact tokens and user identifiers where possible; configure structured logging filters.
- **Data retention**:
  - Define retention policy for `ExpertChatInteraction` and raw chunks; provide export and deletion flows (router exists for user deletion).
- **Compliance**:
  - Map flows to GDPR/CCPA if applicable: right to access/delete, consent for uploads, audit logs for admin operations.

---

### Observability and Performance

- **Logging**: Python logging configured globally; RAG pipeline uses `ComponentTimer` for component timings; external service calls log results and errors.
- **Metrics**: Add counters and histograms: retrieval duration, ingestion duration, reranker latency, Qdrant query counts, MQTT publish success rates.
- **Tracing**: Adopt OpenTelemetry for spans across FE → BE → external services; propagate request IDs via headers.
- **Scaling**:
  - Stateless pipelines make horizontal backend scaling straightforward.
  - EMQX scales with clustered brokers; consider per-tenant topic isolation if needed.
  - Qdrant: tune GRPC, shard by collection; monitor memory usage of hybrid vectors.
  - Redis: use dedicated instance with persistence off for caches; consider sentinel or managed service.
  - MinIO: use distributed mode with erasure coding for HA; enable S3 gateway if needed.

---

### API Contracts (abbreviated)

- Auth:
  - `POST /auth/login` returns `access_token` (bearer). Token payload includes `user_id`, `role`, `industry`.
- Agent:
  - `POST /agents/query` body: `{ agent_type, query, chat_history?, use_web_search?, use_document_search?, use_deep_search?, allowed_files?, space_id?, chat_session_id? }`
  - Response: `{ agent_name, query, answer, sources[], used_rag?, processing_stats?, response_id }`
- Knowledge:
  - `POST /knowledge/` (text); `POST /knowledge/upload/single|bulk` (files); `GET /knowledge/{id}`; `GET /knowledge/{id}/download`; `GET /knowledge/space/{space_id}/documents`; `POST /knowledge/space/{space_id}/index`.
- Chat:
  - `GET /chat/conversations`; `POST /chat/conversations`; `POST /chat/conversations/{id}/messages`; `PUT /chat/conversations/{id}/read`; `DELETE /chat/conversations/{id}`; `POST /chat/attachments`; `GET /chat/attachments/download`; `POST /chat/mqtt-token`.

Provide an OpenAPI export or Postman collection to accelerate FE integration.

---

### Data Lifecycle and Lineage

- Upload (FE) → `knowledge/upload` → MinIO object stored + `KnowledgeDocument` row → index trigger (manual or admin bulk) → `external_services.ingest_documents` streams MinIO to Data Ingestion → Multimodal pipeline chunks & embeds → Qdrant upserts + `raw_documents` DB grounding rows → RAG retrieval uses Qdrant + rerank + raw DB fetch → Gemini answer → FE renders `SourcesDisplay`.

---

### Threat Model (summary)

- Token theft → mitigate with short `ACCESS_TOKEN_EXPIRE_MINUTES`, secure storage, enforce refresh, and logout on anomaly.
- URL leakage → MinIO signed URLs time-bounded and path-scoped; avoid logging them.
- Topic abuse → EMQX broker JWT ACL restricts pub/sub to user topics; server token distinct from browser tokens.
- PII exposure → redact logs, restrict admin data endpoints, rate-limit sensitive operations.

---

### Testing and QA

- Unit tests:
  - Auth deps, MinIO upload/download, Redis cache decorators.
  - Agent pipeline: mock Embedding/Rerank services; assert file filtering, reranking fallback, and raw chunk fetch.
- Integration tests:
  - Upload + index + query end-to-end with a test Qdrant and Postgres.
  - Realtime: simulate MQTT publish and FE subscription with mocked client.
- Performance tests:
  - RAG latency budgets per step (< 300ms retrieval, < 1200ms total typical).
  - Bulk ingestion throughput with varying chunk sizes.

---

### Roadmap

- Add endpoint: `/api/v1/health` aggregator (BE + Redis + Qdrant + MinIO + external services + MQTT microservice).
- Tighten CORS and default config for production.
- Add OpenTelemetry tracing for pipelines and MQTT bridge publishing.
- Optimize SourcesDisplay to show aggregated confidence and deduplicated files with section grouping.
- Self-service exports for user data (privacy compliance).
- Multi-tenant isolation (topic prefixes and Qdrant collections per tenant).

---

### Quick References

- Where routers are mounted:
```139:153:Backend/app/main.py
app.include_router(auth_router, prefix=settings.API_PREFIX)
...
```

- How JWTs are created:
```7:19:Backend/app/auth/jwt_handler.py
encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
```

- Vector store configuration (hybrid):
```1054:1063:Backend/app/Data_Retriever/enhanced_pipeline.py
QdrantVectorStore(..., retrieval_mode=RetrievalMode.HYBRID, vector_name="dense", sparse_vector_name="sparse", distance=Distance.DOT)
```

- FE obtains MQTT token and connection info:
```497:537:Backend/app/routers/chat.py
@router.post("/mqtt-token") ... return {"token": ..., "mqtt_host": ..., ...}
```

---

### Guidance per Team

- Backend:
  - Use `get_current_user` in protected endpoints; enforce space permissions on docs.
  - Cache readonly list endpoints with `cached` and invalidate on writes with provided helpers.
  - For RAG features, prefer `external_services.query_documents` for consistency.

- AI/RAG:
  - Adjust multi-query thresholds; rerank top_n; hybrid vector configs; file filtering strategies.
  - Keep `metadata.id` stable and ensure `raw_documents` table is populated for all ingested chunks.

- Frontend:
  - Call `/chat/mqtt-token` after login; initialize `MQTTManager` with returned `topic_prefix`.
  - Render source cards with filename, snippet, and confidence; preserve accessibility.
  - On 401 responses, clear user and route to `/login`.

- Design:
  - Provide consistent visual language for knowledge status, presence, read receipts, and source provenance.
  - UX flows for duplicate uploads, bulk indexing progress, and cancellation of long-running queries.
  - States for MQTT reconnect attempts and offline queue.