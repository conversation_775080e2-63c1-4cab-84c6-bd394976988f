import React, { useEffect, useState } from 'react';
// @ts-ignore
import { PhotographIcon, DocumentIcon } from '@heroicons/react/solid';
import { API_BASE_URL } from '@/lib/api';

interface MediaItem {
  id: number;
  original_url: string;
  media_type: string;
  thumb_url: string | null;
}

interface LinkItem {
  id: number;
  url: string;
}

interface Props {
  conversationId: number;
  open: boolean;
  onClose: () => void;
}

const MediaLinksDrawer: React.FC<Props> = ({ conversationId, open, onClose }) => {
  const [media, setMedia] = useState<MediaItem[]>([]);
  const [links, setLinks] = useState<LinkItem[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!open) return;
    const token = localStorage.getItem('access_token');
    if (!token) return;
    const fetchData = async () => {
      setLoading(true);
      try {
        const [mediaRes, linkRes] = await Promise.all([
          fetch(`${API_BASE_URL}/chat/conversations/${conversationId}/media`, {
            headers: { Authorization: `Bearer ${token}` }
          }),
          fetch(`${API_BASE_URL}/chat/conversations/${conversationId}/links`, {
            headers: { Authorization: `Bearer ${token}` }
          }),
        ]);
        if (mediaRes.ok) setMedia(await mediaRes.json());
        if (linkRes.ok) setLinks(await linkRes.json());
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [open, conversationId]);

  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 z-50 flex justify-end">
      <div className="w-full max-w-md bg-white h-full shadow-xl overflow-y-auto">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-lg font-semibold">Media & Links</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">✕</button>
        </div>
        {loading ? (
          <p className="p-4">Loading…</p>
        ) : (
          <>
            <section className="p-4">
              <h3 className="font-medium mb-2">Media</h3>
              {media.length === 0 && <p className="text-sm text-gray-500">No media yet.</p>}
              <div className="grid grid-cols-3 gap-3">
                {media.map((m) => {
                  const isImage = m.media_type === 'image' || /\.(png|jpe?g|gif|webp)$/i.test(m.original_url);
                  return (
                    <a
                      href={m.original_url}
                      target="_blank"
                      key={m.id}
                      rel="noopener noreferrer"
                      className="block w-full h-28 bg-gray-100 rounded overflow-hidden flex items-center justify-center"
                    >
                      {isImage ? (
                        <img
                          src={m.thumb_url || m.original_url}
                          alt="media"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="flex flex-col items-center text-gray-500 text-xs">
                          <DocumentIcon className="w-8 h-8 mb-1" />
                          {m.original_url.split('.').pop()?.toUpperCase()}
                        </div>
                      )}
                    </a>
                  );
                })}
              </div>
            </section>

            <section className="p-4 border-t">
              <h3 className="font-medium mb-2">Links</h3>
              {links.length === 0 && <p className="text-sm text-gray-500">No links shared.</p>}
              <ul className="space-y-1 text-sm break-all">
                {links.map((l) => (
                  <li key={l.id}>
                    <a href={l.url} target="_blank" rel="noopener noreferrer" className="text-blue-900 underline">
                      {l.url}
                    </a>
                  </li>
                ))}
              </ul>
            </section>
          </>
        )}
      </div>
    </div>
  );
};

export default MediaLinksDrawer; 