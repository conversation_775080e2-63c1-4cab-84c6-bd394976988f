import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from sqlalchemy import select, func, desc, and_, or_
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from ..database import get_db
from ..models.user import User
from ..models.feedback import ExpertChatInteraction, QueryFeedback, SharedResponse
from ..models.note import Note
from ..models.knowledge_base import KnowledgeDocument
from ..models.user_space import UserSpace
from ..models.chat import UserStatus
from ..auth.dependencies import get_current_user, require_admin
from ..services.redis_service import redis_service, cached
from .. import config

router = APIRouter(prefix="/dashboard", tags=["Dashboard"])

# Configure logging for this module
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UserMetrics:
    """Data class for user metrics"""
    def __init__(self):
        self.ai_query_count = 0
        self.knowledge_docs_uploaded = 0
        self.knowledge_docs_indexed = 0
        self.feedback_submitted = 0
        self.feedback_helpful_count = 0
        self.feedback_avg_rating = 0.0
        self.ai_responses_shared = 0
        self.last_seen = None
        self.is_online = False

class MeSummaryResponse(BaseModel):
    chats_7d: int
    chats_30d: int
    notes_total: int
    feedback_total: int
    feedback_helpful: int
    last_active_at: str | None

class ActivityItem(BaseModel):
    type: str
    id: int
    created_at: str
    # Optional fields depending on type
    content_preview: str | None = None
    title: str | None = None
    action: str | None = None
    rating: int | None = None
    is_helpful: bool | None = None

class ActivityFeedResponse(BaseModel):
    items: list[ActivityItem]
    next_offset: int | None

@router.get("/user-metrics")
@cached(
    prefix=f"{config.CACHE_PREFIX_ADMIN}:user_metrics",
    ttl=config.CACHE_TTL_ADMIN_DASHBOARD
)
async def get_all_user_metrics(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    Get comprehensive metrics for all users (Admin only)
    """
    try:
        # Get all approved users
        result = await db.execute(
            select(User).where(
                User.status == "approved",
                User.is_active == True
            ).order_by(User.username)
        )
        users = result.scalars().all()
        
        user_metrics = []
        
        for user in users:
            metrics = UserMetrics()
            
            # AI Query Count
            ai_query_count = await db.scalar(
                select(func.count(ExpertChatInteraction.id)).where(
                    ExpertChatInteraction.user_id == user.id
                )
            )
            metrics.ai_query_count = ai_query_count
            
            # Knowledge Documents Uploaded and Indexed
            result = await db.execute(
                select(KnowledgeDocument).where(
                    KnowledgeDocument.created_by == user.id
                )
            )
            knowledge_docs = result.scalars().all()
            metrics.knowledge_docs_uploaded = len(knowledge_docs)
            metrics.knowledge_docs_indexed = len([doc for doc in knowledge_docs if doc.indexed])
            
            # Feedback Submitted
            result = await db.execute(
                select(QueryFeedback).where(
                    QueryFeedback.user_id == user.id
                )
            )
            feedback_entries = result.scalars().all()
            metrics.feedback_submitted = len(feedback_entries)
            
            if feedback_entries:
                helpful_count = len([f for f in feedback_entries if f.is_helpful])
                avg_rating = sum(f.rating for f in feedback_entries) / len(feedback_entries)
                metrics.feedback_helpful_count = helpful_count
                metrics.feedback_avg_rating = round(avg_rating, 1)
            
            # AI Responses Shared
            shared_responses = await db.scalar(
                select(func.count(SharedResponse.id)).where(
                    SharedResponse.shared_by_user_id == user.id
                )
            )
            metrics.ai_responses_shared = shared_responses
            
            # Presence/Last Seen
            result = await db.execute(
                select(UserStatus).where(
                    UserStatus.user_id == user.id
                )
            )
            user_status = result.scalars().first()
            
            if user_status:
                metrics.is_online = user_status.is_online
                metrics.last_seen = user_status.last_seen.isoformat() if user_status.last_seen else None
            
            user_metrics.append({
                "user_id": user.id,
                "username": user.username,
                "email": user.email,
                "industry": user.industry,
                "role": user.role,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "metrics": {
                    "ai_query_count": metrics.ai_query_count,
                    "knowledge_docs_uploaded": metrics.knowledge_docs_uploaded,
                    "knowledge_docs_indexed": metrics.knowledge_docs_indexed,
                    "feedback_submitted": metrics.feedback_submitted,
                    "feedback_helpful_count": metrics.feedback_helpful_count,
                    "feedback_avg_rating": metrics.feedback_avg_rating,
                    "ai_responses_shared": metrics.ai_responses_shared,
                    "is_online": metrics.is_online,
                    "last_seen": metrics.last_seen
                }
            })
        
        return {
            "success": True,
            "total_users": len(user_metrics),
            "users": user_metrics
        }
        
    except Exception as e:
        logger.error(f"Error fetching user metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch user metrics: {str(e)}"
        )

@router.get("/me/summary", response_model=MeSummaryResponse)
async def get_me_summary(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    try:
        now = datetime.utcnow()
        week_ago = now - timedelta(days=7)
        month_ago = now - timedelta(days=30)

        chats_7d = await db.scalar(
            select(func.count(ExpertChatInteraction.id)).where(
                ExpertChatInteraction.user_id == current_user.id,
                ExpertChatInteraction.created_at >= week_ago
            )
        )

        chats_30d = await db.scalar(
            select(func.count(ExpertChatInteraction.id)).where(
                ExpertChatInteraction.user_id == current_user.id,
                ExpertChatInteraction.created_at >= month_ago
            )
        )

        notes_total = await db.scalar(
            select(func.count(Note.id)).where(Note.user_id == current_user.id)
        )

        feedback_total = await db.scalar(
            select(func.count(QueryFeedback.id)).where(QueryFeedback.user_id == current_user.id)
        )
        feedback_helpful = await db.scalar(
            select(func.count(QueryFeedback.id)).where(
                QueryFeedback.user_id == current_user.id,
                QueryFeedback.is_helpful == True
            )
        )

        result = await db.execute(
            select(UserStatus).where(UserStatus.user_id == current_user.id)
        )
        status_row = result.scalars().first()
        last_active_at = status_row.last_seen.isoformat() if status_row and status_row.last_seen else None

        return MeSummaryResponse(
            chats_7d=chats_7d,
            chats_30d=chats_30d,
            notes_total=notes_total,
            feedback_total=feedback_total,
            feedback_helpful=feedback_helpful,
            last_active_at=last_active_at
        )
    except Exception as e:
        logger.error(f"Error in /dashboard/me/summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to load summary")

@router.get("/me/metrics")
async def get_me_metrics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    try:
        # Reuse logic from get_user_metrics for the current user
        user_id = current_user.id
        metrics = UserMetrics()

        ai_query_count = await db.scalar(
            select(func.count(ExpertChatInteraction.id)).where(
                ExpertChatInteraction.user_id == user_id
            )
        )
        metrics.ai_query_count = ai_query_count

        result = await db.execute(
            select(KnowledgeDocument).where(
                KnowledgeDocument.created_by == user_id
            )
        )
        knowledge_docs = result.scalars().all()
        metrics.knowledge_docs_uploaded = len(knowledge_docs)
        metrics.knowledge_docs_indexed = len([doc for doc in knowledge_docs if doc.indexed])

        result = await db.execute(
            select(QueryFeedback).where(
                QueryFeedback.user_id == user_id
            )
        )
        feedback_entries = result.scalars().all()
        metrics.feedback_submitted = len(feedback_entries)
        if feedback_entries:
            helpful_count = len([f for f in feedback_entries if f.is_helpful])
            avg_rating = sum(f.rating for f in feedback_entries) / len(feedback_entries)
            metrics.feedback_helpful_count = helpful_count
            metrics.feedback_avg_rating = round(avg_rating, 1)

        shared_responses = await db.scalar(
            select(func.count(SharedResponse.id)).where(
                SharedResponse.shared_by_user_id == user_id
            )
        )
        metrics.ai_responses_shared = shared_responses

        result = await db.execute(
            select(UserStatus).where(UserStatus.user_id == user_id)
        )
        user_status = result.scalars().first()
        if user_status:
            metrics.is_online = user_status.is_online
            metrics.last_seen = user_status.last_seen.isoformat() if user_status.last_seen else None

        return {
            "success": True,
            "user": {
                "user_id": current_user.id,
                "username": current_user.username,
                "email": current_user.email,
                "industry": current_user.industry,
                "role": current_user.role,
                "created_at": current_user.created_at.isoformat() if current_user.created_at else None,
            },
            "metrics": {
                "ai_query_count": metrics.ai_query_count,
                "knowledge_docs_uploaded": metrics.knowledge_docs_uploaded,
                "knowledge_docs_indexed": metrics.knowledge_docs_indexed,
                "feedback_submitted": metrics.feedback_submitted,
                "feedback_helpful_count": metrics.feedback_helpful_count,
                "feedback_avg_rating": metrics.feedback_avg_rating,
                "ai_responses_shared": metrics.ai_responses_shared,
                "is_online": metrics.is_online,
                "last_seen": metrics.last_seen
            }
        }
    except Exception as e:
        logger.error(f"Error in /dashboard/me/metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to load metrics")

@router.get("/me/activity", response_model=ActivityFeedResponse)
async def get_me_activity(
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    try:
        # Collect events from interactions, notes, and feedback
        result = await db.execute(
            select(ExpertChatInteraction).where(
                ExpertChatInteraction.user_id == current_user.id
            ).order_by(ExpertChatInteraction.created_at.desc())
        )
        interactions = result.scalars().all()
        
        result = await db.execute(
            select(Note).where(Note.user_id == current_user.id).order_by(Note.created_at.desc())
        )
        notes = result.scalars().all()
        
        result = await db.execute(
            select(QueryFeedback).where(QueryFeedback.user_id == current_user.id).order_by(QueryFeedback.created_at.desc())
        )
        feedbacks = result.scalars().all()

        # Merge and sort
        merged: list[ActivityItem] = []
        for it in interactions:
            merged.append(ActivityItem(
                type="chat",
                id=it.id,
                created_at=it.created_at.isoformat() if it.created_at else datetime.utcnow().isoformat(),
                content_preview=(it.query[:140] + '...') if it.query and len(it.query) > 140 else it.query
            ))
        for n in notes:
            merged.append(ActivityItem(
                type="note",
                id=n.id,
                created_at=n.created_at.isoformat() if n.created_at else datetime.utcnow().isoformat(),
                title=n.title,
                action="created"
            ))
        for f in feedbacks:
            merged.append(ActivityItem(
                type="feedback",
                id=f.id,
                created_at=f.created_at.isoformat() if f.created_at else datetime.utcnow().isoformat(),
                rating=f.rating,
                is_helpful=f.is_helpful
            ))

        merged.sort(key=lambda x: x.created_at, reverse=True)

        sliced = merged[offset:offset + limit]
        next_offset = offset + limit if (offset + limit) < len(merged) else None

        return ActivityFeedResponse(items=sliced, next_offset=next_offset)
    except Exception as e:
        logger.error(f"Error in /dashboard/me/activity: {e}")
        raise HTTPException(status_code=500, detail="Failed to load activity")

@router.get("/user-metrics/{user_id}")
async def get_user_metrics(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get metrics for a specific user (Admin can view any user, users can view their own)
    """
    try:
        # Check permissions
        if not current_user.is_admin() and current_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only view your own metrics"
            )
        
        # Get target user
        result = await db.execute(
            select(User).where(
                User.id == user_id,
                User.is_active == True
            )
        )
        target_user = result.scalars().first()
        
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        metrics = UserMetrics()
        
        # AI Query Count
        ai_query_count = await db.scalar(
            select(func.count(ExpertChatInteraction.id)).where(
                ExpertChatInteraction.user_id == user_id
            )
        )
        metrics.ai_query_count = ai_query_count
        
        # Knowledge Documents Uploaded and Indexed
        result = await db.execute(
            select(KnowledgeDocument).where(
                KnowledgeDocument.created_by == user_id
            )
        )
        knowledge_docs = result.scalars().all()
        metrics.knowledge_docs_uploaded = len(knowledge_docs)
        metrics.knowledge_docs_indexed = len([doc for doc in knowledge_docs if doc.indexed])
        
        # Feedback Submitted
        result = await db.execute(
            select(QueryFeedback).where(
                QueryFeedback.user_id == user_id
            )
        )
        feedback_entries = result.scalars().all()
        metrics.feedback_submitted = len(feedback_entries)
        
        if feedback_entries:
            helpful_count = len([f for f in feedback_entries if f.is_helpful])
            avg_rating = sum(f.rating for f in feedback_entries) / len(feedback_entries)
            metrics.feedback_helpful_count = helpful_count
            metrics.feedback_avg_rating = round(avg_rating, 1)
        
        # AI Responses Shared
        shared_responses = await db.scalar(
            select(func.count(SharedResponse.id)).where(
                SharedResponse.shared_by_user_id == user_id
            )
        )
        metrics.ai_responses_shared = shared_responses
        
        # Presence/Last Seen
        result = await db.execute(
            select(UserStatus).where(
                UserStatus.user_id == user_id
            )
        )
        user_status = result.scalars().first()
        
        if user_status:
            metrics.is_online = user_status.is_online
            metrics.last_seen = user_status.last_seen.isoformat() if user_status.last_seen else None
        
        return {
            "success": True,
            "user": {
                "user_id": target_user.id,
                "username": target_user.username,
                "email": target_user.email,
                "industry": target_user.industry,
                "role": target_user.role,
                "created_at": target_user.created_at.isoformat() if target_user.created_at else None,
            },
            "metrics": {
                "ai_query_count": metrics.ai_query_count,
                "knowledge_docs_uploaded": metrics.knowledge_docs_uploaded,
                "knowledge_docs_indexed": metrics.knowledge_docs_indexed,
                "feedback_submitted": metrics.feedback_submitted,
                "feedback_helpful_count": metrics.feedback_helpful_count,
                "feedback_avg_rating": metrics.feedback_avg_rating,
                "ai_responses_shared": metrics.ai_responses_shared,
                "is_online": metrics.is_online,
                "last_seen": metrics.last_seen
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching user metrics for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch user metrics: {str(e)}"
        )

@router.get("/summary-stats")
@cached(
    prefix=f"{config.CACHE_PREFIX_ADMIN}:summary_stats",
    ttl=config.CACHE_TTL_ADMIN_DASHBOARD
)
async def get_dashboard_summary(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    Get summary statistics for the dashboard (Admin only)
    """
    try:
        # Total users by status
        total_users = await db.scalar(select(func.count(User.id)))
        active_users = await db.scalar(select(func.count(User.id)).where(User.is_active == True))
        pending_users = await db.scalar(select(func.count(User.id)).where(User.status == "pending"))
        approved_users = await db.scalar(select(func.count(User.id)).where(User.status == "approved"))
        rejected_users = await db.scalar(select(func.count(User.id)).where(User.status == "rejected"))
        
        # Total AI queries
        total_ai_queries = await db.scalar(select(func.count(ExpertChatInteraction.id)))
        
        # Total knowledge documents
        total_knowledge_docs = await db.scalar(select(func.count(KnowledgeDocument.id)))
        indexed_docs = await db.scalar(select(func.count(KnowledgeDocument.id)).where(KnowledgeDocument.indexed == True))
        
        # Total feedback
        total_feedback = await db.scalar(select(func.count(QueryFeedback.id)))
        helpful_feedback = await db.scalar(select(func.count(QueryFeedback.id)).where(QueryFeedback.is_helpful == True))
        
        # Total shared responses
        total_shared_responses = await db.scalar(select(func.count(SharedResponse.id)))
        
        # Online users
        online_users = await db.scalar(select(func.count(UserStatus.user_id)).where(UserStatus.is_online == True))
        
        # Recent activity (last 7 days)
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_queries = await db.scalar(
            select(func.count(ExpertChatInteraction.id)).where(
                ExpertChatInteraction.created_at >= week_ago
            )
        )
        
        recent_docs = await db.scalar(
            select(func.count(KnowledgeDocument.id)).where(
                KnowledgeDocument.created_at >= week_ago
            )
        )
        
        return {
            "success": True,
            "summary": {
                "users": {
                    "total": total_users,
                    "active": active_users,
                    "pending": pending_users,
                    "approved": approved_users,
                    "rejected": rejected_users,
                    "online": online_users
                },
                "ai_queries": {
                    "total": total_ai_queries,
                    "recent_week": recent_queries
                },
                "knowledge_docs": {
                    "total": total_knowledge_docs,
                    "indexed": indexed_docs,
                    "recent_week": recent_docs
                },
                "feedback": {
                    "total": total_feedback,
                    "helpful": helpful_feedback,
                    "helpful_percentage": round((helpful_feedback / total_feedback * 100) if total_feedback > 0 else 0, 1)
                },
                "shared_responses": {
                    "total": total_shared_responses
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching dashboard summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch dashboard summary: {str(e)}"
        )

@router.get("/activity-timeline")
@cached(
    prefix=f"{config.CACHE_PREFIX_ADMIN}:activity_timeline",
    ttl=config.CACHE_TTL_MEDIUM
)
async def get_activity_timeline(
    days: int = 30,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    Get activity timeline for the last N days (Admin only)
    """
    try:
        if days > 365:  # Limit to 1 year
            days = 365
            
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # AI queries over time
        result = await db.execute(
            select(
                func.date(ExpertChatInteraction.created_at).label('date'),
                func.count(ExpertChatInteraction.id).label('count')
            ).where(
                ExpertChatInteraction.created_at >= start_date
            ).group_by(
                func.date(ExpertChatInteraction.created_at)
            ).order_by('date')
        )
        ai_queries_timeline = result.all()
        
        # Knowledge docs over time
        result = await db.execute(
            select(
                func.date(KnowledgeDocument.created_at).label('date'),
                func.count(KnowledgeDocument.id).label('count')
            ).where(
                KnowledgeDocument.created_at >= start_date
            ).group_by(
                func.date(KnowledgeDocument.created_at)
            ).order_by('date')
        )
        docs_timeline = result.all()
        
        # User registrations over time
        result = await db.execute(
            select(
                func.date(User.created_at).label('date'),
                func.count(User.id).label('count')
            ).where(
                User.created_at >= start_date
            ).group_by(
                func.date(User.created_at)
            ).order_by('date')
        )
        users_timeline = result.all()
        
        return {
            "success": True,
            "timeline": {
                "ai_queries": [
                    {"date": str(item.date), "count": item.count} 
                    for item in ai_queries_timeline
                ],
                "knowledge_docs": [
                    {"date": str(item.date), "count": item.count} 
                    for item in docs_timeline
                ],
                "user_registrations": [
                    {"date": str(item.date), "count": item.count} 
                    for item in users_timeline
                ]
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching activity timeline: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch activity timeline: {str(e)}"
        )
