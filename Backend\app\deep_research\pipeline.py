"""
Enhanced Search Pipeline Module
Contains the main processing pipeline for orchestrating multi-agent research tasks.
"""

from typing import Dict, Any, List
from textwrap import dedent
from agno.team.team import Team
from agno.agent import Agent
import logging
import sys
import traceback
import gc
from .agents_config import (
    GeneralSearchAgent, ScientificAgent, NewsAgent, URLSearchAgent, 
    DeepSearchAgent, LegalComplianceAgent,
    VerificationAgent, AnalyticalAgent, CodeExecutionAgent, 
    SynthesisAgent, ResponseGenerator, gemini_flash
)

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
)
logger = logging.getLogger(__name__)

class EnhancedSearchPipeline:
    """
    Advanced AI Search Pipeline with query reformulation and multi-query processing capabilities
    that orchestrates multiple teams of agents based on router decisions.
    """
    
    def __init__(self, router, tool_hook=None):
        print("[DEBUG] Initializing EnhancedSearchPipeline...")
        self.router = router
        self.search_results = {}
        self.verified_info = {}
        self.analytics = {}
        self.synthesised_info = {}
        self.tool_hook = tool_hook
        self.capture_callback = None
        self.execution_details = []
        self.cancellation_event = None
        
        # Agent mapping
        self.agent_map = {
            "GeneralSearchAgent": GeneralSearchAgent,
            "ScientificAgent": ScientificAgent,
            "NewsAgent": NewsAgent,
            "URLSearchAgent": URLSearchAgent,
            "DeepSearchAgent": DeepSearchAgent,
            "LegalComplianceAgent": LegalComplianceAgent,
            "VerificationAgent": VerificationAgent,
            "AnalyticalAgent": AnalyticalAgent,
            "CodeExecutionAgent": CodeExecutionAgent,
            "SynthesisAgent": SynthesisAgent,
            "ResponseGenerator": ResponseGenerator
        }
        
        print(f"[DEBUG] Pipeline initialized with {len(self.agent_map)} available agents")
        logger.info("EnhancedSearchPipeline initialized successfully")
    
    def set_capture_callback(self, callback):
        """Set callback function to capture individual agent executions."""
        self.capture_callback = callback
    
    def _execute_with_capture(self, executor, query: str, executor_name: str = "Unknown"):
        """
        Execute an agent or team while capturing individual responses.
        
        Args:
            executor: Agent or Team object to execute
            query: Query to process
            executor_name: Name for logging purposes
            
        Returns:
            Execution result
        """
        print(f"[DEBUG] Executing {executor_name} with capture enabled")
        
        try:
            # 🚨 CANCELLATION CHECK BEFORE EXECUTION
            if self.cancellation_event and self.cancellation_event.is_set():
                print(f"[DEBUG] 🚨 EXECUTION CANCELLED: {executor_name}")
                from ..agents.base import QueryCancelledException
                raise QueryCancelledException()
            
            # For teams, we need to capture individual member responses
            if hasattr(executor, 'members') and executor.members:
                print(f"[DEBUG] {executor_name} is a team with {len(executor.members)} members")
                
                # Create a custom execution wrapper to capture individual agent responses
                original_run_methods = {}
                
                # Override each member's run method to capture their responses
                for member in executor.members:
                    if hasattr(member, 'run'):
                        # Store the original method
                        original_run_methods[member.name] = member.run
                        
                        # Create a capture wrapper with proper closure
                        def create_capture_wrapper(original_run_method, member_name):
                            def wrapper(*args, **kwargs):
                                print(f"[DEBUG] Capturing individual execution from team member: {member_name}")
                                try:
                                    member_response = original_run_method(*args, **kwargs)
                                    
                                    # Capture reasoning from individual team member
                                    if hasattr(member_response, 'reasoning_content') and member_response.reasoning_content:
                                        execution_detail = {
                                            'agent_name': f"{executor_name}::{member_name}",
                                            'query': args[0] if args else query,
                                            'response_type': type(member_response).__name__,
                                            'has_content': hasattr(member_response, 'content'),
                                            'has_reasoning': True,
                                            'reasoning': member_response.reasoning_content,
                                            'content': getattr(member_response, 'content', None)
                                        }
                                        self.execution_details.append(execution_detail)
                                        print(f"[DEBUG] ✓ Captured reasoning from team member {member_name}")
                                        
                                        # Also call the original capture callback if available
                                        if self.capture_callback:
                                            try:
                                                self.capture_callback(f"{executor_name}::{member_name}", member_response, args[0] if args else query)
                                            except Exception as e:
                                                print(f"[DEBUG] Error in capture callback for {member_name}: {e}")
                                    
                                    return member_response
                                    
                                except Exception as e:
                                    print(f"[DEBUG] Error capturing from team member {member_name}: {e}")
                                    # Store error details
                                    execution_detail = {
                                        'agent_name': f"{executor_name}::{member_name}",
                                        'query': args[0] if args else query,
                                        'error': str(e),
                                        'reasoning': f"Team member execution failed: {str(e)}"
                                    }
                                    self.execution_details.append(execution_detail)
                                    raise e
                            
                            return wrapper
                        
                        # Apply the wrapper with proper closure
                        member.run = create_capture_wrapper(member.run, member.name)
                
                # 🚨 CANCELLATION CHECK BEFORE TEAM EXECUTION
                if self.cancellation_event and self.cancellation_event.is_set():
                    print(f"[DEBUG] 🚨 TEAM EXECUTION CANCELLED: {executor_name}")
                    from ..agents.base import QueryCancelledException
                    raise QueryCancelledException()
                
                # Execute the team
                response = executor.run(query)
                
                # Restore original methods
                for member in executor.members:
                    if member.name in original_run_methods:
                        member.run = original_run_methods[member.name]
                
                # Capture team-level response as well
                if hasattr(response, 'reasoning_content') and response.reasoning_content:
                    execution_detail = {
                        'agent_name': executor_name,
                        'query': query,
                        'response_type': type(response).__name__,
                        'has_content': hasattr(response, 'content'),
                        'has_reasoning': True,
                        'reasoning': response.reasoning_content,
                        'content': getattr(response, 'content', None)
                    }
                    self.execution_details.append(execution_detail)
                    print(f"[DEBUG] ✓ Captured team-level reasoning from {executor_name}")
                
            else:
                # Single agent execution
                response = executor.run(query)
                
                # Capture execution details if callback is available
                if self.capture_callback:
                    try:
                        self.capture_callback(executor_name, response, query)
                    except Exception as e:
                        print(f"[DEBUG] Error in capture callback for {executor_name}: {e}")
                
                # Store execution details for summary
                execution_detail = {
                    'agent_name': executor_name,
                    'query': query,
                    'response_type': type(response).__name__,
                    'has_content': hasattr(response, 'content'),
                    'has_reasoning': hasattr(response, 'reasoning_content'),
                    'reasoning': getattr(response, 'reasoning_content', None),
                    'content': getattr(response, 'content', None)
                }
                self.execution_details.append(execution_detail)
            
            print(f"[DEBUG] Successfully executed {executor_name}")
            return response
            
        except Exception as e:
            error_msg = f"Error executing {executor_name}: {e}"
            print(f"[ERROR] {error_msg}")
            
            # Store error details
            execution_detail = {
                'agent_name': executor_name,
                'query': query,
                'error': str(e),
                'reasoning': f"Execution failed: {str(e)}"
            }
            self.execution_details.append(execution_detail)
            
            raise e
    
    def create_search_team(self, search_agents: List[str], query: str) -> Team:
        """Create a coordinated search team with specified agents."""
        print(f"[DEBUG] Creating search team with agents: {search_agents}")
        print(f"[DEBUG] Search team query: {query[:100]}...")
        
        try:
            # Create agent instances with tool hooks
            members = []
            for agent_name in search_agents:
                if agent_name in self.agent_map:
                    base_agent = self.agent_map[agent_name]
                    # Clone the agent with tool hooks if available
                    if self.tool_hook:
                        # Create a new agent instance with the same config but add tool hooks
                        agent_with_hooks = Agent(
                            name=base_agent.name,
                            model=base_agent.model,
                            description=base_agent.description,
                            tools=base_agent.tools,
                            instructions=base_agent.instructions,
                            debug_mode=True,
                            markdown=True,
                            tool_hooks=[self.tool_hook]
                        )
                        members.append(agent_with_hooks)
                    else:
                        members.append(base_agent)
            
            print(f"[DEBUG] Successfully created {len(members)} agents for search team")
            
            if not members:
                print("[WARNING] No valid agents found for search team")
                logger.warning("No valid agents found for search team")
                return None
            
            search_team = Team(
                model=gemini_flash,
                instructions=dedent(f"""
                    You are the coordinator of a search team tasked with gathering comprehensive information.
                    
                    Your responsibilities:
                    1. Analyze the query: "{query}"
                    2. Break it down into sub-tasks or sub-queries that can be handled by different agents
                    3. Assign appropriate sub-tasks to each team member based on their expertise
                    4. Ensure comprehensive coverage without redundancy
                    5. Coordinate the information gathering process
                    6. Compile all findings into a structured report
                    
                    Team members and their specialties:
                    {chr(10).join([f"- {agent.name}: {agent.description}" for agent in members])}
                    
                    Create a plan that leverages each agent's strengths effectively.
                """),
                members=members,
                debug_mode=True,
                tool_hooks=[self.tool_hook] if self.tool_hook else []
            )
            
            print(f"[DEBUG] ✓ Search team created successfully with {len(members)} members")
            return search_team
            
        except Exception as e:
            error_msg = f"Error creating search team: {e}"
            print(f"[ERROR] {error_msg}")
            logger.error(error_msg)
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
            return None
    
    def create_analysis_team(self, analysis_agents: List[str], query: str, search_agents: List[str] = None, context: Dict = None) -> Team:
        """Create an analysis team that includes search team as members when both search and analysis are needed."""
        print(f"[DEBUG] Creating analysis team with agents: {analysis_agents}")
        if search_agents:
            print(f"[DEBUG] Including search agents: {search_agents}")
        print(f"[DEBUG] Analysis team query: {query[:100]}...")
        
        try:
            # Create analysis agent instances with tool hooks
            members = []
            for agent_name in analysis_agents:
                if agent_name in self.agent_map:
                    base_agent = self.agent_map[agent_name]
                    # Clone the agent with tool hooks if available
                    if self.tool_hook:
                        agent_with_hooks = Agent(
                            name=base_agent.name,
                            model=base_agent.model,
                            description=base_agent.description,
                            tools=base_agent.tools,
                            instructions=base_agent.instructions,
                            debug_mode=True,
                            markdown=True,
                            tool_hooks=[self.tool_hook]
                        )
                        members.append(agent_with_hooks)
                    else:
                        members.append(base_agent)
            
            print(f"[DEBUG] Successfully created {len(members)} analysis agents")
            
            # If search agents are provided, add them as members of the analysis team
            if search_agents:
                for agent_name in search_agents:
                    if agent_name in self.agent_map:
                        base_agent = self.agent_map[agent_name]
                        # Clone the agent with tool hooks if available
                        if self.tool_hook:
                            agent_with_hooks = Agent(
                                name=base_agent.name,
                                model=base_agent.model,
                                description=base_agent.description,
                                tools=base_agent.tools,
                                instructions=base_agent.instructions,
                                debug_mode=True,
                                markdown=True,
                                tool_hooks=[self.tool_hook]
                            )
                            members.append(agent_with_hooks)
                        else:
                            members.append(base_agent)
                print(f"[DEBUG] Added {len([a for a in search_agents if a in self.agent_map])} search agents to analysis team")
                
                team_description = f"""
                ## ROLE
                You are the **Team Leader** of a dynamic research team composed of specialized search and analytical agents. Your mission is to orchestrate these agents to deliver a precise, well-sourced, and insight-driven response to the user’s query.

                ## TEAM MEMBERS                    
                        Analysis specialists:
                        {chr(10).join([f"- {self.agent_map[agent_name].name}: {self.agent_map[agent_name].description}" 
                                        for agent_name in analysis_agents if agent_name in self.agent_map])}
                        
                        Search specialists:
                        {chr(10).join([f"- {self.agent_map[agent_name].name}: {self.agent_map[agent_name].description}" 
                                        for agent_name in search_agents if agent_name in self.agent_map])}

                
                ## OBJECTIVE
                Produce a high-quality detailed context backed by relevant data and deep analysis. Search agents will locate and retrieve information related to domain; analysis agents are capable of strong reasoning, analysis, computation, and coding related tasks if needed.

                ## USER QUERY
                    "{query}"
                
                ## INSTRUCTIONS

                1. **Task Decomposition**
                - Analyze the user’s query and divide it into smaller sub-tasks.
                - Assign each task to the most appropriate agent(s), based on their capabilities.

                2. **Search Coordination**
                - Clearly instruct each search agent on how to approach their task:
                - Specify keywords, time filters, or target domains if applicable.
                - Indicate the type of information or evidence required.
                - Ensure all retrieved data is documented with proper metadata (URL, title, date, author if available).

                3. **Result Aggregation**
                - Organize all retrieved content into a structured summary that clearly maps each finding to its source and corresponding task.

                4. **Analysis Coordination**
                - Use the summarized data to assign specific reasoning, calculation, or synthesis tasks to the analysis agents.
                - If computations are required, specify the the details.
                - If logical or conceptual analysis is needed, define the framing or assumptions to apply.
                - Only request revisions when you detect a concrete gap , logical error or wrong or ambiguous answer.
                - Always check each agent’s `next_action`.
                        – If `"continue"` → send it back to the same agent with targeted
                        follow-up.
                        – If `"final_answer"` → pull it, combine with the other agent’s
                        work, and run a self-check for completeness & consistency.


                5. **Cross-Agent Integration**
                - Ensure all the sub tasks are completed.
                - Merge the outputs from both search and analysis agents into a unified narrative.
                - Resolve any contradictions, inconsistencies, or knowledge gaps through clarification or re-tasking.

                ## OUTPUT FORMAT : **STRICTLY MARKDOWN**
                """
            else:
                team_description = f"""
                ## ROLE
                You are **TeamLeader**, the orchestrator of a dynamic analysis team.

                ## TEAM MEMBERS
                {chr(10).join([f"- {agent.name}: {agent.description}" for agent in members])}

                ## OBJECTIVE
                Deliver a robust answer derived solely through computation, reasoning, and structured analysis. Your goal is precision, clarity, and evidence-based conclusions.

                ## USER QUERY
                    "{query}"

                ## 🔹 Primary Duties

                1. Parse the user’s query, clarify if needed, and decompose it into logical subtasks."""

                agent_names = [agent.name for agent in members]
                if "AnalyticalAgent" in agent_names and "CodeExecutionAgent" in agent_names:
                        team_description += """
                2. Dispatch subtasks to the appropriate agent(s):
                • Analytical reasoning → **AnalyticalAgent**
                • Coding / numeric computations → **CodeExecutionAgent**"""
                
                elif "AnalyticalAgent" in agent_names:
                        team_description += """
                2. Dispatch all subtasks to **AnalyticalAgent**, focusing on logical reasoning and high-level analysis."""
                
                elif "CodeExecutionAgent" in agent_names:
                        team_description += """
                2. Dispatch all subtasks to **CodeExecutionAgent**, focusing on coding, simulation, and computation."""

                team_description += """
                3. Review, merge, and iterate on agent outputs until the answer is complete, correct, and clear.
                4. Deliver a structured final response to the user.

                ## 🔹 Interaction Rules with Sub-Agents
                - Use the **same JSON envelope** they use: `title`, `content`, `next_action`, and optional `confidence` (1–10).
                - Always check each agent’s `next_action`:
                - If `"continue"` → send back to the same agent with precise follow-up.
                - If `"final_answer"` → integrate and validate their output.
                - Request clarifications only when a concrete issue is found (avoid unnecessary cycles).

                ## 🔹 Quality-Control Checklist (run silently before finalizing):
                ✔ All sub-questions resolved?  
                ✔ Code executed and results accurate?  
                ✔ Reasoning is consistent and justified?  
                ✔ Assumptions/caveats are stated clearly?  

                ## 🔹 **Confidence** - 1-10 score with brief reasoning

                ## 🔹 Final Output Format :  **STRICTLY MARKDOWN**
                """
            
            if not members:
                print("[WARNING] No valid agents found for analysis team")
                logger.warning("No valid agents found for analysis team")
                return None
            
            analysis_team = Team(
                model=gemini_flash,
                instructions=dedent(team_description),
                members=members,
                context=context,
                add_context=True if context else False,
                debug_mode=True,
                tool_hooks=[self.tool_hook] if self.tool_hook else []
            )
            
            print(f"[DEBUG] ✓ Analysis team created successfully with {len(members)} total members")
            return analysis_team
            
        except Exception as e:
            error_msg = f"Error creating analysis team: {e}"
            print(f"[ERROR] {error_msg}")
            logger.error(error_msg)
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
            return None
    
    def process_single_query(self, query: str, routing_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single query through the pipeline.
        
        Args:
            query: The reformulated query to process
            routing_result: The routing result for this query
            
        Returns:
            Dictionary with the query results
        """
        print(f"[DEBUG] ========== PROCESSING SINGLE QUERY ==========")
        print(f"[DEBUG] Query: {query}")
        
        analysis = routing_result['analysis']
        teams = routing_result['selected_teams']
        
        print(f"[DEBUG] Analysis requirements:")
        print(f"[DEBUG] - Complexity: {analysis['complexity']}")
        print(f"[DEBUG] - Requires Search: {analysis['requires_search']}")
        print(f"[DEBUG] - Requires Verification: {analysis['requires_verification']}")
        print(f"[DEBUG] - Requires Analysis: {analysis['requires_analysis']}")
        print(f"[DEBUG] Selected teams: {teams}")
        
        logger.info(f"Processing single query: {query}")
        query_results = {
            "query": query,
            "search_results": None,
            "verified_info": None,
            "analytics": None
        }
        
        try:
            # Step 2: Determine execution path based on analysis requirements
            # IMPORTANT: If analysis is true, always skip verification
            if analysis['requires_analysis']:
                # When analysis is required, we always use analysis-first approach
                print("[DEBUG] Analysis required - using integrated approach (skipping verification)")
                logger.info("Analysis required - using integrated approach (skipping verification)")
                
                if teams['analysis_agents']:
                    # If both search and analysis are needed, create integrated team
                    if analysis['requires_search'] and teams['search_agents']:
                        print(f"[DEBUG] Creating integrated analysis team with search agents: {teams['search_agents']}")
                        logger.info(f"Creating integrated analysis team with search agents: {teams['search_agents']}")
                        
                        analysis_team = self.create_analysis_team(
                            teams['analysis_agents'], 
                            query,
                            search_agents=teams['search_agents']  # Pass search agents to be included as members
                        )
                        
                        if analysis_team:
                            print("[DEBUG] Running integrated analysis team...")
                            try:
                                response = self._execute_with_capture(
                                    analysis_team, 
                                    query, 
                                    f"Integrated Analysis Team ({', '.join(teams['analysis_agents'])} + {', '.join(teams['search_agents'])})"
                                )
                                query_results["analytics"] = response.content
                                print("[DEBUG] ✓ Integrated analysis completed successfully")
                            except Exception as e:
                                error_msg = f"Error running integrated analysis team: {e}"
                                print(f"[ERROR] {error_msg}")
                                logger.error(error_msg)
                                print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                                query_results["analytics"] = f"Error in analysis: {error_msg}"
                        else:
                            print("[ERROR] Failed to create integrated analysis team")
                            query_results["analytics"] = "Failed to create analysis team"
                        
                    else:
                        # Analysis only, no search needed
                        print("[DEBUG] Creating analysis-only team")
                        logger.info("Creating analysis-only team")
                        
                        analysis_team = self.create_analysis_team(
                            teams['analysis_agents'], 
                            query
                        )
                        
                        if analysis_team:
                            print("[DEBUG] Running analysis-only team...")
                            try:
                                response = self._execute_with_capture(
                                    analysis_team, 
                                    query, 
                                    f"Analysis Team ({', '.join(teams['analysis_agents'])})"
                                )
                                query_results["analytics"] = response.content
                                print("[DEBUG] ✓ Analysis-only completed successfully")
                            except Exception as e:
                                error_msg = f"Error running analysis-only team: {e}"
                                print(f"[ERROR] {error_msg}")
                                logger.error(error_msg)
                                print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                                query_results["analytics"] = f"Error in analysis: {error_msg}"
                        else:
                            print("[ERROR] Failed to create analysis-only team")
                            query_results["analytics"] = "Failed to create analysis team"
                else:
                    print("[WARNING] Analysis required but no analysis agents selected")
                    logger.warning("Analysis required but no analysis agents selected")
                    
            else:
                # No analysis required - use traditional search approach
                print("[DEBUG] No analysis required - using traditional search approach")
                logger.info("No analysis required - using traditional search approach")
                
                # Step 3a: Information Gathering (if needed)
                if analysis['requires_search'] and teams['search_agents']:
                    print(f"[DEBUG] Running search team with agents: {teams['search_agents']}")
                    logger.info(f"Running search team with agents: {teams['search_agents']}")
                    
                    search_team = self.create_search_team(teams['search_agents'], query)
                    if search_team:
                        print("[DEBUG] Executing search team...")
                        try:
                            response = self._execute_with_capture(
                                search_team, 
                                query, 
                                f"Search Team ({', '.join(teams['search_agents'])})"
                            )
                            query_results["search_results"] = response.content
                            print("[DEBUG] ✓ Search completed successfully")
                            logger.info("Search completed")
                        except Exception as e:
                            error_msg = f"Error running search team: {e}"
                            print(f"[ERROR] {error_msg}")
                            logger.error(error_msg)
                            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                            query_results["search_results"] = f"Error in search: {error_msg}"
                    else:
                        print("[ERROR] Failed to create search team")
                        query_results["search_results"] = "Failed to create search team"
                
                # Step 3b: Verification (if needed and analysis is not required)
                # Note: Verification is skipped when analysis is true
                if analysis['requires_verification'] and teams.get('verification_agents') and not analysis['requires_analysis']:
                    print("[DEBUG] Running verification")
                    logger.info("Running verification")
                    
                    try:
                        # Update VerificationAgent context with search results
                        VerificationAgent.context = query_results["search_results"]
                        verification_query = f"Verify the following information for the query: {query}\n\nInformation to verify:\n{query_results['search_results']}"
                        
                        verification_result = self._execute_with_capture(
                            VerificationAgent,
                            verification_query,
                            "Verification Agent"
                        )
                        query_results["verified_info"] = verification_result.content
                        print("[DEBUG] ✓ Verification completed successfully")
                        logger.info("Verification completed")
                    except Exception as e:
                        error_msg = f"Error running verification: {e}"
                        print(f"[ERROR] {error_msg}")
                        logger.error(error_msg)
                        print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                        query_results["verified_info"] = f"Error in verification: {error_msg}"
        
        except Exception as e:
            error_msg = f"Critical error in process_single_query: {e}"
            print(f"[ERROR] {error_msg}")
            logger.error(error_msg)
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
            
            # Add error information to results
            query_results["error"] = error_msg
        
        print(f"[DEBUG] ========== SINGLE QUERY PROCESSING COMPLETED ==========")
        print(f"[DEBUG] Results summary:")
        print(f"[DEBUG] - Search results: {'✓' if query_results['search_results'] else '✗'}")
        print(f"[DEBUG] - Verified info: {'✓' if query_results['verified_info'] else '✗'}")
        print(f"[DEBUG] - Analytics: {'✓' if query_results['analytics'] else '✗'}")
        
        return query_results
    
    def run_pipeline(self, query: str, cancellation_event=None) -> Dict[str, Any]:
        """
        Execute the complete search pipeline based on router decisions.
        
        Pipeline Logic:
        1. Reformulate query and potentially divide into sub-queries
        2. For each query:
           - Route to appropriate agents
           - Execute agents
        3. If multiple queries, synthesize results from all queries
        4. Generate final response
        
        Args:
            query: User query to process
            cancellation_event: Event to check for cancellation
            
        Returns:
            Final response with all processing results
        """
        print(f"[DEBUG] ========== STARTING PIPELINE EXECUTION ==========")
        print(f"[DEBUG] Original query: {query}")
        
        # Store cancellation event
        self.cancellation_event = cancellation_event
        
        # 🚨 IMMEDIATE CANCELLATION CHECK
        if self.cancellation_event and self.cancellation_event.is_set():
            print("[DEBUG] 🚨 PIPELINE CANCELLED BEFORE START")
            from ..agents.base import QueryCancelledException
            raise QueryCancelledException()
        
        # Clear previous execution details
        self.execution_details = []
        
        try:
            # 🔬 PIPELINE CANCELLATION CHECK 1
            if self.cancellation_event and self.cancellation_event.is_set():
                print("[DEBUG] 🔬 PIPELINE CANCELLED: Before routing")
                logger.info("🔬 Pipeline: Query cancelled before routing")
                from ..agents.base import QueryCancelledException
                raise QueryCancelledException()
            
            # Step 1: Route the query (including reformulation and potential sub-queries)
            print("[DEBUG] Step 1: Routing query...")
            logger.info("🔬 Pipeline: Starting query routing")
            routing_result = self.router.route_query(query)
            logger.info("🔬 Pipeline: Query routing completed")
            print(f"[DEBUG] Routing completed. Multiple queries: {routing_result.get('multiple_queries', False)}")
            logger.info(f"Pipeline started for query: {query}")
            
            # 🔬 PIPELINE CANCELLATION CHECK 2
            if self.cancellation_event and self.cancellation_event.is_set():
                print("[DEBUG] 🔬 PIPELINE CANCELLED: After routing")
                logger.info("🔬 Pipeline: Query cancelled after routing")
                from ..agents.base import QueryCancelledException
                raise QueryCancelledException()
            
            # Check if we have multiple queries
            if routing_result.get("multiple_queries", False):
                print(f"[DEBUG] Processing multiple queries: {routing_result.get('queries_count', 0)}")
                logger.info(f"🔬 Pipeline: Processing multiple queries: {routing_result.get('queries_count', 0)}")
                
                # Process each sub-query
                sub_query_results = []
                routing_results = routing_result.get("routing_results", [])
                
                for i, sub_routing in enumerate(routing_results):
                    # 🔬 SUB-QUERY CANCELLATION CHECK
                    if self.cancellation_event and self.cancellation_event.is_set():
                        print(f"[DEBUG] 🔬 SUB-QUERY CANCELLED: Before processing sub-query {i+1}")
                        logger.info(f"🔬 Pipeline: Sub-query {i+1} cancelled before processing - STOPPING ALL QUERIES")
                        from ..agents.base import QueryCancelledException
                        raise QueryCancelledException()
                    
                    sub_query = sub_routing.get("query", "")
                    print(f"[DEBUG] Processing sub-query {i+1}/{len(routing_results)}: {sub_query}")
                    logger.info(f"🔬 Pipeline: Starting sub-query {i+1}: {sub_query[:100]}...")
           
                    try:
                        # Process each sub-query
                        result = self.process_single_query(sub_query, sub_routing)
                        sub_query_results.append(result)
                        print(f"[DEBUG] ✓ Sub-query {i+1} processed successfully")
                        logger.info(f"🔬 Pipeline: Sub-query {i+1} completed successfully")
                        
                        # 🔬 SUB-QUERY CANCELLATION CHECK AFTER PROCESSING
                        if self.cancellation_event and self.cancellation_event.is_set():
                            print(f"[DEBUG] 🔬 SUB-QUERY CANCELLED: After processing sub-query {i+1}")
                            logger.info(f"🔬 Pipeline: Sub-query {i+1} cancelled after processing - STOPPING ALL QUERIES")
                            from ..agents.base import QueryCancelledException
                            raise QueryCancelledException()
                            
                    except Exception as e:
                        if hasattr(e, 'is_cancelled') and e.is_cancelled:
                            logger.info(f"🔬 Pipeline: Sub-query {i+1} was cancelled - STOPPING ENTIRE PIPELINE")
                            raise e  # Re-raise to stop the entire pipeline
                        else:
                            error_msg = f"Error processing sub-query {i+1}: {e}"
                            print(f"[ERROR] {error_msg}")
                            logger.error(f"🔬 Pipeline: Error in sub-query {i+1}: {error_msg}")
                            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                            # Add error result
                            sub_query_results.append({
                                "query": sub_query,
                                "error": error_msg,
                                "search_results": None,
                                "verified_info": None,
                                "analytics": None
                            })
                
                # After processing all sub-queries, we need to synthesize the results
                print("[DEBUG] Synthesizing results from multiple queries...")
                logger.info("Synthesizing results from multiple queries")
                
                try:
                    # Update SynthesisAgent context and prompt for multi-query synthesis
                    SynthesisAgent.context = {"sub queries with results": sub_query_results}
                    
                    synthesis_prompt = f"""
                    Synthesize the results from multiple sub-queries for the original query: {query}
                    
                    This complex query was broken down into {len(sub_query_results)} sub-queries:
                    {chr(10).join([f"- {result.get('query', 'Unknown query')}" for result in sub_query_results])}
                    
                    Create a comprehensive synthesis that:
                    1. Addresses the original query fully by integrating information from all sub-queries
                    2. Resolves any contradictions or inconsistencies between sub-query results
                    3. Presents a unified, coherent response that follows a logical structure
                    4. Identifies connections and relationships between the different aspects of the query
                    5. Ensures all important information from each sub-query is included
                    
                    Your synthesis should read as a cohesive whole, not as separate answers to separate questions.
                    """
                    
                    print("[DEBUG] Running synthesis agent...")
                    synthesis_response = self._execute_with_capture(
                        SynthesisAgent,
                        synthesis_prompt,
                        "Multi-query Synthesis Agent"
                    )
                    self.synthesised_info = synthesis_response.content
                    print("[DEBUG] ✓ Multi-query synthesis completed")
                    logger.info("Multi-query synthesis completed")
                    
                except Exception as e:
                    error_msg = f"Error in synthesis: {e}"
                    print(f"[ERROR] {error_msg}")
                    logger.error(error_msg)
                    print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                    self.synthesised_info = f"Error in synthesis: {error_msg}"
                
                # Generate final response using the synthesized information
                print("[DEBUG] Generating final response for multiple queries...")
                try:
                    response_context = {
                        'sub_queries': [result.get("query", "") for result in sub_query_results],
                        'synthesised_info': self.synthesised_info
                    }
                    
                    # Update ResponseGenerator context
                    ResponseGenerator.context = response_context
                    
                    response_prompt = f"""
                    Generate a comprehensive response for the complex user query: {query}
                    
                    This query was broken down into {len(sub_query_results)} sub-queries, and the results have been synthesized.
                    
                    Use the synthesized information to create a clear, helpful, and actionable response that fully addresses
                    the user's original question.
                    
                    Ensure that your response:
                    1. Directly answers the original query
                    2. Integrates information from all sub-queries seamlessly
                    3. Has a logical structure and flow
                    4. Is comprehensive yet concise
                    5. Includes any important caveats or limitations
                    """
                    
                    final_response = self._execute_with_capture(
                        ResponseGenerator,
                        response_prompt,
                        "Multi-query Response Generator"
                    )
                    print("[DEBUG] ✓ Final response for multiple queries generated")
                    logger.info("Final response for multiple queries generated")
                    
                except Exception as e:
                    error_msg = f"Error generating final response: {e}"
                    print(f"[ERROR] {error_msg}")
                    logger.error(error_msg)
                    print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                    raise e
                
            else:
                # Single query processing
                print("[DEBUG] Processing single query")
                logger.info("Processing single query")
                query = routing_result.get("query", query)
                
                # Process the single query
                print("[DEBUG] Processing single query through pipeline...")
                query_result = self.process_single_query(query, routing_result)
                
                # Extract the results
                self.search_results = query_result.get("search_results")
                self.verified_info = query_result.get("verified_info")
                self.analytics = query_result.get("analytics")
                
                print(f"[DEBUG] Single query results extracted:")
                print(f"[DEBUG] - Search results: {'✓' if self.search_results else '✗'}")
                print(f"[DEBUG] - Verified info: {'✓' if self.verified_info else '✗'}")
                print(f"[DEBUG] - Analytics: {'✓' if self.analytics else '✗'}")
                
                # Step 4: Synthesis (if we have any results to synthesize)
                results_count = len([x for x in [self.search_results, self.verified_info, self.analytics] if x])
                if results_count >= 2:
                    print(f"[DEBUG] Running synthesis for single query ({results_count} result types)")
                    logger.info("Running synthesis for single query")
                    
                    try:
                        # Prepare synthesis context
                        synthesis_context = {
                            'search_results': self.search_results,
                            'verified_info': self.verified_info,
                            'analytics': self.analytics
                        }
                        
                        # Update SynthesisAgent context
                        SynthesisAgent.context = synthesis_context
                        synthesis_prompt = f"""
                        Synthesize all the gathered information for the query: {query}
                        
                        Available information:
                        - Search Results: {'Yes' if self.search_results else 'No'}
                        - Verified Information: {'Yes' if self.verified_info else 'No'}
                        - Analytics: {'Yes' if self.analytics else 'No'}
                        
                        Create a coherent, comprehensive synthesis that addresses all aspects of the query.
                        """
                        
                        print("[DEBUG] Running synthesis for single query...")
                        synthesis_response = self._execute_with_capture(
                            SynthesisAgent,
                            synthesis_prompt,
                            "Single-query Synthesis Agent"
                        )
                        self.synthesised_info = synthesis_response.content
                        print("[DEBUG] ✓ Synthesis completed")
                        logger.info("Synthesis completed")
                        
                    except Exception as e:
                        error_msg = f"Error in synthesis: {e}"
                        print(f"[ERROR] {error_msg}")
                        logger.error(error_msg)
                        print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                        self.synthesised_info = f"Error in synthesis: {error_msg}"
                else:
                    print(f"[DEBUG] Skipping synthesis - only {results_count} result type(s) available")
                
                # Step 5: Generate Final Response
                print("[DEBUG] Generating final response for single query...")
                logger.info("Generating final response for single query")
                
                try:
                    # Prepare response context
                    response_context = {
                        'synthesised_info': self.synthesised_info if self.synthesised_info else self.analytics or self.search_results
                    }
                    
                    # Update ResponseGenerator context
                    ResponseGenerator.context = response_context
                    
                    response_prompt = f"""
                    Generate a comprehensive response for the user query: {query}
                    
                    Based on the analysis, this query:
                    - Complexity: {routing_result.get('analysis', {}).get('complexity', 'unknown')}
                    - Domain focus: {', '.join(routing_result.get('analysis', {}).get('domain_focus', [])) if routing_result.get('analysis', {}).get('domain_focus') else 'general'}
                    - Required search: {routing_result.get('analysis', {}).get('requires_search', False)}
                    - Required verification: {routing_result.get('analysis', {}).get('requires_verification', False)}
                    - Required analysis: {routing_result.get('analysis', {}).get('requires_analysis', False)}
                    
                    Use the synthesized information to create a clear, helpful, and actionable response.
                    """
                    
                    final_response = self._execute_with_capture(
                        ResponseGenerator,
                        response_prompt,
                        "Single-query Response Generator"
                    )
                    print("[DEBUG] ✓ Final response for single query generated")
                    logger.info("Pipeline completed successfully")
                    
                except Exception as e:
                    error_msg = f"Error generating final response: {e}"
                    print(f"[ERROR] {error_msg}")
                    logger.error(error_msg)
                    print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                    raise e
            
            print(f"[DEBUG] ========== PIPELINE EXECUTION COMPLETED ==========")
            return final_response
            
        except Exception as e:
            if hasattr(e, 'is_cancelled') and e.is_cancelled:
                logger.info("🔬 Pipeline: Pipeline execution cancelled by user (expected behavior)")
                return {"content": "Query was cancelled by user."}
            else:
                error_msg = f"Critical error in pipeline execution: {e}"
                print(f"[ERROR] {error_msg}")
                logger.error(error_msg)
                print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                
                raise e
    
    def get_pipeline_summary(self) -> Dict[str, Any]:
        """Get a summary of what was processed in the pipeline."""
        return {
            'has_search_results': bool(self.search_results),
            'has_verified_info': bool(self.verified_info),
            'has_analytics': bool(self.analytics),
            'has_synthesis': bool(self.synthesised_info),
            'search_results_preview': str(self.search_results)[:200] + '...' if self.search_results else None,
            'analytics_preview': str(self.analytics)[:200] + '...' if self.analytics else None,
            'synthesis_preview': str(self.synthesised_info)[:200] + '...' if self.synthesised_info else None,
            'execution_details': self.execution_details,
            'agents_executed': len(self.execution_details),
            'agents_with_reasoning': len([detail for detail in self.execution_details if detail.get('reasoning')])
        } 