import React from 'react';

interface ContextUsageIndicatorProps {
  percentUsed: number;
  usedTokens: number;
  remainingTokens: number;
  inputBudget: number;
}

const ContextUsageIndicator: React.FC<ContextUsageIndicatorProps> = ({
  percentUsed,
  usedTokens,
  remainingTokens,
  inputBudget
}) => {
  // Clamp percent between 0-100
  const clampedPercent = Math.min(100, Math.max(0, percentUsed));
  
  // Calculate circle properties
  const radius = 10;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (clampedPercent / 100) * circumference;
  
  // Determine color based on usage aligned to app theme (blue primary)
  // <70% -> primary blue, 70-90% -> amber caution, >90% -> red danger
  const getColor = () => {
    if (clampedPercent < 70) return '#3b82f6'; // tailwind blue-500 (lighter)
    if (clampedPercent < 90) return '#fbbf24'; // amber-400 (lighter)
    return '#ef4444'; // red-500 (slightly lighter)
  };

  return (
    <div 
      className="flex items-center gap-2 group relative"
      role="status"
      aria-label={`Context usage ${clampedPercent.toFixed(1)} percent`}
    >
      {/* Circular Progress Ring */}
      <div className="relative flex items-center justify-center">
        <svg 
          width="32" 
          height="32" 
          className="transform -rotate-90"
        >
          {/* Background track */}
          <circle
            cx="16"
            cy="16"
            r={radius}
            fill="none"
            stroke="currentColor"
            strokeWidth="3"
            className="text-gray-200 dark:text-gray-600"
          />
          {/* Progress arc */}
          <circle
            cx="16"
            cy="16"
            r={radius}
            fill="none"
            stroke={getColor()}
            strokeWidth="3"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-300 ease-in-out"
          />
          {/* Inline percentage for improved visibility */}
          <g className="rotate-90">
            <text
              x="16"
              y="16"
              textAnchor="middle"
              dominantBaseline="central"
              className="fill-slate-700 dark:fill-slate-200"
              fontSize="9"
              fontWeight="600"
            >
              {clampedPercent.toFixed(1)}%
            </text>
          </g>
        </svg>
      </div>

      {/* Percentage Text (adjacent label for larger screens) */}
      <span 
        className="hidden sm:inline-block text-sm font-semibold tracking-tight text-gray-900 dark:text-gray-600 tabular-nums"
        style={{ minWidth: '48px' }}
      >
        {clampedPercent.toFixed(1)}%
      </span>

      {/* Tooltip */}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
        <div className="flex flex-col gap-1">
          <div>Context used: {usedTokens.toLocaleString()}/{inputBudget.toLocaleString()} tokens</div>
          <div>Remaining: {remainingTokens.toLocaleString()} tokens</div>
        </div>
        {/* Tooltip arrow */}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 -mt-px">
          <div className="border-4 border-transparent border-t-gray-900 dark:border-t-gray-800"></div>
        </div>
      </div>
    </div>
  );
};

export default ContextUsageIndicator;
