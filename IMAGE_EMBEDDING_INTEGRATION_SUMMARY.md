# Image Embedding Integration Summary

This document summarizes all the changes made to integrate SigLIP-2 image embedding functionality into the codebase.

## Overview

The image embedding feature adds automatic image extraction, embedding generation, and retrieval capabilities to the multimodal RAG pipeline. Images are extracted from documents (PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX), embedded using the SigLIP-2 model, and stored in Qdrant for cross-modal text-to-image retrieval.

## Files Added/Modified

### 1. Data_Ingestion_Service/image_embedding_client.py (NEW)
**Purpose**: HTTP client for communicating with the SigLIP-2 image embedding service during document ingestion.

**Key Features**:
- `embed_images()`: Generate 1152-dimensional embeddings for images
- `caption_images()`: Generate captions for images using SigLIP-2
- `embed_text()`: Generate text embeddings for cross-modal retrieval
- `health_check()`: Verify service availability
- Retry logic and timeout handling

**Source**: Moved from root `image_embedding_client_ingestion.py`

### 2. Data_Retriever_Service/image_embedding_client.py (NEW)
**Purpose**: Lightweight HTTP client for the SigLIP-2 service during retrieval (text embedding only).

**Key Features**:
- `embed_text()`: Generate text embeddings for image-text retrieval queries
- `health_check()`: Verify service availability
- Graceful failure handling (retriever continues without image search if unavailable)

**Source**: Moved from root `image_embedding_client.py`

### 3. Data_Retriever_Service/image_retrieval.py (NEW)
**Purpose**: Complete image retrieval system using SigLIP-2 embeddings.

**Key Features**:
- `retrieve_images_by_text()`: Search images using text queries with CLIP embeddings
- `retrieve_images_with_base64()`: Retrieve images with base64 data from PostgreSQL
- `fetch_image_from_pg()`: Fetch raw image data from PostgreSQL raw_documents table
- Named vector support for Qdrant ('image' vector)

**Source**: Moved from root `image_retrieval.py`

### 4. Data_Ingestion_Service/pipeline.py (MODIFIED)
**Changes Made**:
- Added import and initialization of `ImageEmbeddingClient` (line 212-215)
- Image embedding client connected to SigLIP-2 service at `http://**************:8019`
- Existing image processing methods (`extract_images_with_pymupdf`, `create_image_documents`, `add_image_documents_to_qdrant`) already integrated
- Qdrant collection setup includes 'image' vector (1152-dim, COSINE distance)

**Integration Point**: `__init__` method in `MultimodalDataPipeline` class

### 5. Data_Retriever_Service/config.py (MODIFIED)
**Changes Made**:
- Added `image_embedding_service_url` parameter to `RetrieverConfig` dataclass
- Default value: `"http://**************:8019"`
- Updated docstring to include new parameter

**Line**: 39

### 6. Data_Retriever_Service/agent_pipeline.py (MODIFIED)
**Changes Made**:
- Added import and initialization of `ImageRetriever` in `_setup_components()` method (lines 127-138)
- Graceful failure handling: if ImageRetriever initialization fails, agent continues without image search
- ImageRetriever receives Qdrant client, database URL, and service URL from config

**Integration Point**: `_setup_components()` method in `IntelligentRAGAgent` class

### 7. Backend/app/utils/image_processing.py (MODIFIED)
**Changes Made**:
- Added Windows-specific python-magic DLL path fix (lines 19-35)
- Added `MAGIC_AVAILABLE` flag for graceful fallback if python-magic unavailable
- Enhanced error handling and logging
- Improved HEIC/HEIF support

**Purpose**: Better cross-platform compatibility for image validation and processing

## Architecture

### Document Ingestion Flow
```
1. Document uploaded → PyMuPDF extracts images with metadata
2. Images sent to SigLIP-2 service → Generate embeddings (1152-dim) + captions
3. Image documents created with:
   - Vector: SigLIP-2 embedding in 'image' named vector
   - Payload: metadata (page, bbox, dimensions, caption, etc.)
4. Stored in:
   - Qdrant: embeddings + metadata
   - PostgreSQL: raw base64 image data + captions
```

### Image Retrieval Flow
```
1. User query (text) → SigLIP-2 service embeds text (1152-dim)
2. Search Qdrant using 'image' named vector
3. Filter: type='image'
4. Return: Top-k images with scores and metadata
5. Optional: Fetch base64 data from PostgreSQL for display
```

### Qdrant Collection Schema
```
Collection: {collection_name}
Vectors:
  - "dense": 768-dim (all-mpnet-base-v2 for text)
  - "sparse": Sparse vectors for text
  - "image": 1152-dim (SigLIP-2 for images)
Distance: DOT for text, COSINE for images
```

### PostgreSQL Storage
```
Table: raw_documents
Columns:
  - id: UUID
  - space_id: Integer (FK)
  - type: 'text' | 'image'
  - content: Text (caption for images, chunk for text)
  - base64: Base64 encoded image data (NULL for text)
  - source: Source file name
```

## Configuration

### Environment Variables (if using .env)
```
IMAGE_EMBEDDING_SERVICE_URL=http://**************:8019
```

### Service URLs
- **Ingestion Service**: Uses hardcoded URL in `pipeline.py` (line 214)
- **Retriever Service**: Configurable via `RetrieverConfig.image_embedding_service_url`

## Backward Compatibility

All changes maintain backward compatibility:
- Existing text-only retrieval continues to work
- Image processing is optional and gracefully fails if service unavailable
- No breaking changes to existing APIs or data structures

## Key Features

1. **Automatic Image Extraction**: PyMuPDF extracts images with metadata (page, bbox, dimensions)
2. **SigLIP-2 Integration**: State-of-the-art CLIP model for image-text embeddings
3. **Cross-Modal Retrieval**: Search images using natural language text queries
4. **Dual Storage**: Qdrant for fast vector search, PostgreSQL for raw data
5. **Graceful Degradation**: System continues working if image service unavailable
6. **Metadata Preservation**: Page numbers, bounding boxes, dimensions stored
7. **Caption Generation**: Automatic caption generation for all extracted images

## Dependencies

New dependencies to add to requirements.txt (if not already present):
```
requests
pillow
python-magic
pillow-heif
```

## Testing

To verify the integration:

1. **Test Image Embedding Client (Ingestion)**:
```python
from Data_Ingestion_Service.image_embedding_client import ImageEmbeddingClient

client = ImageEmbeddingClient()
result = client.health_check()
print(result)
```

2. **Test Image Retrieval**:
```python
from Data_Retriever_Service.image_retrieval import ImageRetriever

retriever = ImageRetriever(
    image_embedding_service_url="http://**************:8019",
    qdrant_client=qdrant_client,
    database_url=database_url
)
images = retriever.retrieve_images_by_text("show me charts", "collection_name", top_k=5)
```

3. **Test Full Pipeline**:
Upload a PDF with images and verify:
- Images are extracted
- Embeddings are generated
- Images are searchable by text
- Base64 data is retrievable

## Next Steps

1. Add image_embedding_service_url to environment variables
2. Deploy SigLIP-2 service (if not already running)
3. Test image extraction and retrieval with sample documents
4. Monitor service health and performance
5. Consider adding image reranking for improved results

## Notes

- SigLIP-2 service must be running at the configured URL
- Image dimension: 1152 (SigLIP-2 embedding size)
- Text dimension for cross-modal search: 1152 (same model)
- Images smaller than 100x100 pixels are filtered out
- Only static images are supported (animated images rejected)

## Removed Files

The following files were removed from the root directory after being moved to their correct locations:
- `image_embedding_client_ingestion.py` → `Data_Ingestion_Service/image_embedding_client.py`
- `image_embedding_client.py` → `Data_Retriever_Service/image_embedding_client.py`
- `image_retrieval.py` → `Data_Retriever_Service/image_retrieval.py`
- `image_processing.py` (updated version in `Backend/app/utils/`)
- `pipeline.py` (updated version in `Data_Ingestion_Service/`)

