'use client';

import React, { useState, useEffect } from 'react';
import { API_BASE_URL } from '@/lib/api';
import UserMetricsCard from './UserMetricsCard';
import UserMetricsModal from './UserMetricsModal';
import UserDeletionModal from '../admin/UserDeletionModal';
import AddUserModal from '../admin/AddUserModal';

interface UserMetrics {
  ai_query_count: number;
  knowledge_docs_uploaded: number;
  knowledge_docs_indexed: number;
  feedback_submitted: number;
  feedback_helpful_count: number;
  feedback_avg_rating: number;
  ai_responses_shared: number;
  is_online: boolean;
  last_seen: string | null;
}

interface UserWithMetrics {
  user_id: number;
  username: string;
  email: string;
  industry: string | null;
  role: string;
  created_at: string | null;
  metrics: UserMetrics;
}

interface UserMetricsListProps {
  refreshTrigger?: number;
}

export default function UserMetricsList({ refreshTrigger }: UserMetricsListProps) {
  const [users, setUsers] = useState<UserWithMetrics[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'username' | 'ai_queries' | 'docs' | 'last_seen'>('username');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedUser, setSelectedUser] = useState<UserWithMetrics | null>(null);
  const [currentUser, setCurrentUser] = useState<{ id: number; role: string } | null>(null);
  const [deletionModal, setDeletionModal] = useState<{ isOpen: boolean; userId: number; username: string }>({
    isOpen: false,
    userId: 0,
    username: ''
  });
  const [addUserModal, setAddUserModal] = useState(false);

  const fetchUserMetrics = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/dashboard/user-metrics`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user metrics');
      }

      const data = await response.json();
      setUsers(data.users || []);
      setError('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserMetrics();
  }, [refreshTrigger]);

  // Load current user once for permission checks
  useEffect(() => {
    try {
      const raw = localStorage.getItem('user');
      if (raw) {
        const parsed = JSON.parse(raw);
        if (parsed && typeof parsed.id === 'number') {
          setCurrentUser({ id: parsed.id, role: parsed.role });
        }
      }
    } catch {}
  }, []);

  // Listen for cross-view refresh triggers (from AccountRequestsPanel or elsewhere)
  useEffect(() => {
    const onRefresh = () => fetchUserMetrics();
    window.addEventListener('dashboard_metrics_refresh', onRefresh);
    const onStorage = (e: StorageEvent) => {
      if (e.key === 'dashboard_metrics_refresh') fetchUserMetrics();
    };
    window.addEventListener('storage', onStorage);
    return () => {
      window.removeEventListener('dashboard_metrics_refresh', onRefresh);
      window.removeEventListener('storage', onStorage);
    };
  }, []);

  const handleDeleteUser = async (userId: number) => {
    const user = users.find(u => u.user_id === userId);
    const username = user ? user.username : `User ${userId}`;
    
    // Open the deletion modal
    setDeletionModal({
      isOpen: true,
      userId,
      username
    });
  };

  const handleConfirmDeletion = async () => {
    const { userId, username } = deletionModal;
    
    try {
      const token = localStorage.getItem('access_token');
      if (!token) throw new Error('Not authenticated');

      // Perform the actual deletion
      const res = await fetch(`${API_BASE_URL}/user-deletion/${userId}/delete`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${token}` },
      });
      
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to delete user');
      }

      const result = await res.json();
      
      // Remove user from local state
      setUsers(prev => prev.filter(u => u.user_id !== userId));
      
      // Show success message
      alert(`✅ User account "${username}" has been deleted.\n\nNote: All user data (documents, chats, notes) has been preserved.\n\n${result.message}`);
      
      // Refresh the list to ensure consistency
      fetchUserMetrics();
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      alert(`❌ Failed to delete user: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const handleCloseDeletionModal = () => {
    setDeletionModal({
      isOpen: false,
      userId: 0,
      username: ''
    });
  };

  // Filter and sort users
  const filteredAndSortedUsers = users
    .filter(user => 
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.industry && user.industry.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'username':
          aValue = a.username.toLowerCase();
          bValue = b.username.toLowerCase();
          break;
        case 'ai_queries':
          aValue = a.metrics.ai_query_count;
          bValue = b.metrics.ai_query_count;
          break;
        case 'docs':
          aValue = a.metrics.knowledge_docs_uploaded;
          bValue = b.metrics.knowledge_docs_uploaded;
          break;
        case 'last_seen':
          aValue = a.metrics.last_seen ? new Date(a.metrics.last_seen).getTime() : 0;
          bValue = b.metrics.last_seen ? new Date(b.metrics.last_seen).getTime() : 0;
          break;
        default:
          aValue = a.username.toLowerCase();
          bValue = b.username.toLowerCase();
      }
      
      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

  const handleSort = (newSortBy: typeof sortBy) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('desc'); // Default to descending for metrics
    }
  };

  const handleToggleExpand = (user: UserWithMetrics) => {
    setSelectedUser(user);
  };

  const handleCloseModal = () => {
    setSelectedUser(null);
  };

  const handleAddUserSuccess = () => {
    // Refresh the user metrics list to show the new user
    fetchUserMetrics();

    // Dispatch custom event to refresh other components that might need it
    window.dispatchEvent(new Event('dashboard_metrics_refresh'));
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="animate-pulse">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="h-12 w-12 bg-gray-200 rounded-full"></div>
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-32"></div>
                  </div>
                </div>
                <div className="grid grid-cols-4 gap-4">
                  {[...Array(4)].map((_, j) => (
                    <div key={j} className="h-16 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <div className="flex items-center justify-between">
          <span>{error}</span>
          <button
            onClick={fetchUserMetrics}
            className="ml-4 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header and Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
        <div>
          <p className="text-sm text-gray-600">{filteredAndSortedUsers.length} users found</p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
          {/* Add User Button */}
          <button
            onClick={() => setAddUserModal(true)}
            className="inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium shadow-sm"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add User
          </button>

          {/* Search */}
          <input
            type="text"
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Sort Controls */}
      <div className="flex flex-wrap gap-2 mb-4 p-3 bg-white rounded-lg border border-gray-200">
        <span className="text-sm text-gray-600 flex items-center font-medium">Sort by:</span>
        {[
          { key: 'username', label: 'Name' },
          { key: 'ai_queries', label: 'AI Queries' },
          { key: 'docs', label: 'Documents' },
          { key: 'last_seen', label: 'Last Seen' }
        ].map(({ key, label }) => (
          <button
            key={key}
            onClick={() => handleSort(key as typeof sortBy)}
            className={`px-3 py-1.5 text-sm rounded-md transition-colors font-medium ${
              sortBy === key
                ? 'bg-blue-900 text-white shadow-sm'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {label}
            {sortBy === key && (
              <span className="ml-1">
                {sortOrder === 'asc' ? '↑' : '↓'}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Users List - NO nested scroll */}
      <div className="space-y-3">
        {filteredAndSortedUsers.length === 0 ? (
          <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <p className="text-gray-500 font-medium">
              {searchTerm ? `No users found matching "${searchTerm}"` : 'No user metrics available.'}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {filteredAndSortedUsers.map((user) => (
              <UserMetricsCard
                key={user.user_id}
                user={user}
                metrics={user.metrics}
                isCollapsed={true}
                onToggleExpand={() => handleToggleExpand(user)}
              />
            ))}
          </div>
        )}
      </div>

      {/* User Deletion Modal */}
      <UserDeletionModal
        isOpen={deletionModal.isOpen}
        onClose={handleCloseDeletionModal}
        onConfirm={handleConfirmDeletion}
        userId={deletionModal.userId}
        username={deletionModal.username}
      />

      {/* User Metrics Modal */}
      {selectedUser && (
        <UserMetricsModal
          isOpen={!!selectedUser}
          onClose={handleCloseModal}
          user={selectedUser}
          metrics={selectedUser.metrics}
          canDelete={!!currentUser && (currentUser.role === 'admin') && selectedUser.role !== 'admin' && selectedUser.user_id !== currentUser.id}
          onDelete={() => {
            handleCloseModal();
            handleDeleteUser(selectedUser.user_id);
          }}
        />
      )}

      {/* Add User Modal */}
      <AddUserModal
        isOpen={addUserModal}
        onClose={() => setAddUserModal(false)}
        onSuccess={handleAddUserSuccess}
      />
    </div>
  );
}