# Deployment Guide - Image Embedding Service

## Quick Start

### Prerequisites
1. NVIDIA GPU with CUDA support
2. Docker with NVIDIA Container Toolkit
3. Docker Compose (optional)

### Step 1: Verify GPU Setup

```bash
nvidia-smi
```

Should show your GPU information.

### Step 2: Test Docker GPU Access

```bash
docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi
```

### Step 3: Build and Run

#### Using Docker Compose (Recommended)

```bash
cd Image_Embeddings
docker-compose up -d --build
```

#### Using Docker

```bash
cd Image_Embeddings
docker build -t image-embedding-service .
docker run -d \
  --name image-embedding-service \
  --gpus all \
  -p 8019:8019 \
  -v $(pwd)/model_cache:/app/model_cache \
  image-embedding-service
```

### Step 4: Verify Service

```bash
curl http://localhost:8019/health

python test_service.py
```

## GPU Setup on Ubuntu

### Install NVIDIA Drivers

```bash
ubuntu-drivers devices
sudo ubuntu-drivers autoinstall
sudo reboot
```

### Install NVIDIA Container Toolkit

```bash
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg
curl -s -L https://nvidia.github.io/libnvidia-container/$distribution/libnvidia-container.list | \
  sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
  sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list

sudo apt-get update
sudo apt-get install -y nvidia-container-toolkit
sudo nvidia-ctk runtime configure --runtime=docker
sudo systemctl restart docker
```

## GPU Setup on Windows

### Install NVIDIA Drivers
1. Download from: https://www.nvidia.com/Download/index.aspx
2. Install and reboot

### Install Docker Desktop
1. Download Docker Desktop for Windows
2. Enable WSL 2 backend
3. In Docker Desktop Settings > Resources > WSL Integration, enable integration

### Install NVIDIA Container Toolkit for WSL2

```powershell
wsl
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg
curl -s -L https://nvidia.github.io/libnvidia-container/experimental/$distribution/libnvidia-container.list | \
  sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
  sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list
sudo apt-get update
sudo apt-get install -y nvidia-container-toolkit
sudo nvidia-ctk runtime configure --runtime=docker
sudo systemctl restart docker
```

## Production Deployment

### Environment Configuration

Create `.env` file:

```env
PORT=8019
HOST=0.0.0.0
MODEL_NAME=google/siglip-so400m-patch14-384
DEVICE=cuda
BATCH_SIZE=8
MAX_CAPTION_LENGTH=100
CACHE_DIR=/app/model_cache
LOG_LEVEL=INFO
```

### Docker Compose for Production

```yaml
version: '3.8'

services:
  image-embedding-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: image-embedding-service
    ports:
      - "8019:8019"
    env_file:
      - .env
    volumes:
      - ./model_cache:/app/model_cache
      - ./logs:/app/logs
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        limits:
          memory: 16G
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8019/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 180s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: image-embedding-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: image-embedding-service
  template:
    metadata:
      labels:
        app: image-embedding-service
    spec:
      containers:
      - name: image-embedding-service
        image: image-embedding-service:latest
        ports:
        - containerPort: 8019
        resources:
          limits:
            nvidia.com/gpu: 1
            memory: "16Gi"
          requests:
            memory: "8Gi"
        env:
        - name: PORT
          value: "8019"
        - name: MODEL_NAME
          value: "google/siglip-so400m-patch14-384"
        - name: DEVICE
          value: "cuda"
        volumeMounts:
        - name: model-cache
          mountPath: /app/model_cache
      volumes:
      - name: model-cache
        persistentVolumeClaim:
          claimName: model-cache-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: image-embedding-service
spec:
  selector:
    app: image-embedding-service
  ports:
  - protocol: TCP
    port: 8019
    targetPort: 8019
  type: ClusterIP
```

## Monitoring

### Prometheus Metrics (Future Enhancement)

Add to `image_embedding_service.py`:

```python
from prometheus_client import Counter, Histogram, make_asgi_app

# Metrics
request_count = Counter('requests_total', 'Total requests')
request_duration = Histogram('request_duration_seconds', 'Request duration')

# Mount metrics endpoint
metrics_app = make_asgi_app()
app.mount("/metrics", metrics_app)
```

### Grafana Dashboard

Monitor:
- Request rate
- Processing time
- GPU utilization
- Memory usage
- Error rate

## Scaling

### Horizontal Scaling
- Deploy multiple instances behind a load balancer
- Each instance uses one GPU
- Use Redis for caching (optional)

### Vertical Scaling
- Use larger GPU (A100, H100)
- Increase batch size
- Use model quantization

## Troubleshooting

### Service Won't Start

1. Check logs:
```bash
docker-compose logs -f image-embedding-service
```

2. Verify GPU access:
```bash
docker exec -it image-embedding-service nvidia-smi
```

3. Check port availability:
```bash
lsof -i :8019
```

### Out of Memory

1. Reduce batch size in `.env`:
```env
BATCH_SIZE=4
```

2. Monitor GPU memory:
```bash
watch -n 1 nvidia-smi
```

### Slow Performance

1. Verify GPU is being used:
```bash
curl http://localhost:8019/model-info
```

2. Check GPU utilization:
```bash
nvidia-smi dmon
```

3. Increase batch size for batch processing

## Security

### Production Checklist

- [ ] Enable authentication (JWT/API Key)
- [ ] Use HTTPS/TLS
- [ ] Rate limiting
- [ ] Input validation
- [ ] Log sanitization
- [ ] Network isolation
- [ ] Regular security updates

### Add API Key Authentication

```python
from fastapi import Security, HTTPException
from fastapi.security import APIKeyHeader

API_KEY = os.getenv("API_KEY")
api_key_header = APIKeyHeader(name="X-API-Key")

async def verify_api_key(api_key: str = Security(api_key_header)):
    if api_key != API_KEY:
        raise HTTPException(status_code=403, detail="Invalid API Key")
    return api_key

@app.post("/embed")
async def embed_images(
    request: ImageEmbeddingRequest,
    api_key: str = Depends(verify_api_key)
):
    # ... existing code
```

## Backup and Recovery

### Backup Model Cache

```bash
tar -czf model_cache_backup.tar.gz model_cache/
```

### Restore Model Cache

```bash
tar -xzf model_cache_backup.tar.gz
```

## Performance Optimization

### Use Model Quantization

```python
from transformers import BitsAndBytesConfig

quantization_config = BitsAndBytesConfig(
    load_in_8bit=True,
    llm_int8_threshold=6.0
)

model = AutoModel.from_pretrained(
    config.model_name,
    quantization_config=quantization_config
)
```

### Use TorchScript

```python
traced_model = torch.jit.trace(model, example_inputs)
traced_model.save("model_traced.pt")
```

### Enable Flash Attention

```python
model = AutoModel.from_pretrained(
    config.model_name,
    torch_dtype=torch.float16,
    attn_implementation="flash_attention_2"
)
```

## Maintenance

### Update Model

1. Update MODEL_NAME in `.env`
2. Clear cache: `rm -rf model_cache/*`
3. Restart service: `docker-compose restart`

### View Logs

```bash
docker-compose logs -f image-embedding-service
```

### Update Service

```bash
docker-compose pull
docker-compose up -d --build
```

## Cost Optimization

### Cloud GPU Pricing (Approximate)

| Provider | GPU | Cost/Hour | Suitable For |
|----------|-----|-----------|-------------|
| AWS | g4dn.xlarge (T4) | $0.526 | Development |
| AWS | p3.2xlarge (V100) | $3.06 | Production |
| GCP | n1-standard-4 + T4 | $0.35 | Development |
| Azure | NC6s_v3 (V100) | $3.06 | Production |

### Optimization Tips

1. Use spot instances for non-critical workloads
2. Schedule scaling based on load
3. Cache model in persistent storage
4. Use batch processing
5. Enable auto-scaling

## Support

For issues:
1. Check logs
2. Verify GPU setup
3. Test with `test_service.py`
4. Review error messages

