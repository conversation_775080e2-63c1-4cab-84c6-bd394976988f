import { useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { getSecureToken } from '@/lib/api';

/**
 * Custom hook for secure authentication handling
 */
export const useSecureAuth = () => {
  const router = useRouter();

  const getTokenOrRedirect = useCallback(() => {
    const token = getSecureToken();
    if (!token) {
      router.push('/login');
      return null;
    }
    return token;
  }, [router]);

  const getUserData = useCallback(() => {
    try {
      const userData = localStorage.getItem('user');
      if (!userData) {
        router.push('/login');
        return null;
      }
      return JSON.parse(userData);
    } catch (error) {
      console.error('Error parsing user data:', error);
      localStorage.removeItem('user');
      router.push('/login');
      return null;
    }
  }, [router]);

  return { getTokenOrRedirect, getUserData };
};