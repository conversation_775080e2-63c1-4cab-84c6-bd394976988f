"""
Cache Optimization Service
Provides optimized caching strategies and utilities for better performance
"""

import hashlib
import json
from typing import Any, Dict, Optional, List
from functools import wraps
import logging

from .redis_service import redis_service
from .. import config

logger = logging.getLogger(__name__)


class CacheOptimizer:
    """
    Provides optimized caching strategies for different types of data
    """
    
    @staticmethod
    def build_user_specific_key(
        prefix: str,
        user_id: int,
        *args,
        **kwargs
    ) -> str:
        """
        Build a user-specific cache key
        
        Args:
            prefix: Cache key prefix
            user_id: User ID
            *args: Additional positional arguments for the key
            **kwargs: Additional keyword arguments for the key
        """
        key_parts = [prefix, str(user_id)]
        
        # Add positional arguments
        for arg in args:
            if arg is not None:
                key_parts.append(str(arg))
        
        # Add keyword arguments
        for k, v in sorted(kwargs.items()):
            if v is not None:
                key_parts.append(f"{k}:{v}")
        
        return ":".join(key_parts)
    
    @staticmethod
    def build_query_cache_key(
        query: str,
        user_id: int,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Build a cache key for query results
        
        Args:
            query: The query string
            user_id: User ID
            context: Additional context for the query
        """
        # Create a hash of the query and context
        cache_data = {
            "query": query.lower().strip(),
            "user_id": user_id,
            "context": context or {}
        }
        
        cache_str = json.dumps(cache_data, sort_keys=True)
        query_hash = hashlib.md5(cache_str.encode()).hexdigest()[:16]
        
        return f"{config.CACHE_PREFIX_CHAT}:query:{user_id}:{query_hash}"
    
    @staticmethod
    async def batch_cache_get(keys: List[str]) -> Dict[str, Any]:
        """
        Get multiple cache entries at once
        
        Args:
            keys: List of cache keys
            
        Returns:
            Dictionary mapping keys to their values
        """
        if not keys:
            return {}
        
        values = await redis_service.mget(keys)
        return {key: value for key, value in zip(keys, values) if value is not None}
    
    @staticmethod
    async def batch_cache_set(
        data: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """
        Set multiple cache entries at once
        
        Args:
            data: Dictionary of key-value pairs to cache
            ttl: Time to live for all entries
        """
        if not data:
            return True
        
        return await redis_service.mset(data, ttl=ttl)
    
    @staticmethod
    async def warm_cache_for_user(user_id: int):
        """
        Pre-warm cache with commonly accessed data for a user
        
        Args:
            user_id: User ID to warm cache for
        """
        try:
            from ..database import async_session
            from ..models.user import User
            from ..models.user_space import UserSpace
            from ..models.chat import Conversation
            from sqlalchemy.orm import joinedload
            from sqlalchemy import select, or_
            
            async with async_session() as db:
                # Get user data
                result = await db.execute(select(User).where(User.id == user_id))
                user = result.scalars().first()
                if not user:
                    return
                
                # Cache user profile
                user_key = f"{config.CACHE_PREFIX_USER}:profile:{user_id}"
                await redis_service.set(
                    user_key,
                    {
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "role": user.role,
                        "status": user.status,
                        "industry": user.industry
                    },
                    ttl=config.CACHE_TTL_USER_SESSION
                )
                
                # Cache user spaces
                result = await db.execute(
                    select(UserSpace).where(UserSpace.owner_id == user_id)
                )
                spaces = result.scalars().all()
                
                spaces_key = f"{config.CACHE_PREFIX_USER}:spaces:{user_id}"
                await redis_service.set(
                    spaces_key,
                    [{"id": s.id, "name": s.name} for s in spaces],
                    ttl=config.CACHE_TTL_MEDIUM
                )
                
                # Cache recent conversations
                result = await db.execute(
                    select(Conversation).where(
                        or_(
                            Conversation.participant1_id == user_id,
                            Conversation.participant2_id == user_id
                        ),
                        Conversation.is_active == True
                    ).limit(10)
                )
                conversations = result.scalars().all()
                
                conv_key = f"{config.CACHE_PREFIX_CHAT}:recent:{user_id}"
                await redis_service.set(
                    conv_key,
                    [{"id": c.id, "updated_at": c.updated_at.isoformat()} for c in conversations],
                    ttl=config.CACHE_TTL_SHORT
                )
                
                logger.info(f"Cache warmed for user {user_id}")
                
        except Exception as e:
            logger.error(f"Error warming cache for user {user_id}: {e}")


def cached_with_user(
    prefix: str,
    ttl: int = 3600,
    include_params: List[str] = None
):
    """
    Decorator for caching with user-specific keys
    
    Args:
        prefix: Cache key prefix
        ttl: Time to live in seconds
        include_params: List of parameter names to include in cache key
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract user from kwargs (assumes current_user parameter)
            current_user = kwargs.get("current_user")
            if not current_user or not hasattr(current_user, "id"):
                # No user context, execute without caching
                return await func(*args, **kwargs)
            
            # Build cache key
            cache_params = {}
            if include_params:
                for param in include_params:
                    if param in kwargs:
                        value = kwargs[param]
                        if hasattr(value, "id"):
                            cache_params[param] = value.id
                        else:
                            cache_params[param] = value
            
            cache_key = CacheOptimizer.build_user_specific_key(
                prefix,
                current_user.id,
                **cache_params
            )
            
            # Try to get from cache
            cached_value = await redis_service.get(cache_key)
            if cached_value is not None:
                logger.debug(f"Cache hit for user {current_user.id}: {cache_key}")
                return cached_value
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Cache result
            if result is not None:
                await redis_service.set(cache_key, result, ttl=ttl)
                logger.debug(f"Cached result for user {current_user.id}: {cache_key}")
            
            return result
        
        return wrapper
    return decorator


def invalidate_user_specific_cache(pattern_template: str):
    """
    Decorator to invalidate user-specific cache entries
    
    Args:
        pattern_template: Pattern template with {user_id} placeholder
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Execute function first
            result = await func(*args, **kwargs)
            
            # Extract user from kwargs
            current_user = kwargs.get("current_user")
            if current_user and hasattr(current_user, "id"):
                pattern = pattern_template.format(user_id=current_user.id)
                deleted = await redis_service.delete_pattern(pattern)
                if deleted > 0:
                    logger.debug(f"Invalidated {deleted} cache entries for user {current_user.id}")
            
            return result
        
        return wrapper
    return decorator


# Singleton instance
cache_optimizer = CacheOptimizer()

