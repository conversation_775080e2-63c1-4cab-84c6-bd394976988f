'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend, CartesianGrid, <PERSON><PERSON>hart, Pie, Cell, Label } from 'recharts';
import { MeSummary, UserMetrics } from '@/lib/api';

interface Props {
  summary: MeSummary | null;
  metrics: UserMetrics | null;
  loadingSummary?: boolean;
  loadingMetrics?: boolean;
}

export default function UserOverviewGrid({ summary, metrics, loadingSummary, loadingMetrics }: Props) {
  const loading = loadingSummary || loadingMetrics;

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="h-24 bg-gray-100 animate-pulse rounded-xl border" />
        ))}
      </div>
    );
  }

  if (!summary && !metrics) return null;

  const chats7 = Number(summary?.chats_7d || 0);
  const chats30 = Number(summary?.chats_30d || 0);
  const notes = Number(summary?.notes_total || 0);
  const docsUploaded = Number(metrics?.knowledge_docs_uploaded || 0);
  const docsIndexed = Number(metrics?.knowledge_docs_indexed || 0);
  const responsesShared = Number(metrics?.ai_responses_shared || 0);

  const barData = [
    { name: 'Chats', last7d: chats7, last30d: chats30 },
  ];

  const stackedDocs = [
    { name: 'Docs', uploaded: docsUploaded, indexed: docsIndexed },
  ];

  const radialData = [
    { name: 'Notes', value: notes, color: '#f59e0b' },
    { name: 'Responses', value: responsesShared, color: '#2563eb' },
  ];

  const textMuted = '#6b7280';
  const gridColor = '#eef2f7';

  const ChartTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;
    return (
      <div className="rounded-lg border border-gray-200 bg-white px-3 py-1.5 shadow-sm text-xs">
        <div className="font-medium text-gray-900 mb-0.5">{label}</div>
        {payload.map((p: any) => (
          <div key={p.dataKey} className="flex items-center gap-2">
            <span className="inline-block w-2 h-2 rounded-sm" style={{ background: p.color }} />
            <span className="text-gray-600">{p.name}:</span>
            <span className="text-gray-900 font-medium">{p.value}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
      {/* Chats: Grouped Bar (7d vs 30d) */}
      <div className="bg-white rounded-xl shadow-sm p-3">
        <div className="text-xs text-gray-500">Chats (7d vs 30d)</div>
        <div className="mt-1.5 h-40">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={barData} margin={{ top: 8, right: 8, left: -16, bottom: 0 }}>
              <defs>
                <linearGradient id="chat7" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#93c5fd" />
                  <stop offset="100%" stopColor="#60a5fa" />
                </linearGradient>
                <linearGradient id="chat30" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#3b82f6" />
                  <stop offset="100%" stopColor="#2563eb" />
                </linearGradient>
              </defs>
              <CartesianGrid stroke={gridColor} vertical={false} />
              <XAxis dataKey="name" tickLine={false} axisLine={false} tick={{ fill: textMuted }} />
              <YAxis allowDecimals={false} tickLine={false} axisLine={false} tick={{ fill: textMuted }} />
              <Tooltip content={<ChartTooltip />} cursor={{ fill: 'rgba(0,0,0,0.03)' }} />
              <Legend verticalAlign="bottom" height={20} wrapperStyle={{ fontSize: '11px' }} />
              <Bar dataKey="last7d" name="7 days" fill="url(#chat7)" radius={[8, 8, 0, 0]} />
              <Bar dataKey="last30d" name="30 days" fill="url(#chat30)" radius={[8, 8, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Docs: Stacked Bar (Uploaded vs Indexed) */}
      <div className="bg-white rounded-xl shadow-sm p-3">
        <div className="text-xs text-gray-500">Documents</div>
        <div className="mt-1.5 h-40">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={stackedDocs} stackOffset="none" margin={{ top: 8, right: 8, left: -16, bottom: 0 }}>
              <defs>
                <linearGradient id="docUp" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#c4b5fd" />
                  <stop offset="100%" stopColor="#a78bfa" />
                </linearGradient>
                <linearGradient id="docIdx" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#8b5cf6" />
                  <stop offset="100%" stopColor="#7c3aed" />
                </linearGradient>
              </defs>
              <CartesianGrid stroke={gridColor} vertical={false} />
              <XAxis dataKey="name" tickLine={false} axisLine={false} tick={{ fill: textMuted }} />
              <YAxis allowDecimals={false} tickLine={false} axisLine={false} tick={{ fill: textMuted }} />
              <Tooltip content={<ChartTooltip />} cursor={{ fill: 'rgba(0,0,0,0.03)' }} />
              <Legend verticalAlign="bottom" height={20} wrapperStyle={{ fontSize: '11px' }} />
              <Bar dataKey="uploaded" name="Uploaded" stackId="docs" fill="url(#docUp)" radius={[8, 8, 0, 0]} />
              <Bar dataKey="indexed" name="Indexed" stackId="docs" fill="url(#docIdx)" radius={[8, 8, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Notes vs Responses: Donut Pies */}
      <div className="bg-white rounded-xl shadow-sm p-3">
        <div className="text-xs text-gray-500">Notes & Responses</div>
        <div className="mt-1.5 h-40">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Tooltip content={<ChartTooltip />} />
              <Legend verticalAlign="bottom" height={20} wrapperStyle={{ fontSize: '11px' }} />
              <Pie
                data={radialData}
                dataKey="value"
                nameKey="name"
                innerRadius={44}
                outerRadius={64}
                paddingAngle={3}
                cornerRadius={8}
                startAngle={90}
                endAngle={450}
              >
                {radialData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} stroke="transparent" />
                ))}
                <Label
                  content={({ viewBox }: any) => {
                    if (!viewBox || !('cx' in viewBox)) return null;
                    const total = radialData.reduce((a, b) => a + (b.value || 0), 0);
                    return (
                      <g>
                        <text x={(viewBox as any).cx} y={(viewBox as any).cy - 4} textAnchor="middle" className="fill-gray-900" fontSize={18} fontWeight={600}>
                          {total}
                        </text>
                        <text x={(viewBox as any).cx} y={(viewBox as any).cy + 14} textAnchor="middle" className="fill-gray-500" fontSize={10}>
                          Total
                        </text>
                      </g>
                    );
                  }}
                  position="center"
                />
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}


