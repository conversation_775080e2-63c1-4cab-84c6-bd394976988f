#!/usr/bin/env python3
"""
Simple GPU Services Setup for AISaarthi

Creates Qdrant collections for all user spaces automatically.
No complex verification - just setup and go.
"""

import os
import asyncio
import logging
import sys
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, SparseVectorParams
import asyncpg
from app.config import DATABASE_URL
from dotenv import load_dotenv
load_dotenv()
# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
ENV = os.getenv("ENVIRONMENT")
# Configuration
QDRANT_URL = os.getenv(f"QDRANT_URL_{ENV}")
# Qdrant API Key Configuration
if ENV == "Prod":
    QDRANT_API_KEY = os.getenv("QDRANT_API_KEY_Prod")
else:
    QDRANT_API_KEY = os.getenv("QDRANT_API_KEY_Dev")
PREFER_GRPC = True
GRPC_PORT = os.getenv(f"GRPC_PORT_{ENV}")


def create_collection(client: QdrantClient, collection_name: str):
    """Create a Qdrant collection if it doesn't exist."""
    try:
        if not client.collection_exists(collection_name):
            client.create_collection(
                collection_name=collection_name,
                vectors_config={
                    "dense": VectorParams(size=768, distance=Distance.DOT)
                },
                sparse_vectors_config={
                    "sparse": SparseVectorParams()
                },
                hnsw_config={
                    "m": 32,
                    "ef_construct": 250,
                    "full_scan_threshold": 1000
                }
            )
            logger.info(f"✓ Created collection: {collection_name}")
        else:
            logger.info(f"✓ Collection exists: {collection_name}")
        return True
    except Exception as e:
        logger.error(f"✗ Failed to create collection {collection_name}: {e}")
        return False


async def get_users():
    """Get all users from database."""
    try:
        # Convert SQLAlchemy-style URL to standard PostgreSQL DSN for asyncpg
        postgres_dsn = DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        conn = await asyncpg.connect(postgres_dsn)
        try:
            result = await conn.fetch("""
                SELECT 
                    u.id,
                    u.username,
                    REGEXP_REPLACE(LOWER(u.username), '[^a-z0-9_]', '_', 'g') as collection_name
                FROM users u
                ORDER BY u.username;
            """)
            
            users = [(row[2], row[1]) for row in result]  # collection_name, username
            logger.info(f"Found {len(users)} users")
            return users
        finally:
            await conn.close()
    except Exception as e:
        logger.error(f"Failed to get users: {e}")
        return []


async def setup_gpu_services():
    """Setup GPU services infrastructure."""
    logger.info("Setting up AISaarthi GPU Services")
    logger.info("=" * 50)
    
    # Connect to Qdrant
    try:
        client = QdrantClient(
            url=QDRANT_URL,
            api_key=QDRANT_API_KEY,
            prefer_grpc=PREFER_GRPC,
            grpc_port=GRPC_PORT
        )
        logger.info(f"✓ Connected to Qdrant at {QDRANT_URL}")
    except Exception as e:
        logger.error(f"✗ Failed to connect to Qdrant: {e}")
        return False
    
    # Get users and create collections
    users = await get_users()
    
    if not users:
        logger.warning("No users found. Run 'python init_db.py --seed' first.")
        return True
    
    success_count = 0
    for collection_name, username in users:
        logger.info(f"Setting up user: {username} → {collection_name}")
        if create_collection(client, collection_name):
            success_count += 1
    
    logger.info(f"\nSetup completed: {success_count}/{len(users)} user collections ready")
    logger.info("GPU services are ready for document ingestion and retrieval!")
    
    return success_count == len(users)


def main():
    """Main entry point."""
    return asyncio.run(setup_gpu_services())


if __name__ == "__main__":
    main()