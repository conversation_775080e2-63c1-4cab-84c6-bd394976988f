#!/usr/bin/env python3
"""
Database migration script to create the notes table if it does not exist
"""

import sys
import os

# Ensure Backend directory (containing the 'app' package) is on sys.path
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
BACKEND_DIR = os.path.dirname(CURRENT_DIR)
if BACKEND_DIR not in sys.path:
    sys.path.insert(0, BACKEND_DIR)

from sqlalchemy import text
from app.database import engine


def migrate_create_notes_table():
    print("Starting migration: Creating notes table if missing...")

    try:
        with engine.connect() as connection:
            trans = connection.begin()
            try:
                # Check if table exists
                result = connection.execute(text("""
                    SELECT to_regclass('public.notes')
                """))
                exists = result.scalar() is not None
                if exists:
                    print("✅ notes table already exists. Migration not needed.")
                    trans.rollback()
                    return True

                # Create table
                print("Creating notes table...")
                connection.execute(text("""
                    CREATE TABLE notes (
                        id SERIAL PRIMARY KEY,
                        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                        interaction_id BIGINT NULL REFERENCES expert_chat_interactions(id) ON DELETE SET NULL,
                        title VARCHAR(255) NOT NULL,
                        combined_text TEXT NULL,
                        extra_text TEXT NULL,
                        sources JSON NULL,                       
                        session_id VARCHAR(100) NULL,                       
                        created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
                        updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW()
                    )
                """))

                # Indexes
                print("Creating indexes...")
                connection.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_notes_user_created ON notes (user_id, created_at);
                """))
                connection.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_notes_interaction ON notes (interaction_id);
                """))
                connection.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_notes_created_at ON notes (created_at);
                """))

                trans.commit()
                print("✅ Migration completed successfully!")
                return True
            except Exception as e:
                trans.rollback()
                print(f"❌ Error during migration: {e}")
                return False
    except Exception as e:
        print(f"❌ Failed to connect to database: {e}")
        return False


if __name__ == "__main__":
    success = migrate_create_notes_table()
    if success:
        print("\n🎉 Notes table ready!")
    else:
        print("\n💥 Migration failed! Please check logs.")
        sys.exit(1)


