/**
 * Component for displaying note content with edit/preview toggle
 */

'use client';

import React from 'react';
import { ParsedContent } from '@/types/note';

interface NoteContentPreviewProps {
  combined: string;
  setCombined: (value: string) => void;
  isEditing: boolean;
  setIsEditing: (value: boolean) => void;
  parsedContent: ParsedContent;
  onOpenFullPreview: () => void;
}

export default function NoteContentPreview({
  combined,
  setCombined,
  isEditing,
  setIsEditing,
  parsedContent,
  onOpenFullPreview,
}: NoteContentPreviewProps) {
  return (
    <div>
      <div className="flex items-center justify-between mb-2">
        <label className="block text-sm text-gray-700">Content</label>
        <div className="flex items-center gap-3">
          <button
            type="button"
            className="text-xs text-gray-600 hover:text-gray-800 flex items-center gap-1"
            onClick={onOpenFullPreview}
            title="Open full view"
            aria-label="Open full view"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-4 w-4" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M4 8V4h4M16 4h4v4M20 16v4h-4M8 20H4v-4" />
            </svg>
            <span className="hidden sm:inline">View</span>
          </button>
          <button
            type="button"
            className="text-xs text-blue-900 hover:text-blue-800"
            onClick={() => setIsEditing(!isEditing)}
          >
            {isEditing ? 'Done' : 'Edit'}
          </button>
        </div>
      </div>
      
      <div className="mb-3 rounded-lg text-sm max-h-[22vh] sm:max-h-[20vh] md:max-h-[18vh] overflow-y-auto border border-gray-200 bg-gray-50">
        {isEditing ? (
          <textarea
            value={combined}
            onChange={(e) => setCombined(e.target.value)}
            className="w-full h-[22vh] sm:h-[20vh] md:h-[18vh] resize-none px-3 py-2 bg-transparent text-gray-800 focus:outline-none whitespace-pre-wrap border-0"
            placeholder="Edit combined user message and AI answer"
          />
        ) : (
          <div className="p-3">
            <ContentDisplay parsedContent={parsedContent} combined={combined} />
          </div>
        )}
      </div>
    </div>
  );
}

function ContentDisplay({ 
  parsedContent, 
  combined 
}: { 
  parsedContent: ParsedContent; 
  combined: string;
}) {
  const { query, answer } = parsedContent;
  
  if (!query && !answer) {
    return <p className="text-gray-600 whitespace-pre-wrap">{combined}</p>;
  }
  
  return (
    <>
      {query && (
        <div className="mb-3">
          <p className="font-medium text-gray-700">Query:</p>
          <p className="text-gray-600 whitespace-pre-wrap">{query}</p>
        </div>
      )}
      {answer && (
        <div>
          <p className="font-medium text-gray-700">Response:</p>
          <p className="text-gray-600 whitespace-pre-wrap">{answer}</p>
        </div>
      )}
    </>
  );
}
