import logging
import time
from typing import List, Dict, Any, Optional, TypedDict, Annotated, Union
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from google import genai
from google.genai import types
from langchain_qdrant import QdrantVectorStore, RetrievalMode
from qdrant_client.models import Distance, Filter, FieldCondition, MatchValue
from qdrant_client import QdrantClient
import asyncio
import asyncpg
from concurrent.futures import ThreadPoolExecutor
from config import RetrieverConfig
from embedding_http_client import EmbeddingClient, EmbeddingConfig, DenseEmbeddingWrapper, SparseEmbeddingWrapper
import operator

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AgentState(TypedDict):
    """State for the RAG Agent"""
    original_query: str
    chat_history: List[Dict[str, Any]]
    messages: Annotated[List[BaseMessage], operator.add]
    
    # Query processing
    transformed_query: str
    multi_queries: List[str]
    queries_generated_count: int
    
    # Retrieval results per query batch
    all_retrieved_docs: List[Any]
    all_reranked_docs: List[Any]
    all_filtered_docs: List[Any]
    all_raw_docs: List[Any]
    
    # Context evaluation
    has_enough_context: bool
    context_quality_score: float
    iteration_count: int
    max_iterations: int
    
    # Final output
    final_response: str
    sources_used: List[str]
    context_used: str
    
    # Error handling
    filter_error: Optional[str]


class IntelligentRAGAgent:
    """
    Intelligent RAG Agent that:
    1. Transforms queries
    2. Generates multi-queries iteratively
    3. Retrieves and evaluates context
    4. Decides if it needs more queries or can answer
    5. Generates final response
    """
    
    def __init__(self, config: RetrieverConfig):
        self.config = config
        self.gemini_api_key = config.gemini_api_key
        self.qdrant_url = config.qdrant_url
        self.qdrant_api_key = config.qdrant_api_key
        self.prefer_grpc = config.prefer_grpc
        self.grpc_port = config.grpc_port
        self.embedding_api_url = config.embedding_api_url
        self.query_llm = config.query_llm
        self.response_model = config.response_model
        self.reranking_service_url = config.reranking_service_url
        self.reranking_timeout = 90
        
        # Dynamic per-request variables
        self.current_collection_name = None
        self.current_response_instructions = None
        self.current_vector_store = None
        self.allowed_files = None
        self.extra_filter = None
        
        logger.info("Initializing Intelligent RAG Agent...")
        self._setup_components()
        self._setup_agent_graph()
        logger.info("Intelligent RAG Agent initialization completed")
    
    def _setup_components(self):
        """Initialize all required components"""
        logger.info("Setting up agent components...")
        
        # Gemini client
        self.gemini_client = genai.Client(api_key=self.gemini_api_key)
        logger.info(f"Initialized Gemini client with query LLM: {self.query_llm}, response LLM: {self.response_model}")
        
        # Embedding service
        embedding_config = EmbeddingConfig(
            api_url=self.config.embedding_api_url,
            timeout=60,
            retry_attempts=3
        )
        self.embedding_client = EmbeddingClient(embedding_config)
        
        health = self.embedding_client.health_check()
        if health.get('status') not in ['healthy', 'partial']:
            logger.warning(f"Embedding API not available: {health}")
        else:
            logger.info("Connected to Embedding API service")
        
        # Embedding wrappers
        self.embeddings = DenseEmbeddingWrapper(self.embedding_client, normalize=True)
        self.sparse_embeddings = SparseEmbeddingWrapper(self.embedding_client)
        logger.info("Initialized API-based dense and sparse embeddings")
        
        # Qdrant client
        self.qdrant_client = QdrantClient(
            url=self.qdrant_url,
            api_key=self.qdrant_api_key,
            prefer_grpc=self.prefer_grpc,
            grpc_port=self.grpc_port
        )
        logger.info(f"Connected to Qdrant at {self.qdrant_url}")
        logger.info(f"Reranker will use external service at {self.reranking_service_url}")
        
        # Image Retriever (optional - will gracefully fail if service unavailable)
        try:
            from image_retrieval import ImageRetriever
            self.image_retriever = ImageRetriever(
                image_embedding_service_url=self.config.image_embedding_service_url,
                qdrant_client=self.qdrant_client,
                database_url=self.config.database_url
            )
            logger.info(f"Initialized Image Retriever with service URL: {self.config.image_embedding_service_url}")
        except Exception as e:
            logger.warning(f"Failed to initialize Image Retriever: {e}. Image search will be unavailable.")
            self.image_retriever = None
    
    def _setup_agent_graph(self):
        """Setup the agent workflow graph"""
        logger.info("Setting up Agent workflow graph...")
        
        workflow = StateGraph(AgentState)
        
        # Add agent nodes
        workflow.add_node("transform_query", self._transform_query_node)
        workflow.add_node("generate_queries", self._generate_queries_node)
        workflow.add_node("retrieve_and_evaluate", self._retrieve_and_evaluate_node)
        workflow.add_node("generate_response", self._generate_response_node)
        
        # Define the flow
        workflow.set_entry_point("transform_query")
        workflow.add_edge("transform_query", "generate_queries")
        workflow.add_edge("generate_queries", "retrieve_and_evaluate")
        
        # Agent decision point: need more context or ready to answer?
        workflow.add_conditional_edges(
            "retrieve_and_evaluate",
            self._should_continue_or_answer,
            {
                "generate_more": "generate_queries",  # Loop back to generate more queries
                "answer": "generate_response",         # Ready to answer
                "max_iterations": "generate_response"  # Hit max iterations, answer anyway
            }
        )
        
        workflow.add_edge("generate_response", END)
        
        self.graph = workflow.compile()
        logger.info("Agent workflow graph setup completed")
    
    def _transform_query_node(self, state: Dict) -> Dict:
        """Agent transforms the original query"""
        logger.info("AGENT: Transforming query...")
        
        original_query = state["original_query"]
        
        try:
            system_prompt = """
            You are an expert at transforming user queries to improve document retrieval.
            Transform the user's query to make it more specific and likely to match relevant documents.
            
            Rules:
            1. Keep the core intent of the query
            2. Add relevant keywords that might appear in documents
            3. Make the query more specific and detailed
            4. Don't change the meaning
            5. Return only the transformed query, no explanation
            """
            
            response = self.gemini_client.models.generate_content(
                model=self.query_llm,
                contents=f"Transform this query: {original_query}",
                config=types.GenerateContentConfig(
                    system_instruction=system_prompt,
                    temperature=0.3,
                    max_output_tokens=200
                )
            )
            
            transformed_query = (response.text or "").strip()
            state["transformed_query"] = transformed_query
            state["multi_queries"] = [original_query, transformed_query]
            state["queries_generated_count"] = 2
            state["iteration_count"] = 0
            state["max_iterations"] = 3
            state["has_enough_context"] = False
            state["all_retrieved_docs"] = []
            state["all_reranked_docs"] = []
            state["all_filtered_docs"] = []
            state["all_raw_docs"] = []
            
            logger.info(f"AGENT: Query transformed: {original_query} -> {transformed_query}")
            
        except Exception as e:
            logger.error(f"AGENT: Query transformation failed: {e}")
            state["transformed_query"] = original_query
            state["multi_queries"] = [original_query]
            state["queries_generated_count"] = 1
            state["iteration_count"] = 0
            state["max_iterations"] = 3
            state["has_enough_context"] = False
            state["all_retrieved_docs"] = []
            state["all_reranked_docs"] = []
            state["all_filtered_docs"] = []
            state["all_raw_docs"] = []
        
        return state
    
    def _generate_queries_node(self, state: Dict) -> Dict:
        """Agent generates additional query variations"""
        iteration = state.get("iteration_count", 0)
        current_queries = state.get("multi_queries", [])
        
        logger.info(f"AGENT: Generating queries (Iteration {iteration + 1})...")
        logger.info(f"AGENT: Current queries count: {len(current_queries)}")
        
        try:
            # Determine how many more queries to generate
            if iteration == 0:
                # First iteration: generate 2 more queries (total will be 4)
                num_to_generate = 2
            else:
                # Subsequent iterations: generate 2 more queries each time
                num_to_generate = 2
            
            system_prompt = f"""
            You are an expert at generating query variations for document retrieval.
            Generate {num_to_generate} alternative versions of the query that:
            1. Use different keywords but maintain the same intent
            2. Approach the topic from different angles
            3. Are specific and likely to match relevant documents
            4. Are DIFFERENT from the existing queries
            
            Existing queries:
            {chr(10).join(f"- {q}" for q in current_queries)}
            
            Return only the {num_to_generate} new alternative queries, one per line, no numbering or explanation.
            """
            
            original_query = state["original_query"]
            response = self.gemini_client.models.generate_content(
                model=self.query_llm,
                contents=f"Generate {num_to_generate} new alternatives for: {original_query}",
                config=types.GenerateContentConfig(
                    system_instruction=system_prompt,
                    temperature=0.5,
                    max_output_tokens=300
                )
            )
            
            new_queries = [q.strip() for q in (response.text or "").strip().split('\n') if q.strip()]
            new_queries = new_queries[:num_to_generate]  # Limit to requested number
            
            # Add new queries to existing ones
            all_queries = current_queries + new_queries
            state["multi_queries"] = all_queries
            state["queries_generated_count"] = len(all_queries)
            
            logger.info(f"AGENT: Generated {len(new_queries)} new queries")
            logger.info(f"AGENT: Total queries now: {len(all_queries)}")
            for i, q in enumerate(all_queries, 1):
                logger.info(f"  Query {i}: {q[:60]}...")
            
        except Exception as e:
            logger.error(f"AGENT: Query generation failed: {e}")
        
        return state
    
    def _retrieve_and_evaluate_node(self, state: Dict) -> Dict:
        """Agent retrieves documents for current queries and evaluates if context is sufficient"""
        iteration = state.get("iteration_count", 0)
        queries = state.get("multi_queries", [])
        
        logger.info(f"AGENT: Retrieving and evaluating context (Iteration {iteration + 1})...")
        logger.info(f"AGENT: Processing {len(queries)} queries")
        
        # Retrieve documents for all queries in parallel (preserve per-query results)
        per_query_results = self._parallel_retrieval(queries, return_per_query=True)
        
        # Apply RRF fusion to combine multi-query results intelligently
        logger.info(f"AGENT: Applying RRF fusion to combine results from {len(queries)} queries...")
        rrf_fused_docs = self._rrf_fusion(per_query_results, k=60)
        logger.info(f"AGENT: RRF fusion produced {len(rrf_fused_docs)} unique ranked documents")
        
        # Take top 15 UNIQUE documents from RRF for reranking
        top_candidates = rrf_fused_docs[:15]
        logger.info(f"AGENT: Selected top {len(top_candidates)} documents from RRF for reranking (all unique by doc_id)")
        
        # Optionally rerank top 15 documents (can skip if reranking service fails)
        reranked_docs = top_candidates
        try:
            if self.reranking_service_url:
                logger.info(f"AGENT: Attempting to rerank top {len(top_candidates)} documents...")
                reranked_docs = self._rerank_documents(state["original_query"], top_candidates)
                logger.info(f"AGENT: Reranking completed, got {len(reranked_docs)} reranked documents")
        except Exception as e:
            logger.warning(f"AGENT: Reranking failed, using RRF order: {e}")
            reranked_docs = top_candidates
        
        # Filter documents by confidence score (>= 30%) or top 3 if all below 30%
        filtered_docs = self._filter_documents_by_confidence(reranked_docs)
        logger.info(f"AGENT: Filtered to {len(filtered_docs)} documents based on confidence scores")
        
        # Fetch full raw documents from PostgreSQL database
        logger.info(f"AGENT: Fetching full raw documents from database...")
        raw_docs = self._fetch_raw_documents(filtered_docs)
        logger.info(f"AGENT: Fetched {len(raw_docs)} full documents from database")
        
        # If database fetch failed, fall back to Qdrant content
        if not raw_docs:
            logger.warning("AGENT: Database fetch failed, using Qdrant chunk content as fallback")
            for i, doc in enumerate(filtered_docs):
                if hasattr(doc, 'page_content') and hasattr(doc, 'metadata'):
                    raw_docs.append({
                        "id": doc.metadata.get('id', f'doc_{i}'),
                        "type": "text",
                        "content": doc.page_content,
                        "source": doc.metadata.get('source', 'Unknown')
                    })
        
        # Store results
        state["all_retrieved_docs"] = rrf_fused_docs  # Store RRF fused results instead of deduplicated
        state["all_reranked_docs"] = reranked_docs
        state["all_filtered_docs"] = filtered_docs
        state["all_raw_docs"] = raw_docs
        
        # Agent evaluates: Do I have enough context to answer?
        # Pass filtered_docs which have confidence scores from reranking
        has_enough, quality_score = self._evaluate_context_quality(
            state["original_query"],
            raw_docs,
            filtered_docs  # Use filtered_docs which have confidence scores
        )
        
        state["has_enough_context"] = has_enough
        state["context_quality_score"] = quality_score
        state["iteration_count"] = iteration + 1
        
        logger.info(f"AGENT: Context evaluation - Quality Score: {quality_score:.2f}, Enough: {has_enough}")
        
        return state
    
    def _parallel_retrieval(self, queries: List[str], return_per_query: bool = False) -> Union[List[Any], List[List[Any]]]:
        """Retrieve documents for multiple queries in parallel
        
        Args:
            queries: List of query strings to search
            return_per_query: If True, returns List[List[docs]] preserving per-query results
                            If False, returns flattened List[docs] (legacy behavior)
        
        Returns:
            List of documents (flattened) or List of per-query document lists
        """
        start_time = time.perf_counter()
        logger.info(f"AGENT: Starting parallel retrieval for {len(queries)} queries...")
        logger.info(f"AGENT: File filter active: {self.allowed_files}")
        logger.info(f"AGENT: Extra filter active: {self.extra_filter}")
        
        per_query_results = []
        
        def retrieve_for_query(query_info):
            query_idx, query = query_info
            query_start_time = time.perf_counter()
            try:
                logger.info(f"  [Query {query_idx}] Retrieving: {query[:50]}...")
                
                # Build search kwargs
                search_kwargs = {"k": 20}
                
                if self.allowed_files and len(self.allowed_files) == 1:
                    logger.info(f"  [Query {query_idx}] Applying file filter for: {self.allowed_files[0]}")
                    filter_conditions = [
                        FieldCondition(
                            key="metadata.source",
                            match=MatchValue(value=self.allowed_files[0])
                        )
                    ]
                    if self.extra_filter:
                        filter_conditions.extend(self._build_extra_filter_conditions(self.extra_filter))
                    search_kwargs["filter"] = Filter(must=filter_conditions)
                    search_kwargs["k"] = 50
                    logger.info(f"  [Query {query_idx}] Search with file filter, k=50")
                elif self.extra_filter and not self.allowed_files:
                    extra_filter_conditions = self._build_extra_filter_conditions(self.extra_filter)
                    if extra_filter_conditions:
                        search_kwargs["filter"] = Filter(must=extra_filter_conditions)
                
                docs = self.current_vector_store.as_retriever(search_kwargs=search_kwargs).invoke(query)
                
                query_elapsed = time.perf_counter() - query_start_time
                logger.info(f"  [Query {query_idx}] Retrieved {len(docs)} docs in {query_elapsed:.3f}s")
                return (query_idx, docs)
                
            except Exception as e:
                query_elapsed = time.perf_counter() - query_start_time
                logger.error(f"  [Query {query_idx}] Failed after {query_elapsed:.3f}s: {e}")
                return (query_idx, [])
        
        # Execute in parallel
        query_list = list(enumerate(queries, 1))
        with ThreadPoolExecutor(max_workers=len(query_list)) as executor:
            future_to_query = {executor.submit(retrieve_for_query, q_info): q_info for q_info in query_list}
            
            # Collect results with original query order preserved
            results_dict = {}
            from concurrent.futures import as_completed
            for future in as_completed(future_to_query):
                try:
                    query_idx, docs = future.result()
                    results_dict[query_idx] = docs
                except Exception as e:
                    logger.error(f"Query execution error: {e}")
        
        # Rebuild results in original query order
        for idx in range(1, len(queries) + 1):
            per_query_results.append(results_dict.get(idx, []))
        
        elapsed = time.perf_counter() - start_time
        logger.info(f"AGENT: Text retrieval completed in {elapsed:.3f}s")
        
        # Always search image vectors as well (generic hybrid search)
        if self.image_retriever:
            logger.info(f"AGENT: Also searching image vectors for each query...")
            image_start = time.perf_counter()
            try:
                for query_idx, query in enumerate(queries, 1):
                    # Apply file filter if specified
                    file_filter = None
                    if self.allowed_files and len(self.allowed_files) == 1:
                        logger.info(f"  [Query {query_idx}] Applying file filter for images: {self.allowed_files[0]}")
                        file_filter = self.allowed_files[0]
                    
                    image_docs = self.image_retriever.retrieve_images_by_text(
                        query=query,
                        collection_name=self.current_collection_name,
                        top_k=10,
                        fetch_base64=False,
                        source_filter=file_filter
                    )
                    
                    if image_docs:
                        # Convert image results to document format
                        from langchain_core.documents import Document
                        for img in image_docs:
                            # Create a document from image metadata
                            doc = Document(
                                page_content=img.get('caption', ''),
                                metadata={
                                    'id': img.get('id'),
                                    'source': img.get('source'),
                                    'type': 'image',
                                    'page_number': img.get('page_number'),
                                    'image_index': img.get('image_index'),
                                    'score': img.get('score', 0.0)
                                }
                            )
                            # Add to the corresponding query results
                            per_query_results[query_idx - 1].append(doc)
                        
                        logger.info(f"  [Query {query_idx}] Found {len(image_docs)} additional images")
                
                image_elapsed = time.perf_counter() - image_start
                logger.info(f"AGENT: Image retrieval completed in {image_elapsed:.3f}s")
            except Exception as e:
                logger.warning(f"AGENT: Image retrieval failed: {e}")
        
        total_elapsed = time.perf_counter() - start_time
        logger.info(f"AGENT: Total hybrid retrieval completed in {total_elapsed:.3f}s")
        
        # Return based on mode
        if return_per_query:
            return per_query_results
        else:
            # Flatten for backward compatibility
            all_docs = []
            for docs in per_query_results:
                all_docs.extend(docs)
            return all_docs
    
    def _deduplicate_documents(self, docs: List[Any]) -> List[Any]:
        """
        Remove duplicate documents based on:
        1. Exact ID match
        2. Exact content match (to avoid duplicate chunks)
        """
        seen_ids = set()
        seen_content = set()
        unique_docs = []
        
        for doc in docs:
            doc_id = doc.metadata.get('id', '')
            
            # Get content for comparison
            if hasattr(doc, 'page_content'):
                content = doc.page_content.strip()
            else:
                content = str(doc).strip()
            
            # Create a hash of the content for efficient comparison
            content_hash = hash(content[:500])  # Hash first 500 chars
            
            # Skip if we've seen this ID or this exact content
            if doc_id and doc_id in seen_ids:
                continue
            if content and content_hash in seen_content:
                logger.debug(f"Skipping duplicate content for doc {doc_id}")
                continue
            
            # Add to unique docs
            if doc_id:
                seen_ids.add(doc_id)
            if content:
                seen_content.add(content_hash)
            unique_docs.append(doc)
        
        logger.info(f"Deduplicated {len(docs)} docs to {len(unique_docs)} unique docs")
        return unique_docs[:25]  # Return top 25 unique documents from Qdrant
    
    def _rrf_fusion(self, per_query_results: List[List[Any]], k: int = 60) -> List[Any]:
        """
        Reciprocal Rank Fusion (RRF) to combine results from multiple queries.
        
        RRF gives each document a score based on its rank positions across all searches:
        score = sum(1 / (k + rank)) for each appearance
        
        Documents that appear highly ranked in multiple searches get higher scores.
        
        Args:
            per_query_results: List of document lists, one per query (preserves rank order)
            k: RRF constant (default 60, standard value from research)
        
        Returns:
            List of unique documents sorted by RRF score (highest first)
        """
        logger.info(f"AGENT: Starting RRF fusion with k={k} across {len(per_query_results)} query results")
        
        # Track document scores and metadata
        doc_scores = {}
        
        for query_idx, result_list in enumerate(per_query_results, 1):
            logger.info(f"  RRF: Processing Query {query_idx} with {len(result_list)} results")
            
            for rank, doc in enumerate(result_list, start=1):
                # Use document ID as unique identifier
                doc_id = doc.metadata.get('id', '')
                if not doc_id:
                    continue
                
                # Calculate RRF score: 1 / (k + rank)
                rrf_score = 1.0 / (k + rank)
                
                # Initialize or update document entry
                if doc_id not in doc_scores:
                    doc_scores[doc_id] = {
                        'doc': doc,
                        'rrf_score': 0.0,
                        'appearances': 0,
                        'ranks': []
                    }
                
                # Accumulate RRF score
                doc_scores[doc_id]['rrf_score'] += rrf_score
                doc_scores[doc_id]['appearances'] += 1
                doc_scores[doc_id]['ranks'].append(rank)
        
        # Sort by RRF score (descending)
        sorted_docs = sorted(
            doc_scores.values(),
            key=lambda x: x['rrf_score'],
            reverse=True
        )
        
        # Log top documents with their RRF scores
        logger.info(f"AGENT: RRF fusion completed - {len(sorted_docs)} UNIQUE documents (automatically deduplicated by doc_id)")
        for i, item in enumerate(sorted_docs[:5], 1):
            logger.info(
                f"  RRF Top {i}: score={item['rrf_score']:.4f}, "
                f"appearances={item['appearances']}, ranks={item['ranks']}"
            )
        
        # Return sorted documents (already unique by doc_id)
        return [item['doc'] for item in sorted_docs]
    
    def _rerank_documents(self, query: str, documents: List[Any]) -> List[Any]:
        """Rerank documents using external reranking service"""
        if not documents:
            return []
        
        try:
            import requests
            
            # Convert to dict format
            docs_for_reranking = []
            for doc in documents:
                docs_for_reranking.append({
                    'content': doc.page_content,
                    'metadata': dict(doc.metadata)
                })
            
            # Call reranking service
            response = requests.post(
                f"{self.reranking_service_url}/rerank",
                json={
                    "query": query,
                    "documents": docs_for_reranking,
                    "top_n": len(docs_for_reranking)
                },
                timeout=self.reranking_timeout,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                reranked_documents = result['reranked_documents']
                
                # Convert back to LangChain documents
                from langchain_core.documents import Document
                reranked_docs = []
                for idx, doc_dict in enumerate(reranked_documents):
                    metadata = doc_dict['metadata'].copy()
                    confidence_score = doc_dict.get('relevance_score', doc_dict.get('confidence_score', 0.0))
                    if confidence_score is not None:
                        metadata['confidence_score'] = confidence_score
                        metadata['relevance_score'] = confidence_score
                    
                    reranked_docs.append(Document(
                        page_content=doc_dict['content'],
                        metadata=metadata
                    ))
                    
                    # Log each document with confidence score
                    source = metadata.get('source', 'Unknown')
                    doc_id = metadata.get('id', 'Unknown')
                    logger.info(f"  Reranked Doc {idx+1}: ID={doc_id}, Confidence={confidence_score:.4f}, Source={source}")
                
                if reranked_docs:
                    top_confidence = reranked_docs[0].metadata.get('confidence_score', 0.0)
                    avg_confidence = sum(d.metadata.get('confidence_score', 0.0) for d in reranked_docs) / len(reranked_docs)
                    logger.info(f"AGENT: Top reranked doc confidence: {top_confidence:.4f}")
                    logger.info(f"AGENT: Average confidence: {avg_confidence:.4f}")
                
                return reranked_docs[:15]
            else:
                logger.error(f"Reranking service error: {response.status_code}")
                return documents[:15]
                
        except Exception as e:
            logger.error(f"Reranking failed: {e}")
            return documents[:15]
    
    def _filter_documents_by_confidence(self, reranked_docs: List[Any]) -> List[Any]:
        """
        Filter documents by confidence score for CONTEXT GENERATION:
        - Use ALL documents with confidence > 0% for generating the answer context
        - This is different from frontend display which only shows docs >= 30%
        - If no documents have confidence scores, use top 3 documents as fallback
        """
        if not reranked_docs:
            return []
        
        # Get all documents with confidence scores (for context generation)
        docs_with_confidence = []
        for doc in reranked_docs:
            confidence = doc.metadata.get('confidence_score', 0.0)
            if confidence > 0.0:
                docs_with_confidence.append(doc)
        
        if docs_with_confidence:
            logger.info(f"AGENT: Using {len(docs_with_confidence)} documents for context generation (all with confidence > 0%)")
            # Log confidence range
            confidences = [d.metadata.get('confidence_score', 0.0) for d in docs_with_confidence]
            logger.info(f"AGENT: Confidence range: {min(confidences):.4f} - {max(confidences):.4f}")
            return docs_with_confidence
        else:
            # Fallback to top 3 documents if no confidence scores available
            top_3_docs = reranked_docs[:3]
            logger.info(f"AGENT: No documents with confidence scores, using top 3 documents as fallback")
            return top_3_docs
    
    def _fetch_raw_documents(self, reranked_docs: List[Any]) -> List[Dict]:
        """Fetch raw documents from PostgreSQL"""
        if not reranked_docs:
            return []
        
        raw_docs = []
        q_ids = [doc.metadata.get('id', '') for doc in reranked_docs]
        
        async def _fetch_rows(ids):
            if not ids or not self.config.database_url:
                return []
            
            try:
                # Convert SQLAlchemy-style URL to standard PostgreSQL DSN for asyncpg
                postgres_dsn = self.config.database_url.replace("postgresql+asyncpg://", "postgresql://")
                conn = await asyncpg.connect(postgres_dsn)
                valid_ids = [id for id in ids if id]
                if not valid_ids:
                    return []
                
                rows = await conn.fetch(
                    "SELECT id, type, content, base64, source FROM raw_documents WHERE id = ANY($1)",
                    valid_ids,
                )
                await conn.close()
                return rows
            except Exception as e:
                logger.error(f"Database query failed: {e}")
                return []
        
        def _run_fetch():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(_fetch_rows(q_ids))
            finally:
                loop.close()
        
        with ThreadPoolExecutor(max_workers=1) as executor:
            rows = executor.submit(_run_fetch).result()
        
        row_map = {row[0]: row for row in rows}
        
        for doc in reranked_docs:
            doc_id = doc.metadata.get('id', '')
            if not doc_id or doc_id not in row_map:
                continue
            
            _id, _type, _content, _base64, _source = row_map[doc_id]
            _type = str(_type).lower()
            
            if _type == "text":
                raw_docs.append({
                    "id": _id,
                    "type": "text",
                    "content": _content or "",
                    "source": _source or ""
                })
            elif _type == "image":
                # For images, include caption as content, base64 for display
                raw_docs.append({
                    "id": _id,
                    "type": "image",
                    "content": _content or "",  # Caption
                    "base64": f"data:image/png;base64,{_base64}" if _base64 else None,
                    "source": _source or ""
                })
        
        return raw_docs
    
    def _evaluate_context_quality(self, query: str, raw_docs: List[Dict], reranked_docs: List[Any]) -> tuple:
        """
        Agent evaluates if retrieved context is sufficient to answer the query
        Returns: (has_enough_context: bool, quality_score: float)
        
        Uses confidence scores from reranking combined with document count and content length.
        """
        logger.info("AGENT: Evaluating context quality...")
        
        # No documents = definitely not enough
        if not raw_docs or not reranked_docs:
            logger.info("AGENT: No documents found - need more queries")
            return False, 0.0
        
        # Extract confidence scores from reranked docs
        confidence_scores = [doc.metadata.get('confidence_score', 0.0) for doc in reranked_docs]
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
        max_confidence = max(confidence_scores) if confidence_scores else 0.0
        high_confidence_count = sum(1 for score in confidence_scores if score >= 0.7)
        medium_confidence_count = sum(1 for score in confidence_scores if 0.4 <= score < 0.7)
        
        # Calculate total content length and count images
        total_content_length = 0
        image_count = 0
        text_count = 0
        for doc in raw_docs:
            doc_type = doc.get('type', 'text').lower()
            if doc_type == 'image':
                image_count += 1
                # Count caption length or give minimum score for images
                total_content_length += len(doc.get('content', '')) or 50  # Minimum value for images
            else:
                text_count += 1
                total_content_length += len(doc.get('content', ''))
        
        avg_content_length = total_content_length / len(raw_docs) if raw_docs else 0
        
        logger.info(f"AGENT: Have {len(raw_docs)} documents ({text_count} text, {image_count} images), total content: {total_content_length} chars, avg: {avg_content_length:.0f} chars")
        logger.info(f"AGENT: Confidence scores - Max: {max_confidence:.4f}, Avg: {avg_confidence:.4f}, High (≥70%): {high_confidence_count}, Medium (40-69%): {medium_confidence_count}")
        
        # Confidence-based evaluation rules
        # Rule 1: If we have high confidence documents (≥70%), that's great
        if high_confidence_count >= 2 and avg_content_length > 100:
            logger.info("AGENT: ✓ Have 2+ high-confidence documents (≥70%) - SUFFICIENT")
            return True, 0.85
        
        if high_confidence_count >= 1 and max_confidence >= 0.8 and total_content_length > 500:
            logger.info("AGENT: ✓ Have 1 very high-confidence document (≥80%) with good content - SUFFICIENT")
            return True, 0.8
        
        # Rule 2: Multiple medium-high confidence documents
        if avg_confidence >= 0.6 and len(raw_docs) >= 3 and avg_content_length > 150:
            logger.info("AGENT: ✓ Have 3+ documents with good avg confidence (≥60%) - SUFFICIENT")
            return True, 0.75
        
        # Rule 3: Decent confidence with substantial content
        if avg_confidence >= 0.5 and total_content_length > 2000:
            logger.info("AGENT: ✓ Have decent confidence (≥50%) with substantial content - SUFFICIENT")
            return True, 0.7
        
        # Rule 4: Medium confidence with reasonable documents
        if avg_confidence >= 0.4 and len(raw_docs) >= 4 and avg_content_length > 100:
            logger.info("AGENT: ✓ Have 4+ documents with medium confidence (≥40%) - SUFFICIENT")
            return True, 0.65
        
        # Rule 5: For single document queries (e.g., summarization)
        if len(raw_docs) == 1 and max_confidence >= 0.5 and total_content_length > 1000:
            logger.info("AGENT: ✓ Have 1 document with good confidence and substantial content - SUFFICIENT for single doc query")
            return True, 0.6
        
        # Rule 6: Low confidence or very short content = not enough
        if avg_confidence < 0.3:
            logger.info(f"AGENT: ✗ Low average confidence ({avg_confidence:.4f}) - NEED MORE")
            return False, 0.25
        
        if avg_content_length < 50:
            logger.info("AGENT: ✗ Documents too short (avg < 50 chars) - NEED MORE")
            return False, 0.2
        
        # Rule 7: Borderline case - some confidence with some content
        if avg_confidence >= 0.35 and len(raw_docs) >= 2 and avg_content_length > 100:
            logger.info("AGENT: ✓ Have 2+ documents with borderline confidence - SUFFICIENT (marginal)")
            return True, 0.5
        
        # Default: not enough
        logger.info(f"AGENT: ✗ Not enough quality content - Confidence: {avg_confidence:.4f}, Docs: {len(raw_docs)}, Avg length: {avg_content_length:.0f}")
        return False, 0.3
    
    def _should_continue_or_answer(self, state: Dict) -> str:
        """Agent decides: generate more queries or answer?"""
        has_enough = state.get("has_enough_context", False)
        iteration = state.get("iteration_count", 0)
        max_iterations = state.get("max_iterations", 3)
        quality_score = state.get("context_quality_score", 0.0)
        
        logger.info(f"AGENT DECISION: Iteration {iteration}/{max_iterations}, Quality: {quality_score:.2f}, Enough: {has_enough}")
        
        # Hit max iterations - answer with what we have
        if iteration >= max_iterations:
            logger.info("AGENT: Max iterations reached - generating answer")
            return "max_iterations"
        
        # Have enough context - answer now
        if has_enough:
            logger.info("AGENT: Sufficient context found - generating answer")
            return "answer"
        
        # Need more context - generate more queries
        logger.info("AGENT: Insufficient context - generating more queries")
        return "generate_more"
    
    def _generate_response_node(self, state: Dict) -> Dict:
        """Agent generates final response based on retrieved context"""
        logger.info("AGENT: Generating final response...")
        
        raw_docs = state.get("all_raw_docs", [])
        
        if not raw_docs:
            logger.warning("AGENT: No documents available for response generation")
            state["final_response"] = "I couldn't find any relevant documents to answer your question. Please try rephrasing your query."
            return state
        
        # Build context with additional deduplication and image support
        context = ""
        sources = []
        seen_content_hashes = set()
        unique_doc_count = 0
        has_images = False
        image_count = 0
        
        for i, doc in enumerate(raw_docs, 1):
            if isinstance(doc, dict):
                source = doc.get("source", "Unknown")
                doc_type = doc.get("type", "text").lower()
                
                # Handle IMAGE chunks differently
                if doc_type == "image":
                    has_images = True
                    image_count += 1
                    # For images, use caption or indicate image presence
                    caption = doc.get("content", "")
                    if caption and caption.strip():
                        context += f"[IMAGE {image_count}]: {caption}\n"
                    else:
                        context += f"[IMAGE {image_count}] from {source}\n"
                    context += f"(This document contains an image that may have relevant visual information)\n\n"
                    unique_doc_count += 1
                    if source not in sources:
                        sources.append(source)
                    continue
                
                # Handle TEXT chunks
                content = doc.get("content", "")
                
                # Skip empty text content
                if not content or not content.strip():
                    continue
                
                # Create content hash to detect duplicates
                content_hash = hash(content.strip()[:500])
                
                # Skip if we've already added this exact content
                if content_hash in seen_content_hashes:
                    logger.debug(f"Skipping duplicate content in context building (doc {i})")
                    continue
                
                # Add unique content
                context += f"{content}\n\n"
                seen_content_hashes.add(content_hash)
                unique_doc_count += 1
                
                if source not in sources:
                    sources.append(source)
        
        logger.info(f"AGENT: Built context from {unique_doc_count}/{len(raw_docs)} unique documents (removed {len(raw_docs) - unique_doc_count} duplicates)")
        if has_images:
            logger.info(f"AGENT: Context includes {image_count} image(s)")
        
        # Generate response
        system_prompt = """
        You are an AI assistant answering questions based on the provided context.
        
        Guidelines:
        1. Answer the question using ONLY the information in the provided context
        2. If the context contains [IMAGE] markers, acknowledge that relevant images exist in the documents
        3. When asked about images or visual content, mention that you found relevant images with their captions/descriptions
        4. If the context doesn't contain the answer, say you didn't get enough information from the documents or the documents are not relevant to the question
        5. Be concise and direct
        6. Do not apologize or use phrases like "Based on the provided context"
        7. Format your answer in a clear, readable way
        8. If the context contains conflicting information, acknowledge it
        9. For image-related queries, clearly state what images were found and their captions
        """
        
        try:
            logger.info(f"AGENT: Calling Gemini with model: {self.response_model}")
            logger.info(f"AGENT: Context length: {len(context)} chars")
            logger.info(f"AGENT: Query: {state['original_query']}")
            
            response = self.gemini_client.models.generate_content(
                model=self.response_model,
                contents=f"Context:\n{context}\n\nQuestion: {state['original_query']}",
                config=types.GenerateContentConfig(
                    system_instruction=system_prompt,
                    temperature=0.1,
                    max_output_tokens=4096
                )
            )
            
            logger.info(f"AGENT: Gemini response received, type: {type(response)}")
            logger.info(f"AGENT: Response text: {response.text[:200] if response.text else 'NONE'}")
            
            final_response = (response.text or "").strip()
            
            if not final_response:
                logger.error("AGENT: Response text is empty!")
                final_response = "I'm having trouble processing your request right now. Please try rephrasing your question or try again in a moment."
            
            state["final_response"] = final_response
            state["context_used"] = context
            state["sources_used"] = sources
            
            logger.info("AGENT: Response generated successfully")
            logger.info(f"AGENT: Used {len(raw_docs)} documents from {len(sources)} sources")
            logger.info(f"AGENT: Final response length: {len(final_response)} chars")
            
        except Exception as e:
            logger.error(f"AGENT: Response generation failed: {e}")
            import traceback
            logger.error(f"AGENT: Traceback: {traceback.format_exc()}")
            state["final_response"] = "I'm unable to process your request at the moment. Please try again, or contact support if the issue persists."
        
        return state
    
    def _build_extra_filter_conditions(self, extra_filter: Dict[str, Any]) -> List:
        """Convert extra filter dict to Qdrant filter conditions"""
        conditions = []
        if not extra_filter:
            return conditions
        
        if "must" in extra_filter:
            for condition in extra_filter["must"]:
                if "key" in condition and "match" in condition:
                    key = condition["key"]
                    match_value = condition["match"].get("value")
                    if match_value is not None:
                        conditions.append(
                            FieldCondition(
                                key=key,
                                match=MatchValue(value=match_value)
                            )
                        )
        return conditions
    
    def run_agent(self,
                  query: str,
                  collection_name: str,
                  allowed_files: Optional[List[str]] = None,
                  chat_history: List[Dict[str, Any]] = None,
                  extra_filter: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Run the intelligent RAG agent
        
        Args:
            query: User query
            collection_name: Qdrant collection name
            allowed_files: Optional file filter
            chat_history: Optional chat history
            extra_filter: Optional extra filters
        
        Returns:
            Dict with final_response, sources, and metadata
        """
        logger.info(f"AGENT: Starting intelligent RAG for query: {query[:50]}...")
        
        # Set current request parameters
        self.current_collection_name = collection_name
        self.allowed_files = allowed_files
        self.extra_filter = extra_filter
        
        # Create vector store
        self.current_vector_store = QdrantVectorStore(
            client=self.qdrant_client,
            collection_name=collection_name,
            embedding=self.embeddings,
            sparse_embedding=self.sparse_embeddings,
            retrieval_mode=RetrievalMode.HYBRID,
            vector_name="dense",
            sparse_vector_name="sparse",
            distance=Distance.DOT,
        )
        
        # Initialize state
        initial_state = {
            "original_query": query,
            "chat_history": chat_history or [],
            "messages": [],
            "transformed_query": "",
            "multi_queries": [],
            "queries_generated_count": 0,
            "all_retrieved_docs": [],
            "all_reranked_docs": [],
            "all_filtered_docs": [],
            "all_raw_docs": [],
            "has_enough_context": False,
            "context_quality_score": 0.0,
            "iteration_count": 0,
            "max_iterations": 3,
            "final_response": "",
            "sources_used": [],
            "context_used": "",
            "filter_error": None
        }
        
        try:
            # Run the agent
            final_state = self.graph.invoke(initial_state)
            
            logger.info("AGENT: Execution completed successfully")
            logger.info(f"AGENT: Total iterations: {final_state.get('iteration_count', 0)}")
            logger.info(f"AGENT: Total queries generated: {final_state.get('queries_generated_count', 0)}")
            logger.info(f"AGENT: Final quality score: {final_state.get('context_quality_score', 0):.2f}")
            
            return final_state
            
        except Exception as e:
            logger.error(f"AGENT: Execution failed: {e}")
            import traceback
            logger.error(f"AGENT: Traceback: {traceback.format_exc()}")
            return {
                "original_query": query,
                "final_response": "I'm having trouble processing your request right now. Please try rephrasing your question or try again in a moment.",
                "error": str(e)
            }
