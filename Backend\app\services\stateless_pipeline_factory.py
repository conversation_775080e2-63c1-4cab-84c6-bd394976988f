"""
Stateless Pipeline Factory

Creates lightweight pipeline instances without memory caching.
Uses connection pools and Redis state management for scalable, stateless operation.
"""

import logging
import uuid
from typing import Dict, Any, Optional, Union
from datetime import datetime
from contextlib import contextmanager

from .connection_pools import connection_pools
from .stateless_state_manager import stateless_state
# from ..Data_Ingestion.config import ProcessingConfig

# TODO: Import ProcessingConfig from separate service or define locally
from ..Data_Retriever.config import RetrieverConfig

logger = logging.getLogger(__name__)

class StatelessPipelineFactory:
    """
    Factory for creating stateless pipeline instances.
    
    Instead of caching pipeline instances in memory, this factory:
    1. Creates lightweight pipeline instances per request
    2. Uses shared connection pools for heavy resources
    3. Stores configurations in Redis
    4. Ensures proper cleanup after processing
    """
    
    def __init__(self):
        self.factory_id = str(uuid.uuid4())[:8]
        logger.info(f"Stateless Pipeline Factory initialized: {self.factory_id}")
    
    async def create_ingestion_pipeline(self, config_dict: Dict[str, Any]) -> 'StatelessIngestionPipeline':
        """
        Create a stateless data ingestion pipeline.
        
        Args:
            config_dict: Configuration dictionary
            
        Returns:
            Stateless ingestion pipeline instance
        """
        try:
            # Store config in Redis and get hash
            config_hash = await stateless_state.store_pipeline_config(
                config_dict, "ingestion"
            )
            
            # Create lightweight pipeline instance
            pipeline = StatelessIngestionPipeline(config_hash, config_dict)
            
            logger.debug(f"Created stateless ingestion pipeline: {config_hash}")
            return pipeline
            
        except Exception as e:
            logger.error(f"Error creating ingestion pipeline: {e}")
            raise
    
    async def create_retrieval_pipeline(self, config_dict: Dict[str, Any]) -> 'StatelessRetrievalPipeline':
        """
        Create a stateless document retrieval pipeline.
        
        Args:
            config_dict: Configuration dictionary
            
        Returns:
            Stateless retrieval pipeline instance
        """
        try:
            # Store config in Redis and get hash
            config_hash = await stateless_state.store_pipeline_config(
                config_dict, "retrieval"
            )
            
            # Create lightweight pipeline instance
            pipeline = StatelessRetrievalPipeline(config_hash, config_dict)
            
            logger.debug(f"Created stateless retrieval pipeline: {config_hash}")
            return pipeline
            
        except Exception as e:
            logger.error(f"Error creating retrieval pipeline: {e}")
            raise

class StatelessIngestionPipeline:
    """
    Stateless data ingestion pipeline that uses connection pools.
    No heavy objects cached in memory - all resources obtained from pools.
    """
    
    def __init__(self, config_hash: str, config_dict: Dict[str, Any]):
        self.config_hash = config_hash
        self.config_dict = config_dict
        self.session_id = f"ingestion_{uuid.uuid4().hex[:12]}"
        self.created_at = datetime.utcnow()
        
        # Create ProcessingConfig object for compatibility
        self.config = ProcessingConfig(**config_dict)
        
        logger.debug(f"Stateless ingestion pipeline created: {self.session_id}")
    
    @contextmanager
    def get_pipeline_resources(self):
        """
        Context manager to get all required resources from pools.
        Ensures proper cleanup after use.
        """
        resources = {}
        
        try:
            # Get resources from connection pools
            with connection_pools.get_gemini_client(self.config.gemini_api_key) as gemini_client:
                resources['gemini_client'] = gemini_client
                
                with connection_pools.get_qdrant_client(
                    url=self.config.qdrant_url,
                    api_key=self.config.qdrant_api_key,
                    prefer_grpc=self.config.prefer_grpc,
                    grpc_port=self.config.grpc_port
                ) as qdrant_client:
                    resources['qdrant_client'] = qdrant_client
                    
                    with connection_pools.get_embedding_client(
                        api_url=self.config.embedding_api_url,
                        timeout=60,
                        retry_attempts=5
                    ) as embedding_client:
                        resources['embedding_client'] = embedding_client
                        
                        # Create minimal pipeline wrapper
                        pipeline_wrapper = IngestionPipelineWrapper(
                            config=self.config,
                            resources=resources,
                            session_id=self.session_id
                        )
                        
                        yield pipeline_wrapper
                        
        except Exception as e:
            logger.error(f"Error getting pipeline resources: {e}")
            raise
        finally:
            # Resources are automatically cleaned up by context managers
            logger.debug(f"Released pipeline resources: {self.session_id}")
    
    async def process_documents(self, documents, username: str, 
                              collection_name: str, user_id: Optional[int] = None):
        """
        Process documents using stateless pipeline.
        """
        try:
            # Store session info
            await stateless_state.store_processing_session(self.session_id, {
                "type": "ingestion",
                "username": username,
                "collection_name": collection_name,
                "user_id": user_id,
                "document_count": len(documents)
            })
            
            # Process documents using resource context manager
            with self.get_pipeline_resources() as pipeline:
                result = await pipeline.process_documents(
                    documents, username, collection_name, user_id
                )
            
            # Update session status
            await stateless_state.update_session_status(
                self.session_id, "completed", {"result": result}
            )
            
            return result
            
        except Exception as e:
            await stateless_state.update_session_status(
                self.session_id, "failed", {"error": str(e)}
            )
            raise

class StatelessRetrievalPipeline:
    """
    Stateless document retrieval pipeline that uses connection pools.
    """
    
    def __init__(self, config_hash: str, config_dict: Dict[str, Any]):
        self.config_hash = config_hash
        self.config_dict = config_dict
        self.session_id = f"retrieval_{uuid.uuid4().hex[:12]}"
        self.created_at = datetime.utcnow()
        
        # Create RetrieverConfig object for compatibility
        self.config = RetrieverConfig(**config_dict)
        
        logger.debug(f"Stateless retrieval pipeline created: {self.session_id}")
    
    @contextmanager
    def get_pipeline_resources(self):
        """
        Context manager to get all required resources from pools.
        """
        resources = {}
        
        try:
            # Get resources from connection pools
            with connection_pools.get_gemini_client(self.config.gemini_api_key) as gemini_client:
                resources['gemini_client'] = gemini_client
                
                with connection_pools.get_qdrant_client(
                    url=self.config.qdrant_url,
                    api_key=self.config.qdrant_api_key,
                    prefer_grpc=self.config.prefer_grpc,
                    grpc_port=self.config.grpc_port
                ) as qdrant_client:
                    resources['qdrant_client'] = qdrant_client
                    
                    with connection_pools.get_embedding_client(
                        api_url=self.config.embedding_api_url,
                        timeout=60,
                        retry_attempts=3
                    ) as embedding_client:
                        resources['embedding_client'] = embedding_client
                        
                        with connection_pools.get_http_session("reranking") as http_session:
                            resources['http_session'] = http_session
                            
                            # Create minimal pipeline wrapper
                            pipeline_wrapper = RetrievalPipelineWrapper(
                                config=self.config,
                                resources=resources,
                                session_id=self.session_id
                            )
                            
                            yield pipeline_wrapper
                            
        except Exception as e:
            logger.error(f"Error getting retrieval resources: {e}")
            raise
        finally:
            logger.debug(f"Released retrieval resources: {self.session_id}")
    
    async def query_documents(self, query: str, collection_name: str, 
                            username: str, **kwargs):
        """
        Query documents using stateless pipeline.
        """
        try:
            # Store session info
            await stateless_state.store_processing_session(self.session_id, {
                "type": "retrieval",
                "query": query[:100],  # Truncate for storage
                "username": username,
                "collection_name": collection_name
            })
            
            # Query documents using resource context manager
            with self.get_pipeline_resources() as pipeline:
                result = pipeline.query_documents(
                    query, collection_name, username, **kwargs
                )
            
            # Update session status
            await stateless_state.update_session_status(
                self.session_id, "completed", {"success": result.get("success", False)}
            )
            
            return result
            
        except Exception as e:
            await stateless_state.update_session_status(
                self.session_id, "failed", {"error": str(e)}
            )
            raise

class IngestionPipelineWrapper:
    """
    Lightweight wrapper for ingestion pipeline that uses pooled resources.
    """
    
    def __init__(self, config: ProcessingConfig, resources: Dict[str, Any], session_id: str):
        self.config = config
        self.resources = resources
        self.session_id = session_id
        
        # Import here to avoid circular imports
        # from ..Data_Ingestion.pipeline import MultimodalDataPipeline

        # TODO: Use HTTP calls to separate data ingestion service
        
        # Create pipeline instance with pooled resources
        self.pipeline = MultimodalDataPipeline.__new__(MultimodalDataPipeline)
        self._initialize_pipeline_with_resources()
    
    def _initialize_pipeline_with_resources(self):
        """Initialize pipeline using pooled resources instead of creating new ones."""
        # Import HTTP client for embedding service communication
        from ..Data_Retriever.embedding_http_client import DenseEmbeddingWrapper, SparseEmbeddingWrapper
        from langchain.text_splitter import RecursiveCharacterTextSplitter
        # from ..Data_Ingestion.core import custom_markdown_splitter

        # TODO: Import core functions from separate services
        
        # Set configuration
        self.pipeline.config = self.config
        
        # Use pooled resources instead of creating new ones
        self.pipeline.gemini_client = self.resources['gemini_client']
        self.pipeline.qdrant_client = self.resources['qdrant_client']
        self.pipeline.embedding_client = self.resources['embedding_client']
        
        # Set up other lightweight components
        self.pipeline.llm_model = self.config.llm_model
        self.pipeline.parallel_workers = getattr(self.config, "llm_parallel_workers", 4)
        
        # Initialize text splitter (lightweight)
        self.pipeline.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap,
            separators=["\n\n", "\n", " ", ""]
        )
        self.pipeline.markdown_splitter = custom_markdown_splitter
        
        # Create wrapper classes
        self.pipeline.dense_embeddings = DenseEmbeddingWrapper(
            self.pipeline.embedding_client, normalize=True
        )
        self.pipeline.sparse_embeddings = SparseEmbeddingWrapper(
            self.pipeline.embedding_client
        )
        
        # Initialize timing stats
        self.pipeline.timing_stats = {
            'file_conversion': 0,
            'text_and_image_extraction': 0,
            'text_chunking': 0,
            'content_enhancement': 0,
            'image_description': 0,
            'vector_store_ops': 0,
            'db_operations': 0,
            'total_time': 0
        }
        
        # Dynamic per-request variables
        self.pipeline.current_collection_name = None
        self.pipeline.current_vector_store = None
        self.pipeline.processed_chunks = []
        self.pipeline.processed_images = []
        self.pipeline.raw_data = []
        
        # Set up logging (lightweight)
        self.pipeline.logger = logging.getLogger(f"pipeline.{self.session_id}")
    
    async def process_documents(self, documents, username: str, 
                              collection_name: str, user_id: Optional[int] = None):
        """Process documents using the wrapper pipeline."""
        # Import the original pipeline functions
        # from ..Data_Ingestion.data_ingestion_pipeline import process_documents

        # TODO: Use HTTP calls to separate data ingestion service
        
        # Call the original function but with our stateless pipeline instance
        return await process_documents(
            documents=documents,
            username=username,
            collection_name=collection_name,
            gemini_api_key=self.config.gemini_api_key,
            user_id=user_id,
            processing_config=self.config.__dict__,
            pipeline_instance=self.pipeline  # Pass our stateless instance
        )

class RetrievalPipelineWrapper:
    """
    Lightweight wrapper for retrieval pipeline that uses pooled resources.
    """
    
    def __init__(self, config: RetrieverConfig, resources: Dict[str, Any], session_id: str):
        self.config = config
        self.resources = resources
        self.session_id = session_id
        
        # Import here to avoid circular imports
        from ..Data_Retriever.enhanced_pipeline import EnhancedMultiModalRAGPipeline
        
        # Create pipeline instance with pooled resources
        self.pipeline = EnhancedMultiModalRAGPipeline.__new__(EnhancedMultiModalRAGPipeline)
        self._initialize_pipeline_with_resources()
    
    def _initialize_pipeline_with_resources(self):
        """Initialize pipeline using pooled resources."""
        # Import HTTP client for embedding service communication
        from ..Data_Retriever.embedding_http_client import DenseEmbeddingWrapper, SparseEmbeddingWrapper
        
        # Set configuration
        self.pipeline.config = self.config
        self.pipeline.gemini_api_key = self.config.gemini_api_key
        self.pipeline.qdrant_url = self.config.qdrant_url
        self.pipeline.qdrant_api_key = self.config.qdrant_api_key
        self.pipeline.prefer_grpc = self.config.prefer_grpc
        self.pipeline.grpc_port = self.config.grpc_port
        self.pipeline.query_llm = self.config.query_llm
        self.pipeline.response_model = self.config.response_model
        self.pipeline.reranking_service_url = self.config.reranking_service_url
        self.pipeline.reranking_timeout = 30
        
        # Use pooled resources
        self.pipeline.gemini_client = self.resources['gemini_client']
        self.pipeline.qdrant_client = self.resources['qdrant_client']
        self.pipeline.embedding_client = self.resources['embedding_client']
        
        # Create wrapper classes
        self.pipeline.embeddings = DenseEmbeddingWrapper(
            self.pipeline.embedding_client, normalize=True
        )
        self.pipeline.sparse_embeddings = SparseEmbeddingWrapper(
            self.pipeline.embedding_client
        )
        
        # Dynamic per-request variables
        self.pipeline.current_collection_name = None
        self.pipeline.current_response_instructions = None
        self.pipeline.current_db_path = None
        self.pipeline.current_vector_store = None
        self.pipeline.current_query_classifier = None
        self.pipeline.file_filter = None
        self.pipeline.allowed_files = None
        self.pipeline.department = None
        self.pipeline.extra_filter = None
        
        # Set up the graph (lightweight)
        self.pipeline._setup_graph()
    
    def query_documents(self, query: str, collection_name: str, 
                       username: str, **kwargs):
        """Query documents using the wrapper pipeline."""
        # Import the original function
        from ..Data_Retriever.retrieval_pipeline import query_documents
        
        # Call the original function but with our stateless pipeline
        return query_documents(
            query=query,
            collection_name=collection_name,
            username=username,
            retriever_config=self.config.__dict__,
            query_params=kwargs,
            gemini_api_key=self.config.gemini_api_key,
            chat_history=kwargs.get('chat_history', []),
            pipeline_instance=self.pipeline  # Pass our stateless instance
        )

# Global stateless factory instance
stateless_factory = StatelessPipelineFactory()
