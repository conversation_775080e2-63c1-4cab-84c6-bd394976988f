version: '3.9'

services:
  # Nginx Reverse Proxy with SSL
  nginx:
    image: nginx:alpine
    container_name: nginx_proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./STAR_processvenue_tech:/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - workplace_slm
      - techsarthai_default
    restart: unless-stopped

  # Backend
  backend:
    build:
      context: ./Backend
      dockerfile: Dockerfile
    container_name: ai_sarthi_backend
    environment:
      - QDRANT_URL=http://qdrant_service:6333
      - REDIS_URL=redis://redis:6379/0
      - DATA_INGESTION_URL=http://data_ingestion_service:8020
      # - DATA_RETRIEVER_URL=http://data_retriever_service:8003
      - NEXT_PUBLIC_API_URL=https://slm.processvenue.tech/api/v1
      - PUBLIC_API_BASE_URL=https://slm.processvenue.tech
      # MQTT Configuration
      - MQTT_HOST=emqx
      - MQTT_PORT=1883
      - MQTT_WS_PORT=8083
      - MQTT_WSS_PORT=8084
      - MQTT_CLIENT_ID_PREFIX=backend
      - MQTT_BROKER_JWT_SECRET=664c8ab2b248b876d27c6deecaab01d3337d4f39b74e451053
      - MQTT_BROKER_JWT_EXPIRE_MINUTES=720
      - MQTT_DEDUPE_TTL=300
    volumes:
      - ./Backend:/app
    networks:
      - workplace_slm
      - techsarthai_default
    restart: unless-stopped
    expose:
      - "8000"

  # MQTT Service (handles real-time chat messages)
  mqtt_service:
    build:
      context: ./Backend
      dockerfile: Dockerfile
    container_name: ai_sarthi_mqtt_service
    command: python mqtt_service.py
    environment:
      - QDRANT_URL=http://qdrant_service:6333
      - REDIS_URL=redis://redis:6379/0
      - MQTT_HOST=emqx
      - MQTT_PORT=1883
      - MQTT_WS_PORT=8083
      - MQTT_WSS_PORT=8084
      - MQTT_CLIENT_ID_PREFIX=backend
      - MQTT_BROKER_JWT_SECRET=664c8ab2b248b876d27c6deecaab01d3337d4f39b74e451053
      - MQTT_BROKER_JWT_EXPIRE_MINUTES=720
      - MQTT_DEDUPE_TTL=300
    volumes:
      - ./Backend:/app
    networks:
      - workplace_slm
      - techsarthai_default
    restart: unless-stopped
    expose:
      - "8002"
    depends_on:
      - backend

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_API_URL: https://slm.processvenue.tech/api/v1
    container_name: ai_sarthi_frontend
    environment:
      - NEXT_PUBLIC_API_URL=https://slm.processvenue.tech/api/v1
    networks:
      - workplace_slm
      - techsarthai_default
    restart: unless-stopped
    expose:
      - "3000"
    depends_on:
      - backend
 
 

  # Note: Using existing external containers for qdrant, redis, dozzle, data_ingestion, data_retriever
  # These services are commented out because they're already running externally
  data_ingestion:
    container_name: data_ingestion_service
    build:
      context: ./Data_Ingestion_Service
      dockerfile: Dockerfile
    env_file:
      - .env
    ports:
      - "8020:8020"
    restart: unless-stopped
    deploy:  # Optional: allow access to GPUs if available
      resources:
        reservations:
          devices:
            - driver: nvidia
              capabilities: ["gpu"]
    networks:
      - workplace_slm
      - techsarthai_default

  data_retriever:
    container_name: data_retriever_service
    build:
      context: ./Data_Retriever_Service
      dockerfile: Dockerfile
    env_file:
      - .env
    ports:
      - "8040:8040"
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              capabilities: ["gpu"]
    networks:
      - workplace_slm
      - techsarthai_default

volumes:
  qdrant_data:
  redis_data:
  nginx_logs:

networks:
  workplace_slm:
    driver: bridge
  techsarthai_default:
    external: true