import React, { createContext, useContext, useState, useCallback, ReactNode, useEffect } from 'react';
import { Agent, ChatMessage, queryAgent, generateFollowups, SearchSource, DocumentChunkSource, getAgents, API_BASE_URL } from '../lib/api';

// Add FileInfo interface
interface FileInfo {
  filename: string;
  indexed: boolean;
  title: string;
}

// Extended message interface to include sources, reasoning, and progress
interface ExtendedChatMessage extends ChatMessage {
  sources?: SearchSource[][] | string[] | DocumentChunkSource[];
  webSearchEnabled?: boolean;
  documentSearchEnabled?: boolean;
  deepSearchEnabled?: boolean;
  reasoning?: Array<{agent: string; reasoning: string}>;
  progressMessages?: string[];
  responseId?: string;  // UUID for tracking feedback
  cached?: boolean;     // Whether response came from cache
  cacheConfidence?: number; // Confidence score for cached responses
  followUps?: string[];
}

interface ContextUsageState {
  percentUsed: number;
  usedTokens: number;
  remainingTokens: number;
  inputBudget: number;
  stale: boolean;
}

interface ChatContextType {
  selectedAgent: Agent | null;
  setSelectedAgent: (agent: Agent | null) => void;
  messages: ExtendedChatMessage[];
  setMessages: (messages: ExtendedChatMessage[]) => void;
  addMessage: (message: ExtendedChatMessage) => void;
  sendMessage: (content: string) => Promise<void>;
  isLoading: boolean;
  isLoadingSession: boolean;
  setIsLoadingSession: (loading: boolean) => void;
  isGeneratingFollowups: boolean;
  stopProcessing: () => void;
  clearChat: () => void;
  currentSessionId: string;
  setCurrentSessionId: (sessionId: string) => void;
  startNewChat: () => void;
  switchToSession: (sessionId: string) => void;
  useWebSearch: boolean;
  setUseWebSearch: (value: boolean) => void;
  useDocumentSearch: boolean;
  setUseDocumentSearch: (value: boolean) => void;
  useDeepSearch: boolean;
  setUseDeepSearch: (value: boolean) => void;
  selectedFiles: string[];
  setSelectedFiles: (files: string[]) => void;
  currentSearchSpaceId: number | null;
  setCurrentSearchSpaceId: (spaceId: number | null) => void;
  availableFiles: FileInfo[]; 
  currentQuerySessionId: string | null;
  spaces: any[];
  contextUsageState: ContextUsageState | null;
  updateContextUsage: (contextMeta: any) => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export function ChatProvider({ children }: { children: ReactNode }) {
  const [selectedAgent, setSelectedAgentState] = useState<Agent | null>(null);
  // Automatically load and select the default agent on mount (only once)
  useEffect(() => {
    let isMounted = true;
    
    const loadDefaultAgent = async () => {
      if (selectedAgent !== null) return; // Already loaded
      
      try {
        const agents = await getAgents();
        if (isMounted && agents.length > 0) {
          setSelectedAgent(agents[0]);
        }
      } catch (e) {
        console.error('Failed to load default agent', e);
      }
    };
    
    loadDefaultAgent();
    
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array to run only once

  const [messages, setMessages] = useState<ExtendedChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingFollowups, setIsGeneratingFollowups] = useState(false);
  const [useWebSearch, setUseWebSearch] = useState(false);
  const [useDocumentSearch, setUseDocumentSearch] = useState(false);
  const [useDeepSearch, setUseDeepSearch] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [availableFiles, setAvailableFiles] = useState<FileInfo[]>([]);  // Changed from string[] to FileInfo[]
  const [spaces, setSpaces] = useState<any[]>([]);
  const [currentSearchSpaceId, setCurrentSearchSpaceId] = useState<number | null>(null);
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const [currentQuerySessionId, setCurrentQuerySessionId] = useState<string | null>(null);
  const [currentSessionId, setCurrentSessionId] = useState<string>(() => {
    // Always generate a new session ID on page refresh/load
    const newSessionId = `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem('current_chat_session_id', newSessionId);
    return newSessionId;
  });
  const [contextUsageState, setContextUsageState] = useState<ContextUsageState | null>(null);
  const [isLoadingSession, setIsLoadingSession] = useState(false);

  // Update context usage from backend context_meta
  const updateContextUsage = useCallback((contextMeta: any) => {
    if (!contextMeta || !contextMeta.token_stats) {
      return;
    }

    const tokenStats = contextMeta.token_stats;
    const inputBudget = tokenStats.input_budget || 0;
    const contextTokensUsed = tokenStats.context_tokens_used || 0;
    
    const percentUsed = inputBudget > 0 
      ? Math.min(100, Math.max(0, Math.round((contextTokensUsed / inputBudget) * 100 * 10) / 10))
      : 0;
    
    const remainingTokens = Math.max(0, inputBudget - contextTokensUsed);

    setContextUsageState({
      percentUsed,
      usedTokens: contextTokensUsed,
      remainingTokens,
      inputBudget,
      stale: false
    });
  }, []);

  // Clear selected files and available files when agent changes
  const setSelectedAgent = useCallback((agent: Agent | null) => {
    setSelectedAgentState(agent);
    setSelectedFiles([]);
    setAvailableFiles([]);
    setMessages([]); // Clear current messages first
  }, []);

  const addMessage = useCallback((message: ExtendedChatMessage) => {
    setMessages((prevMessages) => [...prevMessages, message]);
  }, []);

  const clearChat = useCallback(() => {
    setMessages([]);
  }, []);

  const startNewChat = useCallback(() => {
    const newSessionId = `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem('current_chat_session_id', newSessionId);
    setCurrentSessionId(newSessionId);
    // Reset all in-memory chat state for a clean session
    setMessages([]);
    setUseWebSearch(false);
    setUseDocumentSearch(false);
    setUseDeepSearch(false);
    setSelectedFiles([]);
    setCurrentSearchSpaceId(null);
    // Hide context usage until the first response or hydration returns data
    setContextUsageState(null);
  }, []);

  const switchToSession = useCallback((sessionId: string) => {
    // Don't reload if it's the same session
    if (sessionId === currentSessionId) {
      //console.log('Already in this session, skipping switch');
      return;
    }
    
    setIsLoadingSession(true);
    localStorage.setItem('current_chat_session_id', sessionId);
    setCurrentSessionId(sessionId);
    // Clear current messages, ChatHistoryManager will load the session's messages
    setMessages([]);
    // Hide any stale context usage while the new session hydrates
    setContextUsageState(null);
  }, [currentSessionId]);

  const stopProcessing = useCallback(async () => {
    // Immediately stop frontend processing for instant feedback
    setIsLoading(false);
    
    // Abort the frontend request first
    if (abortController) {
      abortController.abort();
      setAbortController(null);
    }
    
    // Cancel backend processing (don't wait for it)
    if (currentQuerySessionId) {
      // Fire and forget - don't await this to avoid UI delay
      fetch(`${API_BASE_URL}/agents/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify({
          query_session_id: currentQuerySessionId
        }),
      }).then(response => {
        if (response.ok) {
          //console.log('Backend query processing cancelled successfully');
        } else {
          console.warn('Failed to cancel backend processing:', response.status);
        }
      }).catch(error => {
        console.error('Error cancelling backend processing:', error);
      });
    }
    
    setCurrentQuerySessionId(null);
    
    // Add a message indicating the process was stopped
    addMessage({
      role: 'assistant',
      content: 'Processing was stopped by user.'
    });
  }, [abortController, addMessage, currentQuerySessionId]);

  const sendMessage = useCallback(
    async (content: string) => {
      if (!selectedAgent) {
        console.error('No agent selected');
        return;
      }

      // Add user message to chat
      const userMessage: ExtendedChatMessage = { role: 'user', content };
      addMessage(userMessage);
      
      // Create abort controller for this request
      const controller = new AbortController();
      const querySessionId = `query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      setAbortController(controller);
      setCurrentQuerySessionId(querySessionId);
      setIsLoading(true);
      
      try {
        // Use unified query endpoint for web search, document search, and deep search
        const response = await queryAgent({
          agent_type: selectedAgent.id,
          query: content,
          chat_history: { messages },
          use_web_search: useWebSearch,
          use_document_search: useDocumentSearch,
          use_deep_search: useDeepSearch,
          chat_session_id: currentSessionId,
          query_session_id: querySessionId,
          allowed_files: useDocumentSearch && selectedFiles.length > 0 ? selectedFiles : undefined,
          space_id: useDocumentSearch ? (currentSearchSpaceId || undefined) : undefined
        }, controller.signal);
        
        // Update context usage if context_meta is present
        if (response.context_meta) {
          updateContextUsage(response.context_meta);
        }

        // Add assistant response to chat
        const assistantMessage: ExtendedChatMessage = {
          role: 'assistant',
          content: response.answer,
          sources: response.sources,
          webSearchEnabled: response.web_search_enabled === true,
          documentSearchEnabled: response.document_search_enabled === true,
          deepSearchEnabled: response.deep_search_enabled === true,
          reasoning: response.reasoning || [],
          progressMessages: response.progress_messages || [],
          responseId: response.response_id,
          cached: response.cached || false,
          cacheConfidence: response.cache_confidence,
          followUps: response.suggested_followups || []
        };
        
    
        // Display assistant response immediately
        addMessage(assistantMessage);
        
        // Set main loading to false since response is now shown
        setIsLoading(false);
        
        // Then fetch follow-up suggestions asynchronously
        setIsGeneratingFollowups(true);
        try {
          const followUps = await generateFollowups({
            chat_history: { messages: [...messages, userMessage] },
            latest_query: content,
            latest_answer: response.answer,
            latest_sources: response.sources,
            response_id: response.response_id,
          });
          // Update the last assistant message with followUps
          setMessages(prev => prev.map(msg =>
            msg.responseId === response.response_id
              ? { ...msg, followUps } as typeof msg
              : msg
          ));
        } catch (e) {
          console.error('Error fetching follow-ups:', e);
        } finally {
          setIsGeneratingFollowups(false);
        }
      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
          return; // Don't add error message for user-initiated cancellation
        }
        
        console.error('Error sending message:', error);
        
        // Add error message to chat
        addMessage({
          role: 'assistant',
          content: 'Sorry, I encountered an error processing your request. Please try again later.'
        });
      } finally {
        // Only set loading to false if we haven't already (in case of errors before response)
        setIsLoading(false);
        setAbortController(null);
        setCurrentQuerySessionId(null);
      }
    },
    [selectedAgent, messages, addMessage, useWebSearch, useDocumentSearch, useDeepSearch, selectedFiles, updateContextUsage]
  );

  // Hydrate context usage on mount or session switch
  useEffect(() => {
    const hydrateContextUsage = async () => {
      if (!selectedAgent || useDeepSearch) {
        // Don't hydrate for deep search or when no agent selected
        return;
      }

      try {
        const { getContextMeta } = await import('../lib/api');
        const result = await getContextMeta(selectedAgent.id, currentSessionId);
        
        if (result.context_meta) {
          updateContextUsage(result.context_meta);
        }
      } catch (error) {
        // Silently fail hydration - not critical
      }
    };

    hydrateContextUsage();
  }, [selectedAgent, currentSessionId, useDeepSearch, updateContextUsage]);

  const value = {
    selectedAgent,
    setSelectedAgent,
    messages,
    setMessages,
    addMessage,
    sendMessage,
    isLoading,
    isLoadingSession,
    setIsLoadingSession,
    isGeneratingFollowups,
    stopProcessing,
    clearChat,
    currentSessionId,
    setCurrentSessionId,
    startNewChat,
    switchToSession,
    useWebSearch,
    setUseWebSearch,
    useDocumentSearch,
    setUseDocumentSearch,
    useDeepSearch,
    setUseDeepSearch,
    selectedFiles,
    setSelectedFiles,
    currentSearchSpaceId,
    setCurrentSearchSpaceId,
    availableFiles,
    // loadAvailableFiles,
    // forceRefreshAvailableFiles,
    currentQuerySessionId,
    spaces,
    contextUsageState,
    updateContextUsage
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
}

export function useChat() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
} 
