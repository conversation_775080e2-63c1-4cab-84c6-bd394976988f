# import logging
# import json
# from typing import Optional

# from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException, status
# from pydantic import BaseModel, Field

# from .. import config
# from ..services.websocket_manager import manager


# logger = logging.getLogger(__name__)

# router = APIRouter(prefix="/external", tags=["ExternalCallbacks"])


# class IngestionProgressEvent(BaseModel):
#     job_id: str = Field(..., description="Unique job identifier across the ingestion batch")
#     user_id: int = Field(..., description="Recipient user ID to notify via WebSocket")
#     document_id: Optional[int] = Field(None, description="Knowledge document ID if available")
#     filename: Optional[str] = None
#     step: str = Field(..., description="Pipeline step name")
#     status: str = Field(..., description="one of: started|running|completed|failed")
#     progress_pct: Optional[float] = Field(None, ge=0, le=100)
#     message: Optional[str] = None
#     metrics: Optional[dict] = None
