'use client';

import React from 'react';
import { NotesModalProps } from '@/types/note';
import { useNoteModal } from '@/hooks';
import NoteContentPreview from './NoteContentPreview';
import NoteSourcesList from './NoteSourcesList';
import NoteFullPreviewModal from './NoteFullPreviewModal';

export default function NotesModal({ isOpen, onClose, preset, onSaved }: NotesModalProps) {
  const {
    title,
    setTitle,
    extraText,
    setExtraText,
    combined,
    setCombined,
    saving,
    error,
    isEditing,
    setIsEditing,
    isFullPreviewOpen,
    setIsFullPreviewOpen,
    canSave,
    sourceLinks,
    parsedContent,
    handleSave,
  } = useNoteModal({ isOpen, preset, onSaved, onClose });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-3xl mx-4 shadow-2xl max-h-[90dvh] sm:max-h-[80dvh] flex flex-col overflow-hidden" onClick={(e) => e.stopPropagation()}>
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 className="text-base font-bold text-gray-800 text-xl">Save to Notes</h3>
          <button
            onClick={onClose}
            className="text-gray-600 hover:text-gray-800"
            aria-label="Close"
            title="Close"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6 space-y-4 flex-1 min-h-0 overflow-y-auto">
          <div>
            <label className="block text-sm text-gray-700 mb-1">Title</label>
            <input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              maxLength={255}
              placeholder="Brief title"
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <NoteContentPreview
            combined={combined}
            setCombined={setCombined}
            isEditing={isEditing}
            setIsEditing={setIsEditing}
            parsedContent={parsedContent}
            onOpenFullPreview={() => setIsFullPreviewOpen(true)}
          />

          <NoteSourcesList sourceLinks={sourceLinks} />

          <div>
            <label className="block text-sm text-gray-700 mb-1">Additional notes (optional)</label>
            <textarea
              value={extraText}
              onChange={(e) => setExtraText(e.target.value)}
              rows={4}
              className="w-full border border-gray-300 rounded-md px-3 py-4 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Add your own context or annotations"
            />
          </div>

          {error && <div className="text-sm text-red-600">{error}</div>}
        </div>

        <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-100 text-gray-600 rounded-md hover:bg-gray-200"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!canSave || saving}
            className="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {saving ? 'Saving…' : 'Save Note'}
          </button>
        </div>
      </div>

      <NoteFullPreviewModal
        isOpen={isFullPreviewOpen}
        onClose={() => setIsFullPreviewOpen(false)}
        combined={combined}
        parsedContent={parsedContent}
      />
    </div>
  );
}



