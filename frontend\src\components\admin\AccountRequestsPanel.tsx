'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { API_BASE_URL } from '@/lib/api';
import AccountRequestCard from './AccountRequestCard';
import AccountRequestModal from './AccountRequestModal';
import { LoadingSpinner } from '../ui/LoadingSpinner';

interface User {
  id: number;
  username: string;
  email: string;
  industry: string;
  role: string;
  status: string;
  is_active: boolean;
  created_at: string;
}

interface AccountRequestsPanelProps {
  refreshTrigger?: number;
}

export default function AccountRequestsPanel({ refreshTrigger }: AccountRequestsPanelProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState<'pending' | 'approved' | 'rejected'>('pending');
  const router = useRouter();
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showModal, setShowModal] = useState(false);

  // Get current user once on mount
  useEffect(() => {
    const data = localStorage.getItem('user');
    if (data) {
      try {
        setCurrentUser(JSON.parse(data));
      } catch {}
    }
  }, []);

  // Fetch users whenever activeTab changes
  useEffect(() => {
    fetchUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]);

  // Fetch users when dashboard refresh button is clicked
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) {
      fetchUsers(true); // Force refresh when dashboard refresh is triggered
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshTrigger]);

  // Check if the current user is admin
  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      const user = JSON.parse(userData);
      if (user.role !== 'admin' && user.role !== 'subadmin') {
        router.push('/');
      }
    } else {
      router.push('/login');
    }
  }, [router]);

  const fetchUsers = async (forceRefresh = false) => {
    setLoading(true);

    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Fetch data with cache busting for force refresh
      const endpoint = `${API_BASE_URL}/account-requests/?status_filter=${activeTab}`;
      const response = await fetch(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Cache-Control': forceRefresh ? 'no-cache, no-store, must-revalidate' : 'default',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();
      setUsers(data);
      setError(''); // Clear any previous errors
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (userId: number, newStatus: 'approve' | 'reject') => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE_URL}/account-requests/${userId}/${newStatus}`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to ${newStatus} user`);
      }

      // Update local state optimistically
      setUsers((prev) => prev.filter((u) => u.id !== userId));

      // Notify other admin views (e.g., Dashboard user metrics) to refresh
      try {
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('dashboard_metrics_refresh'));
        }
        localStorage.setItem('dashboard_metrics_refresh', String(Date.now()));
      } catch {}
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const openUserModal = (user: User) => {
    setSelectedUser(user);
    setShowModal(true);
  };

  return (
    <section className="mt-8">
      <div className="text-left mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Account Requests</h2>
        <p className="mt-1 text-sm text-gray-600">Review and manage user account requests</p>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="inline-flex -mb-px rounded-lg shadow-sm bg-gray-100 p-1">
          {(['pending', 'approved', 'rejected'] as const).map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors duration-150 focus:outline-none ${
                activeTab === tab
                  ? 'bg-white shadow text-blue-900'
                  : 'text-gray-600 hover:bg-white hover:shadow'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </nav>
      </div>

      {loading ? (
        <LoadingSpinner size="md" message="Loading..." />
      ) : users.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500">No {activeTab} account requests found.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {users.map((user) => (
            <AccountRequestCard
              key={user.id}
              user={user}
              onClick={(user) => openUserModal(user)}
              currentUserId={currentUser?.id}
              onApprove={(userId) => handleStatusChange(userId, 'approve')}
              onReject={(userId) => handleStatusChange(userId, 'reject')}
            />
          ))}
        </div>
      )}

      {/* Account Request Modal */}
      <AccountRequestModal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setSelectedUser(null);
        }}
        user={selectedUser}
        currentUserId={currentUser?.id}
        onStatusChange={handleStatusChange}
      />
    </section>
  );
}
