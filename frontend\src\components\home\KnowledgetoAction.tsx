

export default function KnowledgeToAction() {

  return (
    <div className="relative w-full">
      {/* Background Container */}
      <div
        className="min-h-screen w-full"
        style={{
          background: `
            radial-gradient(circle at 94.35384114583333% 89.61588541666666%, #A53A9A 0%, rgba(165, 58, 154, 0) 59%),
            radial-gradient(circle at 0% 95%, #C0A6EA 0%, rgba(192, 166, 234, 0) 54%),
            radial-gradient(circle at 4.583333333333333% 15%, #5919C1 0%, rgba(89, 25, 193, 0) 100%),
            radial-gradient(circle at 73.33333333333333% 17.5%, #FAE34D 0%, rgba(250, 227, 77, 0) 100%),
            radial-gradient(circle at 48.9013671875% 49.521484375%, #000000 0%, rgba(0, 0, 0, 0) 100%)
          `,
        }}
      >
        {/* Content Wrapper */}
        <div className="min-h-screen flex flex-col items-center px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="w-full max-w-5xl text-center pt-8 md:pt-12">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-white mb-6">
              Transform Knowledge into Action <br /> with Workplace SLM Copilot
            </h1>
          </div>

        {/* Vertical Content Sections */}
        <div className="w-full mt-16 flex flex-col items-start md:items-center pl-4 md:pl-96 ">
          {[
            {
              title: "About Us",
              desc: "15+ years of transforming processes into intelligent partnerships.",
              link: "/about"
            },
            {
              title: "Updates",
              desc: "Stay informed with the latest features, security updates, and improvements tailored for your team.",
              link: "/whats-new"
            },
            {
              title: "User Guide",
              desc: "Learn how to use Workplace SLM effectively with our step-by-step guide designed for all users.",
              link: "/whats-new"
            },
          ].map((item, index) => (
            <div key={index} className="max-w-xl w-full text-left mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-semibold text-white mb-2">
                {item.title}
              </h2>
              <p className="text-gray-200 text-sm sm:text-base md:text-lg leading-relaxed mb-4">
                {item.desc}
              </p>

              {/* Button */}
              <button className="group bg-white/90 text-purple-700 px-6 py-3 rounded-full border border-white transition-all duration-300 flex items-center gap-2 hover:bg-transparent hover:text-white">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-6 h-6 -ml-3 transition-transform duration-300 group-hover:rotate-90"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                  />
                </svg>
                Learn More
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
    </div>
  )
}
