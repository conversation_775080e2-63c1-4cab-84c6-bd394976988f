"use client";
import { motion, Variants } from "framer-motion";

export default function HomeHero() {
  // text animation
  const textVariants: Variants = {
    hidden: { opacity: 0, y: 30, scale: 0.8 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { duration: 1, ease: "easeOut" },
    },
  };

  return (
    <div className="bg-white">
      {/* Banner Section */}
      <section className="w-full min-h-[60vh] flex flex-col md:flex-row justify-between items-center px-6 md:px-12 lg:px-20 py-12 md:py-16 gap-12 md:gap-10">
        {/* Left Side — Hero Text */}
        <div className="w-full md:w-[60%] text-center md:text-left">
          <motion.h1
            variants={textVariants}
            initial="hidden"
            animate="visible"
            className="
              font-extralight
              leading-[0.9]
              tracking-tight
              text-[clamp(40px,8vw,120px)]
              text-black
            "
          >
            <span className="block">Your Enterprise</span>
            <span className="block text-purple-600 font-light">
              AI Knowledge Assistant
            </span>
          </motion.h1>
        </div>

        {/* Right Side — Description */}
        <div className="w-full md:w-[38%] space-y-6 text-center md:text-right">
          <p className="text-gray-600 text-base md:text-lg leading-relaxed md:leading-loose text-justify md:text-right">
            Workplace SLM transforms your internal documents and data into a
            secure, "always-on" assistant. Upload policies, playbooks, or
            knowledge files and your team can simply ask questions and get
            instant, precise answers, summaries, and next steps.
          </p>

          <div className="space-y-2 md:space-y-1">
            <h3 className="text-orange-500 font-semibold text-base md:text-lg">
              Smart Insights from Your Docs |
            </h3>
            <h3 className="text-orange-500 font-semibold text-base md:text-lg">
              Secure Always |
            </h3>
            <h3 className="text-orange-500 font-semibold text-base md:text-lg">
              Docs to Decisions, All at one place |
            </h3>
          </div>
        </div>
      </section>
    </div>
  );
}

