import React, { useEffect, useCallback, useRef } from 'react';
import { Agent, getAgentChatHistory, clearAgentChatHistory } from '@/lib/api';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
  response_id?: string;
  sources?: any[];
  web_search_enabled?: boolean;
  document_search_enabled?: boolean;
  deep_search_enabled?: boolean;
  reasoning?: Array<{agent: string; reasoning: string}>;
  progress_messages?: string[];
  cached?: boolean;
  cache_confidence?: number;
  agent_name?: string;
  used_rag?: boolean;
  processing_stats?: any;
}

interface ExtendedChatMessage {
  role: 'user' | 'assistant';
  content: string;
  sources?: any[];
  webSearchEnabled?: boolean;
  documentSearchEnabled?: boolean;
  deepSearchEnabled?: boolean;
  reasoning?: Array<{agent: string; reasoning: string}>;
  progressMessages?: string[];
  responseId?: string;
  cached?: boolean;
  cacheConfidence?: number;
  // follow ups intentionally excluded from history to keep suggestions real-time only
}

interface ChatHistoryManagerProps {
  selectedAgent: Agent | null;
  currentSessionId: string;
  setMessages: (messages: ExtendedChatMessage[]) => void;

  onLoadingStateChange?: (isLoading: boolean) => void;
}

export default function ChatHistoryManager({
  selectedAgent,
  currentSessionId,
  setMessages,
  onLoadingStateChange
}: ChatHistoryManagerProps) {
  const isLoadingRef = useRef(false);
  const lastLoadedAgentRef = useRef<string | null>(null);

  const loadChatHistory = async () => {
    if (!selectedAgent) {
      //console.log('No agent selected, skipping chat history load');
      setMessages([]);
      lastLoadedAgentRef.current = null;
      onLoadingStateChange?.(false);
      return;
    }

    // Check if this is a new session (contains current timestamp)
    const isNewSession = currentSessionId.includes(Date.now().toString().substring(0, 10));
    if (isNewSession) {
      //console.log('New session detected, starting with empty chat history');
      setMessages([]);
      lastLoadedAgentRef.current = `${selectedAgent.id}:${currentSessionId}`;
      onLoadingStateChange?.(false);
      return;
    }

    // Prevent multiple concurrent loads for the same agent and session
    const currentKey = `${selectedAgent.id}:${currentSessionId}`;
    if (isLoadingRef.current) {
      //console.log('Already loading chat history, skipping duplicate request');
      return;
    }
    
    if (lastLoadedAgentRef.current === currentKey) {
      //console.log('Chat history already loaded for this session, skipping duplicate request');
      return;
    }

    isLoadingRef.current = true;
    onLoadingStateChange?.(true);

    try {
      const historyData = await getAgentChatHistory(selectedAgent.id, currentSessionId);
      
      if (historyData.messages && historyData.messages.length > 0) {
        // Convert Redis history to ExtendedChatMessage format
        const convertedMessages: ExtendedChatMessage[] = historyData.messages.map((msg: ChatMessage) => {
          let webSearch = msg.web_search_enabled || false;
          let documentSearch = msg.document_search_enabled || false;
          let deepSearch = msg.deep_search_enabled || false;

          // Infer flags if not explicitly set and sources exist
          if (!webSearch && !documentSearch && !deepSearch && msg.sources && msg.sources.length > 0) {
            const firstSource = msg.sources[0];
            if (Array.isArray(firstSource)) {
              // Nested array typically indicates web search
              webSearch = true;
            } else if (typeof firstSource === 'string') {
              // Array of strings typically indicates document search (legacy)
              documentSearch = true;
            } else if (firstSource && firstSource.title && firstSource.snippet) {
              // Flat array of objects could be deep search or web
              // For simplicity, assume web search if not nested
              webSearch = true;
            } else if (firstSource && firstSource.document && firstSource.snippet) {
              // Document chunk objects
              documentSearch = true;
            }
          }

          return {
            role: msg.role,
            content: msg.content,
            responseId: msg.response_id,
            sources: msg.sources || [],
            webSearchEnabled: webSearch,
            documentSearchEnabled: documentSearch,
            deepSearchEnabled: deepSearch,
            reasoning: msg.reasoning || [],
            progressMessages: msg.progress_messages || [],
            cached: msg.cached || false,
            cacheConfidence: msg.cache_confidence
          };
        });
        
        setMessages(convertedMessages);
        lastLoadedAgentRef.current = `${selectedAgent.id}:${currentSessionId}`;
      } else {
        //console.log('No chat history found');
        setMessages([]);
        lastLoadedAgentRef.current = `${selectedAgent.id}:${currentSessionId}`;
      }
    } catch (error) {
      console.error('Failed to load chat history:', error);
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      // Don't show error to user, just start with empty chat
      setMessages([]);
      lastLoadedAgentRef.current = `${selectedAgent.id}:${currentSessionId}`;
    } finally {
      isLoadingRef.current = false;
      onLoadingStateChange?.(false);
    }
  };

  const clearHistory = useCallback(async () => {
    if (!selectedAgent) {
      //console.log('No agent selected, skipping chat history clear');
      return false;
    }

    try {
      await clearAgentChatHistory(selectedAgent.id);
      
      setMessages([]);
      return true;
    } catch (error) {
      console.error('Failed to clear chat history:', error);
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      return false;
    }
  }, [selectedAgent, setMessages]);

  // Load chat history when agent changes or when session changes
  useEffect(() => {
    // Reset the last loaded agent when a new agent or session is selected
    const currentKey = selectedAgent ? `${selectedAgent.id}:${currentSessionId}` : null;
    if (selectedAgent && lastLoadedAgentRef.current !== currentKey) {
      // Only load history if we have a valid agent and session
      //console.log(`Loading chat history for ${currentKey}`);
      loadChatHistory();
    } else if (!selectedAgent) {
      setMessages([]);
      lastLoadedAgentRef.current = null;
    }
  }, [selectedAgent?.id, currentSessionId]); // Depend on both agent and session

  // Expose functions via refs or custom hook if needed
  // For now, this component handles loading automatically
  
  return null; // This component doesn't render anything
}

// Export functions for manual use
export { getAgentChatHistory, clearAgentChatHistory };
