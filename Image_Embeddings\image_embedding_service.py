import io
import base64
import logging
import time
from typing import List, Dict, Any, Optional
from contextlib import asynccontextmanager

import torch
import numpy as np
from PIL import Image
from transformers import AutoProcessor, AutoModel, AutoTokenizer, Blip2Processor, Blip2ForConditionalGeneration
from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from config import config

logging.basicConfig(
    level=getattr(logging, config.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ImageEmbeddingRequest(BaseModel):
    """Request model for image embedding"""
    images: List[str] = Field(..., description="List of base64 encoded images")


class ImageCaptionRequest(BaseModel):
    """Request model for image captioning"""
    images: List[str] = Field(..., description="List of base64 encoded images")
    max_length: Optional[int] = Field(100, description="Maximum caption length")


class EmbeddingResponse(BaseModel):
    """Response model for embeddings"""
    embeddings: List[List[float]]
    dimension: int
    processing_time: float


class CaptionResponse(BaseModel):
    """Response model for captions"""
    captions: List[str]
    processing_time: float


class ImageEmbeddingService:
    """Service for generating image embeddings and captions using SigLIP-2 + BLIP-2"""
    
    def __init__(self):
        self.device = None
        self.model = None
        self.processor = None
        self.tokenizer = None
        self.caption_model = None
        self.caption_processor = None
        
    def initialize(self):
        """Initialize the model and processor"""
        logger.info(f"Initializing Image Embedding Service with model: {config.model_name}")
        
        # Set device
        if config.device == "cuda" and torch.cuda.is_available():
            self.device = torch.device("cuda")
            logger.info(f"Using GPU: {torch.cuda.get_device_name(0)}")
            logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        else:
            self.device = torch.device("cpu")
            logger.info("Using CPU")
        
        # Load model and processor
        logger.info("Loading SigLIP-2 model...")
        start_time = time.time()
        
        try:
            self.processor = AutoProcessor.from_pretrained(
                config.model_name,
                cache_dir=config.cache_dir
            )
            
            self.model = AutoModel.from_pretrained(
                config.model_name,
                cache_dir=config.cache_dir
            ).to(self.device)
            
            # Set model to evaluation mode
            self.model.eval()
            
            # Load tokenizer for caption generation (if supported)
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    config.model_name,
                    cache_dir=config.cache_dir
                )
            except Exception as e:
                logger.warning(f"Could not load tokenizer: {e}")
                self.tokenizer = None
            
            load_time = time.time() - start_time
            logger.info(f"Model loaded successfully in {load_time:.2f} seconds")
            
            # Load BLIP-2 for caption generation
            logger.info("Loading BLIP-2 model for caption generation...")
            caption_start_time = time.time()
            
            try:
                self.caption_processor = Blip2Processor.from_pretrained(
                    "Salesforce/blip2-opt-2.7b",
                    cache_dir=config.cache_dir
                )
                
                self.caption_model = Blip2ForConditionalGeneration.from_pretrained(
                    "Salesforce/blip2-opt-2.7b",
                    cache_dir=config.cache_dir,
                    torch_dtype=torch.float16 if config.device == "cuda" else torch.float32
                ).to(self.device)
                
                self.caption_model.eval()
                
                caption_load_time = time.time() - caption_start_time
                logger.info(f"BLIP-2 caption model loaded successfully in {caption_load_time:.2f} seconds")
                
            except Exception as e:
                logger.warning(f"Could not load BLIP-2 caption model: {e}")
                logger.warning("Caption generation will use fallback method")
                self.caption_model = None
                self.caption_processor = None
            
            # Warm up the model
            self._warmup()
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def _warmup(self):
        """Warm up the model with a dummy image"""
        logger.info("Warming up model...")
        try:
            dummy_image = Image.new('RGB', (224, 224), color='white')
            inputs = self.processor(images=dummy_image, return_tensors="pt")
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                _ = self.model.get_image_features(**inputs)
            
            logger.info("Model warmup complete")
        except Exception as e:
            logger.warning(f"Warmup failed: {e}")
    
    def decode_base64_image(self, base64_string: str) -> Image.Image:
        """Decode base64 string to PIL Image"""
        try:
            image_bytes = base64.b64decode(base64_string)
            image = Image.open(io.BytesIO(image_bytes)).convert('RGB')
            return image
        except Exception as e:
            logger.error(f"Error decoding image: {e}")
            raise HTTPException(status_code=400, detail=f"Invalid image data: {str(e)}")
    
    def generate_embeddings(self, images: List[str]) -> Dict[str, Any]:
        """Generate embeddings for a list of base64 encoded images"""
        start_time = time.time()
        
        try:
            # Decode images
            pil_images = [self.decode_base64_image(img) for img in images]
            
            # Process images in batches
            all_embeddings = []
            
            for i in range(0, len(pil_images), config.batch_size):
                batch = pil_images[i:i + config.batch_size]
                
                # Preprocess images
                inputs = self.processor(images=batch, return_tensors="pt", padding=True)
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
                
                # Generate embeddings
                with torch.no_grad():
                    image_features = self.model.get_image_features(**inputs)
                    
                    # Normalize embeddings (important for similarity search)
                    image_features = image_features / image_features.norm(dim=-1, keepdim=True)
                    
                    # Convert to list
                    batch_embeddings = image_features.cpu().numpy().tolist()
                    all_embeddings.extend(batch_embeddings)
            
            processing_time = time.time() - start_time
            
            logger.info(f"Generated {len(all_embeddings)} embeddings in {processing_time:.3f}s")
            
            return {
                "embeddings": all_embeddings,
                "dimension": len(all_embeddings[0]) if all_embeddings else 0,
                "processing_time": processing_time
            }
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            raise HTTPException(status_code=500, detail=f"Error generating embeddings: {str(e)}")
    
    def generate_captions(self, images: List[str], max_length: int = 50) -> Dict[str, Any]:
        """Generate captions for a list of base64 encoded images using BLIP-2"""
        start_time = time.time()
        
        try:
            # Decode images
            pil_images = [self.decode_base64_image(img) for img in images]
            
            captions = []
            
            # Check if BLIP-2 caption model is available
            if self.caption_model is None or self.caption_processor is None:
                # Fallback: Generate descriptive text based on image properties
                logger.warning("BLIP-2 model not available, using fallback captions")
                
                for image in pil_images:
                    width, height = image.size
                    caption = f"Image with dimensions {width}x{height} pixels"
                    captions.append(caption)
            else:
                # Use BLIP-2 for proper caption generation
                logger.info(f"Generating captions for {len(pil_images)} images using BLIP-2")
                
                # Process images in batches
                for i in range(0, len(pil_images), config.batch_size):
                    batch = pil_images[i:i + config.batch_size]
                    
                    # Preprocess images
                    inputs = self.caption_processor(images=batch, return_tensors="pt")
                    inputs = {k: v.to(self.device) for k, v in inputs.items()}
                    
                    # Generate captions
                    with torch.no_grad():
                        generated_ids = self.caption_model.generate(
                            **inputs,
                            max_length=max_length,
                            num_beams=5,
                            early_stopping=True,
                            length_penalty=1.0
                        )
                        
                        # Decode captions
                        batch_captions = self.caption_processor.batch_decode(
                            generated_ids, 
                            skip_special_tokens=True
                        )
                        
                        # Clean up captions
                        batch_captions = [caption.strip() for caption in batch_captions]
                        captions.extend(batch_captions)
            
            processing_time = time.time() - start_time
            
            logger.info(f"Generated {len(captions)} captions in {processing_time:.3f}s")
            logger.info(f"Sample captions: {captions[:3]}")
            
            return {
                "captions": captions,
                "processing_time": processing_time
            }
            
        except Exception as e:
            logger.error(f"Error generating captions: {e}")
            raise HTTPException(status_code=500, detail=f"Error generating captions: {str(e)}")
    
    def get_text_embeddings(self, texts: List[str]) -> Dict[str, Any]:
        """Generate embeddings for text queries (for image-text retrieval)"""
        start_time = time.time()
        
        try:
            # Process texts
            inputs = self.processor(text=texts, return_tensors="pt", padding=True)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Generate embeddings
            with torch.no_grad():
                text_features = self.model.get_text_features(**inputs)
                
                # Normalize embeddings
                text_features = text_features / text_features.norm(dim=-1, keepdim=True)
                
                # Convert to list
                embeddings = text_features.cpu().numpy().tolist()
            
            processing_time = time.time() - start_time
            
            logger.info(f"Generated {len(embeddings)} text embeddings in {processing_time:.3f}s")
            
            return {
                "embeddings": embeddings,
                "dimension": len(embeddings[0]) if embeddings else 0,
                "processing_time": processing_time
            }
            
        except Exception as e:
            logger.error(f"Error generating text embeddings: {e}")
            raise HTTPException(status_code=500, detail=f"Error generating text embeddings: {str(e)}")


# Global service instance
service = ImageEmbeddingService()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan context manager for startup and shutdown"""
    # Startup
    logger.info("Starting Image Embedding Service...")
    service.initialize()
    logger.info("Service ready!")
    yield
    # Shutdown
    logger.info("Shutting down Image Embedding Service...")


# Create FastAPI app
app = FastAPI(
    title="Image Embedding Service",
    description="SigLIP-2 based image embedding and captioning service with GPU support",
    version="1.0.0",
    lifespan=lifespan
)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Image Embedding Service",
        "model": config.model_name,
        "device": str(service.device) if service.device else "not initialized",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        if service.model is None:
            return JSONResponse(
                status_code=503,
                content={"status": "unhealthy", "reason": "Model not loaded"}
            )
        
        return {
            "status": "healthy",
            "model": config.model_name,
            "device": str(service.device),
            "cuda_available": torch.cuda.is_available(),
            "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={"status": "unhealthy", "error": str(e)}
        )


@app.post("/embed", response_model=EmbeddingResponse)
async def embed_images(request: ImageEmbeddingRequest):
    """
    Generate embeddings for images
    
    Args:
        request: ImageEmbeddingRequest containing base64 encoded images
        
    Returns:
        EmbeddingResponse with embeddings and metadata
    """
    if not request.images:
        raise HTTPException(status_code=400, detail="No images provided")
    
    result = service.generate_embeddings(request.images)
    return EmbeddingResponse(**result)


@app.post("/caption", response_model=CaptionResponse)
async def caption_images(request: ImageCaptionRequest):
    """
    Generate captions for images
    
    Args:
        request: ImageCaptionRequest containing base64 encoded images
        
    Returns:
        CaptionResponse with captions and metadata
    """
    if not request.images:
        raise HTTPException(status_code=400, detail="No images provided")
    
    result = service.generate_captions(request.images, request.max_length)
    return CaptionResponse(**result)


@app.post("/embed-text")
async def embed_text(texts: List[str]):
    """
    Generate embeddings for text queries (for image-text retrieval)
    
    Args:
        texts: List of text queries
        
    Returns:
        Text embeddings
    """
    if not texts:
        raise HTTPException(status_code=400, detail="No texts provided")
    
    result = service.get_text_embeddings(texts)
    return result


@app.post("/upload-embed")
async def upload_and_embed(files: List[UploadFile] = File(...)):
    """
    Upload images and generate embeddings
    
    Args:
        files: List of image files
        
    Returns:
        Embeddings for uploaded images
    """
    try:
        images_base64 = []
        for file in files:
            # Read file content
            content = await file.read()
            # Convert to base64
            base64_str = base64.b64encode(content).decode('utf-8')
            images_base64.append(base64_str)
        
        result = service.generate_embeddings(images_base64)
        return result
        
    except Exception as e:
        logger.error(f"Error processing uploaded files: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/model-info")
async def model_info():
    """Get information about the loaded model"""
    return {
        "model_name": config.model_name,
        "device": str(service.device) if service.device else "not initialized",
        "cuda_available": torch.cuda.is_available(),
        "cuda_version": torch.version.cuda if torch.cuda.is_available() else None,
        "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
        "gpu_names": [torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())] if torch.cuda.is_available() else [],
        "batch_size": config.batch_size
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "image_embedding_service:app",
        host=config.host,
        port=config.port,
        reload=False,
        log_level=config.log_level.lower()
    )

