from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from jose import JW<PERSON>rror
from ..database import get_db
from ..models.user import User
from .jwt_handler import get_user_from_token

# Security scheme
security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    Get current authenticated user from JWT token
    """
    try:
        token = credentials.credentials
        user_data = get_user_from_token(token)
        
        # Get user from database
        result = await db.execute(select(User).where(User.id == user_data["user_id"]))
        user = result.scalars().first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User account is disabled"
            )
        
        return user
    except J<PERSON><PERSON>rror as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )

def require_role(allowed_roles: list):
    """
    Create a dependency that requires specific roles
    """
    def role_checker(current_user: User = Depends(get_current_user)) -> User:
        if current_user.role not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: {', '.join(allowed_roles)}"
            )
        return current_user
    return role_checker

def require_admin(current_user: User = Depends(get_current_user)) -> User:
    """
    Dependency that requires admin role
    """
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user

def require_space_access(space_id: int):
    """
    Create a dependency that requires access to a specific space
    """
    def space_checker(current_user: User = Depends(get_current_user)) -> User:
        if not current_user.can_access_space(space_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied to space {space_id}"
            )
        return current_user
    return space_checker

def require_space_owner(space_id: int):
    """
    Create a dependency that requires ownership of a specific space
    """
    def space_owner_checker(current_user: User = Depends(get_current_user)) -> User:
        # All users (including admins) need to own the space
        if not any(space.id == space_id for space in current_user.spaces):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"You must own space {space_id} to perform this action"
            )
        return current_user
    return space_owner_checker 