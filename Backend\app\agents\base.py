
from agno.agent import Agent
from agno.models.google import Gemini
import logging
import json
import threading
import asyncio
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
from ..config import GEMINI_API_KEY
from ..services.external_services import external_services


# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QueryCancelledException(Exception):
    """Exception raised when a query is cancelled by user"""
    def __init__(self, message="Query was cancelled by user."):
        super().__init__(message)
        self.is_cancelled = 1  # Binary flag for fast comparison

class BaseAgent:
    """Base agent class that all specialized agents will inherit from."""
    
    def __init__(self, name=None, description=None, instructions=None, 
                 use_web_search=False, use_document_search=False, use_deep_search=False, 
                 collection_name=None, db_path=None, tools=None):
        """
        Initialize the base agent with common configuration.
        
        Args:
            name (str): Agent name
            description (str): Agent description
            instructions (list): List of specific instructions for the agent
            use_web_search (bool): Whether to enable web search capability
            use_document_search (bool): Whether to enable document search capability
            use_deep_search (bool): Whether to enable deep research capability
            collection_name (str): Name of the document collection to search
            db_path (str): Path to the document database
            tools (list): List of agno tools to use
        """
        # Check for conflicting search settings
        search_count = sum([use_web_search, use_document_search, use_deep_search])
        if search_count > 1:
            raise ValueError("Only one search type (web_search, document_search, or deep_search) can be enabled at a time")
        
        self.name = name
        self.description = description
        self.instructions = instructions or []
        self.use_web_search = use_web_search
        self.use_document_search = use_document_search
        self.use_deep_search = use_deep_search
        self.collection_name = collection_name
        self.db_path = db_path
        self.user_tools = tools or []
        # Initialize tool results dictionary as instance variable
        self.tool_results_dict = {}
        
        logger.info(f"Initializing {name} agent with web_search={use_web_search}, document_search={use_document_search}, deep_search={use_deep_search}")
        
        # Initialize with basic agent setup (search capabilities configured later via update_config)
        # This path always executes during app startup since all agents start with False, False, False
        self.agent = Agent(
            name=self.name,
            model=Gemini(id="gemini-2.5-flash-lite", api_key=GEMINI_API_KEY),
            description=self.description,
            instructions=self.instructions,
            tools=[],  # No tools initially
            debug_mode=True,
            markdown=True,
        )
        
        logger.info(f"Agent {name} initialized with basic setup (search capabilities configured dynamically)")
    
    def tool_execution_hook(self, function_name: str, function_call: Callable, arguments: Dict[str, Any]):
        """Hook that captures tool execution and results"""
        logger.info(f"[Executing tool]: {function_name} with args: {arguments}")
        
        # Record start time
        start_time = datetime.now()
        
        # Execute the actual function
        try:
            result = function_call(**arguments)
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # Store the raw result in our instance dictionary
            tool_call_id = f"{function_name}_{start_time.timestamp()}"
            self.tool_results_dict[tool_call_id] = {
                'function_name': function_name,
                'arguments': arguments,
                'result': result,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'execution_time_seconds': execution_time,
                'status': 'success'
            }
            
            logger.info(f"✅ Tool {function_name} completed successfully")
            return result
            
        except Exception as e:
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # Store error information
            tool_call_id = f"{function_name}_{start_time.timestamp()}_error"
            self.tool_results_dict[tool_call_id] = {
                'function_name': function_name,
                'arguments': arguments,
                'error': str(e),
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'execution_time_seconds': execution_time,
                'status': 'error'
            }
            
            logger.error(f"❌ Tool {function_name} failed with error: {e}")
            raise
    
    def format_tool_results(self, tool_results_dict: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Format tool results into a structured format for better presentation.
        
        Args:
            tool_results_dict (dict): Raw tool results dictionary
            
        Returns:
            list: Formatted tool results
        """
        formatted_results = []
        
        for tool_id, result in tool_results_dict.items():
            if result['status'] == 'success':
                formatted_result = []
                
                try:
                    if result['function_name'] == 'google_search':
                        search_results = json.loads(result['result']) if isinstance(result['result'], str) else result['result']
                        for data in search_results:
                            formatted_result.append({
                                'title': data.get('title', 'No title'),
                                'url': data.get('url', 'No URL'),
                                'snippet': data.get('description', 'No snippet'),
                                'source_type': 'google'
                            })
                    
                    elif result['function_name'] == 'web_search_using_tavily':
                        search_results = json.loads(result['result']) if isinstance(result['result'], str) else result['result']
                        for data in search_results.get('results', []):
                            formatted_result.append({
                                'title': data.get('title', 'No title'),
                                'url': data.get('url', 'No URL'),
                                'snippet': data.get('content', 'No snippet'),
                                'source_type': 'tavily'
                            })
                    
                    elif result['function_name'] == 'search_wikipedia':
                        wiki_data = json.loads(result['result']) if isinstance(result['result'], str) else result['result']
                        formatted_result.append({
                            'title': wiki_data.get('name', 'Wikipedia Article'),
                            'url': f"https://en.wikipedia.org/wiki/{wiki_data.get('name', '').replace(' ', '_')}",
                            'snippet': wiki_data.get('content', 'No content')[:500] + '...' if len(wiki_data.get('content', '')) > 500 else wiki_data.get('content', 'No content'),
                            'source_type': 'wikipedia'
                        })
                    
                    elif result['function_name'] == 'get_current_stock_price':
                        symbol = result['arguments'].get('symbol', 'Unknown')
                        formatted_result.append({
                            'title': f"Stock Price for {symbol}",
                            'url': f"https://finance.yahoo.com/quote/{symbol}?p={symbol}",
                            'snippet': f"Current price: {result['result']}",
                            'source_type': 'yahoo_finance'
                        })
                    
                    else:
                        # For any other tools, return raw result
                        formatted_result.append({
                            'title': f"Result from {result['function_name']}",
                            'url': 'N/A',
                            'snippet': str(result['result'])[:500] + '...' if len(str(result['result'])) > 500 else str(result['result']),
                            'source_type': result['function_name']
                        })
                        
                except (json.JSONDecodeError, KeyError, TypeError) as e:
                    logger.warning(f"Error formatting result for {result['function_name']}: {e}")
                    # Fallback to raw result
                    formatted_result.append({
                        'title': f"Result from {result['function_name']}",
                        'url': 'N/A',
                        'snippet': str(result['result'])[:500] + '...' if len(str(result['result'])) > 500 else str(result['result']),
                        'source_type': result['function_name']
                    })
                
                formatted_results.append(formatted_result)
                
            else:  # Error status
                formatted_results.append(["Error"])
        
        return formatted_results
    
    # Canonical summarizer prompt used everywhere in this class.
    SUMMARY_INSTRUCTIONS: str = (
        """
            You are a chat summarizer agent. Your task is to carefully review the entire session's chat history between the user and the assistant and generate a detailed, structured summary that captures all important information.

            Your output must:
            - Be comprehensive enough to serve as a reference for anyone reviewing the session later.
            - Include all key questions, answers, decisions, suggestions, examples, and any evolution in the user's needs or assistant's responses.
            - Preserve chronological flow and group related interactions together where appropriate.
            - Use headings, subheadings, and bullet points to structure the summary clearly.
            - Be written in a neutral, professional tone.
            - Use markdown formatting where helpful (e.g., for lists, emphasis, structure).
            - Focus only on summarizing — do NOT respond to any queries or generate new content outside of what's present in the session.
            - Avoid speculation, filler, or assistant-like phrasing such as "As an AI, I think...".
            -Key Notes:
            1. Good summary output:
            - Clearly identifies the topics discussed and outcomes reached.
            - Includes all key context that might be needed for future follow-ups.
            - Is long and detailed enough to substitute for reading the entire session.

            2. Do not:
            - Answer any of the user’s questions.
            - Say “Here’s a summary” or “Hope this helps.”
            - Include opinions or guesses about what the user meant.

            Example output:

            ##  Session Summary

            ### 1. CRM Integration Query
            - The user asked about integrating a CRM with third-party email platforms.
            - Assistant suggested Mailchimp, SendGrid, and Zoho Campaigns.
            - Assistant also outlined pros and cons of each, including pricing and API support.

            ### 2. GDPR Compliance
            - The user asked what policies are needed to be GDPR compliant.
            - Assistant provided a checklist including:
            - Data processing agreements
            - Consent management
            - Right-to-access procedures
            - A sample privacy policy was shared and reviewed.

            ### 3. Next Steps
            - User plans to review internal CRM workflows before implementing changes.
            - Assistant offered to assist with API integration examples in future sessions.

            Only produce summaries like the example above. Do not simulate assistant behavior.

            Input format:
            - You will receive a JSON array of pairs. Each pair consists of two
              message objects: the first is the user's message and the second is
              the assistant's reply. Example: [{"role":"user","content":"..."}, {"role":"assistant","content":"..."}]
            - If a previous session summary exists, a special PREVIOUS_SUMMARY
              pair will be included at the very beginning with roles
              [{"role":"system","content":"PREVIOUS_SUMMARY"}, {"role":"assistant","content":"<previous-summary-text>"}].
              Treat this as the authoritative condensed history that should be
              integrated into your new summary.
            - Do not answer questions or produce new content; only summarize the
              material provided in the pairs.
        """
    )

    def _create_summarizer_agent(self) -> Agent:
        """Create a summarizer Agent configured with the canonical prompt.

        Returns:
            Agent: A Gemini-powered agent that uses `SUMMARY_INSTRUCTIONS` as
                its system instructions to produce reference-quality session
                summaries.

        Notes:
            - We centralize the summarizer instructions in
              `SUMMARY_INSTRUCTIONS` to ensure consistency throughout the
              codebase and to simplify future prompt updates.
            - A new Agent is created per call to keep configuration changes
              local to the current operation and avoid hidden shared state.
        """

        # Build a fresh, minimal Agent dedicated to summarization.
        return Agent(
            model=Gemini(id="gemini-2.5-flash-lite", api_key=GEMINI_API_KEY),
            description="Generates a detailed and structured summary of the entire session's chat history.",
            instructions=self.SUMMARY_INSTRUCTIONS,
            markdown=True,
        )

    def format_deep_research_tool_results(self, tool_results_dict: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Format deep research tool results into a structured format for better presentation.
        Only formats specific tools used in deep research.
        
        Args:
            tool_results_dict (dict): Raw tool results dictionary
            
        Returns:
            list: Formatted tool results
        """
        formatted_results = []
        
        # Define the tools we want to capture for deep research
        target_tools = [
            'google_search', 'web_search_using_tavily', 'search_wikipedia',
            'get_current_stock_price', 'search_arxiv_and_return_articles', 'search_pubmed', 
            'read_url', 'add', 'subtract', 'multiply', 'divide', 'exponentiate', 'factorial',
            'is_prime', 'square_root', 'save_to_file_and_run'
        ]
        
        for tool_id, result in tool_results_dict.items():
            if result['status'] == 'success' and result['function_name'] in target_tools:
                formatted_result = []
                
                try:
                    if result['function_name'] == 'google_search':
                        search_results = json.loads(result['result']) if isinstance(result['result'], str) else result['result']
                        for data in search_results:
                            formatted_result.append({
                                'title': data.get('title', 'No title'),
                                'url': data.get('url', 'No URL'),
                                'snippet': data.get('description', 'No snippet'),
                                'source_type': 'google'
                            })
                    
                    elif result['function_name'] == 'web_search_using_tavily':
                        search_results = json.loads(result['result']) if isinstance(result['result'], str) else result['result']
                        for data in search_results.get('results', []):
                            formatted_result.append({
                                'title': data.get('title', 'No title'),
                                'url': data.get('url', 'No URL'),
                                'snippet': data.get('content', 'No snippet'),
                                'source_type': 'tavily'
                            })
                    
                    elif result['function_name'] == 'search_wikipedia':
                        wiki_data = json.loads(result['result']) if isinstance(result['result'], str) else result['result']
                        formatted_result.append({
                            'title': wiki_data.get('name', 'Wikipedia Article'),
                            'url': f"https://en.wikipedia.org/wiki/{wiki_data.get('name', '').replace(' ', '_')}",
                            'snippet': wiki_data.get('content', 'No content')[:500] + '...' if len(wiki_data.get('content', '')) > 500 else wiki_data.get('content', 'No content'),
                            'source_type': 'wikipedia'
                        })
                    
                    elif result['function_name'] == 'get_current_stock_price':
                        symbol = result['arguments'].get('symbol', 'Unknown')
                        formatted_result.append({
                            'title': f"Stock Price for {symbol}",
                            'url': f"https://finance.yahoo.com/quote/{symbol}?p={symbol}",
                            'snippet': f"Current price: {result['result']}",
                            'source_type': 'yahoo_finance'
                        })
                    
                    elif result['function_name'] == 'search_arxiv_and_return_articles':
                        search_results = json.loads(result['result']) if isinstance(result['result'], str) else result['result']
                        for data in search_results:
                            formatted_result.append({
                                'title': data.get('title', 'No title'),
                                'url': data.get('pdf_url', 'No URL'),
                                'snippet': f"Authors: <AUTHORS>
                                'source_type': 'arxiv'
                            })
                    
                    elif result['function_name'] == 'search_pubmed':
                        search_results = json.loads(result['result']) if isinstance(result['result'], str) else result['result']
                        for data in search_results:
                            formatted_result.append({
                                'title': data.get('title', str(data)[:50] + '...' if len(str(data)) > 50 else str(data)),
                                'url': data.get('url', 'N/A'),
                                'snippet': str(data)[:200] + '...' if len(str(data)) > 200 else str(data),
                                'source_type': 'pubmed'
                            })
                    
                    elif result['function_name'] == 'read_url':
                        url = result['arguments'].get('url', 'Unknown URL')
                        
                        # Extract title from result
                        import re
                        title_match = re.search(r"'title':\s*'([^']*)'", str(result['result']))
                        title = title_match.group(1) if title_match else 'Web Content'
                        
                        # Extract content snippet
                        key = "'content':"
                        content_start = str(result['result']).find(key)
                        if content_start != -1:
                            substring = str(result['result'])[content_start + len(key):]
                            substring = substring.lstrip()
                            if substring and substring[0] in ('"', "'"):
                                substring = substring[1:]
                            substring = substring.rstrip("'} ")
                            words = substring.split()
                            snippet = ' '.join(words[:25])
                        else:
                            snippet = str(result['result'])[:100] + '...'
                        
                        formatted_result.append({
                            'title': title,
                            'url': url,
                            'snippet': snippet,
                            'source_type': 'web_content'
                        })
                    
                    elif result['function_name'] in ['add', 'subtract', 'multiply', 'divide', 'exponentiate', 'factorial', 'is_prime', 'square_root']:
                        calc_data = json.loads(result['result']) if isinstance(result['result'], str) else result['result']
                        formatted_result.append({
                            'title': f"Calculator: {calc_data.get('operation', result['function_name'])}",
                            'url': 'N/A',
                            'snippet': f"Result: {calc_data.get('result', 'No result')}",
                            'source_type': 'calculator'
                        })
                    
                    elif result['function_name'] == 'save_to_file_and_run':
                        formatted_result.append({
                            'title': 'Code Execution',
                            'url': 'N/A',
                            'snippet': f"Code executed and result: {str(result['result'])[:200]}{'...' if len(str(result['result'])) > 200 else ''}",
                            'source_type': 'code_execution'
                        })
                        
                except (json.JSONDecodeError, KeyError, TypeError) as e:
                    logger.warning(f"Error formatting deep research result for {result['function_name']}: {e}")
                    # Fallback to raw result
                    formatted_result.append({
                        'title': f"Result from {result['function_name']}",
                        'url': 'N/A',
                        'snippet': str(result['result'])[:200] + '...' if len(str(result['result'])) > 200 else str(result['result']),
                        'source_type': result['function_name']
                    })
                
                if formatted_result:  # Only add if we have results
                    formatted_results.extend(formatted_result)
                
            elif result['status'] == 'error' and result['function_name'] in target_tools:
                # Include errors for target tools
                formatted_results.append({
                    'title': f"Error in {result['function_name']}",
                    'url': 'N/A',
                    'snippet': f"Error: {result.get('error', 'Unknown error')}",
                    'source_type': 'error'
                })
        
        return formatted_results
    
    def estimate_tokens(self, text: str) -> int:
        """
        Estimate the number of tokens in a given text string.
        
        Args:
            text (str): The text to estimate tokens for
            
        Returns:
            int: Estimated number of tokens
        """
        from ..utils.tokenization import count_tokens
        from .. import config
        return count_tokens(text, encoding_name=config.TOKEN_ENCODING)
    
    def build_context_with_token_budget(self, chat_history, current_query: str, mode: str, prev_summary_text: Optional[str] = None):
        """
        Build context for the agent while respecting token budget constraints.
        
        This method intelligently manages the context size by:
        1. Calculating available tokens for context after reserving space for instructions and query
        2. For 'deep' mode: Returns minimal context as deep research doesn't need chat history
        3. For other modes: Tries to fit recent chat history within budget, summarizing older messages if needed
        
        Args:
            chat_history: List of chat messages with role and content
            current_query (str): The current user query
            mode (str): The agent mode ('deep', 'document', or other)
            prev_summary_text (Optional[str]): Previous summary text for progressive chaining
            
        Returns:
            tuple: (context_dict, metadata_dict) where:
                - context_dict: Contains chat_history and/or session_summary for the agent
                - metadata_dict: Contains statistics and configuration info
        """
        from .. import config
        
        # Get token budget configuration
        model_window_tokens = config.MODEL_WINDOW_TOKENS
        reserve_for_answer = config.RESERVE_FOR_ANSWER
        
        # Calculate tokens needed for instruction prefix and current query
        instruction_prefix_text = "Strictly follow the given INSTRUCTIONS and then answer the query: "
        instruction_tokens = self.estimate_tokens(instruction_prefix_text)
        question_tokens = self.estimate_tokens(current_query)
        
        # Estimate static header tokens added in web/basic modes so the selection
        # logic accounts for the extra prompt scaffolding we prepend later.
        header_base_tokens = 0  # Conversation-history banner + Question: prefix
        header_recent_tokens = 0  # "Recent messages:" label
        header_summary_label_tokens = 0  # "Session summary:" label (applied post-summarization)
        if mode in ("web", "basic"):
            header_base_text = (
                "Conversation history (consult this; for any question about prior conversation, answer strictly based on this history):\n"
                "Question: "
            )
            header_base_tokens = self.estimate_tokens(header_base_text)
            header_recent_tokens = self.estimate_tokens("Recent messages:\n")
        
        # Calculate remaining tokens available for context (chat history/summary)
        available_context_tokens = (
            config.INPUT_BUDGET
            - instruction_tokens
            - question_tokens
            - header_base_tokens
            - header_recent_tokens
        )
        
        logger.info(
            f"[TokenBudget] Mode={mode}, Encoding={config.TOKEN_ENCODING}, "
            f"Window={model_window_tokens}, Reserve={reserve_for_answer}, "
            f"Instruction={instruction_tokens}, Query={question_tokens}, "
            f"HeadersBase={header_base_tokens}, HeaderRecent={header_recent_tokens}, "
            f"AvailableForContext={available_context_tokens}"
        )
        
        # Small overhead per message approximating role/separator tokens.
        PER_MESSAGE_OVERHEAD = 4
        
        # Deep mode doesn't need chat history context - it relies on tool results
        if mode == 'deep':
            input_budget = model_window_tokens - reserve_for_answer
            input_tokens_used = instruction_tokens + question_tokens
            context_tokens_used = 0  # Deep mode uses no context
            return (
                None,
                {
                    'context_type': 'none',
                    'summary_text': None,
                    'raw_pairs_used_count': 0,
                    'parent_context_required': False,
                    'token_stats': {
                        'model_window_tokens': model_window_tokens,
                        'reserve_for_answer': reserve_for_answer,
                        'instruction_tokens': instruction_tokens,
                        'question_tokens': question_tokens,
                        'input_budget': input_budget,
                        'input_tokens_used': input_tokens_used,
                        'context_tokens_used': context_tokens_used
                    },
                    'instruction_prefix': None
                }
            )
        
        # Document mode: Handle document-specific context building
        if mode == 'document':
            # No chat history available - return empty context
            if not chat_history or len(chat_history) < 2:
                input_budget = model_window_tokens - reserve_for_answer
                input_tokens_used = instruction_tokens + question_tokens
                context_tokens_used = max(0, input_tokens_used - instruction_tokens - question_tokens)
                return (
                    None,
                    {
                        'context_type': 'none',
                        'summary_text': None,
                        'raw_pairs_used_count': 0,
                        'parent_context_required': False,
                        'token_stats': {
                            'model_window_tokens': model_window_tokens,
                            'reserve_for_answer': reserve_for_answer,
                            'instruction_tokens': instruction_tokens,
                            'question_tokens': question_tokens,
                            'input_budget': input_budget,
                            'input_tokens_used': input_tokens_used,
                            'context_tokens_used': context_tokens_used
                        },
                        'instruction_prefix': None
                    }
                )

            # Try to fit as much recent chat history as possible within token budget
            raw_window = []
            used_tokens = 0
            for msg in reversed(chat_history):
                msg_tokens = self.estimate_tokens(msg.get("content", ""))
                if used_tokens + msg_tokens + PER_MESSAGE_OVERHEAD <= available_context_tokens:
                    raw_window.insert(0, msg)  # Insert at beginning to maintain chronological order
                    used_tokens += (msg_tokens + PER_MESSAGE_OVERHEAD)
                else:
                    break

            # If all chat history fits within budget, use it directly
            if len(raw_window) == len(chat_history):
                agno_history = [{"role": msg.get("role", "user"), "content": msg.get("content", "")} for msg in raw_window]
                input_budget = model_window_tokens - reserve_for_answer
                input_tokens_used = instruction_tokens + question_tokens + used_tokens
                context_tokens_used = max(0, input_tokens_used - instruction_tokens - question_tokens)
                return (
                    {"chat_history": agno_history},
                    {
                        'context_type': 'history',
                        'summary_text': None,
                        'raw_pairs_used_count': len(raw_window) // 2,
                        'parent_context_required': False,
                        'token_stats': {
                            'model_window_tokens': model_window_tokens,
                            'reserve_for_answer': reserve_for_answer,
                            'instruction_tokens': instruction_tokens,
                            'question_tokens': question_tokens,
                            'input_budget': input_budget,
                            'input_tokens_used': input_tokens_used,
                            'context_tokens_used': context_tokens_used
                        },
                        'instruction_prefix': "Here are the previous chats, If you think it is relevant to the current query, use it to answer the query otherwise IGNORE IT"
                    }
                )

            # Some history doesn't fit - need to summarize the excluded portion
            excluded_history = chat_history[:-len(raw_window)] if raw_window else chat_history
            try:
                # Convert excluded history to message pairs for summarization
                messages = [{"role": msg["role"], "content": msg["content"]} for msg in excluded_history]
                pairs = []
                for i in range(0, len(messages), 2):
                    if i + 1 < len(messages):
                        pairs.append((messages[i], messages[i+1]))
                
                # Progressive chaining: prepend previous summary if present
                if prev_summary_text:
                    pairs.insert(0, ({"role": "system", "content": "PREVIOUS_SUMMARY"}, {"role": "assistant", "content": prev_summary_text}))
                
                # Generate summary of excluded history
                summarizer_agent = self._create_summarizer_agent()
                summary_obj = summarizer_agent.run(json.dumps(pairs))
                summary_text = summary_obj.content
                summary_tokens = self.estimate_tokens(summary_text)
                total_input_tokens = instruction_tokens + question_tokens + summary_tokens + used_tokens
                
                # Build chat history from the current raw window. If we need to trim
                # to fit the budget, trim this list in-place, and DO NOT rebuild it
                # afterward to avoid inconsistencies.
                agno_history = [{"role": msg.get("role", "user"), "content": msg.get("content", "")} for msg in raw_window]
                
                # Post-summary fit pass (document mode): if the total still exceeds
                # the budget, drop oldest tail messages until it fits.
                if total_input_tokens > config.INPUT_BUDGET:
                    while agno_history and total_input_tokens > config.INPUT_BUDGET:
                        removed = agno_history.pop(0)
                        removed_tokens = self.estimate_tokens(removed.get("content", "")) + PER_MESSAGE_OVERHEAD
                        used_tokens -= removed_tokens
                        total_input_tokens = instruction_tokens + question_tokens + summary_tokens + used_tokens
                input_budget = model_window_tokens - reserve_for_answer
                context_tokens_used = max(0, total_input_tokens - instruction_tokens - question_tokens)
                return (
                    {"session_summary": summary_text, "chat_history": agno_history},
                    {
                        'context_type': 'summary',
                        'summary_text': summary_text,
                        'raw_pairs_used_count': len(agno_history) // 2,
                        'parent_context_required': bool(prev_summary_text),
                        'token_stats': {
                            'model_window_tokens': model_window_tokens,
                            'reserve_for_answer': reserve_for_answer,
                            'instruction_tokens': instruction_tokens,
                            'question_tokens': question_tokens,
                            'input_budget': input_budget,
                            'input_tokens_used': total_input_tokens,
                            'context_tokens_used': context_tokens_used
                        },
                        'instruction_prefix': "Here are the previous chats, If you think it is relevant to the current query, use it to answer the query otherwise IGNORE IT"
                    }
                )
            except Exception:
                # Fallback to raw window only if summarization fails
                agno_history = [{"role": msg.get("role", "user"), "content": msg.get("content", "")} for msg in raw_window]
                input_budget = model_window_tokens - reserve_for_answer
                input_tokens_used = instruction_tokens + question_tokens + used_tokens
                context_tokens_used = max(0, input_tokens_used - instruction_tokens - question_tokens)
                return (
                    {"chat_history": agno_history},
                    {
                        'context_type': 'history',
                        'summary_text': None,
                        'raw_pairs_used_count': len(raw_window) // 2,
                        'parent_context_required': False,
                        'token_stats': {
                            'model_window_tokens': model_window_tokens,
                            'reserve_for_answer': reserve_for_answer,
                            'instruction_tokens': instruction_tokens,
                            'question_tokens': question_tokens,
                            'input_budget': input_budget,
                            'input_tokens_used': input_tokens_used,
                            'context_tokens_used': context_tokens_used
                        },
                        'instruction_prefix': "Here are the previous chats, If you think it is relevant to the current query, use it to answer the query otherwise IGNORE IT"
                    }
                )
        
        # Default mode: Handle general context building (same logic as document mode)
        # No chat history available - return empty context
        if not chat_history or len(chat_history) < 2:
            input_budget = model_window_tokens - reserve_for_answer
            input_tokens_used = instruction_tokens + question_tokens
            context_tokens_used = max(0, input_tokens_used - instruction_tokens - question_tokens)
            return (
                None,
                {
                    'context_type': 'none',
                    'summary_text': None,
                    'raw_pairs_used_count': 0,
                    'parent_context_required': False,
                    'token_stats': {
                        'model_window_tokens': model_window_tokens,
                        'reserve_for_answer': reserve_for_answer,
                        'instruction_tokens': instruction_tokens,
                        'question_tokens': question_tokens,
                        'input_budget': input_budget,
                        'input_tokens_used': input_tokens_used,
                        'context_tokens_used': context_tokens_used
                    },
                    'instruction_prefix': None
                }
            )
        
        # Try to fit as much recent chat history as possible within token budget
        raw_window = []
        used_tokens = 0
        for msg in reversed(chat_history):
            msg_tokens = self.estimate_tokens(msg.get("content", ""))
            if used_tokens + msg_tokens + PER_MESSAGE_OVERHEAD <= available_context_tokens:
                raw_window.insert(0, msg)  # Insert at beginning to maintain chronological order
                used_tokens += (msg_tokens + PER_MESSAGE_OVERHEAD)
            else:
                break
        
        # If all chat history fits within budget, use it directly
        if len(raw_window) == len(chat_history):
            agno_history = [{"role": msg.get("role", "user"), "content": msg.get("content", "")} for msg in raw_window]
            # Post-fit pass for web/basic: account for static header tokens and trim if needed
            total_input_tokens = instruction_tokens + question_tokens + used_tokens + header_base_tokens + header_recent_tokens
            if total_input_tokens > config.INPUT_BUDGET:
                while agno_history and total_input_tokens > config.INPUT_BUDGET:
                    removed = agno_history.pop(0)
                    removed_tokens = self.estimate_tokens(removed.get("content", "")) + PER_MESSAGE_OVERHEAD
                    used_tokens -= removed_tokens
                    total_input_tokens = instruction_tokens + question_tokens + used_tokens + header_base_tokens + header_recent_tokens
            input_budget = model_window_tokens - reserve_for_answer
            context_tokens_used = max(0, total_input_tokens - instruction_tokens - question_tokens)
            return (
                {"chat_history": agno_history},
                {
                    'context_type': 'history',
                    'summary_text': None,
                    'raw_pairs_used_count': len(agno_history) // 2,
                    'parent_context_required': False,
                    'token_stats': {
                        'model_window_tokens': model_window_tokens,
                        'reserve_for_answer': reserve_for_answer,
                        'instruction_tokens': instruction_tokens,
                        'question_tokens': question_tokens,
                        'input_budget': input_budget,
                        'input_tokens_used': total_input_tokens,
                        'context_tokens_used': context_tokens_used
                    },
                    'instruction_prefix': "Here are the previous chats, If you think it is relevant to the current query, use it to answer the query otherwise IGNORE IT"
                }
            )
        
        # Some history doesn't fit - need to summarize the excluded portion
        excluded_history = chat_history[:-len(raw_window)] if raw_window else chat_history
        
        try:
            # Convert excluded history to message pairs for summarization
            messages = [{"role": msg["role"], "content": msg["content"]} for msg in excluded_history]
            pairs = []
            for i in range(0, len(messages), 2):
                if i + 1 < len(messages):
                    pairs.append((messages[i], messages[i+1]))

            # Progressive chaining: if a previous summary exists, prepend it so
            # the summarizer sees S_prev followed by the newly excluded pairs.
            if prev_summary_text:
                pairs.insert(0, ({"role": "system", "content": "PREVIOUS_SUMMARY"}, {"role": "assistant", "content": prev_summary_text}))

            # If no valid pairs found, fallback to raw window only
            if not pairs:
                agno_history = [{"role": msg.get("role", "user"), "content": msg.get("content", "")} for msg in raw_window]
                # Post-fit pass for web/basic: account for headers
                total_input_tokens = instruction_tokens + question_tokens + used_tokens + header_base_tokens + header_recent_tokens
                if total_input_tokens > config.INPUT_BUDGET:
                    while agno_history and total_input_tokens > config.INPUT_BUDGET:
                        removed = agno_history.pop(0)
                        removed_tokens = self.estimate_tokens(removed.get("content", "")) + PER_MESSAGE_OVERHEAD
                        used_tokens -= removed_tokens
                        total_input_tokens = instruction_tokens + question_tokens + used_tokens + header_base_tokens + header_recent_tokens
                input_budget = model_window_tokens - reserve_for_answer
                context_tokens_used = max(0, total_input_tokens - instruction_tokens - question_tokens)
                return (
                    {"chat_history": agno_history},
                    {
                        'context_type': 'history',
                        'summary_text': None,
                        'raw_pairs_used_count': len(agno_history) // 2,
                        'parent_context_required': False,
                        'token_stats': {
                            'model_window_tokens': model_window_tokens,
                            'reserve_for_answer': reserve_for_answer,
                            'instruction_tokens': instruction_tokens,
                            'question_tokens': question_tokens,
                            'input_budget': input_budget,
                            'input_tokens_used': total_input_tokens,
                            'context_tokens_used': context_tokens_used
                        },
                        'instruction_prefix': "Here are the previous chats, If you think it is relevant to the current query, use it to answer the query otherwise IGNORE IT"
                    }
                )
            
            # Generate summary of excluded history
            summarizer_agent = self._create_summarizer_agent()
            summary_obj = summarizer_agent.run(json.dumps(pairs))
            summary_text = summary_obj.content
            
            summary_tokens = self.estimate_tokens(summary_text)
            # Account for static headers in web/basic prompt shape
            header_summary_label_tokens = self.estimate_tokens("Session summary:\n")
            total_input_tokens = (
                instruction_tokens + question_tokens + summary_tokens + used_tokens +
                header_base_tokens + header_recent_tokens + header_summary_label_tokens
            )
            
            logger.info(f"[TokenBudget] Generated summary ({summary_tokens} tokens) + raw tail ({len(raw_window)} msgs, {used_tokens} tokens including overhead); Headers={header_base_tokens + header_recent_tokens + header_summary_label_tokens}, Total={total_input_tokens}")
            
            # Post-summary fit pass (web/basic): drop oldest tail messages until within budget
            agno_history = [{"role": msg.get("role", "user"), "content": msg.get("content", "")} for msg in raw_window]
            if total_input_tokens > config.INPUT_BUDGET:
                while agno_history and total_input_tokens > config.INPUT_BUDGET:
                    removed = agno_history.pop(0)
                    removed_tokens = self.estimate_tokens(removed.get("content", "")) + PER_MESSAGE_OVERHEAD
                    used_tokens -= removed_tokens
                    total_input_tokens = (
                        instruction_tokens + question_tokens + summary_tokens + used_tokens +
                        header_base_tokens + header_recent_tokens + header_summary_label_tokens
                    )
            
            input_budget = model_window_tokens - reserve_for_answer
            context_tokens_used = max(0, total_input_tokens - instruction_tokens - question_tokens)
            return (
                {"session_summary": summary_text, "chat_history": agno_history},
                {
                    'context_type': 'summary',
                    'summary_text': summary_text,
                    'raw_pairs_used_count': len(agno_history) // 2,
                    'parent_context_required': bool(prev_summary_text),
                    'token_stats': {
                        'model_window_tokens': model_window_tokens,
                        'reserve_for_answer': reserve_for_answer,
                        'instruction_tokens': instruction_tokens,
                        'question_tokens': question_tokens,
                        'input_budget': input_budget,
                        'input_tokens_used': total_input_tokens,
                        'context_tokens_used': context_tokens_used
                    },
                    'instruction_prefix': "Here are the previous chats, If you think it is relevant to the current query, use it to answer the query otherwise IGNORE IT"
                }
            )
            
        except Exception as e:
            # Fallback to raw window only if summarization fails
            logger.warning(f"Error creating summary: {e}, falling back to raw window only")
            agno_history = [{"role": msg.get("role", "user"), "content": msg.get("content", "")} for msg in raw_window]
            total_input_tokens = instruction_tokens + question_tokens + used_tokens + header_base_tokens + header_recent_tokens
            if total_input_tokens > config.INPUT_BUDGET:
                while agno_history and total_input_tokens > config.INPUT_BUDGET:
                    removed = agno_history.pop(0)
                    removed_tokens = self.estimate_tokens(removed.get("content", "")) + PER_MESSAGE_OVERHEAD
                    used_tokens -= removed_tokens
                    total_input_tokens = instruction_tokens + question_tokens + used_tokens + header_base_tokens + header_recent_tokens
            input_budget = model_window_tokens - reserve_for_answer
            context_tokens_used = max(0, total_input_tokens - instruction_tokens - question_tokens)
            return (
                {"chat_history": agno_history},
                {
                    'context_type': 'history',
                    'summary_text': None,
                    'raw_pairs_used_count': len(agno_history) // 2,
                    'parent_context_required': False,
                    'token_stats': {
                        'model_window_tokens': model_window_tokens,
                        'reserve_for_answer': reserve_for_answer,
                        'instruction_tokens': instruction_tokens,
                        'question_tokens': question_tokens,
                        'input_budget': input_budget,
                        'input_tokens_used': total_input_tokens,
                        'context_tokens_used': context_tokens_used
                    },
                    'instruction_prefix': "Here are the previous chats, If you think it is relevant to the current query, use it to answer the query otherwise IGNORE IT"
                }
            )

    def create_chat_summary(self, chat_history):
        """
        Create a summary of the chat history using MemorySummarizer if chat is long enough,
        otherwise return the original chat history.
        
        Args:
            chat_history (list): List of chat messages with role and content
            
        Returns:
            dict: {"type": "summary", "content": summary_string} or 
                  {"type": "history", "content": chat_history} or 
                  None if no valid chat history
        """
        if not chat_history:
            return None
        if len(chat_history) < 2:
            return {"type": "history", "content": chat_history}
            
        # Check if summarization is needed
        # Condition 1: More than 6 chats
        # Condition 2: Combined content exceeds 100 words
        total_words = sum(len(msg.get("content", "").split()) for msg in chat_history)
        needs_summary = len(chat_history) > 6 or total_words > 100
        
        if not needs_summary:
            logger.info(
                f"[ChatContext] Using raw history (messages={len(chat_history)}, words={total_words})"
            )
            return {"type": "history", "content": chat_history}
            
        try:
            logger.info(
                f"[ChatContext] Generating  summary (messages={len(chat_history)}, words={total_words})"
            )
            
            # Convert chat history to Message objects
            messages = [{"role": msg["role"], "content": msg["content"]} 
                       for msg in chat_history]

            # Pair them up for summarization (user_msg, assistant_msg)
            pairs = []
            for i in range(0, len(messages), 2):
                if i + 1 < len(messages):
                    pairs.append((messages[i], messages[i+1]))
            
            if not pairs:
                return {"type": "history", "content": chat_history}
                
            # Create summarizer with Gemini model
            summarizer_model = Gemini(
                    id="gemini-2.5-flash-lite",
                    api_key=GEMINI_API_KEY
                )
            # Generate summary
            summarizer_agent = self._create_summarizer_agent()
            summary_obj = summarizer_agent.run(json.dumps(pairs))
            # Log preview (first 120 chars) to avoid noise
            try:
                preview = (summary_obj.content or "")[:120].replace("\n", " ")
                logger.info(f"[ChatContext] Summary generated (preview): {preview}...")
            except Exception:
                pass
            return {"type": "summary", "content": summary_obj.content}
            
        except Exception as e:
            logger.warning(f"Error creating chat summary: {e}, falling back to original history")
            raise e
    
    def update_config(self, use_web_search=None, use_document_search=None, use_deep_search=None,
                      collection_name=None, db_path=None, tools=None, 
                      department=None, allowed_files=None):
        """
        Default update_config method for BaseAgent.
        
        Specialized agents should override this method to handle their specific setup.
        This is a fallback implementation that provides basic configuration update.
        """
        logger.warning(f"Using default BaseAgent update_config for {self.name}. "
                      f"Consider overriding this method in the specialized agent class.")
        
        # Check for conflicting settings first
        search_settings = [use_web_search, use_document_search, use_deep_search]
        active_searches = [s for s in search_settings if s is not None and s]
        if len(active_searches) > 1:
            raise ValueError("Only one search type (web_search, document_search, or deep_search) can be enabled at a time")
        
        # Update the settings if provided
        if use_web_search is not None:
            self.use_web_search = use_web_search
        if use_document_search is not None:
            self.use_document_search = use_document_search
        if use_deep_search is not None:
            self.use_deep_search = use_deep_search
        if collection_name is not None:
            self.collection_name = collection_name
        if db_path is not None:
            self.db_path = db_path
        if tools is not None:
            self.user_tools = tools
        if department is not None:
            self.department = department
        if allowed_files is not None:
            self.allowed_files = allowed_files
            
        logger.info(f"Updating {self.name} configuration:")
        logger.info(f"web_search={self.use_web_search}, document_search={self.use_document_search}, deep_search={self.use_deep_search}")
        if hasattr(self, 'department'):
            logger.info(f"department={self.department}")
        if hasattr(self, 'allowed_files'):
            logger.info(f"allowed_files={self.allowed_files}")
        
        # Clear previous search results
        self.tool_results_dict = {}
        
        # Basic reconfiguration - specialized agents should override for custom behavior
        if self.use_web_search:
            self.tools = self.user_tools.copy() if self.user_tools else []
            
            self.agent = Agent(
                name=self.name,
                model=Gemini(id="gemini-2.5-flash", api_key=GEMINI_API_KEY),
                description=self.description,
                instructions=self.instructions,
                tools=self.tools,
                debug_mode=True,
                markdown=True,
                tool_hooks=[self.tool_execution_hook]
            )
            logger.info(f"Agent {self.name} updated with basic web search configuration")
            
        elif self.use_document_search:
            # No local pipeline initialization needed - external services handle this
            logger.info(f"Agent {self.name} updated to use external document search services")
            
        elif self.use_deep_search:
            # Deep search uses the deep research pipeline
            logger.info(f"Agent {self.name} updated to use deep research capabilities")
            
        else:
            self.agent = Agent(
                name=self.name,
                model=Gemini(id="gemini-2.5-flash-lite", api_key=GEMINI_API_KEY),
                description=self.description,
                instructions=self.instructions,
                tools=[],
                debug_mode=True,
                markdown=True,
            )
            logger.info(f"Agent {self.name} updated without search capabilities")
    
    async def process_query(self, query, chat_history=None, allowed_files=None, username: Optional[str] = None, db: Optional["Session"] = None, cancellation_event=None, collection_name: Optional[str] = None, prev_summary_text: Optional[str] = None):
        """
        Process a user query and return a response.
        
        Args:
            query (str): The user's query
            chat_history (list): Optional chat history for context
            allowed_files (list): Optional list of specific files to search in
            username (str): Current user's username for user/space-based retrieval
            db (Session): SQLAlchemy session to fetch raw documents for sources
            cancellation_event (asyncio.Event): Event to signal query cancellation
            prev_summary_text (str | None): Previous session summary text if available for progressive chaining
            
        Returns:
            dict: Response containing answer and metadata
        """
        logger.info(f"Processing query: {query[:50]}...")
        # Preserve the original user query (without instruction prefixing)
        original_user_query: str = query
        
        # Update allowed_files if provided
        if allowed_files is not None:
            self.allowed_files = allowed_files
        
        # Clear previous tool results
        self.tool_results_dict = {}
        
        # Helper function to check cancellation
        def check_cancellation():
            if cancellation_event and cancellation_event.is_set():
                logger.info("Query cancelled by user")
                raise QueryCancelledException("Query was cancelled by user.")
        
        # Helper function to run agent with cancellation support
        async def run_agent_with_cancellation(query_text):
            """Run agent.run() in a thread while checking for cancellation"""
            if not cancellation_event:
                # No cancellation support, run normally
                return self.agent.run(query_text, stream=False)
            
            # Store original tool hook and create cancellation-aware hook
            original_hook = getattr(self.agent, 'tool_hooks', [])
            
            def cancellation_aware_tool_hook(function_name, function_call, arguments):
                # Check for cancellation before each tool execution
                if cancellation_event.is_set():
                    logger.info(f"🛑 Tool execution cancelled: {function_name} (expected during query cancellation)")
                    # Return a graceful cancellation response instead of raising exception
                    return {
                        "status": "cancelled",
                        "message": "Tool execution was cancelled by user",
                        "tool": function_name
                    }
                
                # Execute original hook if it exists
                if original_hook:
                    return original_hook[0](function_name, function_call, arguments)
                else:
                    return self.tool_execution_hook(function_name, function_call, arguments)
            
            # Temporarily replace tool hooks with cancellation-aware version
            self.agent.tool_hooks = [cancellation_aware_tool_hook]
            
            # Run in thread with cancellation monitoring
            result = [None]  # Use list to modify from inner function
            exception = [None]
            finished = threading.Event()
            
            def run_agent():
                try:
                    # Suppress agno library ERROR messages during cancellation
                    import logging
                    agno_logger = logging.getLogger('agno')
                    warning_logger = logging.getLogger('WARNING')
                    original_agno_level = agno_logger.level
                    original_warning_level = warning_logger.level
                    
                    if cancellation_event.is_set():
                        agno_logger.setLevel(logging.CRITICAL)
                        warning_logger.setLevel(logging.CRITICAL)
                    
                    result[0] = self.agent.run(query_text, stream=False)
                    
                    # Restore original log levels
                    agno_logger.setLevel(original_agno_level)
                    warning_logger.setLevel(original_warning_level)
                    
                except Exception as e:
                    exception[0] = e
                finally:
                    # Restore original tool hooks
                    self.agent.tool_hooks = original_hook
                    finished.set()
            
            # Start agent in background thread
            thread = threading.Thread(target=run_agent)
            thread.daemon = True
            thread.start()
            
            cancellation_logged = False
            # Check for cancellation every 0.05 seconds (more frequent)
            cancellation_wait_time = 0
            while not finished.is_set():
                if cancellation_event.is_set():
                    if not cancellation_logged:
                        logger.info("🛑 Query cancelled during AI processing - stopping gracefully")
                        cancellation_logged = True
                        
                        # Suppress agno library logging to prevent ERROR messages
                        import logging
                        agno_logger = logging.getLogger('agno')
                        agno_logger.setLevel(logging.CRITICAL)
                    
                    # Wait a very short time for graceful shutdown, then force stop
                    cancellation_wait_time += 0.05
                    if cancellation_wait_time >= 0.2:  # Only wait 200ms max
                        logger.info("🛑 Graceful shutdown complete - agent execution stopped")
                        # Restore original tool hooks before raising exception
                        self.agent.tool_hooks = original_hook
                        # Force the exception immediately to stop the main process
                        raise QueryCancelledException("Query was cancelled by user.")
                    
                await asyncio.sleep(0.05)
            
            # Check if there was an exception
            if exception[0]:
                raise exception[0]
                
            return result[0]
        
        # Helper function to run external services with cancellation support
        async def run_external_service_with_cancellation(service_func, *args, **kwargs):
            """Run external service calls in a thread while checking for cancellation"""
            if not cancellation_event:
                # No cancellation support, run normally
                return service_func(*args, **kwargs)
            
            # Run in thread with cancellation monitoring
            result = [None]
            exception = [None]
            finished = threading.Event()
            
            def run_service():
                try:
                    result[0] = service_func(*args, **kwargs)
                except Exception as e:
                    exception[0] = e
                finally:
                    finished.set()
            
            # Start service in background thread
            thread = threading.Thread(target=run_service)
            thread.daemon = True
            thread.start()
            
            cancellation_logged = False
            # Check for cancellation every 0.05 seconds (more frequent checks)
            cancellation_wait_time = 0
            while not finished.is_set():
                if cancellation_event.is_set():
                    if not cancellation_logged:
                        logger.info("Query cancelled during external service processing - forcing immediate stop")
                        cancellation_logged = True
                    
                    # Try to interrupt the thread if possible
                    if hasattr(service_func, '__self__') and hasattr(service_func.__self__, 'cancel'):
                        try:
                            service_func.__self__.cancel()
                            logger.info("Successfully sent cancel signal to external service")
                        except:
                            pass
                    
                    # Wait a very short time for graceful shutdown, then force stop
                    cancellation_wait_time += 0.05
                    if cancellation_wait_time >= 0.2:  # Only wait 200ms max
                        logger.info("Forcing immediate stop - background process will be abandoned")
                        raise QueryCancelledException("Query was cancelled by user.")
                    
                await asyncio.sleep(0.05)  # Check more frequently
            
            # Check if there was an exception
            if exception[0]:
                raise exception[0]
                
            return result[0]
        
        try:
            # Check for cancellation before starting processing
            check_cancellation()
            
            if self.use_web_search:
                # Handle web search using Agno agent
                logger.info("🌐 Agent Type: Web Search | Starting web search processing")
                print(f"[DEBUG] ========== STARTING WEB SEARCH PIPELINE EXECUTION ==========")
                print(f"[DEBUG] Query: {query}")
                logger.info("🌐 Web Search: Starting pipeline execution")
                check_cancellation()
                
                # Build context with token budget management for web search mode
                context_payload, context_meta = self.build_context_with_token_budget(chat_history, query, 'web', prev_summary_text=prev_summary_text)
                
                if context_payload:
                    # Configure agent to use context
                    self.agent.add_context = True
                    self.agent.context = context_payload
                    
                    # Build human-readable context sections for the query
                    context_lines = []
                    
                    # Add session summary if available (from summarized older conversations)
                    if 'session_summary' in context_payload and context_payload['session_summary']:
                        context_lines.append("Session summary:\n" + str(context_payload['session_summary']).strip())
                    
                    # Add recent chat history if available (raw messages that fit in token budget)
                    if 'chat_history' in context_payload and context_payload['chat_history']:
                        hist = []
                        for msg in context_payload['chat_history']:
                            role = msg.get('role', 'user')
                            content = msg.get('content', '')
                            hist.append(f"{role}: {content}")
                        context_lines.append("Recent messages:\n" + "\n".join(hist))
                    
                    # Combine all context sections into a single block with instruction prefix
                    context_block = (
                        "\n\nConversation history (consult this; for any question about prior conversation, answer strictly based on this history):\n"
                        + "\n\n".join(context_lines)
                    ) if context_lines else ""
                    
                    # Prepend context to the user's query for the agent
                    modified_query = (
                        context_block
                        + ("\n\nQuestion: " + query)
                    )
                    response = await run_agent_with_cancellation(modified_query)
                else:
                    response = await run_agent_with_cancellation(query)
                
                # Extract the response content
                answer = response.content if hasattr(response, 'content') else str(response)
                
                # Format tool results
                formatted_sources = self.format_tool_results(self.tool_results_dict)
                
                logger.info(f"Formatted tool results: {formatted_sources}")
                
                return {
                    "agent_name": self.name,
                    "query": query,
                    "answer": answer,
                    "web_search_enabled": True,
                    "document_search_enabled": False,
                    "deep_search_enabled": False,
                    "sources": formatted_sources,  # Using formatted sources instead of raw results
                    "context_meta": context_meta,  # Internal metadata for persistence
                }
        
            elif self.use_document_search:
                # Handle document search using external services
                logger.info("📄 Agent Type: Knowledge Search | Starting document search processing")
                print(f"[DEBUG] ========== STARTING KNOWLEDGE SEARCH PIPELINE EXECUTION ==========")
                print(f"[DEBUG] Query: {query}")
                logger.info("📄 Knowledge Search: Starting pipeline execution")
                
                # Build context payload respecting token budget constraints for document mode
                # This may return a summary of older messages + recent raw messages, or just recent messages
                context_payload, context_meta = self.build_context_with_token_budget(chat_history, query, 'document', prev_summary_text=prev_summary_text)
                
                # Convert context payload into format expected by external document service
                service_history = []
                if context_payload:
                    # If we have a session summary (older messages were summarized), add it as system context
                    if 'session_summary' in context_payload and context_payload['session_summary']:
                        service_history.append({
                            "role": "system",
                            "type": "session_summary",  # Special type to indicate this is summarized content
                            "content": context_payload['session_summary']
                        })
                    # Add any recent raw chat messages that fit within token budget
                    if 'chat_history' in context_payload and context_payload['chat_history']:
                        service_history.extend(context_payload['chat_history'])
                
                # Determine collection name: use provided collection_name, then self.collection_name, then username-based
                if collection_name:
                    target_collection = collection_name
                elif self.collection_name:
                    target_collection = self.collection_name
                else:
                    if not username:
                        raise ValueError("username is required for user/space-based document retrieval")
                    clean_username = ''.join(c if c.isalnum() else '_' for c in username.lower())
                    target_collection = clean_username

                allowed_files_param = getattr(self, 'allowed_files', None)

                # Check for cancellation before external service call
                if cancellation_event and cancellation_event.is_set():
                    logger.info("Query cancelled before document search")
                    return {
                        "agent_name": self.name,
                        "query": query,
                        "answer": "Query was cancelled by user.",
                        "web_search_enabled": False,
                        "document_search_enabled": True,
                        "deep_search_enabled": False,
                        "sources": []
                    }

                result = await run_external_service_with_cancellation(
                    external_services.query_documents,
                    query=query,
                    collection_name=target_collection,
                    username=username or "user",
                    agent_instructions=self.instructions,
                    allowed_files=allowed_files_param,
                    chat_history=service_history
                )
                logger.info(f"External service sources: {[source.get('source', 'Unknown') for source in result.get('document_sources', [])]}")

                # Build sources for frontend
                top_chunks = []
                document_ids = result.get("document_ids") or []
                document_sources = result.get("document_sources", [])
                
                # Build confidence map from document_sources using index mapping
                # document_sources and document_ids are in the same order from the agent service
                confidence_by_index = {}
                for idx, src in enumerate(document_sources):
                    confidence = src.get("confidence_score", src.get("relevance_score", 0.0))
                    confidence_by_index[idx] = confidence
                
                if document_ids and db is not None:
                    try:
                        # Lazy import to avoid circulars
                        from ..models.raw_document import RawDocument
                        # Fetch raw documents by IDs (async)
                        from sqlalchemy import select
                        from sqlalchemy.ext.asyncio import AsyncSession

                        # Ensure we only use async patterns
                        if isinstance(db, AsyncSession):
                            db_result = await db.execute(
                                select(RawDocument).where(RawDocument.id.in_(document_ids))
                            )
                            rows = db_result.scalars().all()
                        else:
                            # Fallback for sync session callers (e.g., tests)
                            rows = db.query(RawDocument).filter(RawDocument.id.in_(document_ids)).all()
                        id_to_row = {str(r.id): r for r in rows}
                        for idx, doc_id in enumerate(document_ids):
                            row = id_to_row.get(str(doc_id))
                            if not row:
                                continue
                            snippet = (row.content or "")
                            source_name = row.source or str(row.id)
                            # Get confidence score from the corresponding document_sources entry
                            confidence = confidence_by_index.get(idx, 0.0)
                            logger.info(f"Document {source_name} (ID: {doc_id}): confidence = {confidence:.4f}")
                            top_chunks.append({
                                "document": source_name,
                                "page_number": None,
                                "snippet": snippet,
                                "confidence_score": confidence,
                            })
                    except Exception as e:
                        logger.warning(f"Failed to build sources from document_ids: {e}")
                else:
                    # Fallback to legacy GPU-provided sources if present
                    for src in document_sources:
                        top_chunks.append({
                            "document": src.get("source", "Unknown"),
                            "page_number": src.get("page_number"),
                            "snippet": src.get("content_snippet", ""),
                            "confidence_score": src.get("confidence_score", src.get("relevance_score", 0.0))
                        })
                
                # Convert external service response to agent format
                return {
                    "agent_name": result.get("agent_name", self.name),
                    "query": result.get("query", query),
                    "answer": result.get("answer", "No answer generated"),
                    "web_search_enabled": False,
                    "document_search_enabled": True,
                    "deep_search_enabled": False,
                    "sources": top_chunks,
                    "used_rag": result.get("used_rag", False),
                    "processing_stats": result.get("processing_stats", {}),
                    "selected_files": getattr(self, 'allowed_files', None),
                    "error": result.get("error"),
                    "context_meta": context_meta,  # Internal metadata for persistence
                }
                
            elif self.use_deep_search:
                # Handle deep search using deep research pipeline
                logger.info("🔬 Agent Type: Deep Research | Starting deep research processing")
                print(f"[DEBUG] ================ STARTING DEEP RESEARCH PIPELINE EXECUTION =================")
                print(f"[DEBUG] Query: {query}")
                logger.info("🔬 Deep Research: Starting pipeline execution")
                
                # Build context meta (deep research doesn't use history)
                _, context_meta = self.build_context_with_token_budget(chat_history, query, 'deep', prev_summary_text=prev_summary_text)
                
                # Check for cancellation before deep search
                if cancellation_event and cancellation_event.is_set():
                    logger.info("Query cancelled before deep search")
                    return {
                        "agent_name": self.name,
                        "query": query,
                        "answer": "Query was cancelled by user.",
                        "web_search_enabled": False,
                        "document_search_enabled": False,
                        "deep_search_enabled": True,
                        "sources": [],
                        "context_meta": context_meta,
                    }
                
                try:
                    # Import and initialize the deep research tool
                    from ..deep_research import DeepResearchTool
                    
                    # Initialize the deep research tool
                    deep_tool = DeepResearchTool(debug=True)
                    
                    # Set cancellation event on deep tool if it supports it
                    if hasattr(deep_tool, 'set_cancellation_event'):
                        deep_tool.set_cancellation_event(cancellation_event)
                    elif hasattr(deep_tool, 'cancellation_event'):
                        deep_tool.cancellation_event = cancellation_event
                    
                    # Force set cancellation event directly on the deep tool
                    deep_tool._cancellation_event = cancellation_event
                    
                    # Define progress callback for real-time updates
                    progress_messages = []
                    def progress_callback(message):
                        progress_messages.append(message)
                        logger.info(f"Deep Research Progress: {message}")
                    
                    # Process the query using deep research with progress tracking
                    # Create a cancellation-aware progress callback
                    def cancellation_aware_progress_callback(message):
                        if cancellation_event and cancellation_event.is_set():
                            logger.info("Deep research cancelled during progress update")
                            raise QueryCancelledException("Query was cancelled by user.")
                        progress_messages.append(message)
                        logger.info(f"Deep Research Progress: {message}")
                    
                    # Create a wrapper function that checks cancellation before calling deep research
                    def cancellation_checked_research():
                        # Check cancellation before starting
                        if cancellation_event and cancellation_event.is_set():
                            logger.info("Deep research cancelled before starting internal pipeline")
                            raise QueryCancelledException("Query was cancelled by user.")
                        
                        # Monkey patch the deep tool to check cancellation frequently
                        original_progress_callback = cancellation_aware_progress_callback
                        
                        def frequent_cancellation_check_callback(message):
                            # Check cancellation on every progress update
                            if cancellation_event and cancellation_event.is_set():
                                logger.info(f"Deep research cancelled during: {message}")
                                raise QueryCancelledException("Query was cancelled by user.")
                            return original_progress_callback(message)
                        
                        # Try to patch internal methods if possible
                        if hasattr(deep_tool, 'pipeline') and hasattr(deep_tool.pipeline, 'check_cancellation'):
                            deep_tool.pipeline.check_cancellation = lambda: cancellation_event and cancellation_event.is_set()
                        
                        return deep_tool.research(query, frequent_cancellation_check_callback)
                    
                    answer = await run_external_service_with_cancellation(
                        cancellation_checked_research
                    )
                    
                    # Get tool results and reasoning
                    tool_results = deep_tool.get_tool_results()
                    agent_reasoning = deep_tool.get_agent_reasoning()
                    
                    # Format the sources using our deep research formatter
                    formatted_sources = self.format_deep_research_tool_results(tool_results)
                    
                    logger.info(f"Deep Research completed with {len(formatted_sources)} sources and {len(agent_reasoning)} reasoning entries")
                                       
                    return {
                        "agent_name": self.name,
                        "query": query,
                        "answer": answer,
                        "web_search_enabled": False,
                        "document_search_enabled": False,
                        "deep_search_enabled": True,
                        "sources": formatted_sources,
                        "reasoning": agent_reasoning,
                        "progress_messages": progress_messages,
                        "processing_stats": {
                            "total_tools_executed": len(tool_results),
                            "agents_with_reasoning": len(agent_reasoning),
                            "progress_updates": len(progress_messages)
                        },
                        "context_meta": context_meta,  # Internal metadata for persistence
                    }
                    
                except Exception as e:
                    if hasattr(e, 'is_cancelled') and e.is_cancelled:
                        logger.info("🔬 Deep Research: Query processing was cancelled by user (expected behavior)")
                        return {
                            "agent_name": self.name,
                            "query": query,
                            "answer": "Query processing was stopped by user.",
                            "web_search_enabled": False,
                            "document_search_enabled": False,
                            "deep_search_enabled": True,
                            "sources": [],
                            "reasoning": [],
                            "progress_messages": ["Query processing was stopped by user."],
                            "error": None
                        }
                    else:
                        logger.error(f"Error in deep research: {e}")
                        return {
                            "agent_name": self.name,
                            "query": query,
                            "answer": f"I encountered an error while conducting deep research: {str(e)}. Please try again or use a different search method.",
                            "web_search_enabled": False,
                            "document_search_enabled": False,
                            "deep_search_enabled": True,
                            "sources": [],
                            "reasoning": [],
                            "progress_messages": [f"Error occurred: {str(e)}"],
                            "error": str(e)
                        }
                
            else:
                # Handle basic query without search
                logger.info("💬 Agent Type: Basic Chat | Starting basic processing (no search)")
                print(f"[DEBUG] =================================STARTING BASIC CHAT PIPELINE EXECUTION ================================================")
                print(f"[DEBUG] Query: {query}")
                logger.info("💬 Basic Chat: Starting pipeline execution")
                
                # Build context payload with token budget constraints for basic chat mode
                context_payload, context_meta = self.build_context_with_token_budget(chat_history, query, 'basic', prev_summary_text=prev_summary_text)
                
                # Enable monitoring for the agent to track execution
                self.agent.monitoring=True
                if context_payload:
                    # Configure agent to use the built context
                    self.agent.add_context = True
                    self.agent.context = context_payload
                    context_lines = []
                    
                    # Add session summary to context if available
                    if 'session_summary' in context_payload and context_payload['session_summary']:
                        context_lines.append("Session summary:\n" + str(context_payload['session_summary']).strip())
                    
                    # Format chat history into readable conversation format
                    if 'chat_history' in context_payload and context_payload['chat_history']:
                        hist = []
                        for msg in context_payload['chat_history']:
                            role = msg.get('role', 'user')
                            content = msg.get('content', '')
                            hist.append(f"{role}: {content}")
                        context_lines.append("Recent messages:\n" + "\n".join(hist))
                    
                    # Build context block with instruction for the agent to consult history
                    context_block = (
                        "\n\nConversation history (consult this; for any question about prior conversation, answer strictly based on this history):\n"
                        + "\n\n".join(context_lines)
                    ) if context_lines else ""
                    
                    # Prepend context to the user's query for comprehensive understanding
                    modified_query = (
                        context_block
                        + ("\n\nQuestion: " + query)
                    )
                    response = await run_agent_with_cancellation(modified_query)
                else:
                    response = await run_agent_with_cancellation(query)
                answer = response.content if hasattr(response, 'content') else str(response)
                
                return {
                    "agent_name": self.name,
                    "query": query,
                    "answer": answer,
                    "web_search_enabled": False,
                    "document_search_enabled": False,
                    "deep_search_enabled": False,
                    "sources": [],
                    "context_meta": context_meta,  # Internal metadata for persistence
                }
        
        except QueryCancelledException:
            # Return cancellation response
            agent_type = "Web Search" if self.use_web_search else "Knowledge Search" if self.use_document_search else "Deep Research" if self.use_deep_search else "Basic Chat"
            logger.info(f"✅ {agent_type}: Query processing was cancelled by user (expected behavior)")
            return {
                "agent_name": self.name,
                "query": query,
                "answer": "Query was cancelled by user.",
                "web_search_enabled": self.use_web_search,
                "document_search_enabled": self.use_document_search,
                "deep_search_enabled": self.use_deep_search,
                "sources": []
            }
        except Exception as e:
            agent_type = "Web Search" if self.use_web_search else "Knowledge Search" if self.use_document_search else "Deep Research" if self.use_deep_search else "Basic Chat"
            if hasattr(e, 'is_cancelled') and e.is_cancelled:
                logger.info(f"✅ {agent_type}: Query processing was cancelled by user (expected behavior)")
                return {
                    "agent_name": self.name,
                    "query": query,
                    "answer": "Query processing was stopped by user.",
                    "web_search_enabled": self.use_web_search,
                    "document_search_enabled": self.use_document_search,
                    "deep_search_enabled": self.use_deep_search,
                    "sources": [],
                    "error": None
                }
            else:
                logger.error(f"❌ {agent_type}: Error processing query: {e}")
                return {
                    "agent_name": self.name,
                    "query": query,
                    "answer": f"I encountered an error while processing your query: {str(e)}",
                    "web_search_enabled": self.use_web_search,
                    "document_search_enabled": self.use_document_search,
                    "deep_search_enabled": self.use_deep_search,
                    "sources": self.format_tool_results(self.tool_results_dict) if self.tool_results_dict else [],
                    "error": str(e)
                }
