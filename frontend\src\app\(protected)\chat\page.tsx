'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { ChatProvider } from '@/contexts';
import { MainLayout } from '@/components/layout';
import { ToastContainer } from '@/components/notifications';
import KnowledgeBaseSidebar from '@/components/KnowledgeBaseSidebar';
import NotesPanel from '@/components/notes/NotesSidebarPanel';
import LiveChatSidebar from '@/components/live-chat/LiveChatSidebar';
import ChatPageContent from '@/components/ai-chat/ChatPageContent';

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  department: string;
  employee_id: string;
}

export default function ChatPage() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [liveChatSidebarOpen, setLiveChatSidebarOpen] = useState(false);
  const [promptGalleryOpen, setPromptGalleryOpen] = useState(false);
  const [notesOpen, setNotesOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [screenWidth, setScreenWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);
  const router = useRouter();

  // Track if any sidebar is open
  const anySidebarOpen = sidebarOpen || liveChatSidebarOpen || promptGalleryOpen;

  // Calculate responsive sidebar width - overlay on tablet, auto-adjust on large desktop
  const getSidebarWidth = () => {
    if (!anySidebarOpen || isMobile) return 'w-0';
    
    // Tablet range (768px - 1048px): Sidebar overlays, chat doesn't shrink
    if (screenWidth >= 768 && screenWidth <= 1048) {
      return 'w-0'; // No space reservation - sidebar overlays on tablet
    }
    
    // Large Desktop (1048px+): Chat interface auto-adjusts to sidebar
    if (screenWidth > 1048) return 'w-[380px]'; // Reduced width for more chat space
    return 'w-0';
  };

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem('access_token');
    const userData = localStorage.getItem('user');

    if (!token || !userData) {
      router.push('/login');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
    } catch (error) {
      console.error('Error parsing user data:', error);
      router.push('/login');
    } finally {
      setLoading(false);
    }
  }, [router]);

  // Detect mobile and screen width changes
  useEffect(() => {
    const detect = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setScreenWidth(width);
    };
    detect();
    window.addEventListener('resize', detect);
    return () => window.removeEventListener('resize', detect);
  }, []);

  // Listen for sidebar state changes
  useEffect(() => {
    const handleKnowledgeBaseOpen = () => setSidebarOpen(true);
    const handleKnowledgeBaseClose = () => setSidebarOpen(false);
    const handleNotesOpen = () => setNotesOpen(true);
    const handleNotesClose = () => setNotesOpen(false);
    const handleLiveChatOpen = () => setLiveChatSidebarOpen(true);
    const handleLiveChatClose = () => setLiveChatSidebarOpen(false);
    const handlePromptGalleryOpen = () => setPromptGalleryOpen(true);
    const handlePromptGalleryClose = () => setPromptGalleryOpen(false);

    window.addEventListener('open-knowledge-base', handleKnowledgeBaseOpen);
    window.addEventListener('close-knowledge-base', handleKnowledgeBaseClose);
    window.addEventListener('open-notes', handleNotesOpen);
    window.addEventListener('close-notes', handleNotesClose);
    window.addEventListener('open-live-chat', handleLiveChatOpen);
    window.addEventListener('close-live-chat', handleLiveChatClose);
    window.addEventListener('open-prompt-gallery', handlePromptGalleryOpen);
    window.addEventListener('close-prompt-gallery', handlePromptGalleryClose);

    return () => {
      window.removeEventListener('open-knowledge-base', handleKnowledgeBaseOpen);
      window.removeEventListener('close-knowledge-base', handleKnowledgeBaseClose);
      window.removeEventListener('open-notes', handleNotesOpen);
      window.removeEventListener('close-notes', handleNotesClose);
      window.removeEventListener('open-live-chat', handleLiveChatOpen);
      window.removeEventListener('close-live-chat', handleLiveChatClose);
      window.removeEventListener('open-prompt-gallery', handlePromptGalleryOpen);
      window.removeEventListener('close-prompt-gallery', handlePromptGalleryClose);
    };
  }, []);



  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <MainLayout>
      <ChatProvider>
        <ChatPageContent />

        {/* Other Sidebars */}
        {!isMobile && <ToastContainer />}
        <KnowledgeBaseSidebar onSidebarToggle={setSidebarOpen} />
        <NotesPanel onSidebarToggle={setNotesOpen} />
        <LiveChatSidebar onSidebarToggle={setLiveChatSidebarOpen} />
      </ChatProvider>
    </MainLayout>
  );
} 