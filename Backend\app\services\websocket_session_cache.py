"""
WebSocket Session Cache Service
Manages WebSocket session persistence using Redis for reconnection support
"""

import json
import logging
from typing import Dict, Any, Optional, List, Set
from datetime import datetime, timedelta
import asyncio

from .redis_service import redis_service
from .. import config

logger = logging.getLogger(__name__)


class WebSocketSessionCache:
    """
    Manages WebSocket session state in Redis for persistence across reconnections
    """
    
    def __init__(self):
        self.session_prefix = f"{config.CACHE_PREFIX_LIVE}:ws_session"
        self.online_users_key = f"{config.CACHE_PREFIX_LIVE}:online_users"
        self.user_channels_prefix = f"{config.CACHE_PREFIX_LIVE}:user_channels"
        self.session_ttl = 3600  # 1 hour session persistence
        
    async def save_session(
        self,
        user_id: int,
        session_data: Dict[str, Any],
        ttl: Optional[int] = None,
    ) -> bool:
        """Save WebSocket session data to Redis"""
        try:
            session_key = f"{self.session_prefix}:{user_id}"
            
            # Add timestamp
            session_data["last_activity"] = datetime.utcnow().isoformat()
            session_data["user_id"] = user_id
            
            # Save session data
            if ttl is None:
                # Preserve existing TTL to honor original token expiry
                success = await redis_service.set(
                    session_key,
                    session_data,
                    ttl=None,
                    keepttl=True,
                )
            else:
                success = await redis_service.set(
                    session_key,
                    session_data,
                    ttl=ttl,
                )
            
            # Add user to online users set
            if success:
                await redis_service.sadd(self.online_users_key, user_id)
                await redis_service.expire(self.online_users_key, self.session_ttl)
            
            logger.debug(f"Saved WebSocket session for user {user_id}")
            return success
            
        except Exception as e:
            logger.error(f"Error saving WebSocket session for user {user_id}: {e}")
            return False
    
    async def get_session(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Retrieve WebSocket session data from Redis"""
        try:
            session_key = f"{self.session_prefix}:{user_id}"
            session_data = await redis_service.get(session_key)
            
            if session_data:
                # Update last activity
                session_data["last_activity"] = datetime.utcnow().isoformat()
                await redis_service.set(
                    session_key, 
                    session_data, 
                    ttl=self.session_ttl
                )
                logger.debug(f"Retrieved WebSocket session for user {user_id}")
                
            return session_data
            
        except Exception as e:
            logger.error(f"Error retrieving WebSocket session for user {user_id}: {e}")
            return None
    
    async def update_session(
        self,
        user_id: int,
        updates: Dict[str, Any],
        ttl: Optional[int] = None,
    ) -> bool:
        """Update existing WebSocket session data"""
        try:
            session_data = await self.get_session(user_id)
            if not session_data:
                session_data = {}
            
            # Merge updates
            session_data.update(updates)
            
            return await self.save_session(user_id, session_data, ttl=ttl)
            
        except Exception as e:
            logger.error(f"Error updating WebSocket session for user {user_id}: {e}")
            return False
    
    async def delete_session(self, user_id: int) -> bool:
        """Delete WebSocket session data from Redis"""
        try:
            session_key = f"{self.session_prefix}:{user_id}"
            
            # Remove from session and online users
            deleted = await redis_service.delete(session_key)
            await redis_service.srem(self.online_users_key, user_id)
            
            # Clear user channels
            channels_key = f"{self.user_channels_prefix}:{user_id}"
            await redis_service.delete(channels_key)
            
            logger.debug(f"Deleted WebSocket session for user {user_id}")
            return deleted > 0
            
        except Exception as e:
            logger.error(f"Error deleting WebSocket session for user {user_id}: {e}")
            return False
    
    async def get_online_users(self) -> Set[int]:
        """Get set of online user IDs"""
        try:
            users = await redis_service.smembers(self.online_users_key)
            return {int(user_id) for user_id in users}
            
        except Exception as e:
            logger.error(f"Error getting online users: {e}")
            return set()
    
    async def is_user_online(self, user_id: int) -> bool:
        """Check if user is online"""
        try:
            return await redis_service.sismember(self.online_users_key, user_id)
            
        except Exception as e:
            logger.error(f"Error checking if user {user_id} is online: {e}")
            return False
    
    async def add_user_to_channel(
        self, 
        user_id: int, 
        channel: str
    ) -> bool:
        """Add user to a channel (group/conversation)"""
        try:
            channels_key = f"{self.user_channels_prefix}:{user_id}"
            await redis_service.sadd(channels_key, channel)
            await redis_service.expire(channels_key, self.session_ttl)
            
            logger.debug(f"Added user {user_id} to channel {channel}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding user {user_id} to channel {channel}: {e}")
            return False
    
    async def remove_user_from_channel(
        self, 
        user_id: int, 
        channel: str
    ) -> bool:
        """Remove user from a channel"""
        try:
            channels_key = f"{self.user_channels_prefix}:{user_id}"
            await redis_service.srem(channels_key, channel)
            
            logger.debug(f"Removed user {user_id} from channel {channel}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing user {user_id} from channel {channel}: {e}")
            return False
    
    async def get_user_channels(self, user_id: int) -> Set[str]:
        """Get all channels user is subscribed to"""
        try:
            channels_key = f"{self.user_channels_prefix}:{user_id}"
            channels = await redis_service.smembers(channels_key)
            return channels
            
        except Exception as e:
            logger.error(f"Error getting channels for user {user_id}: {e}")
            return set()
    
    async def cleanup_expired_sessions(self):
        """Clean up expired sessions (called periodically)"""
        try:
            # Get all session keys
            pattern = f"{self.session_prefix}:*"
            session_keys = await redis_service.get_keys(pattern)
            
            for key in session_keys:
                session_data = await redis_service.get(key)
                if session_data:
                    last_activity = session_data.get("last_activity")
                    if last_activity:
                        last_time = datetime.fromisoformat(last_activity)
                        if datetime.utcnow() - last_time > timedelta(hours=1):
                            # Session expired
                            user_id = session_data.get("user_id")
                            if user_id:
                                await self.delete_session(user_id)
                                logger.info(f"Cleaned up expired session for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {e}")
    
    async def persist_connection_state(
        self,
        user_id: int,
        connection_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Persist connection state for recovery"""
        try:
            connection_key = f"{config.CACHE_PREFIX_LIVE}:connection:{user_id}"
            connection_data = {
                "connection_id": connection_id,
                "user_id": user_id,
                "connected_at": datetime.utcnow().isoformat(),
                "metadata": metadata or {}
            }
            
            return await redis_service.set(
                connection_key,
                connection_data,
                ttl=self.session_ttl
            )
            
        except Exception as e:
            logger.error(f"Error persisting connection state for user {user_id}: {e}")
            return False
    
    async def get_connection_state(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get persisted connection state"""
        try:
            connection_key = f"{config.CACHE_PREFIX_LIVE}:connection:{user_id}"
            return await redis_service.get(connection_key)
            
        except Exception as e:
            logger.error(f"Error getting connection state for user {user_id}: {e}")
            return None


# Create singleton instance
ws_session_cache = WebSocketSessionCache()


# Background task for cleaning up expired sessions
async def cleanup_task():
    """Background task to clean up expired sessions periodically"""
    while True:
        try:
            await asyncio.sleep(3600)  # Run every hour
            await ws_session_cache.cleanup_expired_sessions()
        except Exception as e:
            logger.error(f"Error in cleanup task: {e}")
            await asyncio.sleep(60)  # Retry after 1 minute on error

