"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-jsx-runtime";
exports.ids = ["vendor-chunks/hast-util-to-jsx-runtime"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-jsx-runtime/lib/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toJsxRuntime: () => (/* binding */ toJsxRuntime)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! estree-util-is-identifier-name */ \"(ssr)/./node_modules/estree-util-is-identifier-name/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/hast-to-react.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var style_to_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! style-to-js */ \"(ssr)/./node_modules/style-to-js/cjs/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/**\n * @import {Identifier, Literal, MemberExpression} from 'estree'\n * @import {Jsx, JsxDev, Options, Props} from 'hast-util-to-jsx-runtime'\n * @import {Element, Nodes, Parents, Root, Text} from 'hast'\n * @import {MdxFlowExpressionHast, MdxTextExpressionHast} from 'mdast-util-mdx-expression'\n * @import {MdxJsxFlowElementHast, MdxJsxTextElementHast} from 'mdast-util-mdx-jsx'\n * @import {MdxjsEsmHast} from 'mdast-util-mdxjs-esm'\n * @import {Position} from 'unist'\n * @import {Child, Create, Field, JsxElement, State, Style} from './types.js'\n */\n\n\n\n\n\n\n\n\n\n\n\n// To do: next major: `Object.hasOwn`.\nconst own = {}.hasOwnProperty\n\n/** @type {Map<string, number>} */\nconst emptyMap = new Map()\n\nconst cap = /[A-Z]/g\n\n// `react-dom` triggers a warning for *any* white space in tables.\n// To follow GFM, `mdast-util-to-hast` injects line endings between elements.\n// Other tools might do so too, but they don’t do here, so we remove all of\n// that.\n\n// See: <https://github.com/facebook/react/pull/7081>.\n// See: <https://github.com/facebook/react/pull/7515>.\n// See: <https://github.com/remarkjs/remark-react/issues/64>.\n// See: <https://github.com/rehypejs/rehype-react/pull/29>.\n// See: <https://github.com/rehypejs/rehype-react/pull/32>.\n// See: <https://github.com/rehypejs/rehype-react/pull/45>.\nconst tableElements = new Set(['table', 'tbody', 'thead', 'tfoot', 'tr'])\n\nconst tableCellElement = new Set(['td', 'th'])\n\nconst docs = 'https://github.com/syntax-tree/hast-util-to-jsx-runtime'\n\n/**\n * Transform a hast tree to preact, react, solid, svelte, vue, etc.,\n * with an automatic JSX runtime.\n *\n * @param {Nodes} tree\n *   Tree to transform.\n * @param {Options} options\n *   Configuration (required).\n * @returns {JsxElement}\n *   JSX element.\n */\n\nfunction toJsxRuntime(tree, options) {\n  if (!options || options.Fragment === undefined) {\n    throw new TypeError('Expected `Fragment` in options')\n  }\n\n  const filePath = options.filePath || undefined\n  /** @type {Create} */\n  let create\n\n  if (options.development) {\n    if (typeof options.jsxDEV !== 'function') {\n      throw new TypeError(\n        'Expected `jsxDEV` in options when `development: true`'\n      )\n    }\n\n    create = developmentCreate(filePath, options.jsxDEV)\n  } else {\n    if (typeof options.jsx !== 'function') {\n      throw new TypeError('Expected `jsx` in production options')\n    }\n\n    if (typeof options.jsxs !== 'function') {\n      throw new TypeError('Expected `jsxs` in production options')\n    }\n\n    create = productionCreate(filePath, options.jsx, options.jsxs)\n  }\n\n  /** @type {State} */\n  const state = {\n    Fragment: options.Fragment,\n    ancestors: [],\n    components: options.components || {},\n    create,\n    elementAttributeNameCase: options.elementAttributeNameCase || 'react',\n    evaluater: options.createEvaluater ? options.createEvaluater() : undefined,\n    filePath,\n    ignoreInvalidStyle: options.ignoreInvalidStyle || false,\n    passKeys: options.passKeys !== false,\n    passNode: options.passNode || false,\n    schema: options.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_1__.svg : property_information__WEBPACK_IMPORTED_MODULE_1__.html,\n    stylePropertyNameCase: options.stylePropertyNameCase || 'dom',\n    tableCellAlignToStyle: options.tableCellAlignToStyle !== false\n  }\n\n  const result = one(state, tree, undefined)\n\n  // JSX element.\n  if (result && typeof result !== 'string') {\n    return result\n  }\n\n  // Text node or something that turned into nothing.\n  return state.create(\n    tree,\n    state.Fragment,\n    {children: result || undefined},\n    undefined\n  )\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction one(state, node, key) {\n  if (node.type === 'element') {\n    return element(state, node, key)\n  }\n\n  if (node.type === 'mdxFlowExpression' || node.type === 'mdxTextExpression') {\n    return mdxExpression(state, node)\n  }\n\n  if (node.type === 'mdxJsxFlowElement' || node.type === 'mdxJsxTextElement') {\n    return mdxJsxElement(state, node, key)\n  }\n\n  if (node.type === 'mdxjsEsm') {\n    return mdxEsm(state, node)\n  }\n\n  if (node.type === 'root') {\n    return root(state, node, key)\n  }\n\n  if (node.type === 'text') {\n    return text(state, node)\n  }\n}\n\n/**\n * Handle element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction element(state, node, key) {\n  const parentSchema = state.schema\n  let schema = parentSchema\n\n  if (node.tagName.toLowerCase() === 'svg' && parentSchema.space === 'html') {\n    schema = property_information__WEBPACK_IMPORTED_MODULE_1__.svg\n    state.schema = schema\n  }\n\n  state.ancestors.push(node)\n\n  const type = findComponentFromName(state, node.tagName, false)\n  const props = createElementProps(state, node)\n  let children = createChildren(state, node)\n\n  if (tableElements.has(node.tagName)) {\n    children = children.filter(function (child) {\n      return typeof child === 'string' ? !(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(child) : true\n    })\n  }\n\n  addNode(state, props, type, node)\n  addChildren(props, children)\n\n  // Restore.\n  state.ancestors.pop()\n  state.schema = parentSchema\n\n  return state.create(node, type, props, key)\n}\n\n/**\n * Handle MDX expression.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxFlowExpressionHast | MdxTextExpressionHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxExpression(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    const program = node.data.estree\n    const expression = program.body[0]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(expression.type === 'ExpressionStatement')\n\n    // Assume result is a child.\n    return /** @type {Child | undefined} */ (\n      state.evaluater.evaluateExpression(expression.expression)\n    )\n  }\n\n  crashEstree(state, node.position)\n}\n\n/**\n * Handle MDX ESM.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxjsEsmHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxEsm(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    // Assume result is a child.\n    return /** @type {Child | undefined} */ (\n      state.evaluater.evaluateProgram(node.data.estree)\n    )\n  }\n\n  crashEstree(state, node.position)\n}\n\n/**\n * Handle MDX JSX.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxJsxElement(state, node, key) {\n  const parentSchema = state.schema\n  let schema = parentSchema\n\n  if (node.name === 'svg' && parentSchema.space === 'html') {\n    schema = property_information__WEBPACK_IMPORTED_MODULE_1__.svg\n    state.schema = schema\n  }\n\n  state.ancestors.push(node)\n\n  const type =\n    node.name === null\n      ? state.Fragment\n      : findComponentFromName(state, node.name, true)\n  const props = createJsxElementProps(state, node)\n  const children = createChildren(state, node)\n\n  addNode(state, props, type, node)\n  addChildren(props, children)\n\n  // Restore.\n  state.ancestors.pop()\n  state.schema = parentSchema\n\n  return state.create(node, type, props, key)\n}\n\n/**\n * Handle root.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Root} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction root(state, node, key) {\n  /** @type {Props} */\n  const props = {}\n\n  addChildren(props, createChildren(state, node))\n\n  return state.create(node, state.Fragment, props, key)\n}\n\n/**\n * Handle text.\n *\n * @param {State} _\n *   Info passed around.\n * @param {Text} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction text(_, node) {\n  return node.value\n}\n\n/**\n * Add `node` to props.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Props} props\n *   Props.\n * @param {unknown} type\n *   Type.\n * @param {Element | MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addNode(state, props, type, node) {\n  // If this is swapped out for a component:\n  if (typeof type !== 'string' && type !== state.Fragment && state.passNode) {\n    props.node = node\n  }\n}\n\n/**\n * Add children to props.\n *\n * @param {Props} props\n *   Props.\n * @param {Array<Child>} children\n *   Children.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChildren(props, children) {\n  if (children.length > 0) {\n    const value = children.length > 1 ? children : children[0]\n\n    if (value) {\n      props.children = value\n    }\n  }\n}\n\n/**\n * @param {string | undefined} _\n *   Path to file.\n * @param {Jsx} jsx\n *   Dynamic.\n * @param {Jsx} jsxs\n *   Static.\n * @returns {Create}\n *   Create a production element.\n */\nfunction productionCreate(_, jsx, jsxs) {\n  return create\n  /** @type {Create} */\n  function create(_, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children)\n    const fn = isStaticChildren ? jsxs : jsx\n    return key ? fn(type, props, key) : fn(type, props)\n  }\n}\n\n/**\n * @param {string | undefined} filePath\n *   Path to file.\n * @param {JsxDev} jsxDEV\n *   Development.\n * @returns {Create}\n *   Create a development element.\n */\nfunction developmentCreate(filePath, jsxDEV) {\n  return create\n  /** @type {Create} */\n  function create(node, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children)\n    const point = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_4__.pointStart)(node)\n    return jsxDEV(\n      type,\n      props,\n      key,\n      isStaticChildren,\n      {\n        columnNumber: point ? point.column - 1 : undefined,\n        fileName: filePath,\n        lineNumber: point ? point.line : undefined\n      },\n      undefined\n    )\n  }\n}\n\n/**\n * Create props from an element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current element.\n * @returns {Props}\n *   Props.\n */\nfunction createElementProps(state, node) {\n  /** @type {Props} */\n  const props = {}\n  /** @type {string | undefined} */\n  let alignValue\n  /** @type {string} */\n  let prop\n\n  for (prop in node.properties) {\n    if (prop !== 'children' && own.call(node.properties, prop)) {\n      const result = createProperty(state, prop, node.properties[prop])\n\n      if (result) {\n        const [key, value] = result\n\n        if (\n          state.tableCellAlignToStyle &&\n          key === 'align' &&\n          typeof value === 'string' &&\n          tableCellElement.has(node.tagName)\n        ) {\n          alignValue = value\n        } else {\n          props[key] = value\n        }\n      }\n    }\n  }\n\n  if (alignValue) {\n    // Assume style is an object.\n    const style = /** @type {Style} */ (props.style || (props.style = {}))\n    style[state.stylePropertyNameCase === 'css' ? 'text-align' : 'textAlign'] =\n      alignValue\n  }\n\n  return props\n}\n\n/**\n * Create props from a JSX element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current JSX element.\n * @returns {Props}\n *   Props.\n */\nfunction createJsxElementProps(state, node) {\n  /** @type {Props} */\n  const props = {}\n\n  for (const attribute of node.attributes) {\n    if (attribute.type === 'mdxJsxExpressionAttribute') {\n      if (attribute.data && attribute.data.estree && state.evaluater) {\n        const program = attribute.data.estree\n        const expression = program.body[0]\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(expression.type === 'ExpressionStatement')\n        const objectExpression = expression.expression\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(objectExpression.type === 'ObjectExpression')\n        const property = objectExpression.properties[0]\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(property.type === 'SpreadElement')\n\n        Object.assign(\n          props,\n          state.evaluater.evaluateExpression(property.argument)\n        )\n      } else {\n        crashEstree(state, node.position)\n      }\n    } else {\n      // For JSX, the author is responsible of passing in the correct values.\n      const name = attribute.name\n      /** @type {unknown} */\n      let value\n\n      if (attribute.value && typeof attribute.value === 'object') {\n        if (\n          attribute.value.data &&\n          attribute.value.data.estree &&\n          state.evaluater\n        ) {\n          const program = attribute.value.data.estree\n          const expression = program.body[0]\n          ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(expression.type === 'ExpressionStatement')\n          value = state.evaluater.evaluateExpression(expression.expression)\n        } else {\n          crashEstree(state, node.position)\n        }\n      } else {\n        value = attribute.value === null ? true : attribute.value\n      }\n\n      // Assume a prop.\n      props[name] = /** @type {Props[keyof Props]} */ (value)\n    }\n  }\n\n  return props\n}\n\n/**\n * Create children.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Parents} node\n *   Current element.\n * @returns {Array<Child>}\n *   Children.\n */\nfunction createChildren(state, node) {\n  /** @type {Array<Child>} */\n  const children = []\n  let index = -1\n  /** @type {Map<string, number>} */\n  // Note: test this when Solid doesn’t want to merge my upcoming PR.\n  /* c8 ignore next */\n  const countsByName = state.passKeys ? new Map() : emptyMap\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n    /** @type {string | undefined} */\n    let key\n\n    if (state.passKeys) {\n      const name =\n        child.type === 'element'\n          ? child.tagName\n          : child.type === 'mdxJsxFlowElement' ||\n              child.type === 'mdxJsxTextElement'\n            ? child.name\n            : undefined\n\n      if (name) {\n        const count = countsByName.get(name) || 0\n        key = name + '-' + count\n        countsByName.set(name, count + 1)\n      }\n    }\n\n    const result = one(state, child, key)\n    if (result !== undefined) children.push(result)\n  }\n\n  return children\n}\n\n/**\n * Handle a property.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} prop\n *   Key.\n * @param {Array<number | string> | boolean | number | string | null | undefined} value\n *   hast property value.\n * @returns {Field | undefined}\n *   Field for runtime, optional.\n */\nfunction createProperty(state, prop, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_5__.find)(state.schema, prop)\n\n  // Ignore nullish and `NaN` values.\n  if (\n    value === null ||\n    value === undefined ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return\n  }\n\n  if (Array.isArray(value)) {\n    // Accept `array`.\n    // Most props are space-separated.\n    value = info.commaSeparated ? (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_6__.stringify)(value) : (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_7__.stringify)(value)\n  }\n\n  // React only accepts `style` as object.\n  if (info.property === 'style') {\n    let styleObject =\n      typeof value === 'object' ? value : parseStyle(state, String(value))\n\n    if (state.stylePropertyNameCase === 'css') {\n      styleObject = transformStylesToCssCasing(styleObject)\n    }\n\n    return ['style', styleObject]\n  }\n\n  return [\n    state.elementAttributeNameCase === 'react' && info.space\n      ? property_information__WEBPACK_IMPORTED_MODULE_8__.hastToReact[info.property] || info.property\n      : info.attribute,\n    value\n  ]\n}\n\n/**\n * Parse a CSS declaration to an object.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} value\n *   CSS declarations.\n * @returns {Style}\n *   Properties.\n * @throws\n *   Throws `VFileMessage` when CSS cannot be parsed.\n */\nfunction parseStyle(state, value) {\n  try {\n    return style_to_js__WEBPACK_IMPORTED_MODULE_0__(value, {reactCompat: true})\n  } catch (error) {\n    if (state.ignoreInvalidStyle) {\n      return {}\n    }\n\n    const cause = /** @type {Error} */ (error)\n    const message = new vfile_message__WEBPACK_IMPORTED_MODULE_9__.VFileMessage('Cannot parse `style` attribute', {\n      ancestors: state.ancestors,\n      cause,\n      ruleId: 'style',\n      source: 'hast-util-to-jsx-runtime'\n    })\n    message.file = state.filePath || undefined\n    message.url = docs + '#cannot-parse-style-attribute'\n\n    throw message\n  }\n}\n\n/**\n * Create a JSX name from a string.\n *\n * @param {State} state\n *   To do.\n * @param {string} name\n *   Name.\n * @param {boolean} allowExpression\n *   Allow member expressions and identifiers.\n * @returns {unknown}\n *   To do.\n */\nfunction findComponentFromName(state, name, allowExpression) {\n  /** @type {Identifier | Literal | MemberExpression} */\n  let result\n\n  if (!allowExpression) {\n    result = {type: 'Literal', value: name}\n  } else if (name.includes('.')) {\n    const identifiers = name.split('.')\n    let index = -1\n    /** @type {Identifier | Literal | MemberExpression | undefined} */\n    let node\n\n    while (++index < identifiers.length) {\n      /** @type {Identifier | Literal} */\n      const prop = (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_10__.name)(identifiers[index])\n        ? {type: 'Identifier', name: identifiers[index]}\n        : {type: 'Literal', value: identifiers[index]}\n      node = node\n        ? {\n            type: 'MemberExpression',\n            object: node,\n            property: prop,\n            computed: Boolean(index && prop.type === 'Literal'),\n            optional: false\n          }\n        : prop\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(node, 'always a result')\n    result = node\n  } else {\n    result =\n      (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_10__.name)(name) && !/^[a-z]/.test(name)\n        ? {type: 'Identifier', name}\n        : {type: 'Literal', value: name}\n  }\n\n  // Only literals can be passed in `components` currently.\n  // No identifiers / member expressions.\n  if (result.type === 'Literal') {\n    const name = /** @type {string | number} */ (result.value)\n    return own.call(state.components, name) ? state.components[name] : name\n  }\n\n  // Assume component.\n  if (state.evaluater) {\n    return state.evaluater.evaluateExpression(result)\n  }\n\n  crashEstree(state)\n}\n\n/**\n * @param {State} state\n * @param {Position | undefined} [place]\n * @returns {never}\n */\nfunction crashEstree(state, place) {\n  const message = new vfile_message__WEBPACK_IMPORTED_MODULE_9__.VFileMessage(\n    'Cannot handle MDX estrees without `createEvaluater`',\n    {\n      ancestors: state.ancestors,\n      place,\n      ruleId: 'mdx-estree',\n      source: 'hast-util-to-jsx-runtime'\n    }\n  )\n  message.file = state.filePath || undefined\n  message.url = docs + '#cannot-handle-mdx-estrees-without-createevaluater'\n\n  throw message\n}\n\n/**\n * Transform a DOM casing style object to a CSS casing style object.\n *\n * @param {Style} domCasing\n * @returns {Style}\n */\nfunction transformStylesToCssCasing(domCasing) {\n  /** @type {Style} */\n  const cssCasing = {}\n  /** @type {string} */\n  let from\n\n  for (from in domCasing) {\n    if (own.call(domCasing, from)) {\n      cssCasing[transformStyleToCssCasing(from)] = domCasing[from]\n    }\n  }\n\n  return cssCasing\n}\n\n/**\n * Transform a DOM casing style field to a CSS casing style field.\n *\n * @param {string} from\n * @returns {string}\n */\nfunction transformStyleToCssCasing(from) {\n  let to = from.replace(cap, toDash)\n  // Handle `ms-xxx` -> `-ms-xxx`.\n  if (to.slice(0, 3) === 'ms-') to = '-' + to\n  return to\n}\n\n/**\n * Make `$0` dash cased.\n *\n * @param {string} $0\n *   Capitalized ASCII leter.\n * @returns {string}\n *   Dash and lower letter.\n */\nfunction toDash($0) {\n  return '-' + $0.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js\n");

/***/ })

};
;