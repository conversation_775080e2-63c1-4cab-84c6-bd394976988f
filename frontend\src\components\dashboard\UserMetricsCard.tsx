'use client';

import React from 'react';

interface UserMetrics {
  ai_query_count: number;
  knowledge_docs_uploaded: number;
  knowledge_docs_indexed: number;
  feedback_submitted: number;
  feedback_helpful_count: number;
  feedback_avg_rating: number;
  ai_responses_shared: number;
  is_online: boolean;
  last_seen: string | null;
}

interface UserMetricsCardProps {
  user: {
    user_id: number;
    username: string;
    email: string;
    industry: string | null;
    role: string;
    created_at: string | null;
  };
  metrics: UserMetrics;
  isCollapsed?: boolean;
  onToggleExpand?: (user: UserMetricsCardProps['user'] & { metrics: UserMetrics }) => void;
}

export default function UserMetricsCard({ user, metrics, isCollapsed = false, onToggleExpand }: UserMetricsCardProps) {
  const formatLastSeen = (lastSeenStr: string | null) => {
    if (!lastSeenStr) return 'Never';
    
    const lastSeen = new Date(lastSeenStr);
    const now = new Date();
    const diffInMs = now.getTime() - lastSeen.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return lastSeen.toLocaleDateString();
  };

  const getOnlineStatusColor = () => {
    return metrics.is_online ? 'bg-green-400' : 'bg-gray-400';
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 8) return 'text-green-600';
    if (rating >= 6) return 'text-yellow-600';
    if (rating >= 4) return 'text-orange-600';
    return 'text-red-600';
  };

  const getRoleColor = (role: string) => {
    return role === 'admin' ? 'text-red-800 bg-red-200' : 'text-green-800 bg-green-200';
  };

  const getRoleLabel = (role: string) => {
    return role === 'admin' ? 'Admin' : 'User';
  };

  if (isCollapsed) {
    return (
      <div
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-pointer hover:shadow-md transition-all"
        onClick={() => onToggleExpand?.({ ...user, metrics })}
      >
        {/* Header */}
        <div className="flex items-center space-x-3 mb-4">
          <div className="relative">
            <div className="flex-shrink-0 h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-900 rounded-full flex items-center justify-center shadow-sm">
              <span className="text-white font-semibold text-lg">
                {user.username.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className={`absolute -bottom-0.5 -right-0.5 h-3.5 w-3.5 rounded-full border-2 border-white ${getOnlineStatusColor()}`}></div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-sm font-semibold text-gray-900 truncate">{user.username}</h3>
              <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getRoleColor(user.role)}`}>
                {getRoleLabel(user.role)}
              </span>
            </div>
            <p className="text-xs text-gray-500 truncate">{user.email}</p>
          </div>
        </div>

        {/* Metrics Grid - Unified Color */}
        <div className="grid grid-cols-2 gap-3 mb-3">
          <div className="bg-blue-50 rounded-lg p-3 text-center">
            <p className="text-2xl font-bold text-blue-700">{metrics.ai_query_count}</p>
            <p className="text-xs text-gray-600 mt-1">AI Queries</p>
          </div>

          <div className="bg-blue-50 rounded-lg p-3 text-center">
            <p className="text-2xl font-bold text-blue-700">{metrics.knowledge_docs_uploaded}</p>
            <p className="text-xs text-gray-600 mt-1">Documents</p>
          </div>

          <div className="bg-blue-50 rounded-lg p-3 text-center">
            <p className="text-2xl font-bold text-blue-700">{metrics.feedback_submitted}</p>
            <p className="text-xs text-gray-600 mt-1">Feedback</p>
          </div>

          <div className="bg-blue-50 rounded-lg p-3 text-center">
            <p className="text-2xl font-bold text-blue-700">{metrics.ai_responses_shared}</p>
            <p className="text-xs text-gray-600 mt-1">Shared</p>
          </div>
        </div>

        {/* Footer Info */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <p className="text-xs text-gray-500">{formatLastSeen(metrics.last_seen)}</p>
          <p className="text-xs text-gray-400">Click for more details</p>
        </div>
      </div>
    );
  }

  // Since we're only using modal now, this expanded view is never reached
  // But keeping the structure for potential future use
  return null;
}