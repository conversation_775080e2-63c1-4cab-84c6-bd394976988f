import React, { useState, FormEvent, useRef, useEffect } from 'react';
import ContextUsageIndicator from './ContextUsageIndicator';
import { useChat } from '@/contexts';
import FileSelector from '../FileSelector';
import SearchToggle from '../ui/SearchToggle';

export default function ChatInput() {
  const {
    selectedAgent,
    sendMessage,
    isLoading,
    stopProcessing,
    useWebSearch,
    setUseWebSearch,
    useDocumentSearch,
    setUseDocumentSearch,
    useDeepSearch,
    setUseDeepSearch,
    selectedFiles,
    availableFiles,
    messages,
    contextUsageState,
  } = useChat();

  const [message, setMessage] = useState('');
  const [showFileWarning, setShowFileWarning] = useState(false);
  const [showFileSelector, setShowFileSelector] = useState(false);
  const [showSelectedFilesOverlay, setShowSelectedFilesOverlay] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  // Reference to the ChatInput container so we can scroll into view on mount
  const containerRef = useRef<HTMLDivElement>(null);

  // Command history state
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [savedMessage, setSavedMessage] = useState('');
  const [commandHistory, setCommandHistory] = useState<string[]>([]);

  // Ensure ChatInput is visible when the page first loads
  useEffect(() => {
    containerRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' });
  }, []);

  // Update command history when messages change
  useEffect(() => {
    const userMessages = messages
      .filter(msg => msg.role === 'user')
      .map(msg => msg.content)
      .filter(content => content.trim() !== ''); // Filter out empty messages

    setCommandHistory(userMessages);
    // Reset history index when history changes
    setHistoryIndex(-1);
    setSavedMessage('');
  }, [messages]);

  // Listen for prompt selection events to populate the input field
  useEffect(() => {
    const handlePopulateInput = (event: CustomEvent) => {
      const { message: promptMessage } = event.detail;
      setMessage(promptMessage);
      // Focus the input field after populating
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    };

    window.addEventListener('populate-chat-input', handlePopulateInput as EventListener);
    return () => window.removeEventListener('populate-chat-input', handlePopulateInput as EventListener);
  }, []);

  // Handle file selection requirements for document search
  useEffect(() => {
    // Hide file warning if document search is disabled
    if (!useDocumentSearch) {
      setShowFileWarning(false);
    }

    // Hide warning if files are selected
    if (useDocumentSearch && selectedFiles.length > 0) {
      setShowFileWarning(false);
    }
  }, [useDocumentSearch, selectedFiles]);

  // Handle command history navigation
  const handleHistoryNavigation = (direction: 'up' | 'down') => {
    if (commandHistory.length === 0) return;

    if (direction === 'up') {
      if (historyIndex === -1) {
        // Save current message before going to history
        setSavedMessage(message);
        setHistoryIndex(commandHistory.length - 1);
        setMessage(commandHistory[commandHistory.length - 1]);
      } else if (historyIndex > 0) {
        setHistoryIndex(historyIndex - 1);
        setMessage(commandHistory[historyIndex - 1]);
      }
    } else if (direction === 'down') {
      if (historyIndex === -1) return; // Already at current input

      if (historyIndex < commandHistory.length - 1) {
        setHistoryIndex(historyIndex + 1);
        setMessage(commandHistory[historyIndex + 1]);
      } else {
        // Return to current input (saved message)
        setHistoryIndex(-1);
        setMessage(savedMessage);
      }
    }

    // Focus and move cursor to end
    setTimeout(() => {
      const textarea = inputRef.current;
      if (textarea) {
        textarea.focus();
        textarea.setSelectionRange(textarea.value.length, textarea.value.length);
      }
    }, 0);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    const trimmedMessage = message.trim();
    if (!trimmedMessage || !selectedAgent || isLoading) return;

    // Check if document search is enabled but no files are selected
    if (useDocumentSearch && selectedFiles.length === 0) {
      setShowFileWarning(true);
      return;
    }

    setShowFileWarning(false);
    setMessage('');

    // Reset history navigation state when sending a new message
    setHistoryIndex(-1);
    setSavedMessage('');

    // Reset textarea height after clearing message
    setTimeout(() => {
      const textarea = inputRef.current;
      if (textarea) {
        textarea.style.height = 'auto';
        // Reset to minimum height (matches min-h-[48px] sm:min-h-[55px] classes)
        const minHeight = window.innerWidth >= 640 ? '55px' : '48px';
        textarea.style.height = minHeight;
      }
    }, 0);

    await sendMessage(trimmedMessage);
  };

  // Auto-resize textarea
  const handleInput = () => {
    const textarea = inputRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const minHeight = window.innerWidth >= 640 ? 55 : 48; // Match CSS min-height
      const newHeight = Math.max(Math.min(textarea.scrollHeight, 200), minHeight);
      textarea.style.height = `${newHeight}px`;
    }
  };

  const toggleWebSearch = () => {
    if (useWebSearch) {
      // If already selected, just deselect it
      setUseWebSearch(false);
    } else {
      // If not selected, select it and deselect others
      setUseWebSearch(true);
      setUseDocumentSearch(false);
      setUseDeepSearch(false);
    }
  };

  const toggleDocumentSearch = () => {
    if (useDocumentSearch) {
      // If already selected, just deselect it
      setUseDocumentSearch(false);
    } else {
      // If not selected, select it and deselect others
      setUseDocumentSearch(true);
      setUseWebSearch(false);
      setUseDeepSearch(false);
    }
  };

  const toggleDeepSearch = () => {
    if (useDeepSearch) {
      // If already selected, just deselect it
      setUseDeepSearch(false);
    } else {
      // If not selected, select it and deselect others
      setUseDeepSearch(true);
      setUseWebSearch(false);
      setUseDocumentSearch(false);
    }
  };

  if (!selectedAgent) {
    return null;
  }

  return (
    <div ref={containerRef} className="bg-gray-200 rounded-lg">
      {/* File selector overlay */}
      <FileSelector isOpen={showFileSelector} onClose={() => setShowFileSelector(false)} />

      {/* Selected files overlay */}
      {showSelectedFilesOverlay && selectedFiles.length > 0 && (
        <div
          className="fixed inset-0 z-50 flex items-start justify-center pt-20 bg-black bg-opacity-30"
          onClick={() => setShowSelectedFilesOverlay(false)}
        >
          <div
            className="bg-white rounded-lg shadow-lg w-full max-w-sm mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between p-3 border-b border-gray-200">
              <h3 className="text-sm font-medium text-gray-700">Selected Documents</h3>
              <button
                onClick={() => setShowSelectedFilesOverlay(false)}
                className="text-gray-500 hover:text-gray-700 text-lg leading-none"
              >
                ×
              </button>
            </div>
            <div className="p-4 space-y-2 max-h-60 overflow-y-auto">
              {selectedFiles.map((filename) => {
                const file = availableFiles.find((f) => f.filename === filename);
                return (
                  <div key={filename} className="text-sm text-gray-700 truncate">
                    {file?.title || filename}
                  </div>
                );
              })}
            </div>
            <div className="p-3 border-t border-gray-200 flex justify-end">
              <button
                onClick={() => setShowSelectedFilesOverlay(false)}
                className="px-4 py-2 text-sm bg-blue-900 text-white rounded hover:bg-blue-700"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Search type toggles */}
      <div className="flex flex-wrap items-center gap-1 w-full px-2 py-2 pr-2 rounded-lg  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        {/* Web Search */}
        <SearchToggle
          enabled={useWebSearch}
          onToggle={toggleWebSearch}
          label="Search"
          variant="blue"
          compact
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M2 12h20M12 2c3.978 3.251 4 12 0 20-4-8-3.978-16.749 0-20z"
              />
            </svg>
          }
        />

        {/* Document Search */}
        <SearchToggle
          enabled={useDocumentSearch}
          onToggle={toggleDocumentSearch}
          label="Knowledge Search"
          variant="green"
          compact
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 14l9-5-9-5-9 5 9 5z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 14l9-5-9-5-9 5 9 5z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 20.5l9-5-9-5-9 5 9 5z"
              />
            </svg>
          }
        />

        {/* Deep Research */}
        <SearchToggle
          enabled={useDeepSearch}
          onToggle={toggleDeepSearch}
          label="Deep Research"
          variant="purple"
          compact
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10.5 3a1.5 1.5 0 013 0v6.379a4.5 4.5 0 112.598 8.286l3.468 3.468a1.5 1.5 0 11-2.122 2.121l-3.468-3.468A4.5 4.5 0 0110.5 9.379V3z"
              />
            </svg>
          }
        />

        {/* Context Usage Indicator - hidden when Deep Research is ON */}
        {!useDeepSearch && contextUsageState && (
          <div className={useDocumentSearch && selectedFiles.length > 0 ? 'ml-2' : ''}>
            <ContextUsageIndicator
              percentUsed={contextUsageState.percentUsed}
              usedTokens={contextUsageState.usedTokens}
              remainingTokens={contextUsageState.remainingTokens}
              inputBudget={contextUsageState.inputBudget}
            />
          </div>
        )}

        {/* Selected Files */}
        {useDocumentSearch && (
                  <div
                    className={`flex items-stretch overflow-hidden py-0.5 rounded-full text-xs
                    ${selectedFiles.length > 0
                      ? 'bg-green-100 text-green-700 border border-green-300 divide-x divide-green-300'
                      : 'bg-gray-100 text-gray-700 border border-gray-200 divide-x divide-gray-300'
                    }`}
                  >
                    <span className="px-2 py-1 flex items-center">{selectedFiles.length}</span>

                    <button
                      type="button"
                      onClick={() => setShowSelectedFilesOverlay(true)}
                      className={`px-2 sm:px-3 flex items-center hover:bg-green-200 focus:outline-none`}
                      title="View selected files"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                          stroke="currentColor" className="w-4 h-4">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.522 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.478 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>

                    <button
                      type="button"
                      onClick={() => setShowFileSelector(true)}
                      className={`px-2 sm:px-3 flex items-center hover:bg-green-200 focus:outline-none`}
                      title="Add or remove files"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                          stroke="currentColor" className="w-4 h-4">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                    </button>
                  </div>
                )}

        <span className="ml-auto text-xs text-gray-600 italic whitespace-nowrap">
          {useWebSearch ? 'short quick search' : useDocumentSearch ? 'knowledge base linked' : useDeepSearch ? 'Will search the web more deeply' : 'Select any mode for grounded knowledge.'}
        </span>
      </div>


      {/* File selection warning for document search */}
      {showFileWarning && (
        <div className="flex items-center px-4 py-2 border-b border-yellow-200 bg-yellow-50">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-4 h-4 mr-2 text-yellow-600"
          >
            <path
              fillRule="evenodd"
              d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z"
              clipRule="evenodd"
            />
          </svg>
          <span className="text-sm text-yellow-800">
            Please select at least one document file before asking a question with document search enabled.
          </span>
        </div>
      )}
      <form onSubmit={handleSubmit} className="order-t border-gray-200">
        <div className="flex items-center justify-center pb-1 sm:pt-1">
          <div className="flex-1 relative">
            {/* plus button inside - show when no search type is selected or for document search */}
            {(!useWebSearch && !useDocumentSearch && !useDeepSearch || useDocumentSearch) && (
              <button
                type="button"
                title="Add files"
                onClick={() => {
                  // Trigger global event to open the Upload Document modal from KnowledgeBaseSidebar
                  window.dispatchEvent(new Event('open-upload-modal'));
                }}
                className="absolute left-3 top-2.5 w-8 h-8 rounded-full bg-gray-300 hover:bg-gray-500 text-black flex items-center justify-center shadow z-10"
              >
                <span className="text-base leading-none">+</span>
              </button>
            )}

            <textarea
              ref={inputRef}
              value={message}
              onChange={(e) => {
                setMessage(e.target.value);
                // Reset history navigation if user starts typing after navigating
                if (historyIndex !== -1) {
                  setHistoryIndex(-1);
                  setSavedMessage('');
                }
              }}
              onInput={handleInput}
              placeholder="what do you want to know? "
              rows={1}
              disabled={isLoading}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmit(e);
                } else if (e.key === 'ArrowUp' && !e.shiftKey) {
                  e.preventDefault();
                  handleHistoryNavigation('up');
                } else if (e.key === 'ArrowDown' && !e.shiftKey) {
                  e.preventDefault();
                  handleHistoryNavigation('down');
                }
              }}
              className={`w-full pe-12 resize-none overflow-hidden rounded-[40px] min-h-[48px] sm:min-h-[55px]
                border border-gray-300 py-3 text-sm leading-5 sm:leading-6 shadow-sm
                placeholder:text-base sm:placeholder:text-lg placeholder:text-gray-500
                focus:border-blue-500 focus:border-[1x]
                focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500
                ${(!useWebSearch && !useDocumentSearch && !useDeepSearch || useDocumentSearch) ? 'ps-14 pl-12' : 'ps-3 pl-3'}`}
            />

            {/* Send button inside the input */}
            {isLoading ? (
              /* Stop Processing Button */
              <button
                type="button"
                onClick={stopProcessing}
                className="absolute right-2 top-3 w-9 h-9 rounded-full bg-gradient-to-r from-slate-400 to-slate-500 hover:from-slate-500 hover:to-slate-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95"
                title="Stop Processing"
              >
                {/* Animated background pulse */}
                <div className="absolute inset-0 bg-white opacity-15 rounded-full animate-pulse"></div>

                {/* Stop icon with animation */}
                <div className="relative z-10 flex items-center justify-center">
                  <svg
                    className="h-5 w-5 transform group-hover:rotate-45 transition-transform duration-300"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M6 6h12v12H6z" />
                  </svg>
                </div>

                {/* Ripple effect on hover */}
                <div className="absolute inset-0 rounded-full bg-white opacity-0 group-hover:opacity-15 group-active:opacity-25 transition-opacity duration-200"></div>

                {/* Processing indicator ring */}
                <div className="absolute inset-0 rounded-full border-2 border-white/25 border-t-white/50 animate-spin"></div>

                {/* Subtle glow effect */}
                <div className="absolute -inset-1 bg-gradient-to-r from-slate-400/20 to-slate-500/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
              </button>
            ) : (
              /* Send Message Button */
              <button
                type="submit"
                disabled={!message.trim()}
                className={`absolute right-2 top-2 w-9 h-9 rounded-full shadow-lg transition-all duration-300 transform hover:scale-105 active:scale-95 ${
                  !message.trim()
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gradient-to-r from-blue-500 to-blue-900 hover:from-blue-900 hover:to-blue-700 text-white hover:shadow-xl'
                }`}
                title={!message.trim() ? "Enter a message to send" : "Send message"}
              >
                {/* Animated background gradient */}
                {message.trim() && (
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full"></div>
                )}

                {/* Send icon with animation */}
                <div className="relative z-10 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    className={`h-5 w-5 transition-transform duration-300 ${
                      message.trim() ? 'group-hover:translate-x-0.5 group-hover:-translate-y-0.5' : ''
                    }`}
                  >
                    <path d="M3.478 2.405a.75.75 0 00-.926.94l2.432 7.905H13.5a.75.75 0 010 1.5H4.984l-2.432 7.905a.75.75 0 00.926.94 60.519 60.519 0 0018.445-8.986.75.75 0 000-1.218A60.517 60.517 0 003.478 2.405z" />
                  </svg>
                </div>

                {/* Shine effect on hover */}
                {message.trim() && (
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-full">
                    <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                  </div>
                )}

                {/* Ripple effect on click */}
                <div className="absolute inset-0 rounded-full bg-white opacity-0 group-active:opacity-20 transition-opacity duration-150"></div>
              </button>
            )}
          </div>
        </div>
      </form>

    </div>
  );
}
