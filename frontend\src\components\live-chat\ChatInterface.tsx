'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useChat } from '@/contexts/Livechatcontext';
import MessageActions from './MessageActions';
import EmojiPicker from './EmojiPicker';
import AttachmentButton from './AttachmentButton';
import AttachmentPreview from './AttachmentPreview';
import AttachmentModal from './AttachmentModal';
import MediaLinksDrawer from './MediaLinksDrawer'; // NEW
import GroupCreationModal from './GroupCreationModal';
import AddMembersModal from './AddMembersModal';
import { Menu } from '@headlessui/react';
import { API_BASE_URL } from '@/lib/api';
// @ts-ignore – heroicons has no TS types bundled, safe to ignore
import { DotsVerticalIcon, StarIcon, TrashIcon, ArchiveIcon, SearchIcon, PhoneIcon, CollectionIcon } from '@heroicons/react/solid';
import ViewMembersModal from './ViewMembersModal'; // NEW
import SharedResponseDisplay from '../ai-chat/SharedResponseDisplay';

interface ChatInterfaceProps {
  initialConversationId?: number | null;
}

// Helper to prettify filenames (remove UUID_ prefix and decode URI)
const prettifyFilename = (input:string)=>{
  try{
    const decoded = decodeURIComponent(input);
    return decoded.replace(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}_/i,'');
  }catch{return input;}
};

// Chat Interface Component
export default function ChatInterface({ initialConversationId = null }: ChatInterfaceProps) {
  const {
    state,
    dispatch,
    sendMessage,
    editMessage,
    deleteMessage,
    forwardMessage,
    startTyping,
    stopTyping,
    markConversationAsRead,
    createConversation,
    connectMQTT,
    resetConnection,
    sendGroupMessage,
    editGroupMessage,
    deleteGroupMessage,
    hideGroupMessage,
    leaveGroup,
    hideMessage
  } = useChat();

  const [showUserList, setShowUserList] = useState(false);
  const [messageInput, setMessageInput] = useState('');
  const [searchUser, setSearchUser] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const initialLoadDoneRef = useRef<boolean>(false);
  const [starredIds, setStarredIds] = useState<number[]>(() => {
    if (typeof window === 'undefined') return [];
    const data = localStorage.getItem('starred_conversations');
    return data ? JSON.parse(data) : [];
  });
  const [archivedIds, setArchivedIds] = useState<number[]>(() => {
    if (typeof window === 'undefined') return [];
    const data = localStorage.getItem('archived_conversations');
    return data ? JSON.parse(data) : [];
  });
  const [showArchived, setShowArchived] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showGallery, setShowGallery] = useState(false); // NEW
  const [attachmentModal, setAttachmentModal] = useState<{url:string,type:string}|null>(null);
  const [showGroupModal, setShowGroupModal] = useState(false);
  const [openGroupId, setOpenGroupId] = useState<number | null>(null);
  const [showAddMembers, setShowAddMembers] = useState(false);
  const [showMembersModal, setShowMembersModal] = useState(false);
  const [archivedGroupIds, setArchivedGroupIds] = useState<number[]>(() => {
    if (typeof window === 'undefined') return [];
    const data = localStorage.getItem('archived_groups');
    return data ? JSON.parse(data) : [];
  });

  // Auto-scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [state.activeConversation?.messages]);

  // Load initial data
  useEffect(() => {
    const initializeChat = async () => {
      /////console.log('Chat: Initializing chat interface...');
      
      // Set current user
      const userData = localStorage.getItem('user');
      if (userData) {
        try {
          const user = JSON.parse(userData);
          dispatch({ type: 'SET_CURRENT_USER', payload: user });
         
        } catch (error) {
          console.error('Error parsing user data:', error);
          return;
        }
      }

      // Load conversations and users
      try {
        await Promise.all([
          loadConversations(),
          loadUsers(),
          loadUnreadCount()
        ]);
        ////console.log('Chat: Initial data loaded successfully');
        initialLoadDoneRef.current = true;
        
        // Load initial conversation if provided
        if (initialConversationId) {
          loadConversationMessages(initialConversationId);
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      }
    };

    // Debounce initialization to avoid Hot Refresh conflicts
    const initTimeout = setTimeout(() => {
      initializeChat();
    }, 300);

    return () => {
      clearTimeout(initTimeout);
    };
  }, []);

  // DISABLED: WebSocket connection effect - Feature under maintenance
  // useEffect(() => {
  //   if (state.currentUser && !state.isConnected && !state.isConnecting) {
  //     // Connect with a small delay to ensure everything is ready
  //     const connectTimeout = setTimeout(() => {
  //       if (!state.isConnected && !state.isConnecting) {
  //         //console.log('Chat: Initiating WebSocket connection...');
  //         connectWebSocket();
  //       }
  //     }, 1000); // Increased delay to ensure stability
  //
  //     return () => {
  //       clearTimeout(connectTimeout);
  //     };
  //   }
  // }, [state.currentUser, state.isConnected, state.isConnecting]); // Watch connection state too

  // Load conversations
  const loadConversations = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/conversations`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const conversations = await response.json();
        dispatch({ type: 'SET_CONVERSATIONS', payload: conversations });
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
    }
  };

  // Load all users
  const loadUsers = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/users/all`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const users = await response.json();
        dispatch({ type: 'SET_ALL_USERS', payload: users });
      }
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  // Load unread count
  const loadUnreadCount = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/unread-messages`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        dispatch({ type: 'SET_UNREAD_COUNT', payload: data.total_unread });
      }
    } catch (error) {
      console.error('Error loading unread count:', error);
    }
  };

  // Load conversation messages
  const loadConversationMessages = async (conversationId: number) => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/conversations/${conversationId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const conversation = await response.json();
        dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: conversation });
        
        // Mark as read
        markConversationAsRead(conversationId);
      }
    } catch (error) {
      console.error('Error loading conversation messages:', error);
    }
  };

  // Handle conversation selection
  const handleConversationSelect = (conversation: any) => {
    dispatch({ type: 'SET_ACTIVE_GROUP', payload: null });
    loadConversationMessages(conversation.id);
  };

  // Handle user selection to start new chat
  const handleUserSelect = async (user: any) => {
    const conversation = await createConversation(user.user_id);
    if (conversation) {
      loadConversationMessages(conversation.id);
    }
    setShowUserList(false);
  };

  // Handle emoji select
  const handleEmojiSelect = (emoji: string) => {
    setMessageInput(prev => prev + emoji);
  };

  // Handle forward
  const handleForward = async (messageId: number) => {
    // Show conversation selector modal
    setShowUserList(true);
    // Store the message ID to be forwarded
    const messageToForward = state.activeConversation?.messages?.find(m => m.id === messageId);
    if (messageToForward) {
      localStorage.setItem('messageToForward', JSON.stringify(messageToForward));
    }
  };

  // Handle user selection for forwarding
  const handleUserSelectForForward = async (user: any) => {
    const messageToForward = localStorage.getItem('messageToForward');
    if (messageToForward) {
      const message = JSON.parse(messageToForward);
      const conversation = await createConversation(user.user_id);
      if (conversation) {
        forwardMessage(message.id, conversation.id);
        localStorage.removeItem('messageToForward');
      }
    }
    setShowUserList(false);
  };

  // Handle message sending
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!messageInput.trim()) {
      return;
    }
    
    if (state.activeGroup) {
      sendGroupMessage(state.activeGroup.id, messageInput.trim());
      setMessageInput('');
      return;
    } else if (state.activeConversation) {
      if (!state.activeConversation) {
        dispatch({ type: 'SET_ERROR', payload: 'Please select a conversation first' });
        return;
      }

      if (!state.isConnected) {
        dispatch({ type: 'SET_ERROR', payload: 'Not connected to chat server. Please try reconnecting.' });
        return;
      }

      try {

      // Determine message type
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const imageRegex = /(https?:\/\/.*\.(?:png|jpg|jpeg|gif|webp))/i;
      let msgType: string = 'text';
      if (imageRegex.test(messageInput.trim())) {
        msgType = 'image';
      } else if (urlRegex.test(messageInput.trim())) {
        msgType = 'file';
      }

      sendMessage(state.activeConversation.id, messageInput.trim(), msgType);
      setMessageInput('');
        
        // Stop typing indicator
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }
        stopTyping(state.activeConversation.id);
      } catch (error) {
        console.error('Error sending message:', error);
        dispatch({ type: 'SET_ERROR', payload: 'Failed to send message. Please try again.' });
      }
    }
  };

  // Handle typing
  const handleTyping = (value: string) => {
    setMessageInput(value);
    
    if (state.activeConversation && value.trim()) {
      startTyping(state.activeConversation.id);
      
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Set new timeout to stop typing
      typingTimeoutRef.current = setTimeout(() => {
        if (state.activeConversation) {
          stopTyping(state.activeConversation.id);
        }
      }, 3000);
    }
  };

  // Get other participant in conversation
  const getOtherParticipant = (conversation: any) => {
    if (!state.currentUser) return null;
    
    return conversation.participant1_id === state.currentUser.id 
      ? {
          id: conversation.participant2_id,
          username: conversation.participant2_username,
          department: conversation.participant2_department
        }
      : {
          id: conversation.participant1_id,
          username: conversation.participant1_username,
          department: conversation.participant1_department
        };
  };

  const isUserOnline = (userId: number) => {
    if (state.onlineUsers.some(u => u.user_id === userId)) return true;
    const status = state.allUsers.find(u => u.user_id === userId);
    return Boolean(status?.is_online);
  };

  // Get typing users for current conversation
  const getTypingUsers = () => {
    if (!state.activeConversation) return [];
    const activeId = state.activeConversation.id;
    return state.typingUsers.filter(t => t.conversation_id === activeId);
  };

  // Filter users for search
  const filteredUsers = state.allUsers.filter(user =>
    user.username.toLowerCase().includes(searchUser.toLowerCase()) ||
    user.department.toLowerCase().includes(searchUser.toLowerCase())
  );

  const persistStarred = (ids: number[]) => {
    setStarredIds(ids);
    localStorage.setItem('starred_conversations', JSON.stringify(ids));
  };

  const persistArchived = (ids: number[]) => {
    setArchivedIds(ids);
    localStorage.setItem('archived_conversations', JSON.stringify(ids));
  };

  const toggleStar = (id: number) => {
    const updated = starredIds.includes(id)
      ? starredIds.filter(i => i !== id)
      : [...starredIds, id];
    persistStarred(updated);
  };

  const archiveConversation = (id: number) => {
    if (!archivedIds.includes(id)) {
      persistArchived([...archivedIds, id]);
    }
    if (state.activeConversation?.id === id) {
      dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: null });
    }
  };

  const unarchiveConversation = (id: number) => {
    const updated = archivedIds.filter(i => i !== id);
    persistArchived(updated);
  };

  const deleteConversation = async (id: number) => {
    const token = localStorage.getItem('access_token');

    // First optimistically update UI
    const filtered = state.conversations.filter(c => c.id !== id);
    dispatch({ type: 'SET_CONVERSATIONS', payload: filtered });
    if (state.activeConversation?.id === id) {
      dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: null });
    }

    try {
      const resp = await fetch(`${API_BASE_URL}/chat/conversations/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (!resp.ok) {
        // Revert UI on failure
        dispatch({ type: 'SET_CONVERSATIONS', payload: state.conversations });
        alert('Failed to delete conversation.');
      }
    } catch (err) {
      console.error('deleteConversation', err);
      // Revert UI on error
      dispatch({ type: 'SET_CONVERSATIONS', payload: state.conversations });
      alert('Network error while deleting conversation.');
    }
  };

  const clearChatHistory = async () => {
    const token = localStorage.getItem('access_token');

    // Clear for group chat
    if (state.activeGroup) {
      if (confirm('Clear entire group chat history?')) {
        try {
          await fetch(`${API_BASE_URL}/chat/groups/${state.activeGroup.id}/clear-history`, {
            method: 'DELETE',
            headers: { 'Authorization': `Bearer ${token}` }
          });
        } catch (err) {
          console.error('Failed to clear group history', err);
        }

        const cleared = { ...state.activeGroup, messages: [], last_message: undefined } as any;
        dispatch({ type: 'UPDATE_GROUP', payload: cleared });
        dispatch({ type: 'SET_ACTIVE_GROUP', payload: cleared });
      }
      return;
    }

    // Clear for 1-to-1 conversation
    if (state.activeConversation) {
      if (confirm('Clear entire chat history?')) {
        try {
          await fetch(`${API_BASE_URL}/chat/conversations/${state.activeConversation.id}/clear-history`, {
            method: 'DELETE',
            headers: { 'Authorization': `Bearer ${token}` }
          });
        } catch (err) {
          console.error('Failed to clear chat history', err);
        }

        dispatch({
          type: 'UPDATE_CONVERSATION',
          payload: { ...state.activeConversation, messages: [], last_message: undefined }
        });
      }
    }
  };

  const getLastSeen = (userId: number) => {
    const status = state.allUsers.find(u => u.user_id === userId);
    if (status?.is_online) return 'Online';
    if (status?.last_seen) {
      const date = new Date(status.last_seen);
      return `Last seen ${formatISTTime(date)}`;
    }
    return 'Offline';
  };

  const requestTeamViewer = () => {
    if (state.activeGroup) {
      sendGroupMessage(state.activeGroup.id, 'Requested a TeamViewer session', 'teamviewer_request');
    } else if (state.activeConversation) {
      sendMessage(state.activeConversation.id, 'Requested a TeamViewer session', 'teamviewer_request');
    }
  };

  // Auto-dismiss errors after 5 seconds
  useEffect(() => {
    if (state.error) {
      const errorTimeout = setTimeout(() => {
        dispatch({ type: 'SET_ERROR', payload: null });
      }, 5000);
      
      return () => clearTimeout(errorTimeout);
    }
  }, [state.error]);

  // Helper to format a date/time string in Indian Standard Time (IST)
  const formatISTTime = (dateInput: string | number | Date) => {
    let date: Date;

    if (typeof dateInput === 'string') {
      // Backend sends naive UTC timestamps (e.g., "2024-07-11T12:00:00.000000").
      // 1. Strip microseconds to ensure valid JS Date parsing.
      // 2. Append a 'Z' timezone designator when none is present to explicitly treat it as UTC.
      let isoString = dateInput;

      // Remove fractional seconds beyond milliseconds for compatibility (e.g., ".000000" → "").
      if (isoString.includes('.')) {
        isoString = isoString.split('.')[0];
      }

      // If the string lacks an explicit timezone offset, assume UTC.
      if (!/[Zz]|[+-]\d{2}:?\d{2}$/.test(isoString)) {
        isoString += 'Z';
      }

      date = new Date(isoString);
    } else {
      date = new Date(dateInput);
    }

    // Fallback in case of invalid date
    if (isNaN(date.getTime())) {
      return '';
    }

    return date.toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false, // 24-hour format; switch to true for 12-hour
      timeZone: 'Asia/Kolkata',
    });
  };

  // Load groups
  const loadGroups = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/groups/`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (response.ok) {
        const groups = await response.json();
        dispatch({ type: 'SET_GROUPS', payload: groups });
      }
    } catch (error) {
      console.error('Error loading groups:', error);
    }
  };

  // Handle group selection
  const handleGroupSelect = async (group: any) => {

    dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: null });
    await loadGroupMessages(group.id);
  };

  // Load group messages
  const loadGroupMessages = async (groupId: number) => {
   
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/groups/${groupId}/messages`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (response.ok) {
        const msgs = await response.json();
        const target = state.groups.find(g => g.id === groupId);
        if (target) {
          dispatch({ type: 'UPDATE_GROUP', payload: { ...target, messages: msgs } });
          dispatch({ type: 'SET_ACTIVE_GROUP', payload: { ...target, messages: msgs } });
          setOpenGroupId(groupId);
        }
     
      }
     
    } catch (error) {
      console.error('Error loading group messages:', error);
    }
  };

  const renameGroup = async (groupId: number, newName: string) => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${API_BASE_URL}/chat/groups/${groupId}/rename`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: newName }),
      });
      if (response.ok) {
        const updatedGroup = await response.json();
        dispatch({ type: 'UPDATE_GROUP', payload: updatedGroup });
        dispatch({ type: 'SET_ACTIVE_GROUP', payload: updatedGroup });
        setOpenGroupId(groupId);
      } else {
        const errorData = await response.json();
        dispatch({ type: 'SET_ERROR', payload: errorData.detail || 'Failed to rename group' });
      }
    } catch (error) {
      console.error('Error renaming group:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to rename group. Please try again.' });
    }
  };

  // (leaveGroup handled via ChatContext)

  const deleteGroup = async (groupId: number) => {
    if (!confirm('Delete group permanently?')) return;
    try {
      const token = localStorage.getItem('access_token');
      const resp = await fetch(`${API_BASE_URL}/chat/groups/${groupId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (resp.ok || resp.status === 204) {
        dispatch({ type: 'SET_GROUPS', payload: state.groups.filter(g => g.id !== groupId) });
        dispatch({ type: 'SET_ACTIVE_GROUP', payload: null });
      }
    } catch (e) { console.error('deleteGroup', e); }
  };

  const persistArchivedGroups = (ids:number[])=>{
    setArchivedGroupIds(ids);
    localStorage.setItem('archived_groups', JSON.stringify(ids));
  };

  return (
    <div className="flex h-full bg-white rounded-lg shadow-sm overflow-hidden">
      {/* Sidebar */}
      <div className="w-1/3 bg-gray-50 border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Messages</h2>
            <div className="flex space-x-2">
              <button
                onClick={() => setShowUserList(true)}
                className="px-3 py-1.5 bg-blue-900 text-white rounded-full hover:bg-blue-700 text-sm font-medium"
              >
                + Chat
              </button>
              <button
                onClick={() => setShowArchived(!showArchived)}
                title={showArchived ? 'Show chats' : 'Show archived'}
                className="p-2 bg-gray-200 rounded-full hover:bg-gray-300"
              >
                {showArchived ? '←' : 'A'}
              </button>
              <button 
                onClick={() => setShowGroupModal(true)} 
                className="px-3 py-1.5 bg-blue-900 text-white rounded-full hover:bg-blue-700 text-sm font-medium"
              >
                + Group
              </button>
            </div>
          </div>
          
          {/* Connection Status */}
          {/* <div className="flex items-center mt-2 text-sm">
            <div className={`w-2 h-2 rounded-full mr-2 ${state.isConnected ? 'bg-green-500' : state.isConnecting ? 'bg-yellow-500 animate-pulse' : 'bg-red-500'}`}></div>
            <span className={`${state.isConnected ? 'text-green-600' : state.isConnecting ? 'text-yellow-600' : 'text-red-600'}`}>
              {state.isConnected ? 'Connected' : state.isConnecting ? 'Connecting...' : 'Disconnected'}
            </span>
            {state.isConnected && (
              <span className="ml-2 text-xs text-gray-500">
                • Live
              </span>
            )}
            {!state.isConnected && !state.isConnecting && (
              <div className="ml-2 flex space-x-1">
                <button
                  onClick={() => alert('Live chat is temporarily under maintenance. Please check back later.')}
                  className="text-xs px-2 py-1 bg-gray-400 text-white rounded cursor-not-allowed"
                  disabled
                >
                  Maintenance
                </button>
                <button
                  onClick={resetConnection}
                  className="text-xs px-2 py-1 bg-gray-600 text-white rounded hover:bg-gray-700"
                  title="Reset connection completely"
                >
                  Reset
                </button>
              </div>
            )}
          
          </div> */}
        </div>

        {/* Conversations List */}
        <div className="flex-1 overflow-y-auto">
          {state.conversations.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <p>No conversations yet.</p>
              <p className="text-sm mt-1">Click + to start a new chat!</p>
            </div>
          ) : (
            state.conversations
              .filter(c =>
                showArchived ? archivedIds.includes(c.id) : !archivedIds.includes(c.id)
              )
              .map((conversation) => {
              const otherParticipant = getOtherParticipant(conversation);
              const isOnline = otherParticipant ? isUserOnline(otherParticipant.id) : false;
              const isActive = state.activeConversation?.id === conversation.id;
              
              return (
                <div
                  key={conversation.id}
                  onClick={() => handleConversationSelect(conversation)}
                  className={`p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors ${
                    isActive ? 'bg-blue-50 border-blue-200' : ''
                  } relative`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
                        {otherParticipant?.username[0]?.toUpperCase()}
                      </div>
                      {isOnline && (
                        <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {otherParticipant?.username}
                        </p>
                        <div className="flex items-center space-x-1">
                          {conversation.last_message && (
                            <span className="text-xs text-gray-500">
                              {formatISTTime(conversation.last_message.created_at)}
                            </span>
                          )}
                          <Menu as="div" className="relative inline-block text-left">
                            <Menu.Button onClick={(e)=>e.stopPropagation()} className="p-1 rounded-full hover:bg-gray-200 focus:outline-none">
                              <DotsVerticalIcon className="w-4 h-4 text-gray-600" />
                            </Menu.Button>
                            <Menu.Items className="origin-top-right absolute right-0 mt-2 w-36 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                              <div className="py-1">
                                <Menu.Item>
                                  {({ active }) => (
                                    <button
                                      onClick={() => toggleStar(conversation.id)}
                                      className={`${active ? 'bg-gray-100' : ''} w-full flex items-center px-4 py-2 text-sm text-gray-700`}
                                    >
                                      <StarIcon className="w-4 h-4 mr-2 text-yellow-400" />
                                      {starredIds.includes(conversation.id) ? 'Unstar' : 'Star'}
                                    </button>
                                  )}
                                </Menu.Item>
                                {showArchived ? (
                                  <Menu.Item>
                                    {({ active }) => (
                                      <button
                                        onClick={() => unarchiveConversation(conversation.id)}
                                        className={`${active ? 'bg-gray-100' : ''} w-full flex items-center px-4 py-2 text-sm text-gray-700`}
                                      >
                                        <ArchiveIcon className="w-4 h-4 mr-2 text-gray-500" />
                                        Unarchive
                                      </button>
                                    )}
                                  </Menu.Item>
                                ) : (
                                  <Menu.Item>
                                    {({ active }) => (
                                      <button
                                        onClick={() => archiveConversation(conversation.id)}
                                        className={`${active ? 'bg-gray-100' : ''} w-full flex items-center px-4 py-2 text-sm text-gray-700`}
                                      >
                                        <ArchiveIcon className="w-4 h-4 mr-2 text-gray-500" />
                                        Archive
                                      </button>
                                    )}
                                  </Menu.Item>
                                )}
                                <Menu.Item>
                                  {({ active }) => (
                                    <button
                                      onClick={() => deleteConversation(conversation.id)}
                                      className={`${active ? 'bg-gray-100' : ''} w-full flex items-center px-4 py-2 text-sm text-red-600`}
                                    >
                                      <TrashIcon className="w-4 h-4 mr-2" />
                                      Delete
                                    </button>
                                  )}
                                </Menu.Item>
                              </div>
                            </Menu.Items>
                          </Menu>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500">{otherParticipant?.department}</p>
                      <p className="text-xs text-gray-400">{otherParticipant && getLastSeen(otherParticipant.id)}</p>
                      {conversation.last_message && (
                        <p className="text-sm text-gray-600 truncate mt-1">
                          {conversation.last_message.message_type==='text'
                            ? conversation.last_message.content
                            : (()=>{
                                try{
                                  const m=JSON.parse(conversation.last_message.content);
                                  return m.filename||m.name||m.url;
                                }catch{
                                  const base=conversation.last_message.content.split('?')[0].split('#')[0].split('/').pop();
                                  return prettifyFilename(base||'');
                                }
                              })()}
                        </p>
                      )}
                    </div>
                  </div>
                  {starredIds.includes(conversation.id) && (
                    <StarIcon className="w-4 h-4 text-yellow-400 absolute bottom-2 right-2" />
                  )}
                </div>
              );
            })
          )}
          {/* Groups Section */}
          {state.groups.filter(g=>{
                return showArchived ? archivedGroupIds.includes(g.id) : !archivedGroupIds.includes(g.id);
              }).map(group => {
            const isActive = state.activeGroup?.id === group.id;
            return (
              <div
                key={`grp-${group.id}`}
                onClick={() => handleGroupSelect(group)}
                className={`p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors ${isActive ? 'bg-purple-50 border-purple-200' : ''}`}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white font-medium">
                    {group.name[0]?.toUpperCase()}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 truncate">{group.name}</p>
                      {group.last_message && (
                        <span className="text-xs text-gray-500">{formatISTTime(group.last_message.created_at)}</span>
                      )}
                    </div>
                    {group.last_message && (
                      <p className="text-sm text-gray-600 truncate mt-1">{group.last_message.content}</p>
                    )}
                  </div>
                  <Menu as="div" className="relative inline-block text-left">
                    <Menu.Button onClick={(e)=>e.stopPropagation()} className="p-1 rounded-full hover:bg-gray-200 focus:outline-none">
                      <DotsVerticalIcon className="w-4 h-4 text-gray-600" />
                    </Menu.Button>
                    <Menu.Items className="origin-top-right absolute right-0 mt-2 w-36 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                      <div className="py-1">
                        {archivedGroupIds.includes(group.id)?(
                          <Menu.Item>{({active})=>(
                            <button onClick={(e)=>{e.stopPropagation();persistArchivedGroups(archivedGroupIds.filter(i=>i!==group.id));}} className={`${active?'bg-gray-100':''} w-full px-4 py-2 text-sm text-gray-700`}>Unarchive</button>
                          )}</Menu.Item>
                        ):(
                          <Menu.Item>{({active})=>(
                            <button onClick={(e)=>{e.stopPropagation();persistArchivedGroups([...archivedGroupIds, group.id]);}} className={`${active?'bg-gray-100':''} w-full px-4 py-2 text-sm text-gray-700`}>Archive</button>
                          )}</Menu.Item>
                        )}
                        <Menu.Item>{({active}) => (
                          <button onClick={(e)=>{e.stopPropagation();leaveGroup(group.id);}} className={`${active?'bg-gray-100':''} w-full px-4 py-2 text-sm text-red-600`}>Leave group</button>
                        )}</Menu.Item>
                      </div>
                    </Menu.Items>
                  </Menu>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {state.activeConversation || state.activeGroup ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 bg-white">
              <div className="flex items-center space-x-3">
                {state.activeGroup ? (
                  <>
                    <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white font-medium">
                      {state.activeGroup.name[0]?.toUpperCase()}
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{state.activeGroup.name}</h3>
                      <p className="text-sm text-gray-500">Group chat • {state.activeGroup.members?.length} members</p>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="relative">
                      <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
                        {getOtherParticipant(state.activeConversation)?.username[0]?.toUpperCase()}
                      </div>
                      {getOtherParticipant(state.activeConversation) && 
                       isUserOnline(getOtherParticipant(state.activeConversation)!.id) && (
                        <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        {getOtherParticipant(state.activeConversation)?.username}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {getOtherParticipant(state.activeConversation)?.department}
                      </p>
                      <p className="text-xs text-gray-400">
                        {getOtherParticipant(state.activeConversation) && getLastSeen(getOtherParticipant(state.activeConversation)!.id)}
                      </p>
                    </div>
                  </>
                )}
                {/* header menu */}
                <div className="ml-auto">
                  <Menu as="div" className="relative inline-block text-left">
                    <Menu.Button className="p-1 rounded-full hover:bg-gray-200 focus:outline-none">
                      <DotsVerticalIcon className="w-5 h-5 text-gray-600" />
                    </Menu.Button>
                    <Menu.Items className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                      <div className="py-1">
                        <Menu.Item>{({active}) => (
                          <button onClick={()=>setShowSearch(true)} className={`${active?'bg-gray-100':''} w-full flex items-center px-4 py-2 text-sm text-gray-700`}>
                            <SearchIcon className="w-4 h-4 mr-2"/> Search
                          </button>
                        )}</Menu.Item>
                        <Menu.Item>{({active}) => (
                          <button onClick={clearChatHistory} className={`${active?'bg-gray-100':''} w-full flex items-center px-4 py-2 text-sm text-gray-700`}>
                            <TrashIcon className="w-4 h-4 mr-2"/> Clear history
                          </button>
                        )}</Menu.Item>
                        <Menu.Item>{({active}) => (
                          <button onClick={requestTeamViewer} className={`${active?'bg-gray-100':''} w-full flex items-center px-4 py-2 text-sm text-gray-700`}>
                            <PhoneIcon className="w-4 h-4 mr-2"/> Request TeamViewer
                          </button>
                        )}</Menu.Item>
                        {state.activeGroup && (
                          <Menu.Item>{({active}) => (
                            <button onClick={() => setShowMembersModal(true)} className={`${active?'bg-gray-100':''} w-full flex items-center px-4 py-2 text-sm text-gray-700`}>View members</button>
                          )}</Menu.Item>
                        )}
                        {state.activeGroup && state.currentUser && state.activeGroup.creator_id===state.currentUser.id && (
                          <Menu.Item>{({active}) => (
                            <button onClick={() => setShowAddMembers(true)} className={`${active?'bg-gray-100':''} w-full flex items-center px-4 py-2 text-sm text-gray-700`}>Add members</button>
                          )}</Menu.Item>
                        )}
                        {state.activeGroup && state.currentUser && state.activeGroup.creator_id===state.currentUser.id && (
                          <Menu.Item>{({active}) => (
                            <button onClick={() => deleteGroup(state.activeGroup!.id)} className={`${active?'bg-gray-100':''} w-full flex items-center px-4 py-2 text-sm text-red-600`}>Delete group</button>
                          )}</Menu.Item>
                        )}
                      </div>
                    </Menu.Items>
                  </Menu>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {(state.activeGroup ? state.activeGroup.messages : state.activeConversation?.messages)?.map((message: any) => {
                const isOwn = message.sender_id === state.currentUser?.id;
                
                // Dedicated rendering for system messages (centered, grey text)
                if (message.message_type === 'system') {
                  return (
                    <div key={message.id} id={`msg-${message.id}`} className="flex justify-center">
                      <p className="text-xs text-gray-500 py-1">{message.content}</p>
                    </div>
                  );
                }

                // Dedicated rendering for shared AI responses
                if (message.message_type === 'shared_ai_response') {
                  try {
                    const sharedData = JSON.parse(message.content);
                    return (
                      <div key={message.id} id={`msg-${message.id}`} className="flex justify-start">
                        <div className="w-full max-w-4xl">
                          <SharedResponseDisplay
                            sharedResponse={sharedData.shared_response}
                            shareContext={sharedData.share_context}
                            timestamp={sharedData.timestamp}
                            sharedByUsername={message.sender_username}
                            className="mb-2"
                          />
                        </div>
                      </div>
                    );
                  } catch (error) {
                    console.error('Error parsing shared response:', error);
                    return (
                      <div key={message.id} id={`msg-${message.id}`} className="flex justify-center">
                        <p className="text-xs text-gray-500 py-1">Shared AI response (error displaying)</p>
                      </div>
                    );
                  }
                }

                return (
                  <div key={message.id} id={`msg-${message.id}`} className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}>
                    <div className={`relative max-w-xs lg:max-w-md rounded-lg ${ message.message_type==='teamviewer_request' ? 'border border-blue-500 bg-blue-50 text-blue-900 px-4 py-3' : 'px-4 py-2 ' + (isOwn ? 'bg-blue-900 text-white' : 'bg-gray-200 text-gray-900')}`}>
                      {message.message_type==='deleted' ? (
                        <i className="text-sm text-gray-500">Message deleted</i>
                      ) : message.message_type==='teamviewer_request' ? (
                        <div className="flex items-center space-x-2">
                          <PhoneIcon className="w-5 h-5"/>
                          <span className="text-sm font-medium">TeamViewer session requested</span>
                        </div>
                      ) : (['image','file'].includes(message.message_type)) ? (
                        <AttachmentPreview
                          url={message.content}
                          type={message.message_type}
                          onClick={()=>setAttachmentModal({url:message.content,type:message.message_type})}
                        />
                      ) : (
                        <p className="text-sm break-all">
                          {(() => {
                            if (message.message_type==='text') return message.content;
                            try { const m=JSON.parse(message.content); return m.name || m.url; }
                            catch {
                              const base=message.content.split('?')[0].split('#')[0].split('/').pop();
                              return prettifyFilename(base||'');
                            }
                          })()}
                          {message.is_edited && <span className="text-xs opacity-70"> (edited)</span>}
                        </p>
                      )}
                      <MessageActions
                        message={message}
                        isOwn={isOwn}
                        onEdit={(id,newC)=>{
                          const gid = state.activeGroup?.id ?? (message as any).group_id;
                          if (gid) { editGroupMessage(id, gid, newC); } else { editMessage(id, newC); }
                        }}
                        onDeleteForEveryone={(id)=>{
                          const gid = state.activeGroup?.id ?? (message as any).group_id;
                          if (gid) { deleteGroupMessage(id, gid); } else { deleteMessage(id); }
                        }}
                        onDeleteForMe={(id)=>{
                          const gid = state.activeGroup?.id ?? (message as any).group_id;
                          if (gid) { hideGroupMessage(id, gid); } else { hideMessage(id); }
                        }}
                        onForward={handleForward}
                      />
                      <div className={`mt-2 text-right text-xs ${isOwn ? 'text-blue-100' : 'text-gray-500'}`}> 
                        <span>
                          {formatISTTime(message.created_at)}
                        </span>
                        {isOwn && (
                          <span className="ml-1">
                            {message.is_read ? '✓✓' : '✓'}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                );
                })}
              
              {/* Typing Indicator */}
              {getTypingUsers().length > 0 && (
                <div className="flex justify-start">
                  <div className="bg-gray-200 text-gray-900 px-4 py-2 rounded-lg">
                    <div className="flex items-center space-x-1">
                      <span className="text-sm">
                        {getTypingUsers()[0].username} is typing
                      </span>
                      <div className="flex space-x-1">
                        <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce"></div>
                        <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200 bg-white">
              <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
                <AttachmentButton conversationId={state.activeConversation?.id ?? null} groupId={state.activeGroup?.id ?? null} isGroup={!!state.activeGroup} />
                <EmojiPicker onEmojiSelect={handleEmojiSelect} buttonClassName="p-2 text-gray-600 hover:text-gray-800" />
                <input
                  type="text"
                  value={messageInput}
                  onChange={(e) => handleTyping(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!state.isConnected}
                />
                <button
                  type="submit"
                  disabled={!messageInput.trim() || !state.isConnected}
                  className="px-6 py-2 bg-blue-900 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Send
                </button>
              </form>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a conversation</h3>
              <p className="text-gray-500">Choose a conversation from the sidebar or start a new one</p>
            </div>
          </div>
        )}
      </div>

      {/* User List Modal */}
      {showUserList && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Start New Chat</h3>
              <button
                onClick={() => setShowUserList(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Search */}
            <div className="mb-4">
              <input
                type="text"
                placeholder="Search users..."
                value={searchUser}
                onChange={(e) => setSearchUser(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Users List */}
            <div className="space-y-2">
              {filteredUsers.map((user) => (
                <div
                  key={user.user_id}
                  onClick={() => handleUserSelect(user)}
                  className="flex items-center space-x-3 p-3 hover:bg-gray-100 rounded-md cursor-pointer"
                >
                  <div className="relative">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {user.username[0]?.toUpperCase()}
                    </div>
                    {user.is_online && (
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{user.username}</p>
                    <p className="text-xs text-gray-500">{user.department}</p>
                  </div>
                  {user.is_online && (
                    <span className="text-xs text-green-600 font-medium">Online</span>
                  )}
                </div>
              ))}
              
              {filteredUsers.length === 0 && (
                <p className="text-center text-gray-500 py-4">No users found</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Error Toast */}
      {state.error && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className={`px-6 py-3 rounded-lg shadow-lg flex items-center space-x-3 ${
            state.error.includes('connect') || state.error.includes('server') 
              ? 'bg-red-500 text-white' 
              : 'bg-orange-500 text-white'
          }`}>
            <div className="flex-shrink-0">
              {state.error.includes('connect') || state.error.includes('server') ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
            </div>
            <span className="flex-1">{state.error}</span>
            <button
              onClick={() => dispatch({ type: 'SET_ERROR', payload: null })}
              className="flex-shrink-0 ml-4 text-white hover:text-gray-200"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Search Modal */}
      {showSearch && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg shadow-lg w-full max-w-md">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-medium">Search messages</h3>
              <button onClick={()=>{setShowSearch(false); setSearchQuery('');}} className="text-gray-500">✕</button>
            </div>
            <input
              type="text"
              placeholder="Type to search..."
              value={searchQuery}
              onChange={(e)=>setSearchQuery(e.target.value)}
              className="w-full border px-3 py-2 rounded-md mb-4"
            />
            <div className="max-h-60 overflow-y-auto space-y-2">
              {(state.activeGroup?.messages ?? state.activeConversation?.messages ?? [])
                .filter(m=>m.content && m.content.toLowerCase().includes(searchQuery.toLowerCase()))
                .map(m=> (
                <div key={m.id} className="p-2 bg-gray-100 rounded cursor-pointer" onClick={()=>{
                  // scroll to message
                  const el=document.getElementById('msg-'+m.id);
                  el?.scrollIntoView({behavior:'smooth',block:'center'});
                  setShowSearch(false);
                }}>
                  {m.content}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Drawer for media & links */}
      {state.activeConversation && (
        <MediaLinksDrawer
          conversationId={state.activeConversation.id}
          open={showGallery}
          onClose={() => setShowGallery(false)}
        />
      )}

      {/* Attachment modal */}
      {attachmentModal && (
        <AttachmentModal
          open={true}
          url={attachmentModal.url}
          type={attachmentModal.type}
          onClose={()=>setAttachmentModal(null)}
        />
      )}

      {/* Group Creation Modal */}
      <GroupCreationModal isOpen={showGroupModal} onClose={() => setShowGroupModal(false)} />

      {state.activeGroup && <AddMembersModal isOpen={showAddMembers} onClose={() => setShowAddMembers(false)} groupId={state.activeGroup.id} />}
      {state.activeGroup && <ViewMembersModal isOpen={showMembersModal} onClose={() => setShowMembersModal(false)} group={state.activeGroup} isAdmin={state.activeGroup.creator_id===state.currentUser?.id} />}
    </div>
  );
} 