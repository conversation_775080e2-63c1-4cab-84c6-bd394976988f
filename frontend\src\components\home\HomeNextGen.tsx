

"use client";

import React, { useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";

type Card = {
  title: string;
  body: string;
  iconBg: string;
};

const cards: Card[] = [
  {
    title: "Upload Documents",
    body: "Securely upload your company policies, playbooks, manuals, and knowledge bases. Our system instantly processes and indexes your content for quick retrieval..",
    iconBg: "bg-gradient-to-br from-pink-200 to-pink-100",
  },
  {
    title: "Just Ask AI Assistant",
    body: "Ask questions in natural language and get instant, accurate answers from your documents. No more searching through files or lengthy manuals.",
    iconBg: "bg-gradient-to-br from-blue-200 to-blue-100",
  },
  {
    title: "Explore Answers",
    body: "Get precise, context-aware responses with source citations. Review summaries, actionable insights, and recommended next steps based on your enterprise knowledge.",
    iconBg: "bg-gradient-to-br from-pink-300 to-pink-200",
  },
];

export default function HomeNextGen() {
  const containerRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end end"],
  });

  // Heading animation - stays longer, then moves up very slowly
  const headingY = useTransform(scrollYProgress, [0, 0.12, 0.3], [0, 0, -200]);
  const headingOpacity = useTransform(scrollYProgress, [0, 0.12, 0.3], [1, 1, 0]);

  // Background animation - slides in from BOTTOM and covers heading extra slowly
  const bgY = useTransform(scrollYProgress, [0.08, 0.28], ["100%", "0%"]);
  const bgOpacity = useTransform(scrollYProgress, [0.08, 0.28], [0, 1]);

  // Exit animation - ultra slow, gentle exit
  const containerY = useTransform(scrollYProgress, [0.92, 1], [0, -100]);

  return (
    <div ref={containerRef} className="relative w-full bg-gradient-to-r from-blue-600 via-purple-500 to-orange-400" style={{ height: "280vh" }}>
      <motion.div
        style={{ y: containerY }}
        className="sticky top-0 h-screen w-full overflow-hidden bg-white"
      >
        {/* Heading Section - Goes behind background */}
        <motion.div
          style={{
            y: headingY,
            opacity: headingOpacity,
          }}
          className="absolute inset-0 z-10 flex items-center justify-center"
        >
          <div className="mx-auto max-w-5xl px-4 sm:px-6 py-10 sm:py-14 md:py-20 lg:py-24 text-center">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-semibold tracking-tight leading-tight py-3 sm:py-5">
              Next-Gen Communication for
              <span className="block bg-gradient-to-tr from-orange-500 to-rose-400 bg-clip-text mt-4 sm:mt-6 text-transparent">
                Smarter Workflows
              </span>
            </h1>
            <p className="mt-4 sm:mt-6 md:mt-8 lg:mt-10 text-sm sm:text-base md:text-lg lg:text-xl text-black/90 font-semibold">
              How does it work?
            </p>
          </div>
        </motion.div>

        {/* Gradient Background - Slides in from BOTTOM and goes in front of heading */}
        <motion.div
          style={{
            y: bgY,
            opacity: bgOpacity,
          }}
          className="absolute inset-0 z-20"
        >
          <div className="w-full h-full bg-gradient-to-r from-blue-600 via-purple-500 to-orange-400" />
        </motion.div>

        {/* Cards Section - Appear from right and stack */}
        <div className="absolute inset-0 z-30 flex items-center justify-center px-2 sm:px-4">
          <div className="relative w-full max-w-7xl h-[500px] sm:h-[600px] md:h-[650px]">
            {cards.map((card, index) => (
              <ScrollCard
                key={card.title}
                card={card}
                index={index}
                scrollYProgress={scrollYProgress}
              />
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );
}

function ScrollCard({
  card,
  index,
  scrollYProgress,
}: {
  card: Card;
  index: number;
  scrollYProgress: any;
}) {
  // Stagger timing for each card - extra slow animation for maximum readability
  const startProgress = 0.25 + index * 0.18;
  const endProgress = startProgress + 0.45; // Extra long duration for very slow, highly readable card movement

  // Card comes from RIGHT side with gentle ease-out effect (very slow, smooth movement)
  const cardX = useTransform(
    scrollYProgress,
    [startProgress, endProgress],
    [1200, 0],
    {
      ease: (t) => 1 - Math.pow(1 - t, 5) // Quintic ease-out - ultra smooth, very slow deceleration for readability
    }
  );

  // Instant opacity transition - cards are fully visible immediately when they appear
  const cardOpacity = useTransform(
    scrollYProgress,
    [startProgress, startProgress + 0.05], // Very short duration - almost instant full opacity
    [0, 1]
  );

  // Adjust rotation based on card index - back cards should be less tilted
  const rotationAngles = [
    [15, 0],  // Upload Documents - minimal tilt when settled
    [15, 0],   // Just Ask AI Assistant - completely straight when settled
    [15, 0],   // Explore Answers - slight forward tilt
  ];

  const cardRotate = useTransform(
    scrollYProgress,
    [startProgress, endProgress],
    rotationAngles[index], // Different rotation for each card
    {
      ease: (t) => 1 - Math.pow(1 - t, 3) // Smooth rotation
    }
  );

  // Position offsets for stacking effect - responsive for all devices
  const positions = [
    {
      top: "5%",     // Upload Documents - top position
      left: "50%",   // Centered on all devices
      transform: "translateX(-50%)", // Center alignment
    },
    {
      top: "23%",    // AI Assistant - middle position  
      left: "50%",
      transform: "translateX(-50%)",
    },
    {
      top: "41%",    // Explore Answers 
      left: "50%",
      transform: "translateX(-50%)",
    },
  ];

  // Desktop positions (larger screens)
  const desktopPositions = [
    {
      top: "12%",
      left: "8%",
    },
    {
      top: "29%",
      left: "23%",
    },
    {
      top: "45%",
      left: "38%",
    },
  ];

  return (
    <>
      {/* Mobile/Tablet - Centered cards */}
      <motion.div
        style={{
          x: cardX,
          opacity: cardOpacity,
          rotate: cardRotate,
          top: positions[index].top,
          left: positions[index].left,
          transform: positions[index].transform,
        }}
        className="md:hidden absolute w-[90%] max-w-[350px] sm:max-w-[420px]"
      >
        <div className="bg-white backdrop-blur-md rounded-2xl shadow-2xl p-5 sm:p-6 border border-gray-200 min-h-[300px] sm:min-h-[320px] flex flex-col">
          {/* Card Title */}
          <h2 className="text-2xl sm:text-3xl font-semibold tracking-tight mb-3 text-gray-900">
            {card.title}
          </h2>

          {/* Icon and Description - Pushed to bottom and aligned at bottom */}
          <div className="mt-auto flex items-end gap-4 sm:gap-5">
            {/* Icon */}
            <div
              className={`shrink-0 rounded-xl ${card.iconBg} p-2 flex items-center justify-center shadow-lg`}
            >
              <div className="relative w-16 h-16 sm:w-20 sm:h-20">
                <div className="absolute inset-0 rounded-full bg-gray-500/20" />
                <div className="absolute top-1.5 left-1.5 w-13 h-13 sm:w-16 sm:h-16 rounded-full bg-gray-700/50" />
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gray-800/60" />
              </div>
            </div>

            {/* Description */}
            <p className="text-sm sm:text-base leading-relaxed text-gray-800 font-medium flex-1">
              {card.body}
            </p>
          </div>
        </div>
      </motion.div>

      {/* Desktop - Offset stacked cards */}
      <motion.div
        style={{
          x: cardX,
          opacity: cardOpacity,
          rotate: cardRotate,
          top: desktopPositions[index].top,
          left: desktopPositions[index].left,
        }}
        className="hidden md:block absolute md:w-[600px] lg:w-[720px]"
      >
        <div className="bg-white backdrop-blur-md rounded-2xl shadow-2xl p-10 lg:p-12 border border-gray-200 min-h-[400px] lg:min-h-[440px] flex flex-col">
          {/* Card Title */}
          <h2 className="text-4xl lg:text-5xl font-semibold tracking-tight mb-4 text-gray-900">
            {card.title}
          </h2>

          {/* Icon and Description - Pushed to bottom and aligned at bottom */}
          <div className="mt-auto flex items-end gap-6 lg:gap-8">
            {/* Icon */}
            <div
              className={`shrink-0 rounded-xl ${card.iconBg} p-2 flex items-center justify-center shadow-lg`}
            >
              <div className="relative w-24 h-24 lg:w-28 lg:h-28">
                <div className="absolute inset-0 rounded-full bg-gray-500/20" />
                <div className="absolute top-2 left-2 w-20 h-20 lg:w-24 lg:h-24 rounded-full bg-gray-700/50" />
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-12 lg:w-14 lg:h-14 rounded-full bg-gray-800/60" />
              </div>
            </div>

            {/* Description */}
            <p className="text-base lg:text-lg leading-relaxed text-gray-800 font-medium flex-1">
              {card.body}
            </p>
          </div>
        </div>
      </motion.div>
    </>
  );
}
