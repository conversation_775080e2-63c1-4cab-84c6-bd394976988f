from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    Boolean,
    DateTime,
    ForeignKey,
    JSON,
    Index,
    BigInteger,
)
from sqlalchemy.orm import relationship
from datetime import datetime

from ..database import Base


class Note(Base):
    __tablename__ = "notes"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    interaction_id = Column(BigInteger, ForeignKey('expert_chat_interactions.id', ondelete='SET NULL'), nullable=True, index=True)

    title = Column(String(255), nullable=False)
    combined_text = Column(Text, nullable=True)
    extra_text = Column(Text, nullable=True)
    sources = Column(JSON, nullable=True)
    # agent_type removed per updated requirements
    session_id = Column(String(100), nullable=True)
    # tags removed per requirements
    # pin/archive removed based on updated requirements

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    interaction = relationship("ExpertChatInteraction", foreign_keys=[interaction_id])

    __table_args__ = (
        Index('idx_notes_user_created', 'user_id', 'created_at'),
        Index('idx_notes_interaction', 'interaction_id'),
        Index('idx_notes_created_at', 'created_at'),
    )

    def to_dict(self) -> dict:
        return {
            'id': self.id,
            'user_id': self.user_id,
            'interaction_id': self.interaction_id,
            'title': self.title,
            'combined_text': self.combined_text,
            'extra_text': self.extra_text,
            'sources': self.sources,
            # 'agent_type' removed
            'session_id': self.session_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }


