from sqlalchemy import Column, Integer, String, Text, <PERSON>olean, DateTime, ForeignKey, Float, JSON, Index, BigInteger, SmallInteger, CheckConstraint, UniqueConstraint
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base
import uuid
from sqlalchemy.dialects.postgresql import JSONB

class QueryFeedback(Base):
    __tablename__ = "query_feedback"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    interaction_id = Column(BigInteger, ForeignKey('expert_chat_interactions.id', ondelete='CASCADE'), nullable=False)
    is_helpful = Column(Boolean, nullable=False)
    rating = Column(SmallInteger, nullable=False)
    reasons = Column(JSON, nullable=True)  # Array of strings for predefined reasons
    comment = Column(Text, nullable=True)  # Free-form comment
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="feedback")
    interaction = relationship("ExpertChatInteraction", foreign_keys=[interaction_id])
    
    # Constraints and indexes
    __table_args__ = (
        # Unique constraint: one feedback per user per interaction
        UniqueConstraint('user_id', 'interaction_id', name='uq_feedback_user_interaction'),
        # Check constraint: rating must align with helpfulness
        CheckConstraint(
            '(is_helpful = true AND rating BETWEEN 6 AND 10) OR (is_helpful = false AND rating BETWEEN 1 AND 5)',
            name='chk_feedback_rating_alignment'
        ),
        # Check constraint: rating range
        CheckConstraint('rating BETWEEN 1 AND 10', name='chk_feedback_rating_range'),
        # Indexes for performance
        Index('idx_feedback_interaction', 'interaction_id'),
        Index('idx_feedback_user_created', 'user_id', 'created_at'),
    )
    
    def to_dict(self) -> dict:
        """Convert feedback to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'interaction_id': self.interaction_id,
            'response_id': self.interaction.response_id if self.interaction else None,
            'query_text': self.interaction.query if self.interaction else None,
            'is_helpful': self.is_helpful,
            'rating': self.rating,
            'reasons': self.reasons,
            'comment': self.comment,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
        
    @classmethod
    async def get_user_feedback(cls, db, user_id: int):
        """Get all feedback from a specific user"""
        from sqlalchemy import select
        result = await db.execute(
            select(cls).where(cls.user_id == user_id).order_by(cls.created_at.desc())
        )
        return result.scalars().all()
    
    @classmethod
    async def get_helpful_feedback(cls, db):
        """Get all helpful feedback"""
        from sqlalchemy import select
        result = await db.execute(
            select(cls).where(cls.is_helpful == True).order_by(cls.created_at.desc())
        )
        return result.scalars().all()
    
    @classmethod
    async def get_by_user_and_interaction(cls, db, user_id: int, interaction_id: int):
        """Get feedback by user and interaction"""
        from sqlalchemy import select
        result = await db.execute(
            select(cls).where(
                cls.user_id == user_id,
                cls.interaction_id == interaction_id
            )
        )
        return result.scalars().first()
    
    @classmethod
    async def upsert_feedback(cls, db, user_id: int, interaction_id: int, is_helpful: bool, rating: int, reasons: list = None, comment: str = None):
        """Create or update feedback"""
        existing = await cls.get_by_user_and_interaction(db, user_id, interaction_id)
        
        if existing:
            # Update existing feedback
            existing.is_helpful = is_helpful
            existing.rating = rating
            existing.reasons = reasons
            existing.comment = comment
            existing.updated_at = datetime.utcnow()
            return existing
        else:
            # Create new feedback
            new_feedback = cls(
                user_id=user_id,
                interaction_id=interaction_id,
                is_helpful=is_helpful,
                rating=rating,
                reasons=reasons,
                comment=comment
            )
            db.add(new_feedback)
            return new_feedback

class ExpertChatInteraction(Base):
    """Simplified model for storing expert chat interactions"""
    __tablename__ = "expert_chat_interactions"
    
    id = Column(BigInteger, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    agent_type = Column(String(50), nullable=True)
    session_id = Column(String(100), nullable=True, default='default')  # Add session_id field
    query = Column(Text, nullable=False)
    response = Column(Text, nullable=False)
    sources = Column(JSON, nullable=True)  # Store web sources and metadata
    suggested_followups = Column(JSONB, nullable=True)  # Store suggested follow-up questions
    response_id = Column(String(36), nullable=False, unique=True, default=lambda: str(uuid.uuid4()))
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    shared_responses = relationship("SharedResponse", back_populates="interaction", cascade="all, delete-orphan")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_expert_interactions_user_created', 'user_id', 'created_at'),
        Index('idx_expert_interactions_agent_created', 'agent_type', 'created_at'),
        Index('idx_expert_interactions_response_id', 'response_id'),
        Index('idx_expert_interactions_session', 'user_id', 'session_id', 'agent_type', 'created_at'),
    )
    
    def to_dict(self) -> dict:
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'agent_type': self.agent_type,
            'session_id': self.session_id,
            'query': self.query,
            'response': self.response,
            'sources': self.sources,
            'suggested_followups': self.suggested_followups,
            'response_id': self.response_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    async def get_by_response_id(cls, db, response_id: str):
        """Get interaction by response_id"""
        from sqlalchemy import select
        result = await db.execute(select(cls).where(cls.response_id == response_id))
        return result.scalars().first()
    
    @classmethod
    async def get_user_interactions(cls, db, user_id: int, limit: int = 50):
        """Get user's recent interactions"""
        from sqlalchemy import select
        result = await db.execute(
            select(cls).where(cls.user_id == user_id).order_by(cls.created_at.desc()).limit(limit)
        )
        return result.scalars().all()

class SharedResponse(Base):
    """Track shared AI responses for analytics and context"""
    __tablename__ = "shared_responses"
    
    id = Column(Integer, primary_key=True, index=True)
    interaction_id = Column(BigInteger, ForeignKey('expert_chat_interactions.id', ondelete='CASCADE'), nullable=False)
    shared_by_user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    shared_to_type = Column(String(20), nullable=False)  # 'user' or 'group'
    shared_to_id = Column(Integer, nullable=False)  # user_id or group_id
    share_context = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    interaction = relationship("ExpertChatInteraction", back_populates="shared_responses")
    shared_by_user = relationship("User", foreign_keys=[shared_by_user_id])
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_shared_responses_shared_by', 'shared_by_user_id', 'created_at'),
        Index('idx_shared_responses_target', 'shared_to_type', 'shared_to_id', 'created_at'),
        Index('idx_shared_responses_interaction', 'interaction_id'),
    )
    
    def to_dict(self) -> dict:
        # Check if shared_by_user is loaded to avoid lazy loading
        if 'shared_by_user' in self.__dict__ and self.shared_by_user:
            shared_by_username = self.shared_by_user.username
        else:
            shared_by_username = None
        
        # Check if interaction is loaded to avoid lazy loading
        if 'interaction' in self.__dict__ and self.interaction:
            query = self.interaction.query
            response = self.interaction.response
            agent_type = self.interaction.agent_type
        else:
            query = None
            response = None
            agent_type = None
        
        return {
            'id': self.id,
            'interaction_id': self.interaction_id,
            'shared_by_user_id': self.shared_by_user_id,
            'shared_by_username': shared_by_username,
            'shared_to_type': self.shared_to_type,
            'shared_to_id': self.shared_to_id,
            'share_context': self.share_context,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            # Include interaction details for convenience
            'query': query,
            'response': response,
            'agent_type': agent_type
        }

class AgentChatContextSnapshot(Base):
    """
    Tracks context snapshots for agent chat interactions, including token usage
    and context management strategies used during conversation processing.
    
    This model stores metadata about how the AI agent processed context for each
    interaction, including token limits, context summarization, and hierarchical
    context linking for long conversations.
    """
    __tablename__ = "agent_chat_context_snapshot"
    
    id = Column(BigInteger, primary_key=True, index=True)
    interaction_id = Column(BigInteger, ForeignKey('expert_chat_interactions.id', ondelete='CASCADE'), nullable=False, unique=True)
    user_id = Column(Integer, nullable=False)
    agent_type = Column(String(64), nullable=False)
    chat_session_id = Column(String(128), nullable=False)
    mode = Column(String(16), nullable=False)  # Search mode: web, document, deep, or basic
    context_type = Column(String(16), nullable=False)  # Context strategy: summary, history, or none
    summary_text = Column(Text, nullable=True)  # Generated summary text when context_type is 'summary'
    parent_context_id = Column(BigInteger, ForeignKey('agent_chat_context_snapshot.id', ondelete='SET NULL'), nullable=True)
    
    # Token usage tracking for model context window management
    model_window_tokens = Column(Integer, nullable=False)  # Total available tokens in model window
    reserve_for_answer = Column(Integer, nullable=False)   # Tokens reserved for AI response
    instruction_tokens = Column(Integer, nullable=False)   # Tokens used by system instructions
    input_tokens_used = Column(Integer, nullable=False)    # Tokens actually used for context
    
    instruction_prefix = Column(Text, nullable=True)  # Custom instruction prefix used for this interaction
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    interaction = relationship("ExpertChatInteraction", foreign_keys=[interaction_id])
    parent_context = relationship("AgentChatContextSnapshot", remote_side=[id], foreign_keys=[parent_context_id])
    included_interactions = relationship("AgentChatContextInteraction", back_populates="context_snapshot", cascade="all, delete-orphan")
    
    __table_args__ = (
        # Ensure valid mode and context_type values
        CheckConstraint("mode IN ('web','document','deep','basic')", name='chk_context_mode'),
        CheckConstraint("context_type IN ('summary','history','none')", name='chk_context_type'),
        # Performance indexes for common query patterns
        Index('idx_context_snapshot_user_agent_session', 'user_id', 'agent_type', 'chat_session_id'),
        Index('idx_context_snapshot_parent', 'parent_context_id'),
        Index('idx_context_snapshot_interaction', 'interaction_id'),
    )
    
    def to_dict(self) -> dict:
        """
        Convert the context snapshot to a dictionary representation.
        
        Returns:
            dict: Dictionary containing all snapshot fields with proper serialization
        """
        return {
            'id': self.id,
            'interaction_id': self.interaction_id,
            'user_id': self.user_id,
            'agent_type': self.agent_type,
            'chat_session_id': self.chat_session_id,
            'mode': self.mode,
            'context_type': self.context_type,
            'summary_text': self.summary_text,
            'parent_context_id': self.parent_context_id,
            'model_window_tokens': self.model_window_tokens,
            'reserve_for_answer': self.reserve_for_answer,
            'instruction_tokens': self.instruction_tokens,
            'input_tokens_used': self.input_tokens_used,
            'instruction_prefix': self.instruction_prefix,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class AgentChatContextInteraction(Base):
    """
    Junction table linking context snapshots to the specific prior interactions
    that were included in the context window during processing.
    
    This enables tracking which historical interactions influenced each AI response,
    supporting conversation analysis and context debugging.
    """
    __tablename__ = "agent_chat_context_interactions"
    
    context_id = Column(BigInteger, ForeignKey('agent_chat_context_snapshot.id', ondelete='CASCADE'), primary_key=True)
    interaction_id = Column(BigInteger, ForeignKey('expert_chat_interactions.id', ondelete='CASCADE'), primary_key=True)
    position = Column(SmallInteger, nullable=False)  # Order of interaction in context window (0-based)
    
    # Relationships
    context_snapshot = relationship("AgentChatContextSnapshot", back_populates="included_interactions")
    interaction = relationship("ExpertChatInteraction", foreign_keys=[interaction_id])
    
    __table_args__ = (
        # Performance indexes for efficient querying
        Index('idx_context_interactions_interaction', 'interaction_id'),
        Index('idx_context_interactions_ordered', 'context_id', 'position'),  # For ordered retrieval
    )