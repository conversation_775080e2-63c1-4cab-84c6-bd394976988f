import logging
from dataclasses import dataclass
import os
from dotenv import load_dotenv
load_dotenv()

# Environment Configuration
ENV = os.getenv("ENVIRONMENT")

# Qdrant API Key Configuration
if ENV == "Prod":
    QDRANT_API_KEY = os.getenv("QDRANT_API_KEY_Prod")
else:
    QDRANT_API_KEY = os.getenv("QDRANT_API_KEY_Dev")
# # Document conversion
# try:
#     from spire.doc import Document as SpireDocument
#     from spire.doc import FileFormat
#     SPIRE_AVAILABLE = True
# except ImportError:
#     SPIRE_AVAILABLE = False
#     logging.warning("Spire.Doc not available. Doc/Docx conversion will be skipped.")

# Function to get configuration values based on environment
def get_config_value(key: str, environment: str):
    """Get configuration value based on environment (Dev or Prod)"""
    env_key = f"{key}_{environment}"
    value = os.getenv(env_key)
    if value is None:
        # Fallback to generic key if environment-specific key not found
        value = os.getenv(key)
    return value

# Functions to get environment-specific configuration
def get_qdrant_url(environment: str) -> str:
    return get_config_value("QDRANT_URL", environment)

def get_grpc_port(environment: str) -> int:
    port_str = get_config_value("GRPC_PORT", environment)
    return int(port_str)

def get_database_url(environment: str) -> str:
    return get_config_value("DATABASE_URL", environment)

def get_qdrant_api_key(environment: str) -> str:
    if environment == "Prod":
        return os.getenv("QDRANT_API_KEY_Prod")
    else:
        return os.getenv("QDRANT_API_KEY_Dev")

EMBEDDING_SERVICE_URL = os.getenv("EMBEDDING_SERVICE_URL")
# Gemini API key (same for all environments)
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

# Processing configuration
CHUNK_SIZE = int(os.getenv("CHUNK_SIZE"))
CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP"))
LLM_MODEL = os.getenv("LLM_MODEL")
VISION_MODEL = os.getenv("VISION_MODEL")
PYMUPDF_PRO_KEY = os.getenv("PYMUPDF_PRO_KEY")
@dataclass
class ProcessingConfig:
    """Configuration for the RAG pipeline
    Args:
        Gemini parameters:
            gemini_api_key: str         # Gemini API key

        QDRANT parameters:
            qdrant_url: str           # Qdrant cloud URL
            prefer_grpc: bool         # if you are using Qdrant cloud, set to False
            grpc_port: int            # if you are using Qdrant cloud, set to 6334
            qdrant_api_key: str       # if you are using Qdrant cloud, set to your API key


        use_unstructured: bool = False   # (Only used for pdf and docx files) If set to True, unstructured is used to extract images and text from pdf

        use_custom_splitter: bool     # (Only used for pdf and docx files) By default page wise splitting is done, if this is set to True, custom splitter is used

        Text Splitter parameters (Only used for text files) :
            chunk_size: int           # number of characters in each chunk
            chunk_overlap: int        # number of characters to overlap between chunks

        Custom Splitter parameters (Only used if use_custom_splitter is set to True. This is for pdf and docx files) :
            max_characters : int      # maximum number of characters in a chunk
            combine_text_under_n_chars : int # number of characters to combine under
            new_after_n_chars : int    # number of characters to create a new chunk after
            overlap_chars : int        # number of characters to overlap between chunks

        Embedding models:
            dense_embedding_model: str
            sparse_embedding_model: str

        LLM model:
            llm_model: str

        Vision model:
            vision_model: str

    """
    environment: str  # Environment (Dev or Prod) to determine config values
    qdrant_url: str = None  # Will be set based on environment
    prefer_grpc: bool = True
    grpc_port: int = None  # Will be set based on environment
    qdrant_api_key: str = QDRANT_API_KEY
    DATABASE_URL: str = None  # Will be set based on environment

    # Embedding API configuration (required)
    embedding_api_url: str = "http://43.230.202.228:8015"

    use_custom_splitter: bool = False
    use_unstructured: bool = False
    chunk_size: int = CHUNK_SIZE
    chunk_overlap: int = CHUNK_OVERLAP
    max_characters : int = 2000
    combine_text_under_n_chars : int = 1000
    new_after_n_chars : int = 1200
    overlap_chars : int = 150
    llm_model: str = LLM_MODEL
    vision_model: str = VISION_MODEL
    gemini_api_key: str = GEMINI_API_KEY
    pymupdf_pro_key: str = PYMUPDF_PRO_KEY

