/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unzipper";
exports.ids = ["vendor-chunks/unzipper"];
exports.modules = {

/***/ "(ssr)/./node_modules/unzipper/lib/Buffer.js":
/*!*********************************************!*\
  !*** ./node_modules/unzipper/lib/Buffer.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var Buffer = (__webpack_require__(/*! buffer */ \"buffer\").Buffer);\n\n// Backwards compatibility for node versions < 8\nif (Buffer.from === undefined) {\n  Buffer.from = function (a, b, c) {\n    return new Buffer(a, b, c)\n  };\n\n  Buffer.alloc = Buffer.from;\n}\n\nmodule.exports = Buffer;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW56aXBwZXIvbGliL0J1ZmZlci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxhQUFhLG9EQUF3Qjs7QUFFckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvdW56aXBwZXIvbGliL0J1ZmZlci5qcz8xNjU4Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBCdWZmZXIgPSByZXF1aXJlKCdidWZmZXInKS5CdWZmZXI7XG5cbi8vIEJhY2t3YXJkcyBjb21wYXRpYmlsaXR5IGZvciBub2RlIHZlcnNpb25zIDwgOFxuaWYgKEJ1ZmZlci5mcm9tID09PSB1bmRlZmluZWQpIHtcbiAgQnVmZmVyLmZyb20gPSBmdW5jdGlvbiAoYSwgYiwgYykge1xuICAgIHJldHVybiBuZXcgQnVmZmVyKGEsIGIsIGMpXG4gIH07XG5cbiAgQnVmZmVyLmFsbG9jID0gQnVmZmVyLmZyb207XG59XG5cbm1vZHVsZS5leHBvcnRzID0gQnVmZmVyOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/Buffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/lib/BufferStream.js":
/*!***************************************************!*\
  !*** ./node_modules/unzipper/lib/BufferStream.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var Promise = __webpack_require__(/*! bluebird */ \"(ssr)/./node_modules/bluebird/js/release/bluebird.js\");\nvar Stream = __webpack_require__(/*! stream */ \"stream\");\nvar Buffer = __webpack_require__(/*! ./Buffer */ \"(ssr)/./node_modules/unzipper/lib/Buffer.js\");\n\n// Backwards compatibility for node versions < 8\nif (!Stream.Writable || !Stream.Writable.prototype.destroy)\n  Stream = __webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\");\n\nmodule.exports = function(entry) {\n  return new Promise(function(resolve,reject) {\n    var chunks = [];\n    var bufferStream = Stream.Transform()\n      .on('finish',function() {\n        resolve(Buffer.concat(chunks));\n      })\n      .on('error',reject);\n        \n    bufferStream._transform = function(d,e,cb) {\n      chunks.push(d);\n      cb();\n    };\n    entry.on('error',reject)\n      .pipe(bufferStream);\n  });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW56aXBwZXIvbGliL0J1ZmZlclN0cmVhbS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxjQUFjLG1CQUFPLENBQUMsc0VBQVU7QUFDaEMsYUFBYSxtQkFBTyxDQUFDLHNCQUFRO0FBQzdCLGFBQWEsbUJBQU8sQ0FBQyw2REFBVTs7QUFFL0I7QUFDQTtBQUNBLFdBQVcsbUJBQU8sQ0FBQyx5RUFBaUI7O0FBRXBDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvdW56aXBwZXIvbGliL0J1ZmZlclN0cmVhbS5qcz9lYjU2Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBQcm9taXNlID0gcmVxdWlyZSgnYmx1ZWJpcmQnKTtcbnZhciBTdHJlYW0gPSByZXF1aXJlKCdzdHJlYW0nKTtcbnZhciBCdWZmZXIgPSByZXF1aXJlKCcuL0J1ZmZlcicpO1xuXG4vLyBCYWNrd2FyZHMgY29tcGF0aWJpbGl0eSBmb3Igbm9kZSB2ZXJzaW9ucyA8IDhcbmlmICghU3RyZWFtLldyaXRhYmxlIHx8ICFTdHJlYW0uV3JpdGFibGUucHJvdG90eXBlLmRlc3Ryb3kpXG4gIFN0cmVhbSA9IHJlcXVpcmUoJ3JlYWRhYmxlLXN0cmVhbScpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uKGVudHJ5KSB7XG4gIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbihyZXNvbHZlLHJlamVjdCkge1xuICAgIHZhciBjaHVua3MgPSBbXTtcbiAgICB2YXIgYnVmZmVyU3RyZWFtID0gU3RyZWFtLlRyYW5zZm9ybSgpXG4gICAgICAub24oJ2ZpbmlzaCcsZnVuY3Rpb24oKSB7XG4gICAgICAgIHJlc29sdmUoQnVmZmVyLmNvbmNhdChjaHVua3MpKTtcbiAgICAgIH0pXG4gICAgICAub24oJ2Vycm9yJyxyZWplY3QpO1xuICAgICAgICBcbiAgICBidWZmZXJTdHJlYW0uX3RyYW5zZm9ybSA9IGZ1bmN0aW9uKGQsZSxjYikge1xuICAgICAgY2h1bmtzLnB1c2goZCk7XG4gICAgICBjYigpO1xuICAgIH07XG4gICAgZW50cnkub24oJ2Vycm9yJyxyZWplY3QpXG4gICAgICAucGlwZShidWZmZXJTdHJlYW0pO1xuICB9KTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/BufferStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/lib/Decrypt.js":
/*!**********************************************!*\
  !*** ./node_modules/unzipper/lib/Decrypt.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var bigInt = __webpack_require__(/*! big-integer */ \"(ssr)/./node_modules/big-integer/BigInteger.js\");\nvar Stream = __webpack_require__(/*! stream */ \"stream\");\n\n// Backwards compatibility for node versions < 8\nif (!Stream.Writable || !Stream.Writable.prototype.destroy)\n  Stream = __webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\");\n\nvar table;\n\nfunction generateTable() {\n  var poly = 0xEDB88320,c,n,k;\n  table = [];\n  for (n = 0; n < 256; n++) {\n    c = n;\n    for (k = 0; k < 8; k++)\n      c = (c & 1) ? poly ^ (c >>> 1) :  c = c >>> 1;\n    table[n] = c >>> 0;\n  }\n}\n\nfunction crc(ch,crc) {\n  if (!table)\n    generateTable();\n\n  if (ch.charCodeAt)\n    ch = ch.charCodeAt(0);        \n\n  return (bigInt(crc).shiftRight(8).and(0xffffff)).xor(table[bigInt(crc).xor(ch).and(0xff)]).value;\n}\n\nfunction Decrypt() {\n  if (!(this instanceof Decrypt))\n    return new Decrypt();\n\n  this.key0 = 305419896;\n  this.key1 = 591751049;\n  this.key2 = 878082192;\n}\n\nDecrypt.prototype.update = function(h) {            \n  this.key0 = crc(h,this.key0);\n  this.key1 = bigInt(this.key0).and(255).and(4294967295).add(this.key1)\n  this.key1 = bigInt(this.key1).multiply(134775813).add(1).and(4294967295).value;\n  this.key2 = crc(bigInt(this.key1).shiftRight(24).and(255), this.key2);\n}\n\n\nDecrypt.prototype.decryptByte = function(c) {\n  var k = bigInt(this.key2).or(2);\n  c = c ^ bigInt(k).multiply(bigInt(k^1)).shiftRight(8).and(255);\n  this.update(c);\n  return c;\n};\n\n Decrypt.prototype.stream = function() {\n  var stream = Stream.Transform(),\n      self = this;\n\n  stream._transform = function(d,e,cb) {\n    for (var i = 0; i<d.length;i++) {\n      d[i] = self.decryptByte(d[i]);\n    }\n    this.push(d);\n    cb();\n  };\n  return stream;\n};\n\n\n\n\nmodule.exports = Decrypt;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/Decrypt.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/lib/NoopStream.js":
/*!*************************************************!*\
  !*** ./node_modules/unzipper/lib/NoopStream.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var Stream = __webpack_require__(/*! stream */ \"stream\");\nvar util = __webpack_require__(/*! util */ \"util\");\n\n// Backwards compatibility for node versions < 8\nif (!Stream.Writable || !Stream.Writable.prototype.destroy)\n  Stream = __webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\");\n\nfunction NoopStream() {\n  if (!(this instanceof NoopStream)) {\n    return new NoopStream();\n  }\n  Stream.Transform.call(this);\n}\n\nutil.inherits(NoopStream,Stream.Transform);\n\nNoopStream.prototype._transform = function(d,e,cb) { cb() ;};\n  \nmodule.exports = NoopStream;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW56aXBwZXIvbGliL05vb3BTdHJlYW0uanMiLCJtYXBwaW5ncyI6IkFBQUEsYUFBYSxtQkFBTyxDQUFDLHNCQUFRO0FBQzdCLFdBQVcsbUJBQU8sQ0FBQyxrQkFBTTs7QUFFekI7QUFDQTtBQUNBLFdBQVcsbUJBQU8sQ0FBQyx5RUFBaUI7O0FBRXBDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxxREFBcUQ7QUFDckQ7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3VuemlwcGVyL2xpYi9Ob29wU3RyZWFtLmpzPzVjNDciXSwic291cmNlc0NvbnRlbnQiOlsidmFyIFN0cmVhbSA9IHJlcXVpcmUoJ3N0cmVhbScpO1xudmFyIHV0aWwgPSByZXF1aXJlKCd1dGlsJyk7XG5cbi8vIEJhY2t3YXJkcyBjb21wYXRpYmlsaXR5IGZvciBub2RlIHZlcnNpb25zIDwgOFxuaWYgKCFTdHJlYW0uV3JpdGFibGUgfHwgIVN0cmVhbS5Xcml0YWJsZS5wcm90b3R5cGUuZGVzdHJveSlcbiAgU3RyZWFtID0gcmVxdWlyZSgncmVhZGFibGUtc3RyZWFtJyk7XG5cbmZ1bmN0aW9uIE5vb3BTdHJlYW0oKSB7XG4gIGlmICghKHRoaXMgaW5zdGFuY2VvZiBOb29wU3RyZWFtKSkge1xuICAgIHJldHVybiBuZXcgTm9vcFN0cmVhbSgpO1xuICB9XG4gIFN0cmVhbS5UcmFuc2Zvcm0uY2FsbCh0aGlzKTtcbn1cblxudXRpbC5pbmhlcml0cyhOb29wU3RyZWFtLFN0cmVhbS5UcmFuc2Zvcm0pO1xuXG5Ob29wU3RyZWFtLnByb3RvdHlwZS5fdHJhbnNmb3JtID0gZnVuY3Rpb24oZCxlLGNiKSB7IGNiKCkgO307XG4gIFxubW9kdWxlLmV4cG9ydHMgPSBOb29wU3RyZWFtOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/NoopStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/lib/Open/directory.js":
/*!*****************************************************!*\
  !*** ./node_modules/unzipper/lib/Open/directory.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var binary = __webpack_require__(/*! binary */ \"(ssr)/./node_modules/binary/index.js\");\nvar PullStream = __webpack_require__(/*! ../PullStream */ \"(ssr)/./node_modules/unzipper/lib/PullStream.js\");\nvar unzip = __webpack_require__(/*! ./unzip */ \"(ssr)/./node_modules/unzipper/lib/Open/unzip.js\");\nvar Promise = __webpack_require__(/*! bluebird */ \"(ssr)/./node_modules/bluebird/js/release/bluebird.js\");\nvar BufferStream = __webpack_require__(/*! ../BufferStream */ \"(ssr)/./node_modules/unzipper/lib/BufferStream.js\");\nvar parseExtraField = __webpack_require__(/*! ../parseExtraField */ \"(ssr)/./node_modules/unzipper/lib/parseExtraField.js\");\nvar Buffer = __webpack_require__(/*! ../Buffer */ \"(ssr)/./node_modules/unzipper/lib/Buffer.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar Writer = (__webpack_require__(/*! fstream */ \"(ssr)/./node_modules/fstream/fstream.js\").Writer);\nvar parseDateTime = __webpack_require__(/*! ../parseDateTime */ \"(ssr)/./node_modules/unzipper/lib/parseDateTime.js\");\n\nvar signature = Buffer.alloc(4);\nsignature.writeUInt32LE(0x06054b50,0);\n\nfunction getCrxHeader(source) {\n  var sourceStream = source.stream(0).pipe(PullStream());\n\n  return sourceStream.pull(4).then(function(data) {\n    var signature = data.readUInt32LE(0);\n    if (signature === 0x34327243) {\n      var crxHeader;\n      return sourceStream.pull(12).then(function(data) {\n        crxHeader = binary.parse(data)\n          .word32lu('version')\n          .word32lu('pubKeyLength')\n          .word32lu('signatureLength')\n          .vars;\n      }).then(function() {\n        return sourceStream.pull(crxHeader.pubKeyLength +crxHeader.signatureLength);\n      }).then(function(data) {\n        crxHeader.publicKey = data.slice(0,crxHeader.pubKeyLength);\n        crxHeader.signature = data.slice(crxHeader.pubKeyLength);\n        crxHeader.size = 16 + crxHeader.pubKeyLength +crxHeader.signatureLength;\n        return crxHeader;\n      });\n    }\n  });\n}\n\n// Zip64 File Format Notes: https://pkware.cachefly.net/webdocs/casestudies/APPNOTE.TXT\nfunction getZip64CentralDirectory(source, zip64CDL) {\n  var d64loc = binary.parse(zip64CDL)\n    .word32lu('signature')\n    .word32lu('diskNumber')\n    .word64lu('offsetToStartOfCentralDirectory')\n    .word32lu('numberOfDisks')\n    .vars;\n\n  if (d64loc.signature != 0x07064b50) {\n    throw new Error('invalid zip64 end of central dir locator signature (0x07064b50): 0x' + d64loc.signature.toString(16));\n  }\n\n  var dir64 = PullStream();\n  source.stream(d64loc.offsetToStartOfCentralDirectory).pipe(dir64);\n\n  return dir64.pull(56)\n}\n\n// Zip64 File Format Notes: https://pkware.cachefly.net/webdocs/casestudies/APPNOTE.TXT\nfunction parseZip64DirRecord (dir64record) {\n  var vars = binary.parse(dir64record)\n    .word32lu('signature')\n    .word64lu('sizeOfCentralDirectory')\n    .word16lu('version')\n    .word16lu('versionsNeededToExtract')\n    .word32lu('diskNumber')\n    .word32lu('diskStart')\n    .word64lu('numberOfRecordsOnDisk')\n    .word64lu('numberOfRecords')\n    .word64lu('sizeOfCentralDirectory')\n    .word64lu('offsetToStartOfCentralDirectory')\n    .vars;\n\n  if (vars.signature != 0x06064b50) {\n    throw new Error('invalid zip64 end of central dir locator signature (0x06064b50): 0x0' + vars.signature.toString(16));\n  }\n\n  return vars\n}\n\nmodule.exports = function centralDirectory(source, options) {\n  var endDir = PullStream(),\n      records = PullStream(),\n      tailSize = (options && options.tailSize) || 80,\n      sourceSize,\n      crxHeader,\n      startOffset,\n      vars;\n\n  if (options && options.crx)\n    crxHeader = getCrxHeader(source);\n\n  return source.size()\n    .then(function(size) {\n      sourceSize = size;\n\n      source.stream(Math.max(0,size-tailSize))\n        .on('error', function (error) { endDir.emit('error', error) })\n        .pipe(endDir);\n\n      return endDir.pull(signature);\n    })\n    .then(function() {\n      return Promise.props({directory: endDir.pull(22), crxHeader: crxHeader});\n    })\n    .then(function(d) {\n      var data = d.directory;\n      startOffset = d.crxHeader && d.crxHeader.size || 0;\n\n      vars = binary.parse(data)\n        .word32lu('signature')\n        .word16lu('diskNumber')\n        .word16lu('diskStart')\n        .word16lu('numberOfRecordsOnDisk')\n        .word16lu('numberOfRecords')\n        .word32lu('sizeOfCentralDirectory')\n        .word32lu('offsetToStartOfCentralDirectory')\n        .word16lu('commentLength')\n        .vars;\n\n      // Is this zip file using zip64 format? Use same check as Go:\n      // https://github.com/golang/go/blob/master/src/archive/zip/reader.go#L503\n      // For zip64 files, need to find zip64 central directory locator header to extract\n      // relative offset for zip64 central directory record.\n      if (vars.numberOfRecords == 0xffff|| vars.numberOfRecords == 0xffff ||\n        vars.offsetToStartOfCentralDirectory == 0xffffffff) {\n\n        // Offset to zip64 CDL is 20 bytes before normal CDR\n        const zip64CDLSize = 20\n        const zip64CDLOffset = sourceSize - (tailSize - endDir.match + zip64CDLSize)\n        const zip64CDLStream = PullStream();\n\n        source.stream(zip64CDLOffset).pipe(zip64CDLStream);\n\n        return zip64CDLStream.pull(zip64CDLSize)\n          .then(function (d) { return getZip64CentralDirectory(source, d) })\n          .then(function (dir64record) {\n            vars = parseZip64DirRecord(dir64record)\n          })\n      } else {\n        vars.offsetToStartOfCentralDirectory += startOffset;\n      }\n    })\n    .then(function() {\n      if (vars.commentLength) return endDir.pull(vars.commentLength).then(function(comment) {\n        vars.comment = comment.toString('utf8');\n      });\n    })\n    .then(function() {\n      source.stream(vars.offsetToStartOfCentralDirectory).pipe(records);\n\n      vars.extract = function(opts) {\n        if (!opts || !opts.path) throw new Error('PATH_MISSING');\n        // make sure path is normalized before using it\n        opts.path = path.resolve(path.normalize(opts.path));\n        return vars.files.then(function(files) {\n          return Promise.map(files, function(entry) {\n            if (entry.type == 'Directory') return;\n\n            // to avoid zip slip (writing outside of the destination), we resolve\n            // the target path, and make sure it's nested in the intended\n            // destination, or not extract it otherwise.\n            var extractPath = path.join(opts.path, entry.path);\n            if (extractPath.indexOf(opts.path) != 0) {\n              return;\n            }\n            var writer = opts.getWriter ? opts.getWriter({path: extractPath}) :  Writer({ path: extractPath });\n\n            return new Promise(function(resolve, reject) {\n              entry.stream(opts.password)\n                .on('error',reject)\n                .pipe(writer)\n                .on('close',resolve)\n                .on('error',reject);\n            });\n          }, { concurrency: opts.concurrency > 1 ? opts.concurrency : 1 });\n        });\n      };\n\n      vars.files = Promise.mapSeries(Array(vars.numberOfRecords),function() {\n        return records.pull(46).then(function(data) {    \n          var vars = binary.parse(data)\n            .word32lu('signature')\n            .word16lu('versionMadeBy')\n            .word16lu('versionsNeededToExtract')\n            .word16lu('flags')\n            .word16lu('compressionMethod')\n            .word16lu('lastModifiedTime')\n            .word16lu('lastModifiedDate')\n            .word32lu('crc32')\n            .word32lu('compressedSize')\n            .word32lu('uncompressedSize')\n            .word16lu('fileNameLength')\n            .word16lu('extraFieldLength')\n            .word16lu('fileCommentLength')\n            .word16lu('diskNumber')\n            .word16lu('internalFileAttributes')\n            .word32lu('externalFileAttributes')\n            .word32lu('offsetToLocalFileHeader')\n            .vars;\n\n        vars.offsetToLocalFileHeader += startOffset;\n        vars.lastModifiedDateTime = parseDateTime(vars.lastModifiedDate, vars.lastModifiedTime);\n\n        return records.pull(vars.fileNameLength).then(function(fileNameBuffer) {\n          vars.pathBuffer = fileNameBuffer;\n          vars.path = fileNameBuffer.toString('utf8');\n          vars.isUnicode = (vars.flags & 0x800) != 0;\n          return records.pull(vars.extraFieldLength);\n        })\n        .then(function(extraField) {\n          vars.extra = parseExtraField(extraField, vars);\n          return records.pull(vars.fileCommentLength);\n        })\n        .then(function(comment) {\n          vars.comment = comment;\n          vars.type = (vars.uncompressedSize === 0 && /[\\/\\\\]$/.test(vars.path)) ? 'Directory' : 'File';\n          vars.stream = function(_password) {\n            return unzip(source, vars.offsetToLocalFileHeader,_password, vars);\n          };\n          vars.buffer = function(_password) {\n            return BufferStream(vars.stream(_password));\n          };\n          return vars;\n        });\n      });\n    });\n\n    return Promise.props(vars);\n  });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/Open/directory.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/lib/Open/index.js":
/*!*************************************************!*\
  !*** ./node_modules/unzipper/lib/Open/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\");\nvar Promise = __webpack_require__(/*! bluebird */ \"(ssr)/./node_modules/bluebird/js/release/bluebird.js\");\nvar directory = __webpack_require__(/*! ./directory */ \"(ssr)/./node_modules/unzipper/lib/Open/directory.js\");\nvar Stream = __webpack_require__(/*! stream */ \"stream\");\n\n// Backwards compatibility for node versions < 8\nif (!Stream.Writable || !Stream.Writable.prototype.destroy)\n  Stream = __webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\");\n\nmodule.exports = {\n  buffer: function(buffer, options) {\n    var source = {\n      stream: function(offset, length) {\n        var stream = Stream.PassThrough();\n        stream.end(buffer.slice(offset, length));\n        return stream;\n      },\n      size: function() {\n        return Promise.resolve(buffer.length);\n      }\n    };\n    return directory(source, options);\n  },\n  file: function(filename, options) {\n    var source = {\n      stream: function(offset,length) {\n        return fs.createReadStream(filename,{start: offset, end: length && offset+length});\n      },\n      size: function() {\n        return new Promise(function(resolve,reject) {\n          fs.stat(filename,function(err,d) {\n            if (err)\n              reject(err);\n            else\n              resolve(d.size);\n          });\n        });\n      }\n    };\n    return directory(source, options);\n  },\n\n  url: function(request, params, options) {\n    if (typeof params === 'string')\n      params = {url: params};\n    if (!params.url)\n      throw 'URL missing';\n    params.headers = params.headers || {};\n\n    var source = {\n      stream : function(offset,length) {\n        var options = Object.create(params);\n        options.headers = Object.create(params.headers);\n        options.headers.range = 'bytes='+offset+'-' + (length ? length : '');\n        return request(options);\n      },\n      size: function() {\n        return new Promise(function(resolve,reject) {\n          var req = request(params);\n          req.on('response',function(d) {\n            req.abort();\n            if (!d.headers['content-length'])\n              reject(new Error('Missing content length header'));\n            else\n              resolve(d.headers['content-length']);\n          }).on('error',reject);\n        });\n      }\n    };\n\n    return directory(source, options);\n  },\n\n  s3 : function(client,params, options) {\n    var source = {\n      size: function() {\n        return new Promise(function(resolve,reject) {\n          client.headObject(params, function(err,d) {\n            if (err)\n              reject(err);\n            else\n              resolve(d.ContentLength);\n          });\n        });\n      },\n      stream: function(offset,length) {\n        var d = {};\n        for (var key in params)\n          d[key] = params[key];\n        d.Range = 'bytes='+offset+'-' + (length ? length : '');\n        return client.getObject(d).createReadStream();\n      }\n    };\n\n    return directory(source, options);\n  },\n\n  custom: function(source, options) {\n    return directory(source, options);\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/Open/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/lib/Open/unzip.js":
/*!*************************************************!*\
  !*** ./node_modules/unzipper/lib/Open/unzip.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var Promise = __webpack_require__(/*! bluebird */ \"(ssr)/./node_modules/bluebird/js/release/bluebird.js\");\nvar Decrypt = __webpack_require__(/*! ../Decrypt */ \"(ssr)/./node_modules/unzipper/lib/Decrypt.js\");\nvar PullStream = __webpack_require__(/*! ../PullStream */ \"(ssr)/./node_modules/unzipper/lib/PullStream.js\");\nvar Stream = __webpack_require__(/*! stream */ \"stream\");\nvar binary = __webpack_require__(/*! binary */ \"(ssr)/./node_modules/binary/index.js\");\nvar zlib = __webpack_require__(/*! zlib */ \"zlib\");\nvar parseExtraField = __webpack_require__(/*! ../parseExtraField */ \"(ssr)/./node_modules/unzipper/lib/parseExtraField.js\");\nvar Buffer = __webpack_require__(/*! ../Buffer */ \"(ssr)/./node_modules/unzipper/lib/Buffer.js\");\nvar parseDateTime = __webpack_require__(/*! ../parseDateTime */ \"(ssr)/./node_modules/unzipper/lib/parseDateTime.js\");\n\n// Backwards compatibility for node versions < 8\nif (!Stream.Writable || !Stream.Writable.prototype.destroy)\n  Stream = __webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\");\n\nmodule.exports = function unzip(source,offset,_password, directoryVars) {\n  var file = PullStream(),\n      entry = Stream.PassThrough();\n\n  var req = source.stream(offset);\n  req.pipe(file).on('error', function(e) {\n    entry.emit('error', e);\n  });\n\n  entry.vars = file.pull(30)\n    .then(function(data) {\n      var vars = binary.parse(data)\n        .word32lu('signature')\n        .word16lu('versionsNeededToExtract')\n        .word16lu('flags')\n        .word16lu('compressionMethod')\n        .word16lu('lastModifiedTime')\n        .word16lu('lastModifiedDate')\n        .word32lu('crc32')\n        .word32lu('compressedSize')\n        .word32lu('uncompressedSize')\n        .word16lu('fileNameLength')\n        .word16lu('extraFieldLength')\n        .vars;\n\n      vars.lastModifiedDateTime = parseDateTime(vars.lastModifiedDate, vars.lastModifiedTime);\n\n      return file.pull(vars.fileNameLength)\n        .then(function(fileName) {\n          vars.fileName = fileName.toString('utf8');\n          return file.pull(vars.extraFieldLength);\n        })\n        .then(function(extraField) {\n          var checkEncryption;\n          vars.extra = parseExtraField(extraField, vars);\n          // Ignore logal file header vars if the directory vars are available\n          if (directoryVars && directoryVars.compressedSize) vars = directoryVars;\n\n          if (vars.flags & 0x01) checkEncryption = file.pull(12)\n            .then(function(header) {\n              if (!_password)\n                throw new Error('MISSING_PASSWORD');\n\n              var decrypt = Decrypt();\n\n              String(_password).split('').forEach(function(d) {\n                decrypt.update(d);\n              });\n\n              for (var i=0; i < header.length; i++)\n                header[i] = decrypt.decryptByte(header[i]);\n\n              vars.decrypt = decrypt;\n              vars.compressedSize -= 12;\n\n              var check = (vars.flags & 0x8) ? (vars.lastModifiedTime >> 8) & 0xff : (vars.crc32 >> 24) & 0xff;\n              if (header[11] !== check)\n                throw new Error('BAD_PASSWORD');\n\n              return vars;\n            });\n\n          return Promise.resolve(checkEncryption)\n            .then(function() {\n              entry.emit('vars',vars);\n              return vars;\n            });\n        });\n    });\n\n    entry.vars.then(function(vars) {\n      var fileSizeKnown = !(vars.flags & 0x08) || vars.compressedSize > 0,\n          eof;\n\n      var inflater = vars.compressionMethod ? zlib.createInflateRaw() : Stream.PassThrough();\n\n      if (fileSizeKnown) {\n        entry.size = vars.uncompressedSize;\n        eof = vars.compressedSize;\n      } else {\n        eof = Buffer.alloc(4);\n        eof.writeUInt32LE(0x08074b50, 0);\n      }\n\n      var stream = file.stream(eof);\n\n      if (vars.decrypt)\n        stream = stream.pipe(vars.decrypt.stream());\n\n      stream\n        .pipe(inflater)\n        .on('error',function(err) { entry.emit('error',err);})\n        .pipe(entry)\n        .on('finish', function() {\n          if(req.destroy)\n            req.destroy()\n          else if (req.abort)\n            req.abort();\n          else if (req.close)\n            req.close();\n          else if (req.push)\n            req.push();\n          else\n            console.log('warning - unable to close stream');\n        });\n    })\n    .catch(function(e) {\n      entry.emit('error',e);\n    });\n\n  return entry;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/Open/unzip.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/lib/PullStream.js":
/*!*************************************************!*\
  !*** ./node_modules/unzipper/lib/PullStream.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var Stream = __webpack_require__(/*! stream */ \"stream\");\nvar Promise = __webpack_require__(/*! bluebird */ \"(ssr)/./node_modules/bluebird/js/release/bluebird.js\");\nvar util = __webpack_require__(/*! util */ \"util\");\nvar Buffer = __webpack_require__(/*! ./Buffer */ \"(ssr)/./node_modules/unzipper/lib/Buffer.js\");\nvar strFunction = 'function';\n\n// Backwards compatibility for node versions < 8\nif (!Stream.Writable || !Stream.Writable.prototype.destroy)\n  Stream = __webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\");\n\nfunction PullStream() {\n  if (!(this instanceof PullStream))\n    return new PullStream();\n\n  Stream.Duplex.call(this,{decodeStrings:false, objectMode:true});\n  this.buffer = Buffer.from('');\n  var self = this;\n  self.on('finish',function() {\n    self.finished = true;\n    self.emit('chunk',false);\n  });\n}\n\nutil.inherits(PullStream,Stream.Duplex);\n\nPullStream.prototype._write = function(chunk,e,cb) {\n  this.buffer = Buffer.concat([this.buffer,chunk]);\n  this.cb = cb;\n  this.emit('chunk');\n};\n\n\n// The `eof` parameter is interpreted as `file_length` if the type is number\n// otherwise (i.e. buffer) it is interpreted as a pattern signaling end of stream\nPullStream.prototype.stream = function(eof,includeEof) {\n  var p = Stream.PassThrough();\n  var done,self= this;\n\n  function cb() {\n    if (typeof self.cb === strFunction) {\n      var callback = self.cb;\n      self.cb = undefined;\n      return callback();\n    }\n  }\n\n  function pull() {\n    var packet;\n    if (self.buffer && self.buffer.length) {\n      if (typeof eof === 'number') {\n        packet = self.buffer.slice(0,eof);\n        self.buffer = self.buffer.slice(eof);\n        eof -= packet.length;\n        done = !eof;\n      } else {\n        var match = self.buffer.indexOf(eof);\n        if (match !== -1) {\n          // store signature match byte offset to allow us to reference\n          // this for zip64 offset\n          self.match = match\n          if (includeEof) match = match + eof.length;\n          packet = self.buffer.slice(0,match);\n          self.buffer = self.buffer.slice(match);\n          done = true;\n        } else {\n          var len = self.buffer.length - eof.length;\n          if (len <= 0) {\n            cb();\n          } else {\n            packet = self.buffer.slice(0,len);\n            self.buffer = self.buffer.slice(len);\n          }\n        }\n      }\n      if (packet) p.write(packet,function() {\n        if (self.buffer.length === 0 || (eof.length && self.buffer.length <= eof.length)) cb();\n      });\n    }\n    \n    if (!done) {\n      if (self.finished) {\n        self.removeListener('chunk',pull);\n        self.emit('error', new Error('FILE_ENDED'));\n        return;\n      }\n      \n    } else {\n      self.removeListener('chunk',pull);\n      p.end();\n    }\n  }\n\n  self.on('chunk',pull);\n  pull();\n  return p;\n};\n\nPullStream.prototype.pull = function(eof,includeEof) {\n  if (eof === 0) return Promise.resolve('');\n\n  // If we already have the required data in buffer\n  // we can resolve the request immediately\n  if (!isNaN(eof) && this.buffer.length > eof) {\n    var data = this.buffer.slice(0,eof);\n    this.buffer = this.buffer.slice(eof);\n    return Promise.resolve(data);\n  }\n\n  // Otherwise we stream until we have it\n  var buffer = Buffer.from(''),\n      self = this;\n\n  var concatStream = Stream.Transform();\n  concatStream._transform = function(d,e,cb) {\n    buffer = Buffer.concat([buffer,d]);\n    cb();\n  };\n  \n  var rejectHandler;\n  var pullStreamRejectHandler;\n  return new Promise(function(resolve,reject) {\n    rejectHandler = reject;\n    pullStreamRejectHandler = function(e) {\n      self.__emittedError = e;\n      reject(e);\n    }\n    if (self.finished)\n      return reject(new Error('FILE_ENDED'));\n    self.once('error',pullStreamRejectHandler);  // reject any errors from pullstream itself\n    self.stream(eof,includeEof)\n      .on('error',reject)\n      .pipe(concatStream)\n      .on('finish',function() {resolve(buffer);})\n      .on('error',reject);\n  })\n  .finally(function() {\n    self.removeListener('error',rejectHandler);\n    self.removeListener('error',pullStreamRejectHandler);\n  });\n};\n\nPullStream.prototype._read = function(){};\n\nmodule.exports = PullStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/PullStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/lib/extract.js":
/*!**********************************************!*\
  !*** ./node_modules/unzipper/lib/extract.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = Extract;\n\nvar Parse = __webpack_require__(/*! ./parse */ \"(ssr)/./node_modules/unzipper/lib/parse.js\");\nvar Writer = (__webpack_require__(/*! fstream */ \"(ssr)/./node_modules/fstream/fstream.js\").Writer);\nvar path = __webpack_require__(/*! path */ \"path\");\nvar stream = __webpack_require__(/*! stream */ \"stream\");\nvar duplexer2 = __webpack_require__(/*! duplexer2 */ \"(ssr)/./node_modules/duplexer2/index.js\");\nvar Promise = __webpack_require__(/*! bluebird */ \"(ssr)/./node_modules/bluebird/js/release/bluebird.js\");\n\nfunction Extract (opts) {\n  // make sure path is normalized before using it\n  opts.path = path.resolve(path.normalize(opts.path));\n\n  var parser = new Parse(opts);\n\n  var outStream = new stream.Writable({objectMode: true});\n  outStream._write = function(entry, encoding, cb) {\n\n    if (entry.type == 'Directory') return cb();\n\n    // to avoid zip slip (writing outside of the destination), we resolve\n    // the target path, and make sure it's nested in the intended\n    // destination, or not extract it otherwise.\n    var extractPath = path.join(opts.path, entry.path);\n    if (extractPath.indexOf(opts.path) != 0) {\n      return cb();\n    }\n\n    const writer = opts.getWriter ? opts.getWriter({path: extractPath}) :  Writer({ path: extractPath });\n\n    entry.pipe(writer)\n      .on('error', cb)\n      .on('close', cb);\n  };\n\n  var extract = duplexer2(parser,outStream);\n  parser.once('crx-header', function(crxHeader) {\n    extract.crxHeader = crxHeader;\n  });\n\n  parser\n    .pipe(outStream)\n    .on('finish',function() {\n      extract.emit('close');\n    });\n  \n  extract.promise = function() {\n    return new Promise(function(resolve, reject) {\n      extract.on('close', resolve);\n      extract.on('error',reject);\n    });\n  };\n\n  return extract;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/extract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/lib/parse.js":
/*!********************************************!*\
  !*** ./node_modules/unzipper/lib/parse.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var util = __webpack_require__(/*! util */ \"util\");\nvar zlib = __webpack_require__(/*! zlib */ \"zlib\");\nvar Stream = __webpack_require__(/*! stream */ \"stream\");\nvar binary = __webpack_require__(/*! binary */ \"(ssr)/./node_modules/binary/index.js\");\nvar Promise = __webpack_require__(/*! bluebird */ \"(ssr)/./node_modules/bluebird/js/release/bluebird.js\");\nvar PullStream = __webpack_require__(/*! ./PullStream */ \"(ssr)/./node_modules/unzipper/lib/PullStream.js\");\nvar NoopStream = __webpack_require__(/*! ./NoopStream */ \"(ssr)/./node_modules/unzipper/lib/NoopStream.js\");\nvar BufferStream = __webpack_require__(/*! ./BufferStream */ \"(ssr)/./node_modules/unzipper/lib/BufferStream.js\");\nvar parseExtraField = __webpack_require__(/*! ./parseExtraField */ \"(ssr)/./node_modules/unzipper/lib/parseExtraField.js\");\nvar Buffer = __webpack_require__(/*! ./Buffer */ \"(ssr)/./node_modules/unzipper/lib/Buffer.js\");\nvar parseDateTime = __webpack_require__(/*! ./parseDateTime */ \"(ssr)/./node_modules/unzipper/lib/parseDateTime.js\");\n\n// Backwards compatibility for node versions < 8\nif (!Stream.Writable || !Stream.Writable.prototype.destroy)\n  Stream = __webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\");\n\nvar endDirectorySignature = Buffer.alloc(4);\nendDirectorySignature.writeUInt32LE(0x06054b50, 0);\n\nfunction Parse(opts) {\n  if (!(this instanceof Parse)) {\n    return new Parse(opts);\n  }\n  var self = this;\n  self._opts = opts || { verbose: false };\n\n  PullStream.call(self, self._opts);\n  self.on('finish',function() {\n    self.emit('end');\n    self.emit('close');\n  });\n  self._readRecord().catch(function(e) {\n    if (!self.__emittedError || self.__emittedError !== e)\n      self.emit('error',e);\n  });\n}\n\nutil.inherits(Parse, PullStream);\n\nParse.prototype._readRecord = function () {\n  var self = this;\n  return self.pull(4).then(function(data) {\n    if (data.length === 0)\n      return;\n\n    var signature = data.readUInt32LE(0);\n\n    if (signature === 0x34327243) {\n      return self._readCrxHeader();\n    }\n    if (signature === 0x04034b50) {\n      return self._readFile();\n    }\n    else if (signature === 0x02014b50) {\n      self.reachedCD = true;\n      return self._readCentralDirectoryFileHeader();\n    }\n    else if (signature === 0x06054b50) {\n      return self._readEndOfCentralDirectoryRecord();\n    }\n    else if (self.reachedCD) {\n      // _readEndOfCentralDirectoryRecord expects the EOCD\n      // signature to be consumed so set includeEof=true\n      var includeEof = true;\n      return self.pull(endDirectorySignature, includeEof).then(function() {\n        return self._readEndOfCentralDirectoryRecord();\n      });\n    }\n    else\n      self.emit('error', new Error('invalid signature: 0x' + signature.toString(16)));\n  });\n};\n\nParse.prototype._readCrxHeader = function() {\n  var self = this;\n  return self.pull(12).then(function(data) {\n    self.crxHeader = binary.parse(data)\n      .word32lu('version')\n      .word32lu('pubKeyLength')\n      .word32lu('signatureLength')\n      .vars;\n    return self.pull(self.crxHeader.pubKeyLength + self.crxHeader.signatureLength);\n  }).then(function(data) {\n    self.crxHeader.publicKey = data.slice(0,self.crxHeader.pubKeyLength);\n    self.crxHeader.signature = data.slice(self.crxHeader.pubKeyLength);\n    self.emit('crx-header',self.crxHeader);\n    return self._readRecord();\n  });\n};\n\nParse.prototype._readFile = function () {\n  var self = this;\n  return self.pull(26).then(function(data) {\n    var vars = binary.parse(data)\n      .word16lu('versionsNeededToExtract')\n      .word16lu('flags')\n      .word16lu('compressionMethod')\n      .word16lu('lastModifiedTime')\n      .word16lu('lastModifiedDate')\n      .word32lu('crc32')\n      .word32lu('compressedSize')\n      .word32lu('uncompressedSize')\n      .word16lu('fileNameLength')\n      .word16lu('extraFieldLength')\n      .vars;\n\n    vars.lastModifiedDateTime = parseDateTime(vars.lastModifiedDate, vars.lastModifiedTime);\n\n    if (self.crxHeader) vars.crxHeader = self.crxHeader;\n\n    return self.pull(vars.fileNameLength).then(function(fileNameBuffer) {\n      var fileName = fileNameBuffer.toString('utf8');\n      var entry = Stream.PassThrough();\n      var __autodraining = false;\n\n      entry.autodrain = function() {\n        __autodraining = true;\n        var draining = entry.pipe(NoopStream());\n        draining.promise = function() {\n          return new Promise(function(resolve, reject) {\n            draining.on('finish',resolve);\n            draining.on('error',reject);\n          });\n        };\n        return draining;\n      };\n\n      entry.buffer = function() {\n        return BufferStream(entry);\n      };\n\n      entry.path = fileName;\n      entry.props = {};\n      entry.props.path = fileName;\n      entry.props.pathBuffer = fileNameBuffer;\n      entry.props.flags = {\n        \"isUnicode\": (vars.flags & 0x800) != 0\n      };\n      entry.type = (vars.uncompressedSize === 0 && /[\\/\\\\]$/.test(fileName)) ? 'Directory' : 'File';\n\n      if (self._opts.verbose) {\n        if (entry.type === 'Directory') {\n          console.log('   creating:', fileName);\n        } else if (entry.type === 'File') {\n          if (vars.compressionMethod === 0) {\n            console.log(' extracting:', fileName);\n          } else {\n            console.log('  inflating:', fileName);\n          }\n        }\n      }\n\n      return self.pull(vars.extraFieldLength).then(function(extraField) {\n        var extra = parseExtraField(extraField, vars);\n\n        entry.vars = vars;\n        entry.extra = extra;\n\n        if (self._opts.forceStream) {\n          self.push(entry);\n        } else {\n          self.emit('entry', entry);\n\n          if (self._readableState.pipesCount || (self._readableState.pipes && self._readableState.pipes.length))\n            self.push(entry);\n        }\n\n        if (self._opts.verbose)\n          console.log({\n            filename:fileName,\n            vars: vars,\n            extra: extra\n          });\n\n        var fileSizeKnown = !(vars.flags & 0x08) || vars.compressedSize > 0,\n            eof;\n\n        entry.__autodraining = __autodraining;  // expose __autodraining for test purposes\n        var inflater = (vars.compressionMethod && !__autodraining) ? zlib.createInflateRaw() : Stream.PassThrough();\n\n        if (fileSizeKnown) {\n          entry.size = vars.uncompressedSize;\n          eof = vars.compressedSize;\n        } else {\n          eof = Buffer.alloc(4);\n          eof.writeUInt32LE(0x08074b50, 0);\n        }\n\n        return new Promise(function(resolve, reject) {\n          self.stream(eof)\n            .pipe(inflater)\n            .on('error',function(err) { self.emit('error',err);})\n            .pipe(entry)\n            .on('finish', function() {\n              return fileSizeKnown ?\n                self._readRecord().then(resolve).catch(reject) :\n                self._processDataDescriptor(entry).then(resolve).catch(reject);\n            });\n        });\n      });\n    });\n  });\n};\n\nParse.prototype._processDataDescriptor = function (entry) {\n  var self = this;\n  return self.pull(16).then(function(data) {\n    var vars = binary.parse(data)\n      .word32lu('dataDescriptorSignature')\n      .word32lu('crc32')\n      .word32lu('compressedSize')\n      .word32lu('uncompressedSize')\n      .vars;\n\n    entry.size = vars.uncompressedSize;\n    return self._readRecord();\n  });\n};\n\nParse.prototype._readCentralDirectoryFileHeader = function () {\n  var self = this;\n  return self.pull(42).then(function(data) {\n\n    var vars = binary.parse(data)\n      .word16lu('versionMadeBy')\n      .word16lu('versionsNeededToExtract')\n      .word16lu('flags')\n      .word16lu('compressionMethod')\n      .word16lu('lastModifiedTime')\n      .word16lu('lastModifiedDate')\n      .word32lu('crc32')\n      .word32lu('compressedSize')\n      .word32lu('uncompressedSize')\n      .word16lu('fileNameLength')\n      .word16lu('extraFieldLength')\n      .word16lu('fileCommentLength')\n      .word16lu('diskNumber')\n      .word16lu('internalFileAttributes')\n      .word32lu('externalFileAttributes')\n      .word32lu('offsetToLocalFileHeader')\n      .vars;\n\n    return self.pull(vars.fileNameLength).then(function(fileName) {\n      vars.fileName = fileName.toString('utf8');\n      return self.pull(vars.extraFieldLength);\n    })\n    .then(function(extraField) {\n      return self.pull(vars.fileCommentLength);\n    })\n    .then(function(fileComment) {\n      return self._readRecord();\n    });\n  });\n};\n\nParse.prototype._readEndOfCentralDirectoryRecord = function() {\n  var self = this;\n  return self.pull(18).then(function(data) {\n\n    var vars = binary.parse(data)\n      .word16lu('diskNumber')\n      .word16lu('diskStart')\n      .word16lu('numberOfRecordsOnDisk')\n      .word16lu('numberOfRecords')\n      .word32lu('sizeOfCentralDirectory')\n      .word32lu('offsetToStartOfCentralDirectory')\n      .word16lu('commentLength')\n      .vars;\n\n    return self.pull(vars.commentLength).then(function(comment) {\n      comment = comment.toString('utf8');\n      self.end();\n      self.push(null);\n    });\n\n  });\n};\n\nParse.prototype.promise = function() {\n  var self = this;\n  return new Promise(function(resolve,reject) {\n    self.on('finish',resolve);\n    self.on('error',reject);\n  });\n};\n\nmodule.exports = Parse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/lib/parseDateTime.js":
/*!****************************************************!*\
  !*** ./node_modules/unzipper/lib/parseDateTime.js ***!
  \****************************************************/
/***/ ((module) => {

eval("// Dates in zip file entries are stored as DosDateTime\n// Spec is here: https://docs.microsoft.com/en-us/windows/win32/api/winbase/nf-winbase-dosdatetimetofiletime\n\nmodule.exports = function parseDateTime(date, time) {\n  const day = date & 0x1F;\n  const month = date >> 5 & 0x0F;\n  const year = (date >> 9 & 0x7F) + 1980;\n  const seconds = time ? (time & 0x1F) * 2 : 0;\n  const minutes = time ? (time >> 5) & 0x3F : 0;\n  const hours = time ? (time >> 11): 0;\n\n  return new Date(Date.UTC(year, month-1, day, hours, minutes, seconds));\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW56aXBwZXIvbGliL3BhcnNlRGF0ZVRpbWUuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvdW56aXBwZXIvbGliL3BhcnNlRGF0ZVRpbWUuanM/YzEwYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBEYXRlcyBpbiB6aXAgZmlsZSBlbnRyaWVzIGFyZSBzdG9yZWQgYXMgRG9zRGF0ZVRpbWVcbi8vIFNwZWMgaXMgaGVyZTogaHR0cHM6Ly9kb2NzLm1pY3Jvc29mdC5jb20vZW4tdXMvd2luZG93cy93aW4zMi9hcGkvd2luYmFzZS9uZi13aW5iYXNlLWRvc2RhdGV0aW1ldG9maWxldGltZVxuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIHBhcnNlRGF0ZVRpbWUoZGF0ZSwgdGltZSkge1xuICBjb25zdCBkYXkgPSBkYXRlICYgMHgxRjtcbiAgY29uc3QgbW9udGggPSBkYXRlID4+IDUgJiAweDBGO1xuICBjb25zdCB5ZWFyID0gKGRhdGUgPj4gOSAmIDB4N0YpICsgMTk4MDtcbiAgY29uc3Qgc2Vjb25kcyA9IHRpbWUgPyAodGltZSAmIDB4MUYpICogMiA6IDA7XG4gIGNvbnN0IG1pbnV0ZXMgPSB0aW1lID8gKHRpbWUgPj4gNSkgJiAweDNGIDogMDtcbiAgY29uc3QgaG91cnMgPSB0aW1lID8gKHRpbWUgPj4gMTEpOiAwO1xuXG4gIHJldHVybiBuZXcgRGF0ZShEYXRlLlVUQyh5ZWFyLCBtb250aC0xLCBkYXksIGhvdXJzLCBtaW51dGVzLCBzZWNvbmRzKSk7XG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/parseDateTime.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/lib/parseExtraField.js":
/*!******************************************************!*\
  !*** ./node_modules/unzipper/lib/parseExtraField.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var binary = __webpack_require__(/*! binary */ \"(ssr)/./node_modules/binary/index.js\");\n\nmodule.exports = function(extraField, vars) {\n  var extra;\n  // Find the ZIP64 header, if present.\n  while(!extra && extraField && extraField.length) {\n    var candidateExtra = binary.parse(extraField)\n      .word16lu('signature')\n      .word16lu('partsize')\n      .word64lu('uncompressedSize')\n      .word64lu('compressedSize')\n      .word64lu('offset')\n      .word64lu('disknum')\n      .vars;\n\n    if(candidateExtra.signature === 0x0001) {\n      extra = candidateExtra;\n    } else {\n      // Advance the buffer to the next part.\n      // The total size of this part is the 4 byte header + partsize.\n      extraField = extraField.slice(candidateExtra.partsize + 4);\n    }\n  }\n\n  extra = extra || {};\n\n  if (vars.compressedSize === 0xffffffff)\n    vars.compressedSize = extra.compressedSize;\n\n  if (vars.uncompressedSize  === 0xffffffff)\n    vars.uncompressedSize= extra.uncompressedSize;\n\n  if (vars.offsetToLocalFileHeader === 0xffffffff)\n    vars.offsetToLocalFileHeader= extra.offset;\n\n  return extra;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/parseExtraField.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/lib/parseOne.js":
/*!***********************************************!*\
  !*** ./node_modules/unzipper/lib/parseOne.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var Stream = __webpack_require__(/*! stream */ \"stream\");\nvar Parse = __webpack_require__(/*! ./parse */ \"(ssr)/./node_modules/unzipper/lib/parse.js\");\nvar duplexer2 = __webpack_require__(/*! duplexer2 */ \"(ssr)/./node_modules/duplexer2/index.js\");\nvar BufferStream = __webpack_require__(/*! ./BufferStream */ \"(ssr)/./node_modules/unzipper/lib/BufferStream.js\");\n\n// Backwards compatibility for node versions < 8\nif (!Stream.Writable || !Stream.Writable.prototype.destroy)\n  Stream = __webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\");\n\nfunction parseOne(match,opts) {\n  var inStream = Stream.PassThrough({objectMode:true});\n  var outStream = Stream.PassThrough();\n  var transform = Stream.Transform({objectMode:true});\n  var re = match instanceof RegExp ? match : (match && new RegExp(match));\n  var found;\n\n  transform._transform = function(entry,e,cb) {\n    if (found || (re && !re.exec(entry.path))) {\n      entry.autodrain();\n      return cb();\n    } else {\n      found = true;\n      out.emit('entry',entry);\n      entry.on('error',function(e) {\n        outStream.emit('error',e);\n      });\n      entry.pipe(outStream)\n        .on('error',function(err) {\n          cb(err);\n        })\n        .on('finish',function(d) {\n          cb(null,d);\n        });\n    }\n  };\n\n  inStream.pipe(Parse(opts))\n    .on('error',function(err) {\n      outStream.emit('error',err);\n    })\n    .pipe(transform)\n    .on('error',Object)  // Silence error as its already addressed in transform\n    .on('finish',function() {\n      if (!found)\n        outStream.emit('error',new Error('PATTERN_NOT_FOUND'));\n      else\n        outStream.end();\n    });\n\n  var out = duplexer2(inStream,outStream);\n  out.buffer = function() {\n    return BufferStream(outStream);\n  };\n\n  return out;\n}\n\nmodule.exports = parseOne;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/lib/parseOne.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unzipper/unzip.js":
/*!****************************************!*\
  !*** ./node_modules/unzipper/unzip.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// Polyfills for node 0.8\n__webpack_require__(/*! listenercount */ \"(ssr)/./node_modules/listenercount/index.js\");\n__webpack_require__(/*! buffer-indexof-polyfill */ \"(ssr)/./node_modules/buffer-indexof-polyfill/index.js\");\n__webpack_require__(/*! setimmediate */ \"(ssr)/./node_modules/next/dist/compiled/setimmediate/setImmediate.js\");\n\n\nexports.Parse = __webpack_require__(/*! ./lib/parse */ \"(ssr)/./node_modules/unzipper/lib/parse.js\");\nexports.ParseOne = __webpack_require__(/*! ./lib/parseOne */ \"(ssr)/./node_modules/unzipper/lib/parseOne.js\");\nexports.Extract = __webpack_require__(/*! ./lib/extract */ \"(ssr)/./node_modules/unzipper/lib/extract.js\");\nexports.Open = __webpack_require__(/*! ./lib/Open */ \"(ssr)/./node_modules/unzipper/lib/Open/index.js\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW56aXBwZXIvdW56aXAuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLG1CQUFPLENBQUMsa0VBQWU7QUFDdkIsbUJBQU8sQ0FBQyxzRkFBeUI7QUFDakMsbUJBQU8sQ0FBQywwRkFBYzs7O0FBR3RCLG9HQUFzQztBQUN0Qyw2R0FBNEM7QUFDNUMsMEdBQTBDO0FBQzFDLHVHQUFvQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3VuemlwcGVyL3VuemlwLmpzPzk5YjQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuLy8gUG9seWZpbGxzIGZvciBub2RlIDAuOFxucmVxdWlyZSgnbGlzdGVuZXJjb3VudCcpO1xucmVxdWlyZSgnYnVmZmVyLWluZGV4b2YtcG9seWZpbGwnKTtcbnJlcXVpcmUoJ3NldGltbWVkaWF0ZScpO1xuXG5cbmV4cG9ydHMuUGFyc2UgPSByZXF1aXJlKCcuL2xpYi9wYXJzZScpO1xuZXhwb3J0cy5QYXJzZU9uZSA9IHJlcXVpcmUoJy4vbGliL3BhcnNlT25lJyk7XG5leHBvcnRzLkV4dHJhY3QgPSByZXF1aXJlKCcuL2xpYi9leHRyYWN0Jyk7XG5leHBvcnRzLk9wZW4gPSByZXF1aXJlKCcuL2xpYi9PcGVuJyk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unzipper/unzip.js\n");

/***/ })

};
;