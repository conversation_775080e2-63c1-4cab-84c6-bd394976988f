/**
 * Knowledge Base Type Definitions
 * Consolidated types for spaces, documents, and knowledge management
 */

// =============================================================================
// UNIQUE TYPES (No merge required)
// =============================================================================

// Note: User interface is exported from types/index.ts (already exists with complete fields)

/**
 * Document indexing response
 * Source: KnowledgeBaseSidebar.tsx (lines 86-93)
 */
export interface IndexingResponse {
  success: boolean;
  message: string;
  indexed_count: number;
  failed_count: number;
  collection_name: string;
  total_processing_time?: number;
}

/**
 * File information for document selection
 * Source: FileSelector.tsx (lines 7-14)
 */
export interface FileInfo {
  filename: string;
  indexed: boolean;
  title: string;
  space_id?: number;
  space_name?: string;
  id: number;
}

/**
 * Space membership information
 * Source: lib/api.ts (lines 677-686)
 */
export interface SpaceMembership {
  id: number;
  user_id: number;
  space_id: number;
  role: 'admin' | 'member';
  created_at: string;
  created_by: number;
  user_username: string | null;
  added_by_username: string | null;
}

/**
 * Request payload for creating a new space
 * Source: lib/api.ts (lines 688-693)
 * Updated: Added playground_type for Image Space Support
 */
export interface CreateSpaceRequest {
  name: string;
  description?: string;
  space_type: 'personal' | 'shared';
  playground_type?: 'documents' | 'images'; // NEW: Playground type (immutable after creation)
  create_group?: boolean;
}

/**
 * Basic user information (for member selection)
 * Source: lib/api.ts (lines 788-794)
 */
export interface BasicUser {
  user_id: number;
  username: string;
  industry?: string | null;
  is_online?: boolean;
  last_seen?: string | null;
}

// =============================================================================
// MERGED TYPES (Phase 2 - Complete)
// =============================================================================

/**
 * User space (workspace) information
 * Sources: KnowledgeBaseSidebar.tsx + lib/api.ts (identical versions)
 */
export interface UserSpace {
  id: number;
  name: string;
  description: string | null;
  space_type: 'personal' | 'shared';
  playground_type: 'documents' | 'images'; // NEW: Playground type for content categorization
  created_at: string;
  updated_at: string | null;
  is_active: boolean;
  owner_id: number;
  owner_username: string | null;
  collection_name: string;
  document_count: number;
  member_count: number;
  is_shared: boolean;
  is_personal: boolean;
  user_role?: string;
  linked_group_id?: number | null;
}

/**
 * Knowledge document with complete field set
 * Merged from KnowledgeBaseSidebar.tsx (space-based) + lib/api.ts (department-based)
 * Supports both legacy department-based and new space-based APIs
 */
export interface KnowledgeDocument {
  // Core fields
  id: number;
  title: string;
  description: string | null;
  
  // Context fields (supports both old and new API)
  department?: string;               // Legacy department-based API (optional)
  space_id: number;                  // New space-based API
  space_name: string | null;         // New space-based API
  
  // Document properties
  document_type: string;
  filename: string | null;
  
  // Timestamps
  created_at: string;
  updated_at: string | null;         // Nullable for flexibility
  
  // Creator info
  created_by: number;
  creator_username: string | null;   // Nullable for flexibility
  
  // Content flags
  has_file: boolean;
  has_content: boolean;
  content: string | null;            // Explicitly nullable
  
  // Indexing
  indexed: boolean;
  indexed_at: string | null;
  collection_name: string | null;    // Vector DB collection
  
  // File storage (MinIO)
  file_url?: string;                 // Direct file URL (optional)
  minio_bucket?: string;             // MinIO bucket name (optional)
  minio_object_key?: string;         // MinIO object key (optional)
}

/**
 * Bulk upload response with duplicate handling support
 * Merged from KnowledgeBaseSidebar.tsx + lib/api.ts + actual usage patterns
 * Includes fields for duplicate detection and state management
 */
export interface BulkUploadResponse {
  success: boolean;
  uploaded_count: number;
  failed_count: number;
  uploaded_documents: KnowledgeDocument[];  // Properly typed (not any[])
  failed_uploads: Array<{
    filename: string;
    reason: string;
  }>;
  
  // Duplicate handling fields (used in KB duplicate detection flow)
  duplicate_files?: any[];           // List of duplicate files detected by backend
  was_duplicate_upload?: boolean;    // Flag set after user handles duplicates
}

