from sqlalchemy import Column, Integer, String, Text, DateTime, Foreign<PERSON>ey, Boolean, BigInteger
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

class KnowledgeDocument(Base):
    __tablename__ = "knowledge_documents"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=True)
    space_id = Column(Integer, ForeignKey('user_spaces.id'), nullable=False, index=True)
    document_type = Column(String(20), nullable=False)  # 'pdf', 'text', 'docx'
    content = Column(Text, nullable=True)  # For text documents
    filename = Column(String(255), nullable=True)
    
    # MinIO storage fields (replacing file_data LargeBinary)
    minio_bucket = Column(String(100), nullable=True)
    minio_object_key = Column(String(500), nullable=True)
    file_size = Column(BigInteger, nullable=True)
    content_type = Column(String(100), nullable=True)
    file_url = Column(Text, nullable=True)  # Cached URL for quick access
    
    indexed = Column(Boolean, default=False, nullable=False)  # Track indexing status
    indexed_at = Column(DateTime, nullable=True)  # When document was indexed
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign key to user who uploaded the document
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
    space = relationship("UserSpace", back_populates="documents")
    
    def mark_as_indexed(self):
        """Mark document as indexed"""
        self.indexed = True
        self.indexed_at = datetime.utcnow()
    
    def to_dict(self, include_content: bool = False) -> dict:
        """Convert knowledge document to dictionary"""
        data = {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'space_id': self.space_id,
            'space_name': self.space.name if self.space else None,
            'document_type': self.document_type,
            'filename': self.filename,
            'minio_bucket': self.minio_bucket,
            'minio_object_key': self.minio_object_key,
            'file_size': self.file_size,
            'content_type': self.content_type,
            'file_url': self.file_url,
            'has_file': bool(self.minio_bucket and self.minio_object_key),
            'has_content': bool(self.content and self.content.strip()),
            'indexed': self.indexed,
            'indexed_at': self.indexed_at.isoformat() if self.indexed_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'creator_username': self.creator.username if self.creator else None,
            'collection_name': self.space.collection_name if self.space else None
        }

        # Only include the full content if explicitly requested
        if include_content:
            data['content'] = self.content

        return data
    
    def __repr__(self):
        return f'<KnowledgeDocument {self.title}>' 