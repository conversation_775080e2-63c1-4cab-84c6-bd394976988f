import React from 'react';

export interface FAQItem {
  id: string;
  question: string;
  answer: string | React.ReactNode;
  category: string;
}
  
  export const categories = [
    { id: 'general', name: 'General' },
    { id: 'user', name: 'User Guide' },
    { id: 'slm', name: 'SL<PERSON>' }
  ];
  
export const faqData: FAQItem[] = [
  // Exact Q&A provided
  { id: 'q1', category: 'general', question: `What is Workplace SLM?`, answer: `Workplace SLM is an enterprise AI copilot that helps organizations manage documents, perform intelligent searches, collaborate in real time, and get AI-powered assistance across different industries.` },
  { id: 'q2', category: 'slm', question: `What is an SLM (Small Language Model)?`, answer: `An SLM is a lightweight AI model optimized for speed, cost, and private/on‑prem deployment, ideal for focused, task‑specific workflows.` },
  { id: 'q3', category: 'general', question: `Who can use Workplace SLM?`, answer: `It is suitable for companies across industries like legal, finance, technology, consulting, and healthcare.` },
  { id: 'q4', category: 'general', question: `Is Workplace SLM available as a web or mobile app?`, answer: `Currently it is accessible via a web application. Mobile app support is planned for future releases.` },
  { id: 'q5', category: 'general', question: `What problems does Workplace SLM solve for organizations?`, answer: `It centralizes institutional knowledge, improves productivity, enables quick information retrieval, and enhances team communication.` },
  { id: 'q6', category: 'general', question: `Does Workplace SLM support multiple languages?`, answer: `Currently, Workplace SLM is optimized for English. Multi-language support is planned in future versions.` },
  { id: 'q7', category: 'slm', question: `How is an SLM different from an LLM?`, answer: `SLMs are smaller, faster, cheaper, and easier to run privately; LLMs are larger, more general, and often more accurate on open‑ended tasks.` },
  { id: 'q8', category: 'user', question: `What happens after registration?`, answer: `Once approved, you’ll gain access to your personal workspace where you can upload documents and start chatting with Workplace SLM.` },
  { id: 'q9', category: 'user', question: `What file formats can I upload?`, answer: `Currently, PDF, DOCX, and TXT files are supported.` },
  { id: 'q10', category: 'user', question: `Is there a file size limit for uploads?`, answer: `Yes, each file must be under 10 MB.` },
  { id: 'q11', category: 'user', question: `Can I organize my documents?`, answer: `Yes, documents are organized within your personal "Spaces" for better management.` },
  { id: 'q12', category: 'user', question: `How do to document search?`, answer: `Simply type a query in the chat and enable Document Search. Workplace SLM will retrieve the most relevant content from your knowledge base.` },
  { id: 'q13', category: 'user', question: `how many documents I can select in document search?`, answer: `There’s no hard limit select multiple at once according to your use case.` },
  { id: 'q14', category: 'user', question: `How many spaces can I create for organizing documents?`, answer: `There is no explicit limit--create as many spaces as you need` },
  { id: 'q15', category: 'user', question: `How does Deep Search work?`, answer: `Deep Search uses multiple specialized AI agents to perform in-depth research across documents and the web, returning a more comprehensive answer.` },
  { id: 'q16', category: 'user', question: `Can I chat with colleagues inside Workplace SLM?`, answer: `Yes. You can start 1-on-1 chats or group conversations with other approved users in your organization.` },
  { id: 'q17', category: 'user', question: `Can I cancel a query in progress?`, answer: `Yes, Workplace SLM supports query cancellation.` },
  { id: 'q18', category: 'user', question: `How many follow-up questions does Workplace SLM generate?`, answer: `Workplace SLM automatically generates 3 - 5 contextual follow-up questions after each response.` },
  { id: 'q19', category: 'user', question: `Can I share AI responses with others?`, answer: `Yes, you can create shareable links for AI responses which others can access.` },
  { id: 'q20', category: 'user', question: `Can I take notes while chatting?`, answer: `Yes, Workplace SLM includes a notes feature that lets you capture important information during your chat sessions.` },
  { id: 'q21', category: 'user', question: `How do I provide feedback on AI responses?`, answer: `Each AI response includes a feedback widget where you can rate helpfulness and provide comments to improve the system.` },
  { id: 'q22', category: 'user', question: `Can Workplace SLM handle scanned PDFs or images?`, answer: `Basic text-based files are supported today. Advanced OCR capabilities for scanned documents and images are planned in future updates.` },
  { id: 'q23', category: 'user', question: `Can I upload multiple files at once?`, answer: `Yes, bulk upload is supported you can upload several documents together.` },
  { id: 'q24', category: 'user', question: `Can I use Document Search and Deep Search together?`, answer: `No, these modes are mutually exclusive, you can only enable one per query.` },
  { id: 'q25', category: 'user', question: `Can I download documents I uploaded?`, answer: `Yes, documents stored in your space can be downloaded anytime.` },
  { id: 'q26', category: 'user', question: `Can I see past queries?`, answer: `Yes, you can view chat history by selecting a past session in the Chat sidebar.` },
  { id: 'q27', category: 'user', question: `How do I preview my documents?`, answer: `You can preview PDFs and DOCX files directly in the Knowledge Base sidebar.` },
  { id: 'q28', category: 'user', question: `Can I delete documents or spaces?`, answer: `Yes, you can delete individual documents or entire spaces when no longer needed.` },
  { id: 'q29', category: 'general', question: `What’s the difference between Document Search, Deep Search, and Web Search?`, answer: `Document Search retrieves from your uploaded documents; Deep Search orchestrates multiple specialized agents (news, scientific, URL, verification) for multi-step research; Web Search queries the web. Only one mode can be enabled per query.` },
  { id: 'q30', category: 'general', question: `Does Workplace SLM remember conversation context?`, answer: `Yes. It summarizes long chats and uses either history or a session summary to keep context concise.` },
  { id: 'q31', category: 'general', question: `Why use Workplace SLM instead of a generic chatbot?`, answer: `It’s purpose‑built for companies: private document search with citations, role‑based access, secure file handling, and team features (chat, notes, sharing, analytics).` },
  { id: 'q32', category: 'general', question: `What are the top benefits of using Workplace SLM?`, answer: `Faster answers from your own knowledge, trusted citations, secure data handling, multi‑agent deep research, collaboration features, and usage analytics.` },
  { id: 'q33', category: 'general', question: `Do answers include citations?`, answer: `Yes web/deep searches return titles, URLs, and snippets; document search returns file names and text excerpts.` },
  { id: 'q34', category: 'general', question: `How do I get started quickly?`, answer: `Register and await approval, upload documents to your Space, index them, then ask questions with Document Search (or use Web/Deep Search as needed).` },
  { id: 'q35', category: 'user', question: `How do I create a support ticket?`, answer: `Open the Tickets section in Dashboard and submit a title, description, category, and priority. Any authenticated user can create tickets.` },
  { id: 'q36', category: 'user', question: `Who can assign or change ticket status?`, answer: `Admins can assign tickets and update status. Creators can edit their own ticket’s basic info (title/description/category/priority).` },
  { id: 'q37', category: 'slm', question: `When should I use an SLM vs an LLM?`, answer: `Use SLMs for private, low‑latency, cost‑sensitive, or on‑prem tasks; use LLMs for broad, complex reasoning or creative tasks.` },
];