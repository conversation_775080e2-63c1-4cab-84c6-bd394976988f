from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_
from datetime import datetime
from ..database import get_db
from ..models.user import User
from ..schemas.user import User<PERSON><PERSON>, User<PERSON>ogin, UserResponse, Token, UserUpdate, PasswordChange
from ..auth import create_access_token, get_current_user, require_admin
from .. import config as settings
from fastapi.security import OAuth2<PERSON><PERSON>word<PERSON>earer, OAuth2PasswordRequestForm
from ..services.cache_optimizer import cache_optimizer
from ..services.redis_service import redis_service, invalidate_admin_cache, invalidate_account_requests_cache
import json
import logging

logger = logging.getLogger(__name__)

# Minimum password length (configured in settings)
MIN_PASSWORD_LENGTH = settings.MIN_PASSWORD_LENGTH

router = APIRouter(prefix="/auth", tags=["Authentication"])

@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Register a new user
    """
    # Check if username already exists
    result = await db.execute(
        select(User).where(
            or_(User.username == user_data.username, User.email == user_data.email)
        )
    )
    existing_user = result.scalars().first()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username or email already registered"
        )
    
    # Validate password length
    if len(user_data.password) < MIN_PASSWORD_LENGTH:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Password must be at least {MIN_PASSWORD_LENGTH} characters long"
        )
    
    # Create new user
    new_user = User(
        username=user_data.username,
        email=user_data.email,
        industry=(user_data.industry.strip() if user_data.industry and user_data.industry.strip() else None),
        role=user_data.role,
        status='pending'
    )
    new_user.set_password(user_data.password)
    
    db.add(new_user)
    await db.commit()
    await db.refresh(new_user)
    
    # Invalidate account requests cache so new requests appear instantly
    logger.info(f"New user registered: {new_user.username} (ID: {new_user.id}), invalidating account requests cache")
    deleted_entries = await invalidate_account_requests_cache()
    logger.info(f"Account requests cache invalidation completed, deleted {deleted_entries} entries")
    
    return new_user

@router.post("/admin-create-user", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def admin_create_user(
    user_data: UserCreate,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    Admin endpoint to create new users directly with approved status
    """
    # Check if username already exists
    result = await db.execute(
        select(User).where(
            or_(User.username == user_data.username, User.email == user_data.email)
        )
    )
    existing_user = result.scalars().first()

    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username or email already registered"
        )

    # Validate password length
    if len(user_data.password) < MIN_PASSWORD_LENGTH:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Password must be at least {MIN_PASSWORD_LENGTH} characters long"
        )

    # Create new user with approved status
    new_user = User(
        username=user_data.username,
        email=user_data.email,
        industry=(user_data.industry.strip() if user_data.industry and user_data.industry.strip() else None),
        role=user_data.role,
        status='approved'  # Admin-created users are automatically approved
    )
    new_user.set_password(user_data.password)

    db.add(new_user)
    await db.commit()
    await db.refresh(new_user)

    return new_user

async def _authenticate_user(username: str, password: str, db: AsyncSession):
    """Helper function to authenticate user"""
    # Try to find user by username or email
    result = await db.execute(
        select(User).where(
            or_(User.username == username, User.email == username)
        )
    )
    user = result.scalars().first()
    
    if not user or not user.verify_password(password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username/email or password"
        )
        
    # Check if user account is approved
    if not user.is_approved():
        if user.is_pending():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Account is pending approval"
            )
        else:  # rejected
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Account access has been denied"
            )

    # Generate access token with both username and user_id
    access_token = create_access_token(
        data={
            "sub": user.username,
            "user_id": user.id,
            "role": user.role,
            "industry": user.industry
        }
    )
    
    # Cache user session
    session_key = f"{settings.CACHE_PREFIX_USER}:session:{user.id}"
    session_data = {
        "user_id": user.id,
        "username": user.username,
        "role": user.role,
        "industry": user.industry,
        "login_time": datetime.utcnow().isoformat(),
        "token": access_token
    }
    await redis_service.set(session_key, session_data, ttl=settings.CACHE_TTL_USER_SESSION)
    
    # Warm cache for user
    await cache_optimizer.warm_cache_for_user(user.id)

    # Prepare user data
    user_data = {
        "id": user.id,
        "email": user.email,
        "username": user.username,
        "role": user.role,
        "industry": user.industry
    }

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": user_data
    }

@router.post("/login")
async def login_json(
    user_login: UserLogin,
    db: AsyncSession = Depends(get_db)
):
    """Login endpoint for JSON data"""
    try:
        return await _authenticate_user(user_login.username, user_login.password, db)
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/token")
async def login_form(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """Login endpoint for form data (OAuth2 compatible)"""
    try:
        return await _authenticate_user(form_data.username, form_data.password, db)
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/me")
async def get_current_user(current_user: User = Depends(get_current_user)):
    try:
       
        # If not in cache, prepare user data
        user_data = {
            "id": current_user.id,
            "email": current_user.email,
            "username": current_user.username,
            "role": current_user.role,
            "industry": current_user.industry
        }

        # Cache the user data
        await redis_service.set(f"{settings.CACHE_PREFIX_USER}:profile:{current_user.id}", user_data, ttl=settings.CACHE_TTL_USER_SESSION)

        return user_data

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update current user information
    """
    # Ensure we have a SQLAlchemy instance (may be dict if taken from Redis cache)
    if isinstance(current_user, dict):
        result = await db.execute(select(User).where(User.id == current_user["id"]))
        current_user = result.scalars().first()
        if current_user is None:
            raise HTTPException(status_code=404, detail="User not found in DB")

    # Update user fields
    for field, value in user_update.dict(exclude_unset=True).items():
        if field == "role" and not current_user.is_admin():
            # Only admins can change roles
            continue
        setattr(current_user, field, value)
    
    await db.commit()
    await db.refresh(current_user)
    return current_user

@router.put("/me/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Change current user's password
    """
    # Ensure we have a SQLAlchemy instance (may be dict if taken from Redis cache)
    if isinstance(current_user, dict):
        result = await db.execute(select(User).where(User.id == current_user["id"]))
        current_user = result.scalars().first()
        if current_user is None:
            raise HTTPException(status_code=404, detail="User not found in DB")

    # Verify current password
    if not current_user.verify_password(password_data.current_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )

    # Check if new passwords match
    if password_data.new_password != password_data.confirm_password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="New password and confirmation password do not match"
        )

    # Validate password length
    if len(password_data.new_password) < MIN_PASSWORD_LENGTH:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"New password must be at least {MIN_PASSWORD_LENGTH} characters long"
        )

    # Check if new password is different from current
    if current_user.verify_password(password_data.new_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="New password must be different from current password"
        )

    # Update password
    current_user.set_password(password_data.new_password)
    await db.commit()

    # Clear user session from Redis to force re-login
    session_key = f"{settings.CACHE_PREFIX_USER}:session:{current_user.id}"
    await redis_service.delete(session_key)

    # Clear user-specific cache
    await redis_service.delete_pattern(f"{settings.CACHE_PREFIX_USER}:{current_user.id}:*")
    await redis_service.delete_pattern(f"{settings.CACHE_PREFIX_CHAT}:*:{current_user.id}:*")

    # Clear WebSocket session if exists
    from ..services.websocket_session_cache import ws_session_cache
    await ws_session_cache.delete_session(current_user.id)

    return {"message": "Password changed successfully. Please login again."}

@router.post("/logout")
async def logout(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Logout current user and clear session cache
    """
    # Clear user session from Redis
    session_key = f"{settings.CACHE_PREFIX_USER}:session:{current_user.id}"
    await redis_service.delete(session_key)
    
    # Clear user-specific cache
    await redis_service.delete_pattern(f"{settings.CACHE_PREFIX_USER}:{current_user.id}:*")
    await redis_service.delete_pattern(f"{settings.CACHE_PREFIX_CHAT}:*:{current_user.id}:*")
    
    # Clear WebSocket session if exists
    from ..services.websocket_session_cache import ws_session_cache
    await ws_session_cache.delete_session(current_user.id)
    
    return {"message": "Logged out successfully"}

@router.get("/users", response_model=list[UserResponse])
async def list_users(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    List all users (admin only)
    """
    result = await db.execute(select(User).offset(skip).limit(limit))
    users = result.scalars().all()
    return users

@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    Get user by ID (admin only)
    """
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return user

@router.put("/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    Update user by ID (admin only)
    """
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Update user fields
    for field, value in user_update.dict(exclude_unset=True).items():
        setattr(user, field, value)
    
    await db.commit()
    await db.refresh(user)
    return user 