from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, BigInteger
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

class Document(Base):
    __tablename__ = "documents"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    file_name = Column(String(255), nullable=False)  # Original filename
    space_id = Column(Integer, ForeignKey('user_spaces.id'), nullable=True, index=True)  # Space for the document (optional)
    views = Column(Integer, default=0)  # Number of views
    downloads = Column(Integer, default=0)  # Number of downloads
    
    # MinIO storage fields (replacing file_path)
    minio_bucket = Column(String(100), nullable=False)
    minio_object_key = Column(String(500), nullable=False)
    file_size = Column(BigInteger, nullable=True)  # File size in bytes
    content_type = Column(String(100), nullable=True)  # MIME type
    file_url = Column(Text, nullable=True)  # Cached URL for quick access
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign key to user
    uploaded_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # Relationships
    uploader = relationship("User", back_populates="documents")
    space = relationship("UserSpace")
    
    def to_dict(self) -> dict:
        """Convert document to dictionary"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'file_name': self.file_name,
            'space_id': self.space_id,
            'space_name': self.space.name if self.space else None,
            'views': self.views,
            'downloads': self.downloads,
            'minio_bucket': self.minio_bucket,
            'minio_object_key': self.minio_object_key,
            'file_size': self.file_size,
            'content_type': self.content_type,
            'file_url': self.file_url,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'uploaded_by': self.uploaded_by,
            'uploader_username': self.uploader.username if self.uploader else None
        }
    
    def __repr__(self):
        return f'<Document {self.title}>'
    
    @classmethod
    async def increment_views(cls, db, document_id: int):
        """Increment view count for a document (async)."""
        from sqlalchemy import update
        from sqlalchemy.ext.asyncio import AsyncSession
        if isinstance(db, AsyncSession):
            await db.execute(
                update(cls).where(cls.id == document_id).values(views=cls.views + 1)
            )
            await db.commit()
        else:
            document = db.query(cls).filter(cls.id == document_id).first()
            if document:
                document.views += 1
                db.commit()
    
    @classmethod
    async def increment_downloads(cls, db, document_id: int):
        """Increment download count for a document (async)."""
        from sqlalchemy import update
        from sqlalchemy.ext.asyncio import AsyncSession
        if isinstance(db, AsyncSession):
            await db.execute(
                update(cls).where(cls.id == document_id).values(downloads=cls.downloads + 1)
            )
            await db.commit()
        else:
            document = db.query(cls).filter(cls.id == document_id).first()
            if document:
                document.downloads += 1
                db.commit()