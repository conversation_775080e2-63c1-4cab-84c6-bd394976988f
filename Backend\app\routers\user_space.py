from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from sqlalchemy import select, or_, func, update, delete
from typing import List, Optional

from ..database import get_db
from ..models.user_space import UserSpace, SpaceType, PlaygroundType
from ..models.space_membership import SpaceMembership, SpaceRole
from ..models.user import User
from ..models.group_chat import Group, GroupMember, MemberRole
from ..schemas.user_space import (
    UserSpaceCreate, UserSpaceUpdate, UserSpaceResponse, UserSpaceWithMembers,
    SpaceMembershipResponse, AddMembersRequest, RemoveMembersRequest, SpaceTypeEnum,
    SpaceRoleEnum, PlaygroundTypeEnum
)
from ..auth.dependencies import get_current_user, require_admin
from ..services.redis_service import redis_service, invalidate_user_cache
from ..services.mqtt_service_client import mqtt_client
from ..services.space_permissions import SpacePermissions, require_space_access
import logging
from ..config import REDIS_PREFIX_Space

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/spaces", tags=["user-spaces"])


@router.post("/", response_model=UserSpaceResponse)
async def create_space(
    space_data: UserSpaceCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a personal or shared space for the current user.

    - Ensures unique name per owner among active spaces
    - For shared spaces, also creates a membership row marking the owner as admin
    - Optionally creates a linked group chat for shared spaces
    - Invalidates the creator's space list caches
    """
    result = await db.execute(
        select(UserSpace).where(
            UserSpace.owner_id == current_user.id,
            UserSpace.name == space_data.name,
            UserSpace.is_active == True
        )
    )
    existing_space = result.scalars().first()

    if existing_space:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Space with name '{space_data.name}' already exists"
        )

    # Validate create_group flag only applies to shared spaces
    if space_data.create_group and space_data.space_type != SpaceTypeEnum.shared:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Groups can only be created for shared spaces"
        )

    new_space = UserSpace(
        name=space_data.name,
        description=space_data.description,
        space_type=space_data.space_type.value,
        playground_type=space_data.playground_type.value,  # Set playground type (immutable)
        owner_id=current_user.id
    )

    db.add(new_space)
    await db.commit()
    await db.refresh(new_space)
    
    # Eager-load the owner relationship to avoid lazy loading in to_dict()
    result = await db.execute(
        select(UserSpace).options(joinedload(UserSpace.owner)).where(UserSpace.id == new_space.id)
    )
    new_space = result.scalars().first()

    linked_group_id = None
    created_or_linked_group = None

    if new_space.is_shared():
        # Create space membership for owner
        membership = SpaceMembership(
            user_id=current_user.id,
            space_id=new_space.id,
            role=SpaceRole.admin.value,
            created_by=current_user.id
        )
        db.add(membership)

        # Create linked group if requested
        if space_data.create_group:
            # Check if a group with same name already exists for this user
            result = await db.execute(
                select(Group).where(
                    Group.creator_id == current_user.id,
                    Group.name == space_data.name,
                    Group.is_active == True
                )
            )
            existing_group = result.scalars().first()

            if existing_group:
                # If group exists, check if it's already linked to another space
                if existing_group.space_id is not None:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Group '{space_data.name}' already exists and is linked to another space"
                    )
                # Link existing group to this space
                existing_group.space_id = new_space.id
                linked_group_id = existing_group.id
                created_or_linked_group = existing_group
            else:
                # Create new group linked to the space
                new_group = Group(
                    name=space_data.name,
                    creator_id=current_user.id,
                    space_id=new_space.id
                )
                db.add(new_group)
                await db.commit()
                await db.refresh(new_group)
                linked_group_id = new_group.id
                created_or_linked_group = new_group

                # Add creator as admin member of the group
                group_membership = GroupMember(
                    group_id=new_group.id,
                    user_id=current_user.id,
                    role=MemberRole.admin
                )
                db.add(group_membership)

        await db.commit()

    # For newly created spaces, member_count is 0 (memberships not yet loaded)
    # document_count is also 0 for new spaces
    space_dict = new_space.to_dict(
        linked_group_id=linked_group_id,
        document_count=0,
        member_count=0
    )

    # Invalidate the creator's cached group list so sidebar reflects immediately
    try:
        await invalidate_user_cache(current_user.id)
    except Exception:
        pass

    # Optionally push an update to the creator so UI can reflect new group instantly
    if created_or_linked_group is not None:
        try:
            # Load members for consistent payload
            result = await db.execute(
                select(Group).options(joinedload(Group.members).joinedload(GroupMember.user)).where(Group.id == created_or_linked_group.id)
            )
            group_obj = result.scalars().first()
            if group_obj:
                await mqtt_client.publish_event(current_user.id, {
                    "type": "group_update",
                    "group": group_obj.to_dict(include_members=True)
                })
        except Exception:
            pass

    try:
        await redis_service.delete_pattern(
            f"{REDIS_PREFIX_Space}:user:{current_user.id}:type:*"
        )
        await redis_service.delete(f"{REDIS_PREFIX_Space}:user:{current_user.id}")
        logger.info(
            f"UserSpace cache invalidated for user {current_user.id} after create"
        )
    except Exception as e:
        logger.error(f"Redis cache invalidation error in create_space: {e}")

    return space_dict


@router.get("/", response_model=List[UserSpaceResponse])
async def get_user_spaces(
    space_type: Optional[SpaceTypeEnum] = Query(
        None, description="Filter by space type (personal/shared)"
    ),
    playground: Optional[PlaygroundTypeEnum] = Query(
        None, description="Filter by playground type (documents/images)"
    ),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Return all spaces accessible to the current user (owned + memberships).

    Results are cached per-user and filtered to only active spaces. If a cached
    list contains inactive items due to race conditions, it self-heals by
    rewriting the cache with a filtered list.
    
    Now supports filtering by playground_type for Image vs Document spaces.
    """
    cache_key = f"{REDIS_PREFIX_Space}:user:{current_user.id}:type:{space_type or 'all'}:playground:{playground or 'all'}"

    try:
        cached = await redis_service.get(cache_key)
        if cached is not None:
            if isinstance(cached, list):
                filtered = [
                    s for s in cached
                    if s.get("is_active") in (True, 1, "true", "True")
                ]
                if len(filtered) != len(cached):
                    try:
                        await redis_service.set(cache_key, filtered, ttl=3600)
                    except Exception:
                        pass
                logger.info(f"UserSpace cache HIT for user {current_user.id}")
                return filtered
            logger.info(f"UserSpace cache HIT for user {current_user.id}")
            return cached
        else:
            logger.info(f"UserSpace cache MISS for user {current_user.id}")
    except Exception as e:
        logger.error(f"Redis get error in get_user_spaces: {e}")

    spaces = await SpacePermissions.get_accessible_spaces(
        current_user, db, space_type.value if space_type else None
    )

    # Filter by playground_type if specified
    if playground:
        spaces = [s for s in spaces if s.playground_type == playground.value]

    spaces.sort(key=lambda x: x.created_at, reverse=True)

    space_dicts = []
    for space in spaces:
        if not space.is_active:
            continue
        space_dict = space.to_dict()
        space_dict['user_role'] = SpacePermissions.get_user_role_in_space(
            current_user, space, db
        )
        space_dicts.append(space_dict)

    if space_dicts:
        try:
            success = await redis_service.set(cache_key, space_dicts, ttl=3600)
            if success:
                logger.info(
                    f"UserSpace list cached for user {current_user.id} (ttl 3600s)"
                )
        except Exception as e:
            logger.error(f"Redis set error in get_user_spaces: {e}")

    return space_dicts


@router.get("/{space_id}", response_model=UserSpaceWithMembers)
async def get_space(
    space_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Return a single space plus members (for shared spaces).

    Access is validated with space-scoped permissions. Members are eagerly
    loaded for richer UX in the Manage Members dialog.
    """
    space = await require_space_access(current_user, space_id, db, "view")

    # Get linked group ID for shared spaces
    linked_group_id = None
    if space.is_shared():
        linked_group_id = await space.get_linked_group_id(db)

    space_dict = space.to_dict(linked_group_id=linked_group_id)
    space_dict['user_role'] = SpacePermissions.get_user_role_in_space(
        current_user, space, db
    )

    members = []
    if space.is_shared():
        result = await db.execute(
            select(SpaceMembership).options(
                joinedload(SpaceMembership.user), joinedload(SpaceMembership.added_by)
            ).where(SpaceMembership.space_id == space_id)
        )
        memberships = result.scalars().unique().all()

        members = [membership.to_dict() for membership in memberships]

    return UserSpaceWithMembers(**space_dict, members=members)


@router.put("/{space_id}", response_model=UserSpaceResponse)
async def update_space(
    space_id: int,
    space_update: UserSpaceUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update name/description/active flag for a space (owner only)."""
    space = await require_space_access(current_user, space_id, db, "edit")

    # Defensive guard: prevent playground_type mutation attempts via unknown fields
    # UserSpaceUpdate has extra='forbid', but double-check in case of future changes
    if hasattr(space_update, 'playground_type') and space_update.playground_type is not None:
        from fastapi import HTTPException
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="playground_type is immutable and cannot be changed after creation"
        )

    if space_update.name and space_update.name != space.name:
        result = await db.execute(
            select(UserSpace).where(
                UserSpace.owner_id == space.owner_id,
                UserSpace.name == space_update.name,
                UserSpace.is_active == True,
                UserSpace.id != space_id
            )
        )
        existing_space = result.scalars().first()

        if existing_space:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Space with name '{space_update.name}' already exists"
            )

    if space_update.name is not None:
        space.name = space_update.name
    if space_update.description is not None:
        space.description = space_update.description
    if space_update.is_active is not None:
        space.is_active = space_update.is_active

    await db.commit()
    await db.refresh(space)

    # Invalidate caches so name/description changes reflect immediately
    try:
        await redis_service.delete_pattern(
            f"{REDIS_PREFIX_Space}:user:{current_user.id}:type:*"
        )
        await redis_service.delete(f"{REDIS_PREFIX_Space}:user:{current_user.id}")
        # Invalidate member caches for shared spaces as well
        if space.is_shared():
            result = await db.execute(
                select(SpaceMembership).where(
                    SpaceMembership.space_id == space_id
                )
            )
            memberships = result.scalars().all()
            member_ids = {m.user_id for m in memberships if m.user_id != current_user.id}
            for member_id in member_ids:
                await redis_service.delete_pattern(
                    f"{REDIS_PREFIX_Space}:user:{member_id}:type:*"
                )
                await redis_service.delete(f"{REDIS_PREFIX_Space}:user:{member_id}")
    except Exception as e:
        logger.error(f"Redis cache invalidation error in update_space: {e}")

    # Get linked group ID for shared spaces
    linked_group_id = None
    if space.is_shared():
        linked_group_id = await space.get_linked_group_id(db)

    return space.to_dict(linked_group_id=linked_group_id)


@router.delete("/{space_id}")
async def delete_space(
    space_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Soft-delete a space (marks inactive) and invalidate related caches."""
    space = await require_space_access(current_user, space_id, db, "delete")

    space.is_active = False
    await db.commit()

    cache_key = f"{REDIS_PREFIX_Space}:user:{current_user.id}"
    space_cache_key = f"{REDIS_PREFIX_Space}:space:{space_id}"

    try:
        await redis_service.delete(space_cache_key)

        existing = await redis_service.get(cache_key, default=[])
        if isinstance(existing, list):
            filtered = [s for s in existing if s.get("id") != space_id]
            await redis_service.set(cache_key, filtered, ttl=3600)
            logger.info(
                f"UserSpace cache updated after delete for user {current_user.id} (size={len(filtered)})"
            )
        await redis_service.delete_pattern(
            f"{REDIS_PREFIX_Space}:user:{current_user.id}:type:*"
        )

        if space.is_shared():
            try:
                result = await db.execute(
                    select(SpaceMembership).where(
                        SpaceMembership.space_id == space_id
                    )
                )
                memberships = result.scalars().all()
                member_ids = {
                    m.user_id
                    for m in memberships if m.user_id != current_user.id
                }
                for member_id in member_ids:
                    await redis_service.delete_pattern(
                        f"{REDIS_PREFIX_Space}:user:{member_id}:type:*"
                    )
                    await redis_service.delete(f"{REDIS_PREFIX_Space}:user:{member_id}")
            except Exception as e:
                logger.error(f"Redis member cache update error in delete_space: {e}")
    except Exception as e:
        logger.error(f"Redis cache update error in delete_space: {e}")

    return {"message": "Space deleted successfully"}


@router.post("/{space_id}/members", response_model=List[SpaceMembershipResponse])
async def add_members_to_space(
    space_id: int,
    request: AddMembersRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Add multiple users to a shared space with a role (admin/member).

    Skips duplicates and returns the list of successfully added memberships.
    Also adds members to linked group if one exists.
    """
    space = await require_space_access(current_user, space_id, db, "manage_members")

    if not space.is_shared():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only add members to shared spaces"
        )

    # Find linked group if exists
    result = await db.execute(
        select(Group).where(
            Group.space_id == space_id,
            Group.is_active == True
        )
    )
    linked_group = result.scalars().first()

    added_memberships = []
    errors = []

    for user_id in request.user_ids:
        try:
            result = await db.execute(select(User).where(User.id == user_id))
            target_user = result.scalars().first()
            if not target_user:
                errors.append(f"User with ID {user_id} not found")
                continue

            result = await db.execute(
                select(SpaceMembership).where(
                    SpaceMembership.space_id == space_id,
                    SpaceMembership.user_id == user_id
                )
            )
            existing_membership = result.scalars().first()

            if existing_membership:
                errors.append(f"User {target_user.username} is already a member")
                continue

            # Add to space
            membership = SpaceMembership(
                user_id=user_id,
                space_id=space_id,
                role=request.role.value,
                created_by=current_user.id
            )

            db.add(membership)

            # Add to linked group if exists
            if linked_group:
                result = await db.execute(
                    select(GroupMember).where(
                        GroupMember.group_id == linked_group.id,
                        GroupMember.user_id == user_id
                    )
                )
                existing_group_member = result.scalars().first()

                if not existing_group_member:
                    # Convert space role to group role
                    group_role = MemberRole.admin if request.role == SpaceRoleEnum.admin else MemberRole.member
                    
                    group_membership = GroupMember(
                        group_id=linked_group.id,
                        user_id=user_id,
                        role=group_role
                    )
                    db.add(group_membership)

            await db.commit()
            await db.refresh(membership)

            result = await db.execute(
                select(SpaceMembership).options(
                    joinedload(SpaceMembership.user), joinedload(SpaceMembership.added_by)
                ).where(SpaceMembership.id == membership.id)
            )
            membership = result.scalars().first()

            added_memberships.append(membership.to_dict())

            # Invalidate the added user's group caches so list reloads from DB/Redis
            try:
                await invalidate_user_cache(user_id)
                # Also clear the user's space list caches so Knowledge Base updates
                await redis_service.delete_pattern(f"{REDIS_PREFIX_Space}:user:{user_id}:type:*")
                await redis_service.delete(f"{REDIS_PREFIX_Space}:user:{user_id}")
            except Exception:
                pass

            # Notify the added user about group invitation so their UI updates instantly
            if linked_group:
                try:
                    result = await db.execute(
                        select(Group).options(joinedload(Group.members).joinedload(GroupMember.user)).where(Group.id == linked_group.id)
                    )
                    group_obj = result.scalars().first()
                    if group_obj:
                        await mqtt_client.publish_event(user_id, {
                            "type": "group_invite",
                            "group": group_obj.to_dict(include_members=True),
                            "adder_name": current_user.username
                        })
                except Exception:
                    pass

        except Exception as e:
            errors.append(f"Error adding user {user_id}: {str(e)}")
            await db.rollback()

    if errors:
        logger.warning(f"Errors adding members to space {space_id}: {errors}")

    try:
        await redis_service.delete_pattern(f"{REDIS_PREFIX_Space}:user:*")
    except Exception as e:
        logger.error(f"Cache invalidation error after adding members: {e}")

    return added_memberships


@router.delete("/{space_id}/members", response_model=dict)
async def remove_members_from_space(
    space_id: int,
    request: RemoveMembersRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Remove one or more users from a shared space (cannot remove owner).
    
    Also removes members from linked group if one exists.
    """
    space = await require_space_access(current_user, space_id, db, "manage_members")

    if not space.is_shared():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only remove members from shared spaces"
        )

    # Find linked group if exists
    result = await db.execute(
        select(Group).where(
            Group.space_id == space_id,
            Group.is_active == True
        )
    )
    linked_group = result.scalars().first()

    removed_count = 0
    errors = []

    for user_id in request.user_ids:
        try:
            if user_id == space.owner_id:
                errors.append("Cannot remove space owner from space")
                continue

            result = await db.execute(
                select(SpaceMembership).where(
                    SpaceMembership.space_id == space_id,
                    SpaceMembership.user_id == user_id
                )
            )
            membership = result.scalars().first()

            if not membership:
                errors.append(f"User {user_id} is not a member of this space")
                continue

            # Remove from space
            await db.delete(membership)
            
            # Remove from linked group if exists
            if linked_group:
                result = await db.execute(
                    select(GroupMember).where(
                        GroupMember.group_id == linked_group.id,
                        GroupMember.user_id == user_id
                    )
                )
                group_membership = result.scalars().first()

                if group_membership:
                    await db.delete(group_membership)

            removed_count += 1

            # Invalidate removed user's caches and notify them to drop group from UI
            try:
                await invalidate_user_cache(user_id)
                # Also clear the user's space list caches so the space disappears
                await redis_service.delete_pattern(f"{REDIS_PREFIX_Space}:user:{user_id}:type:*")
                await redis_service.delete(f"{REDIS_PREFIX_Space}:user:{user_id}")
            except Exception:
                pass

            if linked_group:
                try:
                    await mqtt_client.publish_event(user_id, {
                        "type": "group_member_removed",
                        "group_id": linked_group.id,
                        "user_id": user_id
                    })
                except Exception:
                    pass

        except Exception as e:
            errors.append(f"Error removing user {user_id}: {str(e)}")
            await db.rollback()

    if removed_count > 0:
        await db.commit()

    try:
        await redis_service.delete_pattern(f"{REDIS_PREFIX_Space}:user:*")
    except Exception as e:
        logger.error(f"Cache invalidation error after removing members: {e}")

    return {
        "message": f"Removed {removed_count} members from space", "removed_count":
        removed_count, "errors": errors if errors else None
    }


@router.put("/{space_id}/members/{user_id}", response_model=SpaceMembershipResponse)
async def update_member_role(
    space_id: int,
    user_id: int,
    role_update: dict,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Change a member's role within a shared space (owner immutable)."""
    space = await require_space_access(current_user, space_id, db, "manage_members")

    if not space.is_shared():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only update roles in shared spaces"
        )

    if user_id == space.owner_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot change space owner's role"
        )

    result = await db.execute(
        select(SpaceMembership).where(
            SpaceMembership.space_id == space_id,
            SpaceMembership.user_id == user_id
        )
    )
    membership = result.scalars().first()

    if not membership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User is not a member of this space"
        )

    new_role = role_update.get("role")
    if new_role and new_role in [SpaceRole.admin.value, SpaceRole.member.value]:
        membership.role = new_role
        await db.commit()
        await db.refresh(membership)

        result = await db.execute(
            select(SpaceMembership).options(
                joinedload(SpaceMembership.user), joinedload(SpaceMembership.added_by)
            ).where(SpaceMembership.id == membership.id)
        )
        membership = result.scalars().first()

        return membership.to_dict()
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid role specified"
        )
