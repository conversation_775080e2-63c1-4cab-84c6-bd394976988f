"""
Chat Handlers Module

This module contains asynchronous functions to handle various chat-related events
received via MQTT, including 1:1 messages, group messages, edits, deletes, hides,
typing indicators, and read receipts. Each handler performs database operations,
invalidates relevant caches, and returns events to be broadcasted to clients.

Functions:
- handle_message: <PERSON><PERSON> sending a new 1:1 message.
- handle_edit_message: <PERSON><PERSON> editing a 1:1 message.
- handle_delete_message: <PERSON><PERSON> deleting a 1:1 message (soft delete).
- handle_hide_message: <PERSON><PERSON> hiding a 1:1 message for the user (personal delete).
- handle_typing: <PERSON>les typing indicators in 1:1 chats.
- handle_read_receipt: Handles marking messages as read in 1:1 chats.
- handle_group_message: <PERSON>les sending a new group message.
- handle_group_edit_message: <PERSON><PERSON> editing a group message.
- handle_group_delete_message: <PERSON><PERSON> deleting a group message (soft delete).
- handle_group_hide_message: <PERSON><PERSON> hiding a group message for the user (personal delete).

Dependencies:
- SQLAlchemy for database interactions.
- Redis for caching and deduplication.
- MQTT for event publishing.
"""

import json
import logging
import re
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, or_, and_

from ..database import async_session
from ..models.user import User
from ..models.chat import Conversation, Message, UserStatus
from ..models.group_chat import Group, GroupMember, GroupMessage, MemberRole
from ..models.media import Media
from ..models.shared_link import SharedLink
from ..models.deleted_message import DeletedMessage, DeletedGroupMessage
from ..services.redis_service import redis_service, invalidate_group_cache, invalidate_conversation_cache
from .. import config

logger = logging.getLogger(__name__)


class ChatHandlerError(Exception):
    """Custom exception for chat handler errors."""
    pass


async def _check_message_dedupe(client_msg_id: str) -> bool:
    """Check if a message with the given client_msg_id has already been processed to prevent duplicates.

    Uses Redis to store a deduplication key with TTL. If the key is set successfully,
    it means this is the first time processing this message ID.

    Args:
        client_msg_id: Unique client-generated message identifier.

    Returns:
        True if the message is a duplicate (already processed), False otherwise.
    """
    if not client_msg_id:
        return False

    cache_key = f"{config.CACHE_PREFIX_LIVE}:dedupe:{client_msg_id}"

    try:
        # Use SET with NX to only set if key doesn't exist; returns True if set, False if already exists
        result = await redis_service.set(
            cache_key, "processed", ttl=config.MQTT_DEDUPE_TTL, nx=True
        )
        # If result is True, key was set (new message), so not duplicate; else duplicate
        return not result
    except Exception as e:
        logger.error(f"Error checking message deduplication: {e}")
        return False


async def handle_message(user_id: int, payload: Dict[str, Any],
                         mqtt_client) -> List[Dict[str, Any]]:
    """Handle sending a new message in a 1:1 conversation.

    Validates the conversation, creates the message in DB, handles attachments/media,
    extracts URLs for shared links, updates conversation timestamp, invalidates cache,
    and prepares events for broadcasting to participants.

    Args:
        user_id: ID of the user sending the message.
        payload: Dict containing 'conversation_id', 'content', 'message_type' (optional, default 'text'), 'client_msg_id' (optional).
        mqtt_client: MQTT client instance (unused in this function).

    Returns:
        List of event dictionaries to broadcast, each with 'target_user_id'.
    """
    conversation_id = payload.get("conversation_id")
    content = payload.get("content")
    message_type = payload.get("message_type", "text")
    client_msg_id = payload.get("client_msg_id")

    if not conversation_id or not content:
        return []

    if client_msg_id and await _check_message_dedupe(client_msg_id):
        return []

    async with async_session() as db:
        try:
            result = await db.execute(
                select(Conversation).where(
                    Conversation.id == conversation_id,
                    or_(
                        Conversation.participant1_id == user_id,
                        Conversation.participant2_id == user_id
                    )
                )
            )
            conversation = result.scalars().first()

            if not conversation:
                return []

            new_message = Message(
                conversation_id=conversation_id,
                sender_id=user_id,
                content=content,
                message_type=message_type
            )
            db.add(new_message)

            # Handle attachments: parse JSON content for media metadata and create Media record
            if message_type in ("image", "file"):
                try:
                    # Assume content is JSON string or dict with media details
                    media_data = json.loads(content
                                            ) if isinstance(content, str) else content

                    media_rec = Media(
                        message=new_message,
                        conversation_id=conversation_id,
                        uploader_id=user_id,
                        media_type=message_type,
                        filename=media_data.get("filename", "unknown"),
                        minio_bucket=media_data.get("minio_bucket"),
                        minio_object_key=media_data.get("minio_object_key"),
                        file_url=media_data.get("url"),
                        size_bytes=media_data.get("size"),
                        content_type=media_data.get("content_type"),
                        width=media_data.get("width"),
                        height=media_data.get("height")
                    )
                    db.add(media_rec)

                    # Update message content to the file URL for display
                    new_message.content = media_data.get("url", content)

                except (json.JSONDecodeError, AttributeError, TypeError):
                    # Fallback for malformed media data: treat as legacy attachment
                    media_rec = Media(
                        message=new_message,
                        conversation_id=conversation_id,
                        uploader_id=user_id,
                        media_type=message_type,
                        filename="unknown",
                        minio_bucket="legacy",
                        minio_object_key="unknown",
                        file_url=content,
                    )
                    db.add(media_rec)

            # For text messages, extract and store URLs as shared links
            if message_type == "text":
                url_regex = r"https?://[\w\.-/\?=#%&:+~-]+"
                links = re.findall(url_regex, content)
                for link in links:
                    shared = SharedLink(
                        message=new_message,
                        conversation_id=conversation_id,
                        url=link,
                    )
                    db.add(shared)

            conversation.updated_at = datetime.utcnow()

            await db.commit()
            await db.refresh(new_message)

            try:
                await invalidate_conversation_cache(conversation_id)
            except Exception as _e:
                pass
            
            result = await db.execute(select(User).where(User.id == user_id))
            sender = result.scalars().first()

            # Prepare event payload with message details
            base_event = {
                "type": "new_message", "conversation_id": conversation_id, "message": {
                    "id":
                    new_message.id, "conversation_id":
                    new_message.conversation_id, "sender_id":
                    new_message.sender_id, "sender_username":
                    sender.username if sender else None, "content":
                    new_message.content, "message_type":
                    new_message.message_type, "created_at":
                    new_message.created_at.isoformat(), "is_read":
                    new_message.is_read, "is_edited":
                    new_message.is_edited, "edited_at":
                    new_message.edited_at.isoformat() if new_message.edited_at else None
                }
            }

            # Fan out event to both participants and sender (for echo/confirmation)
            events = []
            for uid in [conversation.participant1_id, conversation.participant2_id,
                        user_id]:
                if uid is None:
                    continue
                # Avoid duplicate events for same user
                if not any(e.get('target_user_id') == uid for e in events):
                    events.append({**base_event, "target_user_id": uid})

            return events

        except Exception as e:
            logger.error(f"Error handling message from user {user_id}: {e}")
            await db.rollback()
            return []


async def handle_edit_message(user_id: int, payload: Dict[str, Any],
                              mqtt_client) -> List[Dict[str, Any]]:
    """Handle editing a 1:1 message.

    Validates that the user is the sender, updates the message content and metadata,
    invalidates cache, and prepares events for broadcasting the edit.

    Args:
        user_id: ID of the user editing the message.
        payload: Dict containing 'message_id' and 'content'.
        mqtt_client: MQTT client instance (unused).

    Returns:
        List of event dictionaries to broadcast, each with 'target_user_id'.
    """
    message_id = payload.get("message_id")
    content = payload.get("content")

    if not message_id or not content:
        return []

    async with async_session() as db:
        try:
            result = await db.execute(
                select(Message).where(
                    Message.id == message_id,
                    Message.sender_id == user_id
                )
            )
            msg = result.scalars().first()

            if not msg:
                return []

            msg.content = content
            msg.is_edited = True
            msg.edited_at = datetime.now(timezone.utc)
            await db.commit()

            try:
                await invalidate_conversation_cache(msg.conversation_id)
            except Exception as _e:
                pass

            result = await db.execute(
                select(Conversation).where(Conversation.id == msg.conversation_id)
            )
            conversation = result.scalars().first()
            if not conversation:
                return []

            base_event = {
                "type": "message_edited", "message_id": msg.id, "new_content": msg.content,
                "conversation_id": msg.conversation_id, "edited_at":
                msg.edited_at.isoformat()
            }

            events = []
            if conversation.participant1_id != user_id:
                events.append(
                    {**base_event, "target_user_id": conversation.participant1_id}
                )
            if conversation.participant2_id != user_id:
                events.append(
                    {**base_event, "target_user_id": conversation.participant2_id}
                )

            events.append({**base_event, "target_user_id": user_id})

            return events

        except Exception as e:
            logger.error(f"Error editing message {message_id}: {e}")
            await db.rollback()
            return []


async def handle_delete_message(user_id: int, payload: Dict[str, Any],
                                mqtt_client) -> List[Dict[str, Any]]:
    """Handle deleting a 1:1 message (soft delete for everyone).

    Validates that the user is the sender, marks the message as deleted,
    invalidates cache, and prepares events for broadcasting the deletion.

    Args:
        user_id: ID of the user deleting the message.
        payload: Dict containing 'message_id'.
        mqtt_client: MQTT client instance (unused).

    Returns:
        List of event dictionaries to broadcast, each with 'target_user_id'.
    """
    message_id = payload.get("message_id")

    if not message_id:
        return []

    async with async_session() as db:
        try:
            result = await db.execute(
                select(Message).where(
                    Message.id == message_id,
                    Message.sender_id == user_id
                )
            )
            msg = result.scalars().first()

            if not msg:
                return []

            msg.content = ""
            msg.message_type = "deleted"
            await db.commit()

            try:
                await invalidate_conversation_cache(msg.conversation_id)
            except Exception as _e:
                pass
            
            result = await db.execute(
                select(Conversation).where(Conversation.id == msg.conversation_id)
            )
            conversation = result.scalars().first()
            if not conversation:
                return []

            base_event = {
                "type": "message_deleted",
                "message_id": msg.id,
                "conversation_id": msg.conversation_id,
            }

            events = []
            if conversation.participant1_id != user_id:
                events.append(
                    {**base_event, "target_user_id": conversation.participant1_id}
                )
            if conversation.participant2_id != user_id:
                events.append(
                    {**base_event, "target_user_id": conversation.participant2_id}
                )

            events.append({**base_event, "target_user_id": user_id})

            return events

        except Exception as e:
            logger.error(f"Error deleting message {message_id}: {e}")
            await db.rollback()
            return []


async def handle_hide_message(user_id: int, payload: Dict[str, Any],
                              mqtt_client) -> List[Dict[str, Any]]:
    """Handle hiding a 1:1 message for the user (personal delete).

    Allows the user to hide a message for themselves, regardless of sender.
    Records in DeletedMessage table, invalidates cache, and sends personal notification.

    Args:
        user_id: ID of the user hiding the message.
        payload: Dict containing 'message_id'.
        mqtt_client: MQTT client instance (unused).

    Returns:
        List with a single personal event dictionary for the user.
    """
    message_id = payload.get("message_id")

    if not message_id:
        return []

    async with async_session() as db:
        try:
            result = await db.execute(select(Message).where(Message.id == message_id))
            msg = result.scalars().first()
            if not msg:
                return []

            if msg.sender_id != user_id:
                result = await db.execute(
                    select(Conversation).where(Conversation.id == msg.conversation_id)
                )
                conv = result.scalars().first()
                if not conv or (user_id
                                not in (conv.participant1_id, conv.participant2_id)):
                    return []

            result = await db.execute(
                select(DeletedMessage).where(
                    DeletedMessage.user_id == user_id,
                    DeletedMessage.message_id == message_id,
                )
            )
            exists = result.scalars().first()

            if not exists:
                db.add(DeletedMessage(user_id=user_id, message_id=message_id))
                await db.commit()

            try:
                await invalidate_conversation_cache(msg.conversation_id)
            except Exception as _e:
                pass
            
            return [
                {
                    "type": "message_hidden", "conversation_id": msg.conversation_id,
                    "message_id": msg.id, "target_user_id": user_id
                }
            ]

        except Exception as e:
            logger.error(f"Error hiding message {message_id}: {e}")
            await db.rollback()
            return []


async def handle_typing(user_id: int, payload: Dict[str, Any],
                        mqtt_client) -> List[Dict[str, Any]]:
    """Handle typing indicators in a 1:1 conversation.

    Validates conversation and user, prepares typing event to broadcast to the other participant.

    Args:
        user_id: ID of the user sending the typing indicator.
        payload: Dict containing 'conversation_id' and 'is_typing' (bool).
        mqtt_client: MQTT client instance (unused).

    Returns:
        List of event dictionaries to broadcast, each with 'target_user_id'.
    """
    conversation_id = payload.get("conversation_id")
    is_typing = payload.get("is_typing", False)

    if not conversation_id:
        return []

    async with async_session() as db:
        try:
            result = await db.execute(select(User).where(User.id == user_id))
            user = result.scalars().first()
            if not user:
                return []

            result = await db.execute(
                select(Conversation).where(Conversation.id == conversation_id)
            )
            conversation = result.scalars().first()
            if not conversation:
                return []

            event = {
                "type": "typing", "conversation_id": conversation_id, "user_id": user_id,
                "username": user.username, "is_typing": is_typing
            }

            events = []
            if conversation.participant1_id != user_id:
                events.append({**event, "target_user_id": conversation.participant1_id})
            if conversation.participant2_id != user_id:
                events.append({**event, "target_user_id": conversation.participant2_id})

            return events

        except Exception as e:
            logger.error(f"Error handling typing indicator: {e}")
            return []


async def handle_read_receipt(user_id: int, payload: Dict[str, Any],
                              mqtt_client) -> List[Dict[str, Any]]:
    """Handle read receipts in a 1:1 conversation.

    Marks all unread messages from the other participant as read, and broadcasts the event.

    Args:
        user_id: ID of the user marking messages as read.
        payload: Dict containing 'conversation_id'.
        mqtt_client: MQTT client instance (unused).

    Returns:
        List of event dictionaries to broadcast, each with 'target_user_id'.
    """
    conversation_id = payload.get("conversation_id")

    if not conversation_id:
        return []

    async with async_session() as db:
        try:
            stmt = (
                update(Message)
                .where(
                    Message.conversation_id == conversation_id,
                    Message.sender_id != user_id,
                    Message.is_read == False
                )
                .values(is_read=True)
            )
            result = await db.execute(stmt)
            updated_count = result.rowcount

            await db.commit()

            if updated_count > 0:
                result = await db.execute(
                    select(Conversation).where(Conversation.id == conversation_id)
                )
                conversation = result.scalars().first()
                if not conversation:
                    return []

                event = {
                    "type": "read_receipt", "conversation_id": conversation_id, "reader_id":
                    user_id
                }

                events = []
                if conversation.participant1_id != user_id:
                    events.append({**event, "target_user_id": conversation.participant1_id})
                if conversation.participant2_id != user_id:
                    events.append({**event, "target_user_id": conversation.participant2_id})

                return events

            return []

        except Exception as e:
            logger.error(f"Error handling read receipt: {e}")
            await db.rollback()
            return []


async def handle_group_message(user_id: int, payload: Dict[str, Any],
                               mqtt_client) -> List[Dict[str, Any]]:
    """Handle sending a new message in a group chat.

    Validates group membership, creates the message, updates group timestamp,
    invalidates cache, and prepares events for broadcasting to all group members.

    Args:
        user_id: ID of the user sending the message.
        payload: Dict containing 'group_id', 'content', 'message_type' (optional), 'client_msg_id' (optional).
        mqtt_client: MQTT client instance (unused).

    Returns:
        List of event dictionaries to broadcast, each with 'target_user_id'.
    """
    group_id = payload.get("group_id")
    content = payload.get("content")
    message_type = payload.get("message_type", "text")
    client_msg_id = payload.get("client_msg_id")

    if not group_id or not content:
        return []

    if client_msg_id and await _check_message_dedupe(client_msg_id):
        return []

    async with async_session() as db:
        try:
            result = await db.execute(
                select(GroupMember).where(
                    GroupMember.group_id == group_id,
                    GroupMember.user_id == user_id
                )
            )
            member_rec = result.scalars().first()

            if not member_rec:
                return []

            new_message = GroupMessage(
                group_id=group_id,
                sender_id=user_id,
                content=content,
                message_type=message_type
            )
            db.add(new_message)

            result = await db.execute(select(Group).where(Group.id == group_id))
            group = result.scalars().first()
            if group:
                group.updated_at = datetime.utcnow()

            await db.commit()
            await db.refresh(new_message)

            try:
                await invalidate_group_cache(group_id)
            except Exception as _e:
                pass
            
            result = await db.execute(select(User).where(User.id == user_id))
            sender = result.scalars().first()

            result = await db.execute(
                select(GroupMember).where(GroupMember.group_id == group_id)
            )
            group_members = result.scalars().all()

            base_event = {
                "type": "group_new_message", "group_id": group_id, "message": {
                    "id":
                    new_message.id, "group_id":
                    group_id, "sender_id":
                    user_id, "sender_username":
                    sender.username if sender else None, "content":
                    new_message.content, "message_type":
                    new_message.message_type, "created_at":
                    new_message.created_at.isoformat(), "is_read":
                    new_message.is_read, "is_edited":
                    new_message.is_edited, "edited_at":
                    new_message.edited_at.isoformat() if new_message.edited_at else None
                }
            }

            events = []
            for member in group_members:
                events.append({**base_event, "target_user_id": member.user_id})

            return events

        except Exception as e:
            logger.error(f"Error handling group message from user {user_id}: {e}")
            await db.rollback()
            return []


async def handle_group_edit_message(user_id: int, payload: Dict[str, Any],
                                    mqtt_client) -> List[Dict[str, Any]]:
    """Handle editing a group message.

    Validates group membership and sender, updates the message content and metadata,
    invalidates cache, and prepares events for broadcasting to all group members.

    Args:
        user_id: ID of the user editing the message.
        payload: Dict containing 'group_id', 'message_id', 'content'.
        mqtt_client: MQTT client instance (unused).

    Returns:
        List of event dictionaries to broadcast, each with 'target_user_id'.
    """
    group_id = payload.get("group_id")
    message_id = payload.get("message_id")
    content = payload.get("content")

    if not group_id or not message_id or content is None:
        return []

    async with async_session() as db:
        try:
            result = await db.execute(
                select(GroupMember).where(
                    GroupMember.group_id == group_id,
                    GroupMember.user_id == user_id
                )
            )
            membership = result.scalars().first()

            if not membership:
                return []

            result = await db.execute(
                select(GroupMessage).where(
                    GroupMessage.id == message_id,
                    GroupMessage.sender_id == user_id
                )
            )
            msg = result.scalars().first()

            if not msg:
                return []

            msg.content = content
            msg.is_edited = True
            msg.edited_at = datetime.now(timezone.utc)
            await db.commit()
            await db.refresh(msg)

            try:
                await invalidate_group_cache(group_id)
            except Exception as _e:
                pass
            
            result = await db.execute(
                select(GroupMember).where(GroupMember.group_id == group_id)
            )
            group_members = result.scalars().all()
            
            base_event = {
                "type": "group_update_message", "group_id": group_id, "message":
                msg.to_dict()
            }
            events: List[Dict[str, Any]] = []
            for member in group_members:
                events.append({**base_event, "target_user_id": member.user_id})
            return events

        except Exception as e:
            logger.error(f"Error editing group message {message_id}: {e}")
            await db.rollback()
            return []


async def handle_group_delete_message(
    user_id: int, payload: Dict[str, Any], mqtt_client
) -> List[Dict[str, Any]]:
    """Handle deleting a group message (soft delete for everyone).

    Validates group membership and sender, marks the message as deleted,
    invalidates cache, and prepares events for broadcasting to all group members.

    Args:
        user_id: ID of the user deleting the message.
        payload: Dict containing 'group_id', 'message_id'.
        mqtt_client: MQTT client instance (unused).

    Returns:
        List of event dictionaries to broadcast, each with 'target_user_id'.
    """
    group_id = payload.get("group_id")
    message_id = payload.get("message_id")

    if not group_id or not message_id:
        return []

    async with async_session() as db:
        try:
            result = await db.execute(
                select(GroupMember).where(
                    GroupMember.group_id == group_id,
                    GroupMember.user_id == user_id
                )
            )
            membership = result.scalars().first()

            if not membership:
                return []

            result = await db.execute(
                select(GroupMessage).where(
                    GroupMessage.id == message_id,
                    GroupMessage.sender_id == user_id
                )
            )
            msg = result.scalars().first()

            if not msg:
                return []

            msg.message_type = 'deleted'
            msg.content = ''
            await db.commit()

            try:
                await invalidate_group_cache(group_id)
            except Exception as _e:
                pass
            
            result = await db.execute(
                select(GroupMember).where(GroupMember.group_id == group_id)
            )
            group_members = result.scalars().all()
            
            base_event = {
                "type": "group_delete_message", "group_id": group_id, "message_id": msg.id
            }
            events: List[Dict[str, Any]] = []
            for member in group_members:
                events.append({**base_event, "target_user_id": member.user_id})
            return events

        except Exception as e:
            logger.error(f"Error deleting group message {message_id}: {e}")
            await db.rollback()
            return []


async def handle_group_hide_message(user_id: int, payload: Dict[str, Any],
                                    mqtt_client) -> List[Dict[str, Any]]:
    """Handle hiding a group message for the user (personal delete).

    Validates group membership, records in DeletedGroupMessage table,
    invalidates cache, and sends personal notification to the user.

    Args:
        user_id: ID of the user hiding the message.
        payload: Dict containing 'group_id', 'message_id'.
        mqtt_client: MQTT client instance (unused).

    Returns:
        List with a single personal event dictionary for the user.
    """
    group_id = payload.get("group_id")
    message_id = payload.get("message_id")

    if not group_id or not message_id:
        return []

    async with async_session() as db:
        try:
            result = await db.execute(
                select(GroupMember).where(
                    GroupMember.group_id == group_id,
                    GroupMember.user_id == user_id,
                )
            )
            member_rec = result.scalars().first()

            if not member_rec:
                return []

            result = await db.execute(select(GroupMessage).where(GroupMessage.id == message_id))
            msg = result.scalars().first()
            if not msg or msg.group_id != group_id:
                return []

            result = await db.execute(
                select(DeletedGroupMessage).where(
                    DeletedGroupMessage.user_id == user_id,
                    DeletedGroupMessage.group_message_id == msg.id,
                )
            )
            exists = result.scalars().first()

            if not exists:
                db.add(DeletedGroupMessage(user_id=user_id, group_message_id=msg.id))
                await db.commit()

            try:
                await invalidate_group_cache(group_id)
            except Exception as _e:
                pass
            
            return [
                {
                    "type": "group_message_hidden", "group_id": group_id, "message_id":
                    msg.id, "target_user_id": user_id
                }
            ]

        except Exception as e:
            logger.error(f"Error hiding group message {message_id}: {e}")
            await db.rollback()
            return []
