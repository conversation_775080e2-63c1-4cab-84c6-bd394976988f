from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List
from datetime import datetime

class FeedbackCreate(BaseModel):
    response_id: str = Field(..., description="UUID of the AI response")
    is_helpful: bool = Field(..., description="Whether the response was helpful (thumbs up/down)")
    rating: int = Field(..., ge=1, le=10, description="Rating from 1-10")
    reasons: Optional[List[str]] = Field(default=[], description="List of predefined reason strings")
    comment: Optional[str] = Field(None, description="Optional free-form comment")
    
    @validator('rating')
    def validate_rating_alignment(cls, v, values):
        """Validate that rating aligns with is_helpful"""
        if 'is_helpful' in values:
            is_helpful = values['is_helpful']
            if is_helpful and not (6 <= v <= 10):
                raise ValueError('For helpful feedback, rating must be between 6-10')
            elif not is_helpful and not (1 <= v <= 5):
                raise ValueError('For not helpful feedback, rating must be between 1-5')
        return v

class FeedbackShare(BaseModel):
    feedback_id: int = Field(..., description="ID of the feedback to share")
    share_to_type: str = Field(..., pattern="^(user|group)$", description="Whether sharing to user or group")
    share_to_id: int = Field(..., description="User ID or Group ID to share with")
    share_context: Optional[str] = Field(None, description="Optional sharing context message")

class ResponseShare(BaseModel):
    query: str = Field(..., description="Original query text")
    response: str = Field(..., description="AI response text")
    agent_type: str = Field(..., description="Type of agent used")
    response_data: Dict[str, Any] = Field(..., description="Complete response data including sources")
    share_to_type: str = Field(..., pattern="^(user|group)$", description="Whether sharing to user or group")
    share_to_id: int = Field(..., description="User ID or Group ID to share with")
    share_context: Optional[str] = Field(None, description="Optional sharing context message")

class FeedbackResponse(BaseModel):
    id: int
    user_id: int
    username: Optional[str]
    interaction_id: int
    response_id: Optional[str]
    query_text: Optional[str]
    is_helpful: bool
    rating: int
    reasons: Optional[List[str]]
    comment: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ExpertChatInteractionResponse(BaseModel):
    id: int
    user_id: int
    username: Optional[str]
    agent_type: Optional[str]
    query: str
    response: str
    feedback: Optional[bool]
    response_id: str
    created_at: datetime

    class Config:
        from_attributes = True

class SharedResponseCreate(BaseModel):
    interaction_id: int
    shared_to_type: str = Field(..., pattern="^(user|group)$")
    shared_to_id: int
    share_context: Optional[str] = None

class SharedResponseResponse(BaseModel):
    id: int
    interaction_id: int
    shared_by_user_id: int
    shared_by_username: Optional[str]
    shared_to_type: str
    shared_to_id: int
    share_context: Optional[str]
    created_at: datetime
    # Include interaction details for convenience
    query: Optional[str]
    response: Optional[str]
    agent_type: Optional[str]

    class Config:
        from_attributes = True

class ChatMessageWithSharedResponse(BaseModel):
    """Message format for sharing AI responses in chat"""
    message_type: str = "shared_ai_response"
    shared_response: Dict[str, Any]
    share_context: Optional[str] = None

class FeedbackStats(BaseModel):
    """Statistics about feedback and usage"""
    total_interactions: int
    total_feedback: int
    helpful_percentage: float
    average_rating: float
    rating_distribution: Dict[int, int]  # rating -> count
    most_helpful_queries: List[Dict[str, Any]]
    least_helpful_queries: List[Dict[str, Any]]
    popular_agent_types: List[Dict[str, Any]]
    common_reasons: Dict[str, int]  # reason -> count