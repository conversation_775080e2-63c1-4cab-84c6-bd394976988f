'use client';

import React, { useState, useEffect } from 'react';
import { API_BASE_URL } from '@/lib/api';
import { ThumbUpIcon, ThumbDownIcon, ShareIcon, UsersIcon, UserIcon, BookmarkIcon } from '@heroicons/react/outline';
import { ThumbUpIcon as ThumbUpSolidIcon, ThumbDownIcon as ThumbDownSolidIcon } from '@heroicons/react/solid';
import { fetchUser } from '@/lib/api';
// Import API base URL

// Predefined reasons for helpful feedback (ratings 6-10)
const HELPFUL_REASONS = [
  'Accurate information',
  'Followed instructions perfectly',
  'Thorough explanation',
  'Showcased creativity',
  'Positive attitude',
  'Attention to detail',
  'Clear and concise',
  'Provided useful examples'
];

// Predefined reasons for not helpful feedback (ratings 1-5)
const NOT_HELPFUL_REASONS = [
  'Inaccurate information',
  'Missed instructions',
  'Lack of detail',
  'Off-topic response',
  'Fabricated sources',
  'Poor clarity',
  'Too complex',
  'Incomplete answer'
];

interface FeedbackWidgetProps {
  responseId?: string;
  onFeedback?: (isHelpful: boolean) => void;
  onShare?: (shareData: { shareToType: 'user' | 'group'; shareToId: number; context?: string }) => void;
  onOpenNotes?: () => void;
  query: string;
  response: string;
  agentType: string;
  responseData: any;
  className?: string;
}

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  onShare: (shareData: { shareToType: 'user' | 'group'; shareToId: number; context?: string }) => void;
  query: string;
  response: string;
}

interface FeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (feedback: {
    isHelpful: boolean;
    rating: number;
    reasons: string[];
    comment: string;
  }) => void;
  isHelpful: boolean;
  isSubmitting: boolean;
}

const ShareModal: React.FC<ShareModalProps> = ({ isOpen, onClose, onShare, query, response }) => {
  const [shareToType, setShareToType] = useState<'user' | 'group'>('user');
  const [shareToId, setShareToId] = useState<number>(0);
  const [context, setContext] = useState<string>('');
  const [groups, setGroups] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  React.useEffect(() => {
    if (isOpen) {
      // Only load groups when modal opens
      fetchGroups();
    }
  }, [isOpen]);
  
  // Search for users when query changes
  React.useEffect(() => {
    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    
    if (searchQuery.trim() === '') {
      setSearchResults([]);
      setSelectedUser(null);
      setShareToId(0);
      setIsSearching(false);
      return;
    }
    
    // Set searching state immediately
    setIsSearching(true);
    
    // Debounce the search to avoid too many API calls
    const timeout = setTimeout(async () => {
      try {
        const user = await fetchUser(searchQuery.trim());
        if (user) {
          // Found exact match
          setSearchResults([user]);
          setSelectedUser(user);
          setShareToId(user.id);
        } else {
          // No exact match
          setSearchResults([]);
          setSelectedUser(null);
          setShareToId(0);
        }
      } catch (error) {
        console.error('Error searching for user:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, 500); // 500ms debounce
    
    setSearchTimeout(timeout);
    
    // Cleanup function
    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [searchQuery]);

    const fetchGroups = async () => {
    try {
      // Fetch groups - use the correct endpoint
      const groupsResponse = await fetch(`${API_BASE_URL}/chat/groups/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      });
      if (groupsResponse.ok) {
        const groupsData = await groupsResponse.json();
        setGroups(groupsData);
      } else {
        console.error('Failed to fetch groups:', groupsResponse.status, groupsResponse.statusText);
      }
    } catch (error) {
      console.error('Error fetching groups:', error);
    }
  };

  const handleShare = () => {
    if (shareToId > 0) {
      onShare({
        shareToType,
        shareToId,
        context: context.trim() || undefined
      });
      onClose();
      // Reset form
      setShareToId(0);
      setSelectedUser(null);
      setContext('');
      setSearchQuery('');
      setIsSearching(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 className="text-lg font-semibold mb-4">Share AI Response</h3>

        {/* Preview */}
        <div className="mb-4 p-3 bg-gray-50 rounded-lg text-sm">
          <p className="font-medium text-gray-700">Query:</p>
          <p className="text-gray-600 mb-2">{query.length > 100 ? `${query.substring(0, 100)}...` : query}</p>
          <p className="font-medium text-gray-700">Response:</p>
          <p className="text-gray-600">{response.length > 150 ? `${response.substring(0, 150)}...` : response}</p>
        </div>

        {/* Share type selector */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Share with:</label>
          <div className="flex space-x-4">
            <button
              onClick={() => setShareToType('user')}
              className={`flex items-center px-3 py-2 rounded-md ${shareToType === 'user'
                  ? 'bg-blue-100 text-blue-700 border-blue-300'
                  : 'bg-gray-100 text-gray-700 border-gray-300'
                } border`}
            >
              <UserIcon className="w-4 h-4 mr-2" />
              Individual User
            </button>
            {/* <button
              disabled
              className={`flex items-center px-3 py-2 rounded-md ${shareToType === 'group'
                  ? 'bg-blue-100 text-blue-700 border-blue-300'
                  : 'bg-gray-100 text-gray-700 border-gray-300'
                } border opacity-50 cursor-not-allowed`}
            >
              <UsersIcon className="w-4 h-4 mr-2" />
              Group Chat
            </button> */}
          </div>
        </div>

        {/* Recipient selector */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {shareToType === 'user' ? 'Search and select user:' : 'Select group:'}
          </label>
          
          {shareToType === 'user' ? (
            <div>
              <div className="relative mb-2">
                <input
                  type="text"
                  placeholder="Search by username or email"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    setIsSearching(true);
                  }}
                  className="w-full p-2 pr-8 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">
                  {isSearching ? (
                    <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  )}
                </div>
              </div>
              
              {/* Show selected user or search results */}
              {selectedUser ? (
                <div className="flex items-center p-2 bg-blue-50 border border-blue-300 rounded-md">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold mr-2">
                    {selectedUser.username.charAt(0).toUpperCase()}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{selectedUser.username}</div>
                    {selectedUser.email && <div className="text-xs text-gray-500">{selectedUser.email}</div>}
                  </div>
                  <button 
                    onClick={() => {
                      setSelectedUser(null);
                      setSearchQuery('');
                      setShareToId(0);
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              ) : (
                <div>
                  {searchResults.length > 0 && searchQuery && (
                    <div className="border border-gray-300 rounded-md max-h-40 overflow-y-auto">
                      {searchResults.map(user => (
                        <div 
                          key={user.id} 
                          className="p-2 hover:bg-gray-100 cursor-pointer flex items-center border-b border-gray-200 last:border-b-0"
                          onClick={() => {
                            setSelectedUser(user);
                            setShareToId(user.id);
                            setSearchQuery(user.username);
                            setIsSearching(false);
                          }}
                        >
                          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-semibold mr-2">
                            {user.username.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <div className="font-medium">{user.username}</div>
                            {user.email && <div className="text-xs text-gray-500">{user.email}</div>}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  {searchResults.length === 0 && !isSearching && searchQuery && (
                    <p className="text-sm text-red-500 mt-1">No user found with username '{searchQuery}'</p>
                  )}
                </div>
              )}
            </div>
          ) : (
            <select
              value={shareToId}
              onChange={(e) => setShareToId(Number(e.target.value))}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={0}>Select a group</option>
              {groups.map(group => (
                <option key={group.id} value={group.id}>{group.name}</option>
              ))}
            </select>
          )}
        </div>

        {/* Optional context */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Optional message (context):
          </label>
          <textarea
            value={context}
            onChange={(e) => setContext(e.target.value)}
            placeholder="Add a message to provide context for this share..."
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            rows={3}
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200"
          >
            Cancel
          </button>
          <button
            onClick={handleShare}
            disabled={shareToId === 0}
            className="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {shareToId === 0 ? 'Search for a user' : 'Share'}
          </button>
        </div>
      </div>
    </div>
  );
};

const FeedbackModal: React.FC<FeedbackModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isHelpful,
  isSubmitting
}) => {
  const [rating, setRating] = useState<number>(isHelpful ? 6 : 5);
  const [selectedReasons, setSelectedReasons] = useState<string[]>([]);
  const [comment, setComment] = useState<string>('');

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setRating(isHelpful ? 6 : 5);
      setSelectedReasons([]);
      setComment('');
    }
  }, [isOpen, isHelpful]);

  const handleSubmit = () => {
    onSubmit({
      isHelpful,
      rating,
      reasons: selectedReasons,
      comment
    });
  };

  const handleReasonToggle = (reason: string) => {
    setSelectedReasons(prev => 
      prev.includes(reason)
        ? prev.filter(r => r !== reason)
        : [...prev, reason]
    );
  };

  // Helper functions for rating interface
  const availableReasons = isHelpful ? HELPFUL_REASONS : NOT_HELPFUL_REASONS;
  const allRatings = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  const allowedRatings = isHelpful ? [6, 7, 8, 9, 10] : [1, 2, 3, 4, 5];

  const getRatingLabel = (ratingValue: number) => {
    if (isHelpful) {
      // For helpful responses: 6=Good, 7=Very Good, 8=Great, 9=Excellent, 10=Amazing
      const labels = ['', '', '', '', '', 'Good', 'Very Good', 'Great', 'Excellent', 'Amazing'];
      return labels[ratingValue - 1];
    } else {
      // For not helpful responses: 1=Awful, 2=Poor, 3=Fair, 4=Below Average, 5=Slightly Unhelpful
      const labels = ['Awful', 'Poor', 'Fair', 'Below Average', 'Slightly Unhelpful'];
      return labels[ratingValue - 1];
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 className="text-lg font-semibold mb-4">
          {isHelpful ? 'Rate this helpful response' : 'Tell us what went wrong'}
        </h3>

        {/* Rating Section */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            How would you rate this response?
          </label>
          <div className="flex items-center justify-between mb-2 text-xs text-gray-500">
            <span>1 - Awful</span>
            <span>10 - Amazing</span>
          </div>
          
          <div className="flex items-center justify-center space-x-1 mb-2">
            {allRatings.map((value) => {
              const isAllowed = allowedRatings.includes(value);
              const isSelected = rating === value;
              
              return (
                <button
                  key={value}
                  onClick={() => isAllowed && setRating(value)}
                  disabled={!isAllowed}
                  className={`w-8 h-8 rounded-full border-2 flex items-center justify-center text-xs font-semibold transition-all ${
                    isSelected
                      ? 'border-blue-500 bg-blue-500 text-white'
                      : isAllowed
                      ? 'border-gray-300 bg-white text-gray-700 hover:border-blue-300 hover:bg-blue-50'
                      : 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {value}
                </button>
              );
            })}
          </div>
          
          <p className="text-center text-xs font-medium text-gray-600">
            {getRatingLabel(rating)}
          </p>
        </div>

        {/* Reasons Section */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Why? (Select all that apply)
          </label>
          <div className="grid grid-cols-2 gap-2">
            {availableReasons.map((reason) => (
              <button
                key={reason}
                onClick={() => handleReasonToggle(reason)}
                className={`px-2 py-1 text-xs rounded-md border transition-colors text-left ${
                  selectedReasons.includes(reason)
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
                }`}
              >
                {reason}
              </button>
            ))}
          </div>
        </div>

        {/* Comment Section */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Feel free to add specific details (optional)
          </label>
          <textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Any additional feedback or suggestions..."
            className="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            rows={3}
            maxLength={500}
          />
          <p className="text-xs text-gray-500 mt-1">
            {comment.length}/500 characters
          </p>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            disabled={isSubmitting}
            className="px-3 py-1 text-sm text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="px-4 py-1 text-sm bg-blue-900 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
          </button>
        </div>
      </div>
    </div>
  );
};

const FeedbackWidget: React.FC<FeedbackWidgetProps> = ({
  responseId,
  onFeedback,
  onShare,
  onOpenNotes,
  query,
  response,
  agentType,
  responseData,
  className = ''
}) => {
  const [feedback, setFeedback] = useState<boolean | null>(null);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [pendingHelpfulness, setPendingHelpfulness] = useState<boolean | null>(null);
  const [isSubmittingFeedback, setIsSubmittingFeedback] = useState(false);

  const handleThumbsClick = (isHelpful: boolean) => {
    if (!responseId || isSubmittingFeedback || feedback !== null) return;
    
    // Store the helpfulness and open the feedback modal
    setPendingHelpfulness(isHelpful);
    setIsFeedbackModalOpen(true);
  };

  const handleFeedbackSubmit = async (feedbackData: {
    isHelpful: boolean;
    rating: number;
    reasons: string[];
    comment: string;
  }) => {
    if (!responseId) return;

    setIsSubmittingFeedback(true);

    try {
      const response = await fetch(`${API_BASE_URL}/feedback/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        },
        body: JSON.stringify({
          response_id: responseId,
          is_helpful: feedbackData.isHelpful,
          rating: feedbackData.rating,
          reasons: feedbackData.reasons,
          comment: feedbackData.comment
        })
      });

      if (response.ok) {
        setFeedback(feedbackData.isHelpful);
        onFeedback?.(feedbackData.isHelpful);
        setIsFeedbackModalOpen(false);
        // Show success feedback (could be a toast notification)
        //console.log('Feedback submitted successfully');
      } else {
        console.error('Failed to submit feedback');
        const errorData = await response.json();
        alert(`Failed to submit feedback: ${errorData.detail || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      alert('Error submitting feedback. Please try again.');
    } finally {
      setIsSubmittingFeedback(false);
      setPendingHelpfulness(null);
    }
  };

  const handleCloseFeedbackModal = () => {
    setIsFeedbackModalOpen(false);
    setPendingHelpfulness(null);
  };

  const handleShare = async (shareData: { shareToType: 'user' | 'group'; shareToId: number; context?: string }) => {
    try {
      const shareResponse = await fetch(`${API_BASE_URL}/feedback/share-response`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        },
        body: JSON.stringify({
          query,
          response,
          agent_type: agentType,
          response_data: responseData,
          share_to_type: shareData.shareToType,
          share_to_id: shareData.shareToId,
          share_context: shareData.context
        })
      });

      if (shareResponse.ok) {
        onShare?.(shareData);
        // Show success message
        // alert('Response shared successfully!');
      } else {
        console.error('Failed to share response');
        alert('Failed to share response. Please try again.');
      }
    } catch (error) {
      console.error('Error sharing response:', error);
      alert('Error sharing response. Please try again.');
    }
  };

  return (
    <>
      <div className={`flex items-center py-2 p bg-gray-200 rounded-lg ${className}`}>
        <div className="flex items-center space-x-2">
          <span className="hidden md:inline text-sm text-gray-600">Was this helpful?</span>

          {/* Thumbs up */}
          <button
            onClick={() => handleThumbsClick(true)}
            disabled={isSubmittingFeedback}
            className={`px-2 rounded-full transition-colors ${feedback === true
                ? 'bg-green-100 text-green-600'
                : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
              } ${isSubmittingFeedback ? 'opacity-50 cursor-not-allowed' : ''}`}
            title="Helpful"
          >
            {feedback === true ? (
              <ThumbUpSolidIcon className="w-4 h-4" />
            ) : (
              <ThumbUpIcon className="w-4 h-4" />
            )}
          </button>


            {/* Thumbs down */}
            <button
              onClick={() => handleThumbsClick(false)}
              disabled={isSubmittingFeedback || feedback !== null}
              className={`p-1 rounded-full transition-colors ${
                feedback === false
                  ? 'bg-red-100 text-red-600'
                  : 'text-gray-400 hover:text-red-600 hover:bg-red-50'
              } ${isSubmittingFeedback || feedback !== null ? 'opacity-50 cursor-not-allowed' : ''}`}
              title="Not helpful"
            >
              {feedback === false ? (
                <ThumbDownSolidIcon className="w-4 h-4" />
              ) : (
                <ThumbDownIcon className="w-4 h-4" />
              )}
            </button>
          </div>

          {/* Share & Save to Notes (icon-only) */}
          <div className="ml-2 flex items-center gap-1">
            <button
              onClick={() => setIsShareModalOpen(true)}
              className="p-1 text-blue-900 hover:bg-blue-50 rounded-md transition-colors"
              title="Share"
              aria-label="Share"
            >
              <div className="flex items-center space-x-1">
                <ShareIcon className="w-4 h-4" />
                <span className="hidden md:inline text-xs">Share</span>
              </div>
            </button>
            <button
              onClick={() => onOpenNotes?.()}
              className="p-1 text-amber-600 hover:bg-amber-50 rounded-md transition-colors"
              title="Save to Notes"
              aria-label="Save to Notes"
            >
              <BookmarkIcon className="w-4 h-4" />
            </button>
          </div>
        </div>


      <ShareModal
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        onShare={handleShare}
        query={query}
        response={response}
      />
      
      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={handleCloseFeedbackModal}
        onSubmit={handleFeedbackSubmit}
        isHelpful={pendingHelpfulness === true}
        isSubmitting={isSubmittingFeedback}
      />
    </>
  );
};

export default FeedbackWidget; 