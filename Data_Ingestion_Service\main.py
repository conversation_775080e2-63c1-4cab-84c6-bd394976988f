#!/usr/bin/env python3
"""
FastAPI Data Ingestion Service
============================

A FastAPI service for processing documents and managing data ingestion pipeline.
Provides REST endpoints for file upload, processing, and health checks.
"""

import os
import logging
import asyncio
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
from contextlib import asynccontextmanager

from fastapi import FastAPI, UploadFile, File, HTTPException, BackgroundTasks, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

        # Import existing pipeline components
from config import (
    ProcessingConfig, get_grpc_port, get_qdrant_api_key,
    GEMINI_API_KEY, EMBEDDING_SERVICE_URL, CHUNK_SIZE, CHUNK_OVERLAP,
    LLM_MODEL, VISION_MODEL, PYMUPDF_PRO_KEY
)
from pipeline import MultimodalDataPipeline
from data_ingestion_pipeline import DataIngestionProcessor, health_check as pipeline_health_check
from embedding_client import EmbeddingClient, EmbeddingConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables for background processing
pipeline = None
processor = None
embedding_client = None
# Environment will be passed from backend via API calls
environment = None
grpc_port = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan context manager"""
    global pipeline, processor, embedding_client, grpc_port

    logger.info("Starting Data Ingestion Service...")

    try:
        # Note: Environment will be passed from backend via API calls
        # Create a basic configuration that will be updated with environment-specific values later
        import os

        # grpc_port will be set per request based on environment from backend

        # Create basic processing configuration (environment-specific values will be set later)
        processing_config = ProcessingConfig(
            gemini_api_key=GEMINI_API_KEY,
            environment="PLACEHOLDER",  # Will be overridden by actual API calls
            qdrant_api_key=get_qdrant_api_key(os.getenv("ENVIRONMENT")),
            embedding_api_url=EMBEDDING_SERVICE_URL,
            use_custom_splitter=False,
            use_unstructured=False,
            chunk_size=CHUNK_SIZE,
            chunk_overlap=CHUNK_OVERLAP,
            llm_model=LLM_MODEL,
            vision_model=VISION_MODEL,
            pymupdf_pro_key=PYMUPDF_PRO_KEY
        )

        # Create embedding configuration
        embedding_config = EmbeddingConfig(
            api_url=EMBEDDING_SERVICE_URL,
            timeout=30,
            retry_attempts=3
        )

        # Initialize components on startup
        pipeline = MultimodalDataPipeline(processing_config)
        processor = DataIngestionProcessor()
        embedding_client = EmbeddingClient(embedding_config)

        logger.info("All components initialized successfully")

        yield

    except Exception as e:
        logger.error(f"Failed to initialize components: {e}")
        raise

    finally:
        logger.info("Shutting down Data Ingestion Service...")

# Create FastAPI app
app = FastAPI(
    title="Data Ingestion Service",
    description="FastAPI service for document processing and data ingestion",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class ProcessingRequest(BaseModel):
    username: str
    collection_name: str
    processing_config: Dict[str, Any]
    gemini_api_key: str
    callback_url: Optional[str] = None
    callback_token: Optional[str] = None
    user_id: Optional[int] = None

class ProcessingResponse(BaseModel):
    success: bool
    message: str
    task_id: Optional[str] = None
    processed_documents: int = 0
    failed_documents: int = 0
    total_processing_time: float = 0.0

class HealthResponse(BaseModel):
    status: str
    models_loaded: bool
    pipeline_ready: bool
    embedding_service: bool


@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Data Ingestion Service is running", "version": "1.0.0"}

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        health_data = pipeline_health_check()
        return HealthResponse(**health_data)
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            models_loaded=False,
            pipeline_ready=False,
            embedding_service=False
        )

@app.post("/ingest", response_model=ProcessingResponse, summary="Ingest Files", description="Ingest and process multiple files through the data ingestion pipeline")
async def ingest_files(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    username: str = Form(None),
    collection_name: str = Form(None),
    gemini_api_key: str = Form(None),
    qdrant_url: str = Form(None),
    qdrant_api_key: str = Form(None),
    DATABASE_URL: str = Form(None),
    embedding_api_url: str = Form(None),
    chunk_size: int = Form(750),
    chunk_overlap: int = Form(150),
    llm_model: str = Form(None),
    vision_model: str = Form(None),
    use_custom_splitter: bool = Form(False),
    use_unstructured: bool = Form(False),
    space_id: int = Form(...),
    environment: str = Form()  # Environment passed from backend (Dev or Prod)
):
    """Process uploaded files through the data ingestion pipeline"""
    global grpc_port
    try:
        if not files:
            raise HTTPException(status_code=400, detail="No files provided")

        # Create processing configuration with environment-specific gRPC port
        env_grpc_port = get_grpc_port(environment)
        processing_config = {
            "qdrant_url": qdrant_url,
            "qdrant_api_key": qdrant_api_key,
            "DATABASE_URL": DATABASE_URL,
            "embedding_api_url": embedding_api_url,
            "chunk_size": chunk_size,
            "chunk_overlap": chunk_overlap,
            "llm_model": llm_model,
            "vision_model": vision_model,
            "use_custom_splitter": use_custom_splitter,
            "use_unstructured": use_unstructured,
            "prefer_grpc": True,
            "grpc_port": env_grpc_port
        }

        # Prepare file data for processing - format to match pipeline expectations
        file_data = []
        for i, file in enumerate(files):
            file_bytes = await file.read()
            # Create a stream-like object for the pipeline
            from io import BytesIO
            stream_obj = BytesIO(file_bytes)

            # Format as MinIO-style stream dict that the pipeline expects
            file_data.append({
                'filename': file.filename,
                'stream': stream_obj,
                'source': 'stream',
                'space_id': space_id,
                'content_type': file.content_type
            })

        # Add background task for processing
        task_id = f"{username}_{collection_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        background_tasks.add_task(
            ingest_files_background,
            file_data=file_data,
            username=username,
            collection_name=collection_name,
            processing_config=processing_config,
            gemini_api_key=gemini_api_key,
            task_id=task_id,
            space_id=space_id,
            environment=environment
        )

        return ProcessingResponse(
            success=True,
            message="Files queued for processing",
            task_id=task_id,
            processed_documents=len(files),  # All files are queued for processing
            failed_documents=0
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing files: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


async def ingest_files_background(
    file_data: List[Dict],
    username: str,
    collection_name: str,
    processing_config: Dict[str, Any],
    gemini_api_key: str,
    task_id: str,
    space_id: int,
    environment: str
):
    """Background task to process files"""
    try:
        logger.info(f"Starting background processing for task: {task_id}")

        start_time = datetime.now()

        # Process files directly using the stream format
        temp_files = []
        with tempfile.TemporaryDirectory() as temp_dir:
            for i, file_info in enumerate(file_data):
                if file_info.get('source') == 'stream':
                    # Handle MinIO-style stream format
                    filename = file_info.get('filename', f'file_{i+1}')
                    stream = file_info['stream']
                    space_id = file_info.get('space_id', space_id)

                    # Read from stream and write to temp file
                    if hasattr(stream, 'read'):
                        content = stream.read()
                        if isinstance(content, str):
                            content = content.encode('utf-8')
                    else:
                        content = stream

                    temp_path = Path(temp_dir) / filename
                    temp_path.write_bytes(content)

                    temp_files.append({
                        'filename': filename,
                        'path': str(temp_path),
                        'space_id': space_id
                    })

                # Use global Gemini API key (same for all environments)
                from config import GEMINI_API_KEY
                if not GEMINI_API_KEY:
                    raise ValueError("GEMINI_API_KEY is required but not provided")

                # Create environment-specific processing configuration
                from config import ProcessingConfig, LLM_MODEL, VISION_MODEL, CHUNK_SIZE, CHUNK_OVERLAP, PYMUPDF_PRO_KEY
                # Build config dict, excluding None values so dataclass defaults are used
                config_dict = {
                    'gemini_api_key': GEMINI_API_KEY,
                    'environment': environment,
                    'qdrant_url': processing_config.get('qdrant_url'),
                    'prefer_grpc': processing_config.get('prefer_grpc', True),
                    'grpc_port': processing_config.get('grpc_port'),
                    'qdrant_api_key': processing_config.get('qdrant_api_key'),
                    'DATABASE_URL': processing_config.get('DATABASE_URL'),
                    'embedding_api_url': processing_config.get('embedding_api_url'),
                    'use_custom_splitter': processing_config.get('use_custom_splitter', False),
                    'use_unstructured': processing_config.get('use_unstructured', False),
                }

                # Only add non-None values so dataclass defaults are preserved
                if processing_config.get('chunk_size') is not None:
                    config_dict['chunk_size'] = processing_config.get('chunk_size')
                if processing_config.get('chunk_overlap') is not None:
                    config_dict['chunk_overlap'] = processing_config.get('chunk_overlap')
                if processing_config.get('llm_model') is not None:
                    config_dict['llm_model'] = processing_config.get('llm_model')
                if processing_config.get('vision_model') is not None:
                    config_dict['vision_model'] = processing_config.get('vision_model')
                if processing_config.get('pymupdf_pro_key') is not None:
                    config_dict['pymupdf_pro_key'] = processing_config.get('pymupdf_pro_key')

                env_processing_config = ProcessingConfig(**config_dict)


            # Create pipeline with environment-specific config
            from pipeline import MultimodalDataPipeline
            env_pipeline = MultimodalDataPipeline(env_processing_config)

            # Setup Qdrant collection and embeddings before processing files
            logger.info(f"Setting up Qdrant collection: {collection_name}")
            env_pipeline.setup_qdrant_collection(collection_name)
            logger.info("Qdrant collection setup complete")

            # Process each file
            processed_count = 0
            failed_count = 0

            for file_info in temp_files:
                try:
                    space_id = file_info.get('space_id', space_id)

                    # Open the temp file as a stream for the pipeline
                    with open(file_info['path'], 'rb') as file_stream:
                        # Set filename attribute for the pipeline to recognize
                        file_stream.filename = file_info['filename']
                        result = await env_pipeline.process_file(
                            file_stream,
                            collection_name,
                            space_id
                        )
                    processed_count += 1
                    logger.info(f"Processed file: {file_info['filename']}")

                except Exception as e:
                    logger.error(f"Failed to process file {file_info['filename']}: {e}")
                    failed_count += 1

        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()

        logger.info(f"Background processing completed for task {task_id}: {processed_count} success, {failed_count} failed, {total_time:.2f}s total")

    except Exception as e:
        logger.error(f"Background processing failed for task {task_id}: {e}")


if __name__ == "__main__":
    port = int(os.getenv("DATA_INGESTION_PORT", "8020"))
    host = os.getenv("HOST", "0.0.0.0")

    logger.info(f"Starting server on {host}:{port}")

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )
