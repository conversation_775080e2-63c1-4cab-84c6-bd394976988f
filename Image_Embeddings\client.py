import requests
import base64
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImageEmbeddingClient:
    """Client for Image Embedding Service"""
    
    def __init__(self, service_url: str):
        """
        Initialize client
        
        Args:
            service_url: URL of the image embedding service
        """
        self.service_url = service_url.rstrip('/')
        self._check_connection()
    
    def _check_connection(self):
        """Check if service is available"""
        try:
            response = requests.get(f"{self.service_url}/health", timeout=5)
            if response.status_code == 200:
                logger.info(f"Connected to Image Embedding Service at {self.service_url}")
            else:
                logger.warning(f"Service returned status {response.status_code}")
        except Exception as e:
            logger.error(f"Could not connect to service: {e}")
    
    def health_check(self) -> Dict[str, Any]:
        """
        Check service health
        
        Returns:
            Health status dictionary
        """
        try:
            response = requests.get(f"{self.service_url}/health")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {"status": "error", "message": str(e)}
    
    def encode_image_from_path(self, image_path: str) -> str:
        """
        Load and encode image from file path
        
        Args:
            image_path: Path to image file
            
        Returns:
            Base64 encoded image string
        """
        with open(image_path, "rb") as f:
            return base64.b64encode(f.read()).decode('utf-8')
    
    def embed_images(self, images_base64: List[str]) -> Dict[str, Any]:
        """
        Generate embeddings for images
        
        Args:
            images_base64: List of base64 encoded images
            
        Returns:
            Dictionary with embeddings, dimension, and processing_time
        """
        try:
            response = requests.post(
                f"{self.service_url}/embed",
                json={"images": images_base64}
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            raise
    
    def embed_image_files(self, image_paths: List[str]) -> Dict[str, Any]:
        """
        Generate embeddings for image files
        
        Args:
            image_paths: List of paths to image files
            
        Returns:
            Dictionary with embeddings, dimension, and processing_time
        """
        images_base64 = [self.encode_image_from_path(path) for path in image_paths]
        return self.embed_images(images_base64)
    
    def caption_images(self, images_base64: List[str], max_length: int = 100) -> Dict[str, Any]:
        """
        Generate captions for images
        
        Args:
            images_base64: List of base64 encoded images
            max_length: Maximum caption length
            
        Returns:
            Dictionary with captions and processing_time
        """
        try:
            response = requests.post(
                f"{self.service_url}/caption",
                json={"images": images_base64, "max_length": max_length}
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error generating captions: {e}")
            raise
    
    def caption_image_files(self, image_paths: List[str], max_length: int = 100) -> Dict[str, Any]:
        """
        Generate captions for image files
        
        Args:
            image_paths: List of paths to image files
            max_length: Maximum caption length
            
        Returns:
            Dictionary with captions and processing_time
        """
        images_base64 = [self.encode_image_from_path(path) for path in image_paths]
        return self.caption_images(images_base64, max_length)
    
    def embed_text(self, texts: List[str]) -> Dict[str, Any]:
        """
        Generate embeddings for text queries
        
        Args:
            texts: List of text queries
            
        Returns:
            Dictionary with embeddings, dimension, and processing_time
        """
        try:
            response = requests.post(
                f"{self.service_url}/embed-text",
                json=texts
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error generating text embeddings: {e}")
            raise
    
    def upload_and_embed(self, image_paths: List[str]) -> Dict[str, Any]:
        """
        Upload image files and generate embeddings
        
        Args:
            image_paths: List of paths to image files
            
        Returns:
            Dictionary with embeddings, dimension, and processing_time
        """
        try:
            files = []
            for path in image_paths:
                files.append(('files', open(path, 'rb')))
            
            response = requests.post(
                f"{self.service_url}/upload-embed",
                files=files
            )
            response.raise_for_status()
            
            # Close files
            for _, f in files:
                f.close()
            
            return response.json()
        except Exception as e:
            logger.error(f"Error uploading and embedding: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model
        
        Returns:
            Model information dictionary
        """
        try:
            response = requests.get(f"{self.service_url}/model-info")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error getting model info: {e}")
            raise


# Example usage
if __name__ == "__main__":
    # Initialize client
    client = ImageEmbeddingClient("http://localhost:8019")
    
    # Check health
    health = client.health_check()
    print(f"Health: {health}")
    
    # Get model info
    model_info = client.get_model_info()
    print(f"Model Info: {model_info}")
    
    # Example: Embed an image
    # image_path = "path/to/your/image.jpg"
    # result = client.embed_image_files([image_path])
    # print(f"Embedding dimension: {result['dimension']}")
    # print(f"Processing time: {result['processing_time']}s")

