// =============================================================================
// EMQX Broker Configuration File (base.hocon)
// Version: EMQX 5.8.7 Open Source
// Purpose: Chat application MQTT broker configuration with JWT authentication
// Security: JWT-based auth with HMAC-SHA256, ACL authorization
// Features: MQTT over TCP/WebSocket, disabled retained messages and durable sessions
// =============================================================================

// --- Listeners Configuration ---
// Configure network listeners for MQTT protocol variants
// Standard MQTT over TCP on port 1883 (mandatory for most MQTT clients)
listeners.tcp.default { bind = "0.0.0.0:1883" }
// SSL/TLS encrypted MQTT (disabled - requires certificates)
listeners.ssl.default { enable = false }
// WebSocket Secure (disabled - requires certificates)
listeners.wss.default { enable = false }

// MQTT over WebSocket on port 8083 (required for browser-based clients)
// Clients connect to: ws://host:8083/mqtt
listeners.ws.default  {
  bind = "0.0.0.0:8083"
  websocket.mqtt_path = "/mqtt"
}

// Enable this block when you have real certs in etc/certs
// listeners.wss.default {
//   bind = "0.0.0.0:8084"
//   websocket.mqtt_path = "/mqtt"
//   ssl_options {
//     cacertfile = "etc/certs/ca.pem"
//     certfile   = "etc/certs/server.pem"
//     keyfile    = "etc/certs/server.key"
//   }
// }

// --- Authentication Configuration ---
// Configure client authentication mechanisms
// JWT-based authentication using HMAC-SHA256
authentication = [
  {
    mechanism = jwt              // Use JWT for authentication
    enable = true               // Enable this authentication method
    from = password             // Extract JWT from MQTT CONNECT password field
    algorithm = hmac-based     // Use HMAC-based JWT validation (HS256/384/512)
    use_jwks = false           // Use static secret instead of JWKS endpoint
    secret = "664c8ab2b248b876d27c6deecaab01d3337d4f39b74e451053"  // 512-bit HMAC secret key
    secret_base64_encoded = false  // Secret is provided as hex string
    disconnect_after_expire = true 
    acl_claim_name = "acl"      // Extract ACL rules from JWT 'acl' claim
  }
]

// --- Authorization Configuration ---
// Configure access control and permission checking
authorization = {
  no_match = deny           // Deny access when no authorization rule matches
  sources = []             // Use only JWT-embedded ACL rules (no external authorizers)
}

// --- MQTT Protocol Configuration ---
// Core MQTT protocol settings and limits
mqtt {
  max_packet_size = "1MB"      // Maximum MQTT packet size (1MB)
  max_clientid_len = 65535    // Maximum Client ID length (MQTT v5.0 spec limit)
  max_topic_levels = 128      // Maximum topic hierarchy levels
  max_qos_allowed = 2         // Allow QoS 0, 1, and 2
  max_topic_alias = 65535     // Maximum topic alias mappings (MQTT v5.0)
  retain_available = false    // Disable retained messages (performance optimization)
  wildcard_subscription = true   // Allow wildcard topic subscriptions (+, #)
  shared_subscription = true     // Allow shared subscriptions ($share/group/topic)
  exclusive_subscription = false // Disable exclusive subscriptions
  // Do not store QoS 0 messages in client mqueues (prevents presence floods from filling queues)
  mqueue_store_qos0 = false
}

// --- Durable Sessions Configuration ---
// Session persistence for offline message delivery (MQTT v5.0 feature)
// Disabled for simplicity and performance in chat application
durable_sessions {
  enable = false  // Disable session persistence to disk
}

// --- Message Retainer Configuration ---
// Retained messages storage and management
// Disabled for chat application (retained messages not needed)
retainer {
  enable = false  // Disable retained message functionality

  // Configuration for when retainer is enabled
  allow_never_expire = true        // Allow messages that never expire
  msg_expiry_interval = "0s"       // Messages never expire by default
  max_payload_size    = "1MB"      // Maximum retained message payload size
  stop_publish_clear_msg = false   // Don't clear retained messages on publish

  // Storage backend configuration (Mnesia built-in database)
  backend {
    type = built_in_database       // Use EMQX's built-in Mnesia database
    storage_type = disc           // Store retained messages on disk
    max_retained_messages = 1000000 // Maximum number of retained messages
  }
}
