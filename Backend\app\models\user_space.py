from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, ForeignKey, <PERSON>olean
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum as PyEnum
from ..database import Base


class SpaceType(PyEnum):
    """Space type discriminator used for routing storage and permissions."""
    personal = "personal"
    shared = "shared"


class PlaygroundType(PyEnum):
    """Playground type discriminator for content categorization."""
    documents = "documents"
    images = "images"


class UserSpace(Base):
    """Workspace entity for personal and shared knowledge spaces.

    Personal spaces are private to the owner. Shared spaces are owned by
    a user but include additional members via `SpaceMembership`.
    """
    __tablename__ = "user_spaces"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    space_type = Column(
        String(20), nullable=False, default=SpaceType.personal.value, index=True
    )
    playground_type = Column(
        String(20), nullable=False, default=PlaygroundType.documents.value, index=True
    )
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)

    owner_id = Column(Integer, ForeignKey('users.id'), nullable=False)

    owner = relationship("User", back_populates="spaces")
    documents = relationship("KnowledgeDocument", back_populates="space")
    raw_documents = relationship("RawDocument", back_populates="space")
    memberships = relationship(
        "SpaceMembership", back_populates="space", cascade="all, delete-orphan"
    )

    @property
    def collection_name(self) -> str:
        """Stable storage/search collection name for this space.

        - Shared spaces use a deterministic prefix `shared_space_{id}` so all
          members land in the same MinIO/Qdrant namespace.
        - Personal spaces use a username-derived bucket/collection identifier to
          preserve existing behavior/paths.
        
        Note: For personal spaces, this requires the owner relationship to be loaded.
        If owner is not loaded, falls back to user_{id} format.
        """
        if self.space_type == SpaceType.shared.value:
            return f"shared_space_{self.id}"
        
        # Personal space: derive from owner username if relationship is loaded
        if self.owner:
            username = self.owner.username
        else:
            # Fallback if relationship not loaded - callers should ensure owner is loaded
            username = f"user_{self.owner_id}"

        # Normalize to [a-z0-9_]
        clean_username = ''.join(c if c.isalnum() else '_' for c in username.lower())
        return clean_username

    def is_shared(self) -> bool:
        """Whether this space is shared (has members beyond owner)."""
        return self.space_type == SpaceType.shared.value

    def is_personal(self) -> bool:
        """Whether this space is personal (owner-only)."""
        return self.space_type == SpaceType.personal.value
    
    def is_document_playground(self) -> bool:
        """Whether this space is a document playground."""
        return self.playground_type == PlaygroundType.documents.value
    
    def is_image_playground(self) -> bool:
        """Whether this space is an image playground."""
        return self.playground_type == PlaygroundType.images.value

    def get_member_count(self) -> int:
        """Count members for display.

        Personal: treated as 1 (the owner). Shared: count membership rows.
        
        Note: For shared spaces, only returns accurate count if memberships
        relationship is already loaded. Otherwise returns 0 to avoid lazy loading.
        """
        if self.is_personal():
            return 1
        # Check if memberships is already loaded to avoid triggering lazy load
        if 'memberships' in self.__dict__:
            return len(self.memberships) if self.memberships else 0
        return 0  # Fallback for newly created spaces or when not loaded

    def has_member(self, user_id: int) -> bool:
        """True if `user_id` participates in this space.

        Personal: only the owner matches. Shared: search memberships in-memory
        (relationship is loaded in typical router flows).
        
        Note: Returns False for shared spaces if memberships not loaded to avoid lazy loading.
        """
        if self.is_personal():
            return self.owner_id == user_id
        # Check if memberships is already loaded to avoid triggering lazy load
        if 'memberships' in self.__dict__ and self.memberships:
            return any(membership.user_id == user_id for membership in self.memberships)
        return False  # Fallback when memberships not loaded

    def get_user_role(self, user_id: int) -> str:
        """Return the space-scoped role for `user_id` or None if not a member.

        Personal: owner is effectively admin; others have no role.
        Shared: derive from membership rows.
        
        Note: Returns None for shared spaces if memberships not loaded to avoid lazy loading.
        """
        if self.is_personal():
            return "admin" if self.owner_id == user_id else None

        # Check if memberships is already loaded to avoid triggering lazy load
        if 'memberships' in self.__dict__ and self.memberships:
            for membership in self.memberships:
                if membership.user_id == user_id:
                    return membership.role
        return None

    async def get_linked_group_id(self, db) -> int:
        """Get the ID of the linked group chat if one exists.
        
        Args:
            db: AsyncSession for database queries
            
        Returns:
            Group ID if found, None otherwise
        """
        from .group_chat import Group
        from sqlalchemy import select
        
        if not self.is_shared():
            return None
            
        result = await db.execute(
            select(Group).where(
                Group.space_id == self.id,
                Group.is_active == True
            )
        )
        group = result.scalars().first()
        return group.id if group else None

    def to_dict(self, linked_group_id=None, document_count=None, member_count=None) -> dict:
        """Serialize space for API responses and caching.
        
        Args:
            linked_group_id: Optional pre-fetched linked group ID for shared spaces
            document_count: Optional pre-computed document count to avoid lazy loading
            member_count: Optional pre-computed member count to avoid lazy loading
        """
        # Use provided document_count or check if documents are already loaded
        if document_count is not None:
            doc_count = document_count
        elif hasattr(self, '_document_count'):
            # Check if document count was added as a dynamic attribute from query
            doc_count = self._document_count
        else:
            # Fallback: check if documents relationship is already loaded (in __dict__)
            # to avoid triggering lazy load
            doc_count = len(self.documents) if 'documents' in self.__dict__ else 0
        
        # Use provided member_count or call get_member_count (which is now lazy-load safe)
        mem_count = member_count if member_count is not None else self.get_member_count()
        
        data = {
            'id': self.id, 'name': self.name, 'description': self.description,
            'space_type': self.space_type, 'playground_type': self.playground_type,
            'created_at':
            self.created_at.isoformat() if self.created_at else None, 'updated_at':
            self.updated_at.isoformat() if self.updated_at else None, 'is_active':
            self.is_active, 'owner_id': self.owner_id, 'owner_username':
            self.owner.username if self.owner else None, 'collection_name':
            self.collection_name, 'document_count': doc_count, 'member_count':
            mem_count, 'is_shared': self.is_shared(), 'is_personal':
            self.is_personal()
        }
        
        # Add linked group ID for shared spaces if provided
        if self.is_shared() and linked_group_id is not None:
            data['linked_group_id'] = linked_group_id
        
        return data

    def __repr__(self):
        return f'<UserSpace {self.name}>'
