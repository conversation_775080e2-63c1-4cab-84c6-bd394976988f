import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Optional, Dict, Any
from datetime import datetime
 
from ..database import get_db
from ..models.user import User
from ..models.knowledge_base import KnowledgeDocument
from ..models.user_space import UserSpace
from ..auth import get_current_user, require_admin
from ..services.external_services import external_services
from ..schemas.document_management import (
    UserSpaceFilesResponse,
    DocumentIngestionRequest,
    DocumentIngestionResponse
)
from ..services.redis_service import invalidate_knowledge_cache
 
router = APIRouter(prefix="/documents", tags=["Document Management"])
 
# Configure logging for this module
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# User space helper functions
def get_user_collection_name(username: str) -> str:
    """Get collection name for a user"""
    clean_username = ''.join(c if c.isalnum() else '_' for c in username.lower())
    return clean_username
 
async def get_user_space_files_from_db(space_id: int, db: AsyncSession) -> List[Dict[str, Any]]:
    """Get files for a user space from the knowledge base"""
    result = await db.execute(
        select(KnowledgeDocument).where(
            KnowledgeDocument.space_id == space_id
        )
    )
    documents = result.scalars().all()
   
    files = []
    for doc in documents:
        files.append({
            "id": doc.id,
            "title": doc.title,
            "filename": doc.filename or f"{doc.title}.txt",
            "document_type": doc.document_type,
            "description": doc.description,
            "created_at": doc.created_at.isoformat() if doc.created_at else None,
            "department": doc.department,
            "indexed": doc.indexed,
            "indexed_at": doc.indexed_at.isoformat() if doc.indexed_at else None
        })
   
    return files
 
@router.get("/health")
async def health_check():
    """Check health of document management services"""
    try:
        # Check external services health
        services_health = external_services.check_services_health()
       
        return {
            "status": "healthy",
            "external_services": services_health,
            "message": "Document management services are operational"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Document management services are experiencing issues"
        }
 
@router.get("/index-status/{document_id}")
async def get_document_index_status(
    document_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get index status for a specific document (Available to all users)
    """
    try:
        result = await db.execute(
            select(KnowledgeDocument).where(
                KnowledgeDocument.id == document_id
            )
        )
        document = result.scalars().first()
       
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
       
        return {
            "document_id": document_id,
            "indexed": document.indexed,
            "indexed_at": document.indexed_at.isoformat() if document.indexed_at else None,
            "title": document.title,
            "department": document.department
        }
       
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get index status for document {document_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get document index status"
        )
 
@router.post("/bulk-index")
async def bulk_index_documents(
    department: Optional[str] = None,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    Bulk index all non-indexed documents (Admin only)
    """
    try:
        # Build statement
        stmt = select(KnowledgeDocument).where(KnowledgeDocument.indexed == False)
       
        if department:
            stmt = stmt.where(KnowledgeDocument.department.ilike(department))
       
        result = await db.execute(stmt)
        documents = result.scalars().all()
       
        if not documents:
            return {
                "success": True,
                "message": "No documents found to index",
                "processed_count": 0,
                "failed_count": 0
            }
       
        processed_count = 0
        failed_count = 0
        failed_documents = []
       
        for document in documents:
            try:
                # Prepare document for ingestion based on document type
                if document.document_type in ('pdf', 'docx') and (document.minio_bucket and document.minio_object_key):
                    # For PDF/DOCX stored in MinIO, include MinIO info
                    documents_to_index = [{
                        "content": "",  # PDFs/DOCX don't have raw text content here
                        "filename": document.filename or (
                            f"{document.title}.docx" if document.document_type == "docx" else f"{document.title}.pdf"
                        ),
                        "title": document.title,
                        "document_type": document.document_type,
                        "knowledge_doc_id": document.id,
                        "minio_bucket": document.minio_bucket,
                        "minio_object_key": document.minio_object_key,
                        "content_type": document.content_type,
                        "file_data": None  # Legacy field for backward compatibility
                    }]
                elif document.document_type in ('pdf', 'docx') and document.file_data:
                    # Legacy PDF/DOCX with binary data (for migration)
                    documents_to_index = [{
                        "content": "",  # PDFs/DOCX don't have raw text content here
                        "filename": document.filename or (
                            f"{document.title}.docx" if document.document_type == "docx" else f"{document.title}.pdf"
                        ),
                        "title": document.title,
                        "document_type": document.document_type,
                        "knowledge_doc_id": document.id,
                        "file_data": document.file_data
                    }]
                else:
                    # For text documents (or anything else with inline text), include content
                    documents_to_index = [{
                        "content": document.content or "",
                        "filename": document.filename or f"{document.title}.txt",
                        "title": document.title,
                        "document_type": document.document_type,
                        "knowledge_doc_id": document.id,
                        "file_data": None
                    }]
               
                # Use external services for ingestion
                result = await external_services.ingest_documents(
                    documents=documents_to_index,
                    username=current_user.username,
                    collection_name=get_user_collection_name(current_user.username),
                    user_id=current_user.id,
                    space_id=document.space_id
                )
               
                if result.get("success", True):
                    document.mark_as_indexed()
                    processed_count += 1
                else:
                    failed_count += 1
                    failed_documents.append({
                        "id": document.id,
                        "title": document.title,
                        "error": result.get("error", "Unknown error")
                    })
               
            except Exception as e:
                logger.error(f"Failed to index document {document.id}: {e}")
                failed_count += 1
                failed_documents.append({
                    "id": document.id,
                    "title": document.title,
                    "error": str(e)
                })
       
        # Commit all changes
        await db.commit()
 
        # After bulk indexing, clear all knowledge cache to avoid stale indexing flags
        try:
            await invalidate_knowledge_cache()
        except Exception as e:
            logger.warning(f"Cache invalidation failed after bulk indexing: {e}")
 
        return {
            "success": True,
            "message": f"Bulk indexing completed. Processed: {processed_count}, Failed: {failed_count}",
            "processed_count": processed_count,
            "failed_count": failed_count,
            "failed_documents": failed_documents if failed_documents else None
        }
       
    except Exception as e:
        logger.error(f"Bulk indexing failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Bulk indexing failed: {str(e)}"
        )