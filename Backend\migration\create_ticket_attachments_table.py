#!/usr/bin/env python3
"""
Database migration script to create the ticket_attachments table if it does not exist
"""

import os
import sys
from pathlib import Path

# Add parent directory to path to import config
sys.path.insert(0, str(Path(__file__).parent.parent))

import asyncpg
import asyncio
from app.config import DATABASE_URL


async def migrate_create_ticket_attachments_table():
    """Execute the migration"""
    print("Starting migration: Creating ticket_attachments table if missing...")

    # Convert SQLAlchemy asyncpg URL to plain PostgreSQL URL for asyncpg
    db_url = DATABASE_URL.replace("+asyncpg", "")

    print("Connecting to database...")
    conn = await asyncpg.connect(db_url)

    try:
        # Check if table exists
        result = await conn.fetchval("""
            SELECT to_regclass('public.ticket_attachments')
        """)
        exists = result is not None

        if exists:
            print("✅ ticket_attachments table already exists. Migration not needed.")
            return True

        # Create table
        print("Creating ticket_attachments table...")
        await conn.execute("""
            CREATE TABLE ticket_attachments (
                id SERIAL PRIMARY KEY,
                ticket_id INTEGER NOT NULL REFERENCES tickets(id) ON DELETE CASCADE,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                file_size INTEGER NOT NULL,
                content_type VARCHAR(100) NOT NULL,
                minio_bucket VARCHAR(100) NOT NULL,
                minio_object_key VARCHAR(500) NOT NULL,
                uploaded_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                uploaded_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW()
            )
        """)

        # Create indexes
        print("Creating indexes...")
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_ticket_attachments_ticket_id ON ticket_attachments (ticket_id);
        """)
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_ticket_attachments_uploaded_by ON ticket_attachments (uploaded_by);
        """)
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_ticket_attachments_uploaded_at ON ticket_attachments (uploaded_at);
        """)

        print("✅ Migration completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Error during migration: {e}")
        return False
    finally:
        await conn.close()
        print("Database connection closed.")


if __name__ == "__main__":
    success = asyncio.run(migrate_create_ticket_attachments_table())
    if success:
        print("\n🎉 Ticket attachments table ready!")
    else:
        print("\n💥 Migration failed! Please check logs.")
        sys.exit(1)
