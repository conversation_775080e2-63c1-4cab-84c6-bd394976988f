"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/socks";
exports.ids = ["vendor-chunks/socks"];
exports.modules = {

/***/ "(ssr)/./node_modules/socks/build/client/socksclient.js":
/*!********************************************************!*\
  !*** ./node_modules/socks/build/client/socksclient.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SocksClientError = exports.SocksClient = void 0;\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst net = __webpack_require__(/*! net */ \"net\");\nconst smart_buffer_1 = __webpack_require__(/*! smart-buffer */ \"(ssr)/./node_modules/smart-buffer/build/smartbuffer.js\");\nconst constants_1 = __webpack_require__(/*! ../common/constants */ \"(ssr)/./node_modules/socks/build/common/constants.js\");\nconst helpers_1 = __webpack_require__(/*! ../common/helpers */ \"(ssr)/./node_modules/socks/build/common/helpers.js\");\nconst receivebuffer_1 = __webpack_require__(/*! ../common/receivebuffer */ \"(ssr)/./node_modules/socks/build/common/receivebuffer.js\");\nconst util_1 = __webpack_require__(/*! ../common/util */ \"(ssr)/./node_modules/socks/build/common/util.js\");\nObject.defineProperty(exports, \"SocksClientError\", ({ enumerable: true, get: function () { return util_1.SocksClientError; } }));\nconst ip_address_1 = __webpack_require__(/*! ip-address */ \"(ssr)/./node_modules/ip-address/dist/ip-address.js\");\nclass SocksClient extends events_1.EventEmitter {\n    constructor(options) {\n        super();\n        this.options = Object.assign({}, options);\n        // Validate SocksClientOptions\n        (0, helpers_1.validateSocksClientOptions)(options);\n        // Default state\n        this.setState(constants_1.SocksClientState.Created);\n    }\n    /**\n     * Creates a new SOCKS connection.\n     *\n     * Note: Supports callbacks and promises. Only supports the connect command.\n     * @param options { SocksClientOptions } Options.\n     * @param callback { Function } An optional callback function.\n     * @returns { Promise }\n     */\n    static createConnection(options, callback) {\n        return new Promise((resolve, reject) => {\n            // Validate SocksClientOptions\n            try {\n                (0, helpers_1.validateSocksClientOptions)(options, ['connect']);\n            }\n            catch (err) {\n                if (typeof callback === 'function') {\n                    callback(err);\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    return resolve(err); // Resolves pending promise (prevents memory leaks).\n                }\n                else {\n                    return reject(err);\n                }\n            }\n            const client = new SocksClient(options);\n            client.connect(options.existing_socket);\n            client.once('established', (info) => {\n                client.removeAllListeners();\n                if (typeof callback === 'function') {\n                    callback(null, info);\n                    resolve(info); // Resolves pending promise (prevents memory leaks).\n                }\n                else {\n                    resolve(info);\n                }\n            });\n            // Error occurred, failed to establish connection.\n            client.once('error', (err) => {\n                client.removeAllListeners();\n                if (typeof callback === 'function') {\n                    callback(err);\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    resolve(err); // Resolves pending promise (prevents memory leaks).\n                }\n                else {\n                    reject(err);\n                }\n            });\n        });\n    }\n    /**\n     * Creates a new SOCKS connection chain to a destination host through 2 or more SOCKS proxies.\n     *\n     * Note: Supports callbacks and promises. Only supports the connect method.\n     * Note: Implemented via createConnection() factory function.\n     * @param options { SocksClientChainOptions } Options\n     * @param callback { Function } An optional callback function.\n     * @returns { Promise }\n     */\n    static createConnectionChain(options, callback) {\n        // eslint-disable-next-line no-async-promise-executor\n        return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {\n            // Validate SocksClientChainOptions\n            try {\n                (0, helpers_1.validateSocksClientChainOptions)(options);\n            }\n            catch (err) {\n                if (typeof callback === 'function') {\n                    callback(err);\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    return resolve(err); // Resolves pending promise (prevents memory leaks).\n                }\n                else {\n                    return reject(err);\n                }\n            }\n            // Shuffle proxies\n            if (options.randomizeChain) {\n                (0, util_1.shuffleArray)(options.proxies);\n            }\n            try {\n                let sock;\n                for (let i = 0; i < options.proxies.length; i++) {\n                    const nextProxy = options.proxies[i];\n                    // If we've reached the last proxy in the chain, the destination is the actual destination, otherwise it's the next proxy.\n                    const nextDestination = i === options.proxies.length - 1\n                        ? options.destination\n                        : {\n                            host: options.proxies[i + 1].host ||\n                                options.proxies[i + 1].ipaddress,\n                            port: options.proxies[i + 1].port,\n                        };\n                    // Creates the next connection in the chain.\n                    const result = yield SocksClient.createConnection({\n                        command: 'connect',\n                        proxy: nextProxy,\n                        destination: nextDestination,\n                        existing_socket: sock,\n                    });\n                    // If sock is undefined, assign it here.\n                    sock = sock || result.socket;\n                }\n                if (typeof callback === 'function') {\n                    callback(null, { socket: sock });\n                    resolve({ socket: sock }); // Resolves pending promise (prevents memory leaks).\n                }\n                else {\n                    resolve({ socket: sock });\n                }\n            }\n            catch (err) {\n                if (typeof callback === 'function') {\n                    callback(err);\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    resolve(err); // Resolves pending promise (prevents memory leaks).\n                }\n                else {\n                    reject(err);\n                }\n            }\n        }));\n    }\n    /**\n     * Creates a SOCKS UDP Frame.\n     * @param options\n     */\n    static createUDPFrame(options) {\n        const buff = new smart_buffer_1.SmartBuffer();\n        buff.writeUInt16BE(0);\n        buff.writeUInt8(options.frameNumber || 0);\n        // IPv4/IPv6/Hostname\n        if (net.isIPv4(options.remoteHost.host)) {\n            buff.writeUInt8(constants_1.Socks5HostType.IPv4);\n            buff.writeUInt32BE((0, helpers_1.ipv4ToInt32)(options.remoteHost.host));\n        }\n        else if (net.isIPv6(options.remoteHost.host)) {\n            buff.writeUInt8(constants_1.Socks5HostType.IPv6);\n            buff.writeBuffer((0, helpers_1.ipToBuffer)(options.remoteHost.host));\n        }\n        else {\n            buff.writeUInt8(constants_1.Socks5HostType.Hostname);\n            buff.writeUInt8(Buffer.byteLength(options.remoteHost.host));\n            buff.writeString(options.remoteHost.host);\n        }\n        // Port\n        buff.writeUInt16BE(options.remoteHost.port);\n        // Data\n        buff.writeBuffer(options.data);\n        return buff.toBuffer();\n    }\n    /**\n     * Parses a SOCKS UDP frame.\n     * @param data\n     */\n    static parseUDPFrame(data) {\n        const buff = smart_buffer_1.SmartBuffer.fromBuffer(data);\n        buff.readOffset = 2;\n        const frameNumber = buff.readUInt8();\n        const hostType = buff.readUInt8();\n        let remoteHost;\n        if (hostType === constants_1.Socks5HostType.IPv4) {\n            remoteHost = (0, helpers_1.int32ToIpv4)(buff.readUInt32BE());\n        }\n        else if (hostType === constants_1.Socks5HostType.IPv6) {\n            remoteHost = ip_address_1.Address6.fromByteArray(Array.from(buff.readBuffer(16))).canonicalForm();\n        }\n        else {\n            remoteHost = buff.readString(buff.readUInt8());\n        }\n        const remotePort = buff.readUInt16BE();\n        return {\n            frameNumber,\n            remoteHost: {\n                host: remoteHost,\n                port: remotePort,\n            },\n            data: buff.readBuffer(),\n        };\n    }\n    /**\n     * Internal state setter. If the SocksClient is in an error state, it cannot be changed to a non error state.\n     */\n    setState(newState) {\n        if (this.state !== constants_1.SocksClientState.Error) {\n            this.state = newState;\n        }\n    }\n    /**\n     * Starts the connection establishment to the proxy and destination.\n     * @param existingSocket Connected socket to use instead of creating a new one (internal use).\n     */\n    connect(existingSocket) {\n        this.onDataReceived = (data) => this.onDataReceivedHandler(data);\n        this.onClose = () => this.onCloseHandler();\n        this.onError = (err) => this.onErrorHandler(err);\n        this.onConnect = () => this.onConnectHandler();\n        // Start timeout timer (defaults to 30 seconds)\n        const timer = setTimeout(() => this.onEstablishedTimeout(), this.options.timeout || constants_1.DEFAULT_TIMEOUT);\n        // check whether unref is available as it differs from browser to NodeJS (#33)\n        if (timer.unref && typeof timer.unref === 'function') {\n            timer.unref();\n        }\n        // If an existing socket is provided, use it to negotiate SOCKS handshake. Otherwise create a new Socket.\n        if (existingSocket) {\n            this.socket = existingSocket;\n        }\n        else {\n            this.socket = new net.Socket();\n        }\n        // Attach Socket error handlers.\n        this.socket.once('close', this.onClose);\n        this.socket.once('error', this.onError);\n        this.socket.once('connect', this.onConnect);\n        this.socket.on('data', this.onDataReceived);\n        this.setState(constants_1.SocksClientState.Connecting);\n        this.receiveBuffer = new receivebuffer_1.ReceiveBuffer();\n        if (existingSocket) {\n            this.socket.emit('connect');\n        }\n        else {\n            this.socket.connect(this.getSocketOptions());\n            if (this.options.set_tcp_nodelay !== undefined &&\n                this.options.set_tcp_nodelay !== null) {\n                this.socket.setNoDelay(!!this.options.set_tcp_nodelay);\n            }\n        }\n        // Listen for established event so we can re-emit any excess data received during handshakes.\n        this.prependOnceListener('established', (info) => {\n            setImmediate(() => {\n                if (this.receiveBuffer.length > 0) {\n                    const excessData = this.receiveBuffer.get(this.receiveBuffer.length);\n                    info.socket.emit('data', excessData);\n                }\n                info.socket.resume();\n            });\n        });\n    }\n    // Socket options (defaults host/port to options.proxy.host/options.proxy.port)\n    getSocketOptions() {\n        return Object.assign(Object.assign({}, this.options.socket_options), { host: this.options.proxy.host || this.options.proxy.ipaddress, port: this.options.proxy.port });\n    }\n    /**\n     * Handles internal Socks timeout callback.\n     * Note: If the Socks client is not BoundWaitingForConnection or Established, the connection will be closed.\n     */\n    onEstablishedTimeout() {\n        if (this.state !== constants_1.SocksClientState.Established &&\n            this.state !== constants_1.SocksClientState.BoundWaitingForConnection) {\n            this.closeSocket(constants_1.ERRORS.ProxyConnectionTimedOut);\n        }\n    }\n    /**\n     * Handles Socket connect event.\n     */\n    onConnectHandler() {\n        this.setState(constants_1.SocksClientState.Connected);\n        // Send initial handshake.\n        if (this.options.proxy.type === 4) {\n            this.sendSocks4InitialHandshake();\n        }\n        else {\n            this.sendSocks5InitialHandshake();\n        }\n        this.setState(constants_1.SocksClientState.SentInitialHandshake);\n    }\n    /**\n     * Handles Socket data event.\n     * @param data\n     */\n    onDataReceivedHandler(data) {\n        /*\n          All received data is appended to a ReceiveBuffer.\n          This makes sure that all the data we need is received before we attempt to process it.\n        */\n        this.receiveBuffer.append(data);\n        // Process data that we have.\n        this.processData();\n    }\n    /**\n     * Handles processing of the data we have received.\n     */\n    processData() {\n        // If we have enough data to process the next step in the SOCKS handshake, proceed.\n        while (this.state !== constants_1.SocksClientState.Established &&\n            this.state !== constants_1.SocksClientState.Error &&\n            this.receiveBuffer.length >= this.nextRequiredPacketBufferSize) {\n            // Sent initial handshake, waiting for response.\n            if (this.state === constants_1.SocksClientState.SentInitialHandshake) {\n                if (this.options.proxy.type === 4) {\n                    // Socks v4 only has one handshake response.\n                    this.handleSocks4FinalHandshakeResponse();\n                }\n                else {\n                    // Socks v5 has two handshakes, handle initial one here.\n                    this.handleInitialSocks5HandshakeResponse();\n                }\n                // Sent auth request for Socks v5, waiting for response.\n            }\n            else if (this.state === constants_1.SocksClientState.SentAuthentication) {\n                this.handleInitialSocks5AuthenticationHandshakeResponse();\n                // Sent final Socks v5 handshake, waiting for final response.\n            }\n            else if (this.state === constants_1.SocksClientState.SentFinalHandshake) {\n                this.handleSocks5FinalHandshakeResponse();\n                // Socks BIND established. Waiting for remote connection via proxy.\n            }\n            else if (this.state === constants_1.SocksClientState.BoundWaitingForConnection) {\n                if (this.options.proxy.type === 4) {\n                    this.handleSocks4IncomingConnectionResponse();\n                }\n                else {\n                    this.handleSocks5IncomingConnectionResponse();\n                }\n            }\n            else {\n                this.closeSocket(constants_1.ERRORS.InternalError);\n                break;\n            }\n        }\n    }\n    /**\n     * Handles Socket close event.\n     * @param had_error\n     */\n    onCloseHandler() {\n        this.closeSocket(constants_1.ERRORS.SocketClosed);\n    }\n    /**\n     * Handles Socket error event.\n     * @param err\n     */\n    onErrorHandler(err) {\n        this.closeSocket(err.message);\n    }\n    /**\n     * Removes internal event listeners on the underlying Socket.\n     */\n    removeInternalSocketHandlers() {\n        // Pauses data flow of the socket (this is internally resumed after 'established' is emitted)\n        this.socket.pause();\n        this.socket.removeListener('data', this.onDataReceived);\n        this.socket.removeListener('close', this.onClose);\n        this.socket.removeListener('error', this.onError);\n        this.socket.removeListener('connect', this.onConnect);\n    }\n    /**\n     * Closes and destroys the underlying Socket. Emits an error event.\n     * @param err { String } An error string to include in error event.\n     */\n    closeSocket(err) {\n        // Make sure only one 'error' event is fired for the lifetime of this SocksClient instance.\n        if (this.state !== constants_1.SocksClientState.Error) {\n            // Set internal state to Error.\n            this.setState(constants_1.SocksClientState.Error);\n            // Destroy Socket\n            this.socket.destroy();\n            // Remove internal listeners\n            this.removeInternalSocketHandlers();\n            // Fire 'error' event.\n            this.emit('error', new util_1.SocksClientError(err, this.options));\n        }\n    }\n    /**\n     * Sends initial Socks v4 handshake request.\n     */\n    sendSocks4InitialHandshake() {\n        const userId = this.options.proxy.userId || '';\n        const buff = new smart_buffer_1.SmartBuffer();\n        buff.writeUInt8(0x04);\n        buff.writeUInt8(constants_1.SocksCommand[this.options.command]);\n        buff.writeUInt16BE(this.options.destination.port);\n        // Socks 4 (IPv4)\n        if (net.isIPv4(this.options.destination.host)) {\n            buff.writeBuffer((0, helpers_1.ipToBuffer)(this.options.destination.host));\n            buff.writeStringNT(userId);\n            // Socks 4a (hostname)\n        }\n        else {\n            buff.writeUInt8(0x00);\n            buff.writeUInt8(0x00);\n            buff.writeUInt8(0x00);\n            buff.writeUInt8(0x01);\n            buff.writeStringNT(userId);\n            buff.writeStringNT(this.options.destination.host);\n        }\n        this.nextRequiredPacketBufferSize =\n            constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks4Response;\n        this.socket.write(buff.toBuffer());\n    }\n    /**\n     * Handles Socks v4 handshake response.\n     * @param data\n     */\n    handleSocks4FinalHandshakeResponse() {\n        const data = this.receiveBuffer.get(8);\n        if (data[1] !== constants_1.Socks4Response.Granted) {\n            this.closeSocket(`${constants_1.ERRORS.Socks4ProxyRejectedConnection} - (${constants_1.Socks4Response[data[1]]})`);\n        }\n        else {\n            // Bind response\n            if (constants_1.SocksCommand[this.options.command] === constants_1.SocksCommand.bind) {\n                const buff = smart_buffer_1.SmartBuffer.fromBuffer(data);\n                buff.readOffset = 2;\n                const remoteHost = {\n                    port: buff.readUInt16BE(),\n                    host: (0, helpers_1.int32ToIpv4)(buff.readUInt32BE()),\n                };\n                // If host is 0.0.0.0, set to proxy host.\n                if (remoteHost.host === '0.0.0.0') {\n                    remoteHost.host = this.options.proxy.ipaddress;\n                }\n                this.setState(constants_1.SocksClientState.BoundWaitingForConnection);\n                this.emit('bound', { remoteHost, socket: this.socket });\n                // Connect response\n            }\n            else {\n                this.setState(constants_1.SocksClientState.Established);\n                this.removeInternalSocketHandlers();\n                this.emit('established', { socket: this.socket });\n            }\n        }\n    }\n    /**\n     * Handles Socks v4 incoming connection request (BIND)\n     * @param data\n     */\n    handleSocks4IncomingConnectionResponse() {\n        const data = this.receiveBuffer.get(8);\n        if (data[1] !== constants_1.Socks4Response.Granted) {\n            this.closeSocket(`${constants_1.ERRORS.Socks4ProxyRejectedIncomingBoundConnection} - (${constants_1.Socks4Response[data[1]]})`);\n        }\n        else {\n            const buff = smart_buffer_1.SmartBuffer.fromBuffer(data);\n            buff.readOffset = 2;\n            const remoteHost = {\n                port: buff.readUInt16BE(),\n                host: (0, helpers_1.int32ToIpv4)(buff.readUInt32BE()),\n            };\n            this.setState(constants_1.SocksClientState.Established);\n            this.removeInternalSocketHandlers();\n            this.emit('established', { remoteHost, socket: this.socket });\n        }\n    }\n    /**\n     * Sends initial Socks v5 handshake request.\n     */\n    sendSocks5InitialHandshake() {\n        const buff = new smart_buffer_1.SmartBuffer();\n        // By default we always support no auth.\n        const supportedAuthMethods = [constants_1.Socks5Auth.NoAuth];\n        // We should only tell the proxy we support user/pass auth if auth info is actually provided.\n        // Note: As of Tor v0.3.5.7+, if user/pass auth is an option from the client, by default it will always take priority.\n        if (this.options.proxy.userId || this.options.proxy.password) {\n            supportedAuthMethods.push(constants_1.Socks5Auth.UserPass);\n        }\n        // Custom auth method?\n        if (this.options.proxy.custom_auth_method !== undefined) {\n            supportedAuthMethods.push(this.options.proxy.custom_auth_method);\n        }\n        // Build handshake packet\n        buff.writeUInt8(0x05);\n        buff.writeUInt8(supportedAuthMethods.length);\n        for (const authMethod of supportedAuthMethods) {\n            buff.writeUInt8(authMethod);\n        }\n        this.nextRequiredPacketBufferSize =\n            constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5InitialHandshakeResponse;\n        this.socket.write(buff.toBuffer());\n        this.setState(constants_1.SocksClientState.SentInitialHandshake);\n    }\n    /**\n     * Handles initial Socks v5 handshake response.\n     * @param data\n     */\n    handleInitialSocks5HandshakeResponse() {\n        const data = this.receiveBuffer.get(2);\n        if (data[0] !== 0x05) {\n            this.closeSocket(constants_1.ERRORS.InvalidSocks5IntiailHandshakeSocksVersion);\n        }\n        else if (data[1] === constants_1.SOCKS5_NO_ACCEPTABLE_AUTH) {\n            this.closeSocket(constants_1.ERRORS.InvalidSocks5InitialHandshakeNoAcceptedAuthType);\n        }\n        else {\n            // If selected Socks v5 auth method is no auth, send final handshake request.\n            if (data[1] === constants_1.Socks5Auth.NoAuth) {\n                this.socks5ChosenAuthType = constants_1.Socks5Auth.NoAuth;\n                this.sendSocks5CommandRequest();\n                // If selected Socks v5 auth method is user/password, send auth handshake.\n            }\n            else if (data[1] === constants_1.Socks5Auth.UserPass) {\n                this.socks5ChosenAuthType = constants_1.Socks5Auth.UserPass;\n                this.sendSocks5UserPassAuthentication();\n                // If selected Socks v5 auth method is the custom_auth_method, send custom handshake.\n            }\n            else if (data[1] === this.options.proxy.custom_auth_method) {\n                this.socks5ChosenAuthType = this.options.proxy.custom_auth_method;\n                this.sendSocks5CustomAuthentication();\n            }\n            else {\n                this.closeSocket(constants_1.ERRORS.InvalidSocks5InitialHandshakeUnknownAuthType);\n            }\n        }\n    }\n    /**\n     * Sends Socks v5 user & password auth handshake.\n     *\n     * Note: No auth and user/pass are currently supported.\n     */\n    sendSocks5UserPassAuthentication() {\n        const userId = this.options.proxy.userId || '';\n        const password = this.options.proxy.password || '';\n        const buff = new smart_buffer_1.SmartBuffer();\n        buff.writeUInt8(0x01);\n        buff.writeUInt8(Buffer.byteLength(userId));\n        buff.writeString(userId);\n        buff.writeUInt8(Buffer.byteLength(password));\n        buff.writeString(password);\n        this.nextRequiredPacketBufferSize =\n            constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5UserPassAuthenticationResponse;\n        this.socket.write(buff.toBuffer());\n        this.setState(constants_1.SocksClientState.SentAuthentication);\n    }\n    sendSocks5CustomAuthentication() {\n        return __awaiter(this, void 0, void 0, function* () {\n            this.nextRequiredPacketBufferSize =\n                this.options.proxy.custom_auth_response_size;\n            this.socket.write(yield this.options.proxy.custom_auth_request_handler());\n            this.setState(constants_1.SocksClientState.SentAuthentication);\n        });\n    }\n    handleSocks5CustomAuthHandshakeResponse(data) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield this.options.proxy.custom_auth_response_handler(data);\n        });\n    }\n    handleSocks5AuthenticationNoAuthHandshakeResponse(data) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return data[1] === 0x00;\n        });\n    }\n    handleSocks5AuthenticationUserPassHandshakeResponse(data) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return data[1] === 0x00;\n        });\n    }\n    /**\n     * Handles Socks v5 auth handshake response.\n     * @param data\n     */\n    handleInitialSocks5AuthenticationHandshakeResponse() {\n        return __awaiter(this, void 0, void 0, function* () {\n            this.setState(constants_1.SocksClientState.ReceivedAuthenticationResponse);\n            let authResult = false;\n            if (this.socks5ChosenAuthType === constants_1.Socks5Auth.NoAuth) {\n                authResult = yield this.handleSocks5AuthenticationNoAuthHandshakeResponse(this.receiveBuffer.get(2));\n            }\n            else if (this.socks5ChosenAuthType === constants_1.Socks5Auth.UserPass) {\n                authResult =\n                    yield this.handleSocks5AuthenticationUserPassHandshakeResponse(this.receiveBuffer.get(2));\n            }\n            else if (this.socks5ChosenAuthType === this.options.proxy.custom_auth_method) {\n                authResult = yield this.handleSocks5CustomAuthHandshakeResponse(this.receiveBuffer.get(this.options.proxy.custom_auth_response_size));\n            }\n            if (!authResult) {\n                this.closeSocket(constants_1.ERRORS.Socks5AuthenticationFailed);\n            }\n            else {\n                this.sendSocks5CommandRequest();\n            }\n        });\n    }\n    /**\n     * Sends Socks v5 final handshake request.\n     */\n    sendSocks5CommandRequest() {\n        const buff = new smart_buffer_1.SmartBuffer();\n        buff.writeUInt8(0x05);\n        buff.writeUInt8(constants_1.SocksCommand[this.options.command]);\n        buff.writeUInt8(0x00);\n        // ipv4, ipv6, domain?\n        if (net.isIPv4(this.options.destination.host)) {\n            buff.writeUInt8(constants_1.Socks5HostType.IPv4);\n            buff.writeBuffer((0, helpers_1.ipToBuffer)(this.options.destination.host));\n        }\n        else if (net.isIPv6(this.options.destination.host)) {\n            buff.writeUInt8(constants_1.Socks5HostType.IPv6);\n            buff.writeBuffer((0, helpers_1.ipToBuffer)(this.options.destination.host));\n        }\n        else {\n            buff.writeUInt8(constants_1.Socks5HostType.Hostname);\n            buff.writeUInt8(this.options.destination.host.length);\n            buff.writeString(this.options.destination.host);\n        }\n        buff.writeUInt16BE(this.options.destination.port);\n        this.nextRequiredPacketBufferSize =\n            constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader;\n        this.socket.write(buff.toBuffer());\n        this.setState(constants_1.SocksClientState.SentFinalHandshake);\n    }\n    /**\n     * Handles Socks v5 final handshake response.\n     * @param data\n     */\n    handleSocks5FinalHandshakeResponse() {\n        // Peek at available data (we need at least 5 bytes to get the hostname length)\n        const header = this.receiveBuffer.peek(5);\n        if (header[0] !== 0x05 || header[1] !== constants_1.Socks5Response.Granted) {\n            this.closeSocket(`${constants_1.ERRORS.InvalidSocks5FinalHandshakeRejected} - ${constants_1.Socks5Response[header[1]]}`);\n        }\n        else {\n            // Read address type\n            const addressType = header[3];\n            let remoteHost;\n            let buff;\n            // IPv4\n            if (addressType === constants_1.Socks5HostType.IPv4) {\n                // Check if data is available.\n                const dataNeeded = constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;\n                if (this.receiveBuffer.length < dataNeeded) {\n                    this.nextRequiredPacketBufferSize = dataNeeded;\n                    return;\n                }\n                buff = smart_buffer_1.SmartBuffer.fromBuffer(this.receiveBuffer.get(dataNeeded).slice(4));\n                remoteHost = {\n                    host: (0, helpers_1.int32ToIpv4)(buff.readUInt32BE()),\n                    port: buff.readUInt16BE(),\n                };\n                // If given host is 0.0.0.0, assume remote proxy ip instead.\n                if (remoteHost.host === '0.0.0.0') {\n                    remoteHost.host = this.options.proxy.ipaddress;\n                }\n                // Hostname\n            }\n            else if (addressType === constants_1.Socks5HostType.Hostname) {\n                const hostLength = header[4];\n                const dataNeeded = constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(hostLength); // header + host length + host + port\n                // Check if data is available.\n                if (this.receiveBuffer.length < dataNeeded) {\n                    this.nextRequiredPacketBufferSize = dataNeeded;\n                    return;\n                }\n                buff = smart_buffer_1.SmartBuffer.fromBuffer(this.receiveBuffer.get(dataNeeded).slice(5));\n                remoteHost = {\n                    host: buff.readString(hostLength),\n                    port: buff.readUInt16BE(),\n                };\n                // IPv6\n            }\n            else if (addressType === constants_1.Socks5HostType.IPv6) {\n                // Check if data is available.\n                const dataNeeded = constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;\n                if (this.receiveBuffer.length < dataNeeded) {\n                    this.nextRequiredPacketBufferSize = dataNeeded;\n                    return;\n                }\n                buff = smart_buffer_1.SmartBuffer.fromBuffer(this.receiveBuffer.get(dataNeeded).slice(4));\n                remoteHost = {\n                    host: ip_address_1.Address6.fromByteArray(Array.from(buff.readBuffer(16))).canonicalForm(),\n                    port: buff.readUInt16BE(),\n                };\n            }\n            // We have everything we need\n            this.setState(constants_1.SocksClientState.ReceivedFinalResponse);\n            // If using CONNECT, the client is now in the established state.\n            if (constants_1.SocksCommand[this.options.command] === constants_1.SocksCommand.connect) {\n                this.setState(constants_1.SocksClientState.Established);\n                this.removeInternalSocketHandlers();\n                this.emit('established', { remoteHost, socket: this.socket });\n            }\n            else if (constants_1.SocksCommand[this.options.command] === constants_1.SocksCommand.bind) {\n                /* If using BIND, the Socks client is now in BoundWaitingForConnection state.\n                   This means that the remote proxy server is waiting for a remote connection to the bound port. */\n                this.setState(constants_1.SocksClientState.BoundWaitingForConnection);\n                this.nextRequiredPacketBufferSize =\n                    constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader;\n                this.emit('bound', { remoteHost, socket: this.socket });\n                /*\n                  If using Associate, the Socks client is now Established. And the proxy server is now accepting UDP packets at the\n                  given bound port. This initial Socks TCP connection must remain open for the UDP relay to continue to work.\n                */\n            }\n            else if (constants_1.SocksCommand[this.options.command] === constants_1.SocksCommand.associate) {\n                this.setState(constants_1.SocksClientState.Established);\n                this.removeInternalSocketHandlers();\n                this.emit('established', {\n                    remoteHost,\n                    socket: this.socket,\n                });\n            }\n        }\n    }\n    /**\n     * Handles Socks v5 incoming connection request (BIND).\n     */\n    handleSocks5IncomingConnectionResponse() {\n        // Peek at available data (we need at least 5 bytes to get the hostname length)\n        const header = this.receiveBuffer.peek(5);\n        if (header[0] !== 0x05 || header[1] !== constants_1.Socks5Response.Granted) {\n            this.closeSocket(`${constants_1.ERRORS.Socks5ProxyRejectedIncomingBoundConnection} - ${constants_1.Socks5Response[header[1]]}`);\n        }\n        else {\n            // Read address type\n            const addressType = header[3];\n            let remoteHost;\n            let buff;\n            // IPv4\n            if (addressType === constants_1.Socks5HostType.IPv4) {\n                // Check if data is available.\n                const dataNeeded = constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;\n                if (this.receiveBuffer.length < dataNeeded) {\n                    this.nextRequiredPacketBufferSize = dataNeeded;\n                    return;\n                }\n                buff = smart_buffer_1.SmartBuffer.fromBuffer(this.receiveBuffer.get(dataNeeded).slice(4));\n                remoteHost = {\n                    host: (0, helpers_1.int32ToIpv4)(buff.readUInt32BE()),\n                    port: buff.readUInt16BE(),\n                };\n                // If given host is 0.0.0.0, assume remote proxy ip instead.\n                if (remoteHost.host === '0.0.0.0') {\n                    remoteHost.host = this.options.proxy.ipaddress;\n                }\n                // Hostname\n            }\n            else if (addressType === constants_1.Socks5HostType.Hostname) {\n                const hostLength = header[4];\n                const dataNeeded = constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(hostLength); // header + host length + port\n                // Check if data is available.\n                if (this.receiveBuffer.length < dataNeeded) {\n                    this.nextRequiredPacketBufferSize = dataNeeded;\n                    return;\n                }\n                buff = smart_buffer_1.SmartBuffer.fromBuffer(this.receiveBuffer.get(dataNeeded).slice(5));\n                remoteHost = {\n                    host: buff.readString(hostLength),\n                    port: buff.readUInt16BE(),\n                };\n                // IPv6\n            }\n            else if (addressType === constants_1.Socks5HostType.IPv6) {\n                // Check if data is available.\n                const dataNeeded = constants_1.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;\n                if (this.receiveBuffer.length < dataNeeded) {\n                    this.nextRequiredPacketBufferSize = dataNeeded;\n                    return;\n                }\n                buff = smart_buffer_1.SmartBuffer.fromBuffer(this.receiveBuffer.get(dataNeeded).slice(4));\n                remoteHost = {\n                    host: ip_address_1.Address6.fromByteArray(Array.from(buff.readBuffer(16))).canonicalForm(),\n                    port: buff.readUInt16BE(),\n                };\n            }\n            this.setState(constants_1.SocksClientState.Established);\n            this.removeInternalSocketHandlers();\n            this.emit('established', { remoteHost, socket: this.socket });\n        }\n    }\n    get socksClientOptions() {\n        return Object.assign({}, this.options);\n    }\n}\nexports.SocksClient = SocksClient;\n//# sourceMappingURL=socksclient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socks/build/client/socksclient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socks/build/common/constants.js":
/*!******************************************************!*\
  !*** ./node_modules/socks/build/common/constants.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SOCKS5_NO_ACCEPTABLE_AUTH = exports.SOCKS5_CUSTOM_AUTH_END = exports.SOCKS5_CUSTOM_AUTH_START = exports.SOCKS_INCOMING_PACKET_SIZES = exports.SocksClientState = exports.Socks5Response = exports.Socks5HostType = exports.Socks5Auth = exports.Socks4Response = exports.SocksCommand = exports.ERRORS = exports.DEFAULT_TIMEOUT = void 0;\nconst DEFAULT_TIMEOUT = 30000;\nexports.DEFAULT_TIMEOUT = DEFAULT_TIMEOUT;\n// prettier-ignore\nconst ERRORS = {\n    InvalidSocksCommand: 'An invalid SOCKS command was provided. Valid options are connect, bind, and associate.',\n    InvalidSocksCommandForOperation: 'An invalid SOCKS command was provided. Only a subset of commands are supported for this operation.',\n    InvalidSocksCommandChain: 'An invalid SOCKS command was provided. Chaining currently only supports the connect command.',\n    InvalidSocksClientOptionsDestination: 'An invalid destination host was provided.',\n    InvalidSocksClientOptionsExistingSocket: 'An invalid existing socket was provided. This should be an instance of stream.Duplex.',\n    InvalidSocksClientOptionsProxy: 'Invalid SOCKS proxy details were provided.',\n    InvalidSocksClientOptionsTimeout: 'An invalid timeout value was provided. Please enter a value above 0 (in ms).',\n    InvalidSocksClientOptionsProxiesLength: 'At least two socks proxies must be provided for chaining.',\n    InvalidSocksClientOptionsCustomAuthRange: 'Custom auth must be a value between 0x80 and 0xFE.',\n    InvalidSocksClientOptionsCustomAuthOptions: 'When a custom_auth_method is provided, custom_auth_request_handler, custom_auth_response_size, and custom_auth_response_handler must also be provided and valid.',\n    NegotiationError: 'Negotiation error',\n    SocketClosed: 'Socket closed',\n    ProxyConnectionTimedOut: 'Proxy connection timed out',\n    InternalError: 'SocksClient internal error (this should not happen)',\n    InvalidSocks4HandshakeResponse: 'Received invalid Socks4 handshake response',\n    Socks4ProxyRejectedConnection: 'Socks4 Proxy rejected connection',\n    InvalidSocks4IncomingConnectionResponse: 'Socks4 invalid incoming connection response',\n    Socks4ProxyRejectedIncomingBoundConnection: 'Socks4 Proxy rejected incoming bound connection',\n    InvalidSocks5InitialHandshakeResponse: 'Received invalid Socks5 initial handshake response',\n    InvalidSocks5IntiailHandshakeSocksVersion: 'Received invalid Socks5 initial handshake (invalid socks version)',\n    InvalidSocks5InitialHandshakeNoAcceptedAuthType: 'Received invalid Socks5 initial handshake (no accepted authentication type)',\n    InvalidSocks5InitialHandshakeUnknownAuthType: 'Received invalid Socks5 initial handshake (unknown authentication type)',\n    Socks5AuthenticationFailed: 'Socks5 Authentication failed',\n    InvalidSocks5FinalHandshake: 'Received invalid Socks5 final handshake response',\n    InvalidSocks5FinalHandshakeRejected: 'Socks5 proxy rejected connection',\n    InvalidSocks5IncomingConnectionResponse: 'Received invalid Socks5 incoming connection response',\n    Socks5ProxyRejectedIncomingBoundConnection: 'Socks5 Proxy rejected incoming bound connection',\n};\nexports.ERRORS = ERRORS;\nconst SOCKS_INCOMING_PACKET_SIZES = {\n    Socks5InitialHandshakeResponse: 2,\n    Socks5UserPassAuthenticationResponse: 2,\n    // Command response + incoming connection (bind)\n    Socks5ResponseHeader: 5, // We need at least 5 to read the hostname length, then we wait for the address+port information.\n    Socks5ResponseIPv4: 10, // 4 header + 4 ip + 2 port\n    Socks5ResponseIPv6: 22, // 4 header + 16 ip + 2 port\n    Socks5ResponseHostname: (hostNameLength) => hostNameLength + 7, // 4 header + 1 host length + host + 2 port\n    // Command response + incoming connection (bind)\n    Socks4Response: 8, // 2 header + 2 port + 4 ip\n};\nexports.SOCKS_INCOMING_PACKET_SIZES = SOCKS_INCOMING_PACKET_SIZES;\nvar SocksCommand;\n(function (SocksCommand) {\n    SocksCommand[SocksCommand[\"connect\"] = 1] = \"connect\";\n    SocksCommand[SocksCommand[\"bind\"] = 2] = \"bind\";\n    SocksCommand[SocksCommand[\"associate\"] = 3] = \"associate\";\n})(SocksCommand || (exports.SocksCommand = SocksCommand = {}));\nvar Socks4Response;\n(function (Socks4Response) {\n    Socks4Response[Socks4Response[\"Granted\"] = 90] = \"Granted\";\n    Socks4Response[Socks4Response[\"Failed\"] = 91] = \"Failed\";\n    Socks4Response[Socks4Response[\"Rejected\"] = 92] = \"Rejected\";\n    Socks4Response[Socks4Response[\"RejectedIdent\"] = 93] = \"RejectedIdent\";\n})(Socks4Response || (exports.Socks4Response = Socks4Response = {}));\nvar Socks5Auth;\n(function (Socks5Auth) {\n    Socks5Auth[Socks5Auth[\"NoAuth\"] = 0] = \"NoAuth\";\n    Socks5Auth[Socks5Auth[\"GSSApi\"] = 1] = \"GSSApi\";\n    Socks5Auth[Socks5Auth[\"UserPass\"] = 2] = \"UserPass\";\n})(Socks5Auth || (exports.Socks5Auth = Socks5Auth = {}));\nconst SOCKS5_CUSTOM_AUTH_START = 0x80;\nexports.SOCKS5_CUSTOM_AUTH_START = SOCKS5_CUSTOM_AUTH_START;\nconst SOCKS5_CUSTOM_AUTH_END = 0xfe;\nexports.SOCKS5_CUSTOM_AUTH_END = SOCKS5_CUSTOM_AUTH_END;\nconst SOCKS5_NO_ACCEPTABLE_AUTH = 0xff;\nexports.SOCKS5_NO_ACCEPTABLE_AUTH = SOCKS5_NO_ACCEPTABLE_AUTH;\nvar Socks5Response;\n(function (Socks5Response) {\n    Socks5Response[Socks5Response[\"Granted\"] = 0] = \"Granted\";\n    Socks5Response[Socks5Response[\"Failure\"] = 1] = \"Failure\";\n    Socks5Response[Socks5Response[\"NotAllowed\"] = 2] = \"NotAllowed\";\n    Socks5Response[Socks5Response[\"NetworkUnreachable\"] = 3] = \"NetworkUnreachable\";\n    Socks5Response[Socks5Response[\"HostUnreachable\"] = 4] = \"HostUnreachable\";\n    Socks5Response[Socks5Response[\"ConnectionRefused\"] = 5] = \"ConnectionRefused\";\n    Socks5Response[Socks5Response[\"TTLExpired\"] = 6] = \"TTLExpired\";\n    Socks5Response[Socks5Response[\"CommandNotSupported\"] = 7] = \"CommandNotSupported\";\n    Socks5Response[Socks5Response[\"AddressNotSupported\"] = 8] = \"AddressNotSupported\";\n})(Socks5Response || (exports.Socks5Response = Socks5Response = {}));\nvar Socks5HostType;\n(function (Socks5HostType) {\n    Socks5HostType[Socks5HostType[\"IPv4\"] = 1] = \"IPv4\";\n    Socks5HostType[Socks5HostType[\"Hostname\"] = 3] = \"Hostname\";\n    Socks5HostType[Socks5HostType[\"IPv6\"] = 4] = \"IPv6\";\n})(Socks5HostType || (exports.Socks5HostType = Socks5HostType = {}));\nvar SocksClientState;\n(function (SocksClientState) {\n    SocksClientState[SocksClientState[\"Created\"] = 0] = \"Created\";\n    SocksClientState[SocksClientState[\"Connecting\"] = 1] = \"Connecting\";\n    SocksClientState[SocksClientState[\"Connected\"] = 2] = \"Connected\";\n    SocksClientState[SocksClientState[\"SentInitialHandshake\"] = 3] = \"SentInitialHandshake\";\n    SocksClientState[SocksClientState[\"ReceivedInitialHandshakeResponse\"] = 4] = \"ReceivedInitialHandshakeResponse\";\n    SocksClientState[SocksClientState[\"SentAuthentication\"] = 5] = \"SentAuthentication\";\n    SocksClientState[SocksClientState[\"ReceivedAuthenticationResponse\"] = 6] = \"ReceivedAuthenticationResponse\";\n    SocksClientState[SocksClientState[\"SentFinalHandshake\"] = 7] = \"SentFinalHandshake\";\n    SocksClientState[SocksClientState[\"ReceivedFinalResponse\"] = 8] = \"ReceivedFinalResponse\";\n    SocksClientState[SocksClientState[\"BoundWaitingForConnection\"] = 9] = \"BoundWaitingForConnection\";\n    SocksClientState[SocksClientState[\"Established\"] = 10] = \"Established\";\n    SocksClientState[SocksClientState[\"Disconnected\"] = 11] = \"Disconnected\";\n    SocksClientState[SocksClientState[\"Error\"] = 99] = \"Error\";\n})(SocksClientState || (exports.SocksClientState = SocksClientState = {}));\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socks/build/common/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socks/build/common/helpers.js":
/*!****************************************************!*\
  !*** ./node_modules/socks/build/common/helpers.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ipToBuffer = exports.int32ToIpv4 = exports.ipv4ToInt32 = exports.validateSocksClientChainOptions = exports.validateSocksClientOptions = void 0;\nconst util_1 = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/socks/build/common/util.js\");\nconst constants_1 = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/socks/build/common/constants.js\");\nconst stream = __webpack_require__(/*! stream */ \"stream\");\nconst ip_address_1 = __webpack_require__(/*! ip-address */ \"(ssr)/./node_modules/ip-address/dist/ip-address.js\");\nconst net = __webpack_require__(/*! net */ \"net\");\n/**\n * Validates the provided SocksClientOptions\n * @param options { SocksClientOptions }\n * @param acceptedCommands { string[] } A list of accepted SocksProxy commands.\n */\nfunction validateSocksClientOptions(options, acceptedCommands = ['connect', 'bind', 'associate']) {\n    // Check SOCKs command option.\n    if (!constants_1.SocksCommand[options.command]) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksCommand, options);\n    }\n    // Check SocksCommand for acceptable command.\n    if (acceptedCommands.indexOf(options.command) === -1) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksCommandForOperation, options);\n    }\n    // Check destination\n    if (!isValidSocksRemoteHost(options.destination)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsDestination, options);\n    }\n    // Check SOCKS proxy to use\n    if (!isValidSocksProxy(options.proxy)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsProxy, options);\n    }\n    // Validate custom auth (if set)\n    validateCustomProxyAuth(options.proxy, options);\n    // Check timeout\n    if (options.timeout && !isValidTimeoutValue(options.timeout)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsTimeout, options);\n    }\n    // Check existing_socket (if provided)\n    if (options.existing_socket &&\n        !(options.existing_socket instanceof stream.Duplex)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsExistingSocket, options);\n    }\n}\nexports.validateSocksClientOptions = validateSocksClientOptions;\n/**\n * Validates the SocksClientChainOptions\n * @param options { SocksClientChainOptions }\n */\nfunction validateSocksClientChainOptions(options) {\n    // Only connect is supported when chaining.\n    if (options.command !== 'connect') {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksCommandChain, options);\n    }\n    // Check destination\n    if (!isValidSocksRemoteHost(options.destination)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsDestination, options);\n    }\n    // Validate proxies (length)\n    if (!(options.proxies &&\n        Array.isArray(options.proxies) &&\n        options.proxies.length >= 2)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsProxiesLength, options);\n    }\n    // Validate proxies\n    options.proxies.forEach((proxy) => {\n        if (!isValidSocksProxy(proxy)) {\n            throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsProxy, options);\n        }\n        // Validate custom auth (if set)\n        validateCustomProxyAuth(proxy, options);\n    });\n    // Check timeout\n    if (options.timeout && !isValidTimeoutValue(options.timeout)) {\n        throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsTimeout, options);\n    }\n}\nexports.validateSocksClientChainOptions = validateSocksClientChainOptions;\nfunction validateCustomProxyAuth(proxy, options) {\n    if (proxy.custom_auth_method !== undefined) {\n        // Invalid auth method range\n        if (proxy.custom_auth_method < constants_1.SOCKS5_CUSTOM_AUTH_START ||\n            proxy.custom_auth_method > constants_1.SOCKS5_CUSTOM_AUTH_END) {\n            throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsCustomAuthRange, options);\n        }\n        // Missing custom_auth_request_handler\n        if (proxy.custom_auth_request_handler === undefined ||\n            typeof proxy.custom_auth_request_handler !== 'function') {\n            throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsCustomAuthOptions, options);\n        }\n        // Missing custom_auth_response_size\n        if (proxy.custom_auth_response_size === undefined) {\n            throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsCustomAuthOptions, options);\n        }\n        // Missing/invalid custom_auth_response_handler\n        if (proxy.custom_auth_response_handler === undefined ||\n            typeof proxy.custom_auth_response_handler !== 'function') {\n            throw new util_1.SocksClientError(constants_1.ERRORS.InvalidSocksClientOptionsCustomAuthOptions, options);\n        }\n    }\n}\n/**\n * Validates a SocksRemoteHost\n * @param remoteHost { SocksRemoteHost }\n */\nfunction isValidSocksRemoteHost(remoteHost) {\n    return (remoteHost &&\n        typeof remoteHost.host === 'string' &&\n        Buffer.byteLength(remoteHost.host) < 256 &&\n        typeof remoteHost.port === 'number' &&\n        remoteHost.port >= 0 &&\n        remoteHost.port <= 65535);\n}\n/**\n * Validates a SocksProxy\n * @param proxy { SocksProxy }\n */\nfunction isValidSocksProxy(proxy) {\n    return (proxy &&\n        (typeof proxy.host === 'string' || typeof proxy.ipaddress === 'string') &&\n        typeof proxy.port === 'number' &&\n        proxy.port >= 0 &&\n        proxy.port <= 65535 &&\n        (proxy.type === 4 || proxy.type === 5));\n}\n/**\n * Validates a timeout value.\n * @param value { Number }\n */\nfunction isValidTimeoutValue(value) {\n    return typeof value === 'number' && value > 0;\n}\nfunction ipv4ToInt32(ip) {\n    const address = new ip_address_1.Address4(ip);\n    // Convert the IPv4 address parts to an integer\n    return address.toArray().reduce((acc, part) => (acc << 8) + part, 0) >>> 0;\n}\nexports.ipv4ToInt32 = ipv4ToInt32;\nfunction int32ToIpv4(int32) {\n    // Extract each byte (octet) from the 32-bit integer\n    const octet1 = (int32 >>> 24) & 0xff;\n    const octet2 = (int32 >>> 16) & 0xff;\n    const octet3 = (int32 >>> 8) & 0xff;\n    const octet4 = int32 & 0xff;\n    // Combine the octets into a string in IPv4 format\n    return [octet1, octet2, octet3, octet4].join('.');\n}\nexports.int32ToIpv4 = int32ToIpv4;\nfunction ipToBuffer(ip) {\n    if (net.isIPv4(ip)) {\n        // Handle IPv4 addresses\n        const address = new ip_address_1.Address4(ip);\n        return Buffer.from(address.toArray());\n    }\n    else if (net.isIPv6(ip)) {\n        // Handle IPv6 addresses\n        const address = new ip_address_1.Address6(ip);\n        return Buffer.from(address\n            .canonicalForm()\n            .split(':')\n            .map((segment) => segment.padStart(4, '0'))\n            .join(''), 'hex');\n    }\n    else {\n        throw new Error('Invalid IP address format');\n    }\n}\nexports.ipToBuffer = ipToBuffer;\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socks/build/common/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socks/build/common/receivebuffer.js":
/*!**********************************************************!*\
  !*** ./node_modules/socks/build/common/receivebuffer.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ReceiveBuffer = void 0;\nclass ReceiveBuffer {\n    constructor(size = 4096) {\n        this.buffer = Buffer.allocUnsafe(size);\n        this.offset = 0;\n        this.originalSize = size;\n    }\n    get length() {\n        return this.offset;\n    }\n    append(data) {\n        if (!Buffer.isBuffer(data)) {\n            throw new Error('Attempted to append a non-buffer instance to ReceiveBuffer.');\n        }\n        if (this.offset + data.length >= this.buffer.length) {\n            const tmp = this.buffer;\n            this.buffer = Buffer.allocUnsafe(Math.max(this.buffer.length + this.originalSize, this.buffer.length + data.length));\n            tmp.copy(this.buffer);\n        }\n        data.copy(this.buffer, this.offset);\n        return (this.offset += data.length);\n    }\n    peek(length) {\n        if (length > this.offset) {\n            throw new Error('Attempted to read beyond the bounds of the managed internal data.');\n        }\n        return this.buffer.slice(0, length);\n    }\n    get(length) {\n        if (length > this.offset) {\n            throw new Error('Attempted to read beyond the bounds of the managed internal data.');\n        }\n        const value = Buffer.allocUnsafe(length);\n        this.buffer.slice(0, length).copy(value);\n        this.buffer.copyWithin(0, length, length + this.offset - length);\n        this.offset -= length;\n        return value;\n    }\n}\nexports.ReceiveBuffer = ReceiveBuffer;\n//# sourceMappingURL=receivebuffer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socks/build/common/receivebuffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socks/build/common/util.js":
/*!*************************************************!*\
  !*** ./node_modules/socks/build/common/util.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.shuffleArray = exports.SocksClientError = void 0;\n/**\n * Error wrapper for SocksClient\n */\nclass SocksClientError extends Error {\n    constructor(message, options) {\n        super(message);\n        this.options = options;\n    }\n}\nexports.SocksClientError = SocksClientError;\n/**\n * Shuffles a given array.\n * @param array The array to shuffle.\n */\nfunction shuffleArray(array) {\n    for (let i = array.length - 1; i > 0; i--) {\n        const j = Math.floor(Math.random() * (i + 1));\n        [array[i], array[j]] = [array[j], array[i]];\n    }\n}\nexports.shuffleArray = shuffleArray;\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja3MvYnVpbGQvY29tbW9uL3V0aWwuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CLEdBQUcsd0JBQXdCO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLE9BQU87QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9zb2Nrcy9idWlsZC9jb21tb24vdXRpbC5qcz8wNTJhIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5zaHVmZmxlQXJyYXkgPSBleHBvcnRzLlNvY2tzQ2xpZW50RXJyb3IgPSB2b2lkIDA7XG4vKipcbiAqIEVycm9yIHdyYXBwZXIgZm9yIFNvY2tzQ2xpZW50XG4gKi9cbmNsYXNzIFNvY2tzQ2xpZW50RXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gICAgY29uc3RydWN0b3IobWVzc2FnZSwgb3B0aW9ucykge1xuICAgICAgICBzdXBlcihtZXNzYWdlKTtcbiAgICAgICAgdGhpcy5vcHRpb25zID0gb3B0aW9ucztcbiAgICB9XG59XG5leHBvcnRzLlNvY2tzQ2xpZW50RXJyb3IgPSBTb2Nrc0NsaWVudEVycm9yO1xuLyoqXG4gKiBTaHVmZmxlcyBhIGdpdmVuIGFycmF5LlxuICogQHBhcmFtIGFycmF5IFRoZSBhcnJheSB0byBzaHVmZmxlLlxuICovXG5mdW5jdGlvbiBzaHVmZmxlQXJyYXkoYXJyYXkpIHtcbiAgICBmb3IgKGxldCBpID0gYXJyYXkubGVuZ3RoIC0gMTsgaSA+IDA7IGktLSkge1xuICAgICAgICBjb25zdCBqID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogKGkgKyAxKSk7XG4gICAgICAgIFthcnJheVtpXSwgYXJyYXlbal1dID0gW2FycmF5W2pdLCBhcnJheVtpXV07XG4gICAgfVxufVxuZXhwb3J0cy5zaHVmZmxlQXJyYXkgPSBzaHVmZmxlQXJyYXk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlsLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socks/build/common/util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socks/build/index.js":
/*!*******************************************!*\
  !*** ./node_modules/socks/build/index.js ***!
  \*******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./client/socksclient */ \"(ssr)/./node_modules/socks/build/client/socksclient.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja3MvYnVpbGQvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsb0NBQW9DO0FBQ25EO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYSxtQkFBTyxDQUFDLG9GQUFzQjtBQUMzQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3NvY2tzL2J1aWxkL2luZGV4LmpzPzMwY2UiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19jcmVhdGVCaW5kaW5nID0gKHRoaXMgJiYgdGhpcy5fX2NyZWF0ZUJpbmRpbmcpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICB2YXIgZGVzYyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IobSwgayk7XG4gICAgaWYgKCFkZXNjIHx8IChcImdldFwiIGluIGRlc2MgPyAhbS5fX2VzTW9kdWxlIDogZGVzYy53cml0YWJsZSB8fCBkZXNjLmNvbmZpZ3VyYWJsZSkpIHtcbiAgICAgIGRlc2MgPSB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24oKSB7IHJldHVybiBtW2tdOyB9IH07XG4gICAgfVxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgZGVzYyk7XG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgb1trMl0gPSBtW2tdO1xufSkpO1xudmFyIF9fZXhwb3J0U3RhciA9ICh0aGlzICYmIHRoaXMuX19leHBvcnRTdGFyKSB8fCBmdW5jdGlvbihtLCBleHBvcnRzKSB7XG4gICAgZm9yICh2YXIgcCBpbiBtKSBpZiAocCAhPT0gXCJkZWZhdWx0XCIgJiYgIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChleHBvcnRzLCBwKSkgX19jcmVhdGVCaW5kaW5nKGV4cG9ydHMsIG0sIHApO1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9jbGllbnQvc29ja3NjbGllbnRcIiksIGV4cG9ydHMpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socks/build/index.js\n");

/***/ })

};
;