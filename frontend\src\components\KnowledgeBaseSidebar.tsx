'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useLiveChat as useWsChat } from '@/contexts';
import { API_BASE_URL, getSecureToken } from '@/lib/api';
import { useSecureAuth } from '@/hooks';
import { 
  fetchAllSpaces, 
  fetchSpaceDocuments, 
  createSpace, 
  fetchAllUsers, 
  addMembersToSpace, 
  removeMembersFromSpace, 
  getSpaceWithMembers,
  apiCall,
  deleteSpace,
  createTextDocument,
  uploadDocument,
  bulkUploadDocuments,
  retryBulkUpload,
  getDocument,
  deleteDocument,
  indexDocument,
  indexAllDocuments,
  downloadDocumentAttachment
} from '@/api';
import { 
  getPlaygroundLabels, 
  getPlaygroundAcceptAttribute, 
  isFileAllowedForPlayground,
  canAddTextDocument 
} from '@/utils/playgroundLabels';
import type {
  UserSpace,
  KnowledgeDocument,
  BulkUploadResponse,
  IndexingResponse,
  CreateSpaceRequest
} from '@/types/knowledge-base';
import type { User } from '@/types';

interface KnowledgeBaseSidebarProps {
  onSidebarToggle?: (isOpen: boolean) => void;
}

export default function KnowledgeBaseSidebar({ onSidebarToggle }: KnowledgeBaseSidebarProps) {
  // Helper function to validate document file types
  const isValidDocumentFile = (file: File): boolean => {
    const validTypes = ['pdf', 'officedocument.wordprocessingml.document', 'msword', 'ms-powerpoint', 'officedocument.presentationml.presentation', 'ms-excel', 'officedocument.spreadsheetml.sheet'];
    const validExts = ['.pdf', '.docx', '.doc', '.ppt', '.pptx', '.xls', '.xlsx'];
    return validTypes.some(type => file.type.includes(type)) || validExts.some(ext => file.name.toLowerCase().endsWith(ext));
  };

  const [isOpen, setIsOpen] = useState(false);
  const [expandedSpaces, setExpandedSpaces] = useState<Set<number>>(new Set());
  const [spaceDocuments, setSpaceDocuments] = useState<Map<number, KnowledgeDocument[]>>(new Map());
  const [selectedSpaceId, setSelectedSpaceId] = useState<number | null>(null);
  const [openDropdownDocId, setOpenDropdownDocId] = useState<number | null>(null);
  const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number }>({ top: 0, left: 0 });
  const [isUploded, setIsUploded] = useState(false)
  const [showCreateSpaceForm, setShowCreateSpaceForm] = useState(false);
  const [showAddTextForm, setShowAddTextForm] = useState(false);
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [showBulkUploadForm, setShowBulkUploadForm] = useState(false);
  const [showManageMembers, setShowManageMembers] = useState<null | number>(null); // spaceId
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [selectedMemberIds, setSelectedMemberIds] = useState<number[]>([]);
  const [selectedMemberIdsToRemove, setSelectedMemberIdsToRemove] = useState<number[]>([]);
  const [memberSearch, setMemberSearch] = useState<string>('');
  const [memberSearchResult, setMemberSearchResult] = useState<any | null>(null);
  const [memberSearchSearched, setMemberSearchSearched] = useState<boolean>(false);
  const [currentSpaceMembers, setCurrentSpaceMembers] = useState<any[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<KnowledgeDocument | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [loadingPdf, setLoadingPdf] = useState(false);
  const [bulkUploadResult, setBulkUploadResult] = useState<BulkUploadResponse | null>(null);
  const [showFullNameModal, setShowFullNameModal] = useState<string | null>(null);
  // Modal state for duplicate file confirmation during bulk upload
  const [duplicateModalState, setDuplicateModalState] = useState<{
    files: File[];
    info: any[];
    spaceId: number;
    initialUploaded: number;
    resolve: (value: any) => void;
  } | null>(null);
  // For single-file upload duplicate confirmation
  const [singleDuplicateState, setSingleDuplicateState] = useState<{
    indexed: boolean;
    resolve: (allow: boolean) => void;
  } | null>(null);

  // State for inline space creation
  const [showInlineCreateSpace, setShowInlineCreateSpace] = useState<boolean>(false);
  const [inlineSpaceForm, setInlineSpaceForm] = useState({
    name: '',
    description: '',
    space_type: 'personal' as 'personal' | 'shared',
    playground_type: 'documents' as 'documents' | 'images', // NEW: Playground type
    create_group: false,
    sourceModal: '' // to track which modal initiated space creation
  });

  // Form data
  const [spaceForm, setSpaceForm] = useState({
    name: '',
    description: '',
    space_type: 'personal' as 'personal' | 'shared',
    playground_type: 'documents' as 'documents' | 'images', // NEW: Playground type
    create_group: false,
  });

  const [textForm, setTextForm] = useState({
    title: '',
    description: '',
    space_id: 0,
    document_type: 'text',
    content: '',
  });

  const [uploadForm, setUploadForm] = useState({
    description: '',
    space_id: 0,
  });
  const [indexAfterUpload, setIndexAfterUpload] = useState<boolean>(true);

  const [bulkUploadSpaceId, setBulkUploadSpaceId] = useState(0);
  const [indexingDocuments, setIndexingDocuments] = useState<Set<number>>(new Set());

  // Refs for file inputs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const bulkFileInputRef = useRef<HTMLInputElement>(null);
  const docxPreviewRef = useRef<HTMLDivElement | null>(null);
  const [docxZoom, setDocxZoom] = useState<number>(0.7); // start slightly zoomed out
  const [docxBaseScale, setDocxBaseScale] = useState<number>(1);
  const docxContainerRef = useRef<HTMLDivElement | null>(null);

  // Chat context for integration
  const { getTokenOrRedirect, getUserData } = useSecureAuth();
  // WS chat context for ingestion progress stream
  const wsChat = useWsChat();
  const ingestionProgress = wsChat.state.ingestionProgress || {};
  const [showStatusModal, setShowStatusModal] = useState<boolean>(false);
  const [statusFilter, setStatusFilter] = useState<{ filename?: string; documentId?: number } | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [spaces, setSpaces] = useState<UserSpace[]>([]);
  // Toggle this flag to force re-fetch of spaces
  const [loadingSpaces, setLoadingSpaces] = useState<boolean>(false);
  const [selectedSpace, setSelectedSpace] = useState<UserSpace | null>(null);

  const [documents, setDocuments] = useState<KnowledgeDocument[]>([]);
  const [selectedDocType, setSelectedDocType] = useState<string>('');
  const [loading, setLoading] = useState(true);
  // Notify parent component when sidebar visibility changes

  useEffect(() => {

    if (onSidebarToggle) {
      onSidebarToggle(isOpen);
    }
  }, [isOpen, onSidebarToggle]);

  // Allow external components to open this sidebar via a custom event
  useEffect(() => {
    const handler = () => setIsOpen(true);
    window.addEventListener('open-knowledge-base', handler as EventListener);
    return () => window.removeEventListener('open-knowledge-base', handler as EventListener);
  }, []);

  // Close this sidebar when others open
  useEffect(() => {
    const handler = () => setIsOpen(false);
    window.addEventListener('close-knowledge-base', handler as EventListener);
    return () => window.removeEventListener('close-knowledge-base', handler as EventListener);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('[data-dropdown-container]')) {
        setOpenDropdownDocId(null);
      }
    };
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);



  useEffect(() => {
    // Create async function inside useEffect
    const loadSpaces = async () => {
      try {
        const data = await fetchAllSpaces();
        if (data) setSpaces(data);
      } catch (error) {
        console.error('Error fetching spaces:', error);
      }
    };

    // Call the async function
    loadSpaces();
  }, [loadingSpaces]);

  // Listen for global revalidation trigger to refresh spaces list in real-time
  useEffect(() => {
    const handler = () => {
      setLoadingSpaces(ls => !ls);
    };
    window.addEventListener('spaces-revalidate', handler as EventListener);
    return () => window.removeEventListener('spaces-revalidate', handler as EventListener);
  }, []);

  useEffect(() => {

    // Skip if no spaceId
    if (!selectedSpaceId) return;

    const loadDocuments = async () => {
      try {
        const data = await fetchSpaceDocuments(selectedSpaceId as number);
        if (data) {
          setSpaceDocuments(prev => new Map(prev).set(selectedSpaceId, data));
        }
      } catch (error) {
        console.error('Error fetching space documents:', error);
      }
    };

    loadDocuments();
  }, [selectedSpaceId]);



  const filteredProgress = useMemo(() => {
    const items: Array<{
      job_id: string;
      key: string;
      filename?: string;
      document_id?: number;
      step: string;
      status: string;
      progress_pct?: number;
      message?: string;
      updated_at: string;
    }> = [];
    Object.entries(ingestionProgress).forEach(([jobId, docsMap]) => {
      Object.entries(docsMap || {}).forEach(([docKey, ev]: any) => {
        const byFile = statusFilter?.filename && ev.filename === statusFilter.filename;
        const byId = statusFilter?.documentId && ev.document_id === statusFilter.documentId;
        const match = !statusFilter || byFile || byId;
        if (match) {
          items.push({
            job_id: jobId,
            key: `${jobId}:${docKey}`,
            filename: ev.filename,
            document_id: ev.document_id,
            step: ev.step,
            status: ev.status,
            progress_pct: typeof ev.progress_pct === 'number' ? ev.progress_pct : (ev.progress_pct ? parseFloat(ev.progress_pct) : undefined),
            message: ev.message,
            updated_at: ev.updated_at,
          });
        }
      });
    });
    // sort by updated time desc
    items.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
    return items;
  }, [ingestionProgress, statusFilter]);

  const handleDeleteSpace = async (spaceId: number, spaceName: string) => {
    if (!confirm(`Delete "${spaceName}"?`)) return;

    try {
      await deleteSpace(spaceId);   // backend delete + Redis invalidation
      await refreshSpaces();        // ← fresh list fetch
    } catch (e) {
      console.error('Delete failed:', e);
      alert('Failed to delete space.');
    }
  };

  // Wrapper for indexDocument with local state management
  const handleIndexDocument = async (documentId: number, spaceId?: number) => {
    const resolvedSpaceId = Number(spaceId ?? selectedSpaceId);
    if (!resolvedSpaceId || Number.isNaN(resolvedSpaceId)) {
      console.error('No space selected for indexing document');
      alert('Please select a space first');
      return;
    }

    setIndexingDocuments(prev => new Set(prev).add(documentId));

    try {
      // Set status filter for UI
      const docFromMap = (spaceDocuments.get(resolvedSpaceId) || []).find(d => d.id === documentId);
      setStatusFilter(docFromMap?.filename ? { filename: docFromMap.filename } : { documentId });
      setShowStatusModal(true);

      const result: IndexingResponse = await indexDocument(documentId, resolvedSpaceId);

      if (!result.success) {
        alert(`Failed to index document: ${result.message}`);
        return;
      }

      const nowIso = new Date().toISOString();

      setSpaceDocuments(prev => {
        const map = new Map(prev);
        const arr = map.get(resolvedSpaceId) || [];
        map.set(
          resolvedSpaceId,
          arr.map(d => d.id === documentId ? { ...d, indexed: true, indexed_at: nowIso } : d)
        );
        return map;
      });

      setDocuments(prev =>
        prev.map(d => d.id === documentId ? { ...d, indexed: true, indexed_at: nowIso } : d)
      );

    } catch (error) {
      console.error('Error indexing document:', error);
      alert('Failed to index document. Please try again.');
    } finally {
      setIndexingDocuments(prev => {
        const ns = new Set(prev);
        ns.delete(documentId);
        return ns;
      });
    }
  };


  // Memoized filtered documents
  const filteredDocuments = React.useMemo(() => {
    return documents.filter(doc => {
      if (selectedDocType && doc.document_type !== selectedDocType) return false;
      return true;
    });
  }, [documents, selectedDocType]);

  useEffect(() => {
    // Check authentication and fetch initial data
    const token = getTokenOrRedirect();
    const userData = getUserData();

    if (!token || !userData) {
      return; // Hooks will handle redirect
    }

    setUser(userData);

  }, []);

  const handleViewDocument = useCallback(async (documentId: number) => {
    try {
      const response = await getDocument(documentId);
      // //console.log("response", response);
      const document = response as unknown as KnowledgeDocument;
      setSelectedDocument(document);
    } catch (error) {
      console.error('Error loading document:', error);
      alert('Failed to load document details.');
    }
  }, []);

  // Download handler (PDF or DOCX)
  const handleDownloadFile = useCallback(async (documentId: number) => {
    try {
      const blob = await downloadDocumentAttachment(documentId);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `document_${documentId}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading document:', error);
      alert('Failed to download document. Please try again.');
    }
  }, []);

  // Render DOCX preview using docx-preview when a DOCX document is selected
  useEffect(() => {
    async function renderDocx() {
      if (!selectedDocument || selectedDocument.document_type !== 'docx') return;
      try {
        // Reset zoom to default for each new DOCX selection
        setDocxZoom(0.7);
        const { renderAsync } = await import('docx-preview');
        const blob = await downloadDocumentAttachment(selectedDocument.id);
        if (docxPreviewRef.current) {
          // Clear previous render if any
          docxPreviewRef.current.innerHTML = '';
          await renderAsync(blob, docxPreviewRef.current, undefined, {
            className: 'docx-preview',
          });
          // Measure and compute a base scale that fits width to container
          const computeBaseScale = () => {
            const container = docxContainerRef.current;
            const docEl = docxPreviewRef.current?.querySelector('.docx') as HTMLElement | null;
            if (!container || !docEl) return;
            const containerWidth = container.clientWidth;
            const docWidthScaled = docEl.getBoundingClientRect().width;
            const currentScale = Math.max(0.0001, docxBaseScale * docxZoom);
            const docWidth = docWidthScaled / currentScale; // approximate unscaled width
            if (docWidth > 0) {
              const s = (containerWidth - 24) / docWidth; // fit width with small padding
              setDocxBaseScale(s);
            }
          };
          computeBaseScale();
          const resizeHandler = () => computeBaseScale();
          window.addEventListener('resize', resizeHandler);
          setTimeout(resizeHandler, 0);
        }
      } catch (e) {
        // Silent fallback – UI already shows Download button
        if (docxPreviewRef.current) {
          docxPreviewRef.current.innerHTML = '<p class="text-gray-600">Preview failed. Please use Download.</p>';
        }
      }
    }
    renderDocx();
    // Cleanup when modal closes or doc changes
    return () => {
      if (docxPreviewRef.current) docxPreviewRef.current.innerHTML = '';
    };
  }, [selectedDocument]);

  // Space creation handler
  const handleCreateSpace = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const spaceData: CreateSpaceRequest = {
        name: spaceForm.name,
        description: spaceForm.description || undefined,
        space_type: spaceForm.space_type,
        playground_type: spaceForm.playground_type, // ADD playground_type
        create_group: spaceForm.create_group
      };
      
      const newSpace = await createSpace(spaceData);
      setSpaceForm({ name: '', description: '', space_type: 'personal', playground_type: 'documents', create_group: false });
      setShowCreateSpaceForm(false);
      refreshSpaces(); // Refresh the spaces list
    } catch (error) {
      console.error('Error creating space:', error);
      alert('Failed to create space. Please try again.');
    } finally {
      setSubmitting(false);
    }
  }, [spaceForm]);

  const handleCreateTextDocument = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!textForm.space_id) {
      alert('Please select a space');
      return;
    }
    setSubmitting(true);

    try {
      const newDocument = await createTextDocument(textForm);
      // merge into spaceDocuments if we have a map entry
      if (newDocument && newDocument.space_id) {
        setSpaceDocuments(prev => {
          const map = new Map(prev);
          const arr = map.get(newDocument.space_id) || [];
          map.set(newDocument.space_id, [newDocument, ...arr]);
          return map;
        });
      }
      // Refresh spaces immediately to update counts (backend invalidates Redis)
      await refreshSpaces();
      setTextForm({
        title: '',
        description: '',
        space_id: selectedSpace?.id || 0,
        document_type: 'text',
        content: '',
      });
      setShowAddTextForm(false);
    } catch (error) {
      console.error('Error creating document:', error);
      alert('Failed to create document. Please check your input and try again.');
    } finally {
      setSubmitting(false);
    }
  }, [textForm, selectedSpace]);

  // ======= Upload document handler (auto-title) =======
  const handleUploadDocument = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    const sid = Number(uploadForm.space_id);
    if (!sid || Number.isNaN(sid)) {
      alert('Please select a space');
      return;
    }

    if (!fileInputRef.current?.files?.length) {
      alert('Please select a file to upload');
      return;
    }

    const file = fileInputRef.current.files[0];

    // Auto-derive title from filename (without extension)
    const derivedTitle = file.name.replace(/\.[^.]+$/, '');

    // Validate file type based on selected space's playground type
    const spaceObj = spaces.find(s => s.id === sid);
    const playgroundType = (spaceObj?.playground_type || 'documents') as 'documents' | 'images';
    const isAllowed = isFileAllowedForPlayground(file, playgroundType);
    if (!isAllowed) {
      alert(
        playgroundType === 'images'
          ? 'Only JPEG, PNG, WebP, HEIC, HEIF images are allowed'
          : 'Only PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX files are allowed'
      );
      return;
    }

    // Check file size (limit to 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      alert(`File size exceeds 10MB limit. Your file is ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
      return;
    }

    setSubmitting(true);

    try {
      const newDocument = await uploadDocument(
        file,
        derivedTitle, // ← use filename as title
        uploadForm.description || '',
        sid,
        setSingleDuplicateState // Pass state setter for duplicate handling
      );


      // Update spaceDocuments map with new document
      if (newDocument.status === "success") {
        setIsUploded(true)
      }
      if (newDocument && newDocument.space_id) {
        const newSpaceDocuments = new Map(spaceDocuments);
        const existingDocs = newSpaceDocuments.get(newDocument.space_id) || [];
        newSpaceDocuments.set(newDocument.space_id, [newDocument, ...existingDocs]);
        setSpaceDocuments(newSpaceDocuments);
      }

      // Increment doc count so "X docs" updates instantly
      setSpaces((prevSpaces: any) => {
        if (!Array.isArray(prevSpaces)) return prevSpaces;
        return prevSpaces.map((sp: any) =>
          sp.id === newDocument.space_id
            ? { ...sp, document_count: (sp.document_count || 0) + 1 }
            : sp
        );
      });


      if (
        indexAfterUpload &&
        newDocument &&
        !(('cancelled' in newDocument) && (newDocument as any).cancelled) &&
        // Do not index images
        ((spaceObj?.playground_type || 'documents') === 'documents')
      ) {
        // open status modal and trigger indexing
        if (newDocument?.filename) {
          setStatusFilter({ filename: newDocument.filename });
        } else if (newDocument?.id) {
          setStatusFilter({ documentId: newDocument.id });
        }
        setShowStatusModal(true);
        // trigger indexing for this single doc
        // After backend indexing, force refresh the open space list so indexed flag reflects immediately
        await handleIndexDocument(newDocument.id, newDocument.space_id);
        try {
          const freshDocs = await fetchSpaceDocuments(newDocument.space_id);
          setSpaceDocuments(prev => new Map(prev).set(newDocument.space_id, Array.isArray(freshDocs) ? freshDocs : []));
        } catch (err) {
          console.warn('Post-index refresh failed', err);
        }
      }
      setUploadForm({
        description: '',
        space_id: selectedSpace?.id || 0,
      });
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setShowUploadForm(false);
    } catch (error) {
      console.error('Error uploading document:', error);
      alert(`Failed to upload document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setSubmitting(false);
    }
  }, [uploadForm, selectedSpace]);

  // Bulk upload documents handler
  const handleBulkUpload = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!bulkUploadSpaceId) {
      alert('Please select a space');
      return;
    }

    if (!bulkFileInputRef.current?.files?.length) {
      alert('Please select files to upload');
      return;
    }

    const files = Array.from(bulkFileInputRef.current.files);

    // Determine playground type dynamically for the selected space
    const currentBulkSpace = Array.isArray(spaces)
      ? (spaces as UserSpace[]).find(s => s.id === bulkUploadSpaceId)
      : null;
    const currentPlaygroundType = (currentBulkSpace?.playground_type || 'documents') as 'documents' | 'images';

    // Validate files based on playground type (documents vs images)
    const invalidFiles = files.filter(file => !isFileAllowedForPlayground(file, currentPlaygroundType));
    if (invalidFiles.length > 0) {
      const labels = getPlaygroundLabels(currentPlaygroundType);
      alert(`${invalidFiles.length} invalid file(s) detected. ${labels.uploadHint}`);
      return;
    }

    // Check total file size (limit to 50MB total)
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const maxTotalSize = 50 * 1024 * 1024; // 50MB in bytes

    if (totalSize > maxTotalSize) {
      alert(`Total file size exceeds 50MB limit. Your files total ${(totalSize / (1024 * 1024)).toFixed(2)}MB`);
      return;
    }

    setSubmitting(true);
    setBulkUploadResult(null);

    try {
      const result = await bulkUploadDocuments(
        files,
        bulkUploadSpaceId,
        setDuplicateModalState, // Pass state setter for duplicate handling
        setSpaceDocuments // Pass state setter for document updates
      );

      // Recalculate counts purely from arrays to ensure correctness
      if (Array.isArray(result.uploaded_documents)) {
        result.uploaded_count = result.uploaded_documents.length;
      }
      if (Array.isArray(result.failed_uploads)) {
        result.failed_count = result.failed_uploads.length;
      }

      setBulkUploadResult(result);

      // Refresh spaces/documents if any documents were uploaded
      if (result.uploaded_count > 0) {
        //  refreshSpaces(); // Refresh spaces counts

        // Check if duplicates were detected during upload or if this was a duplicate upload
        const hasDuplicates =
          (result.duplicate_files && Array.isArray(result.duplicate_files) && result.duplicate_files.length > 0) ||
          (result.was_duplicate_upload === true);
       ////console.log('Bulk upload - Has duplicates?', hasDuplicates, 'was_duplicate_upload:', result.was_duplicate_upload);

        // Update spaceDocuments map with new documents (only if no duplicates were detected)
        if (Array.isArray(result.uploaded_documents) && result.uploaded_documents.length > 0 && !hasDuplicates) {
          const newSpaceDocuments = new Map(spaceDocuments);
          const existingDocs = newSpaceDocuments.get(bulkUploadSpaceId) || [];
          const merged = [...result.uploaded_documents, ...existingDocs];
          newSpaceDocuments.set(bulkUploadSpaceId, merged);
          setSpaceDocuments(newSpaceDocuments);

          // Auto-expand the space to show newly uploaded documents
          if (!expandedSpaces.has(bulkUploadSpaceId)) {
            setExpandedSpaces(prev => new Set(Array.from(prev).concat([bulkUploadSpaceId])));
          }
        } else if (hasDuplicates) {
       //  //console.log('Bulk upload success - Skipping state update because duplicates were handled separately',result.duplicate_files);
        }

        // Only update legacy documents state if no duplicates
        if (!hasDuplicates) {
          setDocuments(prev => [...result.uploaded_documents, ...prev]);
        }

        // Always refresh spaces counts (backend invalidates Redis)
        await refreshSpaces();

      }

      if (result.failed_count === 0) {
        setTimeout(() => {
          setShowBulkUploadForm(false);
          setBulkUploadResult(null);
          if (bulkFileInputRef.current) {
            bulkFileInputRef.current.value = '';
          }
        }, 2000);
      }
    } catch (error) {
      console.error('Error bulk uploading documents:', error);
      alert(`Failed to upload documents: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setSubmitting(false);
    }
  }, [bulkUploadSpaceId, selectedSpace]);

  // Toggle workspace row ↔ document list and lazy-load the documents
  const toggleSpaceExpansion = async (spaceId: number) => {
    // Collapse if already open -------------------------------------------------
    setExpandedSpaces(prev => {
      const next = new Set(prev);

      if (next.has(spaceId)) {
        // just close the list
        next.delete(spaceId);
      } else {
        // open the list --------------------------------------------------------
        next.add(spaceId);
      }

      return next;
    });

    // Always fetch fresh documents when expanding (to sync with Redis cache)
    // Check if space is being expanded (not collapsed)
    const wasExpanded = expandedSpaces.has(spaceId);
    const willBeExpanded = !wasExpanded;

    if (willBeExpanded) {
      try {
        const freshDocs = await fetchSpaceDocuments(spaceId);
        const safeDocs = Array.isArray(freshDocs) ? freshDocs : [];
        setSpaceDocuments(prev => {
          const newMap = new Map(prev).set(spaceId, safeDocs);
          // also update spaces list counts so UI shows correct number instantly
          setSpaces((prevSpaces: any) => {
            if (!Array.isArray(prevSpaces)) return prevSpaces;
            return prevSpaces.map((sp: any) =>
              sp.id === spaceId ? { ...sp, document_count: safeDocs.length } : sp
            );
          });
          return newMap;
        });
      } catch (error: any) {
        console.error('Error fetching space documents:', error);
        // If space is not found (deleted), remove it from UI for all users
        if (error && error.status === 404) {
          setSpaces((prevSpaces: any) => Array.isArray(prevSpaces) ? prevSpaces.filter((sp: any) => sp.id !== spaceId) : prevSpaces);
          setSpaceDocuments(prev => { const m = new Map(prev); m.delete(spaceId); return m; });
          if (selectedSpaceId === spaceId) setSelectedSpaceId(null as any);
        }
      }
    } else {
     // //console.log(`Space ${spaceId} is being collapsed`);
    }

    // Update currently-selected space helper state (optional)
    const found = Array.isArray(spaces)
      ? (spaces as UserSpace[]).find(s => s.id === spaceId) || null
      : null;
    setSelectedSpace(found);
  };

  // Delete document handler
  const handleDeleteDocument = async (documentId: number, spaceId: number) => {
    if (!confirm('Are you sure you want to delete this document?')) {
      return;
    }

    try {
      await deleteDocument(documentId); // backend delete

      // Immediately refresh documents list for that workspace from backend (hits Redis cache)
      try {
        const fresh = await fetchSpaceDocuments(spaceId);
        setSpaceDocuments(prev => new Map(prev).set(spaceId, fresh));
      } catch (err) {
        console.error('Failed to reload documents after delete', err);
      }

      // Refresh spaces list so document_count updates & ensure caches stay in sync
      await refreshSpaces();

      // Update spaceDocuments map by removing the deleted document
      const newSpaceDocuments = new Map(spaceDocuments);
      Array.from(newSpaceDocuments.entries()).forEach(([spaceId, docs]) => {
        newSpaceDocuments.set(spaceId, docs.filter(doc => doc.id !== documentId));
      });
      setSpaceDocuments(newSpaceDocuments);
      if (selectedDocument?.id === documentId) {
        setSelectedDocument(null);
      }
    } catch (error) {
      alert('Failed to delete document.');
    }
  };

  // Allow external components to open the Upload Document modal directly via a custom event
  useEffect(() => {
    const handler = () => {
      setIsOpen(true);           // make sure the sidebar itself is visible
      setShowUploadForm(true);   // open the upload form modal
    };
    window.addEventListener('open-upload-modal', handler as EventListener);
    return () => window.removeEventListener('open-upload-modal', handler as EventListener);
  }, []);

  // Handlers for duplicate modal
  const handleDuplicateSkip = async () => {
    if (!duplicateModalState) return;
    const { files, info, spaceId, initialUploaded, resolve } = duplicateModalState;

    // Normalize duplicate filenames (string or object) to lower-case for case-insensitive comparison
    const dupNames = info.map((d: any) => (typeof d === 'string' ? d : d.filename)).map((n: string) => n.toLowerCase());

    // Filter out duplicate files (case-insensitive)
    let uniqueFiles = files.filter(file => !dupNames.includes(file.name.toLowerCase()));

    // Helper to perform retry upload; repeats until backend reports no duplicates or uniqueFiles empty
    const retryUpload = async (remaining: File[]): Promise<any> => {
      if (remaining.length === 0) {
        return { skipped: true, uploaded_count: 0, failed_count: 0, uploaded_documents: [] };
      }

      try {
        const data = await retryBulkUpload(remaining, spaceId, false);

        // If backend still reports duplicate_files, strip them and retry
        if (data.duplicate_files && data.duplicate_files.length > 0) {
          const newDup = data.duplicate_files.map((d: any) => (typeof d === 'string' ? d : d.filename)).map((n: string) => n.toLowerCase());
          const nextUnique = remaining.filter(f => !newDup.includes(f.name.toLowerCase()));
          const nextResult = await retryUpload(nextUnique);

          // Merge counts & arrays
          return {
            uploaded_count: (data.uploaded_count || 0) + (nextResult.uploaded_count || 0),
            failed_count: (data.failed_count || 0) + (nextResult.failed_count || 0),
            uploaded_documents: [...(data.uploaded_documents || []), ...(nextResult.uploaded_documents || [])],
            skipped: false,
            success: (data.success ?? true) && (nextResult.success ?? true),
          };
        }

        return data;
      } catch (error) {
        // treat as all failed
        return { success: false, uploaded_count: 0, failed_count: remaining.length, uploaded_documents: [] };
      }
    };

    try {
      const retryData = await retryUpload(uniqueFiles);

      // Ensure count fields exist or correct
      if (typeof retryData.uploaded_count === 'undefined' || (retryData as any).uploaded_documents && (retryData as any).uploaded_documents.length > 0 && retryData.uploaded_count === 0) {
        retryData.uploaded_count = (retryData as any).uploaded_documents.length;
      }
      if (typeof retryData.failed_count === 'undefined') {
        retryData.failed_count = uniqueFiles.length - (retryData.uploaded_count || 0);
      }

      // Ensure uploaded_documents is always an array
      if (!(retryData as any).uploaded_documents) {
        (retryData as any).uploaded_documents = [];
      }

      // Final fallback: if still zero but we actually attempted uploads
      if ((retryData.uploaded_count === 0 || typeof retryData.uploaded_count === 'undefined') && uniqueFiles.length > 0 && !(retryData as any).error) {
        retryData.uploaded_count = uniqueFiles.length - (retryData.failed_count || 0);
      }

      // Re-compute accurate counts
      retryData.uploaded_count = Array.isArray(retryData.uploaded_documents) ? retryData.uploaded_documents.length + initialUploaded : initialUploaded;
      retryData.failed_count = Array.isArray(retryData.failed_uploads) ? retryData.failed_uploads.length : (retryData.failed_count || 0);

      // If user chose Skip & Upload, retryData.skipped will be true and no additional docs were uploaded
      const totalUploadedCount = retryData.skipped ? initialUploaded : initialUploaded + (retryData.uploaded_count || 0);
      retryData.uploaded_count = totalUploadedCount;
      try {
        const refreshedSpaces = await fetchAllSpaces();
        if (refreshedSpaces) setSpaces(refreshedSpaces);

        const updatedDocs = await fetchSpaceDocuments(spaceId);
        setSpaceDocuments(prev => new Map(prev).set(spaceId, updatedDocs));
      } catch (e) {
        console.error('Error refreshing after bulk upload skip', e);
      }

      resolve(retryData);
    } catch (err) {
      console.error('Retry upload after skipping duplicates failed', err);
      resolve({ skipped: true, error: err instanceof Error ? err.message : 'Unknown error' });
    } finally {
      setDuplicateModalState(null);
    }
  };

  const handleDuplicateKeepBoth = async () => {
    if (!duplicateModalState) return;

    const { files, info, spaceId, initialUploaded, resolve } = duplicateModalState;

    // Build a set of duplicate filenames returned by backend (case-insensitive)
    const duplicateNameSet = new Set(
      info.map((d: any) => (typeof d === 'string' ? d : d.filename).toLowerCase())
    );

    // Get only the duplicate files
    const duplicateFiles = files.filter((file) => duplicateNameSet.has(file.name.toLowerCase()));

    try {
      const retryData = await retryBulkUpload(duplicateFiles, spaceId, true);

      // Ensure counts exist
      const uploadedCnt = (retryData as any).uploaded_count ?? duplicateFiles.length;
      const failedCnt = (retryData as any).failed_count ?? 0;

      // Merge with initial upload count
      const totalUploaded = initialUploaded + uploadedCnt;

      // Update local state with newly uploaded documents
      if (Array.isArray((retryData as any).uploaded_documents)) {
        setSpaceDocuments((prev) => {
          const map = new Map(prev);
          const existing = map.get(spaceId) || [];
          map.set(spaceId, [...(retryData as any).uploaded_documents, ...existing]);
          return map;
        });
      }

      // Update local state with newly uploaded documents (if any)
      const newDocsArr = (retryData as any).uploaded_documents || [];
      ////console.log('handleDuplicateKeepBoth - New docs from retry:', newDocsArr);
      if (Array.isArray(newDocsArr) && newDocsArr.length > 0) {
        setSpaceDocuments((prev) => {
          const map = new Map(prev);
          const existing = map.get(spaceId) || [];
         // //console.log('handleDuplicateKeepBoth - Existing docs in state:', existing.length);

          // Deduplicate by document ID to avoid React key conflicts
          const existingIds = new Set(existing.map(doc => doc.id));
          const filteredNew = newDocsArr.filter(doc => !existingIds.has(doc.id));
          const updated = [...filteredNew, ...existing];

        //  //console.log('handleDuplicateKeepBoth - After deduplication:', updated.length);
         // //console.log('handleDuplicateKeepBoth - Document IDs:', updated.map(d => d.id));
          map.set(spaceId, updated);
          return map;
        });
      }

      // Refresh spaces list so document_count updates
      await refreshSpaces();

      // Force refresh current space documents to sync with Redis
      if (expandedSpaces.has(spaceId)) {
     //   //console.log('Skipping force refresh - duplicate upload already updated state properly');
      }

      // Mark that this was a duplicate upload so bulk handler knows not to overwrite state
      resolve({
        ...retryData,
        uploaded_count: totalUploaded,
        failed_count: failedCnt,
        was_duplicate_upload: true  // Special flag to prevent state overwrite
      });
    } catch (err) {
      console.error('Error uploading duplicate files:', err);
      resolve({ success: false, error: err });
    } finally {
      setDuplicateModalState(null);
    }
  };

  //Index all documents in a space helper
  const handleIndexAll = async (spaceId: number) => {
    const docs = spaceDocuments.get(spaceId) || [];
    const unindexed = docs.filter(d => !d.indexed);

    if (unindexed.length === 0) {
      alert('All documents already indexed');
      return;
    }

    if (!confirm(`Index ${unindexed.length} unindexed documents in this space?`)) {
      return;
    }

    // Add all unindexed document IDs to the indexing set
    setIndexingDocuments(prev => new Set([...Array.from(prev), ...unindexed.map(doc => doc.id)]));

    try {
      // Show status modal filtered by space
      setStatusFilter({ documentId: unindexed[0].id });
      setShowStatusModal(true);

      const result = await indexAllDocuments(spaceId);
      if (result.success) {
        // Update the documents in the spaceDocuments map
        setSpaceDocuments(prev => {
          const newSpaceDocuments = new Map(prev);
          const docsToUpdate = newSpaceDocuments.get(spaceId) || [];

          const updatedDocs = docsToUpdate.map(doc =>
            unindexed.some(unindexedDoc => unindexedDoc.id === doc.id)
              ? { ...doc, indexed: true, indexed_at: new Date().toISOString() }
              : doc
          );

          newSpaceDocuments.set(spaceId, updatedDocs);
          return newSpaceDocuments;
        });

        // If this is the currently selected space, update the documents state too
        if (selectedSpace && selectedSpace.id === spaceId) {
          setDocuments(prev => prev.map(doc =>
            unindexed.some(unindexedDoc => unindexedDoc.id === doc.id)
              ? { ...doc, indexed: true, indexed_at: new Date().toISOString() }
              : doc
          ));
        }

        // Refresh spaces to update document counts
        setTimeout(() => refreshSpaces(), 300);

        alert(`Successfully indexed ${result.indexed_count} documents${result.total_processing_time ? `. Processing time: ${result.total_processing_time.toFixed(2)}s` : ''}`);
      } else {
        alert(`Failed to index documents: ${result.message}`);
      }
    } catch (error) {
      console.error('Error indexing documents:', error);
      alert('Failed to index documents. Please try again.');
    } finally {
      // Remove all document IDs from the indexing set
      setIndexingDocuments(prev => {
        const newSet = new Set(prev);
        unindexed.forEach(doc => newSet.delete(doc.id));
        return newSet;
      });
    }
  };

  // Professional dropdown state management
  const toggleDropdown = (documentId: number, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    if (openDropdownDocId === documentId) {
      setOpenDropdownDocId(null);
    } else {
      setOpenDropdownDocId(documentId);


      const button = event.currentTarget as HTMLElement;
      const buttonRect = button.getBoundingClientRect();
      const dropdownTop = buttonRect.top;
      const dropdownLeft = buttonRect.left + 30;

      setDropdownPosition({
        top: dropdownTop,
        left: dropdownLeft
      });

      document.body.style.position = 'relative';
      document.body.classList.add('dropdown-open');
    }
  };


  const closeDropdown = () => {
    setOpenDropdownDocId(null);
    document.body.classList.remove('dropdown-open');
  };

  const handleDropdownAction = (action: () => void) => {
    action();
    closeDropdown();
  };

  // Helper to reload spaces from backend (uses Redis cache on BE)
  const refreshSpaces = useCallback(async () => {
    try {
      const data = await fetchAllSpaces();
      if (data) setSpaces(data);
    } catch (err) {
      console.error('Failed to refresh spaces:', err);
    }
  }, []);


  // Also refresh on window focus and when tab becomes visible
  useEffect(() => {
    const onFocus = () => { refreshSpaces(); };
    const onVisibility = () => {
      if (document.visibilityState === 'visible') {
        refreshSpaces();
      }
    };
    window.addEventListener('focus', onFocus);
    document.addEventListener('visibilitychange', onVisibility);
    return () => {
      window.removeEventListener('focus', onFocus);
      document.removeEventListener('visibilitychange', onVisibility);
    };
  }, [refreshSpaces]);

  // Preload users when opening manage members
  const openManageMembers = async (spaceId: number) => {
    try {
      if (allUsers.length === 0) {
        const users = await fetchAllUsers();
        setAllUsers(users);
      }
      setSelectedMemberIds([]);
      setSelectedMemberIdsToRemove([]);
      // Always fetch authoritative member list for this space
      try {
        const detailed = await getSpaceWithMembers(spaceId);
        setCurrentSpaceMembers(Array.isArray(detailed?.members) ? detailed.members : []);
      } catch (err) {
        // Fallback: no members on error
        setCurrentSpaceMembers([]);
      }
      setMemberSearch('');
      setMemberSearchResult(null);
      setMemberSearchSearched(false);
      setShowManageMembers(spaceId);
    } catch (e) {
      alert('Failed to load users.');
    }
  };
useEffect(() => {
  if (!selectedSpaceId && spaces.length > 0) {
    setSelectedSpaceId(spaces[0].id);
  }
}, [spaces, selectedSpaceId]);

  // Derived UI state for upload modal
  const selectedUploadSpaceObj = useMemo(() => (
    spaces.find(s => s.id === uploadForm.space_id) || null
  ), [spaces, uploadForm.space_id]);
  const uploadPlaygroundType = (selectedUploadSpaceObj?.playground_type || 'documents') as 'documents' | 'images';
  const uploadLabels = useMemo(() => getPlaygroundLabels(uploadPlaygroundType), [uploadPlaygroundType]);

  // Derived UI state for bulk upload modal
  const selectedBulkSpaceObj = useMemo(() => (
    spaces.find(s => s.id === bulkUploadSpaceId) || null
  ), [spaces, bulkUploadSpaceId]);
  const bulkPlaygroundType = (selectedBulkSpaceObj?.playground_type || 'documents') as 'documents' | 'images';
  const bulkLabels = useMemo(() => getPlaygroundLabels(bulkPlaygroundType), [bulkPlaygroundType]);
  return (
    <>

      {/* Sidebar */}
      <div
        className={`fixed top-[5rem] left-0 h-[calc(100%-5rem)] bg-white shadow-xl z-40 transition-all duration-300 ease-in-out overflow-hidden ${isOpen ? 'w-full md:w-[380px]' : 'w-0'
          }`}
      >
        <div className="h-full flex flex-col overflow-visible">

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Header Section */}
            <div className="relative px-6 py-4 bg-white border-b border-slate-200">
              {/* Close button - absolute top-right */}
              <button
                onClick={() => setIsOpen(false)}
                className="absolute top-3 right-2 w-5 h-5 bg-gray-100 hover:bg-gray-200 rounded-lg flex items-center justify-center transition-colors"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              {/* Header content */}
              <div className="flex items-center justify-start gap-6 my-2">
                <label className="text-xl whitespace-nowrap font-semibold text-slate-700">
                  Knowledge Base
                </label>
                <button
                  onClick={() => setShowCreateSpaceForm(true)}
                  className="inline-flex items-center gap-2 rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-1 transition whitespace-nowrap"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  New Space
                </button>
              </div>
            </div>


            {/* Quick Actions Section - Always visible when spaces exist */}
            {spaces.length > 0 && (
              <div className="p-2 bg-slate-50 border-b border-slate-200">

                <div className="flex items-center justify-between gap-4">
                  <button
                    onClick={() => {
                        const fallback = selectedSpaceId ?? spaces[0]?.id ?? 0;
                        setTextForm(prev => ({ ...prev, space_id: fallback }));
                        setShowAddTextForm(true);
                        setShowUploadForm(false);
                        setShowBulkUploadForm(false);
                      }}
                    className="flex items-center justify-center px-3 py-2 text-sm font-medium whitespace-nowrap text-blue-700 bg-blue-50 border border-blue-200 rounded-xl hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    Add Text
                  </button>
                  <button
                  onClick={() => {
                                    const fallback = selectedSpaceId ?? spaces[0]?.id ?? 0;
                                    setUploadForm(prev => ({ ...prev, space_id: fallback }));
                                    setShowUploadForm(true);
                                    setShowAddTextForm(false);
                                    setShowBulkUploadForm(false);
                                    setIsUploded(false);
                                  }}
                    className="flex items-center justify-center px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-xl hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    Upload
                  </button>
                  <button
          onClick={() => {
                        const fallback = selectedSpaceId ?? spaces[0]?.id ?? 0;
                        setBulkUploadSpaceId(fallback);
                        setSubmitting(false);
                        setShowBulkUploadForm(true);
                        setShowAddTextForm(false);
                        setShowUploadForm(false);
                      }}

                    className="flex items-center justify-center px-2 py-2 text-sm font-medium  whitespace-nowrap text-blue-700 bg-blue-50 border border-blue-200 rounded-xl hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    Bulk Upload
                  </button>

                </div>
              </div>
            )}

            {/* Collapsible Spaces List */}
            {spaces && spaces.length > 0 ? (
              <div className="flex-1">
                {spaces.map((space: any) => (
                  <div key={space.id} className="border-slate-200">
                    {/* Space Header - Clickable */}
                    <div
                      onClick={() => {
                        toggleSpaceExpansion(space.id);
                        setSelectedSpaceId(space.id);
                      }}
                      className="p-3 bg-white border border-slate-200 hover:bg-slate-50 cursor-pointer transition-colors duration-200 flex items-center justify-between"
                    >
                      <div className="flex items-center flex-1">
                        <div className="flex items-center mr-3">
                          <svg
                            className={`w-4 h-4 text-slate-400 transition-transform duration-200 ${expandedSpaces.has(space.id) ? 'rotate-90' : ''
                              }`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                        <div className="flex items-center">
                          <div>
                            <h3 className="font-semibold text-slate-800">{space.name}</h3>
                            {/* {space.description && (
                              <p className="text-sm text-slate-500 truncate">{space.description}</p>
                            )} */}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="inline-flex items-center justify-center w-7 h-7 rounded-full border-2 border-blue-900 text-sm text-slate-700 font-semibold bg-white">
                          {space.document_count}
                        </span>

                        {/* Index-All button - Only for document spaces */}
                        {space.playground_type === 'documents' && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleIndexAll(space.id);
                            }}
                            disabled={!spaceDocuments.has(space.id) || (spaceDocuments.get(space.id) || []).filter(doc => !doc.indexed).length === 0}
                            className="p-1 rounded border text-xs border-orange-300 bg-orange-50 text-orange-500 enabled:hover:bg-orange-100 enabled:hover:text-orange-700 disabled:opacity-40 disabled:cursor-not-allowed"
                            title="Index all documents in this workspace"
                          >
                            Index All ({(spaceDocuments.get(space.id) || []).filter(doc => !doc.indexed).length})
                          </button>
                        )}

                        {/* Delete button */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteSpace(space.id, space.name);
                          }}
                          className="p-1 rounded border border-red-200 bg-red-50 text-red-400 hover:bg-red-100 hover:text-red-600"
                          title="Delete workspace"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                        {space.is_shared && user && user.id === space.owner_id && (
                          <button
                            onClick={(e) => { e.stopPropagation(); openManageMembers(space.id); }}
                            className="p-1 rounded border border-blue-200 bg-blue-50 text-blue-900 hover:bg-blue-100 hover:text-blue-700"
                            title="Manage members"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>

                    {/* Collapsible Documents List */}
                    {expandedSpaces.has(space.id) && (
                      <div className="bg-slate-50 border-t border-slate-200">
                        {/*Expand Documents List*/}
                        {spaceDocuments.has(space.id) ? (
                          <div className="ps-6 mb-5">
                            {(() => {
                              const docs = spaceDocuments.get(space.id) || [];
                              const labels = getPlaygroundLabels(space.playground_type);
                              return null;
                            })()}
                            {(spaceDocuments.get(space.id) || []).length === 0 ? (
                              <div className="text-center py-8">
                                {(() => {
                                  const labels = getPlaygroundLabels(space.playground_type);
                                  return (
                                    <>
                                      <svg className="w-12 h-12 mx-auto text-slate-300 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        {space.playground_type === 'images' ? (
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        ) : (
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        )}
                                      </svg>
                                      <p className="text-slate-500 font-medium">No {labels.itemNamePlural.toLowerCase()} found</p>
                                      <p className="text-sm text-slate-400 mt-1">{labels.emptyStateMessage}</p>
                                    </>
                                  );
                                })()}
                              </div>
                            ) : (
                              <div className="border-3 border-slate-200">
                                {(spaceDocuments.get(space.id) || []).map(document => (
                                  <div
                                    key={document.id}
                                    className="bg-white px-2 py-2 border border-3 rounded-xl border-slate-200 hover:shadow-md hover:border-slate-300 transition-all duration-200"
                                  >
                                    <div className="flex items-start justify-between">
                                      <div className="flex-1 min-w-0">
                                        <div className="flex items-center justify-between">
                                          <h4 className="text-gray-700 truncate mr-2 text-sm font-medium" title={document.title}>
                                            {document.title}.{document.document_type}
                                          </h4>
                                          {document.title.length > 25 && (
                                            <button
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                setShowFullNameModal(document.title);
                                              }}
                                              className="text-slate-400 hover:text-slate-600 transition-colors"
                                              title="Show full filename"
                                            >
                                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                              </svg>
                                            </button>
                                          )}
                                          <div className="flex items-center space-x-2 justify-between">

                                            {/* Indexing status - Only for documents */}
                                            {space.playground_type === 'documents' && (
                                              <>
                                                {indexingDocuments.has(document.id) ? (
                                                  <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                                    <svg className="w-3 h-3 mr-1 animate-spin" fill="none" viewBox="0 0 24 24">
                                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                    Indexing...
                                                  </span>
                                                ) : document.indexed ? (
                                                  <span className="inline-flex items-center gap-1.5 rounded-md border border-green-200 bg-green-50 px-1.5 py-1 text-sm font-medium text-gray-600 shadow-sm">
                                                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                    </svg>
                                                    <span>Indexed</span>
                                                  </span>
                                                ) : (
                                                  <button
                                                    onClick={() => handleIndexDocument(document.id, space.id)}
                                                    className="inline-flex items-center gap-2 whitespace-nowrap rounded-lg border border-yellow-300 bg-yellow-50 px-3 py-0.5 text-sm font-medium text-yellow-800 shadow-sm transition-all hover:bg-yellow-100 hover:text-yellow-900 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-1 active:scale-95"
                                                    title="Click to index this document"
                                                  >
                                                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                                    </svg>
                                                    <span>Not Indexed</span>
                                                  </button>
                                                )}
                                              </>
                                            )}

                                            {/* Image badge for image spaces */}
                                            {space.playground_type === 'images' && (
                                              <span className="inline-flex items-center gap-1.5 rounded-md border border-purple-200 bg-purple-50 px-1.5 py-1 text-sm font-medium text-purple-600 shadow-sm">
                                                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                                                </svg>
                                                <span>Image</span>
                                              </span>
                                            )}

                                            {/* Three-dot dropdown menu */}
                                            <div className="relative" style={{ position: 'static' }} data-dropdown-container>
                                              <button
                                                type="button"
                                                onClick={(e) => toggleDropdown(document.id, e)}
                                                className="p-1.5 rounded-full hover:bg-slate-100 text-slate-400 hover:text-slate-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                                                title="Document actions"
                                                aria-label="Document actions menu"
                                                aria-expanded={openDropdownDocId === document.id}
                                                aria-haspopup="true"
                                              >
                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
                                                </svg>
                                              </button>

                                              {/* Dropdown menu */}
                                              {openDropdownDocId === document.id && (
                                                <div
                                                  data-dropdown-id={document.id}
                                                  className="fixed w-40 bg-white rounded-md shadow-xl border border-gray-300 py-1.5"
                                                  style={{
                                                    position: 'fixed',
                                                    top: `${dropdownPosition.top}px`,
                                                    left: `${dropdownPosition.left}px`,
                                                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
                                                    backgroundColor: '#ffffff',
                                                    backgroundImage: 'none',
                                                    opacity: 1,
                                                    backdropFilter: 'none',
                                                    WebkitBackdropFilter: 'none',
                                                    zIndex: 999999,
                                                    isolation: 'isolate'
                                                  }}
                                                  onClick={(e) => e.stopPropagation()}
                                                >
                                                  <button
                                                    onClick={() => handleDropdownAction(() => handleViewDocument(document.id))}
                                                    className="w-full text-left px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center transition-colors duration-150"
                                                  >
                                                    <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                    </svg>
                                                    {getPlaygroundLabels(space.playground_type).viewAction}
                                                  </button>

                                                  {(document.document_type === 'pdf' || document.document_type === 'docx') && (
                                                    <button
                                                      onClick={() => handleDropdownAction(() => handleDownloadFile(document.id))}
                                                      className="w-full text-left px-3 py-2.5 text-sm text-gray-700 bg-white hover:bg-gray-50 flex items-center transition-colors duration-150"
                                                    >
                                                      <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                      </svg>
                                                      Download File
                                                    </button>
                                                  )}

                                                  {(document.document_type === 'pdf' || document.document_type === 'docx') && (
                                                    <div className="border-t border-gray-200 my-1.5 mx-2 bg-white"></div>
                                                  )}

                                                  <button
                                                    onClick={() => handleDropdownAction(() => handleDeleteDocument(document.id, space.id))}
                                                    className="w-full text-left px-2 py-2.5 text-sm font-medium text-red-600 bg-white hover:bg-red-50 flex items-center transition-colors duration-150"
                                                  >
                                                    <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                    Delete Document
                                                  </button>
                                                </div>
                                              )}
                                            </div>

                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="p-4 flex items-center justify-center">
                            <div className="flex items-center text-slate-500">
                              <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Loading documents...
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-6 text-center py-16">
                <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-2xl flex items-center justify-center">
                  <svg className="w-10 h-10 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-slate-700 mb-2">No workspaces found</h3>
                <p className="text-sm text-slate-500 mb-4">Create your first workspace to get started</p>
                <button
                  onClick={() => setShowCreateSpaceForm(true)}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Create New Workspace
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Create Space Form */}
      {showCreateSpaceForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
            <h2 className="text-xl font-bold text-gray-800 mb-4">Create New Space</h2>

            <form onSubmit={handleCreateSpace} className="space-y-4">
              {/* Playground Type Selection - First */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Playground Type *
                </label>
                <div className="space-y-3">
                  <label className="flex items-start space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="playground_type"
                      value="documents"
                      checked={spaceForm.playground_type === 'documents'}
                      onChange={(e) => setSpaceForm(prev => ({ ...prev, playground_type: e.target.value as 'documents' | 'images' }))}
                      className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span className="font-medium text-gray-900">Documents</span>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">PDF, DocX, XlsX, PptX files with AI-powered search and indexing</p>
                    </div>
                  </label>
                  
                  <label className="flex items-start space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="playground_type"
                      value="images"
                      checked={spaceForm.playground_type === 'images'}
                      onChange={(e) => setSpaceForm(prev => ({ ...prev, playground_type: e.target.value as 'documents' | 'images' }))}
                      className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span className="font-medium text-gray-900">Images</span>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">JPEG, PNG, WebP, HEIC , HEIF for visual content with AI vision</p>
                    </div>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Space Name *
                </label>
                <input
                  type="text"
                  required
                  value={spaceForm.name}
                  onChange={(e) => setSpaceForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 text-gray-700 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter space name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  rows={3}
                  value={spaceForm.description}
                  onChange={(e) => setSpaceForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 text-gray-700 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Optional description"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Space Type
                </label>
                <div className="space-y-3">
                  <label className="flex items-start space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="space_type"
                      value="personal"
                      checked={spaceForm.space_type === 'personal'}
                      onChange={(e) => setSpaceForm(prev => ({ ...prev, space_type: e.target.value as 'personal' | 'shared' }))}
                      className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <span className="font-medium text-gray-900">Private Space</span>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">Only you can access and manage this space</p>
                    </div>
                  </label>
                  
                  <label className="flex items-start space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="space_type"
                      value="shared"
                      checked={spaceForm.space_type === 'shared'}
                      onChange={(e) => setSpaceForm(prev => ({ ...prev, space_type: e.target.value as 'personal' | 'shared' }))}
                      className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span className="font-medium text-gray-900">Shared Space</span>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">Collaborate with team members - you become the Space Admin</p>
                    </div>
                  </label>
                </div>
              </div>

              {/* Group Creation Option - Only show for shared spaces */}
              {spaceForm.space_type === 'shared' && (
                <div className="border-t pt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Group Chat Options
                  </label>
                  <div className="space-y-3">
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={spaceForm.create_group}
                        onChange={(e) => setSpaceForm(prev => ({ ...prev, create_group: e.target.checked }))}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                          <span className="font-medium text-gray-900">Create ChatRoom </span>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">Automatically create a linked group chat for team collaboration</p>
                      </div>
                    </label>
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateSpaceForm(false)}
                  className="px-3 py-1 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  onClick={() => setLoadingSpaces(true)}
                  disabled={submitting}
                  className="px-3 py-1 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50"
                >
                  {submitting 
                    ? 'Creating...' 
                    : spaceForm.space_type === 'shared' && spaceForm.create_group 
                      ? 'Create Space with Group' 
                      : spaceForm.space_type === 'shared' 
                        ? 'Create Space without Group'
                        : 'Create Space'
                  }
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Manage Members Modal */}
      {showManageMembers !== null && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
            <h2 className="text-xl font-bold text-gray-800 mb-4">Manage Members</h2>

            {/* Existing members list */}
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-700 mb-1">Current Members</h3>
              <div className="max-h-40 overflow-y-auto border rounded-md divide-y divide-gray-100">
                {currentSpaceMembers && currentSpaceMembers.length > 0 ? (
                  currentSpaceMembers.map((m: any) => (
                    <div key={m.user_id} className="flex items-center justify-between px-3 py-2 text-sm">
                      <div className="flex items-center gap-2">
                        {/* Select for removal (owner cannot be removed) */}
                        {(() => {
                          const space = spaces.find(s => s.id === showManageMembers);
                          const isOwner = space ? (m.user_id === space.owner_id) : false;
                          return (
                            <input
                              type="checkbox"
                              className="accent-red-600"
                              disabled={isOwner}
                              checked={selectedMemberIdsToRemove.includes(m.user_id)}
                              onChange={() => {
                                if (isOwner) return;
                                setSelectedMemberIdsToRemove(prev => prev.includes(m.user_id)
                                  ? prev.filter(id => id !== m.user_id)
                                  : [...prev, m.user_id]
                                );
                              }}
                              title={isOwner ? 'Owner cannot be removed' : 'Select to remove'}
                            />
                          );
                        })()}
                        <div className="w-6 h-6 rounded-full bg-indigo-100 text-indigo-700 flex items-center justify-center text-xs font-semibold">
                          {(m.user_username || '').charAt(0).toUpperCase()}
                        </div>
                        <span className="text-gray-800">{m.user_username}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`text-xs px-2 py-0.5 rounded-full ${m.role === 'admin' ? 'bg-yellow-100 text-yellow-800' : 'bg-slate-100 text-slate-700'}`}>{m.role}</span>
                        {(() => {
                          const space = spaces.find(s => s.id === showManageMembers);
                          const isOwner = space ? (m.user_id === space.owner_id) : false;
                          return (
                            <button
                              type="button"
                              disabled={isOwner}
                              onClick={async () => {
                                if (isOwner) return;
                                if (!confirm(`Remove ${m.user_username} from space?`)) return;
                                try {
                                  await removeMembersFromSpace(showManageMembers as number, [m.user_id]);
                                  // Refresh members and spaces
                                  try {
                                    const detailed = await getSpaceWithMembers(showManageMembers as number);
                                    setCurrentSpaceMembers(Array.isArray(detailed?.members) ? detailed.members : []);
                                  } catch {}
                                  await refreshSpaces();
                                  setSelectedMemberIdsToRemove(prev => prev.filter(id => id !== m.user_id));
                                } catch (e) {
                                  alert('Failed to remove member');
                                }
                              }}
                              className={`p-1 rounded ${isOwner ? 'opacity-40 cursor-not-allowed' : 'border border-red-200 bg-red-50 text-red-500 hover:bg-red-100'}`}
                              title={isOwner ? 'Owner cannot be removed' : 'Remove member'}
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          );
                        })()}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="px-3 py-6 text-center text-sm text-gray-500">No members yet</div>
                )}
              </div>
            </div>

            {/* Search & add new members (Start New Chat style) */}
            <div className="mb-3">
              <div className="relative mb-2">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search users..."
                  value={memberSearch}
                  onChange={(e) => { setMemberSearchSearched(false); setMemberSearch(e.target.value); setMemberSearchResult(null); }}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white"
                />
              </div>
              <button 
                onClick={async () => {
                  const q = (memberSearch || '').trim();
                  setMemberSearchSearched(true);
                  if (!q) { setMemberSearchResult(null); return; }
                  // Prefer exact match from allUsers (server provides list)
                  const found = allUsers.find((u: any) => (u.username || '').toLowerCase() === q.toLowerCase());
                  setMemberSearchResult(found || null);
                }}
                className="mt-1 w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2.5 rounded-lg font-medium transition-colors flex items-center justify-center"
              >
                <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                Search
              </button>
            </div>

            {/* Search Results */}
            <div className="bg-white rounded-lg border border-gray-200 p-3 mb-4">
              {(!memberSearch || !memberSearchSearched) && (
                <div className="text-center text-sm text-gray-500">Enter a username to find users</div>
              )}
              {memberSearch && memberSearchSearched && (
                memberSearchResult ? (
                  selectedMemberIds.includes(memberSearchResult.user_id) ? (
                    <div className="text-center text-sm text-gray-500">
                      {memberSearchResult.username} is already selected
                    </div>
                  ) : (
                    <label className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        className="accent-indigo-600"
                        checked={false}
                        onChange={() => setSelectedMemberIds(prev => prev.includes(memberSearchResult.user_id) ? prev.filter(id => id !== memberSearchResult.user_id) : [...prev, memberSearchResult.user_id])}
                      />
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 rounded-full bg-indigo-100 text-indigo-700 flex items-center justify-center text-sm font-semibold">
                          {(memberSearchResult.username || '').charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-800">{memberSearchResult.username}</div>
                          <div className="text-xs text-gray-500">ID: {memberSearchResult.user_id}</div>
                        </div>
                      </div>
                    </label>
                  )
                ) : (
                  <div className="text-center text-sm text-gray-500">No user found with username <span className="text-indigo-600 font-medium">"{memberSearch}"</span></div>
                )
              )}
            </div>

            {/* Persistently show selected users to add */}
            {selectedMemberIds.length > 0 && (
              <div className="mb-4">
                <h3 className="text-sm font-medium text-gray-700 mb-1">Selected to add</h3>
                <div className="max-h-40 overflow-y-auto border rounded-md divide-y divide-gray-100">
                  {selectedMemberIds.map((uid) => {
                    const u = allUsers.find((x:any) => x.user_id === uid) || { username: 'Unknown', user_id: uid };
                    return (
                      <div key={uid} className="flex items-center justify-between px-3 py-2 text-sm">
                        <div className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            className="accent-indigo-600"
                            checked={true}
                            onChange={() => setSelectedMemberIds(prev => prev.filter(id => id !== uid))}
                            title="Unselect"
                          />
                          <div className="w-6 h-6 rounded-full bg-indigo-100 text-indigo-700 flex items-center justify-center text-xs font-semibold">
                            {(u.username || '').charAt(0).toUpperCase()}
                          </div>
                          <span className="text-gray-800">{u.username}</span>
                          <span className="text-xs text-gray-500">ID: {u.user_id}</span>
                        </div>
                        <button
                          type="button"
                          onClick={() => setSelectedMemberIds(prev => prev.filter(id => id !== uid))}
                          className="p-1 rounded border border-gray-200 bg-white text-gray-500 hover:bg-gray-50"
                          title="Remove from selection"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3 pt-2">
              <button
                type="button"
                onClick={() => { setSelectedMemberIds([]); setSelectedMemberIdsToRemove([]); setShowManageMembers(null); }}
                className="px-3 py-1 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Close
              </button>
              <button
                type="button"
                disabled={selectedMemberIdsToRemove.length === 0}
                onClick={async () => {
                  if (selectedMemberIdsToRemove.length === 0) return;
                  if (!confirm(`Remove ${selectedMemberIdsToRemove.length} selected member(s) from this space?`)) return;
                  try {
                    await removeMembersFromSpace(showManageMembers as number, selectedMemberIdsToRemove);
                    setSelectedMemberIdsToRemove([]);
                    // Refresh members and spaces
                    try {
                      const detailed = await getSpaceWithMembers(showManageMembers as number);
                      setCurrentSpaceMembers(Array.isArray(detailed?.members) ? detailed.members : []);
                    } catch {}
                    await refreshSpaces();
                  } catch (e) {
                    alert('Failed to remove members');
                  }
                }}
                className="px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                Remove Selected
              </button>
              <button
                type="button"
                disabled={selectedMemberIds.length === 0}
                onClick={async () => {
                  try {
                    await addMembersToSpace(showManageMembers as number, selectedMemberIds, 'member');
                    setSelectedMemberIds([]);
                    setShowManageMembers(null);
                    await refreshSpaces();
                  } catch (e) {
                    alert('Failed to add members');
                  }
                }}
                className="px-3 py-1 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50"
              >
                Add Members
              </button>
            </div>
          </div>
        </div>
      )}
      {/* Add Text Document Form */}
      {showAddTextForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold text-gray-800 mb-4">Add Text Document</h2>

            <form onSubmit={handleCreateTextDocument} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Space *
                </label>
                <div className="flex space-x-2 items-center">
                  <select
                    value={textForm.space_id || ''}
                    onChange={(e) => setTextForm(prev => ({ ...prev, space_id: parseInt(e.target.value) || 0 }))}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select a space</option>
                    {spaces
                      .filter(space => canAddTextDocument(space.playground_type)) // Filter out image spaces
                      .map(space => (
                        <option key={space.id} value={space.id}>
                          {space.name}
                        </option>
                      ))}
                  </select>
                  <button
                    type="button"
                    onClick={() => {
                      setInlineSpaceForm(prev => ({ ...prev, sourceModal: 'text' }));
                      setShowInlineCreateSpace(true);
                    }}
                    className="px-2 py-2 bg-indigo-600 text-white text-xs rounded hover:bg-indigo-700 flex-shrink-0"
                    title="Create New Space"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Text documents can only be added to document spaces
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Title *
                </label>
                <input
                  type="text"
                  required
                  value={textForm.title}
                  onChange={(e) => setTextForm(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Document title"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <input
                  type="text"
                  value={textForm.description}
                  onChange={(e) => setTextForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Brief description"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Content *
                </label>
                <textarea
                  required
                  rows={8}
                  value={textForm.content}
                  onChange={(e) => setTextForm(prev => ({ ...prev, content: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Document content..."
                />
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowAddTextForm(false)}
                  className="px-3 py-1 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={submitting || !textForm.space_id}
                  className="px-3 py-1 bg-blue-900 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                  {submitting ? 'Submitting...' : 'Submit Document'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Upload Document Form */}
      {showUploadForm && !isUploded && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold text-gray-800 mb-4">{uploadLabels.uploadSingleAction}</h2>

            <form onSubmit={handleUploadDocument} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Space *
                </label>
                <div className="flex space-x-2 items-center">
                  <select
                    value={uploadForm.space_id || ''}
                    onChange={(e) => setUploadForm(prev => ({ ...prev, space_id: parseInt(e.target.value) || 0 }))}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select a space</option>
                    {spaces.map(space => (
                      <option key={space.id} value={space.id}>
                        {space.name} ({space.playground_type === 'images' ? 'Images' : 'Documents'})
                      </option>
                    ))}
                  </select>
                  <button
                    type="button"
                    onClick={() => {
                      setInlineSpaceForm(prev => ({ ...prev, sourceModal: 'upload' }));
                      setShowInlineCreateSpace(true);
                    }}
                    className="px-2 py-2 bg-indigo-600 text-white text-xs rounded hover:bg-indigo-700 flex-shrink-0"
                    title="Create New Space"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {uploadLabels.itemName} File *
                </label>
                <input
                  type="file"
                  accept={getPlaygroundAcceptAttribute(uploadPlaygroundType)}
                  ref={fileInputRef}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="mt-1 text-xs text-gray-500">{uploadLabels.uploadHint}</p>
              </div>

              {/* Duplicate warning for single-file upload */}
              {singleDuplicateState && (
                <div className="mt-4 rounded-lg border border-yellow-300 bg-yellow-50 p-4 shadow-sm">
                  <p className="text-sm text-yellow-800 mb-3">
                    A document with the same name already exists
                    {singleDuplicateState.indexed && ' and is indexed'}.<br />
                    Do you want to keep both?
                  </p>
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      type="button"
                      onClick={() => {
                        singleDuplicateState.resolve(false);
                        setSingleDuplicateState(null);
                        setSubmitting(false);
                      }}
                      className="rounded-md border py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Skip Upload
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        singleDuplicateState.resolve(true);
                        setSingleDuplicateState(null);
                        // refreshSpaces();
                      }}
                      className="rounded-md bg-indigo-600 py-2 text-sm text-white hover:bg-indigo-700"
                    >
                      Keep Both & Upload
                    </button>
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowUploadForm(false)}
                  className="px-3 py-1 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={submitting || !uploadForm.space_id}
                  className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                >
                  {submitting ? 'Uploading...' : uploadLabels.uploadSingleAction}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Ingestion Status Modal - REMOVED */}

      {/* Bulk Upload Documents Form */}
      {showBulkUploadForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold text-gray-800 mb-4">{bulkLabels.uploadBulkAction}</h2>

            {/* If duplicate files detected show confirmation UI else show normal form */}

            <form onSubmit={handleBulkUpload} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Space *
                </label>
                <div className="flex space-x-2 items-center">
                  <select
                    value={bulkUploadSpaceId || ''}
                    onChange={(e) => setBulkUploadSpaceId(parseInt(e.target.value) || 0)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select a space</option>
                    {spaces.map(space => (
                      <option key={space.id} value={space.id}>
                        {space.name} ({space.playground_type === 'images' ? 'Images' : 'Documents'})
                      </option>
                    ))}
                  </select>
                  <button
                    type="button"
                    onClick={() => {
                      setInlineSpaceForm(prev => ({ ...prev, sourceModal: 'bulk' }));
                      setShowInlineCreateSpace(true);
                    }}
                    className="px-2 py-2 bg-indigo-600 text-white text-xs rounded hover:bg-indigo-700 flex-shrink-0"
                    title="Create New Space"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {bulkLabels.itemNamePlural} Files *
                </label>
                <input
                  type="file"
                  accept={getPlaygroundAcceptAttribute(bulkPlaygroundType)}
                  ref={bulkFileInputRef}
                  multiple
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Select multiple {bulkLabels.itemNamePlural.toLowerCase()}. {bulkLabels.uploadHint}
                </p>
              </div>

              {bulkUploadResult && (
                <div className={`mt-4 p-4 rounded-md ${bulkUploadResult.failed_count === 0 ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'
                  }`}>
                  <h3 className="font-medium text-sm mb-2">Upload Results</h3>
                  <p className="text-sm">
                    Successfully uploaded: <span className="font-semibold text-green-700"></span> documents
                  </p>
                  {bulkUploadResult.failed_count > 0 && (
                    <div className="mt-2">
                      <p className="font-semibold text-xs text-red-700">Failed uploads: {bulkUploadResult.failed_count}</p>
                      <ul className="mt-1 pl-5 list-disc">
                        {bulkUploadResult.failed_uploads.map((failed, index) => (
                          <li key={index} className="text-xs text-red-600 truncate" title={`${failed.filename}: ${failed.reason}`}>
                            {failed.filename}: {failed.reason}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
              {duplicateModalState && (
                <div className="mt-6 rounded-lg border border-yellow-300 bg-yellow-50 p-4 shadow-sm">
                  <div className="flex items-center mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-600 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M4.293 6.293l1.414 1.414M18.293 6.293l-1.414 1.414M12 3a9 9 0 110 18 9 9 0 010-18z" />
                    </svg>
                    <h3 className="text-sm font-semibold text-yellow-800">
                      {duplicateModalState.info.length} duplicate file{duplicateModalState.info.length > 1 ? 's' : ''} detected
                    </h3>
                  </div>
                  <ul className="max-h-32 overflow-y-auto space-y-1 text-xs text-gray-800 list-disc list-inside mb-4">
                    {duplicateModalState.info.map((d: any, idx: number) => (
                      <li key={idx} title={typeof d === 'string' ? d : d.filename} className="truncate">
                        {typeof d === 'string' ? d : d.filename}
                      </li>
                    ))}
                  </ul>
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      type="button"
                      onClick={handleDuplicateSkip}
                      className="rounded-md border border-gray-300 bg-white py-2 text-sm text-gray-700 transition hover:bg-gray-100"
                    >
                      Skip & Upload
                    </button>
                    <button
                      type="button"
                      onClick={handleDuplicateKeepBoth}
                      className="rounded-md bg-indigo-600 py-2 text-sm text-white transition hover:bg-indigo-700"
                    >
                      Keep Both & Upload
                    </button>
                  </div>
                </div>
              )}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setSubmitting(false); // ensure UI resets
                    setShowBulkUploadForm(false);
                    setBulkUploadResult(null);
                    setDuplicateModalState(null);
                    if (bulkFileInputRef.current) {
                      bulkFileInputRef.current.value = '';
                    }
                  }}
                  className="px-3 py-1 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Close
                </button>
                <button
                  type="submit"
                  disabled={submitting || !bulkUploadSpaceId}
                  className="px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50"
                >
                  {submitting ? 'Uploading...' : 'Upload Files'}
                </button>
              </div>

              {/* Duplicate warning below buttons */}

            </form>

          </div>
        </div>
      )}

      {/* Inline Create Space Modal */}
      {showInlineCreateSpace && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
            <h2 className="text-xl font-bold text-gray-800 mb-4">Create New Space</h2>

            <form onSubmit={(e) => {
              e.preventDefault();
              setSubmitting(true);

              createSpace({
                name: inlineSpaceForm.name,
                description: inlineSpaceForm.description || undefined,
                space_type: inlineSpaceForm.space_type,
                playground_type: inlineSpaceForm.playground_type, // ADD playground_type
                create_group: inlineSpaceForm.create_group
              })
                .then(newSpace => {
                  // Add the new space to the spaces list
                  // refreshSpaces();

                  // Update the form in the source modal
                  if (inlineSpaceForm.sourceModal === 'upload') {
                    setUploadForm(prev => ({ ...prev, space_id: newSpace.id }));
                  } else if (inlineSpaceForm.sourceModal === 'text') {
                    setTextForm(prev => ({ ...prev, space_id: newSpace.id }));
                  } else if (inlineSpaceForm.sourceModal === 'bulk') {
                    setBulkUploadSpaceId(newSpace.id);
                  }

                  // Reset the form and close the modal
                  setInlineSpaceForm({ name: '', description: '', space_type: 'personal', playground_type: 'documents', create_group: false, sourceModal: '' });
                  setShowInlineCreateSpace(false);
                })
                .catch(error => {
                  console.error('Error creating space:', error);
                  alert('Failed to create space. Please try again.');
                })
                .finally(() => {
                  setSubmitting(false);
                });
            }} className="space-y-4">
              {/* Playground Type Selection - First */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Playground Type *
                </label>
                <div className="space-y-3">
                  <label className="flex items-start space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="inline_playground_type"
                      value="documents"
                      checked={inlineSpaceForm.playground_type === 'documents'}
                      onChange={(e) => setInlineSpaceForm(prev => ({ ...prev, playground_type: e.target.value as 'documents' | 'images' }))}
                      className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span className="font-medium text-gray-900">Documents</span>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">PDF, DocX, XlsX, PptX files with AI-powered search and indexing</p>
                    </div>
                  </label>
                  
                  <label className="flex items-start space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="inline_playground_type"
                      value="images"
                      checked={inlineSpaceForm.playground_type === 'images'}
                      onChange={(e) => setInlineSpaceForm(prev => ({ ...prev, playground_type: e.target.value as 'documents' | 'images' }))}
                      className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span className="font-medium text-gray-900">Images</span>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">JPEG, PNG, WebP, HEIC , HEIF for visual content with AI vision</p>
                    </div>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Space Name *
                </label>
                <input
                  type="text"
                  required
                  value={inlineSpaceForm.name}
                  onChange={(e) => setInlineSpaceForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter space name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  rows={3}
                  value={inlineSpaceForm.description}
                  onChange={(e) => setInlineSpaceForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe this space..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Space Type
                </label>
                <div className="space-y-3">
                  <label className="flex items-start space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="inline_space_type"
                      value="personal"
                      checked={inlineSpaceForm.space_type === 'personal'}
                      onChange={(e) => setInlineSpaceForm(prev => ({ ...prev, space_type: e.target.value as 'personal' | 'shared', create_group: false }))}
                      className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <span className="font-medium text-gray-900">Private Space</span>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">Only you can access and manage this space</p>
                    </div>
                  </label>
                  
                  <label className="flex items-start space-x-3 p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="inline_space_type"
                      value="shared"
                      checked={inlineSpaceForm.space_type === 'shared'}
                      onChange={(e) => setInlineSpaceForm(prev => ({ ...prev, space_type: e.target.value as 'personal' | 'shared' }))}
                      className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span className="font-medium text-gray-900">Shared Space</span>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">Collaborate with team members - you become the Space Admin</p>
                    </div>
                  </label>
                </div>
              </div>

              {/* Group Creation Option - Only show for shared spaces */}
              {inlineSpaceForm.space_type === 'shared' && (
                <div className="border-t pt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Group Chat Options
                  </label>
                  <div className="space-y-3">
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={inlineSpaceForm.create_group}
                        onChange={(e) => setInlineSpaceForm(prev => ({ ...prev, create_group: e.target.checked }))}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                          <span className="font-medium text-gray-900">Create with Group Chat</span>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">Automatically create a linked group chat for team collaboration</p>
                      </div>
                    </label>
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setInlineSpaceForm({ name: '', description: '', space_type: 'personal', playground_type: 'documents', create_group: false, sourceModal: '' });
                    setShowInlineCreateSpace(false);
                  }}
                  className="px-3 py-1 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={submitting}
                  className="px-3 py-1 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50"
                >
                  {submitting 
                    ? 'Creating...' 
                    : inlineSpaceForm.space_type === 'shared' && inlineSpaceForm.create_group 
                      ? 'Create Space with Group' 
                      : inlineSpaceForm.space_type === 'shared' 
                        ? 'Create Space without Group'
                        : 'Create Space'
                  }
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Full Filename Modal */}
      {showFullNameModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4">
            <div className="flex justify-between items-start mb-4">
              <h2 className="text-lg font-semibold text-gray-800">Full Filename</h2>
              <button
                onClick={() => setShowFullNameModal(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="bg-gray-50 p-4 rounded border border-gray-200 break-all">
              {showFullNameModal}
            </div>
            <div className="flex justify-end mt-4">
              <button
                onClick={() => setShowFullNameModal(null)}
                className="px-3 py-1 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Document View Modal */}
      {selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h2 className="text-xl font-bold text-gray-800 mb-1 break-words">
                  {selectedDocument.title}
                </h2>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    {selectedDocument.space_name}
                  </span>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${selectedDocument.document_type === 'pdf' ? 'bg-red-100 text-red-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                    {selectedDocument.document_type ? selectedDocument.document_type.toUpperCase() : 'DOCUMENT'}
                  </span>
                </div>
              </div>
              <button
                onClick={() => setSelectedDocument(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="mb-4 text-sm text-gray-600">
              <p>Uploaded by {selectedDocument.creator_username} on {selectedDocument.created_at ? new Date(selectedDocument.created_at).toLocaleString() : 'Invalid Date'}</p>
              {selectedDocument.description && (
                <p className="mt-2">{selectedDocument.description}</p>
              )}
            </div>

            {/* Document Content */}
            <div className="mb-4">
              {!selectedDocument.document_type ? (
                <div className="bg-gray-50 p-4 rounded border border-gray-200">
                  <div className="flex flex-col items-center justify-center py-6">
                    <div className="text-amber-500 mb-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                    </div>
                    <p className="text-gray-600 text-center">
                      Document type is missing. Attempting to determine file type...
                    </p>
                    <button
                      onClick={() => {
                        // Try to infer document type from filename or extension
                        if (selectedDocument.filename) {
                          const filename = selectedDocument.filename.toLowerCase();
                          let inferredType = '';

                          if (filename.endsWith('.pdf')) inferredType = 'pdf';
                          else if (filename.endsWith('.docx') || filename.endsWith('.doc')) inferredType = 'docx';
                          else if (filename.endsWith('.txt') || filename.endsWith('.md')) inferredType = 'text';

                          if (inferredType) {
                           // //console.log(`Setting document_type to ${inferredType} based on filename ${selectedDocument.filename}`);
                            setSelectedDocument({ ...selectedDocument, document_type: inferredType });
                            return;
                          }
                        }

                        // If we couldn't infer from filename, try to fetch document again
                        handleViewDocument(selectedDocument.id);
                      }}
                      className="mt-4 px-4 py-2 bg-blue-900 text-white rounded hover:bg-blue-700 transition-colors"
                    >
                      Fix Document Type
                    </button>
                  </div>
                </div>
              ) : selectedDocument.document_type === 'text' && selectedDocument.content ? (
                <div className="bg-gray-50 p-4 rounded border border-gray-200">
                  <pre className="whitespace-pre-wrap text-gray-800 font-sans text-sm">{selectedDocument.content}</pre>
                </div>
              ) : selectedDocument.document_type === 'pdf' ? (
                <div className="flex flex-col">
                  {/* Always show PDF preview - use download URL if file_url is not available */}
                  <div className="bg-white border border-gray-200 rounded-lg h-[500px] overflow-hidden">
                    <iframe
                      src={`${API_BASE_URL}/knowledge/${selectedDocument.id}/download?token=${getSecureToken() || ''}&t=${Date.now()}#toolbar=0&view=FitH`}
                      className="w-full h-full"
                      title="PDF Preview"
                      onError={(e) => {
                        console.error("PDF iframe load error:", e);
                        console.error(`Failed URL: ${API_BASE_URL}/knowledge/${selectedDocument.id}/download (token omitted)`);
                      }}
                    />
                  </div>
                  {/* Download button moved to footer */}
                </div>
              ) : selectedDocument.document_type === 'docx' ? (
                <div className="bg-white border border-gray-200 rounded-lg max-h-[500px] overflow-auto relative">
                  <div className="sticky top-0 z-10 bg-white/95 backdrop-blur flex items-center justify-end gap-2 p-2 border-b border-gray-100">
                    <button
                      onClick={() => setDocxZoom(z => Math.max(0.5, parseFloat((z - 0.1).toFixed(2))))}
                      className="px-2 py-1 text-sm border rounded hover:bg-gray-50"
                      title="Zoom out"
                    >
                      −
                    </button>
                    <span className="text-xs text-gray-600 w-14 text-center">{Math.round(docxBaseScale * docxZoom * 100)}%</span>
                    <button
                      onClick={() => setDocxZoom(z => Math.min(2, parseFloat((z + 0.1).toFixed(2))))}
                      className="px-2 py-1 text-sm border rounded hover:bg-gray-50"
                      title="Zoom in"
                    >
                      +
                    </button>
                    <button
                      onClick={() => setDocxZoom(1)}
                      className="px-2 py-1 text-xs border rounded hover:bg-gray-50"
                      title="Actual size"
                    >
                      100%
                    </button>
                  </div>
                  <div className="p-4 flex justify-center" ref={docxContainerRef}>
                    <div
                      style={{
                        zoom: docxBaseScale * docxZoom, // ensures scroll height matches visual height
                        transformOrigin: 'top center'
                      }}
                    >
                      <div ref={docxPreviewRef} />
                    </div>
                  </div>
                </div>
              ) : selectedDocument.document_type === 'image' ? (
                <div className="bg-white border border-gray-200 rounded-lg max-h-[70vh] overflow-auto p-2">
                  {(() => {
                    const src = (selectedDocument.file_url && selectedDocument.file_url.length > 0)
                      ? selectedDocument.file_url
                      : `${API_BASE_URL}/knowledge/${selectedDocument.id}/download?token=${getSecureToken() || ''}&t=${Date.now()}`;
                    return (
                      <img
                        src={src}
                        alt={selectedDocument.title || selectedDocument.filename || 'Image'}
                        className="w-full object-contain max-h-[65vh] rounded"
                        onError={(e) => {
                          (e.currentTarget as HTMLImageElement).style.display = 'none';
                          const container = (e.currentTarget.parentElement as HTMLElement);
                          if (container) {
                            const fallback = document.createElement('div');
                            fallback.className = 'text-sm text-gray-600';
                            fallback.innerText = 'Image preview unavailable.';
                            container.appendChild(fallback);
                          }
                        }}
                      />
                    );
                  })()}
                </div>
              ) : (
                <p className="text-gray-600">No content available for this document.</p>
              )}
            </div>

            <div className="flex justify-between">
              <div>
                {(selectedDocument.document_type === 'pdf' || selectedDocument.document_type === 'docx') && (
                  <button
                    onClick={() => handleDownloadFile(selectedDocument.id)}
                    className="px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors mr-2"
                  >
                    Download
                  </button>
                )}
                {selectedDocument && selectedDocument.document_type !== 'image' && !selectedDocument.indexed && (
                  <button
                    onClick={() => {
                      if (selectedDocument && selectedDocument.id) {
                        handleIndexDocument(selectedDocument.id, selectedDocument.space_id);
                      } else {
                        console.error('Cannot index document: Invalid document ID');
                        alert('Cannot index document: Invalid document ID');
                      }
                      setSelectedDocument(null);
                    }}
                    disabled={indexingDocuments.has(selectedDocument.id)}
                    className="px-3 py-1 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {indexingDocuments.has(selectedDocument.id) ? 'Indexing...' : 'Index Document'}
                  </button>
                )}
              </div>
              <button
                onClick={() => setSelectedDocument(null)}
                className="px-3 py-1 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
