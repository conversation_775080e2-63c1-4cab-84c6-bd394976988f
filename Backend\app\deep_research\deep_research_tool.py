"""
Deep Research Tool Package
A comprehensive AI-powered research and analysis tool with multi-agent orchestration.
"""
from agno.agent import Agent
from agno.models.google import Gemini
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Literal, Union
import logging
import sys
import traceback
import gc
import atexit
from textwrap import dedent

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
)
logger = logging.getLogger(__name__)

# Global client instances to manage properly
_gemini_clients = {}


def _get_gemini_client(model_id: str) -> Gemini:
    """Get or create a Gemini client instance with proper cleanup."""
    print(f"[DEBUG] Getting Gemini client for model: {model_id}")
    if model_id not in _gemini_clients:
        print(f"[DEBUG] Creating new Gemini client for {model_id}")
        try:
            _gemini_clients[model_id] = Gemini(id=model_id)
            print(f"[DEBUG] Successfully created Gemini client for {model_id}")
        except Exception as e:
            print(f"[ERROR] Failed to create Gemini client for {model_id}: {e}")
            logger.error(f"Failed to create Gemini client for {model_id}: {e}")
            raise
    return _gemini_clients[model_id]

def _cleanup_clients():
    """Cleanup all client instances on exit."""
    print("[DEBUG] Cleaning up client instances...")
    try:
        
        for client_id, client in _gemini_clients.items():
            try:
                print(f"[DEBUG] Cleaning up Gemini client: {client_id}")
                if hasattr(client, '_client') and hasattr(client._client, 'close'):
                    client._client.close()
            except Exception as e:
                print(f"[WARNING] Error cleaning up Gemini client {client_id}: {e}")
                
        _gemini_clients.clear()
        gc.collect()
        print("[DEBUG] Client cleanup completed")
    except Exception as e:
        print(f"[ERROR] Error during client cleanup: {e}")

# Register cleanup function
atexit.register(_cleanup_clients)

# Initialize models with proper error handling
print("[DEBUG] Initializing AI models...")
try:
    gemini_flash = _get_gemini_client("gemini-2.5-flash")
    print("[DEBUG] ✓ Gemini Flash initialized")
except Exception as e:
    print(f"[ERROR] Failed to initialize Gemini Flash: {e}")
    gemini_flash = None

print("[DEBUG] Model initialization completed")

# Response Models for Structured Output
class QueryResponse(BaseModel):
    queries: List[str]

class QueryAnalysis(BaseModel):
    """Structured model for query complexity analysis."""
    complexity: Literal["low", "medium", "high"] = Field(
        description="Query complexity level"
    )
    requires_search: bool = Field(
        description="Whether the query requires information retrieval"
    )
    requires_verification: bool = Field(
        description="Whether the query requires fact verification"
    )
    requires_analysis: bool = Field(
        description="Whether the query requires data analysis or complex reasoning"
    )
    requires_calculation_or_coding: bool = Field(
        description="Whether the query requires calculations or coding tasks"
    )
    domain_focus: List[str] = Field(
        default=[],
        description="Primary domains this query focuses on (e.g., 'scientific', 'financial', 'news', 'legal')"
    )
    reasoning: str = Field(
        description="Detailed explanation of the analysis decision"
    )

class AgentSelection(BaseModel):
    """Structured model for agent selection."""
    search_agents: List[str] = Field(
        default=[],
        description="Selected search agents for information gathering"
    )
    verification_agents: List[str] = Field(
        default=[],
        description="Selected verification agents for fact checking"
    )
    analysis_agents: List[str] = Field(
        default=[],
        description="Selected analysis agents for data processing"
    )
    reasoning: str = Field(
        description="Explanation for agent selection decisions"
    )

# QueryReformulation Agent with enhanced error handling
print("[DEBUG] Creating QueryReformulationAgent...")
try:
    QueryReformulationAgent = Agent(
        name="QueryReformulationAgent",
        model=gemini_flash,
        description="Expert at understanding user queries, reformulating them for clarity, and decomposing complex tasks into subtasks.",
        instructions=[
            "Analyze user queries for clarity, completeness, and complexity",
            "Reformulate vague or ambiguous queries to make them more specific and actionable",
            "Break down complex tasks into logical subtasks when appropriate",
            "Identify implied questions within the user's query that should be addressed",
            "Preserve the user's original intent while improving query structure",
            "Generate multiple queries only when the task logically requires decomposition into subtasks",
            "Determine when a query is already clear and specific enough to use as-is",
            "Explain your reasoning for reformulation or decomposition decisions",
            "Return a list containing either a single reformulated query or multiple sub-queries",
            "Keep reformulations concise and focused on the core information need"
        ],
        # response_model=QueryResponse,
        debug_mode=True,
        markdown=True
    )
    print("[DEBUG] ✓ QueryReformulationAgent created successfully")
except Exception as e:
    print(f"[ERROR] Failed to create QueryReformulationAgent: {e}")
    logger.error(f"Failed to create QueryReformulationAgent: {e}")
    QueryReformulationAgent = None

class RouterAgent:
    """
    Enhanced LLM-based router agent with structured response models
    that analyzes queries, reformulates them, potentially divides into sub-tasks,
    and determines which teams and agents should handle them.
    """

    def __init__(self, debug: bool = False):
        """
        Initialize the router agent.

        Args:
            debug: Enable debug logging
        """
        print(f"[DEBUG] Initializing RouterAgent with debug={debug}")
        self.debug = debug
        self.cancellation_event = None
        
        # Select best available models with fallbacks
        self.model = gemini_flash
        print(f"[DEBUG] Selected primary model: {self.model.id if self.model else 'None'}")

        if not self.model:
            error_msg = "Failed to initialize models - no working models available"
            print(f"[ERROR] {error_msg}")
            logger.error(error_msg)
            raise RuntimeError(error_msg)

        # Define available agents by category
        self.available_agents = {
            "search": [
                "GeneralSearchAgent", "URLSearchAgent", "DeepSearchAgent", "ScientificAgent", 
                "NewsAgent", "LegalComplianceAgent"
            ],
            "verification": ["VerificationAgent"],
            "analysis": ["AnalyticalAgent", "CodeExecutionAgent"],
            "synthesis": ["SynthesisAgent"],
            "response_generator": ["ResponseGenerator"]
        }
        
        print(f"[DEBUG] RouterAgent initialized with {len(self.available_agents)} agent categories")
        logger.info("RouterAgent initialized successfully")
        
    def reformulate_query(self, query: str) -> List[str]:
        """
        Reformulate the user's query for clarity and possibly decompose it into subtasks.
        
        Args:
            query: The original user query
            
        Returns:
            List of one or more reformulated queries
        """
        print(f"[DEBUG] Starting query reformulation for: {query[:100]}...")
        logger.info(f"Reformulating query: {query}")
        
        if not QueryReformulationAgent:
            print("[WARNING] QueryReformulationAgent not available, returning original query")
            logger.warning("QueryReformulationAgent not available, returning original query")
            return [query]
        
        prompt = f"""
You are an expert query analyzer and reformulator. Your task is to:

1. Understand the user's original query
2. Reformulate it to be more specific, clear, and answerable if needed
3. Determine if the task needs to be broken down into multiple subtasks
4. Return either a single reformulated query or a list of sub-queries

ORIGINAL QUERY: "{query}"

GUIDELINES:
- If the query is already clear, specific, and represents a single coherent task, simply return it with minimal adjustments
- If the query is vague, ambiguous, or could be interpreted in multiple ways, reformulate it to be more specific
- If the query represents a complex task that should be divided into subtasks, break it down into logical sub-queries
- Preserve the user's original intent and information needs
- Don't create multiple sub-queries unless the task logically requires decomposition
- Each query or sub-query should be self-contained and independently answerable

Return your response as a list of strings, where each string is either:
1. A single reformulated query that captures the user's intent more clearly
2. Multiple sub-queries that together address the complex task

ONLY return the list of queries, with no additional explanations.
"""

        try:
            # 🔬 ROUTER CANCELLATION CHECK - Query Reformulation
            if hasattr(self, 'cancellation_event') and self.cancellation_event and self.cancellation_event.is_set():
                print("[DEBUG] 🔬 ROUTER CANCELLED: Before query reformulation")
                logger.info("🔬 RouterAgent: Query reformulation cancelled before AI call")
                from ..agents.base import QueryCancelledException
                raise QueryCancelledException()
            
            print("[DEBUG] Calling QueryReformulationAgent...")
            response = QueryReformulationAgent.run(prompt).content
            
            print(f"[DEBUG] Query reformulation response: {response.queries}")
            if self.debug:
                logger.info(f"Query reformulation completed: {response.queries}")
            
            return response.queries
            
        except Exception as e:
            if hasattr(e, 'is_cancelled') and e.is_cancelled:
                logger.info("🔬 RouterAgent: Query reformulation cancelled by user (expected behavior)")
                print("[DEBUG] Query reformulation cancelled by user")
                raise e  # Re-raise to stop the entire routing process
            else:
                error_msg = f"Error in query reformulation: {e}"
                print(f"[ERROR] {error_msg}")
                logger.error(error_msg)
                print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                # Return original query as fallback
                print("[DEBUG] Falling back to original query")
                return [query]

    def analyze_query_complexity(self, query: str) -> QueryAnalysis:
        """
        Analyze query complexity using LLM with structured response model.

        Args:
            query: The user query

        Returns:
            QueryAnalysis object with structured analysis results
        """
        print(f"[DEBUG] Starting query complexity analysis for: {query[:100]}...")
        logger.info(f"Analyzing query complexity: {query}")
        
        prompt = f"""
You are an expert query analyzer. Analyze the following query to determine its complexity and requirements.

QUERY: "{query}"

COMPLEXITY LEVELS:
- LOW: Simple factual queries, basic information retrieval, straightforward questions, general conversation
- MEDIUM: Queries requiring context from multiple sources, moderate reasoning, domain-specific knowledge
- HIGH: Complex analytical queries, multi-step reasoning, technical analysis, financial modeling, advanced calculations

REQUIREMENTS ANALYSIS:
- requires_search: True if query needs external information retrieval (facts, current events, specific data)
- requires_verification: True only in rare cases only if query involves factual claims, legal matters, or critical information that absolutely needs validation otherwise False as in most cases it is not required
- requires_analysis: True if query needs data processing, complex reasoning, pattern analysis, or technical evaluation
- requires_calculation_or_coding: True if query involves mathematical calculations, coding tasks, or computational work

DOMAIN FOCUS OPTIONS:
- scientific: Academic research, scientific papers, technical documentation
- financial: Market data, financial analysis, economic information
- news: Current events, trending topics, recent developments
- legal: Legal documents, regulations, compliance matters
- technical: Programming, technology, engineering topics
- medical: Health, medical research, clinical information
- general: General knowledge, everyday information
- url: Url search for the information from the urls given by the user

IMPORTANT GUIDELINES:
1. Analyze the query for its specific requirements and complexity
2. Identify the primary domain(s) the query focuses on
3. Be precise in your assessments - avoid over-selecting requirements
4. For conversational queries or general knowledge questions, set requires_search to False
5. Only set requires_verification to True for factual or critical information queries

Provide your analysis in the exact JSON format specified by the response model.
"""

        try:
            print("[DEBUG] Creating analysis agent...")
            analysis_agent = Agent(
                model=self.model,
                instructions=[
                    "You are a precise query analyzer.",
                    "Analyze the query methodically based on the given criteria.",
                    "Identify specific domain requirements for optimal agent selection.",
                    "Be accurate in complexity and requirement assessments.",
                    "Provide structured JSON responses only."
                ],
            )
            print("[DEBUG] Analysis agent created, running analysis...")
            
            # 🔬 ROUTER CANCELLATION CHECK - Complexity Analysis
            if hasattr(self, 'cancellation_event') and self.cancellation_event and self.cancellation_event.is_set():
                print("[DEBUG] 🔬 ROUTER CANCELLED: Before complexity analysis")
                logger.info("🔬 RouterAgent: Complexity analysis cancelled before AI call")
                from ..agents.base import QueryCancelledException
                raise QueryCancelledException()

            response = analysis_agent.run(prompt).content
            
            print(f"[DEBUG] Query analysis completed: complexity={response.complexity}, requires_search={response.requires_search}, domain_focus={response.domain_focus}")
            if self.debug:
                logger.info(f"Query analysis completed: {response}")
            
            return response

        except Exception as e:
            if hasattr(e, 'is_cancelled') and e.is_cancelled:
                logger.info("🔬 RouterAgent: Query complexity analysis cancelled by user (expected behavior)")
                print("[DEBUG] Query complexity analysis cancelled by user")
                raise e  # Re-raise to stop the entire routing process
            else:
                error_msg = f"Error in query analysis: {e}"
                print(f"[ERROR] {error_msg}")
                logger.error(error_msg)
                print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                
                # Return a default analysis as fallback
                print("[DEBUG] Creating fallback analysis...")
                fallback_analysis = QueryAnalysis(
                    complexity="medium",
                    requires_search=True,
                    requires_verification=False,
                    requires_analysis=False,
                    requires_calculation_or_coding=False,
                    domain_focus=["general"],
                    reasoning="Fallback analysis due to error in main analysis"
                )
                print(f"[DEBUG] Returning fallback analysis: {fallback_analysis}")
                return fallback_analysis

    def select_agents(self, query: str, analysis: QueryAnalysis) -> AgentSelection:
        """
        Select appropriate agents based on query analysis with structured response model.

        Args:
            query: The user query
            analysis: QueryAnalysis object with query analysis results

        Returns:
            AgentSelection object with selected agents
        """
        print(f"[DEBUG] Starting agent selection for query: {query[:50]}...")
        print(f"[DEBUG] Analysis input - complexity: {analysis.complexity}, search: {analysis.requires_search}, analysis: {analysis.requires_analysis}")
        logger.info(f"Selecting agents for query: {query}")

        agents_info = f"""
AVAILABLE AGENTS:

SEARCH AGENTS:
- GeneralSearchAgent: General web search and information retrieval
- URLSearchAgent: Url search for the information from the urls given by the user
- DeepSearchAgent: Comprehensive deep search for complex topics of any domain excluding 'legal' and 'financial'. (use instead of GeneralSearchAgent for thorough research)
- ScientificAgent: Scientific papers, research, and technical information
- NewsAgent: Current news, events, and recent developments
- LegalComplianceAgent: Legal information, regulations, and compliance matters

VERIFICATION AGENTS:
- VerificationAgent: Fact-checking and information validation

ANALYSIS AGENTS:
- AnalyticalAgent: Data analysis, pattern recognition, and complex reasoning
- CodeExecutionAgent: Code execution, programming tasks, and computational work

"""

        prompt = f"""
You are an expert agent selector. Based on the query and its analysis, select the most appropriate agents.

QUERY: "{query}"

ANALYSIS RESULTS:
- Complexity: {analysis.complexity}
- Requires Search: {analysis.requires_search}
- Requires Verification: {analysis.requires_verification}
- Requires Analysis: {analysis.requires_analysis}
- Requires Calculation/Coding: {analysis.requires_calculation_or_coding}
- Domain Focus: {', '.join(analysis.domain_focus) if analysis.domain_focus else 'general'}
- Reasoning: {analysis.reasoning}

{agents_info}

SELECTION GUIDELINES:
1. SEARCH AGENTS:
   - Only select if requires_search is True
   - Match domain-specific agents to the query's domain focus
   - Use DeepSearchAgent for complex, multi-faceted research (don't combine with GeneralSearchAgent)
   - Use GeneralSearchAgent only for simple, general queries without specific domain focus
   - Use URLSearchAgent for url search for the information from the urls given by the user

2. VERIFICATION AGENTS:
   - Only select if requires_verification is True
   - Essential for factual claims, legal matters, or critical information

3. ANALYSIS AGENTS:
   - Only select if requires_analysis or requires_calculation_or_coding is True
   - AnalyticalAgent for data analysis, pattern recognition, and complex reasoning
   - CodeExecutionAgent for programming, calculations, or computational tasks

IMPORTANT CONSIDERATIONS:
- Prioritize comprehensive information retrieval from multiple agents instead of just domain specific agents e.g. if query is about weather, then use Current events then use NewsAgent and GeneralSearchAgent both.
- Ensure comprehensive coverage while avoiding unnecessary redundancy
- **Consider query complexity when selecting between GeneralSearchAgent and DeepSearchAgent, prefer DeepSearchAgent for medium and high complexity queries**
- Since DeepSearchAgent does not have access to 'legal' and 'financial' information, so for complex queries requiring 'legal' or 'financial' information along with other domain information for analysis, use DeepSearchAgent along with respective agents.

Select agents and provide detailed reasoning for your choices, explaining how they address the query requirements.
"""

        try:
            print("[DEBUG] Creating agent selection agent...")
            selection_agent = Agent(
                model=self.model,
                instructions=[
                    "You are a precise agent selector.",
                    "Select only necessary agents based on query requirements.",
                    "Avoid redundant agent selections.",
                    "Provide clear reasoning for selections.",
                    "Follow the selection guidelines strictly."
                ],
            )
            print("[DEBUG] Agent selection agent created, running selection...")
            
            # 🔬 ROUTER CANCELLATION CHECK - Agent Selection
            if hasattr(self, 'cancellation_event') and self.cancellation_event and self.cancellation_event.is_set():
                print("[DEBUG] 🔬 ROUTER CANCELLED: Before agent selection")
                logger.info("🔬 RouterAgent: Agent selection cancelled before AI call")
                from ..agents.base import QueryCancelledException
                raise QueryCancelledException()

            response = selection_agent.run(prompt).content
            
            print(f"[DEBUG] Agent selection completed:")
            print(f"[DEBUG] - Search agents: {response.search_agents}")
            print(f"[DEBUG] - Verification agents: {response.verification_agents}")
            print(f"[DEBUG] - Analysis agents: {response.analysis_agents}")
            print(f"[DEBUG] - Reasoning: {response.reasoning[:100]}...")
            
            if self.debug:
                logger.info(f"Agent selection completed: {response}")
            
            return response

        except Exception as e:
            if hasattr(e, 'is_cancelled') and e.is_cancelled:
                logger.info("🔬 RouterAgent: Agent selection cancelled by user (expected behavior)")
                print("[DEBUG] Agent selection cancelled by user")
                raise e  # Re-raise to stop the entire routing process
            else:
                error_msg = f"Error in agent selection: {e}"
                print(f"[ERROR] {error_msg}")
                logger.error(error_msg)
                print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                
                # Return a default selection as fallback
                print("[DEBUG] Creating fallback agent selection...")
                fallback_selection = AgentSelection(
                    search_agents=["GeneralSearchAgent"] if analysis.requires_search else [],
                    verification_agents=[],
                    analysis_agents=["AnalyticalAgent"] if analysis.requires_analysis else [],
                    reasoning="Fallback selection due to error in main selection"
                )
                print(f"[DEBUG] Returning fallback selection: {fallback_selection}")
                return fallback_selection

    def compile_routing_result(self, query: str, analysis: QueryAnalysis, agent_selection: AgentSelection) -> Dict[str, Any]:
        """
        Compile the final routing result with all selected agents organized by function.

        Args:
            query: Original user query
            analysis: Query analysis results
            agent_selection: Selected agents

        Returns:
            Complete routing information dictionary
        """
        print(f"[DEBUG] Compiling routing result for query: {query[:50]}...")
        logger.info(f"Compiling routing result for query: {query}")
        
        selected_teams = {
            "search_agents": agent_selection.search_agents,
            "verification_agents": agent_selection.verification_agents,
            "analysis_agents": agent_selection.analysis_agents
        }

        routing_result = {
            "query": query,
            "analysis": {
                "complexity": analysis.complexity,
                "requires_search": analysis.requires_search,
                "requires_verification": analysis.requires_verification,
                "requires_analysis": analysis.requires_analysis,
                "requires_calculation_or_coding": analysis.requires_calculation_or_coding,
                "domain_focus": analysis.domain_focus,
                "reasoning": analysis.reasoning
            },
            "agent_selection_reasoning": agent_selection.reasoning,
            "selected_teams": selected_teams,
            "total_agents": sum(len(agents) for agents in selected_teams.values())
        }

        print(f"[DEBUG] Routing result compiled with {routing_result['total_agents']} total agents")
        logger.info(f"Routing result compiled with {routing_result['total_agents']} total agents")
        return routing_result

    def route_query(self, query: str) -> Dict[str, Any]:
        """
        Main routing function that orchestrates the entire process.

        Args:
            query: The user query

        Returns:
            Dictionary with complete routing information or a list of dictionaries for multi-query tasks
        """
        print(f"[DEBUG] ========== STARTING QUERY ROUTING ==========")
        print(f"[DEBUG] Query: {query}")
        
        # 🔬 ROUTER CANCELLATION CHECK - Start
        if hasattr(self, 'cancellation_event') and self.cancellation_event and self.cancellation_event.is_set():
            print("[DEBUG] 🔬 ROUTER CANCELLED: Before starting route_query")
            logger.info("🔬 RouterAgent: route_query cancelled before starting")
            from ..agents.base import QueryCancelledException
            raise QueryCancelledException()
        
        logger.info(f"Routing query: {query}")

        try:
            # Step 1: Reformulate the query and potentially break it into sub-queries
            print("[DEBUG] Step 1: Query reformulation...")
            reformulated_queries = self.reformulate_query(query)
            print(f"[DEBUG] Reformulated queries: {reformulated_queries}")
            
            # If we have multiple queries, we'll need to route each one
            if len(reformulated_queries) > 1:
                print(f"[DEBUG] Multiple queries detected: {len(reformulated_queries)} sub-queries")
                logger.info(f"Task broken into {len(reformulated_queries)} sub-queries")
                
                # Route each sub-query
                routing_results = []
                for i, sub_query in enumerate(reformulated_queries):
                    print(f"[DEBUG] Processing sub-query {i+1}/{len(reformulated_queries)}: {sub_query}")
                    logger.info(f"Routing sub-query {i+1}: {sub_query}")
                    
                    try:
                        # For each sub-query, perform the regular routing process
                        print(f"[DEBUG] Analyzing sub-query {i+1}...")
                        analysis = self.analyze_query_complexity(sub_query)
                        
                        print(f"[DEBUG] Selecting agents for sub-query {i+1}...")
                        agent_selection = self.select_agents(sub_query, analysis)
                        
                        print(f"[DEBUG] Compiling routing result for sub-query {i+1}...")
                        routing_result = self.compile_routing_result(sub_query, analysis, agent_selection)
                        routing_results.append(routing_result)
                        
                        print(f"[DEBUG] ✓ Sub-query {i+1} routing completed")
                        
                    except Exception as e:
                        error_msg = f"Error routing sub-query {i+1}: {e}"
                        print(f"[ERROR] {error_msg}")
                        logger.error(error_msg)
                        print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                        continue
                
                # Return results for all sub-queries along with original query and metadata
                result = {
                    "original_query": query,
                    "multiple_queries": True,
                    "queries_count": len(reformulated_queries),
                    "routing_results": routing_results
                }
                print(f"[DEBUG] ========== MULTI-QUERY ROUTING COMPLETED ==========")
                print(f"[DEBUG] Successfully routed {len(routing_results)}/{len(reformulated_queries)} sub-queries")
                return result
            
            else:
                # Single query case - proceed with normal routing
                print("[DEBUG] Single query processing")
                logger.info("Single query processing")
                reformulated_query = reformulated_queries[0]
                print(f"[DEBUG] Working with reformulated query: {reformulated_query}")
                
                # Step 2: Analyze query complexity and requirements
                print("[DEBUG] Step 2: Query complexity analysis...")
                analysis = self.analyze_query_complexity(reformulated_query)
                
                # Step 3: Select appropriate agents based on analysis
                print("[DEBUG] Step 3: Agent selection...")
                agent_selection = self.select_agents(reformulated_query, analysis)
                
                # Step 4: Compile final routing result
                print("[DEBUG] Step 4: Compiling routing result...")
                routing_result = self.compile_routing_result(reformulated_query, analysis, agent_selection)
                
                # Add metadata about reformulation
                routing_result["multiple_queries"] = False
                
                print(f"[DEBUG] ========== SINGLE QUERY ROUTING COMPLETED ==========")
                print(f"[DEBUG] Successfully routed query: {reformulated_query}")
                logger.info(f"Routing completed successfully for query: {reformulated_query}")
                return routing_result

        except Exception as e:
            if hasattr(e, 'is_cancelled') and e.is_cancelled:
                logger.info("🔬 RouterAgent: route_query cancelled by user (expected behavior)")
                print("[DEBUG] Route query cancelled by user")
                raise e
            else:
                error_msg = f"Critical error in route_query: {e}"
                print(f"[ERROR] {error_msg}")
                logger.error(error_msg)
                print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                raise e
