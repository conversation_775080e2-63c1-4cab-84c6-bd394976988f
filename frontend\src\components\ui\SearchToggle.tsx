/**
 * Unified SearchToggle component
 * Reusable toggle switch for different search features
 */

import React from 'react';

export type SearchToggleVariant = 'blue' | 'green' | 'purple';

export interface SearchToggleProps {
  /** Current enabled state */
  enabled: boolean;
  /** Callback when toggle is clicked */
  onToggle: () => void;
  /** Display label for the toggle */
  label: string;
  /** Icon element (optional) */
  icon?: React.ReactNode;
  /** Color variant for the toggle */
  variant?: SearchToggleVariant;
  /** Optional className for additional styling */
  className?: string;
  /** Show as pill button (compact mode) */
  compact?: boolean;
}

const variantStyles = {
  blue: {
    active: 'bg-blue-100 text-blue-700 border-blue-300',
    inactive: 'bg-white text-gray-700 border-gray-300 hover:bg-blue-50',
  },
  green: {
    active: 'bg-green-100 text-green-700 border-green-300',
    inactive: 'bg-white text-gray-700 border-gray-300 hover:bg-green-50',
  },
  purple: {
    active: 'bg-purple-100 text-purple-700 border-purple-300',
    inactive: 'bg-white text-gray-700 border-gray-300 hover:bg-purple-50',
  },
};

export default function SearchToggle({
  enabled,
  onToggle,
  label,
  icon,
  variant = 'blue',
  className = '',
  compact = false,
}: SearchToggleProps) {
  const colors = variantStyles[variant];

  // Compact mode - pill button style
  if (compact) {
    return (
      <button
        className={`flex items-center space-x-1 p-1 sm:p-[9px] rounded-full transition-colors border ${
          enabled ? colors.active : colors.inactive
        } ${className}`}
        onClick={onToggle}
        type="button"
        title={label}
      >
        {icon}
        <span className="hidden sm:inline">{label}</span>
      </button>
    );
  }

  // Default mode - full toggle with switch
  return (
    <div className={`flex items-center space-x-2 p-3 border border-gray-300 bg-white rounded-lg mb-2 ${className}`}>
      <label className="flex items-center cursor-pointer">
        <div className="relative">
          <input
            type="checkbox"
            className="sr-only"
            checked={enabled}
            onChange={onToggle}
            aria-label={label}
          />
          <div 
            className={`block w-10 h-6 rounded-full ${
              enabled ? `bg-${variant}-900` : 'bg-gray-200'
            }`} 
          />
          <div 
            className={`dot absolute left-1 top-1 w-4 h-4 rounded-full transition ${
              enabled ? 'transform translate-x-4 bg-white' : 'bg-white'
            }`} 
          />
        </div>
        <div className="ml-3 text-gray-800 font-medium">
          {label}
        </div>
      </label>
    </div>
  );
}
