'use client';

import React, { ReactNode } from 'react';
import { usePathname } from 'next/navigation';
import Header from './Header';
import Footer from './Footer';

interface MainLayoutProps {
  children: ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const pathname = usePathname();
  const isChatPage = pathname === '/chat';
  const isDashboardPage = pathname === '/dashboard' || pathname === '/activity';
  
  // For chat and dashboard pages, use fixed height with overflow hidden
  if (isChatPage || isDashboardPage) {
    return (
      <div className="flex flex-col h-screen overflow-hidden">
        <Header />
        <main className="flex-1 min-w-0 w-full overflow-hidden flex flex-col">
          {children}
        </main>
        <Footer />
      </div>
    );
  }
  
  // For other pages (like home), use natural scrolling
  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-1 min-w-0 w-full flex flex-col">
        {children}
      </main>
      {/* Show footer on all pages */}
      <Footer />
    </div>
  );
};

export default MainLayout; 