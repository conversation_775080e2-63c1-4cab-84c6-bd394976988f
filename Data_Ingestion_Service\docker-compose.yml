version: '3.9'

services:
  data_ingestion_service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: data_ingestion_service
    env_file:
      - .env
    ports:
      - "8001:8001"
    restart: unless-stopped
    environment:
      - HOST=0.0.0.0
      - DATA_INGESTION_PORT=8001
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              capabilities: ["gpu"]
    networks:
      - data_ingestion_network
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8001/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  data_ingestion_network:
    driver: bridge
