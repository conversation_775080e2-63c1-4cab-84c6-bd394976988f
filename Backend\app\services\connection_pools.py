"""
Connection Pool Manager for Stateless Architecture

Manages shared connection pools to avoid memory exhaustion from cached instances.
All pipelines use shared pools instead of creating their own clients.
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager, contextmanager
from threading import Lock
import requests
from google import genai
from qdrant_client import QdrantClient
import redis.asyncio as redis
from ..config import GEMINI_API_KEY
# Import HTTP client for embedding service communication
from ..Data_Retriever.embedding_http_client import EmbeddingClient, EmbeddingConfig

logger = logging.getLogger(__name__)

class ConnectionPoolManager:
    """
    Manages all connection pools for stateless pipeline architecture.
    Ensures proper resource management and prevents memory leaks.
    """
    
    def __init__(self):
        self._gemini_pool: Dict[str, genai.Client] = {}
        self._qdrant_pool: Dict[str, QdrantClient] = {} 
        self._embedding_pool: Dict[str, EmbeddingClient] = {}
        self._http_sessions: Dict[str, requests.Session] = {}
        self._redis_pool: Optional[redis.ConnectionPool] = None
        self._lock = Lock()
        
        # Pool configuration
        self.max_pool_size = 10
        self.pool_timeout = 30
        
        logger.info("Connection Pool Manager initialized")
    
    @contextmanager
    def get_gemini_client(self, api_key: str = None):
        """
        Get a Gemini client from the pool.
        Creates new client if not in pool, reuses existing one.
        """
        api_key = api_key or GEMINI_API_KEY
        client_key = f"gemini_{hash(api_key) % 1000}"
        
        with self._lock:
            if client_key not in self._gemini_pool:
                if len(self._gemini_pool) >= self.max_pool_size:
                    # Remove oldest client to make space
                    oldest_key = next(iter(self._gemini_pool))
                    del self._gemini_pool[oldest_key]
                    logger.debug(f"Removed oldest Gemini client: {oldest_key}")
                
                self._gemini_pool[client_key] = genai.Client(api_key=api_key)
                logger.debug(f"Created new Gemini client: {client_key}")
            
            client = self._gemini_pool[client_key]
        
        try:
            yield client
        except Exception as e:
            logger.error(f"Error using Gemini client {client_key}: {e}")
            # Remove faulty client from pool
            with self._lock:
                if client_key in self._gemini_pool:
                    del self._gemini_pool[client_key]
            raise
    
    @contextmanager 
    def get_qdrant_client(self, url: str, api_key: str = None, 
                         prefer_grpc: bool = True, grpc_port: int = 6334):
        """
        Get a Qdrant client from the pool.
        """
        client_key = f"qdrant_{hash(f'{url}_{api_key}_{prefer_grpc}_{grpc_port}') % 1000}"
        
        with self._lock:
            if client_key not in self._qdrant_pool:
                if len(self._qdrant_pool) >= self.max_pool_size:
                    # Remove oldest client
                    oldest_key = next(iter(self._qdrant_pool))
                    try:
                        self._qdrant_pool[oldest_key].close()
                    except:
                        pass
                    del self._qdrant_pool[oldest_key]
                    logger.debug(f"Removed oldest Qdrant client: {oldest_key}")
                
                self._qdrant_pool[client_key] = QdrantClient(
                    url=url,
                    api_key=api_key,
                    prefer_grpc=prefer_grpc,
                    grpc_port=grpc_port,
                    timeout=self.pool_timeout
                )
                logger.debug(f"Created new Qdrant client: {client_key}")
            
            client = self._qdrant_pool[client_key]
        
        try:
            yield client
        except Exception as e:
            logger.error(f"Error using Qdrant client {client_key}: {e}")
            # Remove faulty client from pool
            with self._lock:
                if client_key in self._qdrant_pool:
                    try:
                        self._qdrant_pool[client_key].close()
                    except:
                        pass
                    del self._qdrant_pool[client_key]
            raise
    
    @contextmanager
    def get_embedding_client(self, api_url: str, timeout: int = 60, retry_attempts: int = 3):
        """
        Get an embedding client from the pool.
        """
        client_key = f"embedding_{hash(f'{api_url}_{timeout}') % 1000}"
        
        with self._lock:
            if client_key not in self._embedding_pool:
                if len(self._embedding_pool) >= self.max_pool_size:
                    # Remove oldest client
                    oldest_key = next(iter(self._embedding_pool))
                    try:
                        self._embedding_pool[oldest_key].session.close()
                    except:
                        pass
                    del self._embedding_pool[oldest_key]
                    logger.debug(f"Removed oldest embedding client: {oldest_key}")
                
                config = EmbeddingConfig(
                    api_url=api_url,
                    timeout=timeout,
                    retry_attempts=retry_attempts
                )
                self._embedding_pool[client_key] = EmbeddingClient(config)
                logger.debug(f"Created new embedding client: {client_key}")
            
            client = self._embedding_pool[client_key]
        
        try:
            yield client
        except Exception as e:
            logger.error(f"Error using embedding client {client_key}: {e}")
            # Remove faulty client from pool
            with self._lock:
                if client_key in self._embedding_pool:
                    try:
                        self._embedding_pool[client_key].session.close()
                    except:
                        pass
                    del self._embedding_pool[client_key]
            raise
    
    @contextmanager
    def get_http_session(self, session_key: str = "default"):
        """
        Get an HTTP session from the pool.
        """
        with self._lock:
            if session_key not in self._http_sessions:
                if len(self._http_sessions) >= self.max_pool_size:
                    # Remove oldest session
                    oldest_key = next(iter(self._http_sessions))
                    try:
                        self._http_sessions[oldest_key].close()
                    except:
                        pass
                    del self._http_sessions[oldest_key]
                    logger.debug(f"Removed oldest HTTP session: {oldest_key}")
                
                session = requests.Session()
                session.headers.update({
                    'Content-Type': 'application/json',
                    'User-Agent': 'ChatBot-Agent/1.0'
                })
                self._http_sessions[session_key] = session
                logger.debug(f"Created new HTTP session: {session_key}")
            
            session = self._http_sessions[session_key]
        
        try:
            yield session
        except Exception as e:
            logger.error(f"Error using HTTP session {session_key}: {e}")
            # Remove faulty session from pool
            with self._lock:
                if session_key in self._http_sessions:
                    try:
                        self._http_sessions[session_key].close()
                    except:
                        pass
                    del self._http_sessions[session_key]
            raise
    
    async def get_redis_connection(self):
        """
        Get Redis connection from the pool.
        """
        if self._redis_pool is None:
            self._redis_pool = redis.ConnectionPool.from_url(
                "redis://43.230.202.228:6379",
                max_connections=20,
                retry_on_timeout=True
            )
            logger.debug("Created Redis connection pool")
        
        return redis.Redis(connection_pool=self._redis_pool)
    
    def cleanup_pools(self):
        """
        Clean up all connection pools.
        Call this on application shutdown.
        """
        logger.info("Cleaning up connection pools...")
        
        with self._lock:
            # Close Qdrant clients
            for client in self._qdrant_pool.values():
                try:
                    client.close()
                except:
                    pass
            self._qdrant_pool.clear()
            
            # Close embedding clients
            for client in self._embedding_pool.values():
                try:
                    client.session.close()
                except:
                    pass
            self._embedding_pool.clear()
            
            # Close HTTP sessions
            for session in self._http_sessions.values():
                try:
                    session.close()
                except:
                    pass
            self._http_sessions.clear()
            
            # Clear Gemini clients (no explicit close method)
            self._gemini_pool.clear()
        
        if self._redis_pool:
            self._redis_pool.disconnect()
            self._redis_pool = None
        
        logger.info("Connection pools cleaned up successfully")
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """
        Get statistics about current pool usage.
        """
        with self._lock:
            return {
                "gemini_clients": len(self._gemini_pool),
                "qdrant_clients": len(self._qdrant_pool),
                "embedding_clients": len(self._embedding_pool),
                "http_sessions": len(self._http_sessions),
                "max_pool_size": self.max_pool_size,
                "pool_timeout": self.pool_timeout
            }

# Global connection pool manager instance
connection_pools = ConnectionPoolManager()
