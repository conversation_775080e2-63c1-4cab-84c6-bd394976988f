import React, { useRef } from 'react';
import { useChat } from '@/contexts/Livechatcontext';
import { API_BASE_URL } from '@/lib/api';

interface AttachmentButtonProps {
  conversationId?: number | null;
  groupId?: number | null;
  isGroup?: boolean;
}

export default function AttachmentButton({ conversationId = null, groupId = null, isGroup = false }: AttachmentButtonProps) {
  const { sendMessage, sendGroupMessage, state } = useChat();
  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleButtonClick = () => {
    inputRef.current?.click();
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const token = localStorage.getItem('access_token');
      const formData = new FormData();
      formData.append('file', file);

      const resp = await fetch(`${API_BASE_URL}/chat/attachments`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token ?? ''}`
        },
        body: formData
      });

      if (!resp.ok) throw new Error('Failed to upload file');

      const meta = await resp.json();
      const { url } = meta;
      const ext = file.name.split('.').pop()?.toLowerCase();
      const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
      const messageType = imageExts.includes(ext ?? '') ? 'image' : 'file';

      const payload = JSON.stringify(meta);

      if (isGroup && groupId) {
        sendGroupMessage(groupId, payload, messageType);
      } else if (!isGroup && conversationId) {
        sendMessage(conversationId, payload, messageType);
      }
    } catch (err) {
      console.error(err);
    } finally {
      if (inputRef.current) inputRef.current.value = '';
    }
  };

  const disabled = isGroup ? !groupId : !conversationId;
  if (disabled) return null;

  return (
    <>
      <input ref={inputRef} type="file" className="hidden" onChange={handleFileChange} />
      <button type="button" onClick={handleButtonClick} title="Attach file" className="p-2 rounded-full hover:bg-gray-200 text-gray-600" disabled={!state.isConnected}>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
          <path strokeLinecap="round" strokeLinejoin="round" d="M21.44 11.05l-9.193 9.193a5.125 5.125 0 01-7.246-7.246l9.193-9.193a3.063 3.063 0 014.332 4.332l-9.193 9.193a1.001 1.001 0 01-1.414-1.414l9.193-9.193" />
        </svg>
      </button>
    </>
  );
} 