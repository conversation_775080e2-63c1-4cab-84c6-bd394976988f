"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ip-address";
exports.ids = ["vendor-chunks/ip-address"];
exports.modules = {

/***/ "(ssr)/./node_modules/ip-address/dist/address-error.js":
/*!*******************************************************!*\
  !*** ./node_modules/ip-address/dist/address-error.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AddressError = void 0;\nclass AddressError extends Error {\n    constructor(message, parseMessage) {\n        super(message);\n        this.name = 'AddressError';\n        this.parseMessage = parseMessage;\n    }\n}\nexports.AddressError = AddressError;\n//# sourceMappingURL=address-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaXAtYWRkcmVzcy9kaXN0L2FkZHJlc3MtZXJyb3IuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaXAtYWRkcmVzcy9kaXN0L2FkZHJlc3MtZXJyb3IuanM/YjAxOCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQWRkcmVzc0Vycm9yID0gdm9pZCAwO1xuY2xhc3MgQWRkcmVzc0Vycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIHBhcnNlTWVzc2FnZSkge1xuICAgICAgICBzdXBlcihtZXNzYWdlKTtcbiAgICAgICAgdGhpcy5uYW1lID0gJ0FkZHJlc3NFcnJvcic7XG4gICAgICAgIHRoaXMucGFyc2VNZXNzYWdlID0gcGFyc2VNZXNzYWdlO1xuICAgIH1cbn1cbmV4cG9ydHMuQWRkcmVzc0Vycm9yID0gQWRkcmVzc0Vycm9yO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWRkcmVzcy1lcnJvci5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/address-error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/common.js":
/*!************************************************!*\
  !*** ./node_modules/ip-address/dist/common.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isInSubnet = isInSubnet;\nexports.isCorrect = isCorrect;\nexports.numberToPaddedHex = numberToPaddedHex;\nexports.stringToPaddedHex = stringToPaddedHex;\nexports.testBit = testBit;\nfunction isInSubnet(address) {\n    if (this.subnetMask < address.subnetMask) {\n        return false;\n    }\n    if (this.mask(address.subnetMask) === address.mask()) {\n        return true;\n    }\n    return false;\n}\nfunction isCorrect(defaultBits) {\n    return function () {\n        if (this.addressMinusSuffix !== this.correctForm()) {\n            return false;\n        }\n        if (this.subnetMask === defaultBits && !this.parsedSubnet) {\n            return true;\n        }\n        return this.parsedSubnet === String(this.subnetMask);\n    };\n}\nfunction numberToPaddedHex(number) {\n    return number.toString(16).padStart(2, '0');\n}\nfunction stringToPaddedHex(numberString) {\n    return numberToPaddedHex(parseInt(numberString, 10));\n}\n/**\n * @param binaryValue Binary representation of a value (e.g. `10`)\n * @param position Byte position, where 0 is the least significant bit\n */\nfunction testBit(binaryValue, position) {\n    const { length } = binaryValue;\n    if (position > length) {\n        return false;\n    }\n    const positionInString = length - position;\n    return binaryValue.substring(positionInString, positionInString + 1) === '1';\n}\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/ip-address.js":
/*!****************************************************!*\
  !*** ./node_modules/ip-address/dist/ip-address.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.v6 = exports.AddressError = exports.Address6 = exports.Address4 = void 0;\nvar ipv4_1 = __webpack_require__(/*! ./ipv4 */ \"(ssr)/./node_modules/ip-address/dist/ipv4.js\");\nObject.defineProperty(exports, \"Address4\", ({ enumerable: true, get: function () { return ipv4_1.Address4; } }));\nvar ipv6_1 = __webpack_require__(/*! ./ipv6 */ \"(ssr)/./node_modules/ip-address/dist/ipv6.js\");\nObject.defineProperty(exports, \"Address6\", ({ enumerable: true, get: function () { return ipv6_1.Address6; } }));\nvar address_error_1 = __webpack_require__(/*! ./address-error */ \"(ssr)/./node_modules/ip-address/dist/address-error.js\");\nObject.defineProperty(exports, \"AddressError\", ({ enumerable: true, get: function () { return address_error_1.AddressError; } }));\nconst helpers = __importStar(__webpack_require__(/*! ./v6/helpers */ \"(ssr)/./node_modules/ip-address/dist/v6/helpers.js\"));\nexports.v6 = { helpers };\n//# sourceMappingURL=ip-address.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/ip-address.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/ipv4.js":
/*!**********************************************!*\
  !*** ./node_modules/ip-address/dist/ipv4.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable no-param-reassign */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Address4 = void 0;\nconst common = __importStar(__webpack_require__(/*! ./common */ \"(ssr)/./node_modules/ip-address/dist/common.js\"));\nconst constants = __importStar(__webpack_require__(/*! ./v4/constants */ \"(ssr)/./node_modules/ip-address/dist/v4/constants.js\"));\nconst address_error_1 = __webpack_require__(/*! ./address-error */ \"(ssr)/./node_modules/ip-address/dist/address-error.js\");\n/**\n * Represents an IPv4 address\n * @class Address4\n * @param {string} address - An IPv4 address string\n */\nclass Address4 {\n    constructor(address) {\n        this.groups = constants.GROUPS;\n        this.parsedAddress = [];\n        this.parsedSubnet = '';\n        this.subnet = '/32';\n        this.subnetMask = 32;\n        this.v4 = true;\n        /**\n         * Returns true if the address is correct, false otherwise\n         * @memberof Address4\n         * @instance\n         * @returns {Boolean}\n         */\n        this.isCorrect = common.isCorrect(constants.BITS);\n        /**\n         * Returns true if the given address is in the subnet of the current address\n         * @memberof Address4\n         * @instance\n         * @returns {boolean}\n         */\n        this.isInSubnet = common.isInSubnet;\n        this.address = address;\n        const subnet = constants.RE_SUBNET_STRING.exec(address);\n        if (subnet) {\n            this.parsedSubnet = subnet[0].replace('/', '');\n            this.subnetMask = parseInt(this.parsedSubnet, 10);\n            this.subnet = `/${this.subnetMask}`;\n            if (this.subnetMask < 0 || this.subnetMask > constants.BITS) {\n                throw new address_error_1.AddressError('Invalid subnet mask.');\n            }\n            address = address.replace(constants.RE_SUBNET_STRING, '');\n        }\n        this.addressMinusSuffix = address;\n        this.parsedAddress = this.parse(address);\n    }\n    static isValid(address) {\n        try {\n            // eslint-disable-next-line no-new\n            new Address4(address);\n            return true;\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    /*\n     * Parses a v4 address\n     */\n    parse(address) {\n        const groups = address.split('.');\n        if (!address.match(constants.RE_ADDRESS)) {\n            throw new address_error_1.AddressError('Invalid IPv4 address.');\n        }\n        return groups;\n    }\n    /**\n     * Returns the correct form of an address\n     * @memberof Address4\n     * @instance\n     * @returns {String}\n     */\n    correctForm() {\n        return this.parsedAddress.map((part) => parseInt(part, 10)).join('.');\n    }\n    /**\n     * Converts a hex string to an IPv4 address object\n     * @memberof Address4\n     * @static\n     * @param {string} hex - a hex string to convert\n     * @returns {Address4}\n     */\n    static fromHex(hex) {\n        const padded = hex.replace(/:/g, '').padStart(8, '0');\n        const groups = [];\n        let i;\n        for (i = 0; i < 8; i += 2) {\n            const h = padded.slice(i, i + 2);\n            groups.push(parseInt(h, 16));\n        }\n        return new Address4(groups.join('.'));\n    }\n    /**\n     * Converts an integer into a IPv4 address object\n     * @memberof Address4\n     * @static\n     * @param {integer} integer - a number to convert\n     * @returns {Address4}\n     */\n    static fromInteger(integer) {\n        return Address4.fromHex(integer.toString(16));\n    }\n    /**\n     * Return an address from in-addr.arpa form\n     * @memberof Address4\n     * @static\n     * @param {string} arpaFormAddress - an 'in-addr.arpa' form ipv4 address\n     * @returns {Adress4}\n     * @example\n     * var address = Address4.fromArpa(**********.in-addr.arpa.)\n     * address.correctForm(); // '**********'\n     */\n    static fromArpa(arpaFormAddress) {\n        // remove ending \".in-addr.arpa.\" or just \".\"\n        const leader = arpaFormAddress.replace(/(\\.in-addr\\.arpa)?\\.$/, '');\n        const address = leader.split('.').reverse().join('.');\n        return new Address4(address);\n    }\n    /**\n     * Converts an IPv4 address object to a hex string\n     * @memberof Address4\n     * @instance\n     * @returns {String}\n     */\n    toHex() {\n        return this.parsedAddress.map((part) => common.stringToPaddedHex(part)).join(':');\n    }\n    /**\n     * Converts an IPv4 address object to an array of bytes\n     * @memberof Address4\n     * @instance\n     * @returns {Array}\n     */\n    toArray() {\n        return this.parsedAddress.map((part) => parseInt(part, 10));\n    }\n    /**\n     * Converts an IPv4 address object to an IPv6 address group\n     * @memberof Address4\n     * @instance\n     * @returns {String}\n     */\n    toGroup6() {\n        const output = [];\n        let i;\n        for (i = 0; i < constants.GROUPS; i += 2) {\n            output.push(`${common.stringToPaddedHex(this.parsedAddress[i])}${common.stringToPaddedHex(this.parsedAddress[i + 1])}`);\n        }\n        return output.join(':');\n    }\n    /**\n     * Returns the address as a `bigint`\n     * @memberof Address4\n     * @instance\n     * @returns {bigint}\n     */\n    bigInt() {\n        return BigInt(`0x${this.parsedAddress.map((n) => common.stringToPaddedHex(n)).join('')}`);\n    }\n    /**\n     * Helper function getting start address.\n     * @memberof Address4\n     * @instance\n     * @returns {bigint}\n     */\n    _startAddress() {\n        return BigInt(`0b${this.mask() + '0'.repeat(constants.BITS - this.subnetMask)}`);\n    }\n    /**\n     * The first address in the range given by this address' subnet.\n     * Often referred to as the Network Address.\n     * @memberof Address4\n     * @instance\n     * @returns {Address4}\n     */\n    startAddress() {\n        return Address4.fromBigInt(this._startAddress());\n    }\n    /**\n     * The first host address in the range given by this address's subnet ie\n     * the first address after the Network Address\n     * @memberof Address4\n     * @instance\n     * @returns {Address4}\n     */\n    startAddressExclusive() {\n        const adjust = BigInt('1');\n        return Address4.fromBigInt(this._startAddress() + adjust);\n    }\n    /**\n     * Helper function getting end address.\n     * @memberof Address4\n     * @instance\n     * @returns {bigint}\n     */\n    _endAddress() {\n        return BigInt(`0b${this.mask() + '1'.repeat(constants.BITS - this.subnetMask)}`);\n    }\n    /**\n     * The last address in the range given by this address' subnet\n     * Often referred to as the Broadcast\n     * @memberof Address4\n     * @instance\n     * @returns {Address4}\n     */\n    endAddress() {\n        return Address4.fromBigInt(this._endAddress());\n    }\n    /**\n     * The last host address in the range given by this address's subnet ie\n     * the last address prior to the Broadcast Address\n     * @memberof Address4\n     * @instance\n     * @returns {Address4}\n     */\n    endAddressExclusive() {\n        const adjust = BigInt('1');\n        return Address4.fromBigInt(this._endAddress() - adjust);\n    }\n    /**\n     * Converts a BigInt to a v4 address object\n     * @memberof Address4\n     * @static\n     * @param {bigint} bigInt - a BigInt to convert\n     * @returns {Address4}\n     */\n    static fromBigInt(bigInt) {\n        return Address4.fromHex(bigInt.toString(16));\n    }\n    /**\n     * Returns the first n bits of the address, defaulting to the\n     * subnet mask\n     * @memberof Address4\n     * @instance\n     * @returns {String}\n     */\n    mask(mask) {\n        if (mask === undefined) {\n            mask = this.subnetMask;\n        }\n        return this.getBitsBase2(0, mask);\n    }\n    /**\n     * Returns the bits in the given range as a base-2 string\n     * @memberof Address4\n     * @instance\n     * @returns {string}\n     */\n    getBitsBase2(start, end) {\n        return this.binaryZeroPad().slice(start, end);\n    }\n    /**\n     * Return the reversed ip6.arpa form of the address\n     * @memberof Address4\n     * @param {Object} options\n     * @param {boolean} options.omitSuffix - omit the \"in-addr.arpa\" suffix\n     * @instance\n     * @returns {String}\n     */\n    reverseForm(options) {\n        if (!options) {\n            options = {};\n        }\n        const reversed = this.correctForm().split('.').reverse().join('.');\n        if (options.omitSuffix) {\n            return reversed;\n        }\n        return `${reversed}.in-addr.arpa.`;\n    }\n    /**\n     * Returns true if the given address is a multicast address\n     * @memberof Address4\n     * @instance\n     * @returns {boolean}\n     */\n    isMulticast() {\n        return this.isInSubnet(new Address4('*********/4'));\n    }\n    /**\n     * Returns a zero-padded base-2 string representation of the address\n     * @memberof Address4\n     * @instance\n     * @returns {string}\n     */\n    binaryZeroPad() {\n        return this.bigInt().toString(2).padStart(constants.BITS, '0');\n    }\n    /**\n     * Groups an IPv4 address for inclusion at the end of an IPv6 address\n     * @returns {String}\n     */\n    groupForV6() {\n        const segments = this.parsedAddress;\n        return this.address.replace(constants.RE_ADDRESS, `<span class=\"hover-group group-v4 group-6\">${segments\n            .slice(0, 2)\n            .join('.')}</span>.<span class=\"hover-group group-v4 group-7\">${segments\n            .slice(2, 4)\n            .join('.')}</span>`);\n    }\n}\nexports.Address4 = Address4;\n//# sourceMappingURL=ipv4.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/ipv4.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/ipv6.js":
/*!**********************************************!*\
  !*** ./node_modules/ip-address/dist/ipv6.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable prefer-destructuring */\n/* eslint-disable no-param-reassign */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Address6 = void 0;\nconst common = __importStar(__webpack_require__(/*! ./common */ \"(ssr)/./node_modules/ip-address/dist/common.js\"));\nconst constants4 = __importStar(__webpack_require__(/*! ./v4/constants */ \"(ssr)/./node_modules/ip-address/dist/v4/constants.js\"));\nconst constants6 = __importStar(__webpack_require__(/*! ./v6/constants */ \"(ssr)/./node_modules/ip-address/dist/v6/constants.js\"));\nconst helpers = __importStar(__webpack_require__(/*! ./v6/helpers */ \"(ssr)/./node_modules/ip-address/dist/v6/helpers.js\"));\nconst ipv4_1 = __webpack_require__(/*! ./ipv4 */ \"(ssr)/./node_modules/ip-address/dist/ipv4.js\");\nconst regular_expressions_1 = __webpack_require__(/*! ./v6/regular-expressions */ \"(ssr)/./node_modules/ip-address/dist/v6/regular-expressions.js\");\nconst address_error_1 = __webpack_require__(/*! ./address-error */ \"(ssr)/./node_modules/ip-address/dist/address-error.js\");\nconst common_1 = __webpack_require__(/*! ./common */ \"(ssr)/./node_modules/ip-address/dist/common.js\");\nfunction assert(condition) {\n    if (!condition) {\n        throw new Error('Assertion failed.');\n    }\n}\nfunction addCommas(number) {\n    const r = /(\\d+)(\\d{3})/;\n    while (r.test(number)) {\n        number = number.replace(r, '$1,$2');\n    }\n    return number;\n}\nfunction spanLeadingZeroes4(n) {\n    n = n.replace(/^(0{1,})([1-9]+)$/, '<span class=\"parse-error\">$1</span>$2');\n    n = n.replace(/^(0{1,})(0)$/, '<span class=\"parse-error\">$1</span>$2');\n    return n;\n}\n/*\n * A helper function to compact an array\n */\nfunction compact(address, slice) {\n    const s1 = [];\n    const s2 = [];\n    let i;\n    for (i = 0; i < address.length; i++) {\n        if (i < slice[0]) {\n            s1.push(address[i]);\n        }\n        else if (i > slice[1]) {\n            s2.push(address[i]);\n        }\n    }\n    return s1.concat(['compact']).concat(s2);\n}\nfunction paddedHex(octet) {\n    return parseInt(octet, 16).toString(16).padStart(4, '0');\n}\nfunction unsignByte(b) {\n    // eslint-disable-next-line no-bitwise\n    return b & 0xff;\n}\n/**\n * Represents an IPv6 address\n * @class Address6\n * @param {string} address - An IPv6 address string\n * @param {number} [groups=8] - How many octets to parse\n * @example\n * var address = new Address6('2001::/32');\n */\nclass Address6 {\n    constructor(address, optionalGroups) {\n        this.addressMinusSuffix = '';\n        this.parsedSubnet = '';\n        this.subnet = '/128';\n        this.subnetMask = 128;\n        this.v4 = false;\n        this.zone = '';\n        // #region Attributes\n        /**\n         * Returns true if the given address is in the subnet of the current address\n         * @memberof Address6\n         * @instance\n         * @returns {boolean}\n         */\n        this.isInSubnet = common.isInSubnet;\n        /**\n         * Returns true if the address is correct, false otherwise\n         * @memberof Address6\n         * @instance\n         * @returns {boolean}\n         */\n        this.isCorrect = common.isCorrect(constants6.BITS);\n        if (optionalGroups === undefined) {\n            this.groups = constants6.GROUPS;\n        }\n        else {\n            this.groups = optionalGroups;\n        }\n        this.address = address;\n        const subnet = constants6.RE_SUBNET_STRING.exec(address);\n        if (subnet) {\n            this.parsedSubnet = subnet[0].replace('/', '');\n            this.subnetMask = parseInt(this.parsedSubnet, 10);\n            this.subnet = `/${this.subnetMask}`;\n            if (Number.isNaN(this.subnetMask) ||\n                this.subnetMask < 0 ||\n                this.subnetMask > constants6.BITS) {\n                throw new address_error_1.AddressError('Invalid subnet mask.');\n            }\n            address = address.replace(constants6.RE_SUBNET_STRING, '');\n        }\n        else if (/\\//.test(address)) {\n            throw new address_error_1.AddressError('Invalid subnet mask.');\n        }\n        const zone = constants6.RE_ZONE_STRING.exec(address);\n        if (zone) {\n            this.zone = zone[0];\n            address = address.replace(constants6.RE_ZONE_STRING, '');\n        }\n        this.addressMinusSuffix = address;\n        this.parsedAddress = this.parse(this.addressMinusSuffix);\n    }\n    static isValid(address) {\n        try {\n            // eslint-disable-next-line no-new\n            new Address6(address);\n            return true;\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    /**\n     * Convert a BigInt to a v6 address object\n     * @memberof Address6\n     * @static\n     * @param {bigint} bigInt - a BigInt to convert\n     * @returns {Address6}\n     * @example\n     * var bigInt = BigInt('1000000000000');\n     * var address = Address6.fromBigInt(bigInt);\n     * address.correctForm(); // '::e8:d4a5:1000'\n     */\n    static fromBigInt(bigInt) {\n        const hex = bigInt.toString(16).padStart(32, '0');\n        const groups = [];\n        let i;\n        for (i = 0; i < constants6.GROUPS; i++) {\n            groups.push(hex.slice(i * 4, (i + 1) * 4));\n        }\n        return new Address6(groups.join(':'));\n    }\n    /**\n     * Convert a URL (with optional port number) to an address object\n     * @memberof Address6\n     * @static\n     * @param {string} url - a URL with optional port number\n     * @example\n     * var addressAndPort = Address6.fromURL('http://[ffff::]:8080/foo/');\n     * addressAndPort.address.correctForm(); // 'ffff::'\n     * addressAndPort.port; // 8080\n     */\n    static fromURL(url) {\n        let host;\n        let port = null;\n        let result;\n        // If we have brackets parse them and find a port\n        if (url.indexOf('[') !== -1 && url.indexOf(']:') !== -1) {\n            result = constants6.RE_URL_WITH_PORT.exec(url);\n            if (result === null) {\n                return {\n                    error: 'failed to parse address with port',\n                    address: null,\n                    port: null,\n                };\n            }\n            host = result[1];\n            port = result[2];\n            // If there's a URL extract the address\n        }\n        else if (url.indexOf('/') !== -1) {\n            // Remove the protocol prefix\n            url = url.replace(/^[a-z0-9]+:\\/\\//, '');\n            // Parse the address\n            result = constants6.RE_URL.exec(url);\n            if (result === null) {\n                return {\n                    error: 'failed to parse address from URL',\n                    address: null,\n                    port: null,\n                };\n            }\n            host = result[1];\n            // Otherwise just assign the URL to the host and let the library parse it\n        }\n        else {\n            host = url;\n        }\n        // If there's a port convert it to an integer\n        if (port) {\n            port = parseInt(port, 10);\n            // squelch out of range ports\n            if (port < 0 || port > 65536) {\n                port = null;\n            }\n        }\n        else {\n            // Standardize `undefined` to `null`\n            port = null;\n        }\n        return {\n            address: new Address6(host),\n            port,\n        };\n    }\n    /**\n     * Create an IPv6-mapped address given an IPv4 address\n     * @memberof Address6\n     * @static\n     * @param {string} address - An IPv4 address string\n     * @returns {Address6}\n     * @example\n     * var address = Address6.fromAddress4('***********');\n     * address.correctForm(); // '::ffff:c0a8:1'\n     * address.to4in6(); // '::ffff:***********'\n     */\n    static fromAddress4(address) {\n        const address4 = new ipv4_1.Address4(address);\n        const mask6 = constants6.BITS - (constants4.BITS - address4.subnetMask);\n        return new Address6(`::ffff:${address4.correctForm()}/${mask6}`);\n    }\n    /**\n     * Return an address from ip6.arpa form\n     * @memberof Address6\n     * @static\n     * @param {string} arpaFormAddress - an 'ip6.arpa' form address\n     * @returns {Adress6}\n     * @example\n     * var address = Address6.fromArpa(e.f.f.f.3.c.2.6.f.f.f.e.6.6.8.e.*******.9.4.e.c.0.0.0.0.*******.ip6.arpa.)\n     * address.correctForm(); // '2001:0:ce49:7601:e866:efff:62c3:fffe'\n     */\n    static fromArpa(arpaFormAddress) {\n        // remove ending \".ip6.arpa.\" or just \".\"\n        let address = arpaFormAddress.replace(/(\\.ip6\\.arpa)?\\.$/, '');\n        const semicolonAmount = 7;\n        // correct ip6.arpa form with ending removed will be 63 characters\n        if (address.length !== 63) {\n            throw new address_error_1.AddressError(\"Invalid 'ip6.arpa' form.\");\n        }\n        const parts = address.split('.').reverse();\n        for (let i = semicolonAmount; i > 0; i--) {\n            const insertIndex = i * 4;\n            parts.splice(insertIndex, 0, ':');\n        }\n        address = parts.join('');\n        return new Address6(address);\n    }\n    /**\n     * Return the Microsoft UNC transcription of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String} the Microsoft UNC transcription of the address\n     */\n    microsoftTranscription() {\n        return `${this.correctForm().replace(/:/g, '-')}.ipv6-literal.net`;\n    }\n    /**\n     * Return the first n bits of the address, defaulting to the subnet mask\n     * @memberof Address6\n     * @instance\n     * @param {number} [mask=subnet] - the number of bits to mask\n     * @returns {String} the first n bits of the address as a string\n     */\n    mask(mask = this.subnetMask) {\n        return this.getBitsBase2(0, mask);\n    }\n    /**\n     * Return the number of possible subnets of a given size in the address\n     * @memberof Address6\n     * @instance\n     * @param {number} [subnetSize=128] - the subnet size\n     * @returns {String}\n     */\n    // TODO: probably useful to have a numeric version of this too\n    possibleSubnets(subnetSize = 128) {\n        const availableBits = constants6.BITS - this.subnetMask;\n        const subnetBits = Math.abs(subnetSize - constants6.BITS);\n        const subnetPowers = availableBits - subnetBits;\n        if (subnetPowers < 0) {\n            return '0';\n        }\n        return addCommas((BigInt('2') ** BigInt(subnetPowers)).toString(10));\n    }\n    /**\n     * Helper function getting start address.\n     * @memberof Address6\n     * @instance\n     * @returns {bigint}\n     */\n    _startAddress() {\n        return BigInt(`0b${this.mask() + '0'.repeat(constants6.BITS - this.subnetMask)}`);\n    }\n    /**\n     * The first address in the range given by this address' subnet\n     * Often referred to as the Network Address.\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    startAddress() {\n        return Address6.fromBigInt(this._startAddress());\n    }\n    /**\n     * The first host address in the range given by this address's subnet ie\n     * the first address after the Network Address\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    startAddressExclusive() {\n        const adjust = BigInt('1');\n        return Address6.fromBigInt(this._startAddress() + adjust);\n    }\n    /**\n     * Helper function getting end address.\n     * @memberof Address6\n     * @instance\n     * @returns {bigint}\n     */\n    _endAddress() {\n        return BigInt(`0b${this.mask() + '1'.repeat(constants6.BITS - this.subnetMask)}`);\n    }\n    /**\n     * The last address in the range given by this address' subnet\n     * Often referred to as the Broadcast\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    endAddress() {\n        return Address6.fromBigInt(this._endAddress());\n    }\n    /**\n     * The last host address in the range given by this address's subnet ie\n     * the last address prior to the Broadcast Address\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    endAddressExclusive() {\n        const adjust = BigInt('1');\n        return Address6.fromBigInt(this._endAddress() - adjust);\n    }\n    /**\n     * Return the scope of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getScope() {\n        let scope = constants6.SCOPES[parseInt(this.getBits(12, 16).toString(10), 10)];\n        if (this.getType() === 'Global unicast' && scope !== 'Link local') {\n            scope = 'Global';\n        }\n        return scope || 'Unknown';\n    }\n    /**\n     * Return the type of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getType() {\n        for (const subnet of Object.keys(constants6.TYPES)) {\n            if (this.isInSubnet(new Address6(subnet))) {\n                return constants6.TYPES[subnet];\n            }\n        }\n        return 'Global unicast';\n    }\n    /**\n     * Return the bits in the given range as a BigInt\n     * @memberof Address6\n     * @instance\n     * @returns {bigint}\n     */\n    getBits(start, end) {\n        return BigInt(`0b${this.getBitsBase2(start, end)}`);\n    }\n    /**\n     * Return the bits in the given range as a base-2 string\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getBitsBase2(start, end) {\n        return this.binaryZeroPad().slice(start, end);\n    }\n    /**\n     * Return the bits in the given range as a base-16 string\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getBitsBase16(start, end) {\n        const length = end - start;\n        if (length % 4 !== 0) {\n            throw new Error('Length of bits to retrieve must be divisible by four');\n        }\n        return this.getBits(start, end)\n            .toString(16)\n            .padStart(length / 4, '0');\n    }\n    /**\n     * Return the bits that are set past the subnet mask length\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getBitsPastSubnet() {\n        return this.getBitsBase2(this.subnetMask, constants6.BITS);\n    }\n    /**\n     * Return the reversed ip6.arpa form of the address\n     * @memberof Address6\n     * @param {Object} options\n     * @param {boolean} options.omitSuffix - omit the \"ip6.arpa\" suffix\n     * @instance\n     * @returns {String}\n     */\n    reverseForm(options) {\n        if (!options) {\n            options = {};\n        }\n        const characters = Math.floor(this.subnetMask / 4);\n        const reversed = this.canonicalForm()\n            .replace(/:/g, '')\n            .split('')\n            .slice(0, characters)\n            .reverse()\n            .join('.');\n        if (characters > 0) {\n            if (options.omitSuffix) {\n                return reversed;\n            }\n            return `${reversed}.ip6.arpa.`;\n        }\n        if (options.omitSuffix) {\n            return '';\n        }\n        return 'ip6.arpa.';\n    }\n    /**\n     * Return the correct form of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    correctForm() {\n        let i;\n        let groups = [];\n        let zeroCounter = 0;\n        const zeroes = [];\n        for (i = 0; i < this.parsedAddress.length; i++) {\n            const value = parseInt(this.parsedAddress[i], 16);\n            if (value === 0) {\n                zeroCounter++;\n            }\n            if (value !== 0 && zeroCounter > 0) {\n                if (zeroCounter > 1) {\n                    zeroes.push([i - zeroCounter, i - 1]);\n                }\n                zeroCounter = 0;\n            }\n        }\n        // Do we end with a string of zeroes?\n        if (zeroCounter > 1) {\n            zeroes.push([this.parsedAddress.length - zeroCounter, this.parsedAddress.length - 1]);\n        }\n        const zeroLengths = zeroes.map((n) => n[1] - n[0] + 1);\n        if (zeroes.length > 0) {\n            const index = zeroLengths.indexOf(Math.max(...zeroLengths));\n            groups = compact(this.parsedAddress, zeroes[index]);\n        }\n        else {\n            groups = this.parsedAddress;\n        }\n        for (i = 0; i < groups.length; i++) {\n            if (groups[i] !== 'compact') {\n                groups[i] = parseInt(groups[i], 16).toString(16);\n            }\n        }\n        let correct = groups.join(':');\n        correct = correct.replace(/^compact$/, '::');\n        correct = correct.replace(/(^compact)|(compact$)/, ':');\n        correct = correct.replace(/compact/, '');\n        return correct;\n    }\n    /**\n     * Return a zero-padded base-2 string representation of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     * @example\n     * var address = new Address6('2001:4860:4001:803::1011');\n     * address.binaryZeroPad();\n     * // '0010000000000001010010000110000001000000000000010000100000000011\n     * //  0000000000000000000000000000000000000000000000000001000000010001'\n     */\n    binaryZeroPad() {\n        return this.bigInt().toString(2).padStart(constants6.BITS, '0');\n    }\n    // TODO: Improve the semantics of this helper function\n    parse4in6(address) {\n        const groups = address.split(':');\n        const lastGroup = groups.slice(-1)[0];\n        const address4 = lastGroup.match(constants4.RE_ADDRESS);\n        if (address4) {\n            this.parsedAddress4 = address4[0];\n            this.address4 = new ipv4_1.Address4(this.parsedAddress4);\n            for (let i = 0; i < this.address4.groups; i++) {\n                if (/^0[0-9]+/.test(this.address4.parsedAddress[i])) {\n                    throw new address_error_1.AddressError(\"IPv4 addresses can't have leading zeroes.\", address.replace(constants4.RE_ADDRESS, this.address4.parsedAddress.map(spanLeadingZeroes4).join('.')));\n                }\n            }\n            this.v4 = true;\n            groups[groups.length - 1] = this.address4.toGroup6();\n            address = groups.join(':');\n        }\n        return address;\n    }\n    // TODO: Make private?\n    parse(address) {\n        address = this.parse4in6(address);\n        const badCharacters = address.match(constants6.RE_BAD_CHARACTERS);\n        if (badCharacters) {\n            throw new address_error_1.AddressError(`Bad character${badCharacters.length > 1 ? 's' : ''} detected in address: ${badCharacters.join('')}`, address.replace(constants6.RE_BAD_CHARACTERS, '<span class=\"parse-error\">$1</span>'));\n        }\n        const badAddress = address.match(constants6.RE_BAD_ADDRESS);\n        if (badAddress) {\n            throw new address_error_1.AddressError(`Address failed regex: ${badAddress.join('')}`, address.replace(constants6.RE_BAD_ADDRESS, '<span class=\"parse-error\">$1</span>'));\n        }\n        let groups = [];\n        const halves = address.split('::');\n        if (halves.length === 2) {\n            let first = halves[0].split(':');\n            let last = halves[1].split(':');\n            if (first.length === 1 && first[0] === '') {\n                first = [];\n            }\n            if (last.length === 1 && last[0] === '') {\n                last = [];\n            }\n            const remaining = this.groups - (first.length + last.length);\n            if (!remaining) {\n                throw new address_error_1.AddressError('Error parsing groups');\n            }\n            this.elidedGroups = remaining;\n            this.elisionBegin = first.length;\n            this.elisionEnd = first.length + this.elidedGroups;\n            groups = groups.concat(first);\n            for (let i = 0; i < remaining; i++) {\n                groups.push('0');\n            }\n            groups = groups.concat(last);\n        }\n        else if (halves.length === 1) {\n            groups = address.split(':');\n            this.elidedGroups = 0;\n        }\n        else {\n            throw new address_error_1.AddressError('Too many :: groups found');\n        }\n        groups = groups.map((group) => parseInt(group, 16).toString(16));\n        if (groups.length !== this.groups) {\n            throw new address_error_1.AddressError('Incorrect number of groups found');\n        }\n        return groups;\n    }\n    /**\n     * Return the canonical form of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    canonicalForm() {\n        return this.parsedAddress.map(paddedHex).join(':');\n    }\n    /**\n     * Return the decimal form of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    decimal() {\n        return this.parsedAddress.map((n) => parseInt(n, 16).toString(10).padStart(5, '0')).join(':');\n    }\n    /**\n     * Return the address as a BigInt\n     * @memberof Address6\n     * @instance\n     * @returns {bigint}\n     */\n    bigInt() {\n        return BigInt(`0x${this.parsedAddress.map(paddedHex).join('')}`);\n    }\n    /**\n     * Return the last two groups of this address as an IPv4 address string\n     * @memberof Address6\n     * @instance\n     * @returns {Address4}\n     * @example\n     * var address = new Address6('2001:4860:4001::1825:bf11');\n     * address.to4().correctForm(); // '************'\n     */\n    to4() {\n        const binary = this.binaryZeroPad().split('');\n        return ipv4_1.Address4.fromHex(BigInt(`0b${binary.slice(96, 128).join('')}`).toString(16));\n    }\n    /**\n     * Return the v4-in-v6 form of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    to4in6() {\n        const address4 = this.to4();\n        const address6 = new Address6(this.parsedAddress.slice(0, 6).join(':'), 6);\n        const correct = address6.correctForm();\n        let infix = '';\n        if (!/:$/.test(correct)) {\n            infix = ':';\n        }\n        return correct + infix + address4.address;\n    }\n    /**\n     * Return an object containing the Teredo properties of the address\n     * @memberof Address6\n     * @instance\n     * @returns {Object}\n     */\n    inspectTeredo() {\n        /*\n        - Bits 0 to 31 are set to the Teredo prefix (normally 2001:0000::/32).\n        - Bits 32 to 63 embed the primary IPv4 address of the Teredo server that\n          is used.\n        - Bits 64 to 79 can be used to define some flags. Currently only the\n          higher order bit is used; it is set to 1 if the Teredo client is\n          located behind a cone NAT, 0 otherwise. For Microsoft's Windows Vista\n          and Windows Server 2008 implementations, more bits are used. In those\n          implementations, the format for these 16 bits is \"CRAAAAUG AAAAAAAA\",\n          where \"C\" remains the \"Cone\" flag. The \"R\" bit is reserved for future\n          use. The \"U\" bit is for the Universal/Local flag (set to 0). The \"G\" bit\n          is Individual/Group flag (set to 0). The A bits are set to a 12-bit\n          randomly generated number chosen by the Teredo client to introduce\n          additional protection for the Teredo node against IPv6-based scanning\n          attacks.\n        - Bits 80 to 95 contains the obfuscated UDP port number. This is the\n          port number that is mapped by the NAT to the Teredo client with all\n          bits inverted.\n        - Bits 96 to 127 contains the obfuscated IPv4 address. This is the\n          public IPv4 address of the NAT with all bits inverted.\n        */\n        const prefix = this.getBitsBase16(0, 32);\n        const bitsForUdpPort = this.getBits(80, 96);\n        // eslint-disable-next-line no-bitwise\n        const udpPort = (bitsForUdpPort ^ BigInt('0xffff')).toString();\n        const server4 = ipv4_1.Address4.fromHex(this.getBitsBase16(32, 64));\n        const bitsForClient4 = this.getBits(96, 128);\n        // eslint-disable-next-line no-bitwise\n        const client4 = ipv4_1.Address4.fromHex((bitsForClient4 ^ BigInt('0xffffffff')).toString(16));\n        const flagsBase2 = this.getBitsBase2(64, 80);\n        const coneNat = (0, common_1.testBit)(flagsBase2, 15);\n        const reserved = (0, common_1.testBit)(flagsBase2, 14);\n        const groupIndividual = (0, common_1.testBit)(flagsBase2, 8);\n        const universalLocal = (0, common_1.testBit)(flagsBase2, 9);\n        const nonce = BigInt(`0b${flagsBase2.slice(2, 6) + flagsBase2.slice(8, 16)}`).toString(10);\n        return {\n            prefix: `${prefix.slice(0, 4)}:${prefix.slice(4, 8)}`,\n            server4: server4.address,\n            client4: client4.address,\n            flags: flagsBase2,\n            coneNat,\n            microsoft: {\n                reserved,\n                universalLocal,\n                groupIndividual,\n                nonce,\n            },\n            udpPort,\n        };\n    }\n    /**\n     * Return an object containing the 6to4 properties of the address\n     * @memberof Address6\n     * @instance\n     * @returns {Object}\n     */\n    inspect6to4() {\n        /*\n        - Bits 0 to 15 are set to the 6to4 prefix (2002::/16).\n        - Bits 16 to 48 embed the IPv4 address of the 6to4 gateway that is used.\n        */\n        const prefix = this.getBitsBase16(0, 16);\n        const gateway = ipv4_1.Address4.fromHex(this.getBitsBase16(16, 48));\n        return {\n            prefix: prefix.slice(0, 4),\n            gateway: gateway.address,\n        };\n    }\n    /**\n     * Return a v6 6to4 address from a v6 v4inv6 address\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    to6to4() {\n        if (!this.is4()) {\n            return null;\n        }\n        const addr6to4 = [\n            '2002',\n            this.getBitsBase16(96, 112),\n            this.getBitsBase16(112, 128),\n            '',\n            '/16',\n        ].join(':');\n        return new Address6(addr6to4);\n    }\n    /**\n     * Return a byte array\n     * @memberof Address6\n     * @instance\n     * @returns {Array}\n     */\n    toByteArray() {\n        const valueWithoutPadding = this.bigInt().toString(16);\n        const leadingPad = '0'.repeat(valueWithoutPadding.length % 2);\n        const value = `${leadingPad}${valueWithoutPadding}`;\n        const bytes = [];\n        for (let i = 0, length = value.length; i < length; i += 2) {\n            bytes.push(parseInt(value.substring(i, i + 2), 16));\n        }\n        return bytes;\n    }\n    /**\n     * Return an unsigned byte array\n     * @memberof Address6\n     * @instance\n     * @returns {Array}\n     */\n    toUnsignedByteArray() {\n        return this.toByteArray().map(unsignByte);\n    }\n    /**\n     * Convert a byte array to an Address6 object\n     * @memberof Address6\n     * @static\n     * @returns {Address6}\n     */\n    static fromByteArray(bytes) {\n        return this.fromUnsignedByteArray(bytes.map(unsignByte));\n    }\n    /**\n     * Convert an unsigned byte array to an Address6 object\n     * @memberof Address6\n     * @static\n     * @returns {Address6}\n     */\n    static fromUnsignedByteArray(bytes) {\n        const BYTE_MAX = BigInt('256');\n        let result = BigInt('0');\n        let multiplier = BigInt('1');\n        for (let i = bytes.length - 1; i >= 0; i--) {\n            result += multiplier * BigInt(bytes[i].toString(10));\n            multiplier *= BYTE_MAX;\n        }\n        return Address6.fromBigInt(result);\n    }\n    /**\n     * Returns true if the address is in the canonical form, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isCanonical() {\n        return this.addressMinusSuffix === this.canonicalForm();\n    }\n    /**\n     * Returns true if the address is a link local address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isLinkLocal() {\n        // Zeroes are required, i.e. we can't check isInSubnet with 'fe80::/10'\n        if (this.getBitsBase2(0, 64) ===\n            '1111111010000000000000000000000000000000000000000000000000000000') {\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Returns true if the address is a multicast address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isMulticast() {\n        return this.getType() === 'Multicast';\n    }\n    /**\n     * Returns true if the address is a v4-in-v6 address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    is4() {\n        return this.v4;\n    }\n    /**\n     * Returns true if the address is a Teredo address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isTeredo() {\n        return this.isInSubnet(new Address6('2001::/32'));\n    }\n    /**\n     * Returns true if the address is a 6to4 address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    is6to4() {\n        return this.isInSubnet(new Address6('2002::/16'));\n    }\n    /**\n     * Returns true if the address is a loopback address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isLoopback() {\n        return this.getType() === 'Loopback';\n    }\n    // #endregion\n    // #region HTML\n    /**\n     * @returns {String} the address in link form with a default port of 80\n     */\n    href(optionalPort) {\n        if (optionalPort === undefined) {\n            optionalPort = '';\n        }\n        else {\n            optionalPort = `:${optionalPort}`;\n        }\n        return `http://[${this.correctForm()}]${optionalPort}/`;\n    }\n    /**\n     * @returns {String} a link suitable for conveying the address via a URL hash\n     */\n    link(options) {\n        if (!options) {\n            options = {};\n        }\n        if (options.className === undefined) {\n            options.className = '';\n        }\n        if (options.prefix === undefined) {\n            options.prefix = '/#address=';\n        }\n        if (options.v4 === undefined) {\n            options.v4 = false;\n        }\n        let formFunction = this.correctForm;\n        if (options.v4) {\n            formFunction = this.to4in6;\n        }\n        const form = formFunction.call(this);\n        if (options.className) {\n            return `<a href=\"${options.prefix}${form}\" class=\"${options.className}\">${form}</a>`;\n        }\n        return `<a href=\"${options.prefix}${form}\">${form}</a>`;\n    }\n    /**\n     * Groups an address\n     * @returns {String}\n     */\n    group() {\n        if (this.elidedGroups === 0) {\n            // The simple case\n            return helpers.simpleGroup(this.address).join(':');\n        }\n        assert(typeof this.elidedGroups === 'number');\n        assert(typeof this.elisionBegin === 'number');\n        // The elided case\n        const output = [];\n        const [left, right] = this.address.split('::');\n        if (left.length) {\n            output.push(...helpers.simpleGroup(left));\n        }\n        else {\n            output.push('');\n        }\n        const classes = ['hover-group'];\n        for (let i = this.elisionBegin; i < this.elisionBegin + this.elidedGroups; i++) {\n            classes.push(`group-${i}`);\n        }\n        output.push(`<span class=\"${classes.join(' ')}\"></span>`);\n        if (right.length) {\n            output.push(...helpers.simpleGroup(right, this.elisionEnd));\n        }\n        else {\n            output.push('');\n        }\n        if (this.is4()) {\n            assert(this.address4 instanceof ipv4_1.Address4);\n            output.pop();\n            output.push(this.address4.groupForV6());\n        }\n        return output.join(':');\n    }\n    // #endregion\n    // #region Regular expressions\n    /**\n     * Generate a regular expression string that can be used to find or validate\n     * all variations of this address\n     * @memberof Address6\n     * @instance\n     * @param {boolean} substringSearch\n     * @returns {string}\n     */\n    regularExpressionString(substringSearch = false) {\n        let output = [];\n        // TODO: revisit why this is necessary\n        const address6 = new Address6(this.correctForm());\n        if (address6.elidedGroups === 0) {\n            // The simple case\n            output.push((0, regular_expressions_1.simpleRegularExpression)(address6.parsedAddress));\n        }\n        else if (address6.elidedGroups === constants6.GROUPS) {\n            // A completely elided address\n            output.push((0, regular_expressions_1.possibleElisions)(constants6.GROUPS));\n        }\n        else {\n            // A partially elided address\n            const halves = address6.address.split('::');\n            if (halves[0].length) {\n                output.push((0, regular_expressions_1.simpleRegularExpression)(halves[0].split(':')));\n            }\n            assert(typeof address6.elidedGroups === 'number');\n            output.push((0, regular_expressions_1.possibleElisions)(address6.elidedGroups, halves[0].length !== 0, halves[1].length !== 0));\n            if (halves[1].length) {\n                output.push((0, regular_expressions_1.simpleRegularExpression)(halves[1].split(':')));\n            }\n            output = [output.join(':')];\n        }\n        if (!substringSearch) {\n            output = [\n                '(?=^|',\n                regular_expressions_1.ADDRESS_BOUNDARY,\n                '|[^\\\\w\\\\:])(',\n                ...output,\n                ')(?=[^\\\\w\\\\:]|',\n                regular_expressions_1.ADDRESS_BOUNDARY,\n                '|$)',\n            ];\n        }\n        return output.join('');\n    }\n    /**\n     * Generate a regular expression that can be used to find or validate all\n     * variations of this address.\n     * @memberof Address6\n     * @instance\n     * @param {boolean} substringSearch\n     * @returns {RegExp}\n     */\n    regularExpression(substringSearch = false) {\n        return new RegExp(this.regularExpressionString(substringSearch), 'i');\n    }\n}\nexports.Address6 = Address6;\n//# sourceMappingURL=ipv6.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/ipv6.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/v4/constants.js":
/*!******************************************************!*\
  !*** ./node_modules/ip-address/dist/v4/constants.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RE_SUBNET_STRING = exports.RE_ADDRESS = exports.GROUPS = exports.BITS = void 0;\nexports.BITS = 32;\nexports.GROUPS = 4;\nexports.RE_ADDRESS = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/g;\nexports.RE_SUBNET_STRING = /\\/\\d{1,2}$/;\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaXAtYWRkcmVzcy9kaXN0L3Y0L2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx3QkFBd0IsR0FBRyxrQkFBa0IsR0FBRyxjQUFjLEdBQUcsWUFBWTtBQUM3RSxZQUFZO0FBQ1osY0FBYztBQUNkLGtCQUFrQjtBQUNsQix3QkFBd0IsU0FBUyxJQUFJO0FBQ3JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaXAtYWRkcmVzcy9kaXN0L3Y0L2NvbnN0YW50cy5qcz85ZDFhIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5SRV9TVUJORVRfU1RSSU5HID0gZXhwb3J0cy5SRV9BRERSRVNTID0gZXhwb3J0cy5HUk9VUFMgPSBleHBvcnRzLkJJVFMgPSB2b2lkIDA7XG5leHBvcnRzLkJJVFMgPSAzMjtcbmV4cG9ydHMuR1JPVVBTID0gNDtcbmV4cG9ydHMuUkVfQUREUkVTUyA9IC9eKDI1WzAtNV18MlswLTRdWzAtOV18WzAxXT9bMC05XVswLTldPylcXC4oMjVbMC01XXwyWzAtNF1bMC05XXxbMDFdP1swLTldWzAtOV0/KVxcLigyNVswLTVdfDJbMC00XVswLTldfFswMV0/WzAtOV1bMC05XT8pXFwuKDI1WzAtNV18MlswLTRdWzAtOV18WzAxXT9bMC05XVswLTldPykkL2c7XG5leHBvcnRzLlJFX1NVQk5FVF9TVFJJTkcgPSAvXFwvXFxkezEsMn0kLztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnN0YW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/v4/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/v6/constants.js":
/*!******************************************************!*\
  !*** ./node_modules/ip-address/dist/v6/constants.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RE_URL_WITH_PORT = exports.RE_URL = exports.RE_ZONE_STRING = exports.RE_SUBNET_STRING = exports.RE_BAD_ADDRESS = exports.RE_BAD_CHARACTERS = exports.TYPES = exports.SCOPES = exports.GROUPS = exports.BITS = void 0;\nexports.BITS = 128;\nexports.GROUPS = 8;\n/**\n * Represents IPv6 address scopes\n * @memberof Address6\n * @static\n */\nexports.SCOPES = {\n    0: 'Reserved',\n    1: 'Interface local',\n    2: 'Link local',\n    4: 'Admin local',\n    5: 'Site local',\n    8: 'Organization local',\n    14: 'Global',\n    15: 'Reserved',\n};\n/**\n * Represents IPv6 address types\n * @memberof Address6\n * @static\n */\nexports.TYPES = {\n    'ff01::1/128': 'Multicast (All nodes on this interface)',\n    'ff01::2/128': 'Multicast (All routers on this interface)',\n    'ff02::1/128': 'Multicast (All nodes on this link)',\n    'ff02::2/128': 'Multicast (All routers on this link)',\n    'ff05::2/128': 'Multicast (All routers in this site)',\n    'ff02::5/128': 'Multicast (OSPFv3 AllSPF routers)',\n    'ff02::6/128': 'Multicast (OSPFv3 AllDR routers)',\n    'ff02::9/128': 'Multicast (RIP routers)',\n    'ff02::a/128': 'Multicast (EIGRP routers)',\n    'ff02::d/128': 'Multicast (PIM routers)',\n    'ff02::16/128': 'Multicast (MLDv2 reports)',\n    'ff01::fb/128': 'Multicast (mDNSv6)',\n    'ff02::fb/128': 'Multicast (mDNSv6)',\n    'ff05::fb/128': 'Multicast (mDNSv6)',\n    'ff02::1:2/128': 'Multicast (All DHCP servers and relay agents on this link)',\n    'ff05::1:2/128': 'Multicast (All DHCP servers and relay agents in this site)',\n    'ff02::1:3/128': 'Multicast (All DHCP servers on this link)',\n    'ff05::1:3/128': 'Multicast (All DHCP servers in this site)',\n    '::/128': 'Unspecified',\n    '::1/128': 'Loopback',\n    'ff00::/8': 'Multicast',\n    'fe80::/10': 'Link-local unicast',\n};\n/**\n * A regular expression that matches bad characters in an IPv6 address\n * @memberof Address6\n * @static\n */\nexports.RE_BAD_CHARACTERS = /([^0-9a-f:/%])/gi;\n/**\n * A regular expression that matches an incorrect IPv6 address\n * @memberof Address6\n * @static\n */\nexports.RE_BAD_ADDRESS = /([0-9a-f]{5,}|:{3,}|[^:]:$|^:[^:]|\\/$)/gi;\n/**\n * A regular expression that matches an IPv6 subnet\n * @memberof Address6\n * @static\n */\nexports.RE_SUBNET_STRING = /\\/\\d{1,3}(?=%|$)/;\n/**\n * A regular expression that matches an IPv6 zone\n * @memberof Address6\n * @static\n */\nexports.RE_ZONE_STRING = /%.*$/;\nexports.RE_URL = /^\\[{0,1}([0-9a-f:]+)\\]{0,1}/;\nexports.RE_URL_WITH_PORT = /\\[([0-9a-f:]+)\\]:([0-9]{1,5})/;\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/v6/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/v6/helpers.js":
/*!****************************************************!*\
  !*** ./node_modules/ip-address/dist/v6/helpers.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.spanAllZeroes = spanAllZeroes;\nexports.spanAll = spanAll;\nexports.spanLeadingZeroes = spanLeadingZeroes;\nexports.simpleGroup = simpleGroup;\n/**\n * @returns {String} the string with all zeroes contained in a <span>\n */\nfunction spanAllZeroes(s) {\n    return s.replace(/(0+)/g, '<span class=\"zero\">$1</span>');\n}\n/**\n * @returns {String} the string with each character contained in a <span>\n */\nfunction spanAll(s, offset = 0) {\n    const letters = s.split('');\n    return letters\n        .map((n, i) => `<span class=\"digit value-${n} position-${i + offset}\">${spanAllZeroes(n)}</span>`)\n        .join('');\n}\nfunction spanLeadingZeroesSimple(group) {\n    return group.replace(/^(0+)/, '<span class=\"zero\">$1</span>');\n}\n/**\n * @returns {String} the string with leading zeroes contained in a <span>\n */\nfunction spanLeadingZeroes(address) {\n    const groups = address.split(':');\n    return groups.map((g) => spanLeadingZeroesSimple(g)).join(':');\n}\n/**\n * Groups an address\n * @returns {String} a grouped address\n */\nfunction simpleGroup(addressString, offset = 0) {\n    const groups = addressString.split(':');\n    return groups.map((g, i) => {\n        if (/group-v4/.test(g)) {\n            return g;\n        }\n        return `<span class=\"hover-group group-${i + offset}\">${spanLeadingZeroesSimple(g)}</span>`;\n    });\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/v6/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/v6/regular-expressions.js":
/*!****************************************************************!*\
  !*** ./node_modules/ip-address/dist/v6/regular-expressions.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ADDRESS_BOUNDARY = void 0;\nexports.groupPossibilities = groupPossibilities;\nexports.padGroup = padGroup;\nexports.simpleRegularExpression = simpleRegularExpression;\nexports.possibleElisions = possibleElisions;\nconst v6 = __importStar(__webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ip-address/dist/v6/constants.js\"));\nfunction groupPossibilities(possibilities) {\n    return `(${possibilities.join('|')})`;\n}\nfunction padGroup(group) {\n    if (group.length < 4) {\n        return `0{0,${4 - group.length}}${group}`;\n    }\n    return group;\n}\nexports.ADDRESS_BOUNDARY = '[^A-Fa-f0-9:]';\nfunction simpleRegularExpression(groups) {\n    const zeroIndexes = [];\n    groups.forEach((group, i) => {\n        const groupInteger = parseInt(group, 16);\n        if (groupInteger === 0) {\n            zeroIndexes.push(i);\n        }\n    });\n    // You can technically elide a single 0, this creates the regular expressions\n    // to match that eventuality\n    const possibilities = zeroIndexes.map((zeroIndex) => groups\n        .map((group, i) => {\n        if (i === zeroIndex) {\n            const elision = i === 0 || i === v6.GROUPS - 1 ? ':' : '';\n            return groupPossibilities([padGroup(group), elision]);\n        }\n        return padGroup(group);\n    })\n        .join(':'));\n    // The simplest case\n    possibilities.push(groups.map(padGroup).join(':'));\n    return groupPossibilities(possibilities);\n}\nfunction possibleElisions(elidedGroups, moreLeft, moreRight) {\n    const left = moreLeft ? '' : ':';\n    const right = moreRight ? '' : ':';\n    const possibilities = [];\n    // 1. elision of everything (::)\n    if (!moreLeft && !moreRight) {\n        possibilities.push('::');\n    }\n    // 2. complete elision of the middle\n    if (moreLeft && moreRight) {\n        possibilities.push('');\n    }\n    if ((moreRight && !moreLeft) || (!moreRight && moreLeft)) {\n        // 3. complete elision of one side\n        possibilities.push(':');\n    }\n    // 4. elision from the left side\n    possibilities.push(`${left}(:0{1,4}){1,${elidedGroups - 1}}`);\n    // 5. elision from the right side\n    possibilities.push(`(0{1,4}:){1,${elidedGroups - 1}}${right}`);\n    // 6. no elision\n    possibilities.push(`(0{1,4}:){${elidedGroups - 1}}0{1,4}`);\n    // 7. elision (including sloppy elision) from the middle\n    for (let groups = 1; groups < elidedGroups - 1; groups++) {\n        for (let position = 1; position < elidedGroups - groups; position++) {\n            possibilities.push(`(0{1,4}:){${position}}:(0{1,4}:){${elidedGroups - position - groups - 1}}0{1,4}`);\n        }\n    }\n    return groupPossibilities(possibilities);\n}\n//# sourceMappingURL=regular-expressions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/v6/regular-expressions.js\n");

/***/ })

};
;