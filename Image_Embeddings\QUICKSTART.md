# Quick Start Guide - Image Embedding Service

## Manual Start

### 1. Start the Service

```bash
cd Image_Embeddings
docker-compose up -d --build
```

### 2. Verify It's Running

```bash
# For local testing
curl http://localhost:8019/health

# For external access (replace YOUR_SERVER_IP)
curl http://YOUR_SERVER_IP:8019/health
```

Expected response:
```json
{
  "status": "healthy",
  "model": "google/siglip-so400m-patch14-384",
  "device": "cuda:0",
  "cuda_available": true,
  "gpu_name": "NVIDIA GeForce RTX 3090"
}
```

## Usage Examples

### Python

```python
from Image_Embeddings.client import ImageEmbeddingClient

# Initialize client with appropriate URL:
# - Local testing: "http://localhost:8019"
# - Docker network: "http://image-embedding-service:8019" 
# - External server: "http://YOUR_SERVER_IP:8019"
client = ImageEmbeddingClient("http://image-embedding-service:8019")

# Generate embeddings
result = client.embed_image_files(["path/to/image.jpg"])
print(f"Embedding dimension: {result['dimension']}")
print(f"Processing time: {result['processing_time']}s")

# Generate captions
captions = client.caption_image_files(["path/to/image.jpg"])
print(f"Caption: {captions['captions'][0]}")
```

### cURL

```bash
# Note: Replace with your actual service URL
# - Local: http://localhost:8019
# - Docker network: http://image-embedding-service:8019
# - External: http://YOUR_SERVER_IP:8019

# Encode image to base64
base64_image=$(base64 -w 0 image.jpg)

# Generate embedding
curl -X POST http://YOUR_SERVER_IP:8019/embed \
  -H "Content-Type: application/json" \
  -d "{\"images\": [\"$base64_image\"]}"

# Generate caption
curl -X POST http://YOUR_SERVER_IP:8019/caption \
  -H "Content-Type: application/json" \
  -d "{\"images\": [\"$base64_image\"], \"max_length\": 100}"
```

### JavaScript/Node.js

```javascript
const axios = require('axios');
const fs = require('fs');

// Read and encode image
const imageBuffer = fs.readFileSync('image.jpg');
const base64Image = imageBuffer.toString('base64');

// Generate embedding
const response = await axios.post('http://localhost:8019/embed', {
  images: [base64Image]
});

console.log('Embedding dimension:', response.data.dimension);
console.log('Processing time:', response.data.processing_time);
```

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/health` | GET | Health check |
| `/model-info` | GET | Model information |
| `/embed` | POST | Generate image embeddings |
| `/caption` | POST | Generate image captions |
| `/embed-text` | POST | Generate text embeddings |
| `/upload-embed` | POST | Upload and embed images |

## Common Commands

```bash
# View logs
docker-compose logs -f image-embedding-service

# Stop service
docker-compose down

# Restart service
docker-compose restart

# Rebuild and start
docker-compose up -d --build

# Check GPU usage
nvidia-smi

# Check service status (adjust URL as needed)
curl http://localhost:8019/health  # Local
curl http://YOUR_SERVER_IP:8019/health  # External
```

## Troubleshooting

### GPU Not Detected
```bash
# Check if GPU is available
nvidia-smi

# Test Docker GPU access
docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi
```

### Service Won't Start
```bash
# Check logs
docker-compose logs image-embedding-service

# Check if port is available
lsof -i :8019  # Linux/Mac
netstat -ano | findstr :8019  # Windows
```

### Out of Memory
Edit `.env` and reduce batch size:
```env
BATCH_SIZE=4
```

Then restart:
```bash
docker-compose restart
```

## Next Steps

1. Read [README.md](README.md) for detailed documentation
2. Check [DEPLOYMENT.md](DEPLOYMENT.md) for production deployment
3. Review [FILES_OVERVIEW.md](FILES_OVERVIEW.md) for architecture
4. Run `test_service.py` for comprehensive testing

## Performance

### Expected Performance (GPU)
- **Embedding**: 50-100ms per image
- **Caption**: 800ms-1.5s per image
- **Batch (8 images)**: ~400ms total

### Expected Performance (CPU)
- **Embedding**: 300-500ms per image
- **Caption**: 2-3s per image

## Support

Need help?
1. Check logs: `docker-compose logs -f`
2. Verify GPU: `nvidia-smi`
3. Test API: `curl http://localhost:8019/health`
4. Run tests: `python test_service.py`

## Integration Example

Integrate with Data Ingestion Service:

```python
# In Data_Ingestion_Service/pipeline.py
from Image_Embeddings.client import ImageEmbeddingClient
import os

class DocumentProcessor:
    def __init__(self, config):
        # Use environment variable or Docker service name
        service_url = os.getenv("IMAGE_EMBEDDING_SERVICE_URL", "http://image-embedding-service:8019")
        self.image_embedding_client = ImageEmbeddingClient(service_url)
    
    def process_document_images(self, images_base64):
        # Generate embeddings for Qdrant
        embeddings_result = self.image_embedding_client.embed_images(
            images_base64
        )
        embeddings = embeddings_result['embeddings']
        
        # Generate captions for context
        captions_result = self.image_embedding_client.caption_images(
            images_base64
        )
        captions = captions_result['captions']
        
        return embeddings, captions
```

That's it! Your Image Embedding Service is ready to use. 🚀

