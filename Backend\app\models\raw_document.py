from sqlalchemy import Column, Integer, String, Text, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON>an
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

class RawDocument(Base):
    __tablename__ = "raw_documents"
    
    id = Column(String(255), primary_key=True, index=True)
    space_id = Column(Integer, ForeignKey('user_spaces.id'), nullable=False, index=True)
    type = Column(String(50), nullable=False, index=True)  # 'text', 'pdf', 'image', etc.
    content = Column(Text, nullable=True)
    base64 = Column(Text, nullable=True)  # For binary content
    source = Column(String(500), nullable=True)  # Original source/filename
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    space = relationship("UserSpace", back_populates="raw_documents")
    
    def to_dict(self) -> dict:
        """Convert raw document to dictionary"""
        return {
            'id': self.id,
            'space_id': self.space_id,
            'space_name': self.space.name if self.space else None,
            'type': self.type,
            'source': self.source,
            'has_content': bool(self.content),
            'has_base64': bool(self.base64),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'collection_name': self.space.collection_name if self.space else None,  # This will be username only now
            'username': self.space.owner.username if self.space and self.space.owner else None
        }
    
    def __repr__(self):
        return f'<RawDocument {self.id}>'
