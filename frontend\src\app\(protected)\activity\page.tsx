'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { MainLayout } from '@/components/layout';
import { UserOverviewGrid, RecentActivityFeed } from '@/components/dashboard';
import UserTicketsPanel from '@/components/tickets/UserTicketsPanel';
import TicketCreationModal from '@/components/tickets/TicketCreationModal';
import { getMySummary, getMyMetrics, UserMetrics } from '@/lib/api';
import ProfileModal from '@/components/profile/ProfileModal';
import Link from 'next/link';

type NavigationTab = 'overview' | 'activity' | 'tickets' | 'profile';

export default function ActivityPage() {
  const router = useRouter();
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [summary, setSummary] = useState<any>(null);
  const [summaryLoading, setSummaryLoading] = useState(true);
  const [metrics, setMetrics] = useState<UserMetrics | null>(null);
  const [metricsLoading, setMetricsLoading] = useState(true);
  const [showTicketModal, setShowTicketModal] = useState(false);
  const [activeTab, setActiveTab] = useState<NavigationTab>('overview');

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        setCurrentUser(user);
      } catch {
        router.push('/login');
      }
    } else {
      router.push('/login');
    }
  }, [router]);

  const handleRefresh = () => setRefreshTrigger((v) => v + 1);

  useEffect(() => {
    const fetchSummary = async () => {
      try {
        setSummaryLoading(true);
        const s = await getMySummary();
        setSummary(s);
      } catch (e) {
        // swallow; component shows empty states
      } finally {
        setSummaryLoading(false);
      }
    };
    const fetchMetrics = async () => {
      try {
        setMetricsLoading(true);
        const m = await getMyMetrics();
        setMetrics(m.metrics);
      } catch (e) {
      } finally {
        setMetricsLoading(false);
      }
    };
    if (currentUser) {
      fetchSummary();
      fetchMetrics();
    }
  }, [currentUser, refreshTrigger]);

  if (!currentUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  const navigationItems = [
    {
      id: 'overview' as NavigationTab,
      label: 'Overview',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
    },
    {
      id: 'activity' as NavigationTab,
      label: 'Activity',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      id: 'tickets' as NavigationTab,
      label: 'Tickets',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
        </svg>
      ),
    },
    {
      id: 'profile' as NavigationTab,
      label: 'Profile',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
    },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <UserOverviewGrid 
              summary={summary} 
              metrics={metrics} 
              loadingSummary={summaryLoading} 
              loadingMetrics={metricsLoading} 
            />
          </div>
        );
      
      case 'activity':
        return (
          <div>
            <RecentActivityFeed refreshTrigger={refreshTrigger} />
          </div>
        );
      
      case 'tickets':
        return (
          <div>
            <UserTicketsPanel />
          </div>
        );
      
      case 'profile':
        return (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 sm:p-8 hover:shadow-xl transition-shadow duration-300">
              <ProfileModal user={currentUser} variant="inline" />
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <MainLayout>
      <div className="flex h-full">
        {/* Sidebar Navigation - 20% */}
        <aside className="hidden lg:flex lg:flex-col lg:w-1/5 bg-white border-r border-gray-200 sticky top-0 h-screen overflow-y-auto">
          <div className="p-6">
            <h1 className="text-lg font-semibold text-gray-900">Dashboard</h1>
            <p className="text-xs text-gray-500 mt-1">Manage your workspace</p>
          </div>
          
          <nav className="flex-1 px-3 space-y-1">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === item.id
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                {item.icon}
                <span>{item.label}</span>
              </button>
            ))}
          </nav>

          <div className="p-3 space-y-2 border-t border-gray-200">
            <Link
              href="/faq"
              className="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>FAQ</span>
            </Link>
            
            <button
              onClick={() => setShowTicketModal(true)}
              className="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium bg-green-600 text-white hover:bg-green-700 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Raise Ticket</span>
            </button>
          </div>
        </aside>

        {/* Mobile Navigation */}
        <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-10">
          <nav className="flex justify-around items-center px-2 py-2">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`flex flex-col items-center gap-1 px-3 py-2 rounded-lg text-xs font-medium transition-colors ${
                  activeTab === item.id
                    ? 'text-blue-700'
                    : 'text-gray-600'
                }`}
              >
                {item.icon}
                <span>{item.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Main Content Area - 80% */}
        <main className="flex-1 overflow-y-auto bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8 pb-20 lg:pb-8">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">
                  {activeTab === 'overview' && 'Usage Overview'}
                  {activeTab === 'activity' && 'Activity Feed'}
                  {activeTab === 'tickets' && 'My Support Tickets'}
                  {activeTab === 'profile' && 'My Profile'}
                </h1>
                <p className="mt-1 text-sm text-gray-500">
                  {activeTab === 'overview' && 'Monitor your usage statistics and engagement metrics'}
                  {activeTab === 'activity' && 'View your recent interactions and activities'}
                  {activeTab === 'tickets' && 'Track and manage your support ticket requests'}
                  {activeTab === 'profile' && 'Manage your personal profile information'}
                </p>
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={handleRefresh}
                  className="inline-flex items-center justify-center p-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors"
                  title="Refresh"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </button>
                
                {/* Mobile actions */}
                <div className="lg:hidden flex gap-2">
                  <Link
                    href="/faq"
                    className="inline-flex items-center justify-center px-3 py-2 rounded-lg text-sm font-medium bg-blue-900 text-white hover:bg-blue-700 transition-colors"
                  >
                    FAQ
                  </Link>
                  <button
                    onClick={() => setShowTicketModal(true)}
                    className="inline-flex items-center justify-center px-3 py-2 rounded-lg text-sm font-medium bg-green-600 text-white hover:bg-green-700 transition-colors"
                  >
                    Ticket
                  </button>
                </div>
              </div>
            </div>

            {/* Dynamic Content */}
            {renderContent()}
          </div>
        </main>
      </div>

      {/* Ticket Creation Modal */}
      <TicketCreationModal
        isOpen={showTicketModal}
        onClose={() => setShowTicketModal(false)}
        onTicketCreated={() => {
          handleRefresh();
          setActiveTab('tickets');
        }}
      />
    </MainLayout>
  );
}


