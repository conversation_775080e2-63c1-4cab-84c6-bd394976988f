'use client';

import { ReactNode } from 'react';
import { NotificationProvider, LiveChatProvider } from '@/contexts';
import { ToastContainer } from '@/components/notifications';


interface ProvidersProps {
  children: ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
 
  return (
    <NotificationProvider>
      <LiveChatProvider>
        {children}
        <ToastContainer />
      </LiveChatProvider>
    </NotificationProvider>
  );
}