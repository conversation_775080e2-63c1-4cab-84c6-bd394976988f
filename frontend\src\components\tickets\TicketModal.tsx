'use client';

import React, { useState, useEffect } from 'react';
import { FiX } from 'react-icons/fi';
import { useTicketActions } from '@/hooks';
import { 
  Ticket, 
  TICKET_PRIORITIES, 
  TICKET_CATEGORIES, 
  TICKET_STATUSES,
  TicketPriority,
  TicketCategory,
  TicketStatus
} from '@/types/ticket';

interface TicketModalProps {
  isOpen: boolean;
  onClose: () => void;
  ticket: Ticket | null;
  isAdmin?: boolean;
  onStatusChange?: (ticketId: number, newStatus: string) => void;
  onRefresh?: () => void;
}

export default function TicketModal({
  isOpen,
  onClose,
  ticket,
  isAdmin = false,
  onStatusChange,
  onRefresh
}: TicketModalProps) {
  const [adminNotes, setAdminNotes] = useState('');
  const [viewingImage, setViewingImage] = useState<string | null>(null);
  const { updateTicketNotes, downloadAttachment, viewAttachment, isSubmitting } = useTicketActions();

  useEffect(() => {
    if (ticket) {
      setAdminNotes(ticket.admin_notes || '');
    }
  }, [ticket]);

  useEffect(() => {
    return () => {
      if (viewingImage) {
        window.URL.revokeObjectURL(viewingImage);
      }
    };
  }, [viewingImage]);

  const handleViewImage = async (attachmentId: number) => {
    try {
      const imageUrl = await viewAttachment(attachmentId);
      setViewingImage(imageUrl);
    } catch (err) {
      console.error('Error viewing image:', err);
    }
  };

  const closeImageViewer = () => {
    if (viewingImage) {
      window.URL.revokeObjectURL(viewingImage);
      setViewingImage(null);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const handleAdminNotesUpdate = async () => {
    if (!ticket) return;

    try {
      await updateTicketNotes(ticket.id, adminNotes);
      onRefresh?.();
    } catch (err) {
      console.error('Error updating admin notes:', err);
    }
  };

  if (!isOpen || !ticket) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-xl sm:w-full max-h-[90vh] overflow-y-auto">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-4 sm:pb-4">
            {/* Close button */}
            <div className="absolute top-0 right-0 pt-4 pr-4">
              <button
                type="button"
                onClick={onClose}
                className="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiX className="h-6 w-6" />
              </button>
            </div>

            {/* Header */}
            <div className="flex items-start justify-between mb-6">
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">{ticket.title}</h2>
                <p className="text-sm text-gray-600">
                  Ticket #{ticket.id}
                  {isAdmin && ticket.creator_username && (
                    <> • Created by {ticket.creator_username}</>
                  )}
                  {' • ' + formatDate(ticket.created_at)}
                </p>
              </div>
            </div>

            {/* Status badges */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">Status:</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  TICKET_STATUSES[ticket.status as TicketStatus]?.color || 'bg-gray-100 text-gray-800'
                }`}>
                  {TICKET_STATUSES[ticket.status as TicketStatus]?.label || ticket.status}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">Priority:</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  TICKET_PRIORITIES[ticket.priority as TicketPriority]?.color || 'bg-gray-100 text-gray-800'
                }`}>
                  {TICKET_PRIORITIES[ticket.priority as TicketPriority]?.label || ticket.priority}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">Category:</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  TICKET_CATEGORIES[ticket.category as TicketCategory]?.color || 'bg-gray-100 text-gray-800'
                }`}>
                  {TICKET_CATEGORIES[ticket.category as TicketCategory]?.label || ticket.category}
                </span>
              </div>
            </div>

            {/* Description */}
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Description</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-700 whitespace-pre-wrap">{ticket.description}</p>
              </div>
            </div>

            {/* Attachments */}
            {ticket.attachments && ticket.attachments.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Attachments</h3>
                <div className="space-y-3">
                  {ticket.attachments.map((attachment) => (
                    <div key={attachment.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 bg-gray-50 rounded-lg border gap-3">
                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                        <div className="flex-shrink-0">
                          {attachment.content_type.startsWith('image/') ? (
                            <svg className="h-8 w-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          ) : (
                            <svg className="h-8 w-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          )}
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="text-sm font-medium text-gray-900 truncate">{attachment.original_filename}</p>
                          <p className="text-xs text-gray-500">
                            {(attachment.file_size / 1024).toFixed(1)} KB • Uploaded by {attachment.uploader_username || 'Unknown'} • {formatDate(attachment.uploaded_at)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center justify-end space-x-2 sm:space-x-3 flex-shrink-0">
                        {attachment.content_type.startsWith('image/') && (
                          <button
                            onClick={() => handleViewImage(attachment.id)}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium px-2 py-1 sm:px-3 sm:py-1.5 rounded-md hover:bg-blue-50 transition-colors min-w-0"
                          >
                            View
                          </button>
                        )}
                        <button
                          onClick={() => downloadAttachment(attachment.id, attachment.original_filename)}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium px-2 py-1 sm:px-3 sm:py-1.5 rounded-md hover:bg-blue-50 transition-colors min-w-0"
                        >
                          Download
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Admin Notes (Admin only) */}
            {isAdmin && (
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-lg font-medium text-gray-900">Admin Notes</h3>
                  <button
                    onClick={handleAdminNotesUpdate}
                    disabled={isSubmitting}
                    className="px-3 py-1 bg-blue-900 text-white text-sm rounded-md hover:bg-blue-800 transition-colors disabled:opacity-50"
                  >
                    {isSubmitting ? 'Saving...' : 'Save Notes'}
                  </button>
                </div>
                <textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Add admin notes here..."
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical min-h-[100px]"
                  rows={4}
                />
                {ticket.admin_notes && (
                  <div className="mt-2 text-xs text-gray-500">
                    Last updated: {formatDate(ticket.updated_at)}
                  </div>
                )}
              </div>
            )}

            {/* Timeline */}
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Timeline</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg className="h-4 w-4 text-blue-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">Ticket Created</p>
                    <p className="text-sm text-gray-500">{formatDate(ticket.created_at)}</p>
                  </div>
                </div>

                {ticket.status !== 'open' && (
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                      <svg className="h-4 w-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">
                        Status Updated to {ticket.status.replace('_', ' ').toUpperCase()}
                      </p>
                      <p className="text-sm text-gray-500">{formatDate(ticket.updated_at)}</p>
                    </div>
                  </div>
                )}

                {ticket.resolved_at && (
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                      <svg className="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">Ticket Resolved</p>
                      <p className="text-sm text-gray-500">{formatDate(ticket.resolved_at)}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Footer Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              {isAdmin && ticket.status === 'open' && (
                <button
                  onClick={() => onStatusChange?.(ticket.id, 'in_progress')}
                  className="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 transition-colors"
                >
                  Start Working
                </button>
              )}
              {isAdmin && ticket.status === 'in_progress' && (
                <button
                  onClick={() => onStatusChange?.(ticket.id, 'resolved')}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                >
                  Mark Resolved
                </button>
              )}
              {isAdmin && (ticket.status === 'open' || ticket.status === 'in_progress') && (
                <button
                  onClick={() => onStatusChange?.(ticket.id, 'closed')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                >
                  Close Ticket
                </button>
              )}
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Image Viewer Modal */}
      {viewingImage && (
        <div className="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-90" onClick={closeImageViewer}>
          <button
            onClick={closeImageViewer}
            className="absolute top-4 right-4 text-white hover:text-gray-300 bg-black bg-opacity-50 rounded-full p-2"
          >
            <FiX className="h-8 w-8" />
          </button>
          <div className="max-w-[90vw] max-h-[90vh] overflow-auto" onClick={(e) => e.stopPropagation()}>
            <img
              src={viewingImage}
              alt="Attachment preview"
              className="max-w-full max-h-[90vh] object-contain"
            />
          </div>
        </div>
      )}
    </div>
  );
}
