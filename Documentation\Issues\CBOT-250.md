# CBOT-250 - Image Input Support in Tickets
Developer: Onkar A<PERSON>junmundhe
Date: 25 October 2025

## 2. Code Changes

### Backend Changes
- **Backend/app/models/ticket_attachment.py** - NEW FILE - Created TicketAttachment model for storing file attachment metadata
- **Backend/app/models/__init__.py** - Added TicketAttachment import to model registry
- **Backend/app/models/ticket.py** - Added relationship to TicketAttachment model
- **Backend/app/routers/ticket.py** - Enhanced ticket creation endpoint to handle file attachments via FormData
- **Backend/app/schemas/ticket.py** - Added TicketAttachmentResponse schema for API responses
- **Backend/app/services/minio_service.py** - Extended MinIO service for ticket attachment storage
- **Backend/app/utils/image_processing.py** - Enhanced image validation for ticket attachments
- **Backend/migration/create_ticket_attachments_table.py** - NEW FILE - Database migration for ticket_attachments table

### Frontend Changes
- **frontend/src/components/tickets/TicketCreationModal.tsx** - Added image upload functionality with drag-drop support
- **frontend/src/components/tickets/TicketModal.tsx** - Added attachment viewing and download capabilities
- **frontend/src/components/tickets/UserTicketsPanel.tsx** - Enhanced ticket display with attachment indicators
- **frontend/src/components/admin/TicketsPanel.tsx** - Added admin attachment management features
- **frontend/src/components/tickets/Pagination.tsx** - NEW FILE - Pagination component for ticket lists
- **frontend/src/components/tickets/index.ts** - Updated exports for new components
- **frontend/src/contexts/TicketContext.tsx** - Added attachment state management
- **frontend/src/hooks/useTicketActions.ts** - Added attachment upload/download hooks
- **frontend/src/hooks/useTickets.ts** - Enhanced ticket fetching with attachment data
- **frontend/src/types/ticket.ts** - Added TicketAttachment type definitions

## 3. Business Logic

### Problem Being Solved
Users needed the ability to attach images and files to support tickets to better describe issues, provide screenshots, and share additional context with support staff.

### Solution Approach
- Implemented a comprehensive file attachment system using FormData for multipart uploads
- Leveraged existing MinIO infrastructure for secure file storage
- Extended image processing utilities to validate and sanitize ticket attachments
- Created a dedicated TicketAttachment model to track file metadata
- Built responsive frontend components for file upload, preview, and management

### Key Workflows
1. **File Upload**: Users select images/files → Frontend validates → Backend processes → MinIO storage → Database record
2. **File Viewing**: Admin/User clicks attachment → Backend retrieves from MinIO → Frontend displays
3. **File Download**: User requests download → Backend generates presigned URL → File downloaded

## 4. Dependent Modules

### Database Changes
- New table: `ticket_attachments` with foreign key to `tickets`
- Migration script: `create_ticket_attachments_table.py`

### API Endpoints Modified/Created
- `POST /tickets/with-attachments` - Enhanced to accept FormData with files
- `GET /tickets/{id}/attachments` - Retrieve ticket attachments
- `GET /attachments/{id}/download` - Download attachment file
- `GET /attachments/{id}/view` - View attachment (images)

### Services Affected
- MinIO Service - Extended for ticket attachment bucket management
- Image Processing Service - Validation for ticket attachments
- Ticket Service - Enhanced with attachment handling

### New Dependencies
- Enhanced file upload libraries (frontend)
- Image processing utilities (backend)
- FormData handling improvements

## 5. Testing Requirements

### Unit Tests
- TicketAttachment model CRUD operations
- Image validation for ticket attachments
- MinIO upload/download for ticket bucket
- Frontend component rendering with attachments

### Integration Tests
- End-to-end ticket creation with file attachments
- File upload with various formats (JPEG, PNG, PDF, DOCX)
- Attachment viewing and downloading
- Error handling for oversized files and invalid formats

### API Testing
- POST /tickets/with-attachments with multipart/form-data
- File size validation (max 10MB per file, max 5 files)
- Content-type validation for images and documents
- Authentication and authorization for attachment access

### Edge Cases
- Upload with no files selected
- Upload with files exceeding size limits
- Upload with unsupported file types

## 6. Production Deployment Checklist

### Database Migration
- Run `create_ticket_attachments_table.py` migration
- Verify foreign key constraints are properly created

### MinIO Configuration
- Ensure `support_tickets` bucket exists and has proper permissions
- Verify bucket policies for attachment access

### Environment Variables
- Verify MAX_ATTACHMENT_SIZE configuration
- Check MINIO_ENDPOINT and credentials
- Validate file type restrictions in config

## 7. Known Issues & Warnings

### Current Limitations
- File size limited to 10MB per attachment
- Maximum 5 attachments per ticket
- Image formats restricted to JPEG, PNG, WebP, HEIC, HEIF

### Technical Debt
- Frontend file upload could benefit from progress indicators
- Bulk attachment operations not yet implemented
- Attachment compression for large images could be optimized