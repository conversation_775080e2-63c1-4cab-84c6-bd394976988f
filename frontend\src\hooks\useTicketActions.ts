import { useState, useCallback } from 'react';
import { API_BASE_URL } from '@/lib/api';
import { TicketCreationData, TicketUpdateData } from '@/types/ticket';

interface UseTicketActionsReturn {
  createTicket: (data: TicketCreationData) => Promise<void>;
  updateTicketStatus: (ticketId: number, status: string) => Promise<void>;
  updateTicketNotes: (ticketId: number, adminNotes: string) => Promise<void>;
  deleteTicket: (ticketId: number) => Promise<void>;
  downloadAttachment: (attachmentId: number, filename: string) => Promise<void>;
  viewAttachment: (attachmentId: number) => Promise<string>;
  isSubmitting: boolean;
  error: string | null;
  clearError: () => void;
}

/*
 * Custom hook for ticket CRUD operations
 * Handles creating, updating, and deleting tickets
 */
export const useTicketActions = (): UseTicketActionsReturn => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const createTicket = useCallback(async (data: TicketCreationData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const token = localStorage.getItem('access_token');
      if (!token) throw new Error('Not authenticated');

      let body: string | FormData;
      let headers: Record<string, string> = {
        Authorization: `Bearer ${token}`,
      };
      let url = `${API_BASE_URL}/tickets`;

      if (data.images && data.images.length > 0) {
        // Use FormData endpoint for file uploads
        const formData = new FormData();
        formData.append('title', data.title);
        formData.append('description', data.description);
        formData.append('category', data.category);
        formData.append('priority', data.priority);

        data.images.forEach((image) => {
          formData.append('files', image);
        });

        body = formData;
        url = `${API_BASE_URL}/tickets/with-attachments`;
        // Don't set Content-Type for FormData, let browser set it with boundary
      } else {
        // Use JSON for text-only tickets
        headers['Content-Type'] = 'application/json';
        body = JSON.stringify({
          title: data.title,
          description: data.description,
          category: data.category,
          priority: data.priority,
        });
      }

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to create ticket');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err; // Re-throw so caller can handle it
    } finally {
      setIsSubmitting(false);
    }
  }, []);

  const updateTicketStatus = useCallback(async (ticketId: number, status: string) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const token = localStorage.getItem('access_token');
      if (!token) throw new Error('Not authenticated');

      const response = await fetch(`${API_BASE_URL}/tickets/${ticketId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error('Failed to update ticket status');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsSubmitting(false);
    }
  }, []);

  const updateTicketNotes = useCallback(async (ticketId: number, adminNotes: string) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const token = localStorage.getItem('access_token');
      if (!token) throw new Error('Not authenticated');

      const response = await fetch(`${API_BASE_URL}/tickets/${ticketId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ admin_notes: adminNotes }),
      });

      if (!response.ok) {
        throw new Error('Failed to update admin notes');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsSubmitting(false);
    }
  }, []);

  const deleteTicket = useCallback(async (ticketId: number) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const token = localStorage.getItem('access_token');
      if (!token) throw new Error('Not authenticated');

      const response = await fetch(`${API_BASE_URL}/tickets/${ticketId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete ticket');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsSubmitting(false);
    }
  }, []);

  const downloadAttachment = useCallback(async (attachmentId: number, filename: string) => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) throw new Error('Not authenticated');

      const response = await fetch(
        `${API_BASE_URL}/tickets/attachments/${attachmentId}/download`,
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to download attachment');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to download attachment';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const viewAttachment = useCallback(async (attachmentId: number): Promise<string> => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) throw new Error('Not authenticated');

      const response = await fetch(
        `${API_BASE_URL}/tickets/attachments/${attachmentId}/download`,
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to load attachment');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      return url;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load attachment';
      setError(errorMessage);
      throw err;
    }
  }, []);

  return {
    createTicket,
    updateTicketStatus,
    updateTicketNotes,
    deleteTicket,
    downloadAttachment,
    viewAttachment,
    isSubmitting,
    error,
    clearError
  };
};
