import sys
from pathlib import Path
import argparse

sys.path.append(str(Path(__file__).parent))

from app.database import SessionLocal, engine, Base
from sqlalchemy import MetaData
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def drop_and_create_tables():
    """Drop all tables and recreate them.

    Uses full database reflection to drop tables in dependency order to avoid
    foreign key constraint errors from objects not present in SQLAlchemy models.
    """
    from app.models import (
        User, UserSpace, RawDocument,
        QueryFeedback, ExpertChatInteraction, SharedResponse,
        KnowledgeDocument, Conversation, Message, UserStatus,
        Document, Media, SharedLink, Group, GroupMember, GroupMessage,
        DeletedMessage, DeletedGroupMessage
    )
    
    logger.info("Dropping all tables (via reflection to respect dependencies)...")
    # Reflect the entire database so we can drop in the correct dependency order
    reflected_metadata = MetaData()
    reflected_metadata.reflect(bind=engine)

    if reflected_metadata.tables:
        logger.info(f"Found {len(reflected_metadata.tables)} existing tables to drop")
        reflected_metadata.drop_all(bind=engine)
    else:
        # Fallback: if nothing reflects (empty DB), still call drop_all on Base
        Base.metadata.drop_all(bind=engine)

    logger.info("Creating all tables...")
    Base.metadata.create_all(bind=engine)
    logger.info(f"Created {len(Base.metadata.tables)} tables:")
    for table_name in Base.metadata.tables.keys():
        logger.info(f"  - {table_name}")

def init_database(seed_data: bool = False):
    logger.info("Initializing AI Sarthi database...")
    drop_and_create_tables()
    
    if seed_data:
        # Import seeder here to avoid circular imports
        from app.models.seeder import seed_all_data, print_seeding_summary
        
        # Create session for seeding
        db = SessionLocal()
        
        try:
            seed_all_data(db, clear_existing=True)
            print_seeding_summary()
            
            # Automatically setup GPU services
            print("\n" + "="*60)
            print("Setting up GPU Services...")
            print("="*60)
            
            # Import and run GPU setup
            sys.path.append(str(Path(__file__).parent.parent))
            from qdrant_collection import main as setup_gpu_services
            
            gpu_setup_success = setup_gpu_services()
            
            print("\n" + "="*60)
            print("Database initialization completed successfully!")
            print("="*60)
            print(f"API Documentation: http://localhost:8000/api/v1/docs")
            print("Note: Update the frontend NEXT_PUBLIC_API_URL environment variable to match your backend URL")
            if gpu_setup_success:
                print("✓ GPU Services: Ready for document processing")
            else:
                print("⚠ GPU Services: Some issues occurred (check logs)")
            print("="*60)
            
        except Exception as e:
            logger.error(f"Error during database seeding: {e}")
            db.rollback()
            raise
        finally:
            db.close()
    else:
        print("\nDatabase tables created successfully!")
        print("To seed default data and setup GPU services, run: python init_db.py --seed")
        


def main():
    """Main function with command line argument parsing"""
    parser = argparse.ArgumentParser(description='Initialize AI Sarthi database')
    parser.add_argument('--seed', action='store_true', 
                       help='Seed database with default users and knowledge documents')
    
    args = parser.parse_args()

    # Initialize the database (and optionally seed data)
    init_database(seed_data=args.seed)

if __name__ == "__main__":
    main() 