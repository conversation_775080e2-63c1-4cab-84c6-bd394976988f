import React, { useState, useMemo } from 'react';
import { SearchSource, DocumentChunkSource, API_BASE_URL, getSecureToken } from '@/lib/api';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';

interface SourcesDisplayProps {
  sources: SearchSource[][] | string[] | DocumentChunkSource[] | any[];
  isWebSearch: boolean;
  isDocumentSearch: boolean;
  isDeepSearch: boolean;
}

export default function SourcesDisplay({ 
  sources, 
  isWebSearch, 
  isDocumentSearch, 
  isDeepSearch
}: SourcesDisplayProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [imagePreview, setImagePreview] = useState<{ url: string; title?: string } | null>(null);

  // Markdown components to ensure links open in a new tab
  const markdownComponents = {
    a: ({ node, ...props }: any) => (
      <a
        {...props}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-900 hover:underline"
      />
    ),
  };

  // Helper to deduplicate string filenames
  const uniqueDocumentSources = useMemo(() => {
    if (!Array.isArray(sources)) return [];
    const seen = new Set<string>();
    return (sources as (string | DocumentChunkSource)[]).filter(src => {
      if (typeof src !== 'string') return false;
      const key = src;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    }) as string[];
  }, [sources]);

  // Runtime detection for image sources coming from Image Spaces
  const isImageSources = Array.isArray(sources) && sources.length > 0 && typeof (sources as any[])[0] === 'object' && (
    String((sources as any[])[0]?.content_type || '').startsWith('image/') || Boolean((sources as any[])[0]?.url)
  );

  // Don't show when there truly are no sources
  if (!sources || (Array.isArray(sources) && sources.length === 0)) {
    return null;
  }

  // Determine source count and type
  let sourceCount = 0;
  let sourceType = '';

  if (isWebSearch && sources && sources.length > 0) {
    // Web search sources are nested arrays of objects
    const flattenedSources = (sources as SearchSource[][]).flat();
    sourceCount = flattenedSources.filter(source => source && source.title && source.url).length;
    sourceType = 'Web Sources';
  } else if ((isDocumentSearch || isImageSources) && sources && sources.length > 0) {
    // Document search may now return array of chunk objects OR legacy filenames
    const first = sources[0] as any;
    if (isImageSources) {
      sourceCount = (sources as any[]).length;
      sourceType = 'Image Sources';
    } else if (typeof first === 'string') {
      sourceCount = uniqueDocumentSources.length;
      sourceType = 'Document Sources';
    } else {
      sourceCount = (sources as DocumentChunkSource[]).length;
      sourceType = 'Document Sources';
    }
  } else if (isDeepSearch && sources && sources.length > 0) {
    // Deep research sources are flat array of objects
    const deepSources = sources as unknown as SearchSource[];
    sourceCount = deepSources.filter(source => source && source.title).length;
    sourceType = 'Deep Research Sources';
  }

  if (sourceCount === 0) {
    return null;
  }

  const renderWebSources = () => {
    const flattenedSources = (sources as SearchSource[][]).flat();
    const validSources = flattenedSources.filter(source => source && source.title && source.url);

    return (
      <div className="space-y-3">
        {validSources.map((source, index) => (
          <div key={index} className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-all">
            <div className="space-y-2">
              <h4 className="text-sm font-semibold text-blue-900 hover:text-blue-700">
                <a 
                  href={source.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="hover:underline"
                >
                  {source.title}
                </a>
              </h4>
              <p className="text-sm text-gray-600 leading-relaxed">{source.snippet}</p>
              <div className="flex items-center justify-between">
                <a 
                  href={source.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-xs text-gray-500 hover:text-gray-700 truncate max-w-xs"
                >
                  {source.url}
                </a>
                <a 
                  href={source.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-2 py-1 text-xs text-blue-900 hover:text-blue-700 hover:bg-blue-50 rounded transition-colors"
                >
                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  Visit
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderDocumentSources = () => {
    const first = sources[0] as any;

    // Legacy string filenames rendering
    if (typeof first === 'string') {
      return (
        <div className="space-y-3">
          {uniqueDocumentSources.map((filename, index) => {
            const isPdf = filename.toLowerCase().endsWith('.pdf');
            const isText = filename.toLowerCase().endsWith('.txt');
            const documentParam = isText
              ? encodeURIComponent(filename.substring(0, filename.length - 4))
              : encodeURIComponent(filename);
            return (
              <div key={index} className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-all">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${isPdf ? 'bg-red-100' : 'bg-blue-100'}`}>
                      <svg className={`w-5 h-5 ${isPdf ? 'text-red-600' : 'text-blue-900'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">{filename}</h4>
                      <p className="text-xs text-gray-500">
                        {isPdf ? 'PDF Document' : 'Text Document'}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      window.open(`/knowledge?document=${documentParam}`, '_blank');
                    }}
                    className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-900 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                  >
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    View
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      );
    }

    // New chunk based rendering
    const chunks = sources as DocumentChunkSource[];

    return (
      <div className="space-y-3">
        {chunks.map((chunk, index) => {
          const confidenceScore = chunk.confidence_score !== undefined ? chunk.confidence_score : null;
          const confidencePercent = confidenceScore !== null ? Math.round(confidenceScore * 100) : null;
          
          // Determine confidence level and color
          let confidenceColor = 'gray';
          let confidenceBgColor = 'bg-gray-100';
          let confidenceTextColor = 'text-gray-700';
          if (confidencePercent !== null) {
            if (confidencePercent >= 70) {
              confidenceColor = 'green';
              confidenceBgColor = 'bg-green-100';
              confidenceTextColor = 'text-green-700';
            } else if (confidencePercent >= 40) {
              confidenceColor = 'yellow';
              confidenceBgColor = 'bg-yellow-100';
              confidenceTextColor = 'text-yellow-700';
            } else {
              confidenceColor = 'orange';
              confidenceBgColor = 'bg-orange-100';
              confidenceTextColor = 'text-orange-700';
            }
          }
          
          return (
            <div key={index} className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-all">
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-semibold text-gray-800 flex items-center">
                      <div className="w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center mr-3">
                        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <div>{chunk.document}</div>
                        <div className="flex items-center gap-2 mt-1">
                          {typeof chunk.page_number === 'number' && (
                            <span className="text-xs text-gray-500 font-normal">Page {chunk.page_number}</span>
                          )}
                          {confidencePercent !== null && (
                            <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${confidenceBgColor} ${confidenceTextColor}`}>
                              {confidencePercent}% relevance
                            </span>
                          )}
                        </div>
                      </div>
                    </h4>
                  </div>
                  {chunk.id && (
                    <button
                      className="inline-flex items-center px-2 py-1 text-xs text-green-600 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
                      onClick={() => {
                        window.open(`/knowledge?raw_id=${encodeURIComponent(chunk.id)}`, '_blank');
                      }}
                    >
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      View Context
                    </button>
                  )}
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="prose prose-sm text-gray-700 max-w-none">
                    <ReactMarkdown components={markdownComponents} remarkPlugins={[remarkGfm, remarkBreaks]}>
                      {chunk.snippet || ''}
                    </ReactMarkdown>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderImageSources = () => {
    const imgs = sources as any[];
    const resolveUrl = (src: any): string => {
      const token = getSecureToken();
      if (src.document_id && token) {
        return `${API_BASE_URL}/knowledge/${encodeURIComponent(src.document_id)}/download?token=${encodeURIComponent(token)}&t=${Date.now()}`;
      }
      return String(src.url || '');
    };
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {imgs.map((src: any, index: number) => (
          <div key={index} className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-all">
            <div className="space-y-3">
              {resolveUrl(src) && (
                <img
                  src={resolveUrl(src)}
                  alt={src.title || src.filename || 'image'}
                  className="w-full h-48 object-contain bg-gray-50 rounded cursor-zoom-in"
                  onClick={() => setImagePreview({ url: resolveUrl(src), title: src.title || src.filename })}
                />
              )}
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-gray-900">{src.title || src.filename}</div>
                  <div className="text-xs text-gray-500">{(src.content_type || '').toString()}</div>
                </div>
                {/* No button – image click opens preview */}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };
  
  const renderDeepResearchSources = () => {
    const deepSources = sources as unknown as SearchSource[];

    return (
      <div className="space-y-3">
        {deepSources.map((source, index) => (
          <div key={index} className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-all">
            <div className="space-y-2">
              <h4 className="text-sm font-semibold text-purple-600 hover:text-purple-700">
                {source.url && source.url !== 'N/A' ? (
                  <a 
                    href={source.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="hover:underline"
                  >
                    {source.title}
                  </a>
                ) : (
                  <span>{source.title}</span>
                )}
              </h4>
              <p className="text-sm text-gray-600 leading-relaxed">{source.snippet}</p>
              {source.url && source.url !== 'N/A' && (
                <div className="flex items-center justify-between">
                  <a 
                    href={source.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-xs text-gray-500 hover:text-gray-700 truncate max-w-xs"
                  >
                    {source.url}
                  </a>
                  <a 
                    href={source.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-2 py-1 text-xs text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded transition-colors"
                  >
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    Visit
                  </a>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  

  return (
    <>
      {/* Sources Button */}
      {sourceCount > 0 && (
        <div className="mt-4">
          <button
            onClick={() => setIsModalOpen(true)}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg hover:from-blue-900 hover:to-indigo-700 transition-all duration-200 shadow-md hover:shadow-lg"
          >
            
            <span className="text-sm font-medium">View {sourceCount} Source{sourceCount !== 1 ? 's' : ''}</span>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          </button>
        </div>
      )}

      {/* Sources Modal */}
      {isModalOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
          onClick={() => setIsModalOpen(false)}
        >
          <div
            className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-lg bg-blue-500 flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-800">{sourceType}</h2>
                  <p className="text-sm text-gray-600">{sourceCount} source{sourceCount !== 1 ? 's' : ''} found</p>
                </div>
              </div>
              <button
                onClick={() => setIsModalOpen(false)}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {isWebSearch && renderWebSources()}
              {(isDocumentSearch && !isImageSources) && renderDocumentSources()}
              {isImageSources && renderImageSources()}
              {isDeepSearch && renderDeepResearchSources()}
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end p-6 border-t border-gray-200 bg-gray-50">
              <button
                onClick={() => setIsModalOpen(false)}
                className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Image preview modal */}
      {imagePreview && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-60"
          onClick={() => setImagePreview(null)}
        >
          <div
            className="bg-white rounded-xl shadow-2xl w-full max-w-5xl overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between p-3 border-b border-gray-200">
              <div className="text-sm font-medium text-gray-800 truncate mr-2">{imagePreview.title || 'Image'}</div>
              <button
                onClick={() => setImagePreview(null)}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                aria-label="Close image preview"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-2 bg-gray-50">
              <img
                src={imagePreview.url}
                alt={imagePreview.title || 'image'}
                className="w-full max-h-[80vh] object-contain rounded"
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
} 