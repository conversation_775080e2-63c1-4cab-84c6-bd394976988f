import React from 'react';
import { useChat } from '@/contexts';
import SearchToggle from '../ui/SearchToggle';

export default function WebSearchToggle() {
  const { useWebSearch, setUseWebSearch, selectedAgent } = useChat();

  if (!selectedAgent) {
    return null;
  }

  return (
    <SearchToggle
      enabled={useWebSearch}
      onToggle={() => setUseWebSearch(!useWebSearch)}
      label="Enable Web Search"
      variant="blue"
    />
  );
} 