# followup_agent.py
import json
from typing import List, Dict, Any, Optional
import logging
from .base import BaseAgent  # only for type hints / helpers

logger = logging.getLogger(__name__)


class FollowUpSuggestionAgent:
    """
    Reuses an EXISTING BaseAgent (and its underlying agno.Agent/Gemini)
    to generate 3–5 follow-up questions. Does NOT create another model/agent.
    """

    def __init__(self, existing_base_agent: BaseAgent):
        self.base = existing_base_agent  # reuse same instance

    def _common_rules(self) -> str:
        return (
            "\n\nSTRICT OUTPUT RULES:\n"
            "- FIRST, inspect the assistant's latest answer and decide if it contained enough information.\n"
            "- Reply with ONLY a JSON array of 3 or less than 3 strings, e.g., [\"Q1?\", \"Q2?\"].\n"
            "- Each string must be a question ending with '?'.\n"
            "- Each must be under 15 words.\n"
            "- Do not include duplicates or generic filler.\n"
            "- No text before or after the JSON.\n"
            "- DO NOT use markdown formatting or code blocks (no ```json or ``` tags).\n"
            "- DO NOT wrap the JSON in any formatting - return raw JSON array only.\n"
            "- If unsure, reply with [].\n"
            "- If the assistant could not answer or lacked information, reply with [].\n"
            "- If the latest answer says anything like \"I didn't get enough information\", \"I don't know\", \"I'm not sure\", or otherwise indicates missing knowledge, you MUST return [].\n"
            "- ONLY ask about information that is explicitly supported by the provided sources.\n"
            "- If the sources array is empty, missing, or does not contain the answer to a potential follow-up, you MUST return [].\n"
            "- Returning follow-up questions when the answer lacked information is a violation of these rules.\n"
        )

    def _try_parse_json_array(self, text: str) -> Optional[List[str]]:
        try:
            parsed = json.loads(text)
            if isinstance(parsed, list):
                return [str(x) for x in parsed]
        except Exception:
            return None
        return None

    def _extract_json_fallback(self, bad_text: str) -> List[str]:
        """Extract JSON array from text that may have extra content around it"""
        try:
            # Try to find JSON array boundaries
            start = bad_text.find('[')
            end = bad_text.rfind(']')
            if start != -1 and end != -1 and end > start:
                json_candidate = bad_text[start:end+1]
                parsed = self._try_parse_json_array(json_candidate)
                if parsed is not None:
                    return parsed
            
            logger.warning(f"Failed to extract JSON array from: {bad_text[:100]}...")
            return []
        except Exception as e:
            logger.warning(f"JSON extraction failed: {e}")
            return []

    def _parse_boolean(self, text: str) -> Optional[bool]:
        if not text:
            return None

        normalized = text.strip().lower()
        if normalized in {"true", "false"}:
            return normalized == "true"

        try:
            parsed = json.loads(normalized)
            if isinstance(parsed, bool):
                return parsed
            if isinstance(parsed, str):
                lower_str = parsed.strip().lower()
                if lower_str in {"true", "false"}:
                    return lower_str == "true"
            if isinstance(parsed, dict):
                for value in parsed.values():
                    if isinstance(value, bool):
                        return value
                    if isinstance(value, str):
                        lower_val = value.strip().lower()
                        if lower_val in {"true", "false"}:
                            return lower_val == "true"
        except Exception:
            pass

        if "true" in normalized and "false" not in normalized:
            return True
        if "false" in normalized and "true" not in normalized:
            return False

        return None

    def _should_generate_followups(
        self,
        chat_history: List[Dict[str, Any]],
        latest_user_query: str,
        latest_answer: str,
        session_summary: Optional[str],
        latest_sources: Optional[List[Dict[str, Any]]]
    ) -> bool:
        try:
            prompt_parts = [
                "You are a gatekeeper that decides if follow-up questions should be generated.",
                "",
                "CRITICAL: Reply with ONLY 'true' or 'false' (lowercase, no punctuation, no extra text, no explanations).",
                "",
                "DECISION LOGIC:",
                "",
                "Reply 'false' ONLY if:",
                "- The assistant explicitly stated it lacks information (e.g., 'I don't know', 'I don't have enough information', 'I cannot answer')",
                "- The answer is an error message or system failure",
                "- The response is empty or meaningless",
                "",
                "Reply 'true' for ALL OTHER CASES, including:",
                "- The assistant provided a meaningful, informative answer",
                "- The answer contains actual information or insights (even if brief)",
                "- Any response that satisfies the user's question",
                "- Any normal conversation or informative response",
                "",
                "IMPORTANT: Default to 'true' unless the response clearly failed or lacks information.",
                "",
                "USER QUERY:",
                latest_user_query,
                "",
                "ASSISTANT RESPONSE:",
                latest_answer,
                "",
                "Your decision (true or false):"
            ]

            prompt = "\n".join(prompt_parts)

            result = self.base.agent.run(prompt, stream=False)
            decision_text = getattr(result, "content", str(result))
            decision = self._parse_boolean(decision_text)
            if decision is None:
                logger.warning(
                    "FollowUpSuggestionAgent: Gatekeeper produced unrecognized output '%s'; defaulting to true.",
                    decision_text
                )
                return True

            return decision
        except Exception as e:
            logger.warning(f"FollowUpSuggestionAgent: Gatekeeper evaluation failed: {e}")
            return True

    def generate(
        self,
        chat_history: List[Dict[str, Any]],
        latest_user_query: str,
        latest_answer: str,
        session_summary: Optional[str] = None,
        latest_sources: Optional[List[Dict[str, Any]]] = None
    ) -> List[str]:
        try:
            # Use session summary provided by router (no duplicate summary creation)
            if session_summary:
                logger.info(f"FollowUpAgent: Using provided session summary (length: {len(session_summary)})")
            else:
                logger.info("FollowUpAgent: No session summary provided, proceeding without summary")

            rules = self._common_rules()

            # Limit to top 3 sources to reduce token usage (or all if less than 3)
            top_sources = (latest_sources or [])[:3]
            sources_json = json.dumps(top_sources, ensure_ascii=False)
            
            if latest_sources:
                logger.info(f"FollowUpAgent: Using top {len(top_sources)} sources out of {len(latest_sources)} for follow-up generation")

            should_generate = self._should_generate_followups(
                chat_history,
                latest_user_query,
                latest_answer,
                session_summary,
                latest_sources,
            )

            if not should_generate:
                logger.info("FollowUpAgent: Gatekeeper returned false; no follow-up suggestions generated.")
                return []

            if session_summary:
                prompt = (
                    "Generate follow-up questions.\n"
                    f"Session summary:\n{session_summary}\n\n"
                    f"Latest user query:\n{latest_user_query}\n\n"
                    f"Assistant's latest answer:\n{latest_answer}"
                    f"\nSupporting sources (JSON array):\n{sources_json}\n"
                    f"{rules}"
                    "If the latest answer did not satisfy the question or lacked information (e.g., it said \"I didn't get enough information\", \"I don't know\", \"I'm not sure\", or similar), you MUST respond with [].\n"
                    "If you cannot confidently point to a specific source that answers a proposed follow-up, respond with [].\n"
                    f"\nExample correct output: [\"What about cost?\", \"How to implement?\"]\n"
                    f"Example WRONG output: ```json[\"What about cost?\"]``` (DO NOT use markdown!)"
                )
            else:
                trimmed = chat_history[-10:] if chat_history else []
                history_str = json.dumps(trimmed, ensure_ascii=False)
                prompt = (
                    "Generate follow-up questions.\n"
                    f"Chat history (JSON array):\n{history_str}\n\n"
                    f"Latest user query:\n{latest_user_query}\n\n"
                    f"Assistant's latest answer:\n{latest_answer}"
                    f"\nSupporting sources (JSON array):\n{sources_json}\n"
                    f"{rules}"
                    "If the latest answer did not satisfy the question or lacked information (e.g., it said \"I didn't get enough information\", \"I don't know\", \"I'm not sure\", or similar), you MUST respond with [].\n"
                    "If you cannot confidently point to a specific source that answers a proposed follow-up, respond with [].\n"
                    f"\nExample correct output: [\"What about cost?\", \"How to implement?\"]\n"
                    f"Example WRONG output: ```json[\"What about cost?\"]``` (DO NOT use markdown!)"
                )

            # Reuse the SAME agent/model instance for the call
            result = self.base.agent.run(prompt, stream=False)
            raw = getattr(result, "content", str(result))

            # Enhanced JSON parsing with fallback for non-pure JSON responses
            suggestions = []
            
            # First, try to parse as pure JSON
            parsed = self._try_parse_json_array(raw)
            if parsed is not None:
                suggestions = parsed
            else:
                # Fallback: Extract JSON array if model added extra text
                logger.warning("FollowUpSuggestionAgent: invalid JSON; attempting fallback extraction.")
                suggestions = self._extract_json_fallback(raw)
            
            # Filter and validate suggestions
            valid_suggestions = []
            for suggestion in suggestions:
                if suggestion and suggestion.endswith('?') and len(suggestion.split()) <= 15:
                    valid_suggestions.append(suggestion)
            
            return valid_suggestions[:3]    

        except Exception as e:
            logger.warning(f"FollowUpSuggestionAgent failed: {e}")
            return []