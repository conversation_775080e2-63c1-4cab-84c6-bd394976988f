"""
Image Processing Utility

Provides validation, sanitization, and encoding for image uploads in Image Spaces.
Implements security measures including EXIF stripping, size validation, and format conversion.
"""

import io
import logging
import os
import sys
from PIL import Image, ImageOps
from typing import Tuple, Optional, Dict, Any
from ..config import (MAX_IMAGE_UPLOAD_MB, IMAGE_MAX_LONG_SIDE_PX,
                      ALLOWED_IMAGE_TYPES)

logger = logging.getLogger(__name__)

# Try to fix python-magic DLL path issue on Windows
if sys.platform == 'win32':
    try:
        # Get the magic module directory and add libmagic subdirectory to PATH
        import site
        magic_dir = None
        for path in site.getsitepackages():
            candidate = os.path.join(path, 'magic', 'libmagic')
            if os.path.exists(candidate):
                magic_dir = candidate
                break

        if magic_dir and magic_dir not in os.environ.get('PATH', ''):
            os.environ['PATH'] = magic_dir + os.pathsep + os.environ.get('PATH', '')
            logger.debug(f"Added magic DLL directory to PATH: {magic_dir}")
    except Exception as e:
        logger.debug(f"Failed to set magic DLL path: {e}")

try:
    import magic
    MAGIC_AVAILABLE = True
    logger.info("python-magic loaded successfully")
except ImportError as e:
    MAGIC_AVAILABLE = False
    logger.warning(f"python-magic not available: {e}; will use PIL for MIME detection only")

# Enable HEIC/HEIF decoding support for Pillow
try:
    import pillow_heif  # type: ignore
    try:
        pillow_heif.register_heif_opener()  # Ensure opener is registered
    except Exception:
        # Older versions auto-register on import
        pass
    logger.debug("pillow-heif registered successfully for HEIC/HEIF decoding")
except Exception as e:
    logger.warning(
        f"pillow-heif not available; HEIC/HEIF may not be supported: {e}")


class ImageProcessingError(Exception):
    """Base exception for image processing errors"""
    pass


class ImageValidationError(ImageProcessingError):
    """Raised when image validation fails"""
    pass


class ImageSanitizationError(ImageProcessingError):
    """Raised when image sanitization fails"""
    pass


def validate_image_type(content_type: str,
                        file_bytes: bytes) -> Tuple[bool, str]:
    """
    Validate image type using both MIME type and magic bytes.
    
    Args:
        content_type: MIME type from upload
        file_bytes: Image file bytes
        
    Returns:
        Tuple of (is_valid, detected_type)
        
    Raises:
        ImageValidationError: If validation fails
    """
    # Accept 'application/octet-stream' as unknown; rely on magic bytes detection
    normalized = (content_type or '').lower()
    if normalized != 'application/octet-stream':
        # Strict header check for declared image types
        if normalized not in ALLOWED_IMAGE_TYPES:
            raise ImageValidationError(
                f"Invalid image type: {content_type}. "
                f"Allowed types: {', '.join(ALLOWED_IMAGE_TYPES.keys())}")

    # Verify with magic bytes
    try:
        mime = magic.from_buffer(file_bytes, mime=True)
        mime_l = (mime or '').lower()

        # Preferred: allowlisted MIME
        if mime_l in ALLOWED_IMAGE_TYPES:
            return True, ALLOWED_IMAGE_TYPES[mime_l]

        # Heuristic: vendor-specific HEIC/HEIF
        if 'heic' in mime_l:
            return True, 'heic'
        if 'heif' in mime_l:
            return True, 'heif'

        # If magic is inconclusive (e.g., application/octet-stream), try Pillow detection
        if mime_l == 'application/octet-stream' or not mime_l:
            try:
                img = Image.open(io.BytesIO(file_bytes))
                fmt = (img.format or '').upper()
                fmt_map = {
                    'JPEG': 'jpg',
                    'PNG': 'png',
                    'WEBP': 'webp',
                    'HEIC': 'heic',
                    'HEIF': 'heif',
                }
                if fmt in fmt_map:
                    return True, fmt_map[fmt]
            except Exception:
                pass

        # Could not determine a supported image type
        raise ImageValidationError(
            f"Unsupported image type detected from content: {mime_l or 'unknown'}"
        )
    except ImageValidationError:
        raise
    except Exception as e:
        raise ImageValidationError(
            f"Failed to verify image with magic bytes: {e}")


def sanitize_image(
        file_bytes: bytes,
        max_size_mb: Optional[int] = None,
        max_long_side: Optional[int] = None,
        strip_exif: bool = True,
        reject_animated: bool = True) -> Tuple[bytes, Dict[str, Any]]:
    """
    Sanitize and process an image for safe storage.
    
    This function:
    1. Validates image can be opened
    2. Handles EXIF orientation
    3. Strips EXIF data for privacy
    4. Rejects animated images
    5. Resizes to maximum dimensions
    6. Converts to WebP format
    
    Args:
        file_bytes: Raw image bytes
        max_size_mb: Maximum file size in MB
        max_long_side: Maximum long side dimension in pixels
        strip_exif: Whether to strip EXIF data
        reject_animated: Whether to reject animated images
        
    Returns:
        Tuple of (processed_bytes, metadata_dict)
        
    Raises:
        ImageValidationError: If image fails validation
        ImageSanitizationError: If sanitization fails
    """
    # Apply defaults
    max_size_mb = max_size_mb or MAX_IMAGE_UPLOAD_MB
    max_long_side = max_long_side or IMAGE_MAX_LONG_SIDE_PX

    # 1. Size validation
    size_mb = len(file_bytes) / (1024 * 1024)
    if size_mb > max_size_mb:
        raise ImageValidationError(
            f"Image too large: {size_mb:.2f}MB exceeds limit of {max_size_mb}MB"
        )

    try:
        # 2. Open and verify image
        image = Image.open(io.BytesIO(file_bytes))
        image.verify()

        # Re-open after verify (verify closes the image)
        image = Image.open(io.BytesIO(file_bytes))

        # Store original format and dimensions
        original_format = image.format
        original_size = image.size

        # 3. Check for decompression bombs
        # Pillow automatically protects against this, but we can check explicitly
        try:
            image.load()
        except (Image.DecompressionBombError,
                Image.DecompressionBombWarning) as e:
            raise ImageValidationError(
                f"Image is too large when decompressed: {e}")

        # 4. Reject animated images
        if reject_animated:
            try:
                n_frames = getattr(image, 'n_frames', 1)
                if n_frames > 1:
                    raise ImageValidationError(
                        f"Animated images are not supported (image has {n_frames} frames)"
                    )
            except AttributeError:
                # Image format doesn't support frames, which is fine
                pass

        # 5. Handle EXIF orientation and strip EXIF
        if strip_exif:
            # Apply EXIF orientation to image data, then strip EXIF
            image = ImageOps.exif_transpose(image)

        # 6. Convert to RGB if necessary (handles RGBA, P, L modes)
        if image.mode in ('RGBA', 'LA'):
            # Create white background for images with transparency
            background = Image.new('RGB', image.size, (255, 255, 255))
            background.paste(
                image,
                mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')

        # 7. Resize if needed
        needs_resize = max(image.size) > max_long_side
        if needs_resize:
            # Calculate new dimensions maintaining aspect ratio
            width, height = image.size
            if width > height:
                new_width = max_long_side
                new_height = int(height * (max_long_side / width))
            else:
                new_height = max_long_side
                new_width = int(width * (max_long_side / height))

            image = image.resize((new_width, new_height),
                                 Image.Resampling.LANCZOS)
            logger.info(f"Resized image from {original_size} to {image.size}")

        # 8. Encode to WebP with optimization
        output = io.BytesIO()
        try:
            # Try WebP first (best compression)
            image.save(output, format='WEBP', quality=85, method=6)
            output_format = 'webp'
            content_type = 'image/webp'
        except Exception as webp_error:
            logger.warning(
                f"WebP encoding failed: {webp_error}, falling back to JPEG")
            output = io.BytesIO()
            # Fallback to JPEG
            image.save(output, format='JPEG', quality=90, optimize=True)
            output_format = 'jpg'
            content_type = 'image/jpeg'

        processed_bytes = output.getvalue()

        # Build metadata
        metadata = {
            'original_format': original_format,
            'original_size': original_size,
            'original_size_mb': size_mb,
            'processed_format': output_format,
            'processed_size': image.size,
            'processed_size_mb': len(processed_bytes) / (1024 * 1024),
            'content_type': content_type,
            'resized': needs_resize
        }

        logger.info(
            f"Image processed: {original_format} ({original_size}) -> "
            f"{output_format} ({image.size}), "
            f"{size_mb:.2f}MB -> {metadata['processed_size_mb']:.2f}MB")

        return processed_bytes, metadata

    except ImageValidationError:
        # Re-raise validation errors
        raise
    except Exception as e:
        # Wrap other errors as sanitization errors
        raise ImageSanitizationError(f"Failed to sanitize image: {e}")


def assert_upload_allowed(space: 'UserSpace', content_type: str,
                          file_bytes: bytes) -> None:
    """
    Assert that an upload is allowed for the given space type.
    
    Args:
        space: UserSpace model instance
        content_type: MIME type of the upload
        file_bytes: File content bytes
        
    Raises:
        ImageValidationError: If upload is not allowed
    """
    from ..models.user_space import PlaygroundType

    ct_lower = (content_type or '').lower()
    is_image = (ct_lower
                in ALLOWED_IMAGE_TYPES) or (ct_lower
                                            == 'application/octet-stream')
    is_image_space = space.playground_type == PlaygroundType.images.value

    if is_image_space and not is_image:
        raise ImageValidationError(f"Image spaces only accept image uploads. "
                                   f"Received: {content_type}")

    if not is_image_space and is_image:
        raise ImageValidationError(
            f"Document spaces do not accept image uploads. "
            f"Received: {content_type}")

    # If it's an image upload to an image space, validate it
    if is_image and is_image_space:
        # Validate by magic bytes; tolerate octet-stream headers for images
        validate_image_type(content_type, file_bytes)
