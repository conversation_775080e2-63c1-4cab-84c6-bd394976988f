# Image Embedding File Structure

## Complete File Organization

```
WORKPLACE_SLM/
│
├── Backend/
│   └── app/
│       └── utils/
│           └── image_processing.py          [MODIFIED] ✓
│               - Image validation & sanitization
│               - EXIF stripping, format conversion
│               - Windows python-magic DLL fix
│               - HEIC/HEIF support
│
├── Data_Ingestion_Service/
│   ├── pipeline.py                          [MODIFIED] ✓
│   │   - Added ImageEmbeddingClient initialization
│   │   - Image extraction via PyMuPDF
│   │   - Image document creation for Qdrant
│   │   - Qdrant collection with 'image' vector
│   │
│   └── image_embedding_client.py            [NEW] ✓
│       - SigLIP-2 HTTP client for ingestion
│       - Methods: embed_images(), caption_images(), embed_text()
│       - Health check and retry logic
│
├── Data_Retriever_Service/
│   ├── config.py                            [MODIFIED] ✓
│   │   - Added image_embedding_service_url parameter
│   │   - Default: http://**************:8019
│   │
│   ├── agent_pipeline.py                    [MODIFIED] ✓
│   │   - ImageRetriever initialization in _setup_components()
│   │   - Graceful failure handling
│   │
│   ├── image_embedding_client.py            [NEW] ✓
│   │   - Lightweight SigLIP-2 client for retrieval
│   │   - Method: embed_text() for queries
│   │
│   └── image_retrieval.py                   [NEW] ✓
│       - Complete image retrieval system
│       - Methods: retrieve_images_by_text(), retrieve_images_with_base64()
│       - PostgreSQL integration for base64 data
│
└── IMAGE_EMBEDDING_INTEGRATION_SUMMARY.md   [NEW] ✓
    └── IMAGE_EMBEDDING_FILE_STRUCTURE.md    [NEW] ✓
```

## Data Flow Diagram

### 1. Document Ingestion (Data_Ingestion_Service)

```
┌─────────────────┐
│  Document PDF   │
│  DOC, DOCX, etc │
└────────┬────────┘
         │
         ▼
┌─────────────────────────────────────────────────────────────┐
│  Data_Ingestion_Service/pipeline.py                         │
│                                                              │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  PyMuPDF: extract_images_with_pymupdf()               │ │
│  │  - Extract images with metadata (page, bbox, size)    │ │
│  │  - Filter small images (<100x100)                     │ │
│  └───────────┬───────────────────────────────────────────┘ │
│              │                                              │
│              ▼                                              │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  image_embedding_client.py                            │ │
│  │  - embed_images() → 1152-dim vectors                  │ │
│  │  - caption_images() → text descriptions               │ │
│  └───────────┬───────────────────────────────────────────┘ │
│              │                                              │
│              ▼                                              │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  create_image_documents()                             │ │
│  │  - Create Qdrant points with 'image' vector           │ │
│  │  - Payload: metadata + caption                        │ │
│  └───────────┬───────────────────────────────────────────┘ │
│              │                                              │
└──────────────┼──────────────────────────────────────────────┘
               │
      ┌────────┴────────┐
      │                 │
      ▼                 ▼
┌──────────────┐  ┌──────────────┐
│   Qdrant     │  │  PostgreSQL  │
│              │  │              │
│ Collection:  │  │  Table:      │
│  - dense     │  │  raw_docs    │
│  - sparse    │  │              │
│  - image ✓   │  │  - id        │
│              │  │  - base64 ✓  │
│  Payload:    │  │  - caption ✓ │
│  - caption   │  │  - metadata  │
│  - metadata  │  └──────────────┘
└──────────────┘
```

### 2. Image Retrieval (Data_Retriever_Service)

```
┌──────────────────┐
│  User Text Query │
│  "show me charts"│
└────────┬─────────┘
         │
         ▼
┌─────────────────────────────────────────────────────────────┐
│  Data_Retriever_Service/agent_pipeline.py                   │
│                                                              │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  IntelligentRAGAgent._setup_components()              │ │
│  │  - Initialize ImageRetriever                          │ │
│  └───────────┬───────────────────────────────────────────┘ │
│              │                                              │
└──────────────┼──────────────────────────────────────────────┘
               │
               ▼
┌─────────────────────────────────────────────────────────────┐
│  Data_Retriever_Service/image_retrieval.py                  │
│                                                              │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  retrieve_images_by_text()                            │ │
│  │  1. Call image_embedding_client.embed_text()          │ │
│  │  2. Search Qdrant with 'image' vector                 │ │
│  │  3. Filter: type='image'                              │ │
│  │  4. Return top-k results                              │ │
│  └───────────┬───────────────────────────────────────────┘ │
│              │                                              │
│              ▼                                              │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  retrieve_images_with_base64() [optional]             │ │
│  │  - Fetch base64 from PostgreSQL                       │ │
│  │  - Method: fetch_image_from_pg()                      │ │
│  └───────────┬───────────────────────────────────────────┘ │
│              │                                              │
└──────────────┼──────────────────────────────────────────────┘
               │
               ▼
      ┌────────────────┐
      │  Search Results│
      │                │
      │  - Image IDs   │
      │  - Scores      │
      │  - Captions    │
      │  - Metadata    │
      │  - Base64      │
      └────────────────┘
```

## Service Dependencies

### SigLIP-2 Image Embedding Service

```
Service URL: http://**************:8019

Endpoints:
  - GET  /health              → Service health check
  - POST /embed               → Generate image embeddings
  - POST /caption             → Generate image captions
  - POST /embed-text          → Generate text embeddings
  - GET  /model-info          → Model information

Model: SigLIP-2
Embedding Dimension: 1152
Distance Metric: COSINE
```

## Integration Points Summary

### Data_Ingestion_Service Integration

**File**: `pipeline.py`
**Method**: `__init__()` - Line 212-215
```python
from image_embedding_client import ImageEmbeddingClient
self.image_embedding_client = ImageEmbeddingClient(
    service_url="http://**************:8019"
)
```

**File**: `pipeline.py`
**Method**: `process_file()` - Lines 1574-1604
```python
# Process images with SigLIP-2 service
if images:
    images_base64 = [img['base64'] for img in images]
    embeddings_result = self.image_embedding_client.embed_images(images_base64)
    captions_result = self.image_embedding_client.caption_images(images_base64)
    image_points = self.create_image_documents(images, embeddings, captions, source)
    self.add_image_documents_to_qdrant(image_points, collection_name)
```

**File**: `pipeline.py`
**Method**: `setup_qdrant_collection()` - Lines 1104-1150
```python
vectors_config={
    "dense": VectorParams(size=768, distance=Distance.DOT),
    "image": VectorParams(size=1152, distance=Distance.COSINE)  # NEW
}
```

### Data_Retriever_Service Integration

**File**: `config.py`
**Class**: `RetrieverConfig` - Line 39
```python
image_embedding_service_url: str = "http://**************:8019"
```

**File**: `agent_pipeline.py`
**Method**: `_setup_components()` - Lines 127-138
```python
from image_retrieval import ImageRetriever
self.image_retriever = ImageRetriever(
    image_embedding_service_url=self.config.image_embedding_service_url,
    qdrant_client=self.qdrant_client,
    database_url=self.config.database_url
)
```

## Testing Checklist

- [x] Files moved to correct locations
- [x] Imports added to pipeline.py
- [x] ImageEmbeddingClient initialized in ingestion
- [x] ImageRetriever initialized in retrieval
- [x] Config updated with service URL
- [x] Backend image_processing.py updated
- [x] Root directory cleaned up
- [ ] Test SigLIP-2 service health check
- [ ] Test image extraction from PDF
- [ ] Test image embedding generation
- [ ] Test image caption generation
- [ ] Test image storage in Qdrant
- [ ] Test image retrieval by text query
- [ ] Test base64 image retrieval from PostgreSQL
- [ ] End-to-end test with real documents

## Performance Considerations

**Image Extraction**:
- PyMuPDF is fast and memory-efficient
- Filters out small images (<100x100) automatically

**Embedding Generation**:
- Batch processing supported
- Timeout: 60 seconds
- Max retries: 3

**Storage**:
- Qdrant: Fast vector search with HNSW index
- PostgreSQL: Raw image data separate from vectors
- Dual storage minimizes Qdrant payload size

**Retrieval**:
- Cross-modal search: text → image embeddings
- Filter by type='image' for efficiency
- Optional base64 fetching (only when needed)

## Error Handling

All components implement graceful error handling:

1. **Image Embedding Client**: Retries with exponential backoff
2. **Image Retriever**: Continues without image search if unavailable
3. **Pipeline**: Logs errors and continues processing
4. **Backend**: Validates images before processing

