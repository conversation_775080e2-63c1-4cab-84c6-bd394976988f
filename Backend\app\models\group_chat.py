from sqlalchemy import Column, Integer, String, Text, DateTime, Foreign<PERSON>ey, Boolean, Index, Enum
from sqlalchemy.orm import relationship
from datetime import datetime

from ..database import Base
from enum import Enum as PyEnum

class MemberRole(PyEnum):
    admin = "admin"
    member = "member"

class Group(Base):
    __tablename__ = "chat_groups"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    creator_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    space_id = Column(Integer, ForeignKey('user_spaces.id'), nullable=True)  # Link to shared space
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)

    # Relationships
    creator = relationship("User", foreign_keys=[creator_id])
    space = relationship("UserSpace", foreign_keys=[space_id])
    members = relationship("GroupMember", back_populates="group", cascade="all, delete-orphan")
    messages = relationship("GroupMessage", back_populates="group", cascade="all, delete-orphan")

    __table_args__ = (
        Index('idx_group_updated', updated_at.desc()),
        Index('idx_group_space_id', space_id),
    )

    def to_dict(self, include_members: bool = False) -> dict:
        # Check if messages is already loaded to avoid lazy loading
        if 'messages' in self.__dict__ and self.messages:
            last_msg = self.messages[-1].to_dict()
        else:
            last_msg = None
        
        data = {
            'id': self.id,
            'name': self.name,
            'creator_id': self.creator_id,
            'space_id': self.space_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_active': self.is_active,
            'last_message': last_msg
        }
        if include_members:
            # Check if members is already loaded to avoid lazy loading
            if 'members' in self.__dict__ and self.members:
                data['members'] = [m.to_dict() for m in self.members]
            else:
                data['members'] = []
        return data

class GroupMember(Base):
    __tablename__ = "chat_group_members"

    id = Column(Integer, primary_key=True, index=True)
    group_id = Column(Integer, ForeignKey('chat_groups.id'), nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    role = Column(Enum(MemberRole), default=MemberRole.member)
    joined_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    group = relationship("Group", back_populates="members")
    user = relationship("User")

    __table_args__ = (
        Index('idx_group_member', group_id, user_id, unique=True),
    )

    def to_dict(self) -> dict:
        return {
            'id': self.id,
            'group_id': self.group_id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            # No department field in User model
            'role': self.role.value,
            'joined_at': self.joined_at.isoformat() if self.joined_at else None
        }

class GroupMessage(Base):
    __tablename__ = "chat_group_messages"

    id = Column(Integer, primary_key=True, index=True)
    group_id = Column(Integer, ForeignKey('chat_groups.id'), nullable=False)
    sender_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    content = Column(Text, nullable=False)
    message_type = Column(String(20), default='text')  # 'text', 'image', 'file'
    created_at = Column(DateTime, default=datetime.utcnow)
    is_read = Column(Boolean, default=False)
    is_edited = Column(Boolean, default=False)
    edited_at = Column(DateTime, nullable=True)

    # Relationships
    group = relationship("Group", back_populates="messages")
    sender = relationship("User", foreign_keys=[sender_id])

    __table_args__ = (
        Index('idx_group_msg_group', group_id),
        Index('idx_group_msg_created', created_at.desc()),
    )

    def to_dict(self) -> dict:
        return {
            'id': self.id,
            'group_id': self.group_id,
            'sender_id': self.sender_id,
            'sender_username': self.sender.username if self.sender else None,
            # No department field in User model
            'content': self.content,
            'message_type': self.message_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'is_read': self.is_read,
            'is_edited': self.is_edited,
            'edited_at': self.edited_at.isoformat() if self.edited_at else None
        } 