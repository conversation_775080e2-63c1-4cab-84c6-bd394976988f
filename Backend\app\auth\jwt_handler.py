from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any, Tuple
from jose import JWTError, jwt
from fastapi import HTT<PERSON>Ex<PERSON>, status
from ..config import SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> bool:
    """Verify if token is valid"""
    try:
        jwt.decode(token, SECRET_KEY, algorithms=[ALG<PERSON><PERSON>HM])
        return True
    except JWTError:
        return False

def get_user_from_token(token: str) -> Dict[str, Any]:
    """Extract user data from JWT token for WebSocket authentication"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        user_id: int = payload.get("user_id")
        role: str = payload.get("role")
        industry: str = payload.get("industry")
        
        if username is None or user_id is None:
            raise JWTError("Invalid token payload")
            
        return {
            "username": username,
            "user_id": user_id,
            "role": role,
            "industry": industry
        }
    except jwt.ExpiredSignatureError:
        raise JWTError("Token has expired")
    except JWTError:
        raise JWTError("Invalid token") 

def get_token_payload(token: str) -> Dict[str, Any]:
    """Return full JWT payload (raises on invalid/expired)."""
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    return payload

def get_token_expiry_utc(token: str) -> datetime:
    """Return token expiry as aware UTC datetime (raises on invalid/expired)."""
    payload = get_token_payload(token)
    exp = payload.get("exp")
    if exp is None:
        raise JWTError("Token missing exp claim")
    return datetime.fromtimestamp(exp, tz=timezone.utc)

def get_token_seconds_to_expiry(token: str) -> int:
    """Return seconds until token expiry (0 if already expired)."""
    try:
        exp_dt = get_token_expiry_utc(token)
    except JWTError:
        return 0
    now = datetime.now(timezone.utc)
    delta = (exp_dt - now).total_seconds()
    return max(0, int(delta))