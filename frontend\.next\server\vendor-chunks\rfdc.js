"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rfdc";
exports.ids = ["vendor-chunks/rfdc"];
exports.modules = {

/***/ "(ssr)/./node_modules/rfdc/default.js":
/*!**************************************!*\
  !*** ./node_modules/rfdc/default.js ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/rfdc/index.js\")()\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmZkYy9kZWZhdWx0LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGlCQUFpQixtQkFBTyxDQUFDLHNEQUFZIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmZkYy9kZWZhdWx0LmpzPzg1N2IiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9pbmRleC5qcycpKClcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rfdc/default.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rfdc/index.js":
/*!************************************!*\
  !*** ./node_modules/rfdc/index.js ***!
  \************************************/
/***/ ((module) => {

eval("\nmodule.exports = rfdc\n\nfunction copyBuffer (cur) {\n  if (cur instanceof Buffer) {\n    return Buffer.from(cur)\n  }\n\n  return new cur.constructor(cur.buffer.slice(), cur.byteOffset, cur.length)\n}\n\nfunction rfdc (opts) {\n  opts = opts || {}\n  if (opts.circles) return rfdcCircles(opts)\n\n  const constructorHandlers = new Map()\n  constructorHandlers.set(Date, (o) => new Date(o))\n  constructorHandlers.set(Map, (o, fn) => new Map(cloneArray(Array.from(o), fn)))\n  constructorHandlers.set(Set, (o, fn) => new Set(cloneArray(Array.from(o), fn)))\n  if (opts.constructorHandlers) {\n    for (const handler of opts.constructorHandlers) {\n      constructorHandlers.set(handler[0], handler[1])\n    }\n  }\n\n  let handler = null\n\n  return opts.proto ? cloneProto : clone\n\n  function cloneArray (a, fn) {\n    const keys = Object.keys(a)\n    const a2 = new Array(keys.length)\n    for (let i = 0; i < keys.length; i++) {\n      const k = keys[i]\n      const cur = a[k]\n      if (typeof cur !== 'object' || cur === null) {\n        a2[k] = cur\n      } else if (cur.constructor !== Object && (handler = constructorHandlers.get(cur.constructor))) {\n        a2[k] = handler(cur, fn)\n      } else if (ArrayBuffer.isView(cur)) {\n        a2[k] = copyBuffer(cur)\n      } else {\n        a2[k] = fn(cur)\n      }\n    }\n    return a2\n  }\n\n  function clone (o) {\n    if (typeof o !== 'object' || o === null) return o\n    if (Array.isArray(o)) return cloneArray(o, clone)\n    if (o.constructor !== Object && (handler = constructorHandlers.get(o.constructor))) {\n      return handler(o, clone)\n    }\n    const o2 = {}\n    for (const k in o) {\n      if (Object.hasOwnProperty.call(o, k) === false) continue\n      const cur = o[k]\n      if (typeof cur !== 'object' || cur === null) {\n        o2[k] = cur\n      } else if (cur.constructor !== Object && (handler = constructorHandlers.get(cur.constructor))) {\n        o2[k] = handler(cur, clone)\n      } else if (ArrayBuffer.isView(cur)) {\n        o2[k] = copyBuffer(cur)\n      } else {\n        o2[k] = clone(cur)\n      }\n    }\n    return o2\n  }\n\n  function cloneProto (o) {\n    if (typeof o !== 'object' || o === null) return o\n    if (Array.isArray(o)) return cloneArray(o, cloneProto)\n    if (o.constructor !== Object && (handler = constructorHandlers.get(o.constructor))) {\n      return handler(o, cloneProto)\n    }\n    const o2 = {}\n    for (const k in o) {\n      const cur = o[k]\n      if (typeof cur !== 'object' || cur === null) {\n        o2[k] = cur\n      } else if (cur.constructor !== Object && (handler = constructorHandlers.get(cur.constructor))) {\n        o2[k] = handler(cur, cloneProto)\n      } else if (ArrayBuffer.isView(cur)) {\n        o2[k] = copyBuffer(cur)\n      } else {\n        o2[k] = cloneProto(cur)\n      }\n    }\n    return o2\n  }\n}\n\nfunction rfdcCircles (opts) {\n  const refs = []\n  const refsNew = []\n\n  const constructorHandlers = new Map()\n  constructorHandlers.set(Date, (o) => new Date(o))\n  constructorHandlers.set(Map, (o, fn) => new Map(cloneArray(Array.from(o), fn)))\n  constructorHandlers.set(Set, (o, fn) => new Set(cloneArray(Array.from(o), fn)))\n  if (opts.constructorHandlers) {\n    for (const handler of opts.constructorHandlers) {\n      constructorHandlers.set(handler[0], handler[1])\n    }\n  }\n\n  let handler = null\n  return opts.proto ? cloneProto : clone\n\n  function cloneArray (a, fn) {\n    const keys = Object.keys(a)\n    const a2 = new Array(keys.length)\n    for (let i = 0; i < keys.length; i++) {\n      const k = keys[i]\n      const cur = a[k]\n      if (typeof cur !== 'object' || cur === null) {\n        a2[k] = cur\n      } else if (cur.constructor !== Object && (handler = constructorHandlers.get(cur.constructor))) {\n        a2[k] = handler(cur, fn)\n      } else if (ArrayBuffer.isView(cur)) {\n        a2[k] = copyBuffer(cur)\n      } else {\n        const index = refs.indexOf(cur)\n        if (index !== -1) {\n          a2[k] = refsNew[index]\n        } else {\n          a2[k] = fn(cur)\n        }\n      }\n    }\n    return a2\n  }\n\n  function clone (o) {\n    if (typeof o !== 'object' || o === null) return o\n    if (Array.isArray(o)) return cloneArray(o, clone)\n    if (o.constructor !== Object && (handler = constructorHandlers.get(o.constructor))) {\n      return handler(o, clone)\n    }\n    const o2 = {}\n    refs.push(o)\n    refsNew.push(o2)\n    for (const k in o) {\n      if (Object.hasOwnProperty.call(o, k) === false) continue\n      const cur = o[k]\n      if (typeof cur !== 'object' || cur === null) {\n        o2[k] = cur\n      } else if (cur.constructor !== Object && (handler = constructorHandlers.get(cur.constructor))) {\n        o2[k] = handler(cur, clone)\n      } else if (ArrayBuffer.isView(cur)) {\n        o2[k] = copyBuffer(cur)\n      } else {\n        const i = refs.indexOf(cur)\n        if (i !== -1) {\n          o2[k] = refsNew[i]\n        } else {\n          o2[k] = clone(cur)\n        }\n      }\n    }\n    refs.pop()\n    refsNew.pop()\n    return o2\n  }\n\n  function cloneProto (o) {\n    if (typeof o !== 'object' || o === null) return o\n    if (Array.isArray(o)) return cloneArray(o, cloneProto)\n    if (o.constructor !== Object && (handler = constructorHandlers.get(o.constructor))) {\n      return handler(o, cloneProto)\n    }\n    const o2 = {}\n    refs.push(o)\n    refsNew.push(o2)\n    for (const k in o) {\n      const cur = o[k]\n      if (typeof cur !== 'object' || cur === null) {\n        o2[k] = cur\n      } else if (cur.constructor !== Object && (handler = constructorHandlers.get(cur.constructor))) {\n        o2[k] = handler(cur, cloneProto)\n      } else if (ArrayBuffer.isView(cur)) {\n        o2[k] = copyBuffer(cur)\n      } else {\n        const i = refs.indexOf(cur)\n        if (i !== -1) {\n          o2[k] = refsNew[i]\n        } else {\n          o2[k] = cloneProto(cur)\n        }\n      }\n    }\n    refs.pop()\n    refsNew.pop()\n    return o2\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmZkYy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTtBQUNaOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsaUJBQWlCO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixpQkFBaUI7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZmRjL2luZGV4LmpzPzZiYWUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5tb2R1bGUuZXhwb3J0cyA9IHJmZGNcblxuZnVuY3Rpb24gY29weUJ1ZmZlciAoY3VyKSB7XG4gIGlmIChjdXIgaW5zdGFuY2VvZiBCdWZmZXIpIHtcbiAgICByZXR1cm4gQnVmZmVyLmZyb20oY3VyKVxuICB9XG5cbiAgcmV0dXJuIG5ldyBjdXIuY29uc3RydWN0b3IoY3VyLmJ1ZmZlci5zbGljZSgpLCBjdXIuYnl0ZU9mZnNldCwgY3VyLmxlbmd0aClcbn1cblxuZnVuY3Rpb24gcmZkYyAob3B0cykge1xuICBvcHRzID0gb3B0cyB8fCB7fVxuICBpZiAob3B0cy5jaXJjbGVzKSByZXR1cm4gcmZkY0NpcmNsZXMob3B0cylcblxuICBjb25zdCBjb25zdHJ1Y3RvckhhbmRsZXJzID0gbmV3IE1hcCgpXG4gIGNvbnN0cnVjdG9ySGFuZGxlcnMuc2V0KERhdGUsIChvKSA9PiBuZXcgRGF0ZShvKSlcbiAgY29uc3RydWN0b3JIYW5kbGVycy5zZXQoTWFwLCAobywgZm4pID0+IG5ldyBNYXAoY2xvbmVBcnJheShBcnJheS5mcm9tKG8pLCBmbikpKVxuICBjb25zdHJ1Y3RvckhhbmRsZXJzLnNldChTZXQsIChvLCBmbikgPT4gbmV3IFNldChjbG9uZUFycmF5KEFycmF5LmZyb20obyksIGZuKSkpXG4gIGlmIChvcHRzLmNvbnN0cnVjdG9ySGFuZGxlcnMpIHtcbiAgICBmb3IgKGNvbnN0IGhhbmRsZXIgb2Ygb3B0cy5jb25zdHJ1Y3RvckhhbmRsZXJzKSB7XG4gICAgICBjb25zdHJ1Y3RvckhhbmRsZXJzLnNldChoYW5kbGVyWzBdLCBoYW5kbGVyWzFdKVxuICAgIH1cbiAgfVxuXG4gIGxldCBoYW5kbGVyID0gbnVsbFxuXG4gIHJldHVybiBvcHRzLnByb3RvID8gY2xvbmVQcm90byA6IGNsb25lXG5cbiAgZnVuY3Rpb24gY2xvbmVBcnJheSAoYSwgZm4pIHtcbiAgICBjb25zdCBrZXlzID0gT2JqZWN0LmtleXMoYSlcbiAgICBjb25zdCBhMiA9IG5ldyBBcnJheShrZXlzLmxlbmd0aClcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGtleXMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IGsgPSBrZXlzW2ldXG4gICAgICBjb25zdCBjdXIgPSBhW2tdXG4gICAgICBpZiAodHlwZW9mIGN1ciAhPT0gJ29iamVjdCcgfHwgY3VyID09PSBudWxsKSB7XG4gICAgICAgIGEyW2tdID0gY3VyXG4gICAgICB9IGVsc2UgaWYgKGN1ci5jb25zdHJ1Y3RvciAhPT0gT2JqZWN0ICYmIChoYW5kbGVyID0gY29uc3RydWN0b3JIYW5kbGVycy5nZXQoY3VyLmNvbnN0cnVjdG9yKSkpIHtcbiAgICAgICAgYTJba10gPSBoYW5kbGVyKGN1ciwgZm4pXG4gICAgICB9IGVsc2UgaWYgKEFycmF5QnVmZmVyLmlzVmlldyhjdXIpKSB7XG4gICAgICAgIGEyW2tdID0gY29weUJ1ZmZlcihjdXIpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhMltrXSA9IGZuKGN1cilcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGEyXG4gIH1cblxuICBmdW5jdGlvbiBjbG9uZSAobykge1xuICAgIGlmICh0eXBlb2YgbyAhPT0gJ29iamVjdCcgfHwgbyA9PT0gbnVsbCkgcmV0dXJuIG9cbiAgICBpZiAoQXJyYXkuaXNBcnJheShvKSkgcmV0dXJuIGNsb25lQXJyYXkobywgY2xvbmUpXG4gICAgaWYgKG8uY29uc3RydWN0b3IgIT09IE9iamVjdCAmJiAoaGFuZGxlciA9IGNvbnN0cnVjdG9ySGFuZGxlcnMuZ2V0KG8uY29uc3RydWN0b3IpKSkge1xuICAgICAgcmV0dXJuIGhhbmRsZXIobywgY2xvbmUpXG4gICAgfVxuICAgIGNvbnN0IG8yID0ge31cbiAgICBmb3IgKGNvbnN0IGsgaW4gbykge1xuICAgICAgaWYgKE9iamVjdC5oYXNPd25Qcm9wZXJ0eS5jYWxsKG8sIGspID09PSBmYWxzZSkgY29udGludWVcbiAgICAgIGNvbnN0IGN1ciA9IG9ba11cbiAgICAgIGlmICh0eXBlb2YgY3VyICE9PSAnb2JqZWN0JyB8fCBjdXIgPT09IG51bGwpIHtcbiAgICAgICAgbzJba10gPSBjdXJcbiAgICAgIH0gZWxzZSBpZiAoY3VyLmNvbnN0cnVjdG9yICE9PSBPYmplY3QgJiYgKGhhbmRsZXIgPSBjb25zdHJ1Y3RvckhhbmRsZXJzLmdldChjdXIuY29uc3RydWN0b3IpKSkge1xuICAgICAgICBvMltrXSA9IGhhbmRsZXIoY3VyLCBjbG9uZSlcbiAgICAgIH0gZWxzZSBpZiAoQXJyYXlCdWZmZXIuaXNWaWV3KGN1cikpIHtcbiAgICAgICAgbzJba10gPSBjb3B5QnVmZmVyKGN1cilcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG8yW2tdID0gY2xvbmUoY3VyKVxuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gbzJcbiAgfVxuXG4gIGZ1bmN0aW9uIGNsb25lUHJvdG8gKG8pIHtcbiAgICBpZiAodHlwZW9mIG8gIT09ICdvYmplY3QnIHx8IG8gPT09IG51bGwpIHJldHVybiBvXG4gICAgaWYgKEFycmF5LmlzQXJyYXkobykpIHJldHVybiBjbG9uZUFycmF5KG8sIGNsb25lUHJvdG8pXG4gICAgaWYgKG8uY29uc3RydWN0b3IgIT09IE9iamVjdCAmJiAoaGFuZGxlciA9IGNvbnN0cnVjdG9ySGFuZGxlcnMuZ2V0KG8uY29uc3RydWN0b3IpKSkge1xuICAgICAgcmV0dXJuIGhhbmRsZXIobywgY2xvbmVQcm90bylcbiAgICB9XG4gICAgY29uc3QgbzIgPSB7fVxuICAgIGZvciAoY29uc3QgayBpbiBvKSB7XG4gICAgICBjb25zdCBjdXIgPSBvW2tdXG4gICAgICBpZiAodHlwZW9mIGN1ciAhPT0gJ29iamVjdCcgfHwgY3VyID09PSBudWxsKSB7XG4gICAgICAgIG8yW2tdID0gY3VyXG4gICAgICB9IGVsc2UgaWYgKGN1ci5jb25zdHJ1Y3RvciAhPT0gT2JqZWN0ICYmIChoYW5kbGVyID0gY29uc3RydWN0b3JIYW5kbGVycy5nZXQoY3VyLmNvbnN0cnVjdG9yKSkpIHtcbiAgICAgICAgbzJba10gPSBoYW5kbGVyKGN1ciwgY2xvbmVQcm90bylcbiAgICAgIH0gZWxzZSBpZiAoQXJyYXlCdWZmZXIuaXNWaWV3KGN1cikpIHtcbiAgICAgICAgbzJba10gPSBjb3B5QnVmZmVyKGN1cilcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG8yW2tdID0gY2xvbmVQcm90byhjdXIpXG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBvMlxuICB9XG59XG5cbmZ1bmN0aW9uIHJmZGNDaXJjbGVzIChvcHRzKSB7XG4gIGNvbnN0IHJlZnMgPSBbXVxuICBjb25zdCByZWZzTmV3ID0gW11cblxuICBjb25zdCBjb25zdHJ1Y3RvckhhbmRsZXJzID0gbmV3IE1hcCgpXG4gIGNvbnN0cnVjdG9ySGFuZGxlcnMuc2V0KERhdGUsIChvKSA9PiBuZXcgRGF0ZShvKSlcbiAgY29uc3RydWN0b3JIYW5kbGVycy5zZXQoTWFwLCAobywgZm4pID0+IG5ldyBNYXAoY2xvbmVBcnJheShBcnJheS5mcm9tKG8pLCBmbikpKVxuICBjb25zdHJ1Y3RvckhhbmRsZXJzLnNldChTZXQsIChvLCBmbikgPT4gbmV3IFNldChjbG9uZUFycmF5KEFycmF5LmZyb20obyksIGZuKSkpXG4gIGlmIChvcHRzLmNvbnN0cnVjdG9ySGFuZGxlcnMpIHtcbiAgICBmb3IgKGNvbnN0IGhhbmRsZXIgb2Ygb3B0cy5jb25zdHJ1Y3RvckhhbmRsZXJzKSB7XG4gICAgICBjb25zdHJ1Y3RvckhhbmRsZXJzLnNldChoYW5kbGVyWzBdLCBoYW5kbGVyWzFdKVxuICAgIH1cbiAgfVxuXG4gIGxldCBoYW5kbGVyID0gbnVsbFxuICByZXR1cm4gb3B0cy5wcm90byA/IGNsb25lUHJvdG8gOiBjbG9uZVxuXG4gIGZ1bmN0aW9uIGNsb25lQXJyYXkgKGEsIGZuKSB7XG4gICAgY29uc3Qga2V5cyA9IE9iamVjdC5rZXlzKGEpXG4gICAgY29uc3QgYTIgPSBuZXcgQXJyYXkoa2V5cy5sZW5ndGgpXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBrZXlzLmxlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBrID0ga2V5c1tpXVxuICAgICAgY29uc3QgY3VyID0gYVtrXVxuICAgICAgaWYgKHR5cGVvZiBjdXIgIT09ICdvYmplY3QnIHx8IGN1ciA9PT0gbnVsbCkge1xuICAgICAgICBhMltrXSA9IGN1clxuICAgICAgfSBlbHNlIGlmIChjdXIuY29uc3RydWN0b3IgIT09IE9iamVjdCAmJiAoaGFuZGxlciA9IGNvbnN0cnVjdG9ySGFuZGxlcnMuZ2V0KGN1ci5jb25zdHJ1Y3RvcikpKSB7XG4gICAgICAgIGEyW2tdID0gaGFuZGxlcihjdXIsIGZuKVxuICAgICAgfSBlbHNlIGlmIChBcnJheUJ1ZmZlci5pc1ZpZXcoY3VyKSkge1xuICAgICAgICBhMltrXSA9IGNvcHlCdWZmZXIoY3VyKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3QgaW5kZXggPSByZWZzLmluZGV4T2YoY3VyKVxuICAgICAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICAgICAgYTJba10gPSByZWZzTmV3W2luZGV4XVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGEyW2tdID0gZm4oY3VyKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBhMlxuICB9XG5cbiAgZnVuY3Rpb24gY2xvbmUgKG8pIHtcbiAgICBpZiAodHlwZW9mIG8gIT09ICdvYmplY3QnIHx8IG8gPT09IG51bGwpIHJldHVybiBvXG4gICAgaWYgKEFycmF5LmlzQXJyYXkobykpIHJldHVybiBjbG9uZUFycmF5KG8sIGNsb25lKVxuICAgIGlmIChvLmNvbnN0cnVjdG9yICE9PSBPYmplY3QgJiYgKGhhbmRsZXIgPSBjb25zdHJ1Y3RvckhhbmRsZXJzLmdldChvLmNvbnN0cnVjdG9yKSkpIHtcbiAgICAgIHJldHVybiBoYW5kbGVyKG8sIGNsb25lKVxuICAgIH1cbiAgICBjb25zdCBvMiA9IHt9XG4gICAgcmVmcy5wdXNoKG8pXG4gICAgcmVmc05ldy5wdXNoKG8yKVxuICAgIGZvciAoY29uc3QgayBpbiBvKSB7XG4gICAgICBpZiAoT2JqZWN0Lmhhc093blByb3BlcnR5LmNhbGwobywgaykgPT09IGZhbHNlKSBjb250aW51ZVxuICAgICAgY29uc3QgY3VyID0gb1trXVxuICAgICAgaWYgKHR5cGVvZiBjdXIgIT09ICdvYmplY3QnIHx8IGN1ciA9PT0gbnVsbCkge1xuICAgICAgICBvMltrXSA9IGN1clxuICAgICAgfSBlbHNlIGlmIChjdXIuY29uc3RydWN0b3IgIT09IE9iamVjdCAmJiAoaGFuZGxlciA9IGNvbnN0cnVjdG9ySGFuZGxlcnMuZ2V0KGN1ci5jb25zdHJ1Y3RvcikpKSB7XG4gICAgICAgIG8yW2tdID0gaGFuZGxlcihjdXIsIGNsb25lKVxuICAgICAgfSBlbHNlIGlmIChBcnJheUJ1ZmZlci5pc1ZpZXcoY3VyKSkge1xuICAgICAgICBvMltrXSA9IGNvcHlCdWZmZXIoY3VyKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3QgaSA9IHJlZnMuaW5kZXhPZihjdXIpXG4gICAgICAgIGlmIChpICE9PSAtMSkge1xuICAgICAgICAgIG8yW2tdID0gcmVmc05ld1tpXVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIG8yW2tdID0gY2xvbmUoY3VyKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIHJlZnMucG9wKClcbiAgICByZWZzTmV3LnBvcCgpXG4gICAgcmV0dXJuIG8yXG4gIH1cblxuICBmdW5jdGlvbiBjbG9uZVByb3RvIChvKSB7XG4gICAgaWYgKHR5cGVvZiBvICE9PSAnb2JqZWN0JyB8fCBvID09PSBudWxsKSByZXR1cm4gb1xuICAgIGlmIChBcnJheS5pc0FycmF5KG8pKSByZXR1cm4gY2xvbmVBcnJheShvLCBjbG9uZVByb3RvKVxuICAgIGlmIChvLmNvbnN0cnVjdG9yICE9PSBPYmplY3QgJiYgKGhhbmRsZXIgPSBjb25zdHJ1Y3RvckhhbmRsZXJzLmdldChvLmNvbnN0cnVjdG9yKSkpIHtcbiAgICAgIHJldHVybiBoYW5kbGVyKG8sIGNsb25lUHJvdG8pXG4gICAgfVxuICAgIGNvbnN0IG8yID0ge31cbiAgICByZWZzLnB1c2gobylcbiAgICByZWZzTmV3LnB1c2gobzIpXG4gICAgZm9yIChjb25zdCBrIGluIG8pIHtcbiAgICAgIGNvbnN0IGN1ciA9IG9ba11cbiAgICAgIGlmICh0eXBlb2YgY3VyICE9PSAnb2JqZWN0JyB8fCBjdXIgPT09IG51bGwpIHtcbiAgICAgICAgbzJba10gPSBjdXJcbiAgICAgIH0gZWxzZSBpZiAoY3VyLmNvbnN0cnVjdG9yICE9PSBPYmplY3QgJiYgKGhhbmRsZXIgPSBjb25zdHJ1Y3RvckhhbmRsZXJzLmdldChjdXIuY29uc3RydWN0b3IpKSkge1xuICAgICAgICBvMltrXSA9IGhhbmRsZXIoY3VyLCBjbG9uZVByb3RvKVxuICAgICAgfSBlbHNlIGlmIChBcnJheUJ1ZmZlci5pc1ZpZXcoY3VyKSkge1xuICAgICAgICBvMltrXSA9IGNvcHlCdWZmZXIoY3VyKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3QgaSA9IHJlZnMuaW5kZXhPZihjdXIpXG4gICAgICAgIGlmIChpICE9PSAtMSkge1xuICAgICAgICAgIG8yW2tdID0gcmVmc05ld1tpXVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIG8yW2tdID0gY2xvbmVQcm90byhjdXIpXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgcmVmcy5wb3AoKVxuICAgIHJlZnNOZXcucG9wKClcbiAgICByZXR1cm4gbzJcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rfdc/index.js\n");

/***/ })

};
;