import React, { useState, useEffect } from 'react';
import { useChat } from '@/contexts';
import ChatBox from './ChatBox';
import ChatInput from './ChatInput';
import ChatSidebar from './ChatSidebar';
export default function ChatPageContent() {
  const [chatSidebarOpen, setChatSidebarOpen] = useState(false);

  // Auto-open sidebar on desktop, close on mobile
  useEffect(() => {
    const updateSidebarVisibility = () => {
      const isDesktop = window.innerWidth >= 768;
      setChatSidebarOpen(isDesktop);
    };
    
    updateSidebarVisibility();
    window.addEventListener('resize', updateSidebarVisibility);
    return () => window.removeEventListener('resize', updateSidebarVisibility);
  }, []);
  
  const {
    selectedAgent,
    currentSessionId,
    startNewChat,
    switchToSession,
  } = useChat();

  return (
    <div className="flex h-full bg-gray-200">
      {/* Chat History Sidebar */}
      <ChatSidebar
        selectedAgent={selectedAgent}
        currentSessionId={currentSessionId}
        onSessionSelect={switchToSession}
        onNewChat={startNewChat}
        isOpen={chatSidebarOpen}
        onToggle={() => setChatSidebarOpen(!chatSidebarOpen)}
      />

      {/* Main Chat Area */}
      <div className={`
        flex-1 flex flex-col transition-all duration-300 ease-in-out relative
      `}>

        {/* Chat Messages Area */}
        <div className="flex-1 overflow-hidden">
          <ChatBox />
        </div>
        
        {/* Chat Input Area */}
        <div className="flex-shrink-0 border-t border-gray-200 flex justify-center">
          <div className="max-w-7xl mx-auto px-6 lg:px-8 w-full">
            <ChatInput />
          </div>
        </div>
        
        {/* Footer */}
        <div className="flex-shrink-0 bg-gray-200">
          <div className="max-w-7xl mx-auto px-3 lg:px-8 pb-2 sm:pb-4 flex justify-center items-center">
            <p className="text-gray-600 text-[11px] sm:text-xs md:text-sm text-center leading-snug flex items-start justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0 mt-0">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
              </svg>
              <span className="ml-1">Your data privacy and security is our top priority. All interactions are protected and confidential.</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
