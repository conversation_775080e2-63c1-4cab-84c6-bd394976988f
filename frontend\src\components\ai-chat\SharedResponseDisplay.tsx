'use client';

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { ExternalLinkIcon } from '@heroicons/react/outline';
import remarkGfm from 'remark-gfm';

interface SharedResponseDisplayProps {
  sharedResponse: {
    query: string;
    response: string;
    agent_type: string;
    sources?: any[];
    cache_id: string;
    quality_score: number;
    usage_count: number;
  };
  shareContext?: string;
  timestamp: string;
  sharedByUsername?: string;
  className?: string;
}

const SharedResponseDisplay: React.FC<SharedResponseDisplayProps> = ({
  sharedResponse,
  shareContext,
  timestamp,
  sharedByUsername,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatTimestamp = (isoString: string) => {
    try {
      return new Date(isoString).toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' });
    } catch {
      return isoString;
    }
  };

  const getQualityBadgeColor = (score: number) => {
    if (score >= 0.8) return 'bg-green-100 text-green-800';
    if (score >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getQualityLabel = (score: number) => {
    if (score >= 0.8) return 'High Quality';
    if (score >= 0.6) return 'Good Quality';
    return 'Low Quality';
  };

  // Custom renderer for links to open in new tab
  const customRenderers = {
    a: ({ node, ...props }: any) => (
      <a {...props} target="_blank" rel="noopener noreferrer" className="text-blue-900 hover:underline" />
    ),
  };

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-3 ${className}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          <span className="font-medium text-blue-900 font-semibold">Shared AI Response</span>
          {/* {sharedByUsername && (
            <span className="text-sm text-blue-700">
              by {sharedByUsername}
            </span>
          )} */}
        </div>
        <div className="flex items-center space-x-2">
          {/* <span className={`px-2 py-1 text-xs rounded-full ${getQualityBadgeColor(sharedResponse.quality_score)}`}>
            {getQualityLabel(sharedResponse.quality_score)}
          </span> */}
          
        </div>
      </div>

      {/* Share context */}
      {shareContext && (
        <div className="mb-3 p-2 bg-blue-100 rounded text-sm text-blue-800">
          <span className="font-medium">User Remark:</span> {shareContext}
        </div>
      )}

      {/* Query */}
      <div className="mb-3">
        <p className="text-sm font-medium text-gray-700 mb-1">Original Query:</p>
        <p className="text-sm text-gray-600 bg-white p-2 rounded border">
          {sharedResponse.query}
        </p>
      </div>

      {/* Response */}
      <div className="mb-3">
        <div className="flex items-center justify-between mb-1">
          <p className="text-sm font-medium text-gray-700">AI Response:</p>
          <button onClick={() => setIsExpanded(!isExpanded)} className="text-xs text-blue-900 hover:text-blue-800">
            {isExpanded ? 'Show Less' : 'Show More'}
          </button>
        </div>
        <div className="bg-white p-3 rounded border">
          <div className={`prose prose-sm max-w-none whitespace-pre-wrap ${!isExpanded ? 'line-clamp-3' : ''}`}>
            <ReactMarkdown components={customRenderers} remarkPlugins={[remarkGfm]}>
              {sharedResponse.response || ''}
            </ReactMarkdown>
          </div>
        </div>
      </div>

      {/* Sources (if available and expanded) */}
      {isExpanded && sharedResponse.sources && sharedResponse.sources.length > 0 && (
        <div className="mb-3">
          <p className="text-sm font-medium text-gray-700 mb-2">Sources:</p>
          <div className="space-y-2">
            {sharedResponse.sources.slice(0, 3).map((source: any, index: number) => (
              <div key={index} className="bg-white p-2 rounded border text-xs">
                {source.title && (
                  <div className="font-medium text-gray-800">{source.title}</div>
                )}
                {source.url && (
                  <a
                    href={source.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-900 hover:underline flex items-center"
                  >
                    <ExternalLinkIcon className="w-3 h-3 mr-1" />
                    {source.url}
                  </a>
                )}
                {source.snippet && (
                  <div className="text-gray-600 mt-1">{source.snippet}</div>
                )}
              </div>
            ))}
            {sharedResponse.sources.length > 3 && (
              <div className="text-xs text-gray-500">
                ... and {sharedResponse.sources.length - 3} more sources
              </div>
            )}
          </div>
        </div>
      )}

      {/* Metadata */}
      <div className="flex items-center justify-end text-xs text-gray-500 pt-2 border-t border-blue-200">
        {/* <div className="flex items-center space-x-4">
          <span className="flex items-center">
            <ChipIcon className="w-3 h-3 mr-1" />
            Agent: {sharedResponse.agent_type}
          </span>
          <span className="flex items-center">
            <ThumbUpIcon className="w-3 h-3 mr-1" />
            Quality: {(sharedResponse.quality_score * 100).toFixed(0)}%
          </span>
          <span className="flex items-center">
            <ShareIcon className="w-3 h-3 mr-1" />
            Used {sharedResponse.usage_count} times
          </span>
        </div> */}
        <div className="space-x-3">
          <button 
            onClick={() => navigator.clipboard.writeText(sharedResponse.query)}
            className="text-blue-900 hover:text-blue-800"
            title="Copy query to clipboard"
          >
            Copy Query
          </button>
          <button 
            onClick={() => navigator.clipboard.writeText(sharedResponse.response || '')}
            className="text-blue-900 hover:text-blue-800"
            title="Copy response to clipboard"
          >
            Copy Response
          </button>
        </div>
      </div>
    </div>
  );
};

export default SharedResponseDisplay; 