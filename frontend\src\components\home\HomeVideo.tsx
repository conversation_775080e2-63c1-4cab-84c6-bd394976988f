"use client";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function HomeVideo() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('access_token');
    setIsAuthenticated(!!token);
  }, []);

  const handleLaunchClick = () => {
    if (isAuthenticated) {
      router.push('/chat');
    } else {
      router.push('/login');
    }
  };

  const handleWorkplaceSLMClick = () => {
    router.push('/about');
  };

  return (
    <div className="w-full">
      {/* Section 1: Buttons */}
      <section className="w-full px-4 sm:px-6 lg:px-12 bg-transparent relative">
        <div className="flex justify-start gap-3 sm:gap-5">
          <button 
            onClick={handleLaunchClick}
            className="group inline-flex items-center gap-2 px-5 sm:px-6 py-2.5 rounded-full bg-purple-700 text-white border border-transparent transition-all hover:bg-transparent hover:text-black hover:border-black focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
            aria-label="Launch SLM Application"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-5 h-5 -ml-1 transition-transform duration-300 group-hover:rotate-90"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg>
            <span className="text-sm sm:text-base">Launch Workplace SLM</span>
          </button>

          <button 
            onClick={handleWorkplaceSLMClick}
            className="group inline-flex items-center gap-2 px-5 sm:px-6 py-2.5 rounded-full bg-purple-700 text-white border border-transparent transition-all hover:bg-transparent hover:text-black hover:border-black focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
            aria-label="Learn more about Workplace SLM"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-5 h-5 -ml-1 transition-transform duration-300 group-hover:rotate-90"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg>
            <span className="text-sm sm:text-base">Talk to sales</span>
          </button>
        </div>
      </section>

      {/* Section 2: Video */}
      <section className="w-full py-6 px-4 sm:px-6 lg:px-12">
        <div className="relative rounded-2xl sm:rounded-3xl overflow-hidden min-h-[520px] sm:min-h-[680px] md:min-h-[820px]">
          {/* Background video */}
          <video
            autoPlay
            loop
            muted
            playsInline
            className="absolute inset-0 w-full h-full object-cover"
          >
            <source src="/images/bottom-bg.mp4" type="video/mp4" />
          </video>

          {/* Overlay text */}
          <div className="relative z-20 flex w-full justify-center pt-28 sm:pt-36 md:pt-44 px-4 sm:px-6">
            <p className="max-w-5xl text-center text-white/90 text-xl sm:text-2xl md:text-3xl leading-relaxed">
              Watch how our AI Knowledge Assistant turns your company’s data
              into constructive insights and workflows, helping teams decide
              faster and work smarter.
            </p>
          </div>

          {/* Inner video card */}
          <div className="relative z-20 mt-6 sm:mt-8 md:mt-10 flex justify-center px-2 sm:px-6 md:px-12 mb-10 sm:mb-12 md:mb-16 lg:mb-20">
            <div className="w-full max-w-5xl rounded-[22px] bg-white/20 ring-1 ring-white/60 shadow-[0_10px_30px_rgba(0,0,0,0.25)] p-2 sm:p-3">
              <div className="aspect-video w-full overflow-hidden rounded-xl">
                <video controls className="w-full h-full object-cover">
                  <source src="/images/slm-video.mp4" type="video/mp4" />
                </video>
              </div>
            </div>
          </div>
        </div>
      </section>
      
    </div>
  );
}

