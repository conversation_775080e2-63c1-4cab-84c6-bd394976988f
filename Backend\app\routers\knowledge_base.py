import logging
import io
import urllib.parse
from fastapi import APIRouter, Depends, HTTPException, status, Query, File, Form, UploadFile, Request

logger = logging.getLogger(__name__)
from fastapi.responses import StreamingResponse, JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from sqlalchemy import select, or_, func, update, delete
from typing import List, Optional, Dict, Any
from jose import JWTError
from pathlib import Path
from starlette.responses import JSONResponse

from ..database import get_db
from ..models.knowledge_base import KnowledgeDocument
from ..models.user_space import UserSpace
from ..models.user import User
from ..schemas.knowledge_base import (
    KnowledgeDocumentCreate, KnowledgeDocumentResponse, KnowledgeDocumentUpdate, 
    BulkUploadResponse, DocumentIndexRequest, DocumentIndexResponse
)
from ..auth.dependencies import get_current_user, require_admin
from ..auth.jwt_handler import get_user_from_token
from ..services.minio_service import minio_service
from ..services.external_services import external_services
from ..services.redis_logger import cached_get, cached_set
from ..services.redis_service import redis_service, cached, invalidate_cache, invalidate_knowledge_cache
from ..services.space_permissions import SpacePermissions, require_space_access, require_document_access
from .. import config
from ..config import REDIS_PREFIX_Space, ALLOWED_IMAGE_TYPES, MAX_IMAGE_UPLOAD_MB
from ..services.redis_service import redis_service

router = APIRouter(prefix="/knowledge", tags=["Knowledge Base"])

# Configure logging for this module
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

ALLOWED_FILE_TYPES = {
    "application/pdf": "pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
    "application/msword": "doc",
    "application/vnd.ms-powerpoint": "ppt",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": "pptx",
    "application/vnd.ms-excel": "xls",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
}

# Import image processing utilities and config
from ..utils.image_processing import (
    validate_image_type,
    sanitize_image,
    assert_upload_allowed,
    ImageValidationError,
    ImageSanitizationError
)
@router.post("/", response_model=KnowledgeDocumentResponse, status_code=status.HTTP_201_CREATED)
@invalidate_cache(f"{config.CACHE_PREFIX_KNOWLEDGE}:*")
async def create_text_document(
    document_data: KnowledgeDocumentCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new text-based knowledge document
    """
    # Validate document type
    if document_data.document_type != "text":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="For text documents, use this endpoint. For file uploads, use the /upload endpoint."
        )
    
    # Validate that text content is provided
    if not document_data.content:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Content is required for text documents"
        )
    
    # Validate space access and upload permissions
    space = await require_space_access(current_user, document_data.space_id, db, "upload")
    
    # Block text documents for image spaces
    if space.is_image_playground():
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="Image spaces do not support text documents. Please select a document space."
        )
    
    try:
        # Generate a filename with .txt extension
        filename = f"{document_data.title.replace(' ', '_')}.txt"
        
        # Upload text content to MinIO
        upload_result = minio_service.upload_binary_data(
            data=document_data.content.encode('utf-8'),
            filename=filename,
            bucket_type="knowledge_documents",
            user_id=current_user.id,
            collection=space.collection_name,
            username=current_user.username,
            space_id=document_data.space_id
        )
        
        # Create the document with MinIO references
        new_document = KnowledgeDocument(
            title=document_data.title,
            description=document_data.description,
            space_id=document_data.space_id,
            document_type=document_data.document_type,
            content=document_data.content,  # Store content in DB as well for searching
            filename=filename,
            minio_bucket=upload_result["bucket_name"],
            minio_object_key=upload_result["object_key"],
            file_size=upload_result["file_size"],
            content_type="text/plain",
            file_url=upload_result["url"],
            created_by=current_user.id
        )
        
        db.add(new_document)
        await db.commit()
        await db.refresh(new_document)
        
        # Invalidate caches so FE sees updated lists and counts
        await invalidate_knowledge_cache(document_data.space_id)

        # Also invalidate cached /spaces lists so document_count updates immediately
        try:
            await redis_service.delete_pattern(f"{REDIS_PREFIX_Space}:user:*")
        except Exception as e:
            logger.error(f"Redis cache delete error after text create: {e}")
        
        return new_document.to_dict(include_content=True)
    
    except Exception as e:
        await db.rollback()
        logger.error(f"Error creating text document: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create text document: {str(e)}"
        )

@router.post("/upload/single", status_code=status.HTTP_201_CREATED, response_model=KnowledgeDocumentResponse)
@invalidate_cache(f"{config.CACHE_PREFIX_KNOWLEDGE}:*")
async def upload_document(
    file: UploadFile = File(...),
    title: str = Form(...),
    description: str = Form(None),
    space_id: int = Form(...),
    allow_duplicate: bool = Form(False),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Upload a single document or image file based on space type
    """
    # Validate space access and upload permissions
    space = await require_space_access(current_user, space_id, db, "upload")
    
    # Read file content once
    file_content = await file.read()
    content_type = (file.content_type or "").lower()
    
    # Validate upload is allowed for this space type (tolerates octet-stream for images)
    try:
        assert_upload_allowed(space, content_type, file_content)
    except ImageValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    
    # Route by space type rather than raw header; actual validation happens inside handlers
    if space.is_image_playground():
        return await _handle_image_upload(
            file, file_content, content_type, title, description,
            space_id, space, allow_duplicate, current_user, db
        )
    else:
        return await _handle_document_upload(
            file, file_content, content_type, title, description,
            space_id, space, allow_duplicate, current_user, db
        )


async def _handle_document_upload(
    file: UploadFile,
    file_content: bytes,
    content_type: str,
    title: str,
    description: Optional[str],
    space_id: int,
    space: UserSpace,
    allow_duplicate: bool,
    current_user: User,
    db: AsyncSession
) -> Dict[str, Any]:
    """Handle document file upload (existing logic)"""
    doc_type = ALLOWED_FILE_TYPES.get(content_type)
    if not doc_type:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX files are supported for document spaces"
        )

    # -------------------------------------------------------
    # Duplicate check: exact filename match (without extension) + space
    # -------------------------------------------------------
    bare_name = Path(file.filename).stem.lower() if title is None else title.lower()
    # Check for exact match and all numbered versions (case insensitive)
    result = await db.execute(
        select(KnowledgeDocument).where(
            or_(
                KnowledgeDocument.title.ilike(bare_name),
                KnowledgeDocument.title.ilike(f"{bare_name}_{space_id}_%")
            ),
            KnowledgeDocument.space_id == space_id
        )
    )
    duplicate = result.scalars().all()
    if duplicate:
        if not allow_duplicate:
            # Return a warning response instead of an error so the FE can prompt the user
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "warning": "duplicate",
                    "doc_id": duplicate[0].id,
                    "indexed": getattr(duplicate[0], "indexed", False),
                },
            )
        # Generate suffix pattern with space id: <original>_<spaceId>_<n>
        suffix = 1
        existing_titles = {d.title for d in duplicate}
        candidate = f"{bare_name}_{space_id}_{suffix}"
        while candidate in existing_titles:
            suffix += 1
            candidate = f"{bare_name}_{space_id}_{suffix}"
        bare_name = candidate  # use the new unique title
        title = bare_name  # update title value used later

    try:
        # file_content already provided as parameter
        file_size = len(file_content)

        # Check file size (max 10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        if file_size > max_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File too large. Maximum size is 10MB, got {file_size / (1024 * 1024):.2f}MB"
            )

        # Upload to MinIO
        upload_result = minio_service.upload_binary_data(
            data=file_content,
            filename=file.filename,
            bucket_type="knowledge_documents",
            user_id=current_user.id,
            collection=space.collection_name,
            username=current_user.username,
            space_id=space_id
        )

        # Final title after duplicate logic
        title = bare_name

        # Create document record
        new_document = KnowledgeDocument(
            title=title,
            description=description,
            space_id=space_id,
            document_type=doc_type,  # "pdf" or "docx"
            filename=file.filename,
            minio_bucket=upload_result["bucket_name"],
            minio_object_key=upload_result["object_key"],
            file_size=file_size,
            content_type=content_type,
            file_url=upload_result["url"],
            created_by=current_user.id
        )

        db.add(new_document)
        await db.commit()
        await db.refresh(new_document)

        # Invalidate space cache
        await invalidate_knowledge_cache(space_id)

        # Invalidate user's /spaces list cache so document_count refreshes
        from ..config import REDIS_PREFIX_Space
        from ..services.redis_service import redis_service
        try:
            await redis_service.delete_pattern(f"{REDIS_PREFIX_Space}:user:*")
        except Exception as e:
            logger.error(f"Redis cache delete error after single upload: {e}")

        return new_document.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload document: {str(e)}"
        )


async def _handle_image_upload(
    file: UploadFile,
    file_content: bytes,
    content_type: str,
    title: str,
    description: Optional[str],
    space_id: int,
    space: UserSpace,
    allow_duplicate: bool,
    current_user: User,
    db: AsyncSession
) -> Dict[str, Any]:
    """Handle image file upload with validation and sanitization"""
    try:
        # 1. Validate image type
        try:
            is_valid, detected_type = validate_image_type(content_type, file_content)
        except ImageValidationError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        
        # 2. Sanitize image (resize, strip EXIF, convert to WebP)
        try:
            processed_bytes, metadata = sanitize_image(file_content)
            logger.info(f"Image sanitization metadata: {metadata}")
        except ImageValidationError as e:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE
                if "too large" in str(e).lower() or "bomb" in str(e).lower()
                else status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        except ImageSanitizationError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to process image: {str(e)}"
            )
        
        # 3. Duplicate check (same logic as documents)
        bare_name = Path(file.filename).stem.lower() if not title else title.lower()
        result = await db.execute(
            select(KnowledgeDocument).where(
                or_(
                    KnowledgeDocument.title.ilike(bare_name),
                    KnowledgeDocument.title.ilike(f"{bare_name}_{space_id}_%")
                ),
                KnowledgeDocument.space_id == space_id
            )
        )
        duplicate = result.scalars().all()
        
        if duplicate:
            if not allow_duplicate:
                return JSONResponse(
                    status_code=status.HTTP_200_OK,
                    content={
                        "warning": "duplicate",
                        "doc_id": duplicate[0].id,
                        "indexed": False,  # Images are never indexed
                    },
                )
            # Generate unique suffix
            suffix = 1
            existing_titles = {d.title for d in duplicate}
            candidate = f"{bare_name}_{space_id}_{suffix}"
            while candidate in existing_titles:
                suffix += 1
                candidate = f"{bare_name}_{space_id}_{suffix}"
            bare_name = candidate
            title = bare_name
        
        # 4. Upload to MinIO staging
        staging_result = minio_service.upload_to_staging(
            data=processed_bytes,
            filename=file.filename,
            user_id=current_user.id,
            content_type=metadata['content_type']
        )
        
        # 5. Finalize from staging to image_playground
        final_filename = f"{bare_name}.{metadata['processed_format']}"
        finalize_result = minio_service.finalize_from_staging(
            staging_key=staging_result['staging_key'],
            final_filename=final_filename,
            bucket_type="image_playground",
            user_id=current_user.id,
            username=current_user.username,
            collection=space.collection_name,
            space_id=space_id,
            content_type=metadata['content_type']
        )
        
        # 6. Create document record (indexed=False for images)
        new_document = KnowledgeDocument(
            title=title or bare_name,
            description=description,
            space_id=space_id,
            document_type="image",
            filename=final_filename,
            minio_bucket=finalize_result["bucket_name"],
            minio_object_key=finalize_result["object_key"],
            file_size=finalize_result["file_size"],
            content_type=finalize_result["content_type"],
            file_url=finalize_result["url"],
            indexed=False,  # Images are NOT indexed
            created_by=current_user.id
        )
        
        db.add(new_document)
        await db.commit()
        await db.refresh(new_document)
        
        # 7. Invalidate caches
        await invalidate_knowledge_cache(space_id)
        from ..config import REDIS_PREFIX_Space
        from ..services.redis_service import redis_service
        try:
            await redis_service.delete_pattern(f"{REDIS_PREFIX_Space}:user:*")
        except Exception as e:
            logger.error(f"Redis cache delete error after image upload: {e}")
        
        logger.info(
            f"Image uploaded successfully: {new_document.id} "
            f"({metadata['processed_size_mb']:.2f}MB, {metadata['processed_format']})"
        )
        
        return new_document.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error uploading image: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload image: {str(e)}"
        )

@router.post("/upload/bulk", status_code=status.HTTP_200_OK, response_model=BulkUploadResponse)
async def bulk_upload_documents(
    files: List[UploadFile] = File(...),
    space_id: int = Form(...),
    allow_duplicate: bool = Form(False),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Upload multiple document files in one request
    """
    # Validate space access and upload permissions
    space = await require_space_access(current_user, space_id, db, "upload")
    
    # Debug: Check what documents exist in this space
    result = await db.execute(select(KnowledgeDocument).where(KnowledgeDocument.space_id == space_id))
    existing_docs = result.scalars().all()
    logger.info(f"Space {space_id} currently has {len(existing_docs)} documents: {[d.title for d in existing_docs]}")
    
    # Prepare response
    response = {
        "success": True,
        "uploaded_count": 0,
        "failed_count": 0,
        "uploaded_documents": [],
        "failed_uploads": [],
        "duplicate_files": []
    }
    
    # Track names used in this batch to avoid conflicts within the same upload
    used_names_in_batch = set()

    if not files:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No files provided"
        )

    for file in files:
        try:
            content_type = (file.content_type or "").lower()

            # Gate uploads by space type
            try:
                # Read once for validation/sanitization
                file_content = await file.read()
                assert_upload_allowed(space, content_type, file_content)
            except ImageValidationError as e:
                response["failed_uploads"].append({
                    "filename": file.filename,
                    "reason": str(e)
                })
                response["failed_count"] += 1
                continue

            # -------------------------------------------------------
            # Duplicate check per file
            # -------------------------------------------------------
            bare_name = Path(file.filename).stem.lower()
            
            # Debug logging
            logger.info(f"Checking for duplicates of '{bare_name}' in space {space_id}")
            
            # Check for exact match and all numbered versions (case insensitive)
            result = await db.execute(
                select(KnowledgeDocument).where(
                    or_(
                        KnowledgeDocument.title.ilike(bare_name),
                        KnowledgeDocument.title.ilike(f"{bare_name}_{space_id}_%")
                    ),
                    KnowledgeDocument.space_id == space_id
                )
            )
            duplicate = result.scalars().all()
            
            # Debug logging
            if duplicate:
                logger.info(f"Found {len(duplicate)} potential duplicates: {[d.title for d in duplicate]}")
            else:
                logger.info(f"No duplicates found for '{bare_name}' in space {space_id}")

            if duplicate and not allow_duplicate:
                # collect duplicate info and skip processing this file
                response["duplicate_files"].append({
                    "filename": file.filename,
                    "doc_id": duplicate[0].id,
                    "indexed": getattr(duplicate[0], "indexed", False)
                })
                continue  # skip upload until user confirms

            if duplicate and allow_duplicate:
                # generate unique suffix using space id pattern
                suffix = 1
                existing_titles = {d.title for d in duplicate}
                # Also include names used in this batch
                existing_titles.update(used_names_in_batch)
                candidate = f"{bare_name}_{space_id}_{suffix}"
                while candidate in existing_titles:
                    suffix += 1
                    candidate = f"{bare_name}_{space_id}_{suffix}"
                bare_name = candidate
                # Add this name to the batch tracking
                used_names_in_batch.add(bare_name)
            else:
                # Even if not duplicate, track the original name
                used_names_in_batch.add(bare_name)

            if space.is_document_playground():
                # Document flow
                file_size = len(file_content)
                max_size = 10 * 1024 * 1024  # 10MB
                if file_size > max_size:
                    response["failed_uploads"].append({
                        "filename": file.filename,
                        "reason": f"File too large. Maximum size is 10MB, got {file_size / (1024 * 1024):.2f}MB"
                    })
                    response["failed_count"] += 1
                    continue

                doc_type = ALLOWED_FILE_TYPES.get(content_type)
                if not doc_type:
                    response["failed_uploads"].append({
                        "filename": file.filename,
                        "reason": "Only PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX files are currently supported"
                    })
                    response["failed_count"] += 1
                    continue

                upload_result = minio_service.upload_binary_data(
                    data=file_content,
                    filename=file.filename,
                    bucket_type="knowledge_documents",
                    user_id=current_user.id,
                    collection=space.collection_name,
                    username=current_user.username,
                    space_id=space_id
                )

                title = bare_name
                new_document = KnowledgeDocument(
                    title=title,
                    description=None,
                    space_id=space_id,
                    document_type=doc_type,
                    filename=file.filename,
                    minio_bucket=upload_result["bucket_name"],
                    minio_object_key=upload_result["object_key"],
                    file_size=file_size,
                    content_type=content_type,
                    file_url=upload_result["url"],
                    created_by=current_user.id
                )
                db.add(new_document)
                await db.commit()
                await db.refresh(new_document)
                response["uploaded_documents"].append(new_document.to_dict())
                response["uploaded_count"] += 1
            else:
                # Image flow
                try:
                    processed_bytes, metadata = sanitize_image(file_content)
                except ImageValidationError as e:
                    response["failed_uploads"].append({
                        "filename": file.filename,
                        "reason": str(e)
                    })
                    response["failed_count"] += 1
                    continue

                staging_result = minio_service.upload_to_staging(
                    data=processed_bytes,
                    filename=file.filename,
                    user_id=current_user.id,
                    content_type=metadata['content_type']
                )

                final_filename = f"{bare_name}.{metadata['processed_format']}"
                finalize_result = minio_service.finalize_from_staging(
                    staging_key=staging_result['staging_key'],
                    final_filename=final_filename,
                    bucket_type="image_playground",
                    user_id=current_user.id,
                    username=current_user.username,
                    collection=space.collection_name,
                    space_id=space_id,
                    content_type=metadata['content_type']
                )

                new_document = KnowledgeDocument(
                    title=bare_name,
                    description=None,
                    space_id=space_id,
                    document_type="image",
                    filename=final_filename,
                    minio_bucket=finalize_result["bucket_name"],
                    minio_object_key=finalize_result["object_key"],
                    file_size=finalize_result["file_size"],
                    content_type=finalize_result["content_type"],
                    file_url=finalize_result["url"],
                    indexed=False,
                    created_by=current_user.id
                )
                db.add(new_document)
                await db.commit()
                await db.refresh(new_document)
                response["uploaded_documents"].append(new_document.to_dict())
                response["uploaded_count"] += 1

        except Exception as e:
            await db.rollback()
            logger.error(f"Error uploading file {file.filename}: {e}")
            response["failed_uploads"].append({
                "filename": file.filename,
                "reason": str(e)
            })
            response["failed_count"] += 1

    if response["failed_count"] > 0:
        response["success"] = False

    # -------------------------------------------------------
    # Invalidate caches so counts / docs refresh
    # -------------------------------------------------------
    try:
        # space-level document caches
        await invalidate_knowledge_cache(space_id=space_id)

        # /spaces list cache for all users (document_count)
        from ..config import REDIS_PREFIX_Space
        await redis_service.delete_pattern(f"{REDIS_PREFIX_Space}:user:*")
    except Exception as e:
        logger.error(f"Cache invalidation error after bulk upload: {e}")

    return response

@router.get("/{document_id}", response_model=KnowledgeDocumentResponse)
async def get_document(
    document_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific knowledge document
    """
    # Try cache (scoped by user for access checks)
    cache_key = f"{config.CACHE_PREFIX_KNOWLEDGE}:document:{document_id}:user:{current_user.id}"
    cached = await cached_get(cache_key)
    if cached:
        return cached

    result = await db.execute(
        select(KnowledgeDocument).options(
            joinedload(KnowledgeDocument.creator),
            joinedload(KnowledgeDocument.space)
        ).where(KnowledgeDocument.id == document_id)
    )
    document = result.scalars().first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Use permission helper to check document access
    await require_document_access(current_user, document_id, db, "view")
    
    result = document.to_dict(include_content=True)
    await cached_set(cache_key, result, ttl=config.CACHE_TTL_KNOWLEDGE_BASE)
    return result

@router.get("/{document_id}/download", response_model=None)
async def download_document(
    document_id: int,
    token: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    request: Request = None
):
    """
    View a document file (with inline content disposition)
    
    This endpoint supports both token-based auth and regular auth
    """
    # Initialize current_user as None
    current_user = None
    
    # Try to authenticate with token if provided
    if token:
        try:
            user_data = get_user_from_token(token)
            result = await db.execute(select(User).where(User.id == user_data["user_id"]))
            current_user = result.scalars().first()
        except JWTError:
            pass
    if not current_user and request:
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            try:
                auth_token = auth_header.replace("Bearer ", "")
                user_data = get_user_from_token(auth_token)
                result = await db.execute(select(User).where(User.id == user_data["user_id"]))
                current_user = result.scalars().first()
            except JWTError:
                pass
    if not current_user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated")

    result = await db.execute(
        select(KnowledgeDocument).options(
            joinedload(KnowledgeDocument.space)
        ).where(KnowledgeDocument.id == document_id)
    )
    document = result.scalars().first()
    if not document:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Document not found")

    # Permission: any viewer of the space (owner, member, or app admin)
    if not SpacePermissions.can_view_space(current_user, document.space, db):
        logger.warning(f"Download denied: user {current_user.id} cannot view document {document_id} in space {document.space_id}")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied")

    if not document.minio_bucket or not document.minio_object_key:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Document file not found")

    try:
        file_content = minio_service.download_file(
            bucket_name=document.minio_bucket,
            object_key=document.minio_object_key
        )

        # Defaults by type
        default_media_type = (
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            if document.document_type == "docx"
            else "application/pdf"
        )
        default_filename = f"{(document.title or 'document').replace(' ', '_')}.{document.document_type or 'pdf'}"

        safe_filename = urllib.parse.quote(document.filename or default_filename)

        return StreamingResponse(
            io.BytesIO(file_content),
            media_type=document.content_type or default_media_type,
            headers={"Content-Disposition": f'inline; filename="{safe_filename}"'}
        )

    except Exception as e:
        logger.error(f"Error downloading document {document_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to download document")

@router.get("/{document_id}/download_attachment", response_model=None)
async def download_document_attachment(
    document_id: int,
    token: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    request: Request = None
):
    """
    Download a document file as an attachment
    
    This endpoint supports both token-based auth and regular auth
    """
    # Initialize current_user as None
    current_user = None
    
    # Try to authenticate with token if provided
    if token:
        try:
            user_data = get_user_from_token(token)
            result = await db.execute(select(User).where(User.id == user_data["user_id"]))
            current_user = result.scalars().first()
        except JWTError:
            pass
    if not current_user and request:
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            try:
                auth_token = auth_header.replace("Bearer ", "")
                user_data = get_user_from_token(auth_token)
                result = await db.execute(select(User).where(User.id == user_data["user_id"]))
                current_user = result.scalars().first()
            except JWTError:
                pass
    if not current_user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated")

    result = await db.execute(
        select(KnowledgeDocument).options(
            joinedload(KnowledgeDocument.space)
        ).where(KnowledgeDocument.id == document_id)
    )
    document = result.scalars().first()
    if not document:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Document not found")

    # Permission: any viewer of the space (owner, member, or app admin)
    if not SpacePermissions.can_view_space(current_user, document.space, db):
        logger.warning(f"Download denied: user {current_user.id} cannot download attachment {document_id} in space {document.space_id}")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied")

    if not document.minio_bucket or not document.minio_object_key:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Document file not found")

    try:
        file_content = minio_service.download_file(
            bucket_name=document.minio_bucket,
            object_key=document.minio_object_key
        )

        # Defaults by type
        default_media_type = (
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            if document.document_type == "docx"
            else "application/pdf"
        )
        default_filename = f"{(document.title or 'document').replace(' ', '_')}.{document.document_type or 'pdf'}"

        safe_filename = urllib.parse.quote(document.filename or default_filename)

        return StreamingResponse(
            io.BytesIO(file_content),
            media_type=document.content_type or default_media_type,
            headers={"Content-Disposition": f'attachment; filename="{safe_filename}"'}
        )

    except Exception as e:
        logger.error(f"Error downloading document {document_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to download document")

@router.put("/{document_id}", response_model=KnowledgeDocumentResponse)
@invalidate_cache(f"{config.CACHE_PREFIX_KNOWLEDGE}:*")
async def update_document(
    document_id: int,
    document_update: KnowledgeDocumentUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a knowledge document
    """
    result = await db.execute(
        select(KnowledgeDocument).options(
            joinedload(KnowledgeDocument.space)
        ).where(KnowledgeDocument.id == document_id)
    )
    document = result.scalars().first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Check access permissions (creator or admin)
    if document.created_by != current_user.id and getattr(current_user, "role", "user") != "admin":
        logger.warning(f"Update denied: User {current_user.id} ({getattr(current_user, 'role', 'user')}) tried to update document {document_id} created by user {document.created_by}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only update documents you created"
        )
    
    # If changing space, validate access to new space
    if document_update.space_id and document_update.space_id != document.space_id:
        await require_space_access(current_user, document_update.space_id, db, "upload")
    
    # Capture old space before update
    old_space_id = document.space_id

    # Update fields
    if document_update.title is not None:
        document.title = document_update.title
    
    if document_update.description is not None:
        document.description = document_update.description
    
    if document_update.space_id is not None:
        document.space_id = document_update.space_id
    
    await db.commit()
    await db.refresh(document)
    
    # Invalidate caches: per-document and space lists
    await invalidate_knowledge_cache(space_id=document.space_id, document_ids=[document.id])
    if document_update.space_id and old_space_id != document.space_id:
        await invalidate_knowledge_cache(space_id=old_space_id)
    
    return document.to_dict(include_content=True)

@router.delete("/{document_id}", status_code=status.HTTP_204_NO_CONTENT)
@invalidate_cache(f"{config.CACHE_PREFIX_KNOWLEDGE}:*")
async def delete_document(
    document_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a knowledge document from MinIO, Qdrant, and PostgreSQL
    """
    # Use permission helper to check delete access
    document, space = await require_document_access(current_user, document_id, db, "delete")
    
    try:
        # Move file to backup folder instead of deleting (soft delete)
        if document.minio_bucket and document.minio_object_key:
            backup_result = minio_service.move_to_backup(
                bucket_name=document.minio_bucket,
                object_key=document.minio_object_key
            )
            if backup_result.get("success"):
                logger.info(f"File moved to backup: {backup_result.get('backup_location')}")
            else:
                logger.warning(f"Failed to move file to backup, will continue with deletion: {backup_result.get('error')}")
        
        # Delete chunks from Qdrant and PostgreSQL if document was indexed
        if document.indexed and document.filename and space.collection_name:
            logger.info(f"Deleting chunks for document '{document.filename}' from Qdrant and PostgreSQL")
            deletion_result = external_services.delete_document_chunks(
                filename=document.filename,
                collection_name=space.collection_name,
                space_id=document.space_id
            )
            
            if deletion_result.get("success"):
                logger.info(f"Successfully deleted chunks: {deletion_result.get('message')}")
            else:
                logger.warning(f"Failed to delete chunks: {deletion_result.get('error')}")
                # Continue with document deletion even if chunk deletion fails
        
        # Delete document from database
        space_id = document.space_id
        doc_id = document.id
        await db.delete(document)
        await db.commit()
        
        # Invalidate space and per-document caches
        await invalidate_knowledge_cache(space_id=space_id, document_ids=[doc_id])

        # Also invalidate the current user's /spaces list caches (typed keys) so document_count refreshes
        try:
            await redis_service.delete_pattern(f"{REDIS_PREFIX_Space}:user:{current_user.id}:type:*")
            # Best-effort: remove legacy/non-typed key if present
            await redis_service.delete(f"{REDIS_PREFIX_Space}:user:{current_user.id}")
        except Exception as e:
            logger.error(f"Redis cache delete error after document delete: {e}")
        
        return None
        
    except Exception as e:
        await db.rollback()
        logger.error(f"Error deleting document {document_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete document"
        )

# Space-specific endpoints
@router.get("/space/{space_id}/documents", response_model=List[KnowledgeDocumentResponse])
async def get_space_documents(
    space_id: int,
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    search: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all documents in a specific space
    """
    # Validate space access and query permissions
    await require_space_access(current_user, space_id, db, "query")
    
    # ---------------- Redis cache first -----------------
    cache_key = (
        f"{config.CACHE_PREFIX_KNOWLEDGE}:space:{space_id}:user:{current_user.id}:documents:"  
        f"limit:{limit}:offset:{offset}:search:{(search or '').lower()}"
    )

    try:
        cached = await cached_get(cache_key)
        if cached is not None:
            logger.info(f"KnowledgeDoc cache HIT for {cache_key}")
            return cached
        else:
            logger.info(f"KnowledgeDoc cache MISS for {cache_key}")
    except Exception as e:
        logger.error(f"Redis get error in get_space_documents: {e}")

    # ----------------- DB fallback ----------------------
    stmt = select(KnowledgeDocument).options(
        joinedload(KnowledgeDocument.creator),
        joinedload(KnowledgeDocument.space)
    ).where(KnowledgeDocument.space_id == space_id)

    # Search functionality
    if search:
        stmt = stmt.where(
            or_(
                KnowledgeDocument.title.ilike(f"%{search}%"),
                KnowledgeDocument.description.ilike(f"%{search}%"),
                KnowledgeDocument.content.ilike(f"%{search}%")
            )
        )
    
    # Apply pagination
    stmt = stmt.offset(offset).limit(limit)
    result = await db.execute(stmt)
    documents = result.scalars().all()
    
    result = [doc.to_dict() for doc in documents]
    logger.info(f"get_space_documents - Returning {len(result)} documents for space {space_id}: {[doc['title'] for doc in result]}")

    # Cache the result for subsequent requests
    try:
        await cached_set(cache_key, result, ttl=config.CACHE_TTL_KNOWLEDGE_BASE)
    except Exception as e:
        logger.error(f"Redis set error in get_space_documents: {e}")
    
    return result

@router.post("/space/{space_id}/index", response_model=DocumentIndexResponse)
async def index_space_documents(
    space_id: int,
    request: DocumentIndexRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Index documents in a space using the external ingestion service
    """
    # Validate space access - allow admins
    # Use unified permission helper: members and admins may index (ingest)
    space = await require_space_access(current_user, space_id, db, "upload")
    logger.info(f"User {current_user.id} indexing space {space_id}")
    
    try:
        # Get documents to index
        stmt = select(KnowledgeDocument).where(KnowledgeDocument.space_id == space_id)
        
        if request.document_ids:
            # Index specific documents
            stmt = stmt.where(KnowledgeDocument.id.in_(request.document_ids))
        else:
            # Index all non-indexed documents in the space
            stmt = stmt.where(KnowledgeDocument.indexed == False)
        
        result = await db.execute(stmt)
        documents = result.scalars().all()
        
        if not documents:
            return {
                "success": True,
                "message": "No documents need indexing",
                "indexed_count": 0
            }
        
        # Prepare documents for ingestion
        documents_to_index = []
        
        for doc in documents:
            if doc.minio_bucket and doc.minio_object_key:
                # For documents stored in MinIO, include MinIO info
                doc_data = {
                    "content": "",  # File documents don't have text content in DB
                    "filename": doc.filename or f"{doc.title}.{doc.document_type}",
                    "title": doc.title,
                    "document_type": doc.document_type,
                    "knowledge_doc_id": doc.id,
                    "minio_bucket": doc.minio_bucket,
                    "minio_object_key": doc.minio_object_key,
                    "content_type": doc.content_type,
                    "file_data": None,  # Legacy field for backward compatibility
                    "space_id": space_id,
                }
                documents_to_index.append(doc_data)
                logger.debug(f"Prepared file document from MinIO for indexing: {doc.filename}")
                    
            elif doc.content:
                # For text documents, include content
                doc_data = {
                    "content": doc.content,
                    "filename": doc.filename or f"{doc.title}.txt",
                    "title": doc.title,
                    "document_type": doc.document_type,
                    "knowledge_doc_id": doc.id,
                    "file_data": None,
                    "space_id": space_id
                }
                documents_to_index.append(doc_data)
                logger.debug(f"Prepared text document for indexing: {doc.title}")
            else:
                logger.warning(f"Document {doc.id} has no content or file - skipping")
        
        if not documents_to_index:
            return {
                "success": False,
                "message": "No valid documents found for indexing",
                "indexed_count": 0
            }
        
        # Use external services for ingestion
        collection_name = space.collection_name
        result = await external_services.ingest_documents(
            documents=documents_to_index,
            username=space.owner.username,
            collection_name=collection_name,
            user_id=current_user.id,
            space_id=space_id
        )
        
        # Update indexing status for successfully processed documents
        processed_count = result.get("processed_documents", 0)
        logger.info(f"Indexing result: success={result.get('success', False)}, processed_count={processed_count}")
        logger.info(f"Documents to update: {len(documents)} total documents")

        if result.get("success", False) and processed_count > 0:
            # Only mark documents as indexed if they were actually processed
            indexed_docs = documents[:processed_count]
            logger.info(f"Marking {len(indexed_docs)} documents as indexed: {[doc.id for doc in indexed_docs]}")

            for doc in indexed_docs:
                logger.info(f"Marking document {doc.id} ({doc.title}) as indexed")
                doc.mark_as_indexed()
                logger.info(f"Document {doc.id} indexed status: {doc.indexed}, indexed_at: {doc.indexed_at}")

            try:
                await db.commit()
                logger.info(f"Database commit successful for {len(indexed_docs)} documents")

                # Invalidate space list and per-document caches so FE sees latest status
                await invalidate_knowledge_cache(space_id=space_id, document_ids=[d.id for d in indexed_docs])
                logger.info("Cache invalidation completed")
            except Exception as e:
                logger.error(f"Database commit failed: {e}")
                await db.rollback()
        else:
            logger.warning(f"Skipping indexing update: success={result.get('success', False)}, processed_count={processed_count}")
        
        return {
            "success": result.get("success", False),
            "message": f"Indexed {result.get('processed_documents', 0)} documents",
            "indexed_count": result.get('processed_documents', 0),
            "failed_count": result.get('failed_documents', 0),
            "collection_name": collection_name,
            "total_processing_time": result.get('total_processing_time', 0)
        }
        
    except Exception as e:
        logger.error(f"Failed to index documents for space {space_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Document indexing failed: {str(e)}"
        )