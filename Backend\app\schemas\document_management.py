from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class FileInfo(BaseModel):
    """Information about a single file"""
    id: int
    title: str
    filename: str
    document_type: str
    description: Optional[str] = None
    created_at: Optional[str] = None
    space_id: int
    space_name: str
    username: str

class UserSpaceFilesResponse(BaseModel):
    """Response model for user space files listing"""
    username: str
    space_id: int
    space_name: str
    files: List[FileInfo]
    total_files: int


class FileSelectionOption(BaseModel):
    """Option for file selection in UI"""
    filename: str
    title: str
    document_type: str
    description: Optional[str] = None
    size_info: Optional[str] = None
    indexed: bool = False
    indexed_at: Optional[str] = None

class UserSpaceFilesSelectionResponse(BaseModel):
    """Response for file selection UI"""
    username: str
    space_id: int
    space_name: str
    available_files: List[FileSelectionOption]
    total_files: int
    message: str = "Select files to search in"

# New schemas for external services integration

class DocumentIngestionData(BaseModel):
    """Data for a single document to be ingested"""
    knowledge_doc_id: int = Field(..., description="ID of the knowledge document")
    
    class Config:
        json_schema_extra = {
            "example": {
                "knowledge_doc_id": 123
            }
        }

class DocumentIngestionRequest(BaseModel):
    """Request model for document ingestion using external services"""
    documents: List[DocumentIngestionData] = Field(..., description="List of documents to ingest")
    
    class Config:
        json_schema_extra = {
            "example": {
                "documents": [
                    {"knowledge_doc_id": 123},
                    {"knowledge_doc_id": 124}
                ]
            }
        }

class DocumentIngestionResponse(BaseModel):
    """Response model for document ingestion"""
    success: bool
    username: str
    processed_documents: int
    failed_documents: int
    total_processing_time: float
    collection_name: str
    error: Optional[str] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "username": "admin",
                "processed_documents": 2,
                "failed_documents": 0,
                "total_processing_time": 15.5,
                "collection_name": "admin",
                "error": None
            }
        } 