# Version control
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
*.so
.pytest_cache
.coverage
.tox
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.venv
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Documentation
*.md
!README.md

# Development files
environment.env
.env.local
.env.*.local

# Docker
Dockerfile*
docker-compose*

# Testing
tests/
test_*.py
*_test.py

# Temporary files
tmp/
temp/
*.tmp