import React, { useRef, useEffect, useState } from 'react';
import { useChat } from '@/contexts';
import ReactMarkdown from 'react-markdown';
import SourcesDisplay from './SourcesDisplay';
import FeedbackWidget from './FeedbackWidget';
import remarkGfm from 'remark-gfm';
import PromptGallery from './PromptGallery';
import ChatHistoryManager, { clearAgentChatHistory } from './ChatHistoryManager';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { FaPauseCircle } from 'react-icons/fa';
import NotesModal from '../notes/NotesModal';
import ReferenceParser from './ReferenceParser';
import { downloadSingleQueryPDF } from '@/lib/pdfExport';

import { API_BASE_URL } from '@/lib/api';
import { PromptCatalog } from '@/data/prompts';

/**
 * ChatBox component refactored to use the PromptGallery component.
 * The slide‑out prompts gallery is encapsulated in <PromptGallery />.
 */
export default function ChatBox() {
  const {
    messages,
    setMessages,
    selectedAgent,
    isLoading,
    isLoadingSession,
    setIsLoadingSession,
    isGeneratingFollowups,
    stopProcessing,
    useDeepSearch,
    sendMessage,
    availableFiles,
    currentSessionId,
  } = useChat();

  // Ref for scrolling to the bottom of the chat transcript
  const messagesEndRef = useRef<HTMLDivElement>(null);
const [isMobile, setIsMobile] = useState(false);
 // Detect mobile once and on resize
 useEffect(() => {
  const detect = () => setIsMobile(window.innerWidth < 768);
  detect();
  window.addEventListener('resize', detect);
  return () => window.removeEventListener('resize', detect);
}, []);
  // Local state for the current message input (used when clicking a prompt)
  const [message, setMessage] = useState('');
  const [notesModalOpen, setNotesModalOpen] = useState(false);
  const [notesPreset, setNotesPreset] = useState<{
    responseId?: string;
    interactionId?: number;
    title?: string;
    combinedText?: string;
    sources?: any;
    agentType?: string;
    sessionId?: string;
  } | null>(null);

  // Auto scroll to bottom on new messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);
  

  // Markdown link renderer for opening links in new tabs.
  const customRenderers = {
    a: ({ node, ...props }: any) => (
      <a
        {...props}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-900 hover:underline"
      />
    ),
  };

  // Provide no mode-awareness; PromptGallery lets users filter categories
  const samplePrompts = PromptCatalog.general;

  // Handle a prompt picked from the gallery
  const handlePromptSelect = (prompt: string) => {
    // Dispatch event to populate the input field without sending
    window.dispatchEvent(new CustomEvent('populate-chat-input', {
      detail: { message: prompt }
    }));
  };



  // Build the chat content based on current state.
  let chatContent: React.ReactNode;
  if (!selectedAgent) {
    chatContent = (
      <div className="h-full flex items-center justify-center">
      <LoadingSpinner size="md" message="Loading agent..." />
    </div>
    );
  } else if (isLoadingSession) {
    chatContent = (
      <div className="h-full flex items-center justify-center">
        <LoadingSpinner size="md" message="Loading chat session..." />
      </div>
    );
  } else if (messages.length === 0) {
    chatContent = (
      <div className="flex flex-col items-center justify-center h-full text-center space-y-2">
        <h2 className="font-semibold text-4xl mb-2 text-black">{selectedAgent.name}</h2>
        <p className="text-gray-500 max-w-sm">
          Start a conversation with the {selectedAgent.name}. You can ask about AI trends,
          request document summaries, perform web searches or deep searches.
        </p>
      </div>
    );
  } else {
    chatContent = (
      <>
           {messages.map((m, index) => (
          <div key={index} className={`flex ${m.role === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div
              className={`max-w-[100%] sm:max-w-[80%] px-2 py-3 rounded-[16px] ${m.role === 'user' ? 'bg-blue-500 text-white' : 'bg-transparent text-gray-800'}`}
              >
              {m.role === 'assistant' ? (
                <div>
                  <ReferenceParser 
                    content={m.content}
                    customRenderers={customRenderers}
                  />
                  <SourcesDisplay
                    sources={m.sources || []}
                    isWebSearch={m.webSearchEnabled || false}
                    isDocumentSearch={m.documentSearchEnabled || false}
                    isDeepSearch={m.deepSearchEnabled || false}
                  />
                  <div className="flex items-center gap-2 mt-2">
                    <FeedbackWidget
                      responseId={m.responseId || ''}
                      query={messages[index - 1]?.content || ''}
                      response={m.content}
                      agentType={selectedAgent?.id || 'general'}
                      responseData={{
                        agent_name: selectedAgent?.name || 'AI Assistant',
                        query: messages[index - 1]?.content || '',
                        answer: m.content,
                        web_search_enabled: m.webSearchEnabled || false,
                        document_search_enabled: m.documentSearchEnabled || false,
                        deep_search_enabled: m.deepSearchEnabled || false,
                        sources: m.sources || [],
                        reasoning: m.reasoning || [],
                        progress_messages: m.progressMessages || [],
                      }}
                      onOpenNotes={() => {
                        const combinedText = [
                          messages[index - 1]?.content ? `Query:\n${messages[index - 1]?.content}` : '',
                          m.content ? `Response:\n${m.content}` : ''
                        ].filter(Boolean).join('\n\n');

                        setNotesPreset({
                          responseId: m.responseId,
                          title: messages[index - 1]?.content || m.content,
                          combinedText,
                          sources: m.sources,
                          agentType: selectedAgent?.id,
                        });
                        setNotesModalOpen(true);
                      }}
                    />
                    {/* Copy Response Button */}
                    <button
                      onClick={async () => {
                        try {
                          await navigator.clipboard.writeText(m.content);
                        } catch (error) {
                          console.error('Failed to copy text:', error);
                        }
                      }}
                      className="inline-flex items-center justify-center w-8 h-8 rounded-md bg-transparent hover:bg-gray-50 transition-colors"
                      title="Copy response"
                      aria-label="Copy response to clipboard"
                    >
                      <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </button>
                    {/* Export as PDF Button */}
                    <button
                      onClick={() => {
                        try {
                          const query = messages[index - 1]?.content || '';
                          const response = m.content;
                          const agentName = selectedAgent?.name || 'AI Assistant';
                          downloadSingleQueryPDF(query, response, agentName);
                        } catch (error) {
                          console.error('Failed to export PDF:', error);
                        }
                      }}
                      className="inline-flex items-center justify-center w-8 h-8 rounded-md bg-transparent hover:bg-gray-50 transition-colors"
                      title="Export as PDF"
                      aria-label="Export query and response as PDF"
                    >
                      <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </button>
                  </div>
                  {Array.isArray((m as any).followUps) && (m as any).followUps.length > 0 && (
                    <div className="mt-3 flex flex-wrap gap-2">
                      {(m as any).followUps.slice(0, 5).map((s: string, i: number) => (
                        <button
                          key={i}
                          onClick={() => handlePromptSelect(s)}
                          className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full border border-gray-300 bg-white text-[12px] text-gray-700 hover:text-blue-700 hover:border-blue-300 hover:bg-blue-50 transition"
                          title="Ask this follow-up"
                          aria-label="Ask follow-up"
                        >
                          <span className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-blue-50 text-blue-900 text-[10px]">?</span>
                          <span>{s}</span>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-sm">{m.content}</p>
              )}
            </div>
          </div>
        ))}
        {isLoading && (
          <div className="flex justify-start">
            <div className="max-w-[80%] px-4 py-4">
              <LoadingSpinner 
                size="sm" 
                message={useDeepSearch ? 'Processing your request...' : 'Processing...'}
              />
              
              {/* Additional info for deep search */}
              {useDeepSearch && (
                <div className="mt-2 text-xs text-gray-500 max-w-md">
                  Gathering comprehensive information to provide you with detailed results.
                </div>
              )}
            </div>
          </div>
        )}
        {isGeneratingFollowups && (
          <div className="flex justify-start">
            <div className="max-w-[80%] px-4 py-4">
              {/* <LoadingIndicator 
                text="Generating follow-up questions"
                size="small"
              /> */}
              {/* <div className="mt-2 text-xs text-gray-500 max-w-md">
                Creating related questions to help continue the conversation.
              </div> */}
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </>
    );
  }

  return (
    <div className="h-full bg-white relative flex flex-col">
      {/* Chat History Manager - handles loading/saving chat history */}
      <ChatHistoryManager
        selectedAgent={selectedAgent}
        currentSessionId={currentSessionId}
        setMessages={setMessages}
        onLoadingStateChange={setIsLoadingSession}
      />

      {/* Prompt gallery (floating button + slide-out panel) */}
      <PromptGallery prompts={samplePrompts} onSelect={handlePromptSelect} />

      {/* Chat transcript or placeholder */}
      <div className="flex-1 p-4 overflow-y-auto bg-gray-200 relative">

        <div className="rounded-lg pt-4 space-y-4 max-w-7xl mx-auto lg:px-8 h-full">
          {chatContent}
        </div>
      </div>

      {/* Notes modal */}
      <NotesModal
        isOpen={notesModalOpen}
        onClose={() => setNotesModalOpen(false)}
        preset={notesPreset || undefined}
        onSaved={() => {
          try {
            // Trigger a refresh of the notes panel so the newly created note appears immediately
            window.dispatchEvent(new Event('refresh-notes'));
          } catch {}
        }}
      />
    </div>
  );
}
