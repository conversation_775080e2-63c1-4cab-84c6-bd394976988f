/**
 * API client for interacting with the company assistant backend
 */

// Define API base URL with fallback - ALWAYS use HTTPS

export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL_Dev;


// Types
export interface Agent {
  id: string;
  name: string;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface SearchLink {
  title: string;
  url: string;
}

export interface SearchSource {
  title: string;
  url: string;
  snippet: string;
  source_type: string;
}

export interface DocumentChunkSource {
  // Optional raw_document ID when available for deeper linking
  id?: string;
  // Original filename or document identifier
  document: string;
  // Page number if available from metadata
  page_number?: number | null;
  // Text snippet from the chunk
  snippet: string;
  // Confidence score from reranking (0.0 - 1.0)
  confidence_score?: number;
}

export interface QueryRequest {
  agent_type: string;
  query: string;
  chat_history?: {
    messages: ChatMessage[];
  };
  use_web_search?: boolean;
  use_document_search?: boolean;
  use_deep_search?: boolean;
  chat_session_id?: string;
  query_session_id?: string;
  allowed_files?: string[];
  space_id?: number; // For querying specific shared spaces
}

export interface QueryResponse {
  agent_name: string;
  query: string;
  answer: string;
  web_search_enabled: boolean;
  document_search_enabled: boolean;
  deep_search_enabled: boolean;
  sources?: SearchSource[][] | string[] | DocumentChunkSource[]; // Web: nested objects, Document: chunk array, legacy: filenames
  selected_files?: string[];
  used_rag?: boolean;
  processing_stats?: Record<string, any>;
  reasoning?: Array<{agent: string; reasoning: string}>;
  progress_messages?: string[];
  error?: string;
  
  // New fields for feedback and caching
  response_id?: string;  // UUID for tracking feedback
  cached?: boolean;      // Whether response came from cache
  cache_confidence?: number; // Confidence score for cached responses
  suggested_followups?: string[]; // Follow-up question suggestions
  
  // Context metadata for token usage tracking
  context_meta?: any;
}

export interface FollowupsRequest {
  chat_history: { messages: ChatMessage[] };
  latest_query: string;
  latest_answer: string;
  latest_sources?: any[];  // Sources from the latest response
  response_id: string;  // To identify which interaction to update in DB
}

/**
 * Generate follow-up suggestions based on chat history and latest exchange
 */
export async function generateFollowups(request: FollowupsRequest): Promise<string[]> {
  const response = await fetch(`${API_BASE_URL}/agents/followups`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify(request),
  });
  if (!response.ok) {
    console.error('Failed to generate follow-ups:', response.status);
    return [];
  }
  return await response.json();
}

/**
 * Check if JWT token is expired
 */
const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch {
    return true;
  }
};

/**
 * Get secure token with validation
 */
export const getSecureToken = (): string | null => {
  try {
    const token = localStorage.getItem('access_token');
    if (!token || isTokenExpired(token)) {
      // Clear invalid token
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      return null;
    }
    return token;
  } catch {
    return null;
  }
};

/**
 * Get auth headers with secure token validation
 */
export const getAuthHeaders = () => {
  const token = getSecureToken();
  if (!token) {
    throw new Error('No valid authentication token');
  }
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };
};

/**
 * Fetch available agents from the API
 */
export async function getAgents(): Promise<Agent[]> {
  try {
    const response = await fetch(`${API_BASE_URL}/agents/`, {
      headers: getAuthHeaders()
    });
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    
    const data = await response.json();
    return data.agents;
  } catch (error) {
    console.error('Error fetching agents:', error);
    throw error;
  }
}

/**
 * Send a query to a specific agent
 */
export async function queryAgent(request: QueryRequest, signal?: AbortSignal): Promise<QueryResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/agents/query`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(request),
      signal,
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API error: ${response.status} - ${errorData.detail || 'Unknown error'}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error querying agent:', error);
    throw error;
  }
}

/**
 * Get chat history for a specific agent
 */
export async function getAgentChatHistory(agentType: string, chatSessionId: string = 'default'): Promise<{agent_type: string, chat_session_id: string, messages: Array<{
  role: string, 
  content: string, 
  timestamp?: string, 
  response_id?: string,
  sources?: any[],
  web_search_enabled?: boolean,
  document_search_enabled?: boolean,
  deep_search_enabled?: boolean,
  reasoning?: Array<{agent: string; reasoning: string}>,
  progress_messages?: string[],
  cached?: boolean,
  cache_confidence?: number,
  agent_name?: string,
  used_rag?: boolean,
  processing_stats?: any
}>}> {
  try {
    const response = await fetch(`${API_BASE_URL}/agents/chat-history/${agentType}?chat_session_id=${chatSessionId}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API error: ${response.status} - ${errorData.detail || 'Unknown error'}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error getting agent chat history:', error);
    throw error;
  }
}

/**
 * Clear chat history for a specific agent
 */
export async function clearAgentChatHistory(agentType: string, chatSessionId: string = 'default'): Promise<{message: string}> {
  try {
    const response = await fetch(`${API_BASE_URL}/agents/chat-history/${agentType}?chat_session_id=${chatSessionId}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API error: ${response.status} - ${errorData.detail || 'Unknown error'}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error clearing agent chat history:', error);
    throw error;
  }
}

/**
 * Get all chat sessions for a specific agent
 */
export async function getAgentChatSessions(agentType: string, forceRefresh: boolean = false): Promise<{
  agent_type: string,
  sessions: Array<{
    session_id: string,
    title: string,
    message_count: number,
    last_updated?: string
  }>
}> {
  try {
    const url = `${API_BASE_URL}/agents/chat-sessions/${agentType}${forceRefresh ? '?force_refresh=true' : ''}`;
    //console.log(`Fetching chat sessions from: ${url}`);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API error: ${response.status} - ${errorData.detail || 'Unknown error'}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error getting agent chat sessions:', error);
    throw error;
  }
}

// =============================================================================
// KNOWLEDGE BASE & SPACES - MOVED TO @/api/knowledge-base
// =============================================================================
// The following functions have been moved to frontend/src/api/knowledge-base.ts
// Please import from '@/api' instead of '@/lib/api' for these functions.
// =============================================================================

// All Knowledge Base types are re-exported from @/types/knowledge-base (see bottom of file)

// Dashboard API interfaces
export interface UserMetrics {
  ai_query_count: number;
  knowledge_docs_uploaded: number;
  knowledge_docs_indexed: number;
  feedback_submitted: number;
  feedback_helpful_count: number;
  feedback_avg_rating: number;
  ai_responses_shared: number;
  is_online: boolean;
  last_seen: string | null;
}

export interface UserWithMetrics {
  user_id: number;
  username: string;
  email: string;
  industry: string | null;
  role: string;
  created_at: string | null;
  metrics: UserMetrics;
  // Additional fields that might be returned by the API
  id?: number;
  is_active?: boolean;
  status?: string;
  updated_at?: string;
}

export interface DashboardSummary {
  users: {
    total: number;
    active: number;
    pending: number;
    approved: number;
    online: number;
  };
  ai_queries: {
    total: number;
    recent_week: number;
  };
  knowledge_docs: {
    total: number;
    indexed: number;
    recent_week: number;
  };
  feedback: {
    total: number;
    helpful: number;
    helpful_percentage: number;
  };
  shared_responses: {
    total: number;
  };
}

// Dashboard API functions
export async function getDashboardSummary(): Promise<DashboardSummary> {
  try {
    const response = await fetch(`${API_BASE_URL}/dashboard/summary-stats`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch dashboard summary: ${response.status}`);
    }

    const data = await response.json();
    return data.summary;
  } catch (error) {
    console.error('Error fetching dashboard summary:', error);
    throw error;
  }
}

export async function getAllUserMetrics(): Promise<UserWithMetrics[]> {
  try {
    const response = await fetch(`${API_BASE_URL}/dashboard/user-metrics`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user metrics: ${response.status}`);
    }

    const data = await response.json();
    return data.users;
  } catch (error) {
    console.error('Error fetching user metrics:', error);
    throw error;
  }
}

export async function getUserMetrics(userId: number): Promise<{ user: any; metrics: UserMetrics }> {
  try {
    const response = await fetch(`${API_BASE_URL}/dashboard/user-metrics/${userId}`, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user metrics: ${response.status}`);
    }

    const data = await response.json();
    return { user: data.user, metrics: data.metrics };
  } catch (error) {
    console.error('Error fetching user metrics:', error);
    throw error;
  }
} 

// User dashboard (me) endpoints
export interface MeSummary {
  chats_7d: number;
  chats_30d: number;
  notes_total: number;
}

export async function getMySummary(): Promise<MeSummary> {
  const response = await fetch(`${API_BASE_URL}/dashboard/me/summary`, {
    headers: getAuthHeaders(),
  });
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `Failed to fetch my summary: ${response.status}`);
  }
  return await response.json();
}

export async function getMyMetrics(): Promise<{ user: any; metrics: UserMetrics }> {
  const response = await fetch(`${API_BASE_URL}/dashboard/me/metrics`, {
    headers: getAuthHeaders(),
  });
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `Failed to fetch my metrics: ${response.status}`);
  }
  return await response.json();
}

export interface ActivityItem {
  type: 'chat' | 'note' | 'feedback';
  id: number;
  created_at: string;
  content_preview?: string | null;
  title?: string | null;
  action?: string | null;
  rating?: number | null;
  is_helpful?: boolean | null;
}

export interface ActivityFeedResponse {
  items: ActivityItem[];
  next_offset?: number | null;
}

export async function getMyActivity(params: { limit?: number; offset?: number } = {}): Promise<ActivityFeedResponse> {
  const query = new URLSearchParams();
  if (params.limit) query.set('limit', String(params.limit));
  if (params.offset) query.set('offset', String(params.offset));
  const url = `${API_BASE_URL}/dashboard/me/activity${query.toString() ? `?${query.toString()}` : ''}`;
  const response = await fetch(url, { headers: getAuthHeaders() });
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `Failed to fetch my activity: ${response.status}`);
  }
  return await response.json();
}
  
export async function fetchUser(username: string): Promise<UserWithMetrics | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/chat/find-user/${username}`, {
      headers: getAuthHeaders(),
    });
    if (response.status === 404) {
      // User not found, return null instead of throwing an error
      return null;
    }
    if (!response.ok) {
      throw new Error(`Failed to find user: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Error finding user:', error);
    throw error;
  }
}

/**
 * Get context metadata for a chat session
 */
export const getContextMeta = async (
  agentType: string,
  chatSessionId: string
): Promise<{ context_meta: any | null }> => {
  const params = new URLSearchParams({
    agent_type: agentType,
    chat_session_id: chatSessionId
  });

  const res = await fetch(`${API_BASE_URL}/agents/context-meta?${params}`, {
    method: 'GET',
    headers: getAuthHeaders()
  });

  if (!res.ok) {
    const text = await res.text().catch(() => '');
    throw new Error(`Failed to get context meta: ${res.status} ${text}`);
  }

  return res.json();
};

