from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class QueryCreate(BaseModel):
    query: str = Field(..., min_length=1)
    response: str = Field(..., min_length=1)
    source: Optional[str] = Field(None, max_length=50)
    category: Optional[str] = Field(None, max_length=50)
    confidence: Optional[float] = Field(0.0, ge=0.0, le=1.0)

class QueryResponse(BaseModel):
    id: int
    query: str
    response: str
    source: Optional[str]
    category: Optional[str]
    confidence: float
    usage_count: int
    created_at: datetime
    last_used: datetime

    class Config:
        from_attributes = True

class ChatQuery(BaseModel):
    message: str = Field(..., min_length=1)
    context: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    source: str
    confidence: float
    response_id: str 