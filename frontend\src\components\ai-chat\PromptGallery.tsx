import React, { useEffect, useRef, useState } from 'react';
import { PromptCatalog } from '@/data/prompts';

// Category metadata
const CATEGORIES = [
  { key: 'general', label: 'General' },
  { key: 'document', label: 'Document' },
  { key: 'web', label: 'Web Search' },
  { key: 'deep', label: 'Deep Web Search' },
] as const;

interface Prompt {
  text: string;
}

interface PromptGalleryProps {
  prompts: Prompt[];
  onSelect: (prompt: string) => void;
  /**
   * Optional: start open
   */
  defaultOpen?: boolean;
}

/**
 * PromptGallery
 * A self-contained slide-out panel with a floating trigger button.
 * - Handles outside-click to close.
 * - Calls `onSelect` when a prompt is chosen.
 */
export default function PromptGallery({ prompts, onSelect, defaultOpen = false }: PromptGalleryProps) {
  const [open, setOpen] = useState(defaultOpen);
  const panelRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [panelWidth, setPanelWidth] = useState<number>(380);
  const [isResizing, setIsResizing] = useState<boolean>(false);
  const [isDesktop, setIsDesktop] = useState<boolean>(false);
  const hasCustom = prompts.length > 0;
  const [category, setCategory] = useState<string>(hasCustom ? 'custom' : 'general');
  useEffect(() => {
    setCategory(hasCustom ? 'custom' : 'general');
  }, [hasCustom]);
  
  // Catalog mapping
  const catalogMap: Record<string, Prompt[]> = {
    general: PromptCatalog.general,
    document: PromptCatalog.document,
    web: PromptCatalog.web,
    deep: PromptCatalog.deep,
  } as unknown as Record<string, Prompt[]>;
  const displayPrompts: Prompt[] = category === 'custom' && hasCustom
    ? prompts
    : (catalogMap[category] || PromptCatalog.general);
  
   // Allow external components to open the gallery via a custom event
  useEffect(() => {
    const handler = () => {
      //console.log('PromptGallery: received open-prompt-gallery event');
      setOpen(true);
    };
    window.addEventListener('open-prompt-gallery', handler as EventListener);
    return () => window.removeEventListener('open-prompt-gallery', handler as EventListener);
  }, []);

  // Close this sidebar when others open
  useEffect(() => {
    const handler = () => setOpen(false);
    window.addEventListener('close-prompt-gallery', handler as EventListener);
    return () => window.removeEventListener('close-prompt-gallery', handler as EventListener);
  }, []);
  
    // Close the panel if the user clicks outside of it (and not on the prompt button)
  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (!open) return;
      const target = event.target as Node;
      const panelEl = panelRef.current;
      const buttonEl = buttonRef.current;
      if (panelEl && !panelEl.contains(target) && buttonEl && !buttonEl.contains(target)) {
        setOpen(false);
        // Inform the page layout that the prompt gallery has closed
        window.dispatchEvent(new Event('close-prompt-gallery'));
      }
    };
    if (open) document.addEventListener('click', handleOutsideClick);
    return () => document.removeEventListener('click', handleOutsideClick);
  }, [open]);

  // Track viewport to switch between full width (mobile) and resizable (desktop)
  useEffect(() => {
    const updateViewport = () => setIsDesktop(window.innerWidth >= 768);
    updateViewport();
    window.addEventListener('resize', updateViewport);
    return () => window.removeEventListener('resize', updateViewport);
  }, []);

  // Drag to resize
  useEffect(() => {
    if (!isResizing) return;
    const onMouseMove = (e: MouseEvent) => {
      const next = Math.min(900, Math.max(320, e.clientX));
      setPanelWidth(next);
    };
    const onMouseUp = () => {
      setIsResizing(false);
      document.body.style.cursor = '';
      (document.body.style as any).userSelect = '';
    };
    document.body.style.cursor = 'col-resize';
    (document.body.style as any).userSelect = 'none';
    window.addEventListener('mousemove', onMouseMove);
    window.addEventListener('mouseup', onMouseUp);
    return () => {
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', onMouseUp);
    };
  }, [isResizing]);

  const handlePromptClick = (prompt: Prompt) => {
    onSelect(prompt.text);
    setOpen(false);
    // Ensure global layout state is updated
    window.dispatchEvent(new Event('close-prompt-gallery'));
  };
  

  return (
    <>
    
      {/* Sliding panel - full width on mobile, responsive on desktop */}
      <div
        ref={panelRef}
        className={`fixed top-[5rem] left-0 h-[calc(100%-5rem)] bg-gradient-to-br from-slate-50 to-blue-50 backdrop-blur-sm shadow-2xl border-r border-slate-200 z-40 transition-all duration-500 ease-out overflow-hidden ${open ? '' : 'w-0'}`}
        style={{ width: open ? (isDesktop ? panelWidth : '100%') : 0 }}
      >
        <div className="flex items-center justify-between p-6 border-b border-slate-200/50 bg-gray-300 text-black/80">
          <div className="flex flex-col items-start space-y-1">
            <h3 className="text-xl font-bold tracking-wide">Prompts Gallery</h3>
          </div>
          <button
            onClick={() => {
              setOpen(false);
              window.dispatchEvent(new Event('close-prompt-gallery'));
            }}
            className="w-8 h-8 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/30"
            aria-label="Close Prompts Gallery"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        
        <div className="flex flex-col h-[calc(100%-6rem)]">
          <div className="flex-shrink-0 p-6 pb-4">
            <div className="flex flex-wrap gap-2 mb-3">
              {/* {hasCustom && (
                <button
                  onClick={() => setCategory('custom')}
                  className={`px-3 py-1.5 rounded-full text-sm border transition ${category === 'custom' ? 'bg-blue-900 text-white border-blue-900' : 'bg-white text-slate-700 border-slate-300 hover:border-blue-300 hover:text-blue-700'}`}
                  aria-label="Show custom prompts"
                >
                  Custom
                </button>
              )} */}
              {CATEGORIES.map(c => (
                <button
                  key={c.key}
                  onClick={() => setCategory(c.key)}
                  className={`px-3 py-1.5 rounded-full text-sm border transition ${category === c.key ? 'bg-blue-900 text-white border-blue-900' : 'bg-white text-slate-700 border-slate-300 hover:border-blue-300 hover:text-blue-700'}`}
                  aria-label={`Show ${c.label} prompts`}
                >
                  {c.label}
                </button>
              ))}
            </div>
            <div className="text-xs text-slate-600">
              Tip: Click a prompt to add it to your message. Tweak names, dates, or context before you send.
            </div>
          </div>
          <div className="flex-1 overflow-y-auto px-6 pb-8">
            <div className="space-y-4 pb-4">
              {displayPrompts.map((prompt, index) => (
                <div 
                  key={index}
                  onClick={() => handlePromptClick(prompt)}
                  className="group relative bg-white/80 backdrop-blur-sm border border-slate-200/50 rounded-xl shadow-sm p-4 cursor-pointer hover:bg-white hover:shadow-lg hover:shadow-blue-100/50 hover:border-blue-300/50 hover:-translate-y-0.5 transition-all duration-300 ease-out"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 to-purple-50/50 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-start space-x-3">
                    
                    <p className="text-slate-700 font-medium leading-relaxed group-hover:text-slate-900 transition-colors duration-200">{prompt.text}</p>
                  </div>
                  <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        {/* Resize handle - desktop only */}
        <div
          className="hidden md:block absolute top-0 right-0 h-full w-2 cursor-col-resize bg-transparent hover:bg-gradient-to-b hover:from-blue-200 hover:to-purple-200 transition-all duration-200 group"
          onMouseDown={() => setIsResizing(true)}
          aria-label="Resize prompts panel"
        >
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-8 bg-slate-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
        </div>
      </div>
    </>
  );
}
