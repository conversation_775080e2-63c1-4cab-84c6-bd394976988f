/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mqtt-packet";
exports.ids = ["vendor-chunks/mqtt-packet"];
exports.modules = {

/***/ "(ssr)/./node_modules/mqtt-packet/constants.js":
/*!***********************************************!*\
  !*** ./node_modules/mqtt-packet/constants.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* Protocol - protocol constants */\nconst protocol = module.exports\nconst { Buffer } = __webpack_require__(/*! buffer */ \"buffer\")\n\n/* Command code => mnemonic */\nprotocol.types = {\n  0: 'reserved',\n  1: 'connect',\n  2: 'connack',\n  3: 'publish',\n  4: 'puback',\n  5: 'pubrec',\n  6: 'pubrel',\n  7: 'pubcomp',\n  8: 'subscribe',\n  9: 'suback',\n  10: 'unsubscribe',\n  11: 'unsuback',\n  12: 'pingreq',\n  13: 'pingresp',\n  14: 'disconnect',\n  15: 'auth'\n}\n\nprotocol.requiredHeaderFlags = {\n  1: 0, // 'connect'\n  2: 0, // 'connack'\n  4: 0, // 'puback'\n  5: 0, // 'pubrec'\n  6: 2, // 'pubrel'\n  7: 0, // 'pubcomp'\n  8: 2, // 'subscribe'\n  9: 0, // 'suback'\n  10: 2, // 'unsubscribe'\n  11: 0, // 'unsuback'\n  12: 0, // 'pingreq'\n  13: 0, // 'pingresp'\n  14: 0, // 'disconnect'\n  15: 0 // 'auth'\n}\n\nprotocol.requiredHeaderFlagsErrors = {}\nfor (const k in protocol.requiredHeaderFlags) {\n  const v = protocol.requiredHeaderFlags[k]\n  protocol.requiredHeaderFlagsErrors[k] = 'Invalid header flag bits, must be 0x' + v.toString(16) + ' for ' + protocol.types[k] + ' packet'\n}\n\n/* Mnemonic => Command code */\nprotocol.codes = {}\nfor (const k in protocol.types) {\n  const v = protocol.types[k]\n  protocol.codes[v] = k\n}\n\n/* Header */\nprotocol.CMD_SHIFT = 4\nprotocol.CMD_MASK = 0xF0\nprotocol.DUP_MASK = 0x08\nprotocol.QOS_MASK = 0x03\nprotocol.QOS_SHIFT = 1\nprotocol.RETAIN_MASK = 0x01\n\n/* Length */\nprotocol.VARBYTEINT_MASK = 0x7F\nprotocol.VARBYTEINT_FIN_MASK = 0x80\nprotocol.VARBYTEINT_MAX = 268435455\n\n/* Connack */\nprotocol.SESSIONPRESENT_MASK = 0x01\nprotocol.SESSIONPRESENT_HEADER = Buffer.from([protocol.SESSIONPRESENT_MASK])\nprotocol.CONNACK_HEADER = Buffer.from([protocol.codes.connack << protocol.CMD_SHIFT])\n\n/* Connect */\nprotocol.USERNAME_MASK = 0x80\nprotocol.PASSWORD_MASK = 0x40\nprotocol.WILL_RETAIN_MASK = 0x20\nprotocol.WILL_QOS_MASK = 0x18\nprotocol.WILL_QOS_SHIFT = 3\nprotocol.WILL_FLAG_MASK = 0x04\nprotocol.CLEAN_SESSION_MASK = 0x02\nprotocol.CONNECT_HEADER = Buffer.from([protocol.codes.connect << protocol.CMD_SHIFT])\n\n/* Properties */\nprotocol.properties = {\n  sessionExpiryInterval: 17,\n  willDelayInterval: 24,\n  receiveMaximum: 33,\n  maximumPacketSize: 39,\n  topicAliasMaximum: 34,\n  requestResponseInformation: 25,\n  requestProblemInformation: 23,\n  userProperties: 38,\n  authenticationMethod: 21,\n  authenticationData: 22,\n  payloadFormatIndicator: 1,\n  messageExpiryInterval: 2,\n  contentType: 3,\n  responseTopic: 8,\n  correlationData: 9,\n  maximumQoS: 36,\n  retainAvailable: 37,\n  assignedClientIdentifier: 18,\n  reasonString: 31,\n  wildcardSubscriptionAvailable: 40,\n  subscriptionIdentifiersAvailable: 41,\n  sharedSubscriptionAvailable: 42,\n  serverKeepAlive: 19,\n  responseInformation: 26,\n  serverReference: 28,\n  topicAlias: 35,\n  subscriptionIdentifier: 11\n}\nprotocol.propertiesCodes = {}\nfor (const prop in protocol.properties) {\n  const id = protocol.properties[prop]\n  protocol.propertiesCodes[id] = prop\n}\nprotocol.propertiesTypes = {\n  sessionExpiryInterval: 'int32',\n  willDelayInterval: 'int32',\n  receiveMaximum: 'int16',\n  maximumPacketSize: 'int32',\n  topicAliasMaximum: 'int16',\n  requestResponseInformation: 'byte',\n  requestProblemInformation: 'byte',\n  userProperties: 'pair',\n  authenticationMethod: 'string',\n  authenticationData: 'binary',\n  payloadFormatIndicator: 'byte',\n  messageExpiryInterval: 'int32',\n  contentType: 'string',\n  responseTopic: 'string',\n  correlationData: 'binary',\n  maximumQoS: 'int8',\n  retainAvailable: 'byte',\n  assignedClientIdentifier: 'string',\n  reasonString: 'string',\n  wildcardSubscriptionAvailable: 'byte',\n  subscriptionIdentifiersAvailable: 'byte',\n  sharedSubscriptionAvailable: 'byte',\n  serverKeepAlive: 'int16',\n  responseInformation: 'string',\n  serverReference: 'string',\n  topicAlias: 'int16',\n  subscriptionIdentifier: 'var'\n}\n\nfunction genHeader (type) {\n  return [0, 1, 2].map(qos => {\n    return [0, 1].map(dup => {\n      return [0, 1].map(retain => {\n        const buf = Buffer.alloc(1)\n        buf.writeUInt8(\n          protocol.codes[type] << protocol.CMD_SHIFT |\n          (dup ? protocol.DUP_MASK : 0) |\n          qos << protocol.QOS_SHIFT | retain, 0, true)\n        return buf\n      })\n    })\n  })\n}\n\n/* Publish */\nprotocol.PUBLISH_HEADER = genHeader('publish')\n\n/* Subscribe */\nprotocol.SUBSCRIBE_HEADER = genHeader('subscribe')\nprotocol.SUBSCRIBE_OPTIONS_QOS_MASK = 0x03\nprotocol.SUBSCRIBE_OPTIONS_NL_MASK = 0x01\nprotocol.SUBSCRIBE_OPTIONS_NL_SHIFT = 2\nprotocol.SUBSCRIBE_OPTIONS_RAP_MASK = 0x01\nprotocol.SUBSCRIBE_OPTIONS_RAP_SHIFT = 3\nprotocol.SUBSCRIBE_OPTIONS_RH_MASK = 0x03\nprotocol.SUBSCRIBE_OPTIONS_RH_SHIFT = 4\nprotocol.SUBSCRIBE_OPTIONS_RH = [0x00, 0x10, 0x20]\nprotocol.SUBSCRIBE_OPTIONS_NL = 0x04\nprotocol.SUBSCRIBE_OPTIONS_RAP = 0x08\nprotocol.SUBSCRIBE_OPTIONS_QOS = [0x00, 0x01, 0x02]\n\n/* Unsubscribe */\nprotocol.UNSUBSCRIBE_HEADER = genHeader('unsubscribe')\n\n/* Confirmations */\nprotocol.ACKS = {\n  unsuback: genHeader('unsuback'),\n  puback: genHeader('puback'),\n  pubcomp: genHeader('pubcomp'),\n  pubrel: genHeader('pubrel'),\n  pubrec: genHeader('pubrec')\n}\n\nprotocol.SUBACK_HEADER = Buffer.from([protocol.codes.suback << protocol.CMD_SHIFT])\n\n/* Protocol versions */\nprotocol.VERSION3 = Buffer.from([3])\nprotocol.VERSION4 = Buffer.from([4])\nprotocol.VERSION5 = Buffer.from([5])\nprotocol.VERSION131 = Buffer.from([131])\nprotocol.VERSION132 = Buffer.from([132])\n\n/* QoS */\nprotocol.QOS = [0, 1, 2].map(qos => {\n  return Buffer.from([qos])\n})\n\n/* Empty packets */\nprotocol.EMPTY = {\n  pingreq: Buffer.from([protocol.codes.pingreq << 4, 0]),\n  pingresp: Buffer.from([protocol.codes.pingresp << 4, 0]),\n  disconnect: Buffer.from([protocol.codes.disconnect << 4, 0])\n}\n\nprotocol.MQTT5_PUBACK_PUBREC_CODES = {\n  0x00: 'Success',\n  0x10: 'No matching subscribers',\n  0x80: 'Unspecified error',\n  0x83: 'Implementation specific error',\n  0x87: 'Not authorized',\n  0x90: 'Topic Name invalid',\n  0x91: 'Packet identifier in use',\n  0x97: 'Quota exceeded',\n  0x99: 'Payload format invalid'\n}\n\nprotocol.MQTT5_PUBREL_PUBCOMP_CODES = {\n  0x00: 'Success',\n  0x92: 'Packet Identifier not found'\n}\n\nprotocol.MQTT5_SUBACK_CODES = {\n  0x00: 'Granted QoS 0',\n  0x01: 'Granted QoS 1',\n  0x02: 'Granted QoS 2',\n  0x80: 'Unspecified error',\n  0x83: 'Implementation specific error',\n  0x87: 'Not authorized',\n  0x8F: 'Topic Filter invalid',\n  0x91: 'Packet Identifier in use',\n  0x97: 'Quota exceeded',\n  0x9E: 'Shared Subscriptions not supported',\n  0xA1: 'Subscription Identifiers not supported',\n  0xA2: 'Wildcard Subscriptions not supported'\n}\n\nprotocol.MQTT5_UNSUBACK_CODES = {\n  0x00: 'Success',\n  0x11: 'No subscription existed',\n  0x80: 'Unspecified error',\n  0x83: 'Implementation specific error',\n  0x87: 'Not authorized',\n  0x8F: 'Topic Filter invalid',\n  0x91: 'Packet Identifier in use'\n}\n\nprotocol.MQTT5_DISCONNECT_CODES = {\n  0x00: 'Normal disconnection',\n  0x04: 'Disconnect with Will Message',\n  0x80: 'Unspecified error',\n  0x81: 'Malformed Packet',\n  0x82: 'Protocol Error',\n  0x83: 'Implementation specific error',\n  0x87: 'Not authorized',\n  0x89: 'Server busy',\n  0x8B: 'Server shutting down',\n  0x8D: 'Keep Alive timeout',\n  0x8E: 'Session taken over',\n  0x8F: 'Topic Filter invalid',\n  0x90: 'Topic Name invalid',\n  0x93: 'Receive Maximum exceeded',\n  0x94: 'Topic Alias invalid',\n  0x95: 'Packet too large',\n  0x96: 'Message rate too high',\n  0x97: 'Quota exceeded',\n  0x98: 'Administrative action',\n  0x99: 'Payload format invalid',\n  0x9A: 'Retain not supported',\n  0x9B: 'QoS not supported',\n  0x9C: 'Use another server',\n  0x9D: 'Server moved',\n  0x9E: 'Shared Subscriptions not supported',\n  0x9F: 'Connection rate exceeded',\n  0xA0: 'Maximum connect time',\n  0xA1: 'Subscription Identifiers not supported',\n  0xA2: 'Wildcard Subscriptions not supported'\n}\n\nprotocol.MQTT5_AUTH_CODES = {\n  0x00: 'Success',\n  0x18: 'Continue authentication',\n  0x19: 'Re-authenticate'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mqtt-packet/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mqtt-packet/generate.js":
/*!**********************************************!*\
  !*** ./node_modules/mqtt-packet/generate.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const writeToStream = __webpack_require__(/*! ./writeToStream */ \"(ssr)/./node_modules/mqtt-packet/writeToStream.js\")\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\")\nconst { Buffer } = __webpack_require__(/*! buffer */ \"buffer\")\n\nfunction generate (packet, opts) {\n  const stream = new Accumulator()\n  writeToStream(packet, stream, opts)\n  return stream.concat()\n}\n\nclass Accumulator extends EventEmitter {\n  constructor () {\n    super()\n    this._array = new Array(20)\n    this._i = 0\n  }\n\n  write (chunk) {\n    this._array[this._i++] = chunk\n    return true\n  }\n\n  concat () {\n    let length = 0\n    const lengths = new Array(this._array.length)\n    const list = this._array\n    let pos = 0\n    let i\n\n    for (i = 0; i < list.length && list[i] !== undefined; i++) {\n      if (typeof list[i] !== 'string') lengths[i] = list[i].length\n      else lengths[i] = Buffer.byteLength(list[i])\n\n      length += lengths[i]\n    }\n\n    const result = Buffer.allocUnsafe(length)\n\n    for (i = 0; i < list.length && list[i] !== undefined; i++) {\n      if (typeof list[i] !== 'string') {\n        list[i].copy(result, pos)\n        pos += lengths[i]\n      } else {\n        result.write(list[i], pos)\n        pos += lengths[i]\n      }\n    }\n\n    return result\n  }\n\n  destroy (err) {\n    if (err) this.emit('error', err)\n  }\n}\n\nmodule.exports = generate\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mqtt-packet/generate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mqtt-packet/mqtt.js":
/*!******************************************!*\
  !*** ./node_modules/mqtt-packet/mqtt.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.parser = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/mqtt-packet/parser.js\").parser\nexports.generate = __webpack_require__(/*! ./generate */ \"(ssr)/./node_modules/mqtt-packet/generate.js\")\nexports.writeToStream = __webpack_require__(/*! ./writeToStream */ \"(ssr)/./node_modules/mqtt-packet/writeToStream.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXF0dC1wYWNrZXQvbXF0dC5qcyIsIm1hcHBpbmdzIjoiQUFBQSx5R0FBMkM7QUFDM0Msd0dBQXdDO0FBQ3hDLHVIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21xdHQtcGFja2V0L21xdHQuanM/YTE5ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnRzLnBhcnNlciA9IHJlcXVpcmUoJy4vcGFyc2VyJykucGFyc2VyXG5leHBvcnRzLmdlbmVyYXRlID0gcmVxdWlyZSgnLi9nZW5lcmF0ZScpXG5leHBvcnRzLndyaXRlVG9TdHJlYW0gPSByZXF1aXJlKCcuL3dyaXRlVG9TdHJlYW0nKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mqtt-packet/mqtt.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mqtt-packet/numbers.js":
/*!*********************************************!*\
  !*** ./node_modules/mqtt-packet/numbers.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { Buffer } = __webpack_require__(/*! buffer */ \"buffer\")\nconst max = 65536\nconst cache = {}\n\n// in node 6 Buffer.subarray returns a Uint8Array instead of a Buffer\n// later versions return a Buffer\n// alternative is Buffer.slice but that creates a new buffer\n// creating new buffers takes time\n// SubOk is only false on node < 8\nconst SubOk = Buffer.isBuffer(Buffer.from([1, 2]).subarray(0, 1))\n\nfunction generateBuffer (i) {\n  const buffer = Buffer.allocUnsafe(2)\n  buffer.writeUInt8(i >> 8, 0)\n  buffer.writeUInt8(i & 0x00FF, 0 + 1)\n\n  return buffer\n}\n\nfunction generateCache () {\n  for (let i = 0; i < max; i++) {\n    cache[i] = generateBuffer(i)\n  }\n}\n\nfunction genBufVariableByteInt (num) {\n  const maxLength = 4 // max 4 bytes\n  let digit = 0\n  let pos = 0\n  const buffer = Buffer.allocUnsafe(maxLength)\n\n  do {\n    digit = num % 128 | 0\n    num = num / 128 | 0\n    if (num > 0) digit = digit | 0x80\n\n    buffer.writeUInt8(digit, pos++)\n  } while (num > 0 && pos < maxLength)\n\n  if (num > 0) {\n    pos = 0\n  }\n\n  return SubOk ? buffer.subarray(0, pos) : buffer.slice(0, pos)\n}\n\nfunction generate4ByteBuffer (num) {\n  const buffer = Buffer.allocUnsafe(4)\n  buffer.writeUInt32BE(num, 0)\n  return buffer\n}\n\nmodule.exports = {\n  cache,\n  generateCache,\n  generateNumber: generateBuffer,\n  genBufVariableByteInt,\n  generate4ByteBuffer\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mqtt-packet/numbers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mqtt-packet/packet.js":
/*!********************************************!*\
  !*** ./node_modules/mqtt-packet/packet.js ***!
  \********************************************/
/***/ ((module) => {

eval("class Packet {\n  constructor () {\n    this.cmd = null\n    this.retain = false\n    this.qos = 0\n    this.dup = false\n    this.length = -1\n    this.topic = null\n    this.payload = null\n  }\n}\n\nmodule.exports = Packet\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbXF0dC1wYWNrZXQvcGFja2V0LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tcXR0LXBhY2tldC9wYWNrZXQuanM/M2IzNyJdLCJzb3VyY2VzQ29udGVudCI6WyJjbGFzcyBQYWNrZXQge1xuICBjb25zdHJ1Y3RvciAoKSB7XG4gICAgdGhpcy5jbWQgPSBudWxsXG4gICAgdGhpcy5yZXRhaW4gPSBmYWxzZVxuICAgIHRoaXMucW9zID0gMFxuICAgIHRoaXMuZHVwID0gZmFsc2VcbiAgICB0aGlzLmxlbmd0aCA9IC0xXG4gICAgdGhpcy50b3BpYyA9IG51bGxcbiAgICB0aGlzLnBheWxvYWQgPSBudWxsXG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBQYWNrZXRcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mqtt-packet/packet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mqtt-packet/parser.js":
/*!********************************************!*\
  !*** ./node_modules/mqtt-packet/parser.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const bl = __webpack_require__(/*! bl */ \"(ssr)/./node_modules/bl/bl.js\")\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\")\nconst Packet = __webpack_require__(/*! ./packet */ \"(ssr)/./node_modules/mqtt-packet/packet.js\")\nconst constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/mqtt-packet/constants.js\")\nconst debug = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\")('mqtt-packet:parser')\n\nclass Parser extends EventEmitter {\n  constructor () {\n    super()\n    this.parser = this.constructor.parser\n  }\n\n  static parser (opt) {\n    if (!(this instanceof Parser)) return (new Parser()).parser(opt)\n\n    this.settings = opt || {}\n\n    this._states = [\n      '_parseHeader',\n      '_parseLength',\n      '_parsePayload',\n      '_newPacket'\n    ]\n\n    this._resetState()\n    return this\n  }\n\n  _resetState () {\n    debug('_resetState: resetting packet, error, _list, and _stateCounter')\n    this.packet = new Packet()\n    this.error = null\n    this._list = bl()\n    this._stateCounter = 0\n  }\n\n  parse (buf) {\n    if (this.error) this._resetState()\n\n    this._list.append(buf)\n    debug('parse: current state: %s', this._states[this._stateCounter])\n    while ((this.packet.length !== -1 || this._list.length > 0) &&\n      this[this._states[this._stateCounter]]() &&\n      !this.error) {\n      this._stateCounter++\n      debug('parse: state complete. _stateCounter is now: %d', this._stateCounter)\n      debug('parse: packet.length: %d, buffer list length: %d', this.packet.length, this._list.length)\n      if (this._stateCounter >= this._states.length) this._stateCounter = 0\n    }\n    debug('parse: exited while loop. packet: %d, buffer list length: %d', this.packet.length, this._list.length)\n    return this._list.length\n  }\n\n  _parseHeader () {\n    // There is at least one byte in the buffer\n    const zero = this._list.readUInt8(0)\n    const cmdIndex = zero >> constants.CMD_SHIFT\n    this.packet.cmd = constants.types[cmdIndex]\n    const headerFlags = zero & 0xf\n    const requiredHeaderFlags = constants.requiredHeaderFlags[cmdIndex]\n    if (requiredHeaderFlags != null && headerFlags !== requiredHeaderFlags) {\n      // Where a flag bit is marked as “Reserved” in Table 2.2 - Flag Bits, it is reserved for future use and MUST be set to the value listed in that table [MQTT-2.2.2-1]. If invalid flags are received, the receiver MUST close the Network Connection [MQTT-2.2.2-2]\n      return this._emitError(new Error(constants.requiredHeaderFlagsErrors[cmdIndex]))\n    }\n    this.packet.retain = (zero & constants.RETAIN_MASK) !== 0\n    this.packet.qos = (zero >> constants.QOS_SHIFT) & constants.QOS_MASK\n    if (this.packet.qos > 2) {\n      return this._emitError(new Error('Packet must not have both QoS bits set to 1'))\n    }\n    this.packet.dup = (zero & constants.DUP_MASK) !== 0\n    debug('_parseHeader: packet: %o', this.packet)\n\n    this._list.consume(1)\n\n    return true\n  }\n\n  _parseLength () {\n    // There is at least one byte in the list\n    const result = this._parseVarByteNum(true)\n\n    if (result) {\n      this.packet.length = result.value\n      this._list.consume(result.bytes)\n    }\n    debug('_parseLength %d', result.value)\n    return !!result\n  }\n\n  _parsePayload () {\n    debug('_parsePayload: payload %O', this._list)\n    let result = false\n\n    // Do we have a payload? Do we have enough data to complete the payload?\n    // PINGs have no payload\n    if (this.packet.length === 0 || this._list.length >= this.packet.length) {\n      this._pos = 0\n\n      switch (this.packet.cmd) {\n        case 'connect':\n          this._parseConnect()\n          break\n        case 'connack':\n          this._parseConnack()\n          break\n        case 'publish':\n          this._parsePublish()\n          break\n        case 'puback':\n        case 'pubrec':\n        case 'pubrel':\n        case 'pubcomp':\n          this._parseConfirmation()\n          break\n        case 'subscribe':\n          this._parseSubscribe()\n          break\n        case 'suback':\n          this._parseSuback()\n          break\n        case 'unsubscribe':\n          this._parseUnsubscribe()\n          break\n        case 'unsuback':\n          this._parseUnsuback()\n          break\n        case 'pingreq':\n        case 'pingresp':\n          // These are empty, nothing to do\n          break\n        case 'disconnect':\n          this._parseDisconnect()\n          break\n        case 'auth':\n          this._parseAuth()\n          break\n        default:\n          this._emitError(new Error('Not supported'))\n      }\n\n      result = true\n    }\n    debug('_parsePayload complete result: %s', result)\n    return result\n  }\n\n  _parseConnect () {\n    debug('_parseConnect')\n    let topic // Will topic\n    let payload // Will payload\n    let password // Password\n    let username // Username\n    const flags = {}\n    const packet = this.packet\n\n    // Parse protocolId\n    const protocolId = this._parseString()\n\n    if (protocolId === null) return this._emitError(new Error('Cannot parse protocolId'))\n    if (protocolId !== 'MQTT' && protocolId !== 'MQIsdp') {\n      return this._emitError(new Error('Invalid protocolId'))\n    }\n\n    packet.protocolId = protocolId\n\n    // Parse constants version number\n    if (this._pos >= this._list.length) return this._emitError(new Error('Packet too short'))\n\n    packet.protocolVersion = this._list.readUInt8(this._pos)\n\n    if (packet.protocolVersion >= 128) {\n      packet.bridgeMode = true\n      packet.protocolVersion = packet.protocolVersion - 128\n    }\n\n    if (packet.protocolVersion !== 3 && packet.protocolVersion !== 4 && packet.protocolVersion !== 5) {\n      return this._emitError(new Error('Invalid protocol version'))\n    }\n\n    this._pos++\n\n    if (this._pos >= this._list.length) {\n      return this._emitError(new Error('Packet too short'))\n    }\n\n    if (this._list.readUInt8(this._pos) & 0x1) {\n      // The Server MUST validate that the reserved flag in the CONNECT Control Packet is set to zero and disconnect the Client if it is not zero [MQTT-3.1.2-3]\n      return this._emitError(new Error('Connect flag bit 0 must be 0, but got 1'))\n    }\n    // Parse connect flags\n    flags.username = (this._list.readUInt8(this._pos) & constants.USERNAME_MASK)\n    flags.password = (this._list.readUInt8(this._pos) & constants.PASSWORD_MASK)\n    flags.will = (this._list.readUInt8(this._pos) & constants.WILL_FLAG_MASK)\n\n    const willRetain = !!(this._list.readUInt8(this._pos) & constants.WILL_RETAIN_MASK)\n    const willQos = (this._list.readUInt8(this._pos) &\n        constants.WILL_QOS_MASK) >> constants.WILL_QOS_SHIFT\n\n    if (flags.will) {\n      packet.will = {}\n      packet.will.retain = willRetain\n      packet.will.qos = willQos\n    } else {\n      if (willRetain) {\n        return this._emitError(new Error('Will Retain Flag must be set to zero when Will Flag is set to 0'))\n      }\n      if (willQos) {\n        return this._emitError(new Error('Will QoS must be set to zero when Will Flag is set to 0'))\n      }\n    }\n\n    packet.clean = (this._list.readUInt8(this._pos) & constants.CLEAN_SESSION_MASK) !== 0\n    this._pos++\n\n    // Parse keepalive\n    packet.keepalive = this._parseNum()\n    if (packet.keepalive === -1) return this._emitError(new Error('Packet too short'))\n\n    // parse properties\n    if (packet.protocolVersion === 5) {\n      const properties = this._parseProperties()\n      if (Object.getOwnPropertyNames(properties).length) {\n        packet.properties = properties\n      }\n    }\n    // Parse clientId\n    const clientId = this._parseString()\n    if (clientId === null) return this._emitError(new Error('Packet too short'))\n    packet.clientId = clientId\n    debug('_parseConnect: packet.clientId: %s', packet.clientId)\n\n    if (flags.will) {\n      if (packet.protocolVersion === 5) {\n        const willProperties = this._parseProperties()\n        if (Object.getOwnPropertyNames(willProperties).length) {\n          packet.will.properties = willProperties\n        }\n      }\n      // Parse will topic\n      topic = this._parseString()\n      if (topic === null) return this._emitError(new Error('Cannot parse will topic'))\n      packet.will.topic = topic\n      debug('_parseConnect: packet.will.topic: %s', packet.will.topic)\n\n      // Parse will payload\n      payload = this._parseBuffer()\n      if (payload === null) return this._emitError(new Error('Cannot parse will payload'))\n      packet.will.payload = payload\n      debug('_parseConnect: packet.will.paylaod: %s', packet.will.payload)\n    }\n\n    // Parse username\n    if (flags.username) {\n      username = this._parseString()\n      if (username === null) return this._emitError(new Error('Cannot parse username'))\n      packet.username = username\n      debug('_parseConnect: packet.username: %s', packet.username)\n    }\n\n    // Parse password\n    if (flags.password) {\n      password = this._parseBuffer()\n      if (password === null) return this._emitError(new Error('Cannot parse password'))\n      packet.password = password\n    }\n    // need for right parse auth packet and self set up\n    this.settings = packet\n    debug('_parseConnect: complete')\n    return packet\n  }\n\n  _parseConnack () {\n    debug('_parseConnack')\n    const packet = this.packet\n\n    if (this._list.length < 1) return null\n    const flags = this._list.readUInt8(this._pos++)\n    if (flags > 1) {\n      return this._emitError(new Error('Invalid connack flags, bits 7-1 must be set to 0'))\n    }\n    packet.sessionPresent = !!(flags & constants.SESSIONPRESENT_MASK)\n\n    if (this.settings.protocolVersion === 5) {\n      if (this._list.length >= 2) {\n        packet.reasonCode = this._list.readUInt8(this._pos++)\n      } else {\n        packet.reasonCode = 0\n      }\n    } else {\n      if (this._list.length < 2) return null\n      packet.returnCode = this._list.readUInt8(this._pos++)\n    }\n\n    if (packet.returnCode === -1 || packet.reasonCode === -1) return this._emitError(new Error('Cannot parse return code'))\n    // mqtt 5 properties\n    if (this.settings.protocolVersion === 5) {\n      const properties = this._parseProperties()\n      if (Object.getOwnPropertyNames(properties).length) {\n        packet.properties = properties\n      }\n    }\n    debug('_parseConnack: complete')\n  }\n\n  _parsePublish () {\n    debug('_parsePublish')\n    const packet = this.packet\n    packet.topic = this._parseString()\n\n    if (packet.topic === null) return this._emitError(new Error('Cannot parse topic'))\n\n    // Parse messageId\n    if (packet.qos > 0) if (!this._parseMessageId()) { return }\n\n    // Properties mqtt 5\n    if (this.settings.protocolVersion === 5) {\n      const properties = this._parseProperties()\n      if (Object.getOwnPropertyNames(properties).length) {\n        packet.properties = properties\n      }\n    }\n\n    packet.payload = this._list.slice(this._pos, packet.length)\n    debug('_parsePublish: payload from buffer list: %o', packet.payload)\n  }\n\n  _parseSubscribe () {\n    debug('_parseSubscribe')\n    const packet = this.packet\n    let topic\n    let options\n    let qos\n    let rh\n    let rap\n    let nl\n    let subscription\n\n    packet.subscriptions = []\n\n    if (!this._parseMessageId()) { return }\n\n    // Properties mqtt 5\n    if (this.settings.protocolVersion === 5) {\n      const properties = this._parseProperties()\n      if (Object.getOwnPropertyNames(properties).length) {\n        packet.properties = properties\n      }\n    }\n\n    if (packet.length <= 0) { return this._emitError(new Error('Malformed subscribe, no payload specified')) }\n\n    while (this._pos < packet.length) {\n      // Parse topic\n      topic = this._parseString()\n      if (topic === null) return this._emitError(new Error('Cannot parse topic'))\n      if (this._pos >= packet.length) return this._emitError(new Error('Malformed Subscribe Payload'))\n\n      options = this._parseByte()\n\n      if (this.settings.protocolVersion === 5) {\n        if (options & 0xc0) {\n          return this._emitError(new Error('Invalid subscribe topic flag bits, bits 7-6 must be 0'))\n        }\n      } else {\n        if (options & 0xfc) {\n          return this._emitError(new Error('Invalid subscribe topic flag bits, bits 7-2 must be 0'))\n        }\n      }\n\n      qos = options & constants.SUBSCRIBE_OPTIONS_QOS_MASK\n      if (qos > 2) {\n        return this._emitError(new Error('Invalid subscribe QoS, must be <= 2'))\n      }\n      nl = ((options >> constants.SUBSCRIBE_OPTIONS_NL_SHIFT) & constants.SUBSCRIBE_OPTIONS_NL_MASK) !== 0\n      rap = ((options >> constants.SUBSCRIBE_OPTIONS_RAP_SHIFT) & constants.SUBSCRIBE_OPTIONS_RAP_MASK) !== 0\n      rh = (options >> constants.SUBSCRIBE_OPTIONS_RH_SHIFT) & constants.SUBSCRIBE_OPTIONS_RH_MASK\n\n      if (rh > 2) {\n        return this._emitError(new Error('Invalid retain handling, must be <= 2'))\n      }\n\n      subscription = { topic, qos }\n\n      // mqtt 5 options\n      if (this.settings.protocolVersion === 5) {\n        subscription.nl = nl\n        subscription.rap = rap\n        subscription.rh = rh\n      } else if (this.settings.bridgeMode) {\n        subscription.rh = 0\n        subscription.rap = true\n        subscription.nl = true\n      }\n\n      // Push pair to subscriptions\n      debug('_parseSubscribe: push subscription `%s` to subscription', subscription)\n      packet.subscriptions.push(subscription)\n    }\n  }\n\n  _parseSuback () {\n    debug('_parseSuback')\n    const packet = this.packet\n    this.packet.granted = []\n\n    if (!this._parseMessageId()) { return }\n\n    // Properties mqtt 5\n    if (this.settings.protocolVersion === 5) {\n      const properties = this._parseProperties()\n      if (Object.getOwnPropertyNames(properties).length) {\n        packet.properties = properties\n      }\n    }\n\n    if (packet.length <= 0) { return this._emitError(new Error('Malformed suback, no payload specified')) }\n\n    // Parse granted QoSes\n    while (this._pos < this.packet.length) {\n      const code = this._list.readUInt8(this._pos++)\n      if (this.settings.protocolVersion === 5) {\n        if (!constants.MQTT5_SUBACK_CODES[code]) {\n          return this._emitError(new Error('Invalid suback code'))\n        }\n      } else {\n        if (code > 2 && code !== 0x80) {\n          return this._emitError(new Error('Invalid suback QoS, must be 0, 1, 2 or 128'))\n        }\n      }\n      this.packet.granted.push(code)\n    }\n  }\n\n  _parseUnsubscribe () {\n    debug('_parseUnsubscribe')\n    const packet = this.packet\n\n    packet.unsubscriptions = []\n\n    // Parse messageId\n    if (!this._parseMessageId()) { return }\n\n    // Properties mqtt 5\n    if (this.settings.protocolVersion === 5) {\n      const properties = this._parseProperties()\n      if (Object.getOwnPropertyNames(properties).length) {\n        packet.properties = properties\n      }\n    }\n\n    if (packet.length <= 0) { return this._emitError(new Error('Malformed unsubscribe, no payload specified')) }\n\n    while (this._pos < packet.length) {\n      // Parse topic\n      const topic = this._parseString()\n      if (topic === null) return this._emitError(new Error('Cannot parse topic'))\n\n      // Push topic to unsubscriptions\n      debug('_parseUnsubscribe: push topic `%s` to unsubscriptions', topic)\n      packet.unsubscriptions.push(topic)\n    }\n  }\n\n  _parseUnsuback () {\n    debug('_parseUnsuback')\n    const packet = this.packet\n    if (!this._parseMessageId()) return this._emitError(new Error('Cannot parse messageId'))\n\n    if ((this.settings.protocolVersion === 3 ||\n      this.settings.protocolVersion === 4) && packet.length !== 2) {\n      return this._emitError(new Error('Malformed unsuback, payload length must be 2'))\n    }\n    if (packet.length <= 0) { return this._emitError(new Error('Malformed unsuback, no payload specified')) }\n\n    // Properties mqtt 5\n    if (this.settings.protocolVersion === 5) {\n      const properties = this._parseProperties()\n      if (Object.getOwnPropertyNames(properties).length) {\n        packet.properties = properties\n      }\n      // Parse granted QoSes\n      packet.granted = []\n\n      while (this._pos < this.packet.length) {\n        const code = this._list.readUInt8(this._pos++)\n        if (!constants.MQTT5_UNSUBACK_CODES[code]) {\n          return this._emitError(new Error('Invalid unsuback code'))\n        }\n        this.packet.granted.push(code)\n      }\n    }\n  }\n\n  // parse packets like puback, pubrec, pubrel, pubcomp\n  _parseConfirmation () {\n    debug('_parseConfirmation: packet.cmd: `%s`', this.packet.cmd)\n    const packet = this.packet\n\n    this._parseMessageId()\n\n    if (this.settings.protocolVersion === 5) {\n      if (packet.length > 2) {\n        // response code\n        packet.reasonCode = this._parseByte()\n        switch (this.packet.cmd) {\n          case 'puback':\n          case 'pubrec':\n            if (!constants.MQTT5_PUBACK_PUBREC_CODES[packet.reasonCode]) {\n              return this._emitError(new Error('Invalid ' + this.packet.cmd + ' reason code'))\n            }\n            break\n          case 'pubrel':\n          case 'pubcomp':\n            if (!constants.MQTT5_PUBREL_PUBCOMP_CODES[packet.reasonCode]) {\n              return this._emitError(new Error('Invalid ' + this.packet.cmd + ' reason code'))\n            }\n            break\n        }\n        debug('_parseConfirmation: packet.reasonCode `%d`', packet.reasonCode)\n      } else {\n        packet.reasonCode = 0\n      }\n\n      if (packet.length > 3) {\n        // properies mqtt 5\n        const properties = this._parseProperties()\n        if (Object.getOwnPropertyNames(properties).length) {\n          packet.properties = properties\n        }\n      }\n    }\n\n    return true\n  }\n\n  // parse disconnect packet\n  _parseDisconnect () {\n    const packet = this.packet\n    debug('_parseDisconnect')\n\n    if (this.settings.protocolVersion === 5) {\n      // response code\n      if (this._list.length > 0) {\n        packet.reasonCode = this._parseByte()\n        if (!constants.MQTT5_DISCONNECT_CODES[packet.reasonCode]) {\n          this._emitError(new Error('Invalid disconnect reason code'))\n        }\n      } else {\n        packet.reasonCode = 0\n      }\n      // properies mqtt 5\n      const properties = this._parseProperties()\n      if (Object.getOwnPropertyNames(properties).length) {\n        packet.properties = properties\n      }\n    }\n\n    debug('_parseDisconnect result: true')\n    return true\n  }\n\n  // parse auth packet\n  _parseAuth () {\n    debug('_parseAuth')\n    const packet = this.packet\n\n    if (this.settings.protocolVersion !== 5) {\n      return this._emitError(new Error('Not supported auth packet for this version MQTT'))\n    }\n\n    // response code\n    packet.reasonCode = this._parseByte()\n    if (!constants.MQTT5_AUTH_CODES[packet.reasonCode]) {\n      return this._emitError(new Error('Invalid auth reason code'))\n    }\n    // properies mqtt 5\n    const properties = this._parseProperties()\n    if (Object.getOwnPropertyNames(properties).length) {\n      packet.properties = properties\n    }\n\n    debug('_parseAuth: result: true')\n    return true\n  }\n\n  _parseMessageId () {\n    const packet = this.packet\n\n    packet.messageId = this._parseNum()\n\n    if (packet.messageId === null) {\n      this._emitError(new Error('Cannot parse messageId'))\n      return false\n    }\n\n    debug('_parseMessageId: packet.messageId %d', packet.messageId)\n    return true\n  }\n\n  _parseString (maybeBuffer) {\n    const length = this._parseNum()\n    const end = length + this._pos\n\n    if (length === -1 || end > this._list.length || end > this.packet.length) return null\n\n    const result = this._list.toString('utf8', this._pos, end)\n    this._pos += length\n    debug('_parseString: result: %s', result)\n    return result\n  }\n\n  _parseStringPair () {\n    debug('_parseStringPair')\n    return {\n      name: this._parseString(),\n      value: this._parseString()\n    }\n  }\n\n  _parseBuffer () {\n    const length = this._parseNum()\n    const end = length + this._pos\n\n    if (length === -1 || end > this._list.length || end > this.packet.length) return null\n\n    const result = this._list.slice(this._pos, end)\n\n    this._pos += length\n    debug('_parseBuffer: result: %o', result)\n    return result\n  }\n\n  _parseNum () {\n    if (this._list.length - this._pos < 2) return -1\n\n    const result = this._list.readUInt16BE(this._pos)\n    this._pos += 2\n    debug('_parseNum: result: %s', result)\n    return result\n  }\n\n  _parse4ByteNum () {\n    if (this._list.length - this._pos < 4) return -1\n\n    const result = this._list.readUInt32BE(this._pos)\n    this._pos += 4\n    debug('_parse4ByteNum: result: %s', result)\n    return result\n  }\n\n  _parseVarByteNum (fullInfoFlag) {\n    debug('_parseVarByteNum')\n    const maxBytes = 4\n    let bytes = 0\n    let mul = 1\n    let value = 0\n    let result = false\n    let current\n    const padding = this._pos ? this._pos : 0\n\n    while (bytes < maxBytes && (padding + bytes) < this._list.length) {\n      current = this._list.readUInt8(padding + bytes++)\n      value += mul * (current & constants.VARBYTEINT_MASK)\n      mul *= 0x80\n\n      if ((current & constants.VARBYTEINT_FIN_MASK) === 0) {\n        result = true\n        break\n      }\n      if (this._list.length <= bytes) {\n        break\n      }\n    }\n\n    if (!result && bytes === maxBytes && this._list.length >= bytes) {\n      this._emitError(new Error('Invalid variable byte integer'))\n    }\n\n    if (padding) {\n      this._pos += bytes\n    }\n\n    if (result) {\n      if (fullInfoFlag) {\n        result = { bytes, value }\n      } else {\n        result = value\n      }\n    } else {\n      result = false\n    }\n\n    debug('_parseVarByteNum: result: %o', result)\n    return result\n  }\n\n  _parseByte () {\n    let result\n    if (this._pos < this._list.length) {\n      result = this._list.readUInt8(this._pos)\n      this._pos++\n    }\n    debug('_parseByte: result: %o', result)\n    return result\n  }\n\n  _parseByType (type) {\n    debug('_parseByType: type: %s', type)\n    switch (type) {\n      case 'byte': {\n        return this._parseByte() !== 0\n      }\n      case 'int8': {\n        return this._parseByte()\n      }\n      case 'int16': {\n        return this._parseNum()\n      }\n      case 'int32': {\n        return this._parse4ByteNum()\n      }\n      case 'var': {\n        return this._parseVarByteNum()\n      }\n      case 'string': {\n        return this._parseString()\n      }\n      case 'pair': {\n        return this._parseStringPair()\n      }\n      case 'binary': {\n        return this._parseBuffer()\n      }\n    }\n  }\n\n  _parseProperties () {\n    debug('_parseProperties')\n    const length = this._parseVarByteNum()\n    const start = this._pos\n    const end = start + length\n    const result = {}\n    while (this._pos < end) {\n      const type = this._parseByte()\n      if (!type) {\n        this._emitError(new Error('Cannot parse property code type'))\n        return false\n      }\n      const name = constants.propertiesCodes[type]\n      if (!name) {\n        this._emitError(new Error('Unknown property'))\n        return false\n      }\n      // user properties process\n      if (name === 'userProperties') {\n        if (!result[name]) {\n          result[name] = Object.create(null)\n        }\n        const currentUserProperty = this._parseByType(constants.propertiesTypes[name])\n        if (result[name][currentUserProperty.name]) {\n          if (Array.isArray(result[name][currentUserProperty.name])) {\n            result[name][currentUserProperty.name].push(currentUserProperty.value)\n          } else {\n            const currentValue = result[name][currentUserProperty.name]\n            result[name][currentUserProperty.name] = [currentValue]\n            result[name][currentUserProperty.name].push(currentUserProperty.value)\n          }\n        } else {\n          result[name][currentUserProperty.name] = currentUserProperty.value\n        }\n        continue\n      }\n      if (result[name]) {\n        if (Array.isArray(result[name])) {\n          result[name].push(this._parseByType(constants.propertiesTypes[name]))\n        } else {\n          result[name] = [result[name]]\n          result[name].push(this._parseByType(constants.propertiesTypes[name]))\n        }\n      } else {\n        result[name] = this._parseByType(constants.propertiesTypes[name])\n      }\n    }\n    return result\n  }\n\n  _newPacket () {\n    debug('_newPacket')\n    if (this.packet) {\n      this._list.consume(this.packet.length)\n      debug('_newPacket: parser emit packet: packet.cmd: %s, packet.payload: %s, packet.length: %d', this.packet.cmd, this.packet.payload, this.packet.length)\n      this.emit('packet', this.packet)\n    }\n    debug('_newPacket: new packet')\n    this.packet = new Packet()\n\n    this._pos = 0\n\n    return true\n  }\n\n  _emitError (err) {\n    debug('_emitError', err)\n    this.error = err\n    this.emit('error', err)\n  }\n}\n\nmodule.exports = Parser\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mqtt-packet/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mqtt-packet/writeToStream.js":
/*!***************************************************!*\
  !*** ./node_modules/mqtt-packet/writeToStream.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const protocol = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/mqtt-packet/constants.js\")\nconst { Buffer } = __webpack_require__(/*! buffer */ \"buffer\")\nconst empty = Buffer.allocUnsafe(0)\nconst zeroBuf = Buffer.from([0])\nconst numbers = __webpack_require__(/*! ./numbers */ \"(ssr)/./node_modules/mqtt-packet/numbers.js\")\nconst nextTick = (__webpack_require__(/*! process-nextick-args */ \"(ssr)/./node_modules/process-nextick-args/index.js\").nextTick)\nconst debug = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\")('mqtt-packet:writeToStream')\n\nconst numCache = numbers.cache\nconst generateNumber = numbers.generateNumber\nconst generateCache = numbers.generateCache\nconst genBufVariableByteInt = numbers.genBufVariableByteInt\nconst generate4ByteBuffer = numbers.generate4ByteBuffer\nlet writeNumber = writeNumberCached\nlet toGenerate = true\n\nfunction generate (packet, stream, opts) {\n  debug('generate called')\n  if (stream.cork) {\n    stream.cork()\n    nextTick(uncork, stream)\n  }\n\n  if (toGenerate) {\n    toGenerate = false\n    generateCache()\n  }\n  debug('generate: packet.cmd: %s', packet.cmd)\n  switch (packet.cmd) {\n    case 'connect':\n      return connect(packet, stream, opts)\n    case 'connack':\n      return connack(packet, stream, opts)\n    case 'publish':\n      return publish(packet, stream, opts)\n    case 'puback':\n    case 'pubrec':\n    case 'pubrel':\n    case 'pubcomp':\n      return confirmation(packet, stream, opts)\n    case 'subscribe':\n      return subscribe(packet, stream, opts)\n    case 'suback':\n      return suback(packet, stream, opts)\n    case 'unsubscribe':\n      return unsubscribe(packet, stream, opts)\n    case 'unsuback':\n      return unsuback(packet, stream, opts)\n    case 'pingreq':\n    case 'pingresp':\n      return emptyPacket(packet, stream, opts)\n    case 'disconnect':\n      return disconnect(packet, stream, opts)\n    case 'auth':\n      return auth(packet, stream, opts)\n    default:\n      stream.destroy(new Error('Unknown command'))\n      return false\n  }\n}\n/**\n * Controls numbers cache.\n * Set to \"false\" to allocate buffers on-the-flight instead of pre-generated cache\n */\nObject.defineProperty(generate, 'cacheNumbers', {\n  get () {\n    return writeNumber === writeNumberCached\n  },\n  set (value) {\n    if (value) {\n      if (!numCache || Object.keys(numCache).length === 0) toGenerate = true\n      writeNumber = writeNumberCached\n    } else {\n      toGenerate = false\n      writeNumber = writeNumberGenerated\n    }\n  }\n})\n\nfunction uncork (stream) {\n  stream.uncork()\n}\n\nfunction connect (packet, stream, opts) {\n  const settings = packet || {}\n  const protocolId = settings.protocolId || 'MQTT'\n  let protocolVersion = settings.protocolVersion || 4\n  const will = settings.will\n  let clean = settings.clean\n  const keepalive = settings.keepalive || 0\n  const clientId = settings.clientId || ''\n  const username = settings.username\n  const password = settings.password\n  /* mqtt5 new oprions */\n  const properties = settings.properties\n\n  if (clean === undefined) clean = true\n\n  let length = 0\n\n  // Must be a string and non-falsy\n  if (!protocolId ||\n     (typeof protocolId !== 'string' && !Buffer.isBuffer(protocolId))) {\n    stream.destroy(new Error('Invalid protocolId'))\n    return false\n  } else length += protocolId.length + 2\n\n  // Must be 3 or 4 or 5\n  if (protocolVersion !== 3 && protocolVersion !== 4 && protocolVersion !== 5) {\n    stream.destroy(new Error('Invalid protocol version'))\n    return false\n  } else length += 1\n\n  // ClientId might be omitted in 3.1.1 and 5, but only if cleanSession is set to 1\n  if ((typeof clientId === 'string' || Buffer.isBuffer(clientId)) &&\n     (clientId || protocolVersion >= 4) && (clientId || clean)) {\n    length += Buffer.byteLength(clientId) + 2\n  } else {\n    if (protocolVersion < 4) {\n      stream.destroy(new Error('clientId must be supplied before 3.1.1'))\n      return false\n    }\n    if ((clean * 1) === 0) {\n      stream.destroy(new Error('clientId must be given if cleanSession set to 0'))\n      return false\n    }\n  }\n\n  // Must be a two byte number\n  if (typeof keepalive !== 'number' ||\n      keepalive < 0 ||\n      keepalive > 65535 ||\n      keepalive % 1 !== 0) {\n    stream.destroy(new Error('Invalid keepalive'))\n    return false\n  } else length += 2\n\n  // Connect flags\n  length += 1\n\n  let propertiesData\n  let willProperties\n\n  // Properties\n  if (protocolVersion === 5) {\n    propertiesData = getProperties(stream, properties)\n    if (!propertiesData) { return false }\n    length += propertiesData.length\n  }\n\n  // If will exists...\n  if (will) {\n    // It must be an object\n    if (typeof will !== 'object') {\n      stream.destroy(new Error('Invalid will'))\n      return false\n    }\n    // It must have topic typeof string\n    if (!will.topic || typeof will.topic !== 'string') {\n      stream.destroy(new Error('Invalid will topic'))\n      return false\n    } else {\n      length += Buffer.byteLength(will.topic) + 2\n    }\n\n    // Payload\n    length += 2 // payload length\n    if (will.payload) {\n      if (will.payload.length >= 0) {\n        if (typeof will.payload === 'string') {\n          length += Buffer.byteLength(will.payload)\n        } else {\n          length += will.payload.length\n        }\n      } else {\n        stream.destroy(new Error('Invalid will payload'))\n        return false\n      }\n    }\n    // will properties\n    willProperties = {}\n    if (protocolVersion === 5) {\n      willProperties = getProperties(stream, will.properties)\n      if (!willProperties) { return false }\n      length += willProperties.length\n    }\n  }\n\n  // Username\n  let providedUsername = false\n  if (username != null) {\n    if (isStringOrBuffer(username)) {\n      providedUsername = true\n      length += Buffer.byteLength(username) + 2\n    } else {\n      stream.destroy(new Error('Invalid username'))\n      return false\n    }\n  }\n\n  // Password\n  if (password != null) {\n    if (!providedUsername) {\n      stream.destroy(new Error('Username is required to use password'))\n      return false\n    }\n\n    if (isStringOrBuffer(password)) {\n      length += byteLength(password) + 2\n    } else {\n      stream.destroy(new Error('Invalid password'))\n      return false\n    }\n  }\n\n  // Generate header\n  stream.write(protocol.CONNECT_HEADER)\n\n  // Generate length\n  writeVarByteInt(stream, length)\n\n  // Generate protocol ID\n  writeStringOrBuffer(stream, protocolId)\n\n  if (settings.bridgeMode) {\n    protocolVersion += 128\n  }\n\n  stream.write(\n    protocolVersion === 131\n      ? protocol.VERSION131\n      : protocolVersion === 132\n        ? protocol.VERSION132\n        : protocolVersion === 4\n          ? protocol.VERSION4\n          : protocolVersion === 5\n            ? protocol.VERSION5\n            : protocol.VERSION3\n  )\n\n  // Connect flags\n  let flags = 0\n  flags |= (username != null) ? protocol.USERNAME_MASK : 0\n  flags |= (password != null) ? protocol.PASSWORD_MASK : 0\n  flags |= (will && will.retain) ? protocol.WILL_RETAIN_MASK : 0\n  flags |= (will && will.qos) ? will.qos << protocol.WILL_QOS_SHIFT : 0\n  flags |= will ? protocol.WILL_FLAG_MASK : 0\n  flags |= clean ? protocol.CLEAN_SESSION_MASK : 0\n\n  stream.write(Buffer.from([flags]))\n\n  // Keepalive\n  writeNumber(stream, keepalive)\n\n  // Properties\n  if (protocolVersion === 5) {\n    propertiesData.write()\n  }\n\n  // Client ID\n  writeStringOrBuffer(stream, clientId)\n\n  // Will\n  if (will) {\n    if (protocolVersion === 5) {\n      willProperties.write()\n    }\n    writeString(stream, will.topic)\n    writeStringOrBuffer(stream, will.payload)\n  }\n\n  // Username and password\n  if (username != null) {\n    writeStringOrBuffer(stream, username)\n  }\n  if (password != null) {\n    writeStringOrBuffer(stream, password)\n  }\n  // This is a small packet that happens only once on a stream\n  // We assume the stream is always free to receive more data after this\n  return true\n}\n\nfunction connack (packet, stream, opts) {\n  const version = opts ? opts.protocolVersion : 4\n  const settings = packet || {}\n  const rc = version === 5 ? settings.reasonCode : settings.returnCode\n  const properties = settings.properties\n  let length = 2 // length of rc and sessionHeader\n\n  // Check return code\n  if (typeof rc !== 'number') {\n    stream.destroy(new Error('Invalid return code'))\n    return false\n  }\n  // mqtt5 properties\n  let propertiesData = null\n  if (version === 5) {\n    propertiesData = getProperties(stream, properties)\n    if (!propertiesData) { return false }\n    length += propertiesData.length\n  }\n\n  stream.write(protocol.CONNACK_HEADER)\n  // length\n  writeVarByteInt(stream, length)\n  stream.write(settings.sessionPresent ? protocol.SESSIONPRESENT_HEADER : zeroBuf)\n\n  stream.write(Buffer.from([rc]))\n  if (propertiesData != null) {\n    propertiesData.write()\n  }\n  return true\n}\n\nfunction publish (packet, stream, opts) {\n  debug('publish: packet: %o', packet)\n  const version = opts ? opts.protocolVersion : 4\n  const settings = packet || {}\n  const qos = settings.qos || 0\n  const retain = settings.retain ? protocol.RETAIN_MASK : 0\n  const topic = settings.topic\n  const payload = settings.payload || empty\n  const id = settings.messageId\n  const properties = settings.properties\n\n  let length = 0\n\n  // Topic must be a non-empty string or Buffer\n  if (typeof topic === 'string') length += Buffer.byteLength(topic) + 2\n  else if (Buffer.isBuffer(topic)) length += topic.length + 2\n  else {\n    stream.destroy(new Error('Invalid topic'))\n    return false\n  }\n\n  // Get the payload length\n  if (!Buffer.isBuffer(payload)) length += Buffer.byteLength(payload)\n  else length += payload.length\n\n  // Message ID must a number if qos > 0\n  if (qos && typeof id !== 'number') {\n    stream.destroy(new Error('Invalid messageId'))\n    return false\n  } else if (qos) length += 2\n\n  // mqtt5 properties\n  let propertiesData = null\n  if (version === 5) {\n    propertiesData = getProperties(stream, properties)\n    if (!propertiesData) { return false }\n    length += propertiesData.length\n  }\n\n  // Header\n  stream.write(protocol.PUBLISH_HEADER[qos][settings.dup ? 1 : 0][retain ? 1 : 0])\n\n  // Remaining length\n  writeVarByteInt(stream, length)\n\n  // Topic\n  writeNumber(stream, byteLength(topic))\n  stream.write(topic)\n\n  // Message ID\n  if (qos > 0) writeNumber(stream, id)\n\n  // Properties\n  if (propertiesData != null) {\n    propertiesData.write()\n  }\n\n  // Payload\n  debug('publish: payload: %o', payload)\n  return stream.write(payload)\n}\n\n/* Puback, pubrec, pubrel and pubcomp */\nfunction confirmation (packet, stream, opts) {\n  const version = opts ? opts.protocolVersion : 4\n  const settings = packet || {}\n  const type = settings.cmd || 'puback'\n  const id = settings.messageId\n  const dup = (settings.dup && type === 'pubrel') ? protocol.DUP_MASK : 0\n  let qos = 0\n  const reasonCode = settings.reasonCode\n  const properties = settings.properties\n  let length = version === 5 ? 3 : 2\n\n  if (type === 'pubrel') qos = 1\n\n  // Check message ID\n  if (typeof id !== 'number') {\n    stream.destroy(new Error('Invalid messageId'))\n    return false\n  }\n\n  // properies mqtt 5\n  let propertiesData = null\n  if (version === 5) {\n    // Confirm should not add empty property length with no properties (rfc *******.1)\n    if (typeof properties === 'object') {\n      propertiesData = getPropertiesByMaximumPacketSize(stream, properties, opts, length)\n      if (!propertiesData) { return false }\n      length += propertiesData.length\n    }\n  }\n\n  // Header\n  stream.write(protocol.ACKS[type][qos][dup][0])\n\n  // Length === 3 is only true of version === 5 and no properties; therefore if reasonCode === 0 we are allowed to skip both bytes - but if we write the reason code we also have to write property length [MQTT-3.4.2-1].\n  if (length === 3) length += reasonCode !== 0 ? 1 : -1\n  writeVarByteInt(stream, length)\n\n  // Message ID\n  writeNumber(stream, id)\n\n  // reason code in header - but only if it couldn't be omitted - indicated by length !== 2.\n  if (version === 5 && length !== 2) {\n    stream.write(Buffer.from([reasonCode]))\n  }\n\n  // properties mqtt 5\n  if (propertiesData !== null) {\n    propertiesData.write()\n  } else {\n    if (length === 4) {\n      // we have no properties but have written a reason code - so we need to indicate empty properties by filling in a zero.\n      stream.write(Buffer.from([0]))\n    }\n  }\n  return true\n}\n\nfunction subscribe (packet, stream, opts) {\n  debug('subscribe: packet: ')\n  const version = opts ? opts.protocolVersion : 4\n  const settings = packet || {}\n  const dup = settings.dup ? protocol.DUP_MASK : 0\n  const id = settings.messageId\n  const subs = settings.subscriptions\n  const properties = settings.properties\n\n  let length = 0\n\n  // Check message ID\n  if (typeof id !== 'number') {\n    stream.destroy(new Error('Invalid messageId'))\n    return false\n  } else length += 2\n\n  // properies mqtt 5\n  let propertiesData = null\n  if (version === 5) {\n    propertiesData = getProperties(stream, properties)\n    if (!propertiesData) { return false }\n    length += propertiesData.length\n  }\n\n  // Check subscriptions\n  if (typeof subs === 'object' && subs.length) {\n    for (let i = 0; i < subs.length; i += 1) {\n      const itopic = subs[i].topic\n      const iqos = subs[i].qos\n\n      if (typeof itopic !== 'string') {\n        stream.destroy(new Error('Invalid subscriptions - invalid topic'))\n        return false\n      }\n      if (typeof iqos !== 'number') {\n        stream.destroy(new Error('Invalid subscriptions - invalid qos'))\n        return false\n      }\n\n      if (version === 5) {\n        const nl = subs[i].nl || false\n        if (typeof nl !== 'boolean') {\n          stream.destroy(new Error('Invalid subscriptions - invalid No Local'))\n          return false\n        }\n        const rap = subs[i].rap || false\n        if (typeof rap !== 'boolean') {\n          stream.destroy(new Error('Invalid subscriptions - invalid Retain as Published'))\n          return false\n        }\n        const rh = subs[i].rh || 0\n        if (typeof rh !== 'number' || rh > 2) {\n          stream.destroy(new Error('Invalid subscriptions - invalid Retain Handling'))\n          return false\n        }\n      }\n\n      length += Buffer.byteLength(itopic) + 2 + 1\n    }\n  } else {\n    stream.destroy(new Error('Invalid subscriptions'))\n    return false\n  }\n\n  // Generate header\n  debug('subscribe: writing to stream: %o', protocol.SUBSCRIBE_HEADER)\n  stream.write(protocol.SUBSCRIBE_HEADER[1][dup ? 1 : 0][0])\n\n  // Generate length\n  writeVarByteInt(stream, length)\n\n  // Generate message ID\n  writeNumber(stream, id)\n\n  // properies mqtt 5\n  if (propertiesData !== null) {\n    propertiesData.write()\n  }\n\n  let result = true\n\n  // Generate subs\n  for (const sub of subs) {\n    const jtopic = sub.topic\n    const jqos = sub.qos\n    const jnl = +sub.nl\n    const jrap = +sub.rap\n    const jrh = sub.rh\n    let joptions\n\n    // Write topic string\n    writeString(stream, jtopic)\n\n    // options process\n    joptions = protocol.SUBSCRIBE_OPTIONS_QOS[jqos]\n    if (version === 5) {\n      joptions |= jnl ? protocol.SUBSCRIBE_OPTIONS_NL : 0\n      joptions |= jrap ? protocol.SUBSCRIBE_OPTIONS_RAP : 0\n      joptions |= jrh ? protocol.SUBSCRIBE_OPTIONS_RH[jrh] : 0\n    }\n    // Write options\n    result = stream.write(Buffer.from([joptions]))\n  }\n\n  return result\n}\n\nfunction suback (packet, stream, opts) {\n  const version = opts ? opts.protocolVersion : 4\n  const settings = packet || {}\n  const id = settings.messageId\n  const granted = settings.granted\n  const properties = settings.properties\n  let length = 0\n\n  // Check message ID\n  if (typeof id !== 'number') {\n    stream.destroy(new Error('Invalid messageId'))\n    return false\n  } else length += 2\n\n  // Check granted qos vector\n  if (typeof granted === 'object' && granted.length) {\n    for (let i = 0; i < granted.length; i += 1) {\n      if (typeof granted[i] !== 'number') {\n        stream.destroy(new Error('Invalid qos vector'))\n        return false\n      }\n      length += 1\n    }\n  } else {\n    stream.destroy(new Error('Invalid qos vector'))\n    return false\n  }\n\n  // properies mqtt 5\n  let propertiesData = null\n  if (version === 5) {\n    propertiesData = getPropertiesByMaximumPacketSize(stream, properties, opts, length)\n    if (!propertiesData) { return false }\n    length += propertiesData.length\n  }\n\n  // header\n  stream.write(protocol.SUBACK_HEADER)\n\n  // Length\n  writeVarByteInt(stream, length)\n\n  // Message ID\n  writeNumber(stream, id)\n\n  // properies mqtt 5\n  if (propertiesData !== null) {\n    propertiesData.write()\n  }\n\n  return stream.write(Buffer.from(granted))\n}\n\nfunction unsubscribe (packet, stream, opts) {\n  const version = opts ? opts.protocolVersion : 4\n  const settings = packet || {}\n  const id = settings.messageId\n  const dup = settings.dup ? protocol.DUP_MASK : 0\n  const unsubs = settings.unsubscriptions\n  const properties = settings.properties\n\n  let length = 0\n\n  // Check message ID\n  if (typeof id !== 'number') {\n    stream.destroy(new Error('Invalid messageId'))\n    return false\n  } else {\n    length += 2\n  }\n  // Check unsubs\n  if (typeof unsubs === 'object' && unsubs.length) {\n    for (let i = 0; i < unsubs.length; i += 1) {\n      if (typeof unsubs[i] !== 'string') {\n        stream.destroy(new Error('Invalid unsubscriptions'))\n        return false\n      }\n      length += Buffer.byteLength(unsubs[i]) + 2\n    }\n  } else {\n    stream.destroy(new Error('Invalid unsubscriptions'))\n    return false\n  }\n  // properies mqtt 5\n  let propertiesData = null\n  if (version === 5) {\n    propertiesData = getProperties(stream, properties)\n    if (!propertiesData) { return false }\n    length += propertiesData.length\n  }\n\n  // Header\n  stream.write(protocol.UNSUBSCRIBE_HEADER[1][dup ? 1 : 0][0])\n\n  // Length\n  writeVarByteInt(stream, length)\n\n  // Message ID\n  writeNumber(stream, id)\n\n  // properies mqtt 5\n  if (propertiesData !== null) {\n    propertiesData.write()\n  }\n\n  // Unsubs\n  let result = true\n  for (let j = 0; j < unsubs.length; j++) {\n    result = writeString(stream, unsubs[j])\n  }\n\n  return result\n}\n\nfunction unsuback (packet, stream, opts) {\n  const version = opts ? opts.protocolVersion : 4\n  const settings = packet || {}\n  const id = settings.messageId\n  const dup = settings.dup ? protocol.DUP_MASK : 0\n  const granted = settings.granted\n  const properties = settings.properties\n  const type = settings.cmd\n  const qos = 0\n\n  let length = 2\n\n  // Check message ID\n  if (typeof id !== 'number') {\n    stream.destroy(new Error('Invalid messageId'))\n    return false\n  }\n\n  // Check granted\n  if (version === 5) {\n    if (typeof granted === 'object' && granted.length) {\n      for (let i = 0; i < granted.length; i += 1) {\n        if (typeof granted[i] !== 'number') {\n          stream.destroy(new Error('Invalid qos vector'))\n          return false\n        }\n        length += 1\n      }\n    } else {\n      stream.destroy(new Error('Invalid qos vector'))\n      return false\n    }\n  }\n\n  // properies mqtt 5\n  let propertiesData = null\n  if (version === 5) {\n    propertiesData = getPropertiesByMaximumPacketSize(stream, properties, opts, length)\n    if (!propertiesData) { return false }\n    length += propertiesData.length\n  }\n\n  // Header\n  stream.write(protocol.ACKS[type][qos][dup][0])\n\n  // Length\n  writeVarByteInt(stream, length)\n\n  // Message ID\n  writeNumber(stream, id)\n\n  // properies mqtt 5\n  if (propertiesData !== null) {\n    propertiesData.write()\n  }\n\n  // payload\n  if (version === 5) {\n    stream.write(Buffer.from(granted))\n  }\n  return true\n}\n\nfunction emptyPacket (packet, stream, opts) {\n  return stream.write(protocol.EMPTY[packet.cmd])\n}\n\nfunction disconnect (packet, stream, opts) {\n  const version = opts ? opts.protocolVersion : 4\n  const settings = packet || {}\n  const reasonCode = settings.reasonCode\n  const properties = settings.properties\n  let length = version === 5 ? 1 : 0\n\n  // properies mqtt 5\n  let propertiesData = null\n  if (version === 5) {\n    propertiesData = getPropertiesByMaximumPacketSize(stream, properties, opts, length)\n    if (!propertiesData) { return false }\n    length += propertiesData.length\n  }\n\n  // Header\n  stream.write(Buffer.from([protocol.codes.disconnect << 4]))\n\n  // Length\n  writeVarByteInt(stream, length)\n\n  // reason code in header\n  if (version === 5) {\n    stream.write(Buffer.from([reasonCode]))\n  }\n\n  // properies mqtt 5\n  if (propertiesData !== null) {\n    propertiesData.write()\n  }\n\n  return true\n}\n\nfunction auth (packet, stream, opts) {\n  const version = opts ? opts.protocolVersion : 4\n  const settings = packet || {}\n  const reasonCode = settings.reasonCode\n  const properties = settings.properties\n  let length = version === 5 ? 1 : 0\n\n  if (version !== 5) stream.destroy(new Error('Invalid mqtt version for auth packet'))\n\n  // properies mqtt 5\n  const propertiesData = getPropertiesByMaximumPacketSize(stream, properties, opts, length)\n  if (!propertiesData) { return false }\n  length += propertiesData.length\n\n  // Header\n  stream.write(Buffer.from([protocol.codes.auth << 4]))\n\n  // Length\n  writeVarByteInt(stream, length)\n\n  // reason code in header\n  stream.write(Buffer.from([reasonCode]))\n\n  // properies mqtt 5\n  if (propertiesData !== null) {\n    propertiesData.write()\n  }\n  return true\n}\n\n/**\n * writeVarByteInt - write an MQTT style variable byte integer to the buffer\n *\n * @param <Buffer> buffer - destination\n * @param <Number> pos - offset\n * @param <Number> length - length (>0)\n * @returns <Number> number of bytes written\n *\n * @api private\n */\n\nconst varByteIntCache = {}\nfunction writeVarByteInt (stream, num) {\n  if (num > protocol.VARBYTEINT_MAX) {\n    stream.destroy(new Error(`Invalid variable byte integer: ${num}`))\n    return false\n  }\n\n  let buffer = varByteIntCache[num]\n\n  if (!buffer) {\n    buffer = genBufVariableByteInt(num)\n    if (num < 16384) varByteIntCache[num] = buffer\n  }\n  debug('writeVarByteInt: writing to stream: %o', buffer)\n  return stream.write(buffer)\n}\n\n/**\n * writeString - write a utf8 string to the buffer\n *\n * @param <Buffer> buffer - destination\n * @param <Number> pos - offset\n * @param <String> string - string to write\n * @return <Number> number of bytes written\n *\n * @api private\n */\n\nfunction writeString (stream, string) {\n  const strlen = Buffer.byteLength(string)\n  writeNumber(stream, strlen)\n\n  debug('writeString: %s', string)\n  return stream.write(string, 'utf8')\n}\n\n/**\n * writeStringPair - write a utf8 string pairs to the buffer\n *\n * @param <Buffer> buffer - destination\n * @param <String> name - string name to write\n * @param <String> value - string value to write\n * @return <Number> number of bytes written\n *\n * @api private\n */\nfunction writeStringPair (stream, name, value) {\n  writeString(stream, name)\n  writeString(stream, value)\n}\n\n/**\n * writeNumber - write a two byte number to the buffer\n *\n * @param <Buffer> buffer - destination\n * @param <Number> pos - offset\n * @param <String> number - number to write\n * @return <Number> number of bytes written\n *\n * @api private\n */\nfunction writeNumberCached (stream, number) {\n  debug('writeNumberCached: number: %d', number)\n  debug('writeNumberCached: %o', numCache[number])\n  return stream.write(numCache[number])\n}\nfunction writeNumberGenerated (stream, number) {\n  const generatedNumber = generateNumber(number)\n  debug('writeNumberGenerated: %o', generatedNumber)\n  return stream.write(generatedNumber)\n}\nfunction write4ByteNumber (stream, number) {\n  const generated4ByteBuffer = generate4ByteBuffer(number)\n  debug('write4ByteNumber: %o', generated4ByteBuffer)\n  return stream.write(generated4ByteBuffer)\n}\n/**\n * writeStringOrBuffer - write a String or Buffer with the its length prefix\n *\n * @param <Buffer> buffer - destination\n * @param <Number> pos - offset\n * @param <String> toWrite - String or Buffer\n * @return <Number> number of bytes written\n */\nfunction writeStringOrBuffer (stream, toWrite) {\n  if (typeof toWrite === 'string') {\n    writeString(stream, toWrite)\n  } else if (toWrite) {\n    writeNumber(stream, toWrite.length)\n    stream.write(toWrite)\n  } else writeNumber(stream, 0)\n}\n\nfunction getProperties (stream, properties) {\n  /* connect properties */\n  if (typeof properties !== 'object' || properties.length != null) {\n    return {\n      length: 1,\n      write () {\n        writeProperties(stream, {}, 0)\n      }\n    }\n  }\n  let propertiesLength = 0\n  function getLengthProperty (name, value) {\n    const type = protocol.propertiesTypes[name]\n    let length = 0\n    switch (type) {\n      case 'byte': {\n        if (typeof value !== 'boolean') {\n          stream.destroy(new Error(`Invalid ${name}: ${value}`))\n          return false\n        }\n        length += 1 + 1\n        break\n      }\n      case 'int8': {\n        if (typeof value !== 'number' || value < 0 || value > 0xff) {\n          stream.destroy(new Error(`Invalid ${name}: ${value}`))\n          return false\n        }\n        length += 1 + 1\n        break\n      }\n      case 'binary': {\n        if (value && value === null) {\n          stream.destroy(new Error(`Invalid ${name}: ${value}`))\n          return false\n        }\n        length += 1 + Buffer.byteLength(value) + 2\n        break\n      }\n      case 'int16': {\n        if (typeof value !== 'number' || value < 0 || value > 0xffff) {\n          stream.destroy(new Error(`Invalid ${name}: ${value}`))\n          return false\n        }\n        length += 1 + 2\n        break\n      }\n      case 'int32': {\n        if (typeof value !== 'number' || value < 0 || value > 0xffffffff) {\n          stream.destroy(new Error(`Invalid ${name}: ${value}`))\n          return false\n        }\n        length += 1 + 4\n        break\n      }\n      case 'var': {\n        // var byte integer is max 24 bits packed in 32 bits\n        if (typeof value !== 'number' || value < 0 || value > 0x0fffffff) {\n          stream.destroy(new Error(`Invalid ${name}: ${value}`))\n          return false\n        }\n        length += 1 + Buffer.byteLength(genBufVariableByteInt(value))\n        break\n      }\n      case 'string': {\n        if (typeof value !== 'string') {\n          stream.destroy(new Error(`Invalid ${name}: ${value}`))\n          return false\n        }\n        length += 1 + 2 + Buffer.byteLength(value.toString())\n        break\n      }\n      case 'pair': {\n        if (typeof value !== 'object') {\n          stream.destroy(new Error(`Invalid ${name}: ${value}`))\n          return false\n        }\n        length += Object.getOwnPropertyNames(value).reduce((result, name) => {\n          const currentValue = value[name]\n          if (Array.isArray(currentValue)) {\n            result += currentValue.reduce((currentLength, value) => {\n              currentLength += 1 + 2 + Buffer.byteLength(name.toString()) + 2 + Buffer.byteLength(value.toString())\n              return currentLength\n            }, 0)\n          } else {\n            result += 1 + 2 + Buffer.byteLength(name.toString()) + 2 + Buffer.byteLength(value[name].toString())\n          }\n          return result\n        }, 0)\n        break\n      }\n      default: {\n        stream.destroy(new Error(`Invalid property ${name}: ${value}`))\n        return false\n      }\n    }\n    return length\n  }\n  if (properties) {\n    for (const propName in properties) {\n      let propLength = 0\n      let propValueLength = 0\n      const propValue = properties[propName]\n      if (propValue === undefined) {\n        continue\n      } else if (Array.isArray(propValue)) {\n        for (let valueIndex = 0; valueIndex < propValue.length; valueIndex++) {\n          propValueLength = getLengthProperty(propName, propValue[valueIndex])\n          if (!propValueLength) { return false }\n          propLength += propValueLength\n        }\n      } else {\n        propValueLength = getLengthProperty(propName, propValue)\n        if (!propValueLength) { return false }\n        propLength = propValueLength\n      }\n      if (!propLength) return false\n      propertiesLength += propLength\n    }\n  }\n  const propertiesLengthLength = Buffer.byteLength(genBufVariableByteInt(propertiesLength))\n\n  return {\n    length: propertiesLengthLength + propertiesLength,\n    write () {\n      writeProperties(stream, properties, propertiesLength)\n    }\n  }\n}\n\nfunction getPropertiesByMaximumPacketSize (stream, properties, opts, length) {\n  const mayEmptyProps = ['reasonString', 'userProperties']\n  const maximumPacketSize = opts && opts.properties && opts.properties.maximumPacketSize ? opts.properties.maximumPacketSize : 0\n\n  let propertiesData = getProperties(stream, properties)\n  if (maximumPacketSize) {\n    while (length + propertiesData.length > maximumPacketSize) {\n      const currentMayEmptyProp = mayEmptyProps.shift()\n      if (currentMayEmptyProp && properties[currentMayEmptyProp]) {\n        delete properties[currentMayEmptyProp]\n        propertiesData = getProperties(stream, properties)\n      } else {\n        return false\n      }\n    }\n  }\n  return propertiesData\n}\n\nfunction writeProperty (stream, propName, value) {\n  const type = protocol.propertiesTypes[propName]\n  switch (type) {\n    case 'byte': {\n      stream.write(Buffer.from([protocol.properties[propName]]))\n      stream.write(Buffer.from([+value]))\n      break\n    }\n    case 'int8': {\n      stream.write(Buffer.from([protocol.properties[propName]]))\n      stream.write(Buffer.from([value]))\n      break\n    }\n    case 'binary': {\n      stream.write(Buffer.from([protocol.properties[propName]]))\n      writeStringOrBuffer(stream, value)\n      break\n    }\n    case 'int16': {\n      stream.write(Buffer.from([protocol.properties[propName]]))\n      writeNumber(stream, value)\n      break\n    }\n    case 'int32': {\n      stream.write(Buffer.from([protocol.properties[propName]]))\n      write4ByteNumber(stream, value)\n      break\n    }\n    case 'var': {\n      stream.write(Buffer.from([protocol.properties[propName]]))\n      writeVarByteInt(stream, value)\n      break\n    }\n    case 'string': {\n      stream.write(Buffer.from([protocol.properties[propName]]))\n      writeString(stream, value)\n      break\n    }\n    case 'pair': {\n      Object.getOwnPropertyNames(value).forEach(name => {\n        const currentValue = value[name]\n        if (Array.isArray(currentValue)) {\n          currentValue.forEach(value => {\n            stream.write(Buffer.from([protocol.properties[propName]]))\n            writeStringPair(stream, name.toString(), value.toString())\n          })\n        } else {\n          stream.write(Buffer.from([protocol.properties[propName]]))\n          writeStringPair(stream, name.toString(), currentValue.toString())\n        }\n      })\n      break\n    }\n    default: {\n      stream.destroy(new Error(`Invalid property ${propName} value: ${value}`))\n      return false\n    }\n  }\n}\n\nfunction writeProperties (stream, properties, propertiesLength) {\n  /* write properties to stream */\n  writeVarByteInt(stream, propertiesLength)\n  for (const propName in properties) {\n    if (Object.prototype.hasOwnProperty.call(properties, propName) && properties[propName] != null) {\n      const value = properties[propName]\n      if (Array.isArray(value)) {\n        for (let valueIndex = 0; valueIndex < value.length; valueIndex++) {\n          writeProperty(stream, propName, value[valueIndex])\n        }\n      } else {\n        writeProperty(stream, propName, value)\n      }\n    }\n  }\n}\n\nfunction byteLength (bufOrString) {\n  if (!bufOrString) return 0\n  else if (bufOrString instanceof Buffer) return bufOrString.length\n  else return Buffer.byteLength(bufOrString)\n}\n\nfunction isStringOrBuffer (field) {\n  return typeof field === 'string' || field instanceof Buffer\n}\n\nmodule.exports = generate\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mqtt-packet/writeToStream.js\n");

/***/ })

};
;