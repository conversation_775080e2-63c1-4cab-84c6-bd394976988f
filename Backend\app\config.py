"""Main Application Configuration"""

import os
import logging
from dotenv import load_dotenv
load_dotenv()

# Prefer standard uppercase name, fall back to legacy key, default to LOCAL
ENV = os.getenv("ENVIRONMENT")

API_PREFIX = "/api/v1"
PROJECT_NAME = "TechSartHAI"
CORS_ORIGINS = ["*"] 
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = ["*"]
CORS_ALLOW_HEADERS = ["*"]

MQTT_CORS_ORIGINS = ["*"] 
MQTT_CORS_ALLOW_CREDENTIALS = True
MQTT_CORS_ALLOW_METHODS = ["*"]
MQTT_CORS_ALLOW_HEADERS = ["*"]

SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES =3600
MIN_PASSWORD_LENGTH = 4
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
# Try environment-specific first (e.g., DATABASE_URL_DEV), then generic DATABASE_URL
# Fall back to generic DATABASE_URL if environment-specific is not set
DATABASE_URL = os.getenv(f"DATABASE_URL_{ENV}")

# Try environment-specific Qdrant URL first (e.g., QDRANT_URL_Dev), then generic QDRANT_URL
# Fall back to generic QDRANT_URL if environment-specific is not set
QDRANT_URL = os.getenv(f"QDRANT_URL_{ENV}")
GRPC_PORT = os.getenv(f"GRPC_PORT_{ENV}")

# Qdrant API Key Configuration
if ENV == "Prod":
    QDRANT_API_KEY = os.getenv("QDRANT_API_KEY_Prod")
else:
    QDRANT_API_KEY = os.getenv("QDRANT_API_KEY_Dev")

# Use environment variables if available, otherwise use hardcoded values for development
MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT")
MINIO_PUBLIC_ENDPOINT = os.getenv("MINIO_PUBLIC_ENDPOINT")
MINIO_SECURE = os.getenv("MINIO_SECURE")
MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY")
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY")
# Define bucket types without hardcoded bucket names
MINIO_BUCKET_TYPES = ["chat_attachments", "knowledge_documents", "image_playground", "user_uploads", "system_backups", "temporary_files"]

# Environment-specific main bucket names
if ENV == "Prod":
    MINIO_BUCKET = os.getenv("MINIO_BUCKET_Prod")
else:
    MINIO_BUCKET = os.getenv("MINIO_BUCKET_Dev")

DATA_INGESTION_SERVICE_URL = os.getenv("DATA_INGESTION_SERVICE_URL")
DATA_RETRIEVER_SERVICE_URL = os.getenv("DATA_RETRIEVER_SERVICE_URL")
EMBEDDING_SERVICE_URL = os.getenv("EMBEDDING_SERVICE_URL")
RERANKING_SERVICE_URL = os.getenv("RERANKING_SERVICE_URL")

# Redis Configuration
REDIS_HOST = os.getenv("REDIS_HOST", "**************")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_DB = int(os.getenv("REDIS_DB", 0))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD")
REDIS_SSL = os.getenv("REDIS_SSL", "false").lower() == "true"
REDIS_DECODE_RESPONSES = True
REDIS_MAX_CONNECTIONS = int(os.getenv("REDIS_MAX_CONNECTIONS", 50))

# Cache TTL Settings (in seconds)
CACHE_TTL_SHORT = int(os.getenv("CACHE_TTL_SHORT", 300))  # 5 minutes
CACHE_TTL_MEDIUM = int(os.getenv("CACHE_TTL_MEDIUM", 3600))  # 1 hour
CACHE_TTL_LONG = int(os.getenv("CACHE_TTL_LONG", 86400))  # 24 hours
CACHE_TTL_CHAT_MESSAGES = int(os.getenv("CACHE_TTL_CHAT_MESSAGES", 1800))  # 30 minutes
CACHE_TTL_KNOWLEDGE_BASE = int(os.getenv("CACHE_TTL_KNOWLEDGE_BASE", 7200))  # 2 hours
CACHE_TTL_USER_SESSION = int(os.getenv("CACHE_TTL_USER_SESSION", 3600))  # 1 hour
CACHE_TTL_ADMIN_DASHBOARD = int(os.getenv("CACHE_TTL_ADMIN_DASHBOARD", 600))  # 10 minutes
CACHE_TTL_STATELESS_DEFAULT = int(os.getenv("CACHE_TTL_STATELESS_DEFAULT", 3600))  # 1 hour
CACHE_TTL_STATELESS_CONFIG = int(os.getenv("CACHE_TTL_STATELESS_CONFIG", 7200))  # 2 hours
CACHE_TTL_STATELESS_SESSION = int(os.getenv("CACHE_TTL_STATELESS_SESSION", 1800))  # 30 minutes

# Redis Key Prefixes
# Try environment-specific first (e.g., REDIS_PREFIX_DEV), then generic, with a safe default
REDIS_PREFIX = os.getenv(f"REDIS_PREFIX_{ENV}")
CACHE_PREFIX_CHAT = f"{REDIS_PREFIX}:chat"
CACHE_PREFIX_KNOWLEDGE = f"{REDIS_PREFIX}:knowledge"
CACHE_PREFIX_USER = f"{REDIS_PREFIX}:user"
CACHE_PREFIX_ADMIN = f"{REDIS_PREFIX}:admin"
CACHE_PREFIX_GROUP = f"{REDIS_PREFIX}:group"
CACHE_PREFIX_LIVE = f"{REDIS_PREFIX}:live"
CACHE_PREFIX_NOTES = f"{REDIS_PREFIX}:notes"
CACHE_PREFIX_TICKETS = f"{REDIS_PREFIX}:tickets"
CACHE_PREFIX_STATELESS = f"{REDIS_PREFIX}:stateless"

# MQTT Configuration
MQTT_HOST = os.getenv("MQTT_HOST")
MQTT_PORT = int(os.getenv("MQTT_PORT"))
MQTT_WS_PORT = int(os.getenv("MQTT_WS_PORT"))
MQTT_WSS_PORT = int(os.getenv("MQTT_WSS_PORT"))
MQTT_TLS_ENABLED = os.getenv("MQTT_TLS_ENABLED").lower() == "true"
MQTT_KEEPALIVE = int(os.getenv("MQTT_KEEPALIVE"))
MQTT_RECONNECT_DELAY = int(os.getenv("MQTT_RECONNECT_DELAY"))
MQTT_CLIENT_ID_PREFIX = os.getenv("MQTT_CLIENT_ID_PREFIX")

# MQTT Broker JWT Configuration (separate from API JWT)
MQTT_BROKER_JWT_SECRET = os.getenv("MQTT_BROKER_JWT_SECRET")
MQTT_BROKER_JWT_ALGORITHM = "HS256"
MQTT_BROKER_JWT_EXPIRE_MINUTES = int(os.getenv("MQTT_BROKER_JWT_EXPIRE_MINUTES"))

# MQTT Topic Configuration
MQTT_TOPIC_PREFIX = f"app/emqx"

# Message Deduplication Settings
MQTT_DEDUPE_TTL = int(os.getenv("MQTT_DEDUPE_TTL"))
REDIS_PREFIX_Space = f"{REDIS_PREFIX}:spaces"

# ---------------------------------------------------------------------------
# Agent/Model configuration (Gemini 2.5 Flash Lite)
#
# Gemini does not publish an offline tokenizer. We use a local approximation
# (tiktoken) for budgeting. For exact counts, call Google's count_tokens API.
# ---------------------------------------------------------------------------
# Token budgeting
MODEL_WINDOW_TOKENS = int(os.getenv("MODEL_WINDOW_TOKENS", 100000))
RESERVE_FOR_ANSWER = int(os.getenv("RESERVE_FOR_ANSWER", 1024))
INPUT_BUDGET = MODEL_WINDOW_TOKENS - RESERVE_FOR_ANSWER

# tiktoken encoding approximation for Gemini
# cl100k_base is a reasonable default
TOKEN_ENCODING = os.getenv("TOKEN_ENCODING", "cl100k_base")

# Overhead applied per chat message line when estimating tokens from role-tagged
# transcripts (e.g., "user: ...", "assistant: ..."). This approximates small
# envelope costs such as role labels and separators. Tune per deployment.
MESSAGE_OVERHEAD_TOKENS = int(os.getenv("TOKEN_MESSAGE_OVERHEAD", 4))

# ---------------------------------------------------------------------------
# Image Space Configuration
# ---------------------------------------------------------------------------
# Maximum image upload size in MB
MAX_IMAGE_UPLOAD_MB = int(os.getenv("MAX_IMAGE_UPLOAD_MB", 10))

# Maximum number of images per query for image spaces
MAX_IMAGES_PER_QUERY = int(os.getenv("MAX_IMAGES_PER_QUERY", 4))

# Maximum long side dimension for image resizing (pixels)
IMAGE_MAX_LONG_SIDE_PX = int(os.getenv("IMAGE_MAX_LONG_SIDE_PX", 1600))

# Allowed image MIME types (strict allowlist)
ALLOWED_IMAGE_TYPES = {
    "image/jpeg": "jpg",
    "image/png": "png",
    "image/webp": "webp",
    "image/heic": "heic",
    "image/heif": "heif"
}

# Inline vs Files API threshold (~20 MB total request size)
IMAGE_INLINE_MAX_SIZE_MB = int(os.getenv("IMAGE_INLINE_MAX_SIZE_MB", 20))