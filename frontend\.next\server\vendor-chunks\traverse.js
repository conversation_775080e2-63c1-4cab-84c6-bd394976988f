/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/traverse";
exports.ids = ["vendor-chunks/traverse"];
exports.modules = {

/***/ "(ssr)/./node_modules/traverse/index.js":
/*!****************************************!*\
  !*** ./node_modules/traverse/index.js ***!
  \****************************************/
/***/ ((module) => {

eval("module.exports = Traverse;\nfunction Traverse (obj) {\n    if (!(this instanceof Traverse)) return new Traverse(obj);\n    this.value = obj;\n}\n\nTraverse.prototype.get = function (ps) {\n    var node = this.value;\n    for (var i = 0; i < ps.length; i ++) {\n        var key = ps[i];\n        if (!Object.hasOwnProperty.call(node, key)) {\n            node = undefined;\n            break;\n        }\n        node = node[key];\n    }\n    return node;\n};\n\nTraverse.prototype.set = function (ps, value) {\n    var node = this.value;\n    for (var i = 0; i < ps.length - 1; i ++) {\n        var key = ps[i];\n        if (!Object.hasOwnProperty.call(node, key)) node[key] = {};\n        node = node[key];\n    }\n    node[ps[i]] = value;\n    return value;\n};\n\nTraverse.prototype.map = function (cb) {\n    return walk(this.value, cb, true);\n};\n\nTraverse.prototype.forEach = function (cb) {\n    this.value = walk(this.value, cb, false);\n    return this.value;\n};\n\nTraverse.prototype.reduce = function (cb, init) {\n    var skip = arguments.length === 1;\n    var acc = skip ? this.value : init;\n    this.forEach(function (x) {\n        if (!this.isRoot || !skip) {\n            acc = cb.call(this, acc, x);\n        }\n    });\n    return acc;\n};\n\nTraverse.prototype.deepEqual = function (obj) {\n    if (arguments.length !== 1) {\n        throw new Error(\n            'deepEqual requires exactly one object to compare against'\n        );\n    }\n    \n    var equal = true;\n    var node = obj;\n    \n    this.forEach(function (y) {\n        var notEqual = (function () {\n            equal = false;\n            //this.stop();\n            return undefined;\n        }).bind(this);\n        \n        //if (node === undefined || node === null) return notEqual();\n        \n        if (!this.isRoot) {\n        /*\n            if (!Object.hasOwnProperty.call(node, this.key)) {\n                return notEqual();\n            }\n        */\n            if (typeof node !== 'object') return notEqual();\n            node = node[this.key];\n        }\n        \n        var x = node;\n        \n        this.post(function () {\n            node = x;\n        });\n        \n        var toS = function (o) {\n            return Object.prototype.toString.call(o);\n        };\n        \n        if (this.circular) {\n            if (Traverse(obj).get(this.circular.path) !== x) notEqual();\n        }\n        else if (typeof x !== typeof y) {\n            notEqual();\n        }\n        else if (x === null || y === null || x === undefined || y === undefined) {\n            if (x !== y) notEqual();\n        }\n        else if (x.__proto__ !== y.__proto__) {\n            notEqual();\n        }\n        else if (x === y) {\n            // nop\n        }\n        else if (typeof x === 'function') {\n            if (x instanceof RegExp) {\n                // both regexps on account of the __proto__ check\n                if (x.toString() != y.toString()) notEqual();\n            }\n            else if (x !== y) notEqual();\n        }\n        else if (typeof x === 'object') {\n            if (toS(y) === '[object Arguments]'\n            || toS(x) === '[object Arguments]') {\n                if (toS(x) !== toS(y)) {\n                    notEqual();\n                }\n            }\n            else if (x instanceof Date || y instanceof Date) {\n                if (!(x instanceof Date) || !(y instanceof Date)\n                || x.getTime() !== y.getTime()) {\n                    notEqual();\n                }\n            }\n            else {\n                var kx = Object.keys(x);\n                var ky = Object.keys(y);\n                if (kx.length !== ky.length) return notEqual();\n                for (var i = 0; i < kx.length; i++) {\n                    var k = kx[i];\n                    if (!Object.hasOwnProperty.call(y, k)) {\n                        notEqual();\n                    }\n                }\n            }\n        }\n    });\n    \n    return equal;\n};\n\nTraverse.prototype.paths = function () {\n    var acc = [];\n    this.forEach(function (x) {\n        acc.push(this.path); \n    });\n    return acc;\n};\n\nTraverse.prototype.nodes = function () {\n    var acc = [];\n    this.forEach(function (x) {\n        acc.push(this.node);\n    });\n    return acc;\n};\n\nTraverse.prototype.clone = function () {\n    var parents = [], nodes = [];\n    \n    return (function clone (src) {\n        for (var i = 0; i < parents.length; i++) {\n            if (parents[i] === src) {\n                return nodes[i];\n            }\n        }\n        \n        if (typeof src === 'object' && src !== null) {\n            var dst = copy(src);\n            \n            parents.push(src);\n            nodes.push(dst);\n            \n            Object.keys(src).forEach(function (key) {\n                dst[key] = clone(src[key]);\n            });\n            \n            parents.pop();\n            nodes.pop();\n            return dst;\n        }\n        else {\n            return src;\n        }\n    })(this.value);\n};\n\nfunction walk (root, cb, immutable) {\n    var path = [];\n    var parents = [];\n    var alive = true;\n    \n    return (function walker (node_) {\n        var node = immutable ? copy(node_) : node_;\n        var modifiers = {};\n        \n        var state = {\n            node : node,\n            node_ : node_,\n            path : [].concat(path),\n            parent : parents.slice(-1)[0],\n            key : path.slice(-1)[0],\n            isRoot : path.length === 0,\n            level : path.length,\n            circular : null,\n            update : function (x) {\n                if (!state.isRoot) {\n                    state.parent.node[state.key] = x;\n                }\n                state.node = x;\n            },\n            'delete' : function () {\n                delete state.parent.node[state.key];\n            },\n            remove : function () {\n                if (Array.isArray(state.parent.node)) {\n                    state.parent.node.splice(state.key, 1);\n                }\n                else {\n                    delete state.parent.node[state.key];\n                }\n            },\n            before : function (f) { modifiers.before = f },\n            after : function (f) { modifiers.after = f },\n            pre : function (f) { modifiers.pre = f },\n            post : function (f) { modifiers.post = f },\n            stop : function () { alive = false }\n        };\n        \n        if (!alive) return state;\n        \n        if (typeof node === 'object' && node !== null) {\n            state.isLeaf = Object.keys(node).length == 0;\n            \n            for (var i = 0; i < parents.length; i++) {\n                if (parents[i].node_ === node_) {\n                    state.circular = parents[i];\n                    break;\n                }\n            }\n        }\n        else {\n            state.isLeaf = true;\n        }\n        \n        state.notLeaf = !state.isLeaf;\n        state.notRoot = !state.isRoot;\n        \n        // use return values to update if defined\n        var ret = cb.call(state, state.node);\n        if (ret !== undefined && state.update) state.update(ret);\n        if (modifiers.before) modifiers.before.call(state, state.node);\n        \n        if (typeof state.node == 'object'\n        && state.node !== null && !state.circular) {\n            parents.push(state);\n            \n            var keys = Object.keys(state.node);\n            keys.forEach(function (key, i) {\n                path.push(key);\n                \n                if (modifiers.pre) modifiers.pre.call(state, state.node[key], key);\n                \n                var child = walker(state.node[key]);\n                if (immutable && Object.hasOwnProperty.call(state.node, key)) {\n                    state.node[key] = child.node;\n                }\n                \n                child.isLast = i == keys.length - 1;\n                child.isFirst = i == 0;\n                \n                if (modifiers.post) modifiers.post.call(state, child);\n                \n                path.pop();\n            });\n            parents.pop();\n        }\n        \n        if (modifiers.after) modifiers.after.call(state, state.node);\n        \n        return state;\n    })(root).node;\n}\n\nObject.keys(Traverse.prototype).forEach(function (key) {\n    Traverse[key] = function (obj) {\n        var args = [].slice.call(arguments, 1);\n        var t = Traverse(obj);\n        return t[key].apply(t, args);\n    };\n});\n\nfunction copy (src) {\n    if (typeof src === 'object' && src !== null) {\n        var dst;\n        \n        if (Array.isArray(src)) {\n            dst = [];\n        }\n        else if (src instanceof Date) {\n            dst = new Date(src);\n        }\n        else if (src instanceof Boolean) {\n            dst = new Boolean(src);\n        }\n        else if (src instanceof Number) {\n            dst = new Number(src);\n        }\n        else if (src instanceof String) {\n            dst = new String(src);\n        }\n        else {\n            dst = Object.create(Object.getPrototypeOf(src));\n        }\n        \n        Object.keys(src).forEach(function (key) {\n            dst[key] = src[key];\n        });\n        return dst;\n    }\n    else return src;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/traverse/index.js\n");

/***/ })

};
;