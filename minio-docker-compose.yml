version: '3'

services:
  minio:
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - /root/minio-data:/data
    environment:
      MINIO_ROOT_USER: techsarthi_admin
      MINIO_ROOT_PASSWORD: admin@123
      # Disable storage space checking
      MINIO_DISK_USAGE_THRESHOLD: "0"
      MINIO_API_REQUESTS_MAX: "0"
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped

# No need for volume definition when using direct host path mount
