"""
Intelligent RAG Agent Service
Main FastAPI service for agent-based document retrieval
"""

import uvicorn
import logging
from datetime import datetime
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from agent_pipeline import IntelligentRAGAgent
from config import RetrieverConfig

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Intelligent RAG Agent Service",
    description="Agent-based document retrieval with iterative query generation",
    version="2.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class AgentQueryRequest(BaseModel):
    query: str
    collection_name: str
    username: str
    retriever_config: Dict[str, Any]
    gemini_api_key: str
    query_params: Optional[Dict[str, Any]] = None  # For compatibility with old format
    chat_history: Optional[List[Dict[str, Any]]] = None
    allowed_files: Optional[List[str]] = None
    extra_filter: Optional[Dict[str, Any]] = None


class AgentQueryResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@app.post("/query", response_model=AgentQueryResponse)
async def agent_query_endpoint(request: AgentQueryRequest):
    """Process a query through the intelligent RAG agent"""
    start_time = datetime.now()
    logger.info(f"========== QUERY START TIME: {start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]} ==========")
    
    try:
        logger.info(f"Received agent query request for user: {request.username}")
        logger.info(f"Query: {request.query[:100]}...")
        
        # Extract allowed_files from query_params if not provided directly
        allowed_files = request.allowed_files
        extra_filter = request.extra_filter
        chat_history = request.chat_history
        
        if request.query_params:
            logger.info(f"Using query_params format: {list(request.query_params.keys())}")
            if not allowed_files and 'allowed_files' in request.query_params:
                allowed_files = request.query_params.get('allowed_files')
            if not extra_filter and 'extra_filter' in request.query_params:
                extra_filter = request.query_params.get('extra_filter')
            if not chat_history and 'chat_history' in request.query_params:
                chat_history = request.query_params.get('chat_history')
        
        logger.info(f"Agent will use allowed_files: {allowed_files}")
        logger.info(f"Agent will use extra_filter: {extra_filter}")
        
        # Use agent's own config from environment variables, not backend's config
        config = RetrieverConfig(
            gemini_api_key=request.gemini_api_key,
            qdrant_url=request.retriever_config.get('qdrant_url'),
            prefer_grpc=request.retriever_config.get('prefer_grpc', True),
            grpc_port=request.retriever_config.get('grpc_port'),
            qdrant_api_key=request.retriever_config.get('qdrant_api_key'),
            database_url=request.retriever_config.get('database_url'),
            embedding_api_url=request.retriever_config.get('embedding_api_url'),
            reranking_service_url=request.retriever_config.get('reranking_service_url'),
            # Use agent's own LLM models from environment, not backend's
            # query_llm and response_model will default to values from config.py (environment variables)
        )
        
        agent = IntelligentRAGAgent(config)
        
        result = agent.run_agent(
            query=request.query,
            collection_name=request.collection_name,
            allowed_files=allowed_files,
            chat_history=chat_history,
            extra_filter=extra_filter
        )
        
        # Extract document IDs and sources for the backend
        raw_docs = result.get("all_raw_docs", [])
        filtered_docs = result.get("all_filtered_docs", [])
        
        # Build confidence score mapping from filtered docs (documents actually used)
        confidence_map = {}
        if filtered_docs:
            for doc in filtered_docs:
                doc_id = doc.metadata.get('id')
                confidence = doc.metadata.get('confidence_score', 0.0)
                if doc_id:
                    confidence_map[doc_id] = confidence
            logger.info(f"Built confidence map for {len(confidence_map)} filtered documents")
        
        # Build document_ids and document_sources in parallel, ensuring they stay aligned
        # For frontend display, only include documents with confidence >= 30%
        document_ids_for_frontend = []
        document_sources_for_frontend = []
        seen_ids = set()
        
        for doc in raw_docs:
            if isinstance(doc, dict):
                doc_id = doc.get("id")
                if doc_id and doc_id not in seen_ids:
                    seen_ids.add(doc_id)
                    
                    # Get confidence score from reranked data
                    confidence_score = confidence_map.get(doc_id, 0.0)
                    
                    # Only include documents with confidence >= 30% for frontend display
                    if confidence_score >= 0.3:
                        document_ids_for_frontend.append(doc_id)
                        
                        # Build document source entry for backward compatibility
                        document_sources_for_frontend.append({
                            "source": doc.get("source", "Unknown"),
                            "page_number": None,
                            "relevance_score": confidence_score,
                            "confidence_score": confidence_score,
                            "content_snippet": doc.get("content", "") if doc.get("content") else "",
                            "doc_id": doc_id  # Add doc_id for better mapping
                        })
        
        # If all confidence scores are less than 0.3, show all reranked documents instead
        if not document_ids_for_frontend and raw_docs:
            logger.info("All documents have confidence < 30%, showing all reranked documents instead")
            seen_ids = set()
            for doc in raw_docs:
                if isinstance(doc, dict):
                    doc_id = doc.get("id")
                    if doc_id and doc_id not in seen_ids:
                        seen_ids.add(doc_id)
                        confidence_score = confidence_map.get(doc_id, 0.0)
                        
                        document_ids_for_frontend.append(doc_id)
                        document_sources_for_frontend.append({
                            "source": doc.get("source", "Unknown"),
                            "page_number": None,
                            "relevance_score": confidence_score,
                            "confidence_score": confidence_score,
                            "content_snippet": doc.get("content", "") if doc.get("content") else "",
                            "doc_id": doc_id
                        })
        
        # document_ids and document_sources are now aligned 1:1
        unique_document_ids = document_ids_for_frontend  # Already deduplicated and filtered
        
        # Log confidence scores for debugging
        logger.info(f"Total documents used for context: {len(raw_docs)}")
        logger.info(f"Documents sent to frontend (confidence >= 30%): {len(document_ids_for_frontend)}")
        for idx, (doc_id, src) in enumerate(zip(document_ids_for_frontend, document_sources_for_frontend)):
            logger.info(f"  Doc {idx}: ID={doc_id}, Source={src['source']}, Confidence={src['confidence_score']:.4f}")
        
        response_data = {
            "success": True,
            "agent_name": f"{request.username.title()} Intelligent Assistant",
            "query": request.query,
            "answer": result.get("final_response", "I'm having trouble processing your request right now. Please try rephrasing your question or try again in a moment."),
            "used_rag": True,
            "document_ids": unique_document_ids,  # For backend to fetch full documents (filtered >= 30%)
            "document_sources": document_sources_for_frontend,  # Fallback for legacy support (filtered >= 30%)
            "sources": result.get("sources_used", []),  # Simple list of source filenames
            "context_quality_score": result.get("context_quality_score", 0.0),
            "iterations": result.get("iteration_count", 0),
            "queries_generated": result.get("queries_generated_count", 0),
            "multi_queries": result.get("multi_queries", []),
            "document_count": len(raw_docs),  # Total docs used for context generation
            "metadata": {
                "has_enough_context": result.get("has_enough_context", False),
                "max_iterations_reached": result.get("iteration_count", 0) >= result.get("max_iterations", 3),
                "total_docs_for_context": len(raw_docs),  # All docs used (including <30%)
                "docs_shown_to_user": len(document_ids_for_frontend)  # Only docs >= 30%
            }
        }
        
        logger.info(f"Agent completed - Iterations: {response_data['iterations']}, Queries: {response_data['queries_generated']}")
        
        end_time = datetime.now()
        elapsed_time = (end_time - start_time).total_seconds()
        logger.info(f"========== QUERY END TIME: {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]} ==========")
        logger.info(f"========== TOTAL QUERY EXECUTION TIME: {elapsed_time:.3f} seconds ==========")
        
        return AgentQueryResponse(success=True, data=response_data)
        
    except Exception as e:
        end_time = datetime.now()
        elapsed_time = (end_time - start_time).total_seconds()
        logger.error(f"Error processing agent query: {str(e)}")
        logger.error(f"========== QUERY FAILED - END TIME: {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]} ==========")
        logger.error(f"========== QUERY EXECUTION TIME BEFORE FAILURE: {elapsed_time:.3f} seconds ==========")
        import traceback
        logger.error(traceback.format_exc())
        return AgentQueryResponse(success=False, error=str(e))


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Retriever Agent",
        "version": "2.0.0"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main_agent:app",
        host="0.0.0.0",
        port=8040,
        reload=False,
        log_level="info"
    )
