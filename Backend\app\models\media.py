from sqlalchemy import Column, Integer, String, Text, Foreign<PERSON>ey, BigInteger, DateTime, Index
from sqlalchemy.orm import relationship
from datetime import datetime

from ..database import Base

class Media(Base):
    """Stores metadata for any image/file shared in a conversation."""
    __tablename__ = "media"

    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(Integer, ForeignKey('messages.id', ondelete='CASCADE'), nullable=False)
    conversation_id = Column(Integer, ForeignKey('conversations.id', ondelete='CASCADE'), nullable=False)
    uploader_id = Column(Integer, ForeignKey('users.id', ondelete='SET NULL'))

    media_type = Column(String(20), nullable=False)  # image | file | video | audio
    filename = Column(String(255), nullable=False)  # Original filename
    
    # MinIO storage fields (replacing original_url, thumb_url)
    minio_bucket = Column(String(100), nullable=False)
    minio_object_key = Column(String(500), nullable=False)
    file_url = Column(Text, nullable=True)  # Cached URL for quick access
    
    # Thumbnail for images/videos
    thumb_minio_bucket = Column(String(100), nullable=True)
    thumb_minio_object_key = Column(String(500), nullable=True)
    thumb_url = Column(Text, nullable=True)  # Cached thumbnail URL
    
    size_bytes = Column(BigInteger, nullable=True)
    content_type = Column(String(100), nullable=True)
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships (lazy='joined' keeps queries lean)
    message = relationship("Message")
    conversation = relationship("Conversation")
    uploader = relationship("User")

    __table_args__ = (
        Index('idx_media_conversation', 'conversation_id', 'created_at'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'message_id': self.message_id,
            'conversation_id': self.conversation_id,
            'uploader_id': self.uploader_id,
            'media_type': self.media_type,
            'filename': self.filename,
            'minio_bucket': self.minio_bucket,
            'minio_object_key': self.minio_object_key,
            'file_url': self.file_url,
            'thumb_minio_bucket': self.thumb_minio_bucket,
            'thumb_minio_object_key': self.thumb_minio_object_key,
            'thumb_url': self.thumb_url,
            'size_bytes': self.size_bytes,
            'content_type': self.content_type,
            'width': self.width,
            'height': self.height,
            'created_at': self.created_at.isoformat() if self.created_at else None,
        } 