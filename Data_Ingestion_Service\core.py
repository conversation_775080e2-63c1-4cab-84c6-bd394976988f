import re

def custom_markdown_splitter(
    text,
    max_characters=2000,
    combine_text_under_n_chars=1000,
    new_after_n_chars=1200,
    overlap_chars=150,
):
    """
    Custom markdown splitter that splits text into chunks of a maximum number of characters,
    while preserving tables and other formatting.
    
    Args:
        text (str): The text to split.
        max_characters (int): The maximum number of characters per chunk.
        combine_text_under_n_chars (int): The minimum number of characters to combine into a single chunk.
        new_after_n_chars (int): The minimum number of characters to create a new chunk after.
        overlap_chars (int): The number of characters to overlap between chunks.
        
    Returns:
        list: A list of chunks of text. 
    """
    def get_safe_overlap(chunk, max_overlap):
        if len(chunk) <= max_overlap:
            return chunk
        tail = chunk[-max_overlap:]
        first_space = tail.find(' ')
        return tail[first_space + 1:] if first_space != -1 else ''

    def is_table_block(block):
        """Check if a block contains table content"""
        lines = block.split('\n')
        table_lines = 0
        for line in lines:
            if re.match(r'^\s*\|.*\|\s*$', line) or re.match(r'^\s*[-| ]+\s*$', line):
                table_lines += 1
        return table_lines > 0

    def group_paragraphs_with_tables(text):
        lines = text.splitlines()
        blocks = []
        temp_block = []
        in_table = False

        for line in lines:
            if re.match(r'^\s*\|.*\|\s*$', line):
                temp_block.append(line)
                in_table = True
            elif in_table and re.match(r'^\s*[-| ]+\s*$', line):  # separator like |---|
                temp_block.append(line)
            elif in_table and line.strip() == '':
                # End of table
                blocks.append('\n'.join(temp_block).strip())
                temp_block = []
                in_table = False
            elif in_table:
                temp_block.append(line)
            elif line.strip() == '':
                if temp_block:
                    blocks.append('\n'.join(temp_block).strip())
                    temp_block = []
            else:
                temp_block.append(line)

        if temp_block:
            blocks.append('\n'.join(temp_block).strip())

        return blocks

    def find_safe_split_point(paragraphs, current_index, current_chunk_length):
        """Find a safe split point that doesn't break tables"""
        # Check if the current paragraph is a table
        if current_index < len(paragraphs) and is_table_block(paragraphs[current_index]):
            # Find the last non-table paragraph before this point
            for i in range(current_index - 1, -1, -1):
                if not is_table_block(paragraphs[i]):
                    return i + 1  # Split after this paragraph
            # If no safe point found, return current index (keep table together)
            return current_index
        return current_index

    def split_oversized_paragraph(para, max_chars, overlap_chars):
        """Split a paragraph that's too large, trying to preserve structure"""
        if len(para) <= max_chars:
            return [para]
        
        chunks = []
        current_pos = 0
        
        while current_pos < len(para):
            # Calculate chunk end position
            chunk_end = min(current_pos + max_chars, len(para))
            
            # If this isn't the last chunk, try to find a good break point
            if chunk_end < len(para):
                # Look for sentence breaks first (. ! ?)
                for i in range(chunk_end, max(current_pos, chunk_end - 200), -1):
                    if para[i-1] in '.!?' and para[i] in ' \n':
                        chunk_end = i
                        break
                else:
                    # Look for line breaks
                    for i in range(chunk_end, max(current_pos, chunk_end - 100), -1):
                        if para[i] == '\n':
                            chunk_end = i + 1
                            break
                    else:
                        # Look for word boundaries
                        for i in range(chunk_end, max(current_pos, chunk_end - 50), -1):
                            if para[i] == ' ':
                                chunk_end = i + 1
                                break
            
            chunk = para[current_pos:chunk_end].strip()
            if chunk:
                chunks.append(chunk)
            
            # Move to next position, accounting for overlap
            if chunk_end < len(para) and overlap_chars > 0:
                overlap_start = max(current_pos, chunk_end - overlap_chars)
                # Find word boundary for overlap
                while overlap_start > current_pos and para[overlap_start] != ' ':
                    overlap_start -= 1
                current_pos = overlap_start + 1 if overlap_start > current_pos else chunk_end
            else:
                current_pos = chunk_end
        
        return chunks

    paragraphs = [p for p in group_paragraphs_with_tables(text) if p.strip()]
    
    # Handle oversized paragraphs first
    processed_paragraphs = []
    for para in paragraphs:
        if len(para) > max_characters:
            # Split oversized paragraphs, but be gentle with tables
            if is_table_block(para):
                # For tables, only split if absolutely necessary (way over limit)
                if len(para) > max_characters * 2:
                    processed_paragraphs.extend(split_oversized_paragraph(para, max_characters, overlap_chars))
                else:
                    # Keep large tables intact
                    processed_paragraphs.append(para)
            else:
                processed_paragraphs.extend(split_oversized_paragraph(para, max_characters, overlap_chars))
        else:
            processed_paragraphs.append(para)
    
    paragraphs = processed_paragraphs
    chunks = []
    current_chunk = ''
    i = 0

    while i < len(paragraphs):
        para = paragraphs[i]
        
        # Check if adding this paragraph would exceed max_characters
        if len(current_chunk) + len(para) + 2 > max_characters:
            if current_chunk:
                # If we're about to split within or right before a table, find a safe split point
                if is_table_block(para):
                    safe_split_index = find_safe_split_point(paragraphs, i, len(current_chunk))
                    if safe_split_index < i:
                        # Rebuild current_chunk up to safe split point
                        safe_chunk_parts = []
                        temp_length = 0
                        j = 0
                        
                        # Find where the current chunk actually starts
                        chunk_parts = current_chunk.split('\n\n')
                        if len(chunks) > 0 and overlap_chars > 0:
                            # Remove overlap from calculation
                            overlap = get_safe_overlap(chunks[-1], overlap_chars)
                            if overlap and current_chunk.startswith(overlap):
                                current_chunk = current_chunk[len(overlap):].lstrip('\n')
                        
                        # Recalculate from the beginning to find safe split
                        temp_chunk = ''
                        for k in range(len(paragraphs)):
                            if temp_length + len(paragraphs[k]) + 2 <= max_characters or k < safe_split_index:
                                if temp_chunk:
                                    temp_chunk += '\n\n' + paragraphs[k]
                                else:
                                    temp_chunk = paragraphs[k]
                                temp_length = len(temp_chunk)
                                if k == safe_split_index - 1:
                                    break
                        
                        chunks.append(temp_chunk.strip())
                        
                        # Set up next chunk with overlap
                        if overlap_chars > 0:
                            overlap = get_safe_overlap(temp_chunk, overlap_chars)
                            current_chunk = overlap + '\n\n' + para if overlap else para
                        else:
                            current_chunk = para
                        i += 1
                        continue
                
                # Normal split behavior
                chunks.append(current_chunk.strip())
                current_chunk = para
                
                # Add overlap if specified
                if overlap_chars > 0 and chunks:
                    overlap = get_safe_overlap(chunks[-1], overlap_chars)
                    if overlap:
                        current_chunk = overlap + '\n\n' + current_chunk
            else:
                current_chunk = para
            i += 1
            continue

        # Normal paragraph combining logic
        if len(current_chunk) < combine_text_under_n_chars:
            current_chunk = current_chunk + '\n\n' + para if current_chunk else para
        else:
            if len(current_chunk) >= new_after_n_chars:
                # Before splitting, check if we're in the middle of a table
                if is_table_block(current_chunk) or is_table_block(para):
                    # Don't split, just add the paragraph
                    current_chunk = current_chunk + '\n\n' + para if current_chunk else para
                else:
                    chunks.append(current_chunk.strip())
                    if overlap_chars > 0:
                        overlap = get_safe_overlap(current_chunk, overlap_chars)
                        current_chunk = overlap + '\n\n' + para if overlap else para
                    else:
                        current_chunk = para
            else:
                current_chunk = current_chunk + '\n\n' + para if current_chunk else para
        
        i += 1

    if current_chunk:
        chunks.append(current_chunk.strip())

    return chunks