import os
import uuid
import base64
from pathlib import Path
from typing import List, Dict, Any, Tu<PERSON>, Union
import logging
import time
import shutil

pipeline_import_start = time.perf_counter()
logger = logging.getLogger(__name__)
# Gate noisy import timing logs behind an env flag
ENABLE_IMPORT_TIMING = os.getenv("ENABLE_IMPORT_TIMING", "0") == "1"
def _tlog(message: str):
    if ENABLE_IMPORT_TIMING:
        logger.info(message)
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: pipeline.py imports starting at {pipeline_import_start}")

from tabulate import tabulate
tabulate_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: tabulate imported in {tabulate_time - pipeline_import_start:.3f}s")

from datetime import datetime
datetime_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: datetime imported in {datetime_time - tabulate_time:.3f}s")

# Core libraries
_tlog("🔄 PIPELINE_IMPORT_TIMING: Starting pymupdf4llm import...")
pymupdf_start = time.perf_counter()
import pymupdf4llm
import pymupdf.pro
pymupdf_time = time.perf_counter()

_tlog("🔄 PIPELINE_IMPORT_TIMING: Starting langchain imports...")
langchain_start = time.perf_counter()
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
from langchain_qdrant import QdrantVectorStore, RetrievalMode
langchain_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: langchain imports completed in {langchain_time - langchain_start:.3f}s")

from embedding_client import EmbeddingClient, EmbeddingConfig, DenseEmbeddingWrapper, SparseEmbeddingWrapper
embedding_import_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: embedding_client imported in {embedding_import_time - langchain_time:.3f}s")

_tlog("🔄 PIPELINE_IMPORT_TIMING: Starting qdrant_client imports...")
qdrant_start = time.perf_counter()
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, SparseVectorParams, HnswConfig
qdrant_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: qdrant_client imports completed in {qdrant_time - qdrant_start:.3f}s")

_tlog("🔄 PIPELINE_IMPORT_TIMING: Starting google.genai imports...")
genai_start = time.perf_counter()
from google import genai
from google.genai import types
genai_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: google.genai imports completed in {genai_time - genai_start:.3f}s")

# TIME EVERY SINGLE REMAINING IMPORT
_tlog("🔄 PIPELINE_IMPORT_TIMING: Starting .core import...")
core_start = time.perf_counter()
from core import custom_markdown_splitter
core_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: .core (custom_markdown_splitter) imported in {core_time - core_start:.3f}s")

# COMMENTED OUT: unstructured takes 12+ seconds to load heavy ML models
# logger.info("🔄 PIPELINE_IMPORT_TIMING: Starting .unstructure import...")
# unstructure_start = time.perf_counter()
# from .unstructure import PDFContentExtractor
# unstructure_time = time.perf_counter()
# logger.info(f"🔄 PIPELINE_IMPORT_TIMING: .unstructure (PDFContentExtractor) imported in {unstructure_time - unstructure_start:.3f}s")
_tlog("🔄 PIPELINE_IMPORT_TIMING: .unstructure import SKIPPED (commented out for performance)")

_tlog("🔄 PIPELINE_IMPORT_TIMING: Starting re import...")
re_start = time.perf_counter()
import re
re_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: re imported in {re_time - re_start:.3f}s")

_tlog("🔄 PIPELINE_IMPORT_TIMING: Starting .config import...")
config_start = time.perf_counter()
from config import ProcessingConfig
config_end = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: .config (ProcessingConfig) imported in {config_end - config_start:.3f}s")

# Continue timing the remaining imports
_tlog("🔄 PIPELINE_IMPORT_TIMING: Starting remaining imports...")
remaining_start = time.perf_counter()

# from spire.doc import Document as SpireDocument, FileFormat
import asyncio
asyncio_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: asyncio imported in {asyncio_time - remaining_start:.3f}s")

_tlog("🔄 PIPELINE_IMPORT_TIMING: Starting asyncpg import...")
asyncpg_start = time.perf_counter()
import asyncpg
asyncpg_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: asyncpg imported in {asyncpg_time - asyncpg_start:.3f}s")

import sys
import os
config_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: sys/os/config imported in {config_time - asyncpg_time:.3f}s")

_tlog("🔄 PIPELINE_IMPORT_TIMING: Starting PIL import...")
pil_start = time.perf_counter()
from PIL import Image
pil_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: PIL.Image imported in {pil_time - pil_start:.3f}s")

import io
from itertools import chain
final_time = time.perf_counter()
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: io/itertools imported in {final_time - pil_time:.3f}s")

# NOW log the actual total import time
total_pipeline_import_time = time.perf_counter() - pipeline_import_start
_tlog(f"🔄 PIPELINE_IMPORT_TIMING: *** ACTUAL TOTAL pipeline.py import time: {total_pipeline_import_time:.3f}s ***")
# Removed OpenAI import as we now exclusively use Google Gemini models
class MultimodalDataPipeline:
    '''
    A pipeline for processing multimodal data (text, images, PDFs) and preparing it for retrieval.
    
    This class handles:
    - Document ingestion and chunking
    - Text embedding generation (dense and sparse)
    - Vector storage in Qdrant
    - Image processing and storage
    - Logging and configuration management
    
    The pipeline supports multiple input formats and uses both dense and sparse embeddings
    for improved retrieval performance. It integrates with Gemini for processing and Qdrant
    for vector storage.
    '''
    def __init__(self, config: ProcessingConfig):
        '''
        Initialize the pipeline with the given configuration.
        Heavy models are initialized once. Variable parameters like collection_name 
        and sqlite_db_path are passed per request.
        
        Args:
            config (ProcessingConfig): Configuration for the pipeline.
        '''
        self.config = config

        # Validate required configuration
        from config import GEMINI_API_KEY
        if not GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY is required but not provided")

        # Text model to be used for summarisation / Q&A generation
        self.llm_model = config.llm_model  # e.g. "gemini-2.5-flash-lite"
        # Number of parallel LLM calls to make when enhancing chunks
        self.parallel_workers = getattr(config, "llm_parallel_workers", 4)
        # Store base config that doesn't change per request
        self.gemini_api_key = GEMINI_API_KEY
        self.qdrant_url = config.qdrant_url
        self.qdrant_api_key = config.qdrant_api_key
        self.database_url = config.DATABASE_URL
        self.prefer_grpc = config.prefer_grpc
        self.grpc_port = config.grpc_port
        self.use_custom_splitter = config.use_custom_splitter
        self.use_unstructured = config.use_unstructured
        self.pymupdf_pro_key = config.pymupdf_pro_key

        # Initialize OCR processor (lazy initialization)
        self.ocr_processor = None
        
        # Initialize timing dictionary
        self.timing_stats = {
            'file_conversion': 0,
            'text_and_image_extraction': 0,
            'text_chunking': 0,
            'content_enhancement': 0,
            'image_description': 0,  # KEPT: For compatibility, but will remain 0 (skipped)
            'vector_store_ops': 0,
            'db_operations': 0,
            'total_time': 0
        }
        
        # Initialize text splitters for different document sizes
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=config.chunk_size,
            chunk_overlap=config.chunk_overlap,
            separators= ["\n\n", "\n", " ", ""]
        )
        
        # Large document text splitter with bigger chunks and sentence-aware splitting
        self.large_doc_text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1400,  # Increased size for large documents
            chunk_overlap=200,  # Slightly larger overlap
            separators=["\n\n", "\n", ". ", "! ", "? ", "; ", ", ", " ", ""]  # Sentence-aware separators
        )
        self.markdown_splitter = custom_markdown_splitter
        
        # Initialize embeddings via API client
        embedding_config = EmbeddingConfig(
            api_url=config.embedding_api_url,
            timeout=60,  # Longer timeout for document processing
            retry_attempts=5
        )

        # Create the embedding client instance
        from embedding_client import EmbeddingClient
        self.embedding_client = EmbeddingClient(embedding_config)

        # Initialize Gemini client
        from google import genai
        from google.genai import types
        self.gemini_client = genai.Client(api_key=self.gemini_api_key)

        # Initialize Image Embedding Client
        from image_embedding_client import ImageEmbeddingClient
        self.image_embedding_client = ImageEmbeddingClient(
            service_url="http://43.230.202.228:8019"
        )

        # Initialize Qdrant client
        self.qdrant_client = QdrantClient(
            url=config.qdrant_url,
            api_key=config.qdrant_api_key,
            prefer_grpc=config.prefer_grpc,
            grpc_port=config.grpc_port
        )

        # Initialize embedding functions
        # Create wrapper classes for compatibility with QdrantVectorStore
        self.dense_embeddings = DenseEmbeddingWrapper(self.embedding_client, normalize=True)
        self.sparse_embeddings = SparseEmbeddingWrapper(self.embedding_client)

        # Setup logging first before any logging calls
        self.setup_logging()

        pymupdf.pro.unlock(self.pymupdf_pro_key)
            
        # Test connection
        health = self.embedding_client.health_check()
        if health.get('status') not in ['healthy', 'partial']:
            raise RuntimeError(f"Embedding API not available: {health}")

        self.logger.info("Connected to Embedding API service")

        # Dynamic per-request variables (will be set per request)
        self.current_collection_name = None
        self.current_vector_store = None

        # Storage for processed documents (reset per request)
        self.processed_chunks = []
        self.processed_images = []
        self.raw_data = []
    
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def _get_ocr_processor(self):
        """Lazy initialization of OCR processor"""
        if self.ocr_processor is None:
            try:
                from utils.ocr_processor import OCRProcessor
                self.ocr_processor = OCRProcessor()
                self.logger.info("OCR processor initialized successfully")
            except ImportError as e:
                self.logger.warning(f"OCR processor not available: {e}")
                self.ocr_processor = None
        return self.ocr_processor
    
    # def convert_doc_to_pdf(self, doc_path: str) -> str:
    #     """Convert DOC/DOCX to PDF using Spire.Doc 
        
    #     Args:
    #         doc_path (str): The path to the DOC/DOCX file to convert.
            
    #     Returns:
    #         str: The path to the converted PDF file.
    #     """
    #     if not SPIRE_AVAILABLE:
    #         raise ImportError("Spire.Doc is not available for document conversion")
        
    #     try:
    #         doc = SpireDocument()
    #         doc.LoadFromFile(doc_path)
            
    #         # Always strip *all* suffixes and just append .pdf so that filenames like
    #         # "example.docx.txt" become "example.pdf"
    #         path_obj = Path(doc_path)
    #         output_path = str(path_obj.with_suffix(''))  # remove last suffix
    #         # If another suffix (like .docx) is still present, strip again
    #         while Path(output_path).suffix:
    #             output_path = str(Path(output_path).with_suffix(''))
    #         output_path = output_path + '.pdf'
    #         doc.SaveToFile(output_path, FileFormat.PDF)
    #         doc.Close()

    #         self.logger.info(f"Converted {doc_path} to {output_path}")
    #         return output_path
    #     except Exception as e:
    #         self.logger.error(f"Error converting {doc_path} to PDF: {e}")
    #         raise
    
    def document_to_markdown(self, doc_stream, page_chunks=False, doc_type="document"):
        """Convert documents (PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX) to markdown using PyMuPDF Pro

        Args:
            doc_stream: File-like object (stream)
            page_chunks: Whether to return page chunks or full document
            doc_type: Document type for logging (default: "document")

        Returns:
            tuple: (markdown_content, metadata) where markdown_content is str or List[str]
        """
        try:
            # Handle file streams (always streams in this pipeline)
            if hasattr(doc_stream, 'seek'):
                doc_stream.seek(0)
            data = doc_stream.read()
            # Reset stream position for potential reuse
            if hasattr(doc_stream, 'seek'):
                doc_stream.seek(0)
            self.logger.info(f"Read {len(data)} bytes from stream for {doc_type} processing")
            
            # Use document object approach for all document types
            doc = pymupdf.open(stream=data)
            md = pymupdf4llm.to_markdown(
                doc,
                page_chunks=page_chunks,
                write_images=False,
                dpi=300
            )
            
            # Store the original md for page-specific metadata access
            original_md = md
            
            # Extract metadata and tables from PyMuPDF4LLM output
            doc_metadata = None
            
            # Debug: Log the structure of md to understand the format
            self.logger.info(f"PyMuPDF4LLM output type: {type(md)}")
            if isinstance(md, list) and len(md) > 0:
                self.logger.info(f"First item type: {type(md[0])}")
                if isinstance(md[0], dict):
                    self.logger.info(f"First item keys: {md[0].keys()}")
                    # Extract both metadata and tables from the first chunk
                    first_chunk = md[0]
                    doc_metadata = {}
                    
                    if 'metadata' in first_chunk:
                        doc_metadata.update(first_chunk['metadata'])
                        self.logger.info(f"Found basic metadata: {first_chunk['metadata']}")
                    
                    if 'tables' in first_chunk:
                        doc_metadata['tables'] = first_chunk['tables']
                        self.logger.info(f"Found tables in first chunk: {first_chunk['tables']}")
                        
            elif isinstance(md, dict):
                self.logger.info(f"MD dict keys: {md.keys()}")
                doc_metadata = {}
                
                # Extract metadata if present
                if 'metadata' in md:
                    doc_metadata.update(md['metadata'])
                    self.logger.info(f"Found basic metadata: {md['metadata']}")
                
                # Extract tables if present at the same level
                if 'tables' in md:
                    doc_metadata['tables'] = md['tables']
                    self.logger.info(f"Found tables at root level: {md['tables']}")
                    
            # If we didn't find anything, set to None
            if not doc_metadata:
                doc_metadata = None
            
            doc.close()

        except Exception as e:
            self.logger.error(f"Error converting {doc_type} to markdown: {e}")
            raise

        # Extract text from PyMuPDF Pro format - each page is a dict with 'text' key
        if page_chunks:
            # Expect a list of page dictionaries from PyMuPDF Pro
            if isinstance(md, list):
                page_texts = []
                for page_dict in md:
                    if isinstance(page_dict, dict) and 'text' in page_dict:
                        # Extract text from PyMuPDF Pro format: {"text": "content", "metadata": {...}}
                        page_text = page_dict.get('text', '')
                        page_texts.append(page_text)
                    elif isinstance(page_dict, str):
                        # Already a string, use directly
                        page_texts.append(page_dict)
                    else:
                        # Convert other types to string
                        page_texts.append(str(page_dict))
                return page_texts, doc_metadata, original_md
            elif isinstance(md, str):
                # Single string returned - split into list for consistency
                return [md], doc_metadata, original_md
            else:
                # Fallback for any other type
                return [str(md)], doc_metadata, original_md
        else:
            # Expect one big string
            if isinstance(md, str):
                return md, doc_metadata, original_md
            if isinstance(md, list):
                # Join list items into one string
                try:
                    result = "\n\n".join(
                        [c.get('text', '') if isinstance(c, dict) else str(c) for c in md]
                    )
                    return result, doc_metadata, original_md
                except Exception:
                    return "\n\n".join([str(c) for c in md]), doc_metadata, original_md
            # Fallback
            return str(md), doc_metadata, original_md

    def extract_images_with_pymupdf(self, file_stream, file_type: str = "pdf") -> List[Dict[str, Any]]:
        """Extract images directly from PDF/DOC using PyMuPDF

        Args:
            file_stream: File stream object
            file_type: Type of file (pdf, doc, docx, etc.)

        Returns:
            List[Dict[str, Any]]: List of image dictionaries with metadata
        """
        images = []
        self.logger.info(f"Starting PyMuPDF image extraction from {file_type}")

        try:
            # Read stream data
            if hasattr(file_stream, 'seek'):
                file_stream.seek(0)
            data = file_stream.read()
            if hasattr(file_stream, 'seek'):
                file_stream.seek(0)

            # Open document with PyMuPDF
            doc = pymupdf.open(stream=data, filetype=file_type)

            # Iterate through all pages
            for page_number in range(len(doc)):
                page = doc.load_page(page_number)
                image_list = page.get_images(full=True)

                self.logger.info(f"Found {len(image_list)} images on page {page_number + 1}")

                for img_index, img_info in enumerate(image_list):
                    try:
                        xref = img_info[0]
                        base_image = doc.extract_image(xref)

                        image_bytes = base_image["image"]
                        image_ext = base_image["ext"]
                        width = base_image.get("width", 0)
                        height = base_image.get("height", 0)
                        colorspace = base_image.get("colorspace", 0)

                        # Filter out small/icon images (less than 100x100)
                        if width < 100 or height < 100:
                            self.logger.info(f"Skipping small image: {width}x{height}")
                            continue

                        # Get bounding box on page
                        bbox = None
                        try:
                            img_rects = page.get_image_rects(xref)
                            if img_rects:
                                rect = img_rects[0]
                                bbox = {
                                    'x0': rect.x0,
                                    'y0': rect.y0,
                                    'x1': rect.x1,
                                    'y1': rect.y1
                                }
                        except Exception as bbox_error:
                            self.logger.warning(f"Could not get bounding box for image: {bbox_error}")

                        # Convert to base64
                        image_base64 = base64.b64encode(image_bytes).decode('utf-8')

                        # Perform OCR on the image
                        extracted_text = ""
                        ocr_confidence = 0.0
                        ocr_processor = self._get_ocr_processor()
                        if ocr_processor:
                            try:
                                ocr_result = ocr_processor.extract_text_from_image(image_bytes)
                                extracted_text = ocr_result.get('text', '')
                                ocr_confidence = ocr_result.get('confidence', 0.0)

                                if extracted_text and ocr_confidence > 30:  # Only keep text with decent confidence
                                    self.logger.info(f"OCR extracted text from image (confidence: {ocr_confidence:.1f}%): {extracted_text[:100]}...")
                                else:
                                    self.logger.info(f"OCR found no usable text in image (confidence: {ocr_confidence:.1f}%)")
                                    extracted_text = ""  # Clear text if confidence is too low
                            except Exception as ocr_error:
                                self.logger.warning(f"OCR processing failed for image: {ocr_error}")
                                extracted_text = ""
                                ocr_confidence = 0.0

                        # Create image metadata
                        image_data = {
                            'base64': image_base64,
                            'page_number': page_number + 1,
                            'image_index': img_index,
                            'width': width,
                            'height': height,
                            'format': image_ext,
                            'colorspace': colorspace,
                            'bbox': bbox,
                            'xref': xref,
                            'extracted_text': extracted_text,
                            'ocr_confidence': ocr_confidence
                        }

                        images.append(image_data)
                        self.logger.info(f"Extracted image {img_index} from page {page_number + 1}: {width}x{height}")

                    except Exception as e:
                        self.logger.error(f"Error extracting image {img_index} from page {page_number}: {e}")
                        continue

            doc.close()
            self.logger.info(f"Total images extracted via PyMuPDF: {len(images)}")

        except Exception as e:
            self.logger.error(f"Error in PyMuPDF image extraction: {e}")
            raise

        return images

    def extract_images_from_pdf(self, pdf_path: str) -> List[Dict[str, Any]]:
        """Extract images from PDF using unstructured with coordinates

        Args:
            pdf_path (str): The path to the PDF file to extract images from.

        Returns:
            List[Dict[str, Any]]: A list of dictionaries containing image data.
        """
        images = []
        self.logger.info(f"Starting image extraction from PDF: {pdf_path}")

        if not self.use_unstructured:
            path = Path(pdf_path)
            image_dir = Path(f'{path.stem}_images')
            self.logger.info(f"Using Standard mode. Looking for images in: {image_dir}")

            if not image_dir.exists():
                self.logger.warning(f"Image directory does not exist: {image_dir}")
                return images
            image_paths = chain(
                image_dir.glob('*.jpg'),
                image_dir.glob('*.jpeg'),
                image_dir.glob('*.png'),
                image_dir.glob('*.webp'),
                image_dir.glob('*.bmp'),
                image_dir.glob('*.tiff'),
            )
            for image_path in image_paths:
                self.logger.info(f"Processing image: {image_path}")
                try:
                    with open(image_path, 'rb') as f:
                        image_data = f.read()
                        images.append({
                            'base64': base64.b64encode(image_data).decode('utf-8'),
                            'coordinates': None,
                            'page_number': None,
                        })
                        self.logger.info(f"Successfully processed image: {image_path}")
                except Exception as e:
                    self.logger.error(f"Error processing image {image_path}: {e}")
            self.logger.info(f"Found {len(images)} images in standard mode")
            return images
        else:
            path = 'figures'
            image_dir = Path(path)
            image_paths = chain(
                image_dir.glob('*.jpg'),
                image_dir.glob('*.jpeg'),
                image_dir.glob('*.png'),
                image_dir.glob('*.webp'),
                image_dir.glob('*.bmp'),
                image_dir.glob('*.tiff'),
            )
            self.logger.info(f"Using Unstructured mode. Looking for images in: {image_dir}")

            if not image_dir.exists():
                self.logger.warning(f"Figures directory does not exist: {image_dir}")
                return images

            # Store images in a list of dictionaries only if images exist and their size (either height or width) is greater than 100px
            for image_path in image_paths:
                self.logger.info(f"Processing image: {image_path}")
                try:
                    with open(image_path, 'rb') as f:
                        image_data = f.read()
                        # Open the image using PIL
                        img = Image.open(io.BytesIO(image_data))
                        width, height = img.size
                        self.logger.info(f"Image dimensions - Width: {width}, Height: {height}")

                        # Only include images larger than 100x100 pixels
                        if width > 100 or height > 100:
                            images.append({
                                'base64': base64.b64encode(image_data).decode('utf-8'),
                                'coordinates': None,
                                'page_number': None,
                            })
                            self.logger.info(f"Successfully processed image: {image_path}")
                        else:
                            self.logger.warning(f"Skipping image {image_path} due to invalid dimensions")
                except Exception as e:
                    self.logger.error(f"Error processing image {image_path}: {e}")

            self.logger.info(f"Found {len(images)} valid images in standard mode")
            return images
        
    def merge_split_tables_across_pages(self, page_chunks: List[str]) -> List[str]:
        """
        Merge tables that are split across page boundaries.
        
        Detects incomplete tables at page boundaries (table starts at end of page N
        and continues at start of page N+1) and merges them into single chunks.
        
        Args:
            page_chunks: List of page text chunks
            
        Returns:
            List of chunks with tables merged across pages where detected
        """
        if not page_chunks or len(page_chunks) < 2:
            return page_chunks
        
        def has_table_end(text: str) -> bool:
            """Check if text ends with an incomplete table (has table rows but no clear ending)"""
            lines = text.strip().split('\n')
            if len(lines) < 3:
                return False
            
            # Count table-like lines at the end (lines starting with |)
            table_line_count = 0
            for line in reversed(lines[-10:]):  # Check last 10 lines
                if line.strip().startswith('|') and '|' in line[1:]:
                    table_line_count += 1
                elif line.strip() and table_line_count > 0:
                    break  # Hit non-table content
            
            # If we have 2+ table lines at the end, it might be incomplete
            return table_line_count >= 2
        
        def has_table_start(text: str) -> bool:
            """Check if text starts with a table continuation (has table rows at start)"""
            lines = text.strip().split('\n')
            if len(lines) < 2:
                return False
            
            # Check if first few lines are table rows
            table_line_count = 0
            for line in lines[:10]:  # Check first 10 lines
                if line.strip().startswith('|') and '|' in line[1:]:
                    table_line_count += 1
                elif line.strip():
                    break  # Hit non-table content
            
            return table_line_count >= 2
        
        merged_chunks = []
        i = 0
        
        while i < len(page_chunks):
            current_chunk = page_chunks[i]
            
            # Check if current page ends with incomplete table and next page starts with table
            if i < len(page_chunks) - 1:
                next_chunk = page_chunks[i + 1]
                
                if has_table_end(current_chunk) and has_table_start(next_chunk):
                    # Merge the two pages
                    merged = current_chunk.rstrip() + '\n\n' + next_chunk.lstrip()
                    merged_chunks.append(merged)
                    self.logger.info(f"Merged table spanning pages {i+1} and {i+2}")
                    i += 2  # Skip next page since we merged it
                    continue
            
            merged_chunks.append(current_chunk)
            i += 1
        
        return merged_chunks
    
    def chunk_excel_by_rows(self, excel_markdown: str, total_data_rows: int, rows_per_chunk: int = 25) -> List[str]:
        """
        Chunk Excel markdown content by rows using PyMuPDF metadata row count.
        
        Args:
            excel_markdown (str): The markdown content from Excel file
            total_data_rows (int): Total number of data rows from PyMuPDF metadata
            rows_per_chunk (int): Number of rows per chunk (default: 25)
            
        Returns:
            List[str]: List of markdown chunks, each containing up to rows_per_chunk rows
        """
        lines = excel_markdown.strip().split('\n')
        chunks = []
        
        # Find the header line and separator line
        header_line = None
        separator_line = None
        data_start_idx = 0
        
        for i, line in enumerate(lines):
            if line.startswith('|') and header_line is None:
                header_line = line
                continue
            elif line.startswith('|---') and header_line is not None:
                separator_line = line
                data_start_idx = i + 1
                break
        
        if header_line is None or separator_line is None:
            # If no table structure found, return original content as single chunk
            self.logger.warning("No table structure found in Excel content, returning as single chunk")
            return [excel_markdown]
        
        # Extract data rows (skip header and separator) but limit to PyMuPDF count
        data_rows = []
        row_count = 0
        for i in range(data_start_idx, len(lines)):
            if row_count >= total_data_rows:  # Stop when we reach PyMuPDF's row count
                break
            line = lines[i].strip()
            if line.startswith('|') and not line.startswith('|---'):
                data_rows.append(line)
                row_count += 1
        
        self.logger.info(f"Using PyMuPDF row count: {total_data_rows} data rows for chunking")
        self.logger.info(f"Extracted {len(data_rows)} rows from markdown (limited by PyMuPDF count)")
        
        # Create chunks with header + separator + data rows
        for i in range(0, len(data_rows), rows_per_chunk):
            chunk_rows = data_rows[i:i + rows_per_chunk]
            
            # Build chunk with header, separator, and data rows
            chunk_content = [header_line, separator_line] + chunk_rows
            chunk_text = '\n'.join(chunk_content)
            
            chunks.append(chunk_text)
            self.logger.debug(f"Created chunk {len(chunks)} with {len(chunk_rows)} rows")
        
        self.logger.info(f"Created {len(chunks)} chunks from Excel file ({rows_per_chunk} rows per chunk)")
        return chunks


    def generate_content_enhancements(self, chunks: List[str]) -> List[Dict[str, str]]:
        """Generate title, summary, and Q&A for chunks using LLM **in parallel**.

        Chunks are grouped in pairs (2 chunks per LLM call) regardless of whether they are 
        page-wise chunks from PyMuPDF Pro or character-based chunks from text splitter.
        Each pair is sent to the LLM concurrently using parallel processing.
        The number of parallel workers is controlled by `self.parallel_workers`.
        """

        def _enhance_pair(idx: int, pair: List[str]) -> List[Dict[str, str]]:
            """Internal helper to enhance a single pair of chunks."""
            # Pad if only one chunk
            if len(pair) == 1:
                pair.append("")

            prompt = f"""
            Analyze the following 2 document sections and for **each** section provide separately:
            1. A concise title (max 10 words)
            2. A summary covering all important terms and concepts
            3. 2-3 relevant Q&A pairs
            If a section has no significant content, output 'None' for all fields.

            Section 1:
            {pair[0]}

            Section 2:
            {pair[1]}

            Format your response exactly as:

            Chunk 1:
                TITLE: [title] or 'None'
                SUMMARY: [summary] or 'None'
                Q&A:
                Q1: [question]
                A1: [answer]
                Q2: [question]
                A2: [answer]

            Chunk 2:
                TITLE: [title] or 'None'
                SUMMARY: [summary] or 'None'
                Q&A:
                Q1: [question]
                A1: [answer]
                Q2: [question]
                A2: [answer]
            """

            try:
                response = self.gemini_client.models.generate_content(
                    model=self.llm_model,
                    contents=[
                        types.Content(
                            role="user",
                            parts=[types.Part.from_text(text=prompt)]
                        ),
                    ],
                    config=types.GenerateContentConfig(
                        system_instruction="You are an expert document summarizer.",
                        temperature=0.1,
                        max_output_tokens=1024
                    )
                )

                content = response.text
                # print("DEBUG: LLM enhancement response:\n", content)
                self.logger.info(f"Generated enhancements for chunks {idx + 1} & {idx + 2}")
                return self.parse_enhancement_response(content)
            except Exception as e:
                self.logger.error(f"Error generating enhancements for chunks {idx + 1} & {idx + 2}: {e}")
                # Fallback enhancement for each chunk in pair
                fallback: List[Dict[str, str]] = []
                for offset, chunk in enumerate(pair):
                    fallback.append({
                        'title': f"Document Chunk {idx + offset + 1}",
                        'summary': (chunk[:200] + "...") if chunk else "None",
                        'qna': "Q: What is this about? A: This is a document chunk." if chunk else "None"
                    })
                return fallback

        enhancements: List[Dict[str, str]] = []
        
        for i in range(0, len(chunks), 2):
            pair = chunks[i:i + 2]
            enhancements.extend(_enhance_pair(i, pair))

        return enhancements
    
    def parse_enhancement_response(self, response: str) -> List[Dict[str, str]]:
        """Parse the LLM response into two enhancement dicts.
        """
        enhancements: List[Dict[str, str]] = []
        # split off each "Chunk N:" block
        parts = re.split(r'Chunk\s*\d+:', response)
        # first split is before the first header, so skip it
        for block in parts[1:]:
            title = summary = ""
            qna_lines: List[str] = []
            in_qna = False

            for line in block.strip().splitlines():
                line = line.strip()
                if line.startswith("TITLE:"):
                    title = line.replace("TITLE:", "").strip().strip("'")
                elif line.startswith("SUMMARY:"):
                    summary = line.replace("SUMMARY:", "").strip().strip("'")
                elif line.startswith("Q&A:"):
                    in_qna = True
                elif in_qna and (line.startswith("Q") or line.startswith("A")):
                    qna_lines.append(line)

            qna = " | ".join(qna_lines) if qna_lines else "None"
            enhancements.append({
                'title': title or "None",
                'summary': summary or "None",
                'qna': qna
            })

        return enhancements
    
    def generate_image_descriptions(self, images: List[Dict[str, Any]]) -> List[str]:
        """Generate descriptions for images using vision model
        
        Args:
            images (List[Dict[str, Any]]): A list of dictionaries containing image data with id, type, content, base64, source.
            
        Returns:
            List[str]: A list of descriptions for the images.
        """
        descriptions = []
        
        for i, image_data in enumerate(images):
            try:
                prompt = """
                You are an expert Image analyst. Describe the following image or figure in detail.
                """
                response = self.gemini_client.models.generate_content(
                    model=self.config.vision_model,
                    contents=[
                        types.Content(
                            role="user",
                            parts=[
                                types.Part.from_bytes(data=base64.b64decode(image_data['base64']), mime_type="image/jpeg"),
                                types.Part.from_text(text=prompt),
                            ],
                        ),
                    ],
                    config=types.GenerateContentConfig(
                        temperature=0.3,
                        max_output_tokens=500
                    )
                )
                
                description = response.text
                descriptions.append(description)
                
                self.logger.info(f"Generated description for image {i+1}")
                
            except Exception as e:
                self.logger.error(f"Error generating description for image {i+1}: {e}")
                descriptions.append(f"Image {i+1} - Description unavailable")
        
        return descriptions
    
    def create_langchain_documents(self, chunks: List[str], enhancements: List[Dict[str, str]], 
                                 images: List[Dict[str, Any]], descriptions: List[str], 
                                 source_file: str, skip_enhancement: bool = False) -> Tuple[List[Document], List[Document]]:
        """Create LangChain documents with metadata
        
        Args:
            chunks (List[str]): A list of text chunks to create documents from.
            enhancements (List[Dict[str, str]]): A list of dictionaries containing enhanced content.
            images (List[Dict[str, Any]]): A list of dictionaries containing image data.
            descriptions (List[str]): A list of descriptions for the images.
            source_file (str): The path to the source file.

        Returns:
            Tuple[List[Document], List[Document]]: A tuple containing two lists of text and image documents.
        """
        text_documents = []
        image_documents = []
        
        # Create text documents
        for i, chunk in enumerate(chunks):
            doc_id = str(uuid.uuid4())
            
            if skip_enhancement or not enhancements:
                # For large documents or when enhancements are skipped, use raw chunks
                text_doc = Document(
                    page_content=chunk,  # Store raw page content directly
                    metadata={
                        'id': doc_id,
                        'source': source_file,
                        'title': f"Page {i + 1}",  # Simple page numbering
                        'type': 'text',
                        'enhanced': False  # Flag to indicate no enhancement
                    }
                )
            else:
                # For small documents, store enhanced content in Qdrant
                enhancement = enhancements[i] if i < len(enhancements) else {'title': f"Page {i + 1}", 'summary': chunk[:200], 'qna': ''}
                text_doc = Document(
                    page_content=f"{enhancement['title']}\n\n{enhancement['summary']}\n\n{enhancement['qna']}",
                    metadata={
                        'id': doc_id,
                        'source': source_file,
                        'title': enhancement['title'],
                        'type': 'text',
                        'enhanced': True  # Flag to indicate enhancement was applied
                    }
                )
            
            text_documents.append(text_doc)
            
            # Always store raw chunk data in PostgreSQL (same for both cases)
            self.raw_data.append({
                'id': doc_id,
                'type': 'text',
                'content': chunk,  # Always raw chunk content
                'base64': None,
                'source': source_file
            })
        
        # Create image documents
        # COMMENTED OUT: Skipping image document creation to avoid storing images in VecDB
        # for image_data, description in zip(images, descriptions):
        #     doc_id = str(uuid.uuid4())
        #     
        #     image_doc = Document(
        #         page_content=description,
        #         metadata={
        #             'id': doc_id,
        #             'source': source_file,
        #             'coordinates': image_data.get('coordinates'),
        #             'page_number': image_data.get('page_number'),
        #             'type': 'image'
        #         }
        #     )
        #     image_documents.append(image_doc)
        #     
        #     # Store raw data
        #     self.raw_data.append({
        #         'id': doc_id,
        #         'type': 'image',
        #         'content': description,
        #         'base64': image_data['base64'],
        #         'source': source_file
        #     })
        
        return text_documents, image_documents

    def create_image_documents(
        self,
        images: List[Dict[str, Any]],
        image_embeddings: List[List[float]],
        captions: List[str],
        source_file: str
    ) -> List[Dict[str, Any]]:
        """Create documents for image storage in Qdrant

        Args:
            images: List of image metadata from PyMuPDF extraction
            image_embeddings: List of SigLIP-2 embeddings (1152-dim)
            captions: List of SigLIP-2 generated captions
            source_file: Source file path

        Returns:
            List of point dictionaries for Qdrant
        """
        image_points = []

        for img_data, embedding, caption in zip(images, image_embeddings, captions):
            doc_id = str(uuid.uuid4())

            # Create point for Qdrant with named vectors
            point = {
                'id': doc_id,
                'vector': {
                    'image': embedding,  # SigLIP-2 embedding in 'image' vector
                },
                'payload': {
                    'id': doc_id,
                    'type': 'image',
                    'source': source_file,
                    'page_number': img_data.get('page_number'),
                    'image_index': img_data.get('image_index'),
                    'width': img_data.get('width'),
                    'height': img_data.get('height'),
                    'format': img_data.get('format'),
                    'colorspace': img_data.get('colorspace'),
                    'bbox': img_data.get('bbox'),
                    'xref': img_data.get('xref'),
                    'caption': caption,  # SigLIP-2 generated caption
                    'content': caption  # For text search compatibility
                }
            }

            image_points.append(point)

            # Combine caption with OCR text for content
            extracted_text = img_data.get('extracted_text', '')
            ocr_confidence = img_data.get('ocr_confidence', 0.0)

            # Build content with both caption and OCR text
            image_content = caption  # Start with SigLIP-2 caption

            if extracted_text and ocr_confidence > 30:
                # Add OCR text if confidence is good
                image_content += f"\n\nOCR Text (confidence: {ocr_confidence:.1f}%):\n{extracted_text}"

            # Add image metadata
            image_content += f"\n\nImage Info: {img_data.get('width', 0)}x{img_data.get('height', 0)} {img_data.get('format', 'unknown')}"

            # Store raw image data in PostgreSQL
            self.raw_data.append({
                'id': doc_id,
                'type': 'image',
                'content': image_content,
                'base64': img_data['base64'],
                'source': source_file
            })

        self.logger.info(f"Created {len(image_points)} image documents for Qdrant")
        return image_points

    def add_image_documents_to_qdrant(
        self,
        image_points: List[Dict[str, Any]],
        collection_name: str
    ):
        """Add image documents to Qdrant collection

        Args:
            image_points: List of image point dictionaries
            collection_name: Qdrant collection name
        """
        try:
            from qdrant_client.models import PointStruct

            points = [
                PointStruct(
                    id=point['id'],
                    vector=point['vector'],  # Pass named vector dict directly
                    payload=point['payload']
                )
                for point in image_points
            ]

            self.qdrant_client.upsert(
                collection_name=collection_name,
                points=points
            )

            self.logger.info(f"Added {len(points)} image documents to Qdrant collection: {collection_name}")

        except Exception as e:
            self.logger.error(f"Error adding images to Qdrant: {e}")
            raise

    def setup_qdrant_collection(self, collection_name: str):
        """
        This method creates a Qdrant collection with hybrid embeddings.
        It checks if the collection already exists and creates it if it doesn't.
        Args:
            collection_name (str): Name of the collection to create
        """
        try:
            # Check if collection exists
            collections = self.qdrant_client.get_collections()
            collection_exists = any(col.name == collection_name
                                  for col in collections.collections)

            # If collection exists, check if it has the image vector
            if collection_exists:
                try:
                    collection_info = self.qdrant_client.get_collection(collection_name)
                    has_image_vector = 'image' in collection_info.config.params.vectors

                    if not has_image_vector:
                        self.logger.warning(f"Collection {collection_name} exists but doesn't have 'image' vector. Recreating collection...")
                        # Delete old collection
                        self.qdrant_client.delete_collection(collection_name)
                        self.logger.info(f"Deleted old collection: {collection_name}")
                        # Set flag to create new collection
                        collection_exists = False
                    else:
                        self.logger.info(f"Collection {collection_name} already exists with image vector support")
                except Exception as check_error:
                    self.logger.error(f"Error checking collection schema: {check_error}")
                    self.logger.info(f"Collection {collection_name} already exists (schema check failed)")

            if not collection_exists:
                if self.prefer_grpc:
                    self.qdrant_client.create_collection(
                        collection_name=collection_name,
                        vectors_config={
                        "dense": VectorParams(
                            size=768,  # all-mpnet-base-v2 dimension
                            distance=Distance.DOT
                        ),
                        "image": VectorParams(
                            size=1152,  # SigLIP-2 dimension
                            distance=Distance.COSINE
                        )
                        },
                        sparse_vectors_config={
                            "sparse": SparseVectorParams()
                        },

                        hnsw_config={
                            "m":32,
                            "ef_construct":250,
                            "full_scan_threshold":1000,
                        }
                    )
                else:
                    self.qdrant_client.create_collection(
                        collection_name=collection_name,
                        vectors_config={
                        "dense": VectorParams(
                            size=768,  # all-mpnet-base-v2 dimension
                            distance=Distance.DOT
                        ),
                        "image": VectorParams(
                            size=1152,  # SigLIP-2 dimension
                            distance=Distance.COSINE
                        )
                        },
                        sparse_vectors_config={
                            "sparse": SparseVectorParams()
                        },

                        hnsw_config=HnswConfig(
                            m=32,
                            ef_construct=250,
                            full_scan_threshold=1000
                        )
                    )
                self.logger.info(f"Created collection: {collection_name}")
            else:
                self.logger.info(f"Collection {collection_name} already exists")

        except Exception as e:
            self.logger.error(f"Error setting up Qdrant collection: {e}")
            raise

    def add_to_vector_store(self, documents: List[Document], collection_name: str):
        """
        This method adds documents to the Qdrant vector store with hybrid embeddings.
        It uses the dense and sparse embeddings to create a hybrid vector store.

        Args:
            documents (List[Document]): Documents to add to vector store
            collection_name (str): Name of the collection to add documents to
        """
        try:
            vector_store = QdrantVectorStore(
                client=self.qdrant_client,
                collection_name=collection_name,
                embedding=self.dense_embeddings,
                sparse_embedding=self.sparse_embeddings,
                vector_name="dense",
                sparse_vector_name="sparse",
                retrieval_mode=RetrievalMode.HYBRID,
                distance=Distance.DOT,
            )

            vector_store.add_documents(documents)
            self.logger.info(f"Added {len(documents)} documents to vector store")

        except Exception as e:
            self.logger.error(f"Error adding documents to vector store: {e}")
            raise
    
    def clean_text_for_postgres(self, text: str) -> str:
        """Clean text content to be PostgreSQL UTF-8 compatible"""
        if not text:
            return text
        
        # Remove null bytes and other problematic characters
        cleaned = text.replace('\x00', '')  # Remove null bytes
        cleaned = cleaned.replace('\x01', '')  # Remove start of heading
        cleaned = cleaned.replace('\x02', '')  # Remove start of text
        cleaned = cleaned.replace('\x03', '')  # Remove end of text
        cleaned = cleaned.replace('\x04', '')  # Remove end of transmission
        cleaned = cleaned.replace('\x05', '')  # Remove enquiry
        cleaned = cleaned.replace('\x06', '')  # Remove acknowledge
        cleaned = cleaned.replace('\x07', '')  # Remove bell
        cleaned = cleaned.replace('\x08', '')  # Remove backspace
        # Keep \x09 (tab), \x0A (newline), \x0D (carriage return)
        cleaned = cleaned.replace('\x0B', '')  # Remove vertical tab
        cleaned = cleaned.replace('\x0C', '')  # Remove form feed
        cleaned = cleaned.replace('\x0E', '')  # Remove shift out
        cleaned = cleaned.replace('\x0F', '')  # Remove shift in
        
        # Remove other control characters (0x10-0x1F except tab, newline, carriage return)
        for i in range(16, 32):
            if i not in [9, 10, 13]:  # Keep tab, newline, carriage return
                cleaned = cleaned.replace(chr(i), '')
        
        # Ensure valid UTF-8 encoding
        try:
            cleaned = cleaned.encode('utf-8', errors='ignore').decode('utf-8')
        except UnicodeError:
            self.logger.warning("Unicode encoding error in text, using fallback")
            cleaned = str(text).encode('utf-8', errors='replace').decode('utf-8')
        
        return cleaned

    async def save_raw_data_to_pg(self, space_id: int):
        """Save raw data to PostgreSQL raw_documents table with space_id."""
        
        async def _insert(records):
            # Convert SQLAlchemy-style URL to standard PostgreSQL DSN for asyncpg
            postgres_dsn = self.database_url.replace("postgresql+asyncpg://", "postgresql://")
            self.logger.info(f"🔗 Connecting to database for raw data insertion: {postgres_dsn}")
            conn = await asyncpg.connect(postgres_dsn)
            try:
                await conn.executemany(
                    """
                    INSERT INTO raw_documents (id, space_id, type, content, base64, source)
                    VALUES ($1,$2,$3,$4,$5,$6)
                    ON CONFLICT (id) DO UPDATE
                      SET space_id = EXCLUDED.space_id,
                          type = EXCLUDED.type,
                          content = EXCLUDED.content,
                          base64 = EXCLUDED.base64,
                          source = EXCLUDED.source;
                    """,
                    records
                )
            finally:
                await conn.close()

        try:
            if self.raw_data:
                self.logger.info(f"Preparing raw_data insert. count={len(self.raw_data)}")
                self.logger.info(f"First 5 IDs: {[item.get('id') for item in self.raw_data[:5]]}")
                
                # Clean text content before database insertion
                cleaned_records = []
                for item in self.raw_data:
                    content = item.get('content')
                    if content and isinstance(content, str):
                        content = self.clean_text_for_postgres(content)
                    
                    cleaned_records.append((
                        item['id'],
                        space_id,
                        item['type'],
                        content,
                        item.get('base64'),
                        item.get('source'),
                    ))
                
                await _insert(cleaned_records)
                self.logger.info(f"Inserted {len(cleaned_records)} raw data records into PostgreSQL")
                
                # Clear after successful insertion
                self.raw_data = []
            else:
                self.logger.info("No raw data to insert")
                
        except Exception as e:
            self.logger.error(f"Error saving raw data to PostgreSQL: {e}")
            raise
    
    def _cleanup_image_folders(self, file_path: str):
        """Remove any existing image folders to prevent local storage accumulation"""
        try:
            file_path = Path(file_path)
            
            # Cleanup standard mode image folder
            image_dir_standard = Path(f'{file_path.stem}_images')
            if image_dir_standard.exists():
                shutil.rmtree(image_dir_standard)
                self.logger.info(f"Cleaned up image folder: {image_dir_standard}")
            
            # Cleanup unstructured mode image folder (figures)
            figures_dir = Path('figures')
            if figures_dir.exists():
                shutil.rmtree(figures_dir)
                self.logger.info(f"Cleaned up figures folder: {figures_dir}")
                
        except Exception as e:
            self.logger.warning(f"Could not cleanup image folders: {e}")
    
    async def process_file(self, stream_obj, collection_name: str, space_id: int):
        """
        stream_obj: MinIO HTTPResponse (or any file-like object)
        """

        original_filename = getattr(stream_obj, "filename", None) \
                        or getattr(stream_obj, "name", None) \
                        or getattr(stream_obj, "_filename", None) \
                        or "streamed_file"

        # Diagnostic logging for stream
        try:
            size_hint = None
            if hasattr(stream_obj, 'headers') and stream_obj.headers:
                size_hint = stream_obj.headers.get('Content-Length') or stream_obj.headers.get('content-length')
            self.logger.info(
                f"Stream diagnostics: filename='{original_filename}', type={type(stream_obj)}, "
                f"has_read={hasattr(stream_obj, 'read')}, has_seek={hasattr(stream_obj, 'seek')}, size_hint={size_hint}"
            )
        except Exception:
            pass

        self.logger.info(f"Processing stream: {original_filename} (type={type(stream_obj)})")

        # ------------------------------------------------------------------
        # Step 1 – determine type by filename extension and count pages
        # ------------------------------------------------------------------
        page_count = 0
        supported_extensions = {'.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.pdf'}

        # Handle all document types with unified approach
        if any(original_filename.lower().endswith(ext) for ext in supported_extensions):
            self.logger.info(f"Processing document: {original_filename}")

            try:
                # First, get the actual page count by opening the document directly
                if hasattr(stream_obj, 'seek'):
                    stream_obj.seek(0)
                data = stream_obj.read()
                if hasattr(stream_obj, 'seek'):
                    stream_obj.seek(0)  # Reset stream position

                # Open document with PyMuPDF to get actual page count
                doc = pymupdf.open(stream=data)
                actual_page_count = len(doc)
                doc.close()  # Close immediately after getting page count

                md_text, doc_metadata, original_md = self.document_to_markdown(stream_obj, page_chunks=True, doc_type="document")

                # Use actual page count from document instead of estimating from chunks
                page_count = actual_page_count

                # Log the difference for debugging
                estimated_page_count = len(md_text) if isinstance(md_text, list) else max(1, len(md_text) // 2000) if isinstance(md_text, str) else 1
                self.logger.info(f"Document has {actual_page_count} actual pages (previously estimated as {estimated_page_count} from chunks)")
                
                # Special handling for Excel files - use PyMuPDF table metadata for accurate row count
                if any(original_filename.lower().endswith(ext) for ext in ['.xls', '.xlsx']):
                    data_row_count = 0
                    if doc_metadata and 'tables' in doc_metadata and doc_metadata['tables']:
                        # Use PyMuPDF table metadata to get accurate row count
                        table_info = doc_metadata['tables'][0]  # Get first table
                        data_row_count = table_info.get('rows', 0)  
                        # Subtract 1 for header row if present
                        if data_row_count > 1:
                            data_row_count -= 1
                        
                        # Calculate expected chunks (25 rows per chunk)
                        expected_chunks = max(1, (data_row_count + 24) // 25)  # Round up division
                        self.logger.info(f"Excel file has {data_row_count} data rows (from PyMuPDF metadata), expecting {expected_chunks} chunks")
                        # Use data row count for enhancement decision (this affects whether to skip enhancement)
                        page_count = data_row_count
                    else:
                        self.logger.warning("No table metadata found for Excel file, using default page count")
                
                self.logger.info(f"Document estimated to have {page_count} pages/sections")

            except Exception as doc_error:
                self.logger.error(f"Failed to process document {original_filename}: {doc_error}")
                raise doc_error
            
            # Extract images using PyMuPDF
            try:
                images = self.extract_images_with_pymupdf(stream_obj, file_type="pdf")
                self.logger.info(f"Extracted {len(images)} images from document")
            except Exception as img_error:
                self.logger.error(f"Failed to extract images: {img_error}")
                images = []
        elif original_filename.lower().endswith(".txt"):
            if hasattr(stream_obj, 'read'):
                md_text = stream_obj.read().decode("utf-8", errors="ignore")
            else:
                with open(stream_obj, 'r', encoding='utf-8', errors='ignore') as _f:
                    md_text = _f.read()
            images = []
            page_count = 1  # Text files are considered as 1 page
        else:
            # Surface maximum diagnostics then raise to mark failure upstream
            try:
                hdrs = None
                if hasattr(stream_obj, 'headers'):
                    hdrs = dict(stream_obj.headers) if stream_obj.headers else None
                self.logger.error(
                    f"Unsupported stream type for {original_filename}; diagnostics: "
                    f"type={type(stream_obj)}, has_read={hasattr(stream_obj,'read')}, headers={hdrs}"
                )
            finally:
                raise ValueError(f"Unsupported stream type for {original_filename}")
        try:
            # Step 1: Handle different file types
            # Detect DOC/DOCX files even if they have been given an extra extension like ".txt"
            # if any(sfx.lower() in ['.doc', '.docx'] for sfx in file_path.suffixes):
            #     if not SPIRE_AVAILABLE:
            #         self.logger.error("Cannot process .doc/.docx files without Spire.Doc")
            #         return
            #     start_time = time.time()
            #     # Convert DOC/DOCX to PDF
            #     pdf_path = self.convert_doc_to_pdf(str(file_path))
            #     # Also keep a copy of the generated PDF for inspection
            #     try:
            #         shutil.copy(pdf_path, Path(file_path).parent / Path(pdf_path).name)
            #     except Exception as _e:
            #         self.logger.warning(f"Could not copy pdf to debug folder: {_e}")

            #     self.timing_stats['file_conversion'] += time.time() - start_time

            #     # From this point onwards treat the converted PDF as the working file
            #     file_path = Path(pdf_path)

            #     start_time = time.time()
            #     # Always extract text via pdf_to_markdown; skip Unstructured for DOCX-derived PDFs
            #     if self.use_custom_splitter:
            #         md_text = self.pdf_to_markdown(pdf_path)
            #     else:
            #         md_text = self.pdf_to_markdown(pdf_path, page_chunks=True)
            #     self.timing_stats['text_and_image_extraction'] += time.time() - start_time
                
                
            # Step 2: Apply different chunking strategies based on file type
            start_time = time.time()
            
            # Check if this is an Excel file for special row-based chunking
            is_excel_file = any(original_filename.lower().endswith(ext) for ext in ['.xls', '.xlsx'])
            
            if is_excel_file:
                # For Excel files, use row-based chunking instead of page-based
                if isinstance(md_text, list):
                    # Join all pages into one text for Excel processing
                    full_excel_text = '\n\n'.join(md_text)
                else:
                    full_excel_text = md_text
                
                # Check page count for different Excel processing strategies
                excel_page_count = 1  # Default
                if doc_metadata and 'page_count' in doc_metadata:
                    excel_page_count = doc_metadata.get('page_count', 1)
                
                if excel_page_count > 1:
                    # Multi-page Excel: Process each page separately, then apply 25-row chunking within each page
                    self.logger.info(f"Multi-page Excel file detected ({excel_page_count} pages), processing page-wise with row chunking")
                    excel_chunks = []
                    
                    if isinstance(md_text, list) and len(md_text) >= excel_page_count:
                        for page_idx, page_text in enumerate(md_text[:excel_page_count]):
                            self.logger.info(f"Processing Excel page {page_idx + 1}/{excel_page_count}")
                            
                            # Get row count from this page's metadata
                            page_data_rows = 0
                            try:
                                # Check if the current page has its own metadata structure
                                if isinstance(md_text, list) and page_idx < len(md_text):
                                    # For multi-page Excel, PyMuPDF4LLM should return each page with its metadata
                                    # We need to access the metadata from the original PyMuPDF4LLM structure
                                    # Since we're processing md_text[page_idx], we need the metadata for this specific page
                                    
                                    # Extract metadata from the original md structure for this page
                                    current_page_data = original_md[page_idx] if isinstance(original_md, list) and page_idx < len(original_md) else None
                                    
                                    if current_page_data and isinstance(current_page_data, dict):
                                        # Check if this page has table metadata
                                        if 'tables' in current_page_data and current_page_data['tables']:
                                            page_table_info = current_page_data['tables'][0]
                                            total_rows = page_table_info.get('rows', 0)
                                            page_data_rows = max(0, total_rows - 1)  # Subtract header
                                            self.logger.info(f"Page {page_idx + 1}: Found page-specific metadata: {total_rows} total rows → {page_data_rows} data rows")
                                        elif 'metadata' in current_page_data and 'tables' in current_page_data['metadata']:
                                            # Alternative structure
                                            page_table_info = current_page_data['metadata']['tables'][0]
                                            total_rows = page_table_info.get('rows', 0)
                                            page_data_rows = max(0, total_rows - 1)
                                            self.logger.info(f"Page {page_idx + 1}: Found nested metadata: {total_rows} total rows → {page_data_rows} data rows")
                                    
                                    # If we couldn't get page-specific metadata, use the aggregated metadata as fallback
                                    if page_data_rows == 0 and doc_metadata and 'tables' in doc_metadata and doc_metadata['tables']:
                                        table_info = doc_metadata['tables'][0]
                                        total_rows = table_info.get('rows', 0)
                                        page_data_rows = max(0, total_rows - 1)
                                        self.logger.info(f"Page {page_idx + 1}: Using fallback metadata: {total_rows} total rows → {page_data_rows} data rows")
                                        
                            except Exception as e:
                                self.logger.warning(f"Could not extract page {page_idx + 1} metadata: {e}, page_data_rows will be 0")
                            
                            if page_data_rows > 0:
                                # Apply 25-row chunking to this page
                                page_chunks = self.chunk_excel_by_rows(page_text, page_data_rows, rows_per_chunk=25)
                                excel_chunks.extend(page_chunks)
                                self.logger.info(f"Page {page_idx + 1}: {page_data_rows} data rows → {len(page_chunks)} chunks")
                            else:
                                # If no data rows detected, treat entire page as one chunk
                                excel_chunks.append(page_text)
                                self.logger.info(f"Page {page_idx + 1}: No metadata found, using entire page as chunk")
                    else:
                        # Fallback for multi-page
                        excel_chunks = [full_excel_text]
                        self.logger.warning("Could not process multi-page Excel properly, using single chunk")
                        
                else:
                    # Single-page Excel: Use original row-based chunking with PyMuPDF metadata
                    self.logger.info(f"Single-page Excel file detected, using row-based chunking")
                    
                    if doc_metadata and 'tables' in doc_metadata and doc_metadata['tables']:
                        table_info = doc_metadata['tables'][0]
                        total_rows = table_info.get('rows', 0)
                        data_rows_count = max(0, total_rows - 1) if total_rows > 0 else 0  # Subtract header
                        
                        excel_chunks = self.chunk_excel_by_rows(full_excel_text, data_rows_count, rows_per_chunk=25)
                    else:
                        self.logger.warning("No table metadata available for Excel chunking, using single chunk")
                        excel_chunks = [full_excel_text]
                chunks = []
                for chunk_text in excel_chunks:
                    # Clean text before storing to prevent PostgreSQL encoding issues
                    cleaned_chunk = self.clean_text_for_postgres(chunk_text)
                    chunks.append(cleaned_chunk)
                if excel_page_count > 1:
                    self.logger.info(f"Using page-wise + row-based chunks for Excel file: {len(chunks)} total chunks from {excel_page_count} pages")
                else:
                    self.logger.info(f"Using row-based chunks for Excel file: {len(chunks)} chunks (25 rows per chunk)")
            else:
                # For non-Excel files, use existing page-wise chunking with table merging
                if isinstance(md_text, list):
                    # First, merge tables that span across page boundaries
                    self.logger.info(f"Processing {len(md_text)} page chunks, checking for split tables...")
                    merged_pages = self.merge_split_tables_across_pages(md_text)
                    self.logger.info(f"After table merging: {len(merged_pages)} chunks (was {len(md_text)} pages)")
                    
                    # Use merged page chunks - each chunk may contain one or more pages
                    chunks = []
                    for page_text in merged_pages:
                        # Clean text before storing to prevent PostgreSQL encoding issues
                        cleaned_page = self.clean_text_for_postgres(page_text)
                        chunks.append(cleaned_page)
                    self.logger.info(f"Using table-aware chunks from PyMuPDF Pro: {len(chunks)} chunks")
                else:
                    # Fallback: if single string, treat as one chunk
                    cleaned_text = self.clean_text_for_postgres(md_text)
                    chunks = [cleaned_text]
                    self.logger.info(f"Single text chunk created")
            
            self.timing_stats['text_chunking'] += time.time() - start_time


            # Step 3: Conditionally generate content enhancements based on page count
            start_time = time.time()
            skip_enhancement = page_count > 50
            
            if skip_enhancement:
                self.logger.info(f"Skipping content enhancement for large document ({page_count} pages > 50)")
                enhancements = []  # No enhancements for large documents
            else:
                self.logger.info(f"Generating content enhancements for document ({page_count} pages <= 50)")
                enhancements = self.generate_content_enhancements(chunks)
            
            self.timing_stats['content_enhancement'] += time.time() - start_time
            
            # Step 4: Process images with SigLIP-2 service
            if images:
                try:
                    self.logger.info(f"Processing {len(images)} images with SigLIP-2 service")
                    start_time = time.time()
                    
                    # Get embeddings from service
                    images_base64 = [img['base64'] for img in images]
                    embeddings_result = self.image_embedding_client.embed_images(images_base64)
                    image_embeddings = embeddings_result['embeddings']
                    self.logger.info(f"Generated {len(image_embeddings)} image embeddings")
                    
                    # Get captions from service
                    captions_result = self.image_embedding_client.caption_images(images_base64)
                    captions = captions_result['captions']
                    self.logger.info(f"Generated {len(captions)} image captions")
                    
                    # Create image documents
                    image_points = self.create_image_documents(
                        images, image_embeddings, captions, original_filename
                    )
                    
                    # Add to Qdrant
                    if image_points:
                        self.add_image_documents_to_qdrant(image_points, collection_name)
                        self.logger.info(f"Successfully added {len(image_points)} images to Qdrant")
                    
                    self.timing_stats['image_description'] += time.time() - start_time
                    
                except Exception as img_proc_error:
                    self.logger.error(f"Error processing images with SigLIP-2: {img_proc_error}")
                    # Continue processing even if image processing fails
            
            # Step 5: Create LangChain documents
            filename_only = original_filename  # use the upload name for metadata
            # Skip image documents in LangChain (images are handled separately via SigLIP-2)
            text_docs, image_docs = self.create_langchain_documents(
                chunks, enhancements, [], [], filename_only, skip_enhancement
            )
            
            # Step 6: Add to vector store
            start_time = time.time()
            # MODIFIED: Only add text documents, skip image documents
            all_docs = text_docs  # + image_docs (commented out)
            if all_docs:
                self.add_to_vector_store(all_docs, collection_name)
            self.timing_stats['vector_store_ops'] += time.time() - start_time
            
            self.processed_chunks.extend(text_docs)
            # COMMENTED OUT: Not processing images anymore
            # self.processed_images.extend(image_docs)
                    
            # Save raw data to PostgreSQL (optional - don't fail if PostgreSQL is unavailable)
            start_time = time.time()
            try:
                self.logger.info(f"Saving raw data to PostgreSQL for space ID: {space_id}")
                await self.save_raw_data_to_pg(space_id)
                self.timing_stats['db_operations'] += time.time() - start_time
            except Exception as db_error:
                self.logger.error(f"PostgreSQL save failed: {db_error}")
                self.logger.error(f"Raw data count: {len(self.raw_data)}")
                self.logger.error(f"Space ID: {space_id}")
                self.logger.error(f"Database URL: {self.database_url}")
                # Log first few raw data entries for debugging
                if self.raw_data:
                    self.logger.error(f"Sample raw data: {self.raw_data[0]}")
                self.logger.info("Continuing with processing despite PostgreSQL failure")
                # Don't add to timing stats since operation didn't complete
                start_time = time.time()  # Reset timer
            
            # Cleanup any existing image folders to prevent storage accumulation
            self._cleanup_image_folders(original_filename)
            
            self.logger.info(f"Successfully processed {stream_obj}")
            if skip_enhancement:
                self.logger.info(f"Created {len(text_docs)} text documents with raw chunks (enhancement skipped for {page_count} pages)")
            else:
                self.logger.info(f"Created {len(text_docs)} text documents with enhanced content ({page_count} pages)")
            
            # Log image processing summary
            if images:
                self.logger.info(f"Processed {len(images)} images with SigLIP-2 embeddings and captions")
            
        except Exception as e:
            self.logger.error(f"Error processing file {stream_obj}: {e}")
            raise
    
    async def process_directory(self, directory_path: str, collection_name: str, space_id: int):
        """Process all supported files in a directory
        
        Args:
            directory_path (str): The path to the directory to process.
            collection_name (str): Name of the Qdrant collection to store vectors
            space_id (int): Space ID for the documents
        """
        directory = Path(directory_path)
        supported_extensions = {'.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.txt'}
        
        files = []
        for ext in supported_extensions:
            files.extend(directory.glob(f"*{ext}"))
        
        self.logger.info(f"Found {len(files)} supported files in {directory_path}")
        
        for file_path in files:
            await self.process_file(str(file_path), collection_name, space_id)
    
    async def run_pipeline(self, input_path: str, collection_name: str, space_id: int):
        """Run the complete pipeline
        
        Args:
            input_path (str): The path to the input file or directory to process.
            collection_name (str): Name of the Qdrant collection to store vectors
            space_id (int): Space ID for the documents
        """
        self.logger.info("Starting multimodal RAG pipeline")
        pipeline_start_time = time.time()
        
        # Store current request parameters
        self.current_collection_name = collection_name
        
        # Clear previous processing data
        self.processed_chunks = []
        self.processed_images = []
        self.raw_data = []
        
        # Setup Qdrant collection
        self.setup_qdrant_collection(collection_name)
        
        # Process input
        input_path = Path(input_path)
        if input_path.is_file():
            await self.process_file(str(input_path), collection_name, space_id)
        elif input_path.is_dir():
            await self.process_directory(str(input_path), collection_name, space_id)
        else:
            raise ValueError(f"Input path does not exist: {input_path}")

        
        # Calculate total time
        self.timing_stats['total_time'] = time.time() - pipeline_start_time
        
        # Display timing report
        timing_table = []
        for operation, duration in self.timing_stats.items():
            timing_table.append([
                operation.replace('_', ' ').title(),
                f"{duration:.2f}s",
                f"{(duration/self.timing_stats['total_time']*100):.1f}%"
            ])
        
        self.logger.info("\nPipeline Timing Report:")
        self.logger.info("\n" + tabulate(
            timing_table,
            headers=['Operation', 'Duration', '% of Total'],
            tablefmt='grid'
        ))
        
        self.logger.info("Pipeline completed successfully")
        enhanced_count = sum(1 for doc in self.processed_chunks if doc.metadata.get('enhanced', True))
        raw_count = len(self.processed_chunks) - enhanced_count
        self.logger.info(f"Total processed text documents: {len(self.processed_chunks)} ({enhanced_count} enhanced, {raw_count} raw chunks)")
        # COMMENTED OUT: Not processing images anymore
        # self.logger.info(f"Total processed documents: {len(self.processed_chunks) + len(self.processed_images)}")
