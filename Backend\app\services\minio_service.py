"""
MinIO Object Storage Service

Handles all MinIO operations including file upload, download, and management
with support for user-specific spaces
"""

import logging
import os
import mimetypes
from datetime import datetime, timed<PERSON>ta
from typing import Optional, List, Dict, Any, BinaryIO
from pathlib import Path
import uuid

from minio import Minio
from minio.error import S3Error
from minio.deleteobjects import DeleteObject
from fastapi import UploadFile, HTTPException, status

from ..config import (
    MINIO_ENDPOINT, MINIO_ACCESS_KEY, MINIO_SECRET_KEY,
    MINIO_SECURE, MINIO_BUCKET_TYPES, MINIO_PUBLIC_ENDPOINT,
    MINIO_BUCKET, ENV
)

logger = logging.getLogger(__name__)

class MinIOService:
    """
    MinIO service for handling object storage operations
    with support for user-specific spaces
    """
    
    def __init__(self):
        """Initialize MinIO client"""
        # Force HTTP connection to port 9000 for MinIO API
        endpoint = MINIO_ENDPOINT
        
        # Make sure we're using the API port (9000) not the console port (9001)
        if ":" in endpoint:
            host, port = endpoint.split(":")
            if port == "9001":  # If using console port, switch to API port
                endpoint = f"{host}:9000"
        else:
            endpoint = f"{endpoint}:9000"  # Default to API port if no port specified
            
        self.client = Minio(
            endpoint,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=False  # Force HTTP for development
        )
        self.bucket_types = MINIO_BUCKET_TYPES
    
    def _ensure_required_folders(self, bucket_name: str, username: str) -> None:
        """
        Ensure required folders exist in user space within the main bucket

        Args:
            bucket_name: Main bucket name (environment-based)
            username: Username for creating user folders
        """
        # Clean username for folder structure
        import re
        safe_username = username.lower().replace(" ", "-")
        safe_username = re.sub(r'[^a-z0-9_.-]', '', safe_username)
        if not safe_username or not safe_username[0].isalnum():
            safe_username = f"user-{username}"

        required_folders = ["knowledge_documents", "chat_attachments", "support_tickets", "backups"]

        for folder in required_folders:
            try:
                from io import BytesIO
                empty_data = BytesIO(b"")

                # Create folders under username: username/folder/.folder
                self.client.put_object(
                    bucket_name=bucket_name,
                    object_name=f"{safe_username}/{folder}/.folder",
                    data=empty_data,
                    length=0
                )
                logger.info(f"Created folder {safe_username}/{folder} in bucket {bucket_name}")
            except Exception as e:
                logger.warning(f"Could not create folder {safe_username}/{folder} for user {username}: {e}")
                
    def ensure_user_space(self, user_id: int, username: Optional[str] = None) -> str:
        """
        Ensure the main environment bucket exists and user space is set up within it

        Args:
            user_id: User ID
            username: Username for folder naming (preferred over user_id)

        Returns:
            Main bucket name (environment-based)
        """
        # Get the main environment bucket name
        main_bucket = MINIO_BUCKET

        try:
            # Check if main bucket exists
            bucket_created = False
            if not self.client.bucket_exists(main_bucket):
                # Create the main bucket
                self.client.make_bucket(main_bucket)
                bucket_created = True
                logger.info(f"Created main bucket: {main_bucket}")
            else:
                logger.debug(f"Main bucket already exists: {main_bucket}")

            # Ensure user folders exist within the main bucket
            if username:
                self._ensure_required_folders(main_bucket, username)

            return main_bucket

        except Exception as e:
            logger.error(f"Error ensuring bucket for user {user_id}: {e}")
            raise
        
    def create_user_collection(self, user_id: int, collection_name: str, username: Optional[str] = None) -> str:
        """
        Create a collection within a user's space in the main bucket (simplified for knowledge_documents and chat_attachments only)

        Args:
            user_id: User ID
            collection_name: Collection name (should be knowledge_documents or chat_attachments)
            username: Username for folder naming (preferred over user_id)

        Returns:
            Collection path prefix
        """
        # Ensure main bucket exists and user space is set up
        main_bucket = self.ensure_user_space(user_id, username)

        # Clean username for folder structure
        import re
        safe_username = username.lower().replace(" ", "-") if username else f"user-{user_id}"
        safe_username = re.sub(r'[^a-z0-9_.-]', '', safe_username)
        if not safe_username or not safe_username[0].isalnum():
            safe_username = f"user-{user_id}"

        # Normalize collection name to our three supported types
        if collection_name in ["knowledge_document", "knowledge_documents"]:
            folder_name = "knowledge_documents"
        elif collection_name in ["chat_attachment", "chat_attachments"]:
            folder_name = "chat_attachments"
        elif collection_name in ["support_ticket", "support_tickets"]:
            folder_name = "support_tickets"
        else:
            # Default to chat_attachments
            folder_name = "chat_attachments"

        collection_prefix = f"{safe_username}/{folder_name}/"

        # Create an empty marker object to represent the collection
        try:
            # Use BytesIO instead of bytes directly
            from io import BytesIO
            empty_data = BytesIO(b"")

            self.client.put_object(
                bucket_name=main_bucket,
                object_name=f"{collection_prefix}.folder",
                data=empty_data,
                length=0
            )
            logger.info(f"Created folder {collection_prefix} in bucket {main_bucket}")
        except Exception as e:
            logger.error(f"Error creating folder {collection_prefix} for user {username or user_id}: {e}")
            raise

        return collection_prefix
    
    def _generate_object_key(self, file_type: str, filename: str, user_id: Optional[int] = None, username: Optional[str] = None, collection: Optional[str] = None, space_id: Optional[int] = None) -> str:
        """Generate unique object key based on simplified folder structure within user space"""
        now = datetime.now()
        date_folder = f"{now.year}-{now.month:02d}-{now.day:02d}"
        unique_id = str(uuid.uuid4())

        # Clean filename
        safe_filename = Path(filename).stem[:50]  # Limit length
        extension = Path(filename).suffix

        # Determine base path based on collection type
        if collection and collection.startswith("shared_space_"):
            # For shared spaces: use collection name directly (already formatted as shared_space_{id})
            base_path = collection
        else:
            # For personal spaces: use username-based path (existing logic)
            import re
            safe_username = username.lower().replace(" ", "-") if username else f"user-{user_id}"
            safe_username = re.sub(r'[^a-z0-9_.-]', '', safe_username)
            if not safe_username or not safe_username[0].isalnum():
                safe_username = f"user-{user_id}"
            base_path = safe_username

        # Simplified structure: base_path/folder_name/date/filename
        if file_type in ["chat_attachment", "chat_attachments"]:
            folder_name = "chat_attachments"
        elif file_type in ["knowledge_document", "knowledge_documents"]:
            folder_name = "knowledge_documents"
        elif file_type in ["support_ticket", "support_tickets"]:
            folder_name = "support_tickets"
        elif file_type in ["image_playground", "images"]:
            folder_name = "image_playground"
        elif file_type == "staging":
            folder_name = "staging"
        else:
            # Default to chat_attachments for any other type
            folder_name = "chat_attachments"

        return f"{base_path}/{folder_name}/{date_folder}/{unique_id}_{safe_filename}{extension}"
    
    def _get_content_type(self, filename: str) -> str:
        """Get content type for file"""
        content_type, _ = mimetypes.guess_type(filename)
        return content_type or 'application/octet-stream'
    
    async def upload_file(self, file: UploadFile, bucket_type: str, user_id: Optional[int] = None, department: Optional[str] = None, collection: Optional[str] = None, username: Optional[str] = None) -> Dict[str, Any]:
        """
        Upload file to MinIO bucket
        
        Args:
            file: FastAPI UploadFile object
            bucket_type: Type of bucket (chat_attachments, knowledge_documents, etc.)
            user_id: User ID for user-specific files
            department: Department for knowledge documents
            collection: Collection name for user-specific organization
            username: Username for bucket naming (preferred over user_id)
            
        Returns:
            Dictionary with upload results
        """
        try:
            # Get the appropriate bucket name using username if available
            bucket_name = MINIO_BUCKET
            
            # Ensure bucket exists
            if not self.client.bucket_exists(bucket_name):
                self.client.make_bucket(bucket_name)
                logger.info(f"Created bucket: {bucket_name}")

            # Note: Collection folder creation is handled dynamically by _generate_object_key
            # No need to pre-create folders as MinIO handles them automatically
            
            # Generate object key with user-specific path structure
            object_key = self._generate_object_key(bucket_type, file.filename, user_id, username, collection)
            
            # Get file content - make sure to seek to beginning
            await file.seek(0)
            contents = await file.read()
            file_size = len(contents)
            content_type = self._get_content_type(file.filename)
            
            # Log details for debugging
            logger.info(f"Uploading file to MinIO: bucket={bucket_name}, key={object_key}, size={file_size}, type={content_type}")
            
            # Upload to MinIO
            from io import BytesIO
            self.client.put_object(
                bucket_name=bucket_name,
                object_name=object_key,
                data=BytesIO(contents),
                length=file_size,
                content_type=content_type
            )
            
            logger.info(f"Successfully uploaded file: {object_key} to {bucket_name}")
            
            # Generate URL
            url = self.get_file_url(bucket_name, object_key)
            
            return {
                "success": True,
                "bucket_name": bucket_name,
                "object_key": object_key,
                "file_size": file_size,
                "content_type": content_type,
                "filename": file.filename,
                "url": url,
                "user_id": user_id,
                "collection": collection
            }
            
        except Exception as e:
            logger.error(f"Error uploading file {file.filename}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"File upload failed: {str(e)}"
            )
    
    def upload_binary_data(self, data: bytes, filename: str, bucket_type: str, user_id: Optional[int] = None, department: Optional[str] = None, collection: Optional[str] = None, space_id: Optional[int] = None, username: Optional[str] = None) -> Dict[str, Any]:
        """
        Upload binary data to MinIO bucket
        
        Args:
            data: Binary data to upload
            filename: Original filename
            bucket_type: Type of bucket
            user_id: User ID for user-specific files
            department: Department for knowledge documents
            collection: Collection name for user-specific organization
            username: Username for bucket naming (preferred over user_id)
            
        Returns:
            Dictionary with upload results
        """
        try:
            # Get the appropriate bucket name using username if available
            bucket_name = MINIO_BUCKET
            
            # Ensure bucket exists
         
            if not self.client.bucket_exists(bucket_name):
                self.client.make_bucket(bucket_name)
                logger.info(f"Created bucket: {bucket_name}")
            
            # If this is a user bucket with collection, ensure collection exists
            if user_id and collection:
                self.create_user_collection(user_id, collection, username)
            
            # Generate object key with user-specific path structure
            object_key = self._generate_object_key(bucket_type, filename, user_id, username, collection, space_id)
            
            file_size = len(data)
            content_type = self._get_content_type(filename)
            
            # Upload to MinIO
            from io import BytesIO
            self.client.put_object(
                bucket_name=bucket_name,
                object_name=object_key,
                data=BytesIO(data),
                length=file_size,
                content_type=content_type
            )
            
            logger.info(f"Successfully uploaded binary data: {object_key} to {bucket_name}")
            
            # Generate URL
            url = self.get_file_url(bucket_name, object_key)
            
            return {
                "success": True,
                "bucket_name": bucket_name,
                "object_key": object_key,
                "file_size": file_size,
                "content_type": content_type,
                "filename": filename,
                "url": url,
                "user_id": user_id,
                "collection": collection
            }
            
        except Exception as e:
            logger.error(f"Error uploading binary data {filename}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Binary data upload failed: {str(e)}"
            )
    
    def _generate_url(self, bucket_name: str, object_key: str, bucket_type: str) -> str:
        """Generate appropriate URL for object"""
        # Just use the get_file_url method for consistency
        return self.get_file_url(bucket_name, object_key)
    
    def get_file_url(
        self, 
        bucket_name: str, 
        object_key: str, 
        expires_in_days: int = 7,
        response_headers: Optional[Dict[str, str]] = None
    ) -> str:
        """
        Get signed URL for file access with optional response header overrides.
        
        Args:
            bucket_name: MinIO bucket name
            object_key: Object key/path
            expires_in_days: URL expiration in days
            response_headers: Optional dict of response headers to override
                             (e.g., {'response-content-type': 'image/webp',
                                     'response-content-disposition': 'inline; filename=...'})
        
        Returns:
            Presigned URL string
        """
        try:
            # Build response parameters for S3/MinIO presigned URL
            params = {}
            if response_headers:
                for key, value in response_headers.items():
                    params[key] = value
            
            url = self.client.presigned_get_object(
                bucket_name, 
                object_key, 
                expires=timedelta(days=expires_in_days),
                response_headers=params if params else None
            )
            from urllib.parse import urlparse, urlunparse
            
            # Use the public endpoint if available, otherwise use the same endpoint as the client
            public_endpoint = MINIO_PUBLIC_ENDPOINT if MINIO_PUBLIC_ENDPOINT else self.client._endpoint_url._host
            
            # Ensure we're using the API port (9000) not the console port (9001)
            if ":" in public_endpoint:
                host, port = public_endpoint.split(":")
                if port == "9001":  # If using console port, switch to API port
                    public_endpoint = f"{host}:9000"
            else:
                # Ensure port is included for development setup
                public_endpoint = f"{public_endpoint}:9000"
                
            parsed = urlparse(url)
            parsed = parsed._replace(scheme="http", netloc=public_endpoint)
            return urlunparse(parsed)
        except S3Error as e:
            logger.error(f"Error generating URL for {object_key}: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
    
    def download_file(self, bucket_name: str, object_key: str) -> bytes:
        """Download file from MinIO"""
        try:
            response = self.client.get_object(bucket_name, object_key)
            return response.read()
        except S3Error as e:
            logger.error(f"Error downloading file {object_key}: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        finally:
            if 'response' in locals():
                response.close()
                response.release_conn()
    def get_file_stream(self, bucket_name: str, object_key: str):
        """Get file stream directly from MinIO """
        try:
            response = self.client.get_object(bucket_name, object_key)
            return response  # Return the stream object directly
        except S3Error as e:
            logger.error(f"Error creating stream for {object_key}: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
    
    def move_to_backup(self, bucket_name: str, object_key: str) -> Dict[str, Any]:
        """
        Move file to backups folder instead of deleting (soft delete)
        
        Args:
            bucket_name: Source bucket name
            object_key: Source object key (e.g., 'username/knowledge_documents/2025-10-09/file.pdf')
            
        Returns:
            Dictionary with success status and backup location
        """
        try:
            # Parse the object key to extract username and filename
            parts = object_key.split('/')
            if len(parts) < 2:
                raise ValueError(f"Invalid object key format: {object_key}")
            
            username = parts[0]
            filename = parts[-1]  # Get the actual filename
            
            # Create backup path with timestamp
            timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
            backup_key = f"{username}/backups/{timestamp}_{filename}"
            
            # Copy file to backup location
            from minio.commonconfig import CopySource
            
            self.client.copy_object(
                bucket_name=bucket_name,
                object_name=backup_key,
                source=CopySource(bucket_name, object_key)
            )
            logger.info(f"Copied file to backup: {object_key} -> {backup_key}")
            
            # Delete original file after successful backup
            self.client.remove_object(bucket_name, object_key)
            logger.info(f"Moved file to backup: {object_key} -> {backup_key}")
            
            return {
                "success": True,
                "backup_location": backup_key,
                "original_location": object_key
            }
            
        except S3Error as e:
            logger.error(f"Error moving file to backup {object_key}: {e}")
            return {
                "success": False,
                "error": str(e),
                "backup_location": None,
                "original_location": object_key
            }
        except Exception as e:
            logger.error(f"Unexpected error moving file to backup {object_key}: {e}")
            return {
                "success": False,
                "error": str(e),
                "backup_location": None,
                "original_location": object_key
            }
    
    def delete_file(self, bucket_name: str, object_key: str) -> bool:
        """
        Delete file from MinIO (hard delete)
        Note: Consider using move_to_backup() for soft delete instead
        """
        try:
            self.client.remove_object(bucket_name, object_key)
            logger.info(f"Deleted file: {object_key} from {bucket_name}")
            return True
        except S3Error as e:
            logger.error(f"Error deleting file {object_key}: {e}")
            return False
    
    def upload_to_staging(
        self,
        data: bytes,
        filename: str,
        user_id: int,
        content_type: str
    ) -> Dict[str, Any]:
        """
        Upload file to staging area for validation before final storage.
        
        Args:
            data: File content bytes
            filename: Original filename
            user_id: User ID for isolation
            content_type: MIME type
            
        Returns:
            Dict with staging bucket, key, and metadata
        """
        try:
            bucket_name = MINIO_BUCKET
            
            # Ensure bucket exists
            if not self.client.bucket_exists(bucket_name):
                self.client.make_bucket(bucket_name)
            
            # Generate staging key with user isolation
            staging_key = self._generate_object_key(
                "staging",
                filename,
                user_id=user_id
            )
            
            file_size = len(data)
            
            # Upload to staging
            from io import BytesIO
            self.client.put_object(
                bucket_name=bucket_name,
                object_name=staging_key,
                data=BytesIO(data),
                length=file_size,
                content_type=content_type
            )
            
            logger.info(f"Uploaded to staging: {staging_key} ({file_size} bytes)")
            
            return {
                "bucket_name": bucket_name,
                "staging_key": staging_key,
                "file_size": file_size,
                "content_type": content_type
            }
            
        except Exception as e:
            logger.error(f"Error uploading to staging: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to upload to staging: {str(e)}"
            )
    
    def finalize_from_staging(
        self,
        staging_key: str,
        final_filename: str,
        bucket_type: str,
        user_id: int,
        username: Optional[str] = None,
        collection: Optional[str] = None,
        space_id: Optional[int] = None,
        content_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Move file from staging to final location with server-side copy.
        
        Args:
            staging_key: Key in staging area
            final_filename: Filename for final location
            bucket_type: Type of bucket (image_playground, knowledge_documents, etc.)
            user_id: User ID
            username: Username for path generation
            collection: Collection name
            space_id: Space ID
            content_type: Content type to set on final object
            
        Returns:
            Dict with final bucket, key, and URL
        """
        try:
            bucket_name = MINIO_BUCKET
            
            # Generate final object key
            final_key = self._generate_object_key(
                bucket_type,
                final_filename,
                user_id=user_id,
                username=username,
                collection=collection,
                space_id=space_id
            )
            
            # Server-side copy from staging to final location
            from minio.commonconfig import CopySource
            # Ensure metadata replacement so destination has the desired Content-Type
            from minio.sse import SseCustomerKey  # imported for API signature stability (no-op)
            self.client.copy_object(
                bucket_name=bucket_name,
                object_name=final_key,
                source=CopySource(bucket_name, staging_key),
                metadata={"Content-Type": content_type} if content_type else None,
                metadata_directive="REPLACE" if content_type else None
            )
            
            logger.info(f"Finalized from staging: {staging_key} -> {final_key}")
            
            # Delete staging file (best effort)
            try:
                self.client.remove_object(bucket_name, staging_key)
                logger.info(f"Cleaned up staging file: {staging_key}")
            except Exception as cleanup_error:
                logger.warning(f"Failed to cleanup staging file {staging_key}: {cleanup_error}")
            
            # Generate URL for final object
            # For images, use shorter TTL and inline disposition
            if bucket_type in ["image_playground", "images"]:
                expires_days = 1  # 24 hours for images
                response_headers = {
                    'response-content-type': content_type or 'image/webp',
                    'response-content-disposition': f'inline; filename="{final_filename}"'
                }
                url = self.get_file_url(bucket_name, final_key, expires_days, response_headers)
            else:
                url = self.get_file_url(bucket_name, final_key)
            
            # Get file size
            stat = self.client.stat_object(bucket_name, final_key)
            
            return {
                "success": True,
                "bucket_name": bucket_name,
                "object_key": final_key,
                "file_size": stat.size,
                "content_type": content_type,
                "filename": final_filename,
                "url": url
            }
            
        except Exception as e:
            logger.error(f"Error finalizing from staging: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to finalize upload: {str(e)}"
            )

    def health_check(self) -> Dict[str, Any]:
        """Check MinIO service health"""
        try:
            # Try to list buckets
            buckets = self.client.list_buckets()
            return {
                "status": "healthy",
                "buckets": [bucket.name for bucket in buckets],
                "endpoint": MINIO_ENDPOINT
            }
        except Exception as e:
            logger.error(f"MinIO health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "endpoint": MINIO_ENDPOINT
            }

# Global MinIO service instance
minio_service = MinIOService() 