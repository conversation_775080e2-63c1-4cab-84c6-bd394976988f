import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from 'react-hot-toast';
import Providers from '../components/Providers';

const inter = Inter({ subsets: ["latin"] });

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
};

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://workplaceslm.ai'),
  title: "Workplace SLM",
  description: "AI-powered workplace copilot for enterprises and legal teams - knowledge management, document & image understanding, collaborative workspaces, helpdesk, and productivity.",
  keywords: "Workplace SLM, AI assistant, enterprise chatbot, workplace copilot, legal AI, compliance management, knowledge management, knowledge base search, document Q&A, PDF and Word search, Excel and PowerPoint search, image understanding, vision AI, helpdesk software, team workspaces, collaboration, group chat, follow-up suggestions, chat exports, dashboard analytics, notes management, enterprise productivity, business process automation",
  icons: {
    icon: '/logo.svg',
    shortcut: '/logo.svg',
    apple: '/logo.svg',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://workplaceslm.ai/',
    siteName: 'Workplace SLM',
    title: 'Workplace SLM - AI-Powered Workplace Copilot',
    description: 'AI-powered workplace copilot for enterprises and legal teams - knowledge management, document & image understanding, collaborative workspaces, helpdesk, and productivity.',
    images: [
      {
        url: '/logo.svg',
        width: 1200,
        height: 630,
        alt: 'Workplace SLM Logo',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Workplace SLM - AI-Powered Workplace Copilot',
    description: 'AI-powered workplace copilot for enterprises and legal teams - knowledge management, document & image understanding, collaborative workspaces, helpdesk, and productivity.',
    images: ['/logo.svg'],
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={`${inter.className}`}>
        <Providers>
          {children}
        </Providers>
        <Toaster position="top-right" />
      </body>
    </html>
  );
}
