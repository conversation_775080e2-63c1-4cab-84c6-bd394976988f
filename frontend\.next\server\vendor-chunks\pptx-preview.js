"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pptx-preview";
exports.ids = ["vendor-chunks/pptx-preview"];
exports.modules = {

/***/ "(ssr)/./node_modules/pptx-preview/dist/pptx-preview.es.js":
/*!***********************************************************!*\
  !*** ./node_modules/pptx-preview/dist/pptx-preview.es.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   init: () => (/* binding */ xt)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var jszip__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jszip */ \"(ssr)/./node_modules/jszip/lib/index.js\");\n/* harmony import */ var jszip__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jszip__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"(ssr)/./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var echarts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! echarts */ \"(ssr)/./node_modules/echarts/index.js\");\nvar l=1;function p(t){var e=\"<\".charCodeAt(0),a=\">\".charCodeAt(0),r=\"-\".charCodeAt(0),n=\"/\".charCodeAt(0),o=\"!\".charCodeAt(0),c=\"'\".charCodeAt(0),i='\"'.charCodeAt(0),s=\"?\".charCodeAt(0),h=\"\\r\\n\\t>/= \",p=0;return l=1,d(function l(){for(var d=[];t[p];){if(t.charCodeAt(p)==e){if(t.charCodeAt(p+1)===n)return p=t.indexOf(\">\",p),d;if(t.charCodeAt(p+1)===o){if(t.charCodeAt(p+2)==r){for(;t.charCodeAt(p)!==a||t.charCodeAt(p-1)!=r||t.charCodeAt(p-2)!=r||-1==p;)p=t.indexOf(\">\",p+1);-1===p&&(p=t.length)}else for(p+=2;t.charCodeAt(p)!==a;p++);p++;continue}if(t.charCodeAt(p+1)===s){p=t.indexOf(\">\",p),p++;continue}for(var u=++p;-1===h.indexOf(t[p]);p++);for(var f=t.slice(u,p),y=!1,v={};t.charCodeAt(p)!==a;p++){var w=t.charCodeAt(p);if(w>64&&w<91||w>96&&w<123){for(u=p;-1===h.indexOf(t[p]);p++);for(var b=t.slice(u,p),m=t.charCodeAt(p);m!==c&&m!==i;)p++,m=t.charCodeAt(p);var g=t[p],x=++p;p=t.indexOf(g,x);var L=t.slice(x,p);y||(v={},y=!0),v[b]=L}}var M=[];t.charCodeAt(p-1)!==n&&(p++,M=l()),d.push({children:M,tagName:f,attrs:v})}else{var A=p;-2===(p=t.indexOf(\"<\",p)-1)&&(p=t.length);var P=t.slice(A,p+1);P.length>0&&d.push(P)}p++}return d}())}function d(t){var e={};if(void 0===t)return{};if(1===t.length&&\"string\"==typeof t[0])return t[0];for(var a in t.forEach((function(t){if(e[t.tagName]||(e[t.tagName]=[]),\"object\"==typeof t){var a=d(t.children);\"object\"==typeof a&&(t.attrs&&(a.attrs=t.attrs),void 0===a.attrs?a.attrs={order:l}:a.attrs.order=l),l++,e[t.tagName].push(a)}})),e)1==e[a].length&&(e[a]=e[a][0]);return e}function u(t){return Math.abs(t)>2e4?\"emu\":\"point\"}function f(t){return t/12700}function y(t){return t/20}function v(t){return t/100}function w(t){return t/6e4}function b(t){return t/1e5}function m(t){var e=Math.ceil(t/26),a=(t%26||26)-1+65;return String.fromCharCode(a).repeat(e)}function g(t,e,a){var r={type:\"solidFill\"};if(t[\"a:srgbClr\"])r.color=\"#\"+t[\"a:srgbClr\"].attrs.val;else if(t[\"a:schemeClr\"]){var n=t[\"a:schemeClr\"].attrs.val;a&&(n=a.getColorThemeName(n)),r.color=e.getColor(n)}else if(t[\"a:sysClr\"])r.color=\"#\"+t[\"a:sysClr\"].attrs.lastClr;else if(t[\"a:prstClr\"]){var o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t[\"a:prstClr\"],[\"attrs\",\"val\"]);r.color=function(t){var e,a=[\"white\",\"AliceBlue\",\"AntiqueWhite\",\"Aqua\",\"Aquamarine\",\"Azure\",\"Beige\",\"Bisque\",\"black\",\"BlanchedAlmond\",\"Blue\",\"BlueViolet\",\"Brown\",\"BurlyWood\",\"CadetBlue\",\"Chartreuse\",\"Chocolate\",\"Coral\",\"CornflowerBlue\",\"Cornsilk\",\"Crimson\",\"Cyan\",\"DarkBlue\",\"DarkCyan\",\"DarkGoldenRod\",\"DarkGray\",\"DarkGrey\",\"DarkGreen\",\"DarkKhaki\",\"DarkMagenta\",\"DarkOliveGreen\",\"DarkOrange\",\"DarkOrchid\",\"DarkRed\",\"DarkSalmon\",\"DarkSeaGreen\",\"DarkSlateBlue\",\"DarkSlateGray\",\"DarkSlateGrey\",\"DarkTurquoise\",\"DarkViolet\",\"DeepPink\",\"DeepSkyBlue\",\"DimGray\",\"DimGrey\",\"DodgerBlue\",\"FireBrick\",\"FloralWhite\",\"ForestGreen\",\"Fuchsia\",\"Gainsboro\",\"GhostWhite\",\"Gold\",\"GoldenRod\",\"Gray\",\"Grey\",\"Green\",\"GreenYellow\",\"HoneyDew\",\"HotPink\",\"IndianRed\",\"Indigo\",\"Ivory\",\"Khaki\",\"Lavender\",\"LavenderBlush\",\"LawnGreen\",\"LemonChiffon\",\"LightBlue\",\"LightCoral\",\"LightCyan\",\"LightGoldenRodYellow\",\"LightGray\",\"LightGrey\",\"LightGreen\",\"LightPink\",\"LightSalmon\",\"LightSeaGreen\",\"LightSkyBlue\",\"LightSlateGray\",\"LightSlateGrey\",\"LightSteelBlue\",\"LightYellow\",\"Lime\",\"LimeGreen\",\"Linen\",\"Magenta\",\"Maroon\",\"MediumAquaMarine\",\"MediumBlue\",\"MediumOrchid\",\"MediumPurple\",\"MediumSeaGreen\",\"MediumSlateBlue\",\"MediumSpringGreen\",\"MediumTurquoise\",\"MediumVioletRed\",\"MidnightBlue\",\"MintCream\",\"MistyRose\",\"Moccasin\",\"NavajoWhite\",\"Navy\",\"OldLace\",\"Olive\",\"OliveDrab\",\"Orange\",\"OrangeRed\",\"Orchid\",\"PaleGoldenRod\",\"PaleGreen\",\"PaleTurquoise\",\"PaleVioletRed\",\"PapayaWhip\",\"PeachPuff\",\"Peru\",\"Pink\",\"Plum\",\"PowderBlue\",\"Purple\",\"RebeccaPurple\",\"Red\",\"RosyBrown\",\"RoyalBlue\",\"SaddleBrown\",\"Salmon\",\"SandyBrown\",\"SeaGreen\",\"SeaShell\",\"Sienna\",\"Silver\",\"SkyBlue\",\"SlateBlue\",\"SlateGray\",\"SlateGrey\",\"Snow\",\"SpringGreen\",\"SteelBlue\",\"Tan\",\"Teal\",\"Thistle\",\"Tomato\",\"Turquoise\",\"Violet\",\"Wheat\",\"White\",\"WhiteSmoke\",\"Yellow\",\"YellowGreen\"],r=[\"ffffff\",\"f0f8ff\",\"faebd7\",\"00ffff\",\"7fffd4\",\"f0ffff\",\"f5f5dc\",\"ffe4c4\",\"000000\",\"ffebcd\",\"0000ff\",\"8a2be2\",\"a52a2a\",\"deb887\",\"5f9ea0\",\"7fff00\",\"d2691e\",\"ff7f50\",\"6495ed\",\"fff8dc\",\"dc143c\",\"00ffff\",\"00008b\",\"008b8b\",\"b8860b\",\"a9a9a9\",\"a9a9a9\",\"006400\",\"bdb76b\",\"8b008b\",\"556b2f\",\"ff8c00\",\"9932cc\",\"8b0000\",\"e9967a\",\"8fbc8f\",\"483d8b\",\"2f4f4f\",\"2f4f4f\",\"00ced1\",\"9400d3\",\"ff1493\",\"00bfff\",\"696969\",\"696969\",\"1e90ff\",\"b22222\",\"fffaf0\",\"228b22\",\"ff00ff\",\"dcdcdc\",\"f8f8ff\",\"ffd700\",\"daa520\",\"808080\",\"808080\",\"008000\",\"adff2f\",\"f0fff0\",\"ff69b4\",\"cd5c5c\",\"4b0082\",\"fffff0\",\"f0e68c\",\"e6e6fa\",\"fff0f5\",\"7cfc00\",\"fffacd\",\"add8e6\",\"f08080\",\"e0ffff\",\"fafad2\",\"d3d3d3\",\"d3d3d3\",\"90ee90\",\"ffb6c1\",\"ffa07a\",\"20b2aa\",\"87cefa\",\"778899\",\"778899\",\"b0c4de\",\"ffffe0\",\"00ff00\",\"32cd32\",\"faf0e6\",\"ff00ff\",\"800000\",\"66cdaa\",\"0000cd\",\"ba55d3\",\"9370db\",\"3cb371\",\"7b68ee\",\"00fa9a\",\"48d1cc\",\"c71585\",\"191970\",\"f5fffa\",\"ffe4e1\",\"ffe4b5\",\"ffdead\",\"000080\",\"fdf5e6\",\"808000\",\"6b8e23\",\"ffa500\",\"ff4500\",\"da70d6\",\"eee8aa\",\"98fb98\",\"afeeee\",\"db7093\",\"ffefd5\",\"ffdab9\",\"cd853f\",\"ffc0cb\",\"dda0dd\",\"b0e0e6\",\"800080\",\"663399\",\"ff0000\",\"bc8f8f\",\"4169e1\",\"8b4513\",\"fa8072\",\"f4a460\",\"2e8b57\",\"fff5ee\",\"a0522d\",\"c0c0c0\",\"87ceeb\",\"6a5acd\",\"708090\",\"708090\",\"fffafa\",\"00ff7f\",\"4682b4\",\"d2b48c\",\"008080\",\"d8bfd8\",\"ff6347\",\"40e0d0\",\"ee82ee\",\"f5deb3\",\"ffffff\",\"f5f5f5\",\"ffff00\",\"9acd32\"],n=a.indexOf(t);-1!=n&&(e=r[n]);return\"#\".concat(e||\"000000\")}(o)}var i=t[\"a:srgbClr\"]||t[\"a:schemeClr\"]||t[\"a:sysClr\"],s=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"a:alpha\",\"attrs\",\"val\"],1e5);r.alpha=s/1e5;var h=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"a:shade\",\"attrs\",\"val\"]);h&&(r.shade=h/1e5);var l=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"a:lumMod\",\"attrs\",\"val\"]);l&&(r.lumMod=l/1e5);var p=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"a:lumOff\",\"attrs\",\"val\"]);p&&(r.lumOff=p/1e5);var d=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"a:tint\",\"attrs\",\"val\"]);return d&&(r.tint=d/1e5),r}function x(t,e,a){var r,n={type:\"blipFill\"},o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:blip\",\"attrs\",\"r:embed\"]);if(o){var i=null===(r=a.rels[o])||void 0===r?void 0:r.target;i&&(n.base64=e.getMedia(i))}var s=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:blip\",\"a:alphaModFix\",\"attrs\",\"amt\"]);s&&(n.alpha=s/1e5);var h=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:stretch\",\"a:fillRect\",\"attrs\"]);return h&&(n.fillRect={},h.b&&(n.fillRect.b=h.b/1e5),h.t&&(n.fillRect.t=h.t/1e5),h.r&&(n.fillRect.r=h.r/1e5),h.l&&(n.fillRect.l=h.l/1e5)),n}function L(t,e,a){var r={type:\"gradFill\",tileRect:{},lin:{},gsList:[]};r.flip=t.attrs.flip,r.path=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:path\",\"attrs\",\"path\"])||\"linear\",r.rotWithShape=\"1\"===t.attrs.rotWithShape,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:lin\",\"attrs\",\"ang\"])&&(r.lin.ang=w(t[\"a:lin\"].attrs.ang)),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:lin\",\"attrs\",\"scaled\"])&&(r.lin.scaled=\"1\"===t[\"a:lin\"].attrs.scaled);var n=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:gsLst\",\"a:gs\"])||[];return r.gsList=n.map((function(t){return{color:g(t,e,a),pos:b(t.attrs.pos)}})),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:tileRect\",\"attrs\",\"l\"])&&(r.tileRect.l=b(t[\"a:tileRect\"].attrs.l)),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:tileRect\",\"attrs\",\"t\"])&&(r.tileRect.t=b(t[\"a:tileRect\"].attrs.t)),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:tileRect\",\"attrs\",\"r\"])&&(r.tileRect.r=b(t[\"a:tileRect\"].attrs.r)),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:tileRect\",\"attrs\",\"b\"])&&(r.tileRect.b=b(t[\"a:tileRect\"].attrs.b)),r}function M(t){return t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function A(t){return t<.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055}function P(t,e){var a=t[0],r=t[1],n=t[2],o=M(a/255)*e,c=M(r/255)*e,i=M(n/255)*e;return[Math.round(255*A(o)),Math.round(255*A(c)),Math.round(255*A(i))]}function k(t,e,a){var r=t/255,n=e/255,o=a/255,c=Math.max(r,n,o),i=Math.min(r,n,o),s=c-i,h=0,l=(c+i)/2;return 0===s?h=0:c===r?h=(n-o)/s%6:c===n?h=(o-r)/s+2:c===o&&(h=(r-n)/s+4),(h=Math.round(60*h))<0&&(h+=360),{h:h,s:0===s||0===l||1===l?0:s/(1-Math.abs(2*l-1)),l:l}}function j(t,e,a){var r,n,o,c=(1-Math.abs(2*a-1))*e,i=c*(1-Math.abs(t/60%2-1)),s=a-c/2;return t<60?(r=c,n=i,o=0):t<120?(r=i,n=c,o=0):t<180?(r=0,n=c,o=i):t<240?(r=0,n=i,o=c):t<300?(r=i,n=0,o=c):(r=c,n=0,o=i),[r=Math.round(255*(r+s)),n=Math.round(255*(n+s)),o=Math.round(255*(o+s))]}function S(t,e){if(!t||\"none\"===t.type)return\"\";if(\"solidFill\"===t.type&&/^#[\\da-fA-F]{3,6}$/.test(t.color)){var a=parseInt(t.color.substr(1,2),16),r=parseInt(t.color.substr(3,2),16),n=parseInt(t.color.substr(5,2),16);if(t.shade){var o=P([a,r,n],t.shade);a=o[0],r=o[1],n=o[2]}if(t.lumMod){var c=function(t,e,a,r){var n=k(t,e,a),o=n.h,c=n.l*r;return c>=1&&(c=1),j(o,n.s,c)}(a,r,n,t.lumMod);a=c[0],r=c[1],n=c[2]}if(t.lumOff){c=function(t,e,a,r){var n=k(t,e,a),o=n.h,c=r+n.l;return c>1&&(c=1),j(o,n.s,c)}(a,r,n,t.lumOff);a=c[0],r=c[1],n=c[2]}if(t.tint||(null==e?void 0:e.light)){var i=function(t,e,a,r){var n=k(t,e,a),o=n.h,c=n.l;return r>=1&&(r=1),j(o,n.s,c*r+(1-r))}(a,r,n,t.tint||(null==e?void 0:e.light));a=i[0],r=i[1],n=i[2]}if(null==e?void 0:e.dark){var s=P([a,r,n],null==e?void 0:e.dark);a=s[0],r=s[1],n=s[2]}var h=t.alpha;return\"rgba(\".concat(a,\",\").concat(r,\",\").concat(n,\",\").concat(h,\")\")}}var C=function(){function t(t,e,a){var r,n;this.uuid=(0,uuid__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(),this.offset={x:0,y:0},this.extend={w:0,h:0},this.rotate=0,this.order=0,this.flipV=!1,this.flipH=!1,this.source=t,this.ctx=e,this.group=a;var o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"p:nvSpPr\",\"p:nvPr\"]);if(o){var i=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(o,\"p:ph\");i&&i.attrs&&(this.idx=i.attrs.idx,this.type=i.attrs.type),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(o,[\"attrs\",\"userDrawn\"])&&(this.userDrawn=\"1\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(o,[\"attrs\",\"userDrawn\"]))}if(this.order=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"attrs.order\",0),this.source[\"p:spPr\"]){if(l=this.getXfrm()){var h=this.group&&((null===(r=this.ctx.pptx)||void 0===r?void 0:r.wps)||\"point\"===u(parseInt(l[\"a:off\"].attrs.x)))?y:f;this.offset={x:Math.round(h(parseInt(l[\"a:off\"].attrs.x))),y:Math.round(h(parseInt(l[\"a:off\"].attrs.y)))},this.extend={w:Math.round(h(parseInt(l[\"a:ext\"].attrs.cx))),h:Math.round(h(parseInt(l[\"a:ext\"].attrs.cy||\"0\")))},this.rotate=w(parseInt((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(l,\"attrs.rot\",0))),this.flipV=\"1\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(l,\"attrs.flipV\"),this.flipH=\"1\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(l,\"attrs.flipH\")}}else if(this.source[\"p:xfrm\"]){var l=this.source[\"p:xfrm\"];h=this.group&&((null===(n=this.ctx.pptx)||void 0===n?void 0:n.wps)||\"point\"===u(parseInt(l[\"a:off\"].attrs.x)))?y:f;this.offset={x:Math.round(h(parseInt(l[\"a:off\"].attrs.x))),y:Math.round(h(parseInt(l[\"a:off\"].attrs.y)))},this.extend={w:Math.round(h(parseInt(l[\"a:ext\"].attrs.cx))),h:Math.round(h(parseInt(l[\"a:ext\"].attrs.cy)))}}if(a){var p=a.extend,d=a.chExtend,v=a.chOffset,b=0===d.w?0:p.w/d.w,m=0===d.h?0:p.h/d.h;this.extend.w=this.extend.w*b,this.extend.h=this.extend.h*m,this.offset.x=(this.offset.x-v.x)*b,this.offset.y=(this.offset.y-v.y)*m}}return Object.defineProperty(t.prototype,\"theme\",{get:function(){return(this.ctx.sliderMaster||this.ctx).theme},enumerable:!1,configurable:!0}),t.prototype.getColorThemeName=function(t){return this.ctx.getColorThemeName(t)},t.prototype.getXfrm=function(){var t=this.source[\"p:spPr\"][\"a:xfrm\"];return t||(this.idx?t=this.ctx.getNodeInheritAttrsByIdx(this.idx,[\"p:spPr\",\"a:xfrm\"]):this.type&&(t=this.ctx.getNodeInheritAttrsByType(this.type,[\"p:spPr\",\"a:xfrm\"]))),t},t}(),I=function(){function e(t,e){this.props={},this.inheritProps={},this.source=t,this.node=e,this._getInheritBodyProps(),this._parseBodyProps(),this._parseLstStyle(),this._parseText()}return e.prototype._getInheritBodyProps=function(){var t,e=this.node.ctx,a=this.node.type,r=this.node.idx;if(a||r)switch(e.slideType){case\"slideMaster\":break;case\"slideLayout\":(t=a?e.slideMaster.getNodeByType(a):e.slideMaster.getNodeByIdx(r))&&(this.inheritProps=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"textBody\",\"props\"])||{});break;case\"slide\":(t=a?e.slideLayout.slideMaster.getNodeByType(a):e.slideLayout.slideMaster.getNodeByIdx(r))&&Object.assign(this.inheritProps,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"textBody\",\"props\"])||{}),(t=a?e.slideLayout.getNodeByType(a):e.slideLayout.getNodeByIdx(r))&&Object.assign(this.inheritProps,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"textBody\",\"props\"])||{})}},e.prototype._parseBodyProps=function(){var t=this,e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"a:bodyPr\",\"attrs\"])||{};Object.keys(e).forEach((function(a){switch(a){case\"anchor\":t.props.anchor=e[a];break;case\"rtlCol\":t.props.rtlCol=\"1\"===e[a];break;case\"lIns\":case\"rIns\":case\"tIns\":case\"bIns\":t.props[a]=f(parseInt(e[a]));break;case\"order\":break;default:t.props[a]=e[a]}}));var a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"a:bodyPr\",\"a:normAutofit\",\"attrs\"]);if(a){this.props.normAutofit={};var r=a.fontScale;r&&(this.props.normAutofit.fontScale=b(parseInt(r)));var n=a.lnSpcReduction;n&&(this.props.normAutofit.lnSpcReduction=b(parseInt(n)))}},e.prototype._parseLstStyle=function(){var t=this,e={},a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,\"a:lstStyle\")||{};Object.keys(a).forEach((function(r){if(r.startsWith(\"a:\")&&r.endsWith(\"pPr\")){var n=r.substr(2,r.length-5);e[n]={props:t._formatPPr(a[r])};var o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(a[r],[\"a:defRPr\"]);e[n].defRPr=t._formatRPr(o)}})),this.lstStyle=e},e.prototype._parseText=function(){var t=this,e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"a:p\"])||[];Array.isArray(e)||(e=[e]),this.paragraphs=e.map((function(e){return t._parseParagraph(e)}))},e.prototype._parseParagraph=function(e){var a=this,r={props:{},inheritProps:{},inheritRProps:{},endParaRProps:{},rows:[]},n=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:pPr\"])||{};r.props=this._formatPPr(n);var o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:endParaRPr\"]);r.endParaRProps=this._formatRPr(o);var i=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:r\"])||[];Array.isArray(i)||(i=[i]);var s=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:br\"])||[];return Array.isArray(s)||(s=[s]),(i=i.concat(s.map((function(e){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({isBr:!0},e)})))).sort((function(t,e){return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"order\"])-(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"attrs\",\"order\"])})),r.rows=i.map((function(t){return a._parseRow(t)})),r.inheritProps=this._getInheritPProps(r.props.level),r.inheritRProps=this._getInheritRProps(r.props.level),r},e.prototype._getInheritPProps=function(t){void 0===t&&(t=\"0\");var e,a={},r=this.node.ctx,n=this.node.type,o=this.node.idx;switch(r.slideType){case\"slideMaster\":this.node.isTextBox?Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r.defaultTextStyle,[\"lvl\".concat(t?+t+1:1),\"props\"])||{}):Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r,[\"textStyles\",\"otherStyle\",\"lvl\".concat(t?+t+1:1),\"props\"])||{});break;case\"slideLayout\":this.node.isTextBox?Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r.slideMaster.defaultTextStyle,[\"lvl\".concat(t?+t+1:1),\"props\"])||{}):Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r.slideMaster,[\"textStyles\",\"otherStyle\",\"lvl\".concat(t?+t+1:1),\"props\"])||{}),(n||o)&&(e=n?r.slideMaster.getNodeByType(n):r.slideMaster.getNodeByIdx(o))&&Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"textBody\",\"lstStyle\",\"lvl\".concat(t?+t+1:1),\"props\"])||{});break;case\"slide\":this.node.isTextBox?Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r.slideLayout.slideMaster.defaultTextStyle,[\"lvl\".concat(t?+t+1:1),\"props\"])||{}):Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r.slideLayout.slideMaster,[\"textStyles\",\"otherStyle\",\"lvl\".concat(t?+t+1:1),\"props\"])||{}),(n||o)&&([\"subTitle\",\"ctrTitle\",\"title\"].includes(n)&&Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r.slideLayout.slideMaster,[\"textStyles\",\"titleStyle\",\"lvl\".concat(t?+t+1:1),\"props\"])||{}),(e=n?r.slideLayout.slideMaster.getNodeByType(n):r.slideLayout.slideMaster.getNodeByIdx(o))&&Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"textBody\",\"lstStyle\",\"lvl\".concat(t?+t+1:1),\"props\"])||{}),(e=n?r.slideLayout.getNodeByType(n):r.slideLayout.getNodeByIdx(o))&&Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"textBody\",\"lstStyle\",\"lvl\".concat(t?+t+1:1),\"props\"])||{}))}return a},e.prototype._getInheritRProps=function(t){void 0===t&&(t=\"0\");var e,a={},r=this.node.ctx,n=this.node.type,o=this.node.idx;switch(r.slideType){case\"slideMaster\":this.node.isTextBox?Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r.defaultTextStyle,[\"lvl\".concat(t?+t+1:1),\"defRPr\"])||{}):Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r,[\"textStyles\",\"otherStyle\",\"lvl\".concat(t?+t+1:1),\"defRPr\"])||{});break;case\"slideLayout\":this.node.isTextBox?Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r.slideMaster.defaultTextStyle,[\"lvl\".concat(t?+t+1:1),\"defRPr\"])||{}):Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r.slideMaster,[\"textStyles\",\"otherStyle\",\"lvl\".concat(t?+t+1:1),\"defRPr\"])||{}),(n||o)&&(e=n?r.slideMaster.getNodeByType(n):r.slideMaster.getNodeByIdx(o))&&(a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"textBody\",\"lstStyle\",\"lvl\".concat(t?+t+1:1),\"defRPr\"])||{});break;case\"slide\":this.node.isTextBox?Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r.slideLayout.slideMaster.defaultTextStyle,[\"lvl\".concat(t?+t+1:1),\"defRPr\"])||{}):Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r.slideLayout.slideMaster,[\"textStyles\",\"otherStyle\",\"lvl\".concat(t?+t+1:1),\"defRPr\"])||{}),(n||o)&&([\"subTitle\",\"ctrTitle\",\"title\"].includes(n)&&Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(r.slideLayout.slideMaster,[\"textStyles\",\"titleStyle\",\"lvl\".concat(t?+t+1:1),\"defRPr\"])||{}),(e=n?r.slideLayout.slideMaster.getNodeByType(n):r.slideLayout.slideMaster.getNodeByIdx(o))&&Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"textBody\",\"lstStyle\",\"lvl\".concat(t?+t+1:1),\"defRPr\"])||{}),(e=n?r.slideLayout.getNodeByType(n):r.slideLayout.getNodeByIdx(o))&&Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"textBody\",\"lstStyle\",\"lvl\".concat(t?+t+1:1),\"defRPr\"])||{}))}var i=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.node.source,[\"p:style\",\"a:fontRef\"]);return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,\"a:schemeClr\")&&(a.color=g(i,this.node.theme,this.node)),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.lstStyle,[\"lvl\".concat(t?+t+1:1),\"defRPr\"])&&Object.assign(a,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.lstStyle,[\"lvl\".concat(t?+t+1:1),\"defRPr\"])),a},e.prototype._formatPPr=function(t){var e={},a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"attrs\")||{};return Object.keys(a).forEach((function(t){switch(t){case\"algn\":e.align=a[t];break;case\"marL\":e.marginLeft=f(parseInt(a[t]));break;case\"indent\":e.indent=f(parseInt(a[t]));break;case\"lvl\":e.level=a[t]}})),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:lnSpc\",\"a:spcPct\",\"attrs\",\"val\"])&&(e.lineHeight=parseInt(t[\"a:lnSpc\"][\"a:spcPct\"].attrs.val)/1e5),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:buAutoNum\",\"attrs\",\"type\"])&&(e.buAutoNum=t[\"a:buAutoNum\"].attrs.type),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:buChar\",\"attrs\",\"char\"])&&(e.buChar=t[\"a:buChar\"].attrs.char),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:spcBef\",\"a:spcPts\",\"attrs\",\"val\"])&&(e.spaceBefore=v(parseInt(t[\"a:spcBef\"][\"a:spcPts\"].attrs.val))),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:spcAft\",\"a:spcPts\",\"attrs\",\"val\"])&&(e.spaceAfter=v(parseInt(t[\"a:spcAft\"][\"a:spcPts\"].attrs.val))),e},e.prototype._parseRow=function(t){if(t.isBr)return{isBr:!0};var e={props:{},text:\"\"},a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:rPr\"])||{};return e.props=this._formatRPr(a),e.text=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"a:t\")||\"\",e},e.prototype._formatRPr=function(t){var e={},a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"attrs\")||{};Object.keys(a).forEach((function(t){switch(t){case\"sz\":e.size=parseInt(a[t])/100;break;case\"b\":e.bold=\"1\"===a[t];break;case\"i\":e.italic=\"1\"===a[t];break;case\"u\":e.underline=a[t];break;case\"strike\":e.strike=a[t];break;case\"order\":case\"dirty\":break;default:e[t]=a[t]}}));var r=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"a:solidFill\");r&&(e.color=g(r,this.node.theme,this.node));var n=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"a:highlight\");return n&&(e.background=g(n,this.node.theme,this.node)),e.typeface=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:ea\",\"attrs\",\"typeface\"]),e},e}();function B(t,e,a){var r={};if(!(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"a:noFill\")){(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"attrs.w\")&&(r.width=f(parseInt((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"attrs.w\"))));var n=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"a:solidFill\");n&&(r.color=g(n,e,a));var o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"a:prstDash\");if(o&&(r.type=o.attrs.val),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:miter\"])&&(r.lineJoin=\"miter\"),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:bevel\"])&&(r.lineJoin=\"bevel\"),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:round\"])&&(r.lineJoin=\"round\"),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:miter\",\"attrs\",\"lim\"])&&(r.miterLim=f(parseInt((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:miter\",\"attrs\",\"lim\"])))),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:headEnd\"])){var i=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:headEnd\",\"attrs\"]);r.headEnd={type:i.type,len:i.len,w:i.w}}if((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:tailEnd\"])){var s=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:tailEnd\",\"attrs\"]);r.tailEnd={type:s.type,len:s.len,w:s.w}}return r}}var R=function(a){function r(t,e,r,n){var o=a.call(this,t,r,n)||this;return o.border={},o.prstGeom={},o.isTextBox=!1,o.pptx=e,o._parseShape(),o._parIsTextBox(),o._parsePrstGeom(),o._parseBackground(),o._parseBorder(),o._parseTxt(),o}return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__extends)(r,a),r.prototype._parseShape=function(){if(this.shape=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:spPr\",\"a:prstGeom\",\"attrs\",\"prst\"]),!this.shape&&(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:spPr\",\"a:custGeom\"])){this.shape=\"customGeom\";var t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:spPr\",\"a:custGeom\",\"a:pathLst\",\"a:path\"]),e=[],a=[],r=function(r){switch(r){case\"a:moveTo\":case\"a:cubicBezTo\":case\"a:lnTo\":a=Array.isArray(t[r])?t[r]:[t[r]],e=e.concat(a.map((function(t){return{order:t.attrs.order,type:r.split(\":\")[1],points:(Array.isArray(t[\"a:pt\"])?t[\"a:pt\"]:[t[\"a:pt\"]]).map((function(t){return[f(parseInt((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"x\"]))),f(parseInt((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"y\"])))]}))}})));break;case\"a:close\":a=Array.isArray(t[r])?t[r]:[t[r]],e=e.concat(a.map((function(t){return{order:t.attrs.order,type:r.split(\":\")[1]}})))}};for(var n in t)r(n);e.sort((function(t,e){return t.order-e.order})),this.prstGeom.pathList=e,(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"w\"])&&(this.prstGeom.w=f(parseInt((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"w\"])))),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"h\"])&&(this.prstGeom.h=f(parseInt((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"h\"]))))}},r.prototype._parIsTextBox=function(){this.isTextBox=\"1\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:nvSpPr\",\"p:cNvSpPr\",\"attrs\",\"txBox\"])},r.prototype._parsePrstGeom=function(){var t=this,e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:spPr\",\"a:prstGeom\"]),a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:avLst\",\"a:gd\"]);a&&(Array.isArray(a)||(a=[a]),this.prstGeom.gd=a.map((function(e){var a=[\"pie\",\"chord\",\"arc\"].includes(t.shape)||[\"blockArc\"].includes(t.shape)&&[\"adj1\",\"adj2\"].includes(e.attrs.name)?w(parseInt(e.attrs.fmla.split(\" \")[1])):b(parseInt(e.attrs.fmla.split(\" \")[1]));return{name:e.attrs.name,fmla:a}})))},r.prototype._parseBackground=function(){if(!(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:spPr\",\"a:noFill\"]))if((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:spPr\",\"a:grpFill\"])&&this.group)this.background=this.group.getBackground();else{var t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:spPr\",\"a:solidFill\"]);if(t)this.background=g(t,this.theme,this);else{var e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:spPr\",\"a:gradFill\"]);if(e)this.background=L(e,this.theme,this);else{var a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:spPr\",\"a:blipFill\"]);if(a)this.background=x(a,this.pptx,this.ctx);else{var r=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:style\",\"a:fillRef\"]);r&&(this.background=g(r,this.theme,this))}}}}},r.prototype._parseBorder=function(){var e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:style\",\"a:lnRef\"]);if(e){var a=parseInt(e.attrs.idx),n=this.theme.getLineStyle(a);this.border=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},n),this.border),this.border.color&&this.border.color.color||(this.border.color=g(e,this.theme,this))}var o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:spPr\",\"a:ln\"]);o&&Object.assign(this.border,B(o,this.theme,this)),this.border.color&&this.border.color.color&&!this.border.width&&(this.border.width=r.defaultBorderWidth)},r.prototype._parseTxt=function(){this.textBody=new I((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:txBody\"]),this)},r.defaultBorderWidth=.75,r}(C),T=function(t){function a(e,a,r,n,o){var i,s,h=t.call(this,a,n,o)||this;h.userDrawn=!0,h.pptx=r,h.path=e;var l=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(h.source,[\"p:blipFill\",\"a:srcRect\"]);l&&(h.clip={},l.attrs.b&&(h.clip.b=parseInt(l.attrs.b)/1e5),l.attrs.t&&(h.clip.t=parseInt(l.attrs.t)/1e5),l.attrs.l&&(h.clip.l=parseInt(l.attrs.l)/1e5),l.attrs.r&&(h.clip.r=parseInt(l.attrs.r)/1e5));var p=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(a,[\"p:nvPicPr\",\"p:nvPr\",\"a:audioFile\",\"attrs\",\"r:link\"]);if(p){var d=null===(i=h.ctx.rels[p])||void 0===i?void 0:i.target;h.audioFile=h.pptx.getMedia(d)}var u=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(a,[\"p:nvPicPr\",\"p:nvPr\",\"a:videoFile\",\"attrs\",\"r:link\"]);if(u){var f=null===(s=h.ctx.rels[u])||void 0===s?void 0:s.target;h.videoFile=h.pptx.getMedia(f)}return h}return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__extends)(a,t),Object.defineProperty(a.prototype,\"base64\",{get:function(){return this.pptx.getMedia(this.path)},enumerable:!1,configurable:!0}),a}(C),_=function(a){function r(t,e,r,n){var o=a.call(this,t,r,n)||this;return o.userDrawn=!0,o.props={tableStyleId:\"\"},o.tableGrid={gridCol:[]},o.tr=[],o.tableStyles={},o.pptx=e,o._parseTableProps(),o._parseTableGrid(),o._parseTr(),o._parseInheritStyles(),o}return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__extends)(r,a),Object.defineProperty(r.prototype,\"slideMaster\",{get:function(){return this.ctx.slideMaster||this.ctx},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,\"theme\",{get:function(){return this.slideMaster.theme},enumerable:!1,configurable:!0}),r.prototype._parseTableProps=function(){var t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"a:graphic\",\"a:graphicData\",\"a:tbl\",\"a:tblPr\"]);this.props.tableStyleId=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"a:tableStyleId\"),this.tableStyles=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.slideMaster.tableStyles,this.props.tableStyleId)||{},\"1\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"bandCol\"])&&(this.props.bandCol=!0),\"1\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"bandRow\"])&&(this.props.bandRow=!0),\"1\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"firstCol\"])&&(this.props.firstCol=!0),\"1\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"firstRow\"])&&(this.props.firstRow=!0),\"1\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"lastCol\"])&&(this.props.lastCol=!0),\"1\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"lastRow\"])&&(this.props.lastRow=!0)},r.prototype._parseTableGrid=function(){var t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"a:graphic\",\"a:graphicData\",\"a:tbl\",\"a:tblGrid\",\"a:gridCol\"]);if(t)for(var e=0;e<t.length;e++){var a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t[e],[\"attrs\",\"w\"]);this.tableGrid.gridCol.push({width:f(parseInt(a))})}},r.prototype._parseTr=function(){var t=[],e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"a:graphic\",\"a:graphicData\",\"a:tbl\",\"a:tr\"]);Array.isArray(e)||(e=[e]);for(var a=0;a<e.length;a++){var r={props:{},td:[]},n=e[a];r.props.height=f(parseInt((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(n,[\"attrs\",\"h\"])));var o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(n,[\"a:tc\"]);Array.isArray(o)||(o=[o]);for(var i=0;i<o.length;i++)r.td.push(this._parseTd(o[i]));t.push(r)}this.tr=t},r.prototype._parseTd=function(t){var e,a,r,n,o=this,i={props:{border:{}},paragraphs:[]},s=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:tcPr\",\"attrs\"]);(null==s?void 0:s.marB)&&(i.props.marB=f(parseInt(null==s?void 0:s.marB))),(null==s?void 0:s.marT)&&(i.props.marT=f(parseInt(null==s?void 0:s.marT))),(null==s?void 0:s.marL)&&(i.props.marL=f(parseInt(null==s?void 0:s.marL))),(null==s?void 0:s.marR)&&(i.props.marR=f(parseInt(null==s?void 0:s.marR))),(null==s?void 0:s.anchor)&&(i.props.anchor=null==s?void 0:s.anchor);var h=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:tcPr\"]);(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(h,[\"a:lnR\"])&&(i.props.border.right=B((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(h,[\"a:lnR\"]),this.theme,this.ctx)),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(h,[\"a:lnL\"])&&(i.props.border.left=B((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(h,[\"a:lnL\"]),this.theme,this.ctx)),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(h,[\"a:lnT\"])&&(i.props.border.top=B((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(h,[\"a:lnT\"]),this.theme,this.ctx)),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(h,[\"a:lnB\"])&&(i.props.border.bottom=B((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(h,[\"a:lnB\"]),this.theme,this.ctx)),(null===(e=null==t?void 0:t.attrs)||void 0===e?void 0:e.rowSpan)&&(i.props.rowSpan=parseInt(t.attrs.rowSpan)),(null===(a=null==t?void 0:t.attrs)||void 0===a?void 0:a.gridSpan)&&(i.props.gridSpan=parseInt(t.attrs.gridSpan)),(null===(r=null==t?void 0:t.attrs)||void 0===r?void 0:r.vMerge)&&(i.props.vMerge=\"1\"===t.attrs.vMerge),(null===(n=null==t?void 0:t.attrs)||void 0===n?void 0:n.hMerge)&&(i.props.hMerge=\"1\"===t.attrs.hMerge);var l=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:tcPr\",\"a:solidFill\"]);l&&(i.props.background=g(l,this.theme,this.ctx));var p=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:txBody\"]),d=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(p,[\"a:p\"]);return Array.isArray(d)||(d=[d]),i.paragraphs=d.map((function(t){return o._parseParagraph(t)})),i},r.prototype._parseParagraph=function(e){var a=this,r={props:{},inheritProps:{},inheritRProps:{},endParaRProps:{},rows:[]},n=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:pPr\"])||{};r.props=this._formatPPr(n);var o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:endParaRPr\"]);r.endParaRProps=this._formatRPr(o);var i=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:r\"])||[];Array.isArray(i)||(i=[i]);var s=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:br\"])||[];return Array.isArray(s)||(s=[s]),(i=i.concat(s.map((function(e){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({isBr:!0},e)})))).sort((function(t,e){return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"order\"])-(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"attrs\",\"order\"])})),r.rows=i.map((function(t){return a._parseRow(t)})),r},r.prototype._parseRow=function(t){if(t.isBr)return{isBr:!0};var e={props:{},text:\"\"},a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:rPr\"])||{};return e.props=this._formatRPr(a),e.text=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"a:t\")||\"\",e},r.prototype._formatPPr=function(t){var e={},a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"attrs\")||{};return Object.keys(a).forEach((function(t){if(\"algn\"===t)e.align=a[t]})),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:lnSpc\",\"a:spcPct\",\"attrs\",\"val\"])&&(e.lineHeight=parseInt(t[\"a:lnSpc\"][\"a:spcPct\"].attrs.val)/1e5),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:buAutoNum\",\"attrs\",\"type\"])&&(e.buAutoNum=t[\"a:buAutoNum\"].attrs.type),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:buChar\",\"attrs\",\"char\"])&&(e.buChar=t[\"a:buChar\"].attrs.char),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:spcBef\",\"a:spcPts\",\"attrs\",\"val\"])&&(e.spaceBefore=v(parseInt(t[\"a:spcBef\"][\"a:spcPts\"].attrs.val))),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:spcAft\",\"a:spcPts\",\"attrs\",\"val\"])&&(e.spaceAfter=v(parseInt(t[\"a:spcAft\"][\"a:spcPts\"].attrs.val))),e},r.prototype._formatRPr=function(t){var e={},a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"attrs\")||{};Object.keys(a).forEach((function(t){switch(t){case\"sz\":e.size=parseInt(a[t])/100;break;case\"b\":e.bold=\"1\"===a[t];break;case\"i\":e.italic=\"1\"===a[t];break;case\"u\":e.underline=a[t];break;case\"strike\":e.strike=a[t];break;case\"order\":case\"dirty\":break;default:e[t]=a[t]}}));var r=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"a:solidFill\");r&&(e.color=g(r,this.theme,this.ctx));var n=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"a:highlight\");return n&&(e.background=g(n,this.theme,this.ctx)),e.typeface=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:ea\",\"attrs\",\"typeface\"]),e},r.prototype._isLastCol=function(t,e){var a,r;if(e===t.length-1)return!0;for(var n=e+1;n<t.length;n++)if(!(null===(a=t[n].props)||void 0===a?void 0:a.hMerge)&&!(null===(r=t[n].props)||void 0===r?void 0:r.vMerge))return!1;return!0},r.prototype._isBandRow=function(t){var e;return(null===(e=this.props)||void 0===e?void 0:e.firstRow)?t%2==1:t%2==0},r.prototype._isBandCol=function(t){var e;return(null===(e=this.props)||void 0===e?void 0:e.firstCol)?t%2==1:t%2==0},r.prototype._parseInheritStyles=function(){var e,a,r,n,o,c,i,s,h=this,l=null===(a=null===(e=this.tableStyles)||void 0===e?void 0:e.wholeTbl)||void 0===a?void 0:a.tcStyle,p=null===(n=null===(r=this.tableStyles)||void 0===r?void 0:r.wholeTbl)||void 0===n?void 0:n.tcTxStyle,d=null===(c=null===(o=this.slideMaster.defaultTextStyle)||void 0===o?void 0:o.lvl1)||void 0===c?void 0:c.props,u=null===(s=null===(i=this.slideMaster.defaultTextStyle)||void 0===i?void 0:i.lvl1)||void 0===s?void 0:s.defRPr;this.tr.forEach((function(e,a){e.td.forEach((function(r,n){var o,c,i,s,f,y,v,w,b,m,g,x,L,M,A,P,k,j,S,C,I,B,R,T,_,O,E,N,D,F,Z,G,z,W,H,Q,V,X,q,U,Y,J,K,$,tt,et,at,rt,nt,ot,ct,it,st,ht,lt,pt,dt=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},d),l),ut=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},u),p);h.props.firstRow&&0===a?(dt=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},dt),null===(c=null===(o=h.tableStyles)||void 0===o?void 0:o.firstRow)||void 0===c?void 0:c.tcStyle),{border:(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},null==dt?void 0:dt.border),null===(f=null===(s=null===(i=h.tableStyles)||void 0===i?void 0:i.firstRow)||void 0===s?void 0:s.tcStyle)||void 0===f?void 0:f.border)}),ut=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},ut),null===(v=null===(y=h.tableStyles)||void 0===y?void 0:y.firstRow)||void 0===v?void 0:v.tcTxStyle)):h.props.lastRow&&a===h.tr.length-1?(dt=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},dt),null===(b=null===(w=h.tableStyles)||void 0===w?void 0:w.lastRow)||void 0===b?void 0:b.tcStyle),{border:(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},null==dt?void 0:dt.border),null===(x=null===(g=null===(m=h.tableStyles)||void 0===m?void 0:m.lastRow)||void 0===g?void 0:g.tcStyle)||void 0===x?void 0:x.border)}),ut=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},ut),null===(M=null===(L=h.tableStyles)||void 0===L?void 0:L.lastRow)||void 0===M?void 0:M.tcTxStyle)):h.props.firstCol&&0===n?(dt=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},dt),null===(P=null===(A=h.tableStyles)||void 0===A?void 0:A.firstCol)||void 0===P?void 0:P.tcStyle),{border:(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},null==dt?void 0:dt.border),null===(S=null===(j=null===(k=h.tableStyles)||void 0===k?void 0:k.firstCol)||void 0===j?void 0:j.tcStyle)||void 0===S?void 0:S.border)}),ut=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},ut),null===(I=null===(C=h.tableStyles)||void 0===C?void 0:C.firstCol)||void 0===I?void 0:I.tcTxStyle)):h.props.lastCol&&h._isLastCol(e.td,n)?(dt=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},dt),null===(R=null===(B=h.tableStyles)||void 0===B?void 0:B.lastCol)||void 0===R?void 0:R.tcStyle),{border:(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},null==dt?void 0:dt.border),null===(O=null===(_=null===(T=h.tableStyles)||void 0===T?void 0:T.lastCol)||void 0===_?void 0:_.tcStyle)||void 0===O?void 0:O.border)}),ut=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},ut),null===(N=null===(E=h.tableStyles)||void 0===E?void 0:E.lastCol)||void 0===N?void 0:N.tcTxStyle)):(h.props.bandRow&&(h._isBandRow(a)?(dt=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},dt),null===(F=null===(D=h.tableStyles)||void 0===D?void 0:D.band1H)||void 0===F?void 0:F.tcStyle),{border:(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},null==dt?void 0:dt.border),null===(z=null===(G=null===(Z=h.tableStyles)||void 0===Z?void 0:Z.band1H)||void 0===G?void 0:G.tcStyle)||void 0===z?void 0:z.border)}),ut=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},ut),null===(H=null===(W=h.tableStyles)||void 0===W?void 0:W.band1H)||void 0===H?void 0:H.tcTxStyle)):(dt=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},dt),null===(V=null===(Q=h.tableStyles)||void 0===Q?void 0:Q.band2V)||void 0===V?void 0:V.tcStyle),{border:(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},null==dt?void 0:dt.border),null===(U=null===(q=null===(X=h.tableStyles)||void 0===X?void 0:X.band2V)||void 0===q?void 0:q.tcStyle)||void 0===U?void 0:U.border)}),ut=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},ut),null===(J=null===(Y=h.tableStyles)||void 0===Y?void 0:Y.band2V)||void 0===J?void 0:J.tcTxStyle))),h.props.bandCol&&(h._isBandCol(n)?(dt=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},dt),null===($=null===(K=h.tableStyles)||void 0===K?void 0:K.band1V)||void 0===$?void 0:$.tcStyle),{border:(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},null==dt?void 0:dt.border),null===(at=null===(et=null===(tt=h.tableStyles)||void 0===tt?void 0:tt.band1V)||void 0===et?void 0:et.tcStyle)||void 0===at?void 0:at.border)}),ut=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},ut),null===(nt=null===(rt=h.tableStyles)||void 0===rt?void 0:rt.band1V)||void 0===nt?void 0:nt.tcTxStyle)):(dt=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},dt),null===(ct=null===(ot=h.tableStyles)||void 0===ot?void 0:ot.band2H)||void 0===ct?void 0:ct.tcStyle),{border:(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},null==dt?void 0:dt.border),null===(ht=null===(st=null===(it=h.tableStyles)||void 0===it?void 0:it.band2H)||void 0===st?void 0:st.tcStyle)||void 0===ht?void 0:ht.border)}),ut=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},ut),null===(pt=null===(lt=h.tableStyles)||void 0===lt?void 0:lt.band2H)||void 0===pt?void 0:pt.tcTxStyle)))),r.inheritTcStyle=dt,r.inheritTcTxStyle=ut}))}))},r}(C),O=function(){function t(t,e,a,r){if(this.offset={x:0,y:0},this.chOffset={x:0,y:0},this.extend={w:0,h:0},this.chExtend={w:0,h:0},this.rotate=0,this.nodes=[],this.flipV=!1,this.flipH=!1,this.userDrawn=!0,this.order=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"order\"]),this.pptx=e,this.ctx=a,this.source=t,this.group=r,this.source[\"p:grpSpPr\"]){var n=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:grpSpPr\",\"a:xfrm\"]);if(n&&(this.offset={x:Math.round(this.group&&this.pptx.wps?y(parseInt(n[\"a:off\"].attrs.x)):f(parseInt(n[\"a:off\"].attrs.x))),y:Math.round(this.group&&this.pptx.wps?y(parseInt(n[\"a:off\"].attrs.y)):f(parseInt(n[\"a:off\"].attrs.y)))},this.chOffset={x:Math.round(\"point\"===u(n[\"a:chOff\"].attrs.x)||this.pptx.wps?y(parseInt(n[\"a:chOff\"].attrs.x)):f(parseInt(n[\"a:chOff\"].attrs.x))),y:Math.round(\"point\"===u(n[\"a:chOff\"].attrs.y)||this.pptx.wps?y(parseInt(n[\"a:chOff\"].attrs.y)):f(parseInt(n[\"a:chOff\"].attrs.y)))},this.chExtend={w:Math.round(\"point\"===u(n[\"a:chExt\"].attrs.cx)||this.pptx.wps?y(parseInt(n[\"a:chExt\"].attrs.cx)):f(parseInt(n[\"a:chExt\"].attrs.cx))),h:Math.round(\"point\"===u(n[\"a:chExt\"].attrs.cy)||this.pptx.wps?y(parseInt(n[\"a:chExt\"].attrs.cy)):f(parseInt(n[\"a:chExt\"].attrs.cy)))},this.extend={w:Math.round(this.group&&this.pptx.wps?y(parseInt(n[\"a:ext\"].attrs.cx)):f(parseInt(n[\"a:ext\"].attrs.cx))),h:Math.round(this.group&&this.pptx.wps?y(parseInt(n[\"a:ext\"].attrs.cy)):f(parseInt(n[\"a:ext\"].attrs.cy)))},this.rotate=w(parseInt((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(n,\"attrs.rot\",0))),this.flipV=\"1\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(n,\"attrs.flipV\"),this.flipH=\"1\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(n,\"attrs.flipH\")),r){var o=r.extend,i=r.chExtend,s=r.chOffset,h=o.w/i.w,l=o.h/i.h;this.extend.w=this.extend.w*h,this.extend.h=this.extend.h*l,this.offset.x=(this.offset.x-s.x)*h,this.offset.y=(this.offset.y-s.y)*l}}this._parseBackground(),this._parseNodes()}return t.prototype.getBackground=function(){return this.background&&\"none\"!==this.background.type?this.background:this.group?this.group.getBackground():void 0},t.prototype._parseBackground=function(){var t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:grpSpPr\"]);t&&t[\"a:solidFill\"]?this.background=g(t[\"a:solidFill\"],this.ctx.theme,this.ctx):t&&t[\"a:gradFill\"]?this.background=L(t[\"a:gradFill\"],this.ctx.theme,this.ctx):t&&t[\"a:blipFill\"]&&(this.background=x(t[\"a:blipFill\"],this.pptx,this.ctx))},t.prototype._parseNodes=function(){var t=this.source;Z(this.nodes,t,this.pptx,this.ctx,this)},t}(),E=function(t){function n(e,a,r,n){var o=t.call(this,e,r,n)||this;return o.nodes=[],o.pptx=a,o}return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__extends)(n,t),n.prototype.parseNode=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t,e,a,n,o,i,s,h,l;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(r){switch(r.label){case 0:return r.trys.push([0,3,,4]),(t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"a:graphic\",\"a:graphicData\",\"dgm:relIds\",\"attrs\",\"r:dm\"]))&&this.ctx.rels[t]?(e=this.ctx.rels[t].target,n=p,[4,this.pptx.getXmlByPath(e)]):[2];case 1:return a=n.apply(void 0,[r.sent()]),(o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(a,[\"dgm:dataModel\",\"dgm:extLst\",\"a:ext\",\"dsp:dataModelExt\",\"attrs\",\"relId\"]))&&this.ctx.rels[o]?(i=this.ctx.rels[o].target,[4,this.pptx.getXmlByPath(i)]):[2];case 2:return s=(s=r.sent()).replace(/dsp:/g,\"p:\"),h=p(s),l=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(h,[\"p:drawing\",\"p:spTree\"]),Z(this.nodes,l,this.pptx,this.ctx),[3,4];case 3:return r.sent(),[3,4];case 4:return[2]}}))}))},n}(C),N=function(t){function o(e,a,r,n){var o=t.call(this,e,r,n)||this;return o.options={title:{},tooltip:{},legend:{},series:[],color:[]},o.userDrawn=!0,o.pptx=a,o}return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__extends)(o,t),Object.defineProperty(o.prototype,\"slideMaster\",{get:function(){return this.ctx.slideMaster||this.ctx},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,\"theme\",{get:function(){return this.slideMaster.theme},enumerable:!1,configurable:!0}),o.prototype.parseNode=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t,e,a,n,o,i;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),(t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"a:graphic\",\"a:graphicData\",\"c:chart\",\"attrs\",\"r:id\"]))&&this.ctx.rels[t]?(e=this.ctx.rels[t].target,n=p,[4,this.pptx.getXmlByPath(e)]):[2];case 1:return a=n.apply(void 0,[r.sent()]),o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(a,[\"c:chartSpace\",\"c:chart\"]),i=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(o,[\"c:plotArea\"]),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:lineChart\"])?this.parseLine((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:lineChart\"]),o):(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:line3DChart\"])?this.parseLine((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:line3DChart\"]),o):(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:areaChart\"])?this.parseAreaLine((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:areaChart\"]),o):(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:area3DChart\"])?this.parseAreaLine((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:area3DChart\"]),o):(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:barChart\"])?this.parseBar((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:barChart\"]),o):(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:bar3DChart\"])?this.parseBar((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:bar3DChart\"]),o):(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:pieChart\"])?this.parsePie((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:pieChart\"])):(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:pie3DChart\"])?this.parsePie((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:pie3DChart\"])):(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:doughnutChart\"])&&this.parseDoughnutChart((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"c:doughnutChart\"])),[3,3];case 2:return r.sent(),[3,3];case 3:return[2]}}))}))},o.prototype.parseAreaLine=function(t,e){this.parseLine(t,e),this.options.series=this.options.series.map((function(t){return t.areaStyle={},t}))},o.prototype.parseLine=function(t,e){var a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:ser\"]);Array.isArray(a)||(a=[a]),this.options.title={top:\"top\",left:\"center\",text:this.parseChartTitle((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"c:title\"]))},this.options.xAxis={type:\"category\",data:this.getCategory(a[0])},this.options.yAxis={type:\"value\"},this.options.series=this.parseLineSeries(a,t),this.options.color=this.parseLineColors(a),this.options.legend={bottom:\"bottom\",left:\"center\"},\"percentStacked\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:grouping\",\"attrs\",\"val\"])&&(this.options.tooltip.valueFormatter=function(t){return(100*t).toFixed(2)+\"%\"})},o.prototype.parseBar=function(t,e){var a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:ser\"]);Array.isArray(a)||(a=[a]),this.options.title={top:\"top\",left:\"center\",text:this.parseChartTitle((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"c:title\"]))},\"bar\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:barDir\",\"attrs\",\"val\"])?(this.options.yAxis={type:\"category\",data:this.getCategory(a[0])},this.options.xAxis={type:\"value\"}):(this.options.xAxis={type:\"category\",data:this.getCategory(a[0])},this.options.yAxis={type:\"value\"}),this.options.series=this.parseBarSeries(a,t),this.options.color=this.parseBarColors(a),this.options.legend={bottom:\"bottom\",left:\"center\"},\"percentStacked\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:grouping\",\"attrs\",\"val\"])&&(this.options.tooltip.valueFormatter=function(t){return(100*t).toFixed(2)+\"%\"})},o.prototype.parsePie=function(t){var e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:ser\"]);this.options.title={top:\"top\",left:\"center\",text:this.parsePieTitle(e)},this.options.color=this.parsePieColors(e),this.options.series=[this.parsePieSeries(e,t)],this.options.legend={bottom:\"bottom\",left:\"center\"}},o.prototype.parseDoughnutChart=function(t){var e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:ser\"]);this.options.title.text=this.parsePieTitle(e),this.options.color=this.parsePieColors(e),this.options.series=[this.parsePieSeries(e,t)],this.options.legend={bottom:\"bottom\",left:\"center\"}},o.prototype.parsePieTitle=function(t){return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:tx\",\"c:strRef\",\"c:strCache\",\"c:pt\",\"c:v\"])},o.prototype.parseChartTitle=function(t){var e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:tx\",\"c:rich\",\"a:p\"]);return Array.isArray(e)||(e=[e]),e.map((function(t){var e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:r\"]);return Array.isArray(e)||(e=[e]),e.map((function(t){return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:t\"])||\"\"})).join(\"\")})).join(\"\")||\"图表标题\"},o.prototype.parseBarColors=function(t){var e=this;return t.map((function(t){return S(g((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:spPr\",\"a:solidFill\"]),e.theme,e.ctx))}))},o.prototype.parseLineColors=function(t){var e=this;return t.map((function(t){return S(g((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:spPr\",\"a:ln\",\"a:solidFill\"])||(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:spPr\",\"a:solidFill\"]),e.theme,e.ctx))}))},o.prototype.parsePieColors=function(t){var e=this,a=[],r=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:dPt\"]);return Array.isArray(r)||(r=[r]),r.forEach((function(t){a.push(S(g((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:spPr\",\"a:solidFill\"]),e.theme,e.ctx)))})),a},o.prototype.parsePieSeries=function(t,e){var a={type:\"pie\",radius:\"80%\",startAngle:90,data:[]};(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"c:holeSize\",\"attrs\",\"val\"])&&(a.radius=[\"\".concat(.8*(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"c:holeSize\",\"attrs\",\"val\"]),\"%\"),\"80%\"]);var r=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"c:firstSliceAng\",\"attrs\",\"val\"]);r&&(a.startAngle=90-r);for(var n=this.getCategory(t),o=this.getVal(t),i=0;i<n.length;i++)a.data.push({name:n[i],value:o[i]});return a},o.prototype.parseBarSeries=function(t,e){var a,r=this,o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"c:grouping\",\"attrs\",\"val\"]);\"stacked\"===o?a=\"Ad\":\"percentStacked\"===o&&(a=\"total\");var i=t.map((function(t){return{type:\"bar\",name:(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:tx\",\"c:strRef\",\"c:strCache\",\"c:pt\",\"c:v\"]),data:r.getVal(t),stack:a}}));if(\"percentStacked\"===o){var s=[];i.forEach((function(t,e){s=0===e?(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__spreadArray)([],t.data,!0):s.map((function(e,a){return e+t.data[a]}))})),i.forEach((function(t){t.data=t.data.map((function(t,e){return s[e]<=0?0:t/s[e]}))}))}return i},o.prototype.parseLineSeries=function(t,e){var a,r=this,o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"c:grouping\",\"attrs\",\"val\"]);\"stacked\"===o?a=\"Ad\":\"percentStacked\"===o&&(a=\"total\");var i=t.map((function(t){return{type:\"line\",name:(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:tx\",\"c:strRef\",\"c:strCache\",\"c:pt\",\"c:v\"]),data:r.getVal(t),stack:a}}));if(\"total\"===a){var s=[];i.forEach((function(t,e){s=0===e?(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__spreadArray)([],t.data,!0):s.map((function(e,a){return e+t.data[a]}))})),i.forEach((function(t){t.data=t.data.map((function(t,e){return s[e]<=0?0:t/s[e]}))}))}return i},o.prototype.getCategory=function(t){if((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:cat\",\"c:strRef\"])){var e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:cat\",\"c:strRef\",\"c:strCache\",\"c:pt\"]);return Array.isArray(e)||(e=[e]),e.map((function(t){return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:v\"])}))}if((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:cat\",\"c:numRef\"])){e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:cat\",\"c:numRef\",\"c:numCache\",\"c:pt\"]);return Array.isArray(e)||(e=[e]),e.map((function(t){return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:v\"])}))}},o.prototype.getVal=function(t){var e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:val\",\"c:numRef\",\"c:numCache\",\"c:pt\"]);return Array.isArray(e)||(e=[e]),e.map((function(t){return+(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"c:v\"])}))},o}(C);function D(t){var e={},a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"attrs\")||{};return Object.keys(a).forEach((function(t){switch(t){case\"algn\":e.align=a[t];break;case\"marL\":e.marginLeft=f(parseInt(a[t]));break;case\"indent\":e.indent=f(parseInt(a[t]));break;case\"lvl\":e.level=a[t]}})),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"a:lnSpc\",\"a:spcPct\",\"attrs\",\"val\"])&&(e.lineHeight=parseInt(t[\"a:lnSpc\"][\"a:spcPct\"].attrs.val)/1e5),e}function F(t,e,a){var r={},n=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"attrs\")||{};Object.keys(n).forEach((function(t){switch(t){case\"sz\":r.size=parseInt(n[t])/100;break;case\"b\":r.bold=\"1\"===n[t];break;case\"i\":r.italic=\"1\"===n[t];break;case\"u\":r.underline=n[t];break;case\"strike\":r.strike=n[t];break;case\"order\":case\"dirty\":break;default:r[t]=n[t]}}));var o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,\"a:solidFill\");return o&&(r.color=g(o,e,a)),r}function Z(t,e,n,o,i){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var a,s,h,l,p,d,u,f,y,v,w,b,m,g,x,L,M,A,P,k,j;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(r){switch(r.label){case 0:for(h in s=[],a=e)s.push(h);l=0,r.label=1;case 1:if(!(l<s.length))return[3,17];if(!((h=s[l])in a))return[3,16];switch(p=h){case\"p:sp\":return[3,2];case\"p:pic\":return[3,3];case\"p:cxnSp\":return[3,4];case\"p:graphicFrame\":return[3,5];case\"p:grpSp\":return[3,14]}return[3,15];case 2:for(d=Array.isArray(e[p])?e[p]:[e[p]],k=0;k<d.length;k++)m=d[k],t.push(new R(m,n,o,i));return[3,16];case 3:for(u=Array.isArray(e[p])?e[p]:[e[p]],k=0;k<u.length;k++)f=u[k],y=f[\"p:blipFill\"][\"a:blip\"].attrs[\"r:embed\"],v=o.rels[y].target,w=new T(v,f,n,o,i),t.push(w);return[3,16];case 4:for(b=Array.isArray(e[p])?e[p]:[e[p]],k=0;k<b.length;k++)m=b[k],t.push(new R(m,n,o,i));return[3,16];case 5:g=Array.isArray(e[p])?e[p]:[e[p]],k=0,r.label=6;case 6:if(!(k<g.length))return[3,13];switch(x=g[k],L=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(x,[\"a:graphic\",\"a:graphicData\",\"attrs\",\"uri\"]),L){case\"http://schemas.openxmlformats.org/drawingml/2006/table\":return[3,7];case\"http://schemas.openxmlformats.org/drawingml/2006/diagram\":return[3,8];case\"http://schemas.openxmlformats.org/drawingml/2006/chart\":return[3,10]}return[3,12];case 7:return t.push(new _(x,n,o,i)),[3,12];case 8:return[4,(M=new E(x,n,o,i)).parseNode()];case 9:return r.sent(),t.push(M),[3,12];case 10:return[4,(A=new N(x,n,o,i)).parseNode()];case 11:return r.sent(),t.push(A),[3,12];case 12:return k++,[3,6];case 13:return[3,16];case 14:for(P=Array.isArray(e[p])?e[p]:[e[p]],k=0;k<P.length;k++)j=P[k],t.push(new O(j,n,o,i));return[3,16];case 15:return[3,16];case 16:return l++,[3,1];case 17:return[2]}}))}))}var G=function(){function t(t,e,a){this.slideType=\"slide\",this.rels={},this.background={type:\"none\"},this.nodes=[],this.name=t,this.source=e,this.pptx=a}return Object.defineProperty(t.prototype,\"index\",{get:function(){if(!this.name)return 0;var t=this.name.match(/(\\d+)/);return t?parseInt(t[0]):1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,\"slideMaster\",{get:function(){return this.slideLayout&&this.slideLayout.slideMaster},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,\"theme\",{get:function(){return this.slideMaster.theme},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,\"_relsPath\",{get:function(){return this.name.replace(\"slides/slide\",\"slides/_rels/slide\")+\".rels\"},enumerable:!1,configurable:!0}),t.prototype.load=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(t){switch(t.label){case 0:return[4,this._loadRels()];case 1:return t.sent(),this._loadBackground(),[4,this._loadNodes()];case 2:return t.sent(),[2]}}))}))},t.prototype._loadRels=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t,e,a,n=this;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(r){switch(r.label){case 0:return e=p,[4,this.pptx.getXmlByPath(this._relsPath)];case 1:return t=e.apply(void 0,[r.sent()]),a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"Relationships\",\"Relationship\"])||[],Array.isArray(a)||(a=[a]),a.forEach((function(t){switch((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"Type\"])){case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout\":var e=t.attrs.Target.replace(\"../\",\"ppt/\");e.startsWith(\"/ppt\")&&(e=e.substr(1)),n.slideLayout=n.pptx.getSlideLayout(e);break;case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image\":case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/audio\":case\"http://schemas.microsoft.com/office/2007/relationships/media\":case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/video\":case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/diagramLayout\":case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/diagramData\":case\"http://schemas.microsoft.com/office/2007/relationships/diagramDrawing\":case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/diagramColors\":case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart\":var a=t.attrs.Target.replace(\"../\",\"ppt/\");a.startsWith(\"/ppt\")&&(a=a.substr(1)),n.rels[t.attrs.Id]={type:t.attrs.Type.split(\"/\").pop(),target:a}}})),[2]}}))}))},t.prototype._loadBackground=function(){var t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:sld\",\"p:cSld\",\"p:bg\",\"p:bgPr\"]);t&&t[\"a:solidFill\"]?this.background=g(t[\"a:solidFill\"],this.theme,this):t&&t[\"a:gradFill\"]?this.background=L(t[\"a:gradFill\"],this.theme,this):t&&t[\"a:blipFill\"]&&(this.background=x(t[\"a:blipFill\"],this.pptx,this))},t.prototype._loadNodes=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(e){switch(e.label){case 0:return t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:sld\",\"p:cSld\",\"p:spTree\"]),[4,Z(this.nodes,t,this.pptx,this)];case 1:return e.sent(),[2]}}))}))},t.prototype.getColorThemeName=function(t){return this.slideLayout.getColorThemeName(t)},t.prototype.getNodeInheritAttrsByType=function(t,e){var a=this.slideLayout.getNodeByType(t),r=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(a.source,e);return r||this.slideLayout.getNodeInheritAttrsByType(t,e)},t.prototype.getNodeInheritAttrsByIdx=function(t,e){var a=this.slideLayout.getNodeByIdx(t),r=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(a.source,e);return r||this.slideLayout.getNodeInheritAttrsByIdx(t,e)},t}(),z=function(){function t(t,e,a){this.slideType=\"slideLayout\",this.rels={},this.background={type:\"none\"},this.nodes=[],this.name=t,this.source=e,this.pptx=a}return Object.defineProperty(t.prototype,\"_relsPath\",{get:function(){return this.name.replace(\"slideLayouts/slideLayout\",\"slideLayouts/_rels/slideLayout\")+\".rels\"},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,\"theme\",{get:function(){return this.slideMaster.theme},enumerable:!1,configurable:!0}),t.prototype.load=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(t){switch(t.label){case 0:return[4,this._loadRels()];case 1:return t.sent(),[4,this._loadBackground()];case 2:return t.sent(),[4,this._loadNodes()];case 3:return t.sent(),[2]}}))}))},t.prototype._loadRels=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t,e,a,n=this;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(r){switch(r.label){case 0:return e=p,[4,this.pptx.getXmlByPath(this._relsPath)];case 1:return t=e.apply(void 0,[r.sent()]),a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"Relationships\",\"Relationship\"])||[],Array.isArray(a)||(a=[a]),a.forEach((function(t){switch((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"Type\"])){case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster\":var e=t.attrs.Target.replace(\"../\",\"ppt/\");e.startsWith(\"/ppt\")&&(e=e.substr(1)),n.slideMaster=n.pptx.getSlideMaster(e);break;case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image\":case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/audio\":case\"http://schemas.microsoft.com/office/2007/relationships/media\":case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/video\":var a=t.attrs.Target.replace(\"../\",\"ppt/\");a.startsWith(\"/ppt\")&&(a=a.substr(1)),n.rels[t.attrs.Id]={type:t.attrs.Type.split(\"/\").pop(),target:a}}})),[2]}}))}))},t.prototype._loadBackground=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(e){return(t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:sldLayout\",\"p:cSld\",\"p:bg\",\"p:bgPr\"]))&&t[\"a:solidFill\"]?this.background=g(t[\"a:solidFill\"],this.theme):t&&t[\"a:gradFill\"]?this.background=L(t[\"a:gradFill\"],this.theme,this):t&&t[\"a:blipFill\"]&&(this.background=x(t[\"a:blipFill\"],this.pptx,this)),[2]}))}))},t.prototype._loadNodes=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(e){return t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:sldLayout\",\"p:cSld\",\"p:spTree\"]),Z(this.nodes,t,this.pptx,this),[2]}))}))},t.prototype.getColorThemeName=function(t){return this.slideMaster.getColorThemeName(t)},t.prototype.getNodeByType=function(t){return this.nodes.find((function(e){return e.type===t}))},t.prototype.getNodeByIdx=function(t){return this.nodes.find((function(e){return e.idx===t}))},t.prototype.getNodeInheritAttrsByType=function(t,e){var a=this.slideMaster.getNodeByType(t);return a&&(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(a.source,e)},t.prototype.getNodeInheritAttrsByIdx=function(t,e){var a=this.slideMaster.getNodeByIdx(t);return a&&(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(a.source,e)},t}(),W=function(){function t(t,e,a){this.slideType=\"slideMaster\",this.rels={},this.background={type:\"none\"},this.textStyles={titleStyle:{},bodyStyle:{},otherStyle:{}},this.defaultTextStyle={},this.nodes=[],this.tableStyles={},this.name=t,this.source=e,this.pptx=a,this.load()}return Object.defineProperty(t.prototype,\"_relsPath\",{get:function(){return this.name.replace(\"slideMasters/slideMaster\",\"slideMasters/_rels/slideMaster\")+\".rels\"},enumerable:!1,configurable:!0}),t.prototype.load=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(t){switch(t.label){case 0:return[4,this._parseRels()];case 1:return t.sent(),this._parseColorMap(),this._parseBackground(),this._parseTextStyles(),this._parseTableStyles(),this._parseDefaultTextStyle(),this._loadNodes(),[2]}}))}))},t.prototype._parseRels=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t,e,a,n=this;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(r){switch(r.label){case 0:return e=p,[4,this.pptx.getXmlByPath(this._relsPath)];case 1:return t=e.apply(void 0,[r.sent()]),a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"Relationships\",\"Relationship\"])||[],Array.isArray(a)||(a=[a]),a.forEach((function(t){switch((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t,[\"attrs\",\"Type\"])){case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme\":var e=t.attrs.Target.replace(\"../\",\"ppt/\");e.startsWith(\"/ppt\")&&(e=e.substr(1)),n.theme=n.pptx.getTheme(e);break;case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image\":case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/audio\":case\"http://schemas.microsoft.com/office/2007/relationships/media\":case\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/video\":var a=t.attrs.Target.replace(\"../\",\"ppt/\");a.startsWith(\"/ppt\")&&(a=a.substr(1)),n.rels[t.attrs.Id]={type:t.attrs.Type.split(\"/\").pop(),target:a}}})),[2]}}))}))},t.prototype._parseColorMap=function(){this.colorMap=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.omit)((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:sldMaster\",\"p:clrMap\",\"attrs\"])||{},[\"order\"])},t.prototype.getColorThemeName=function(t){return this.colorMap[t]||t},t.prototype._parseBackground=function(){var t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:sldMaster\",\"p:cSld\",\"p:bg\",\"p:bgPr\"]),e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:sldMaster\",\"p:cSld\",\"p:bg\",\"p:bgRef\"]);t&&t[\"a:solidFill\"]?this.background=g(t[\"a:solidFill\"],this.theme,this):t&&t[\"a:gradFill\"]?this.background=L(t[\"a:gradFill\"],this.theme,this):t&&t[\"a:blipFill\"]?this.background=x(t[\"a:blipFill\"],this.pptx,this):e&&(this.background=g(e,this.theme,this))},t.prototype._parseDefaultTextStyle=function(){var t=this,e=this.pptx.defaultTextStyleSource;Object.keys(e).forEach((function(a){if(a.startsWith(\"a:\")&&a.endsWith(\"pPr\")){var r=a.substr(2,a.length-5),n=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e[a],[\"a:defRPr\"]);t.defaultTextStyle[r]={props:D(e[a]),defRPr:F(n,t.theme,t)}}}))},t.prototype._parseTextStyles=function(){var t=this,e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:sldMaster\",\"p:txStyles\"]);[\"titleStyle\",\"bodyStyle\",\"otherStyle\"].forEach((function(a){var r=t.textStyles[a],n=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,\"p:\".concat(a))||{};Object.keys(n).forEach((function(e){if(e.startsWith(\"a:\")&&e.endsWith(\"pPr\")){var a=e.substr(2,e.length-5);r[a]={},r[a].props=D(n[e]);var o=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(n[e],[\"a:defRPr\"]);r[a].defRPr=F(o,t.theme,t)}}))}))},t.prototype._parseTableStyles=function(){var t=this,e={},a=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.pptx.tableStyles,[\"a:tblStyleLst\",\"a:tblStyle\"])||[];Array.isArray(a)||(a=[a]),a.forEach((function(a){var r=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(a,[\"attrs\",\"styleId\"]);e[r]={},Object.keys(a).forEach((function(n){if(n.startsWith(\"a:\")){var o=n.substr(2);e[r][o]={};var i=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(a[n],[\"a:tcStyle\"]);if(i){var s={};(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"a:fill\",\"a:solidFill\"])&&(s.background=g((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,[\"a:fill\",\"a:solidFill\"]),t.theme,t));var h=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(i,\"a:tcBdr\");h&&(s.border={},Object.keys(h).forEach((function(e){if(e.startsWith(\"a:\")){var a=e.substr(2),r=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(h[e],[\"a:ln\"]);s.border[a]=B(r,t.theme,t)}}))),e[r][o].tcStyle=s}var l=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(a[n],[\"a:tcTxStyle\"]);if(l){var p={};p.color=g(l,t.theme,t),\"on\"===(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(l,[\"attrs\",\"b\"])&&(p.bold=!0),e[r][o].tcTxStyle=p}}}))})),this.tableStyles=e},t.prototype._loadNodes=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(e){return t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"p:sldMaster\",\"p:cSld\",\"p:spTree\"]),Z(this.nodes,t,this.pptx,this),[2]}))}))},t.prototype.getNodeByType=function(t){return this.nodes.find((function(e){return e.type===t}))},t.prototype.getNodeByIdx=function(t){return this.nodes.find((function(e){return e.idx===t}))},t.prototype.getNodeInheritAttrsByType=function(t,e){},t.prototype.getNodeInheritAttrsByIdx=function(t,e){},t}(),H=function(){function t(t,e,a){this.clrScheme={},this.borderScheme=[],this.name=t,this.source=e,this.pptx=a,this._parseClrScheme(),this._parseLineStyleLst()}return t.prototype._parseClrScheme=function(){var t=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"a:theme\",\"a:themeElements\",\"a:clrScheme\"]);for(var e in t)if(e.startsWith(\"a:\")){var a=e.substring(2),r=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t[e],[\"a:sysClr\",\"attrs\",\"lastClr\"])||(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(t[e],[\"a:srgbClr\",\"attrs\",\"val\"]);this.clrScheme[a]=\"#\"+r}},t.prototype._parseLineStyleLst=function(){var t=this,e=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(this.source,[\"a:theme\",\"a:themeElements\",\"a:fmtScheme\",\"a:lnStyleLst\",\"a:ln\"])||[];this.borderScheme=e.map((function(e){var a={color:{}};return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"attrs\",\"w\"])&&(a.width=f(parseInt((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"attrs\",\"w\"])))),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"attrs\",\"algn\"])&&(a.algn=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"attrs\",\"algn\"])),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"attrs\",\"cap\"])&&(a.cap=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"attrs\",\"cap\"])),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"attrs\",\"cmpd\"])&&(a.cmpd=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"attrs\",\"cmpd\"])),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:miter\",\"attrs\",\"lim\"])&&(a.miterLim=f(parseInt((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:miter\",\"attrs\",\"lim\"])))),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:prstDash\",\"attrs\",\"val\"])&&(a.type=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:prstDash\",\"attrs\",\"val\"])),(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:solidFill\"])&&(a.color=g((0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"a:solidFill\"]),t)),a}))},t.prototype.getColor=function(t){if(\"phClr\"!==t)return this.clrScheme[t]||this.defaultColor},t.prototype.getLineStyle=function(t){return this.borderScheme[t-1]},t}();var Q=function(){function t(){this.slides=[],this.slideLayouts=[],this.slideMaster=[],this.themes=[],this.medias={},this.wps=!1}return t.prototype.load=function(t){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var e,a;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(r){switch(r.label){case 0:return e=new (jszip__WEBPACK_IMPORTED_MODULE_0___default()),a=this,[4,e.loadAsync(t)];case 1:return a._zipContents=r.sent(),[4,this._loadThumbnail()];case 2:return r.sent(),[4,this._loadMedia()];case 3:return r.sent(),[4,this._loadPresentation()];case 4:return r.sent(),[4,this._loadContentTypes()];case 5:return r.sent(),[2]}}))}))},t.prototype._loadThumbnail=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(e){switch(e.label){case 0:return e.trys.push([0,3,,4]),this._zipContents.files[\"docProps/thumbnail.jpeg\"]?[4,this._zipContents.files[\"docProps/thumbnail.jpeg\"].async(\"base64\")]:[3,2];case 1:t=e.sent(),this.thumbnail=\"data:image/jpeg;base64,\"+t,e.label=2;case 2:return[3,4];case 3:return e.sent(),[3,4];case 4:return[2]}}))}))},t.prototype._loadPresentation=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t,e,a,n,o,i;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(r){switch(r.label){case 0:return r.trys.push([0,4,,5]),[4,this._zipContents.files[\"ppt/presentation.xml\"].async(\"text\")];case 1:return t=r.sent(),e=p(t),a=e[\"p:presentation\"][\"p:sldSz\"].attrs,this.width=f(parseInt(a.cx)),this.height=f(parseInt(a.cy)),this.defaultTextStyleSource=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(e,[\"p:presentation\",\"p:defaultTextStyle\"]),this._zipContents.files[\"docProps/app.xml\"]?[4,this._zipContents.files[\"docProps/app.xml\"].async(\"text\")]:[3,3];case 2:n=r.sent(),o=p(n),i=(0,lodash__WEBPACK_IMPORTED_MODULE_1__.get)(o,[\"Properties\",\"Application\"]),this.wps=(null==i?void 0:i.includes(\"WPS\"))||!1,r.label=3;case 3:return[3,5];case 4:return r.sent(),[3,5];case 5:return[2]}}))}))},t.prototype._loadContentTypes=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t,e,a,n,o,c,i,s,h,l,d,u,f,y,v,w,b,m,g,x,L;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(r){switch(r.label){case 0:return r.trys.push([0,22,,23]),e=p,[4,this._zipContents.files[\"[Content_Types].xml\"].async(\"text\")];case 1:if(t=e.apply(void 0,[r.sent()]),a=t.Types.Override,!(n=a.filter((function(t){return\"application/vnd.openxmlformats-officedocument.theme+xml\"===t.attrs.ContentType}))))return[3,5];o=0,c=n,r.label=2;case 2:return o<c.length?(i=c[o],m=i.attrs.PartName.substr(1),s=p,[4,this._zipContents.files[m].async(\"text\")]):[3,5];case 3:g=s.apply(void 0,[r.sent()]),this.themes.push(new H(m,g,this)),r.label=4;case 4:return o++,[3,2];case 5:return(h=a.find((function(t){return\"application/vnd.openxmlformats-officedocument.presentationml.tableStyles+xml\"===t.attrs.ContentType})))?(m=h.attrs.PartName.substr(1),l=p,[4,this._zipContents.files[m].async(\"text\")]):[3,7];case 6:g=l.apply(void 0,[r.sent()]),this.tableStyles=g,r.label=7;case 7:d=a.filter((function(t){return\"application/vnd.openxmlformats-officedocument.presentationml.slideMaster+xml\"===t.attrs.ContentType})),b=0,r.label=8;case 8:return b<d.length?(m=d[b].attrs.PartName.substr(1),u=p,[4,this._zipContents.files[m].async(\"text\")]):[3,11];case 9:g=u.apply(void 0,[r.sent()]),this.slideMaster.push(new W(m,g,this)),r.label=10;case 10:return b++,[3,8];case 11:f=a.filter((function(t){return\"application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml\"===t.attrs.ContentType})),b=0,r.label=12;case 12:return b<f.length?(m=f[b].attrs.PartName.substr(1),y=p,[4,this._zipContents.files[m].async(\"text\")]):[3,16];case 13:return g=y.apply(void 0,[r.sent()]),[4,(v=new z(m,g,this)).load()];case 14:r.sent(),this.slideLayouts.push(v),r.label=15;case 15:return b++,[3,12];case 16:w=a.filter((function(t){return\"application/vnd.openxmlformats-officedocument.presentationml.slide+xml\"===t.attrs.ContentType})),b=0,r.label=17;case 17:return b<w.length?(m=w[b].attrs.PartName.substr(1),x=p,[4,this._zipContents.files[m].async(\"text\")]):[3,21];case 18:return g=x.apply(void 0,[r.sent()]),[4,(L=new G(m,g,this)).load()];case 19:r.sent(),this.slides.push(L),r.label=20;case 20:return b++,[3,17];case 21:return this.slides.sort((function(t,e){return t.index-e.index})),[3,23];case 22:return r.sent(),[3,23];case 23:return[2]}}))}))},t.prototype._loadMedia=function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){var t,e,a,n,o,c,i,s,h,l,p,d,u,f=this;return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(r){switch(r.label){case 0:t=Object.keys(this._zipContents.files).filter((function(t){return t.startsWith(\"ppt/media/image\")})).map((function(t){return f._zipContents.files[t]})),e=0,a=t,r.label=1;case 1:return e<a.length?(n=a[e],o=function(t){var e;switch(t){case\"jpg\":case\"jpeg\":e=\"image/jpeg\";break;case\"png\":e=\"image/png\";break;case\"gif\":e=\"image/gif\";break;case\"emf\":e=\"image/x-emf\";break;case\"wmf\":e=\"image/x-wmf\";break;default:e=\"image/*\"}return e}((y=n.name).substr(2+(~-y.lastIndexOf(\".\")>>>0))),[4,n.async(\"base64\")]):[3,4];case 2:c=r.sent(),this.medias[n.name]=\"data:\".concat(o,\";base64,\").concat(c),r.label=3;case 3:return e++,[3,1];case 4:i=Object.keys(this._zipContents.files).filter((function(t){return t.startsWith(\"ppt/media/media\")&&[\"mp3\",\"wav\",\"ogg\",\"mp4\",\"webm\"].includes(t.split(\".\").pop().toLowerCase())})).map((function(t){return f._zipContents.files[t]})),s=0,h=i,r.label=5;case 5:return s<h.length?(l=h[s],p=l.name.split(\".\").pop().toLowerCase(),[4,l.async(\"arraybuffer\")]):[3,8];case 6:d=r.sent(),u=new Blob([d],{type:\"\".concat([\"mp3\",\"wav\"].includes(p)?\"audio\":\"video\",\"/\").concat(p)}),this.medias[l.name]=URL.createObjectURL(u),r.label=7;case 7:return s++,[3,5];case 8:return[2]}var y}))}))},t.prototype.getXmlByPath=function(t){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__awaiter)(this,void 0,void 0,(function(){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__generator)(this,(function(e){switch(e.label){case 0:if(!this._zipContents.files[t])throw new Error(\"文件不存在\");return[4,this._zipContents.files[t].async(\"text\")];case 1:return[2,e.sent()]}}))}))},t.prototype.getSlideLayout=function(t){return this.slideLayouts.find((function(e){return e.name===t}))},t.prototype.getSlideMaster=function(t){return this.slideMaster.find((function(e){return e.name===t}))},t.prototype.getTheme=function(t){return this.themes.find((function(e){return e.name===t}))},t.prototype.getMedia=function(t){return this.medias[t]},t}();function V(t){var e=t.extend,a=t.offset,r=t.clip,n=t.base64,o=t.audioFile,c=t.videoFile,i=document.createElement(\"div\");i.style.setProperty(\"position\",\"absolute\"),i.style.setProperty(\"left\",a.x+\"px\"),i.style.setProperty(\"top\",a.y+\"px\");var s,h,l,p,d=document.createElement(\"div\");d.style.setProperty(\"position\",\"absolute\"),d.style.setProperty(\"left\",\"0\"),d.style.setProperty(\"top\",\"0\"),d.style.setProperty(\"width\",e.w+\"px\"),d.style.setProperty(\"height\",e.h+\"px\"),d.style.setProperty(\"overflow\",\"hidden\"),r?(s=e.w/(1-(void 0===r.l?0:r.l)-(void 0===r.r?0:r.r)),h=e.h/(1-(void 0===r.t?0:r.t)-(void 0===r.b?0:r.b)),l=-1*s*(void 0===r.l?0:r.l),p=-1*h*(void 0===r.t?0:r.t)):(s=e.w,h=e.h,l=0);var u=document.createElement(\"img\");if(u.src=n,u.width=s,u.height=h,u.style.setProperty(\"position\",\"absolute\"),u.style.setProperty(\"left\",l+\"px\"),u.style.setProperty(\"top\",p+\"px\"),d.append(u),i.append(d),o){var f=document.createElement(\"audio\");f.style.position=\"absolute\",f.style.left=\"0\",f.style.top=\"0\",f.src=o,f.controls=!0,f.style.transform=\"translate(-50%)\",i.append(f)}if(c){var y=document.createElement(\"video\");y.style.position=\"absolute\",y.style.left=\"0\",y.style.top=\"0\",y.width=e.w,y.height=e.h,y.src=c,y.controls=!0,i.append(y)}return i}function X(e,a,r){var n,o=e.props,c=e.text,i=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},a),o),s=document.createElement(\"span\");s.innerHTML=\"string\"==typeof c?c:\"\";var h=18;i.size&&((null===(n=null==r?void 0:r.normAutofit)||void 0===n?void 0:n.fontScale)?(h=i.size*r.normAutofit.fontScale,s.style.fontSize=h+\"px\"):(h=i.size,s.style.fontSize=h+\"px\"));var l=S(i.color);l&&(s.style.color=l);var p=/^[^\\u4e00-\\u9fff]+$/;if(i.typeface)switch(s.style.fontFamily=i.typeface,i.typeface){case\"DengXian\":p.test(c)&&(s.style.letterSpacing=-.04*h+\"px\");break;case\"DengXian Light\":p.test(c)&&(s.style.letterSpacing=-.05*h+\"px\");break;case\"STLiti\":case\"SimSun\":case\"NSimSun\":case\"SimHei\":p.test(c)&&(s.style.fontSize=.85*parseInt(s.style.fontSize)+\"px\");break;case\"华文中宋\":case\"Fira Sans Extra Condensed Medium\":s.style.fontSize=.85*parseInt(s.style.fontSize)+\"px\";break;case\"FangSong\":s.style.letterSpacing=-.08*h+\"px\"}else p.test(c)&&(s.style.letterSpacing=-.04*h+\"px\");return i.bold&&(s.style.fontWeight=\"bold\"),i.italic&&(s.style.fontStyle=\"italic\"),i.underline&&\"none\"!==i.underline&&(s.style.textDecoration=\"underline\"),i.background&&(s.style.backgroundColor=S(i.background)),s.style.wordBreak=\"break-word\",s}function q(t,e,a){var r=document.createElement(\"span\"),n=t.firstElementChild;switch(r.style.fontSize=n.style.fontSize,r.style.color=n.style.color,r.style.fontWeight=n.style.fontWeight,r.style.fontStyle=n.style.fontStyle,r.style.marginRight=\"10px\",e.buAutoNum){case\"arabicPeriod\":default:r.textContent=a+\".\";break;case\"circleNumDbPlain\":r.textContent=[\"①\",\"②\",\"③\",\"④\",\"⑤\",\"⑥\",\"⑦\",\"⑧\",\"⑨\",\"⑩\",\"⑪\",\"⑫\",\"⑬\",\"⑭\",\"⑮\",\"⑯\",\"⑰\",\"⑱\",\"⑲\",\"⑳\"][a-1]||a+\"\";break;case\"romanUcPeriod\":r.textContent=function(t){var e=[{value:1e3,numeral:\"M\"},{value:900,numeral:\"CM\"},{value:500,numeral:\"D\"},{value:400,numeral:\"CD\"},{value:100,numeral:\"C\"},{value:90,numeral:\"XC\"},{value:50,numeral:\"L\"},{value:40,numeral:\"XL\"},{value:10,numeral:\"X\"},{value:9,numeral:\"IX\"},{value:5,numeral:\"V\"},{value:4,numeral:\"IV\"},{value:1,numeral:\"I\"}];if(\"number\"!=typeof t||t<1||t>3999)throw new Error(\"Input must be a number between 1 and 3999.\");for(var a=\"\",r=0;r<e.length;r++)for(;t>=e[r].value;)a+=e[r].numeral,t-=e[r].value;return a}(a)+\".\";break;case\"alphaUcPeriod\":r.textContent=m(a)+\".\";break;case\"alphaLcPeriod\":r.textContent=m(a).toLowerCase()+\".\";break;case\"alphaLcParenR\":r.textContent=m(a).toLowerCase()+\")\";break;case\"ea1JpnChsDbPeriod\":r.textContent=function(t){var e=[\"〇\",\"一\",\"二\",\"三\",\"四\",\"五\",\"六\",\"七\",\"八\",\"九\"];if(!Number.isInteger(t)||t<0)return\"\";for(var a=\"\",r=t.toString(),n=0;n<r.length;n++)a+=e[parseInt(r[n],10)];return a}(a)+\".\"}t.prepend(r)}function U(e,a,r){var n,o,c,i;void 0===a&&(a=0),void 0===r&&(r={});var s=e.inheritProps,h=e.inheritRProps,l=e.props,p=e.rows,d=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},s),l),u=function(){for(var t,e,a=0,n=0,o=p;n<o.length;n++){var c=o[n];c.props&&c.props.size&&(a=Math.max(a,c.props.size))}var i=(null===(e=null===(t=null==r?void 0:r.bodyProps)||void 0===t?void 0:t.normAutofit)||void 0===e?void 0:e.fontScale)||1;return(a||h.size||18)*i},f=document.createElement(\"div\"),y=r.isFirst?0:d.spaceBefore||0,v=r.isLast?0:d.spaceAfter||0;f.style.margin=\"\".concat(Math.floor(.2*u()),\"px  0 0 0\"),f.style.padding=\"\".concat(Math.floor(y),\"px 0px \").concat(Math.floor(v),\"px 0px\");var w=document.createElement(\"p\");w.style.margin=\"0\",w.style.padding=\"0px\",w.style.wordBreak=\"break-word\";w.style.textAlign=d.align&&{ctr:\"center\",l:\"left\",r:\"right\",dist:\"justify\"}[d.align]||\"center\",\"dist\"===d.align&&(w.style.textAlignLast=\"justify\");var b=d.hasOwnProperty(\"lineHeight\")?d.lineHeight:1;if((null===(o=null===(n=r.bodyProps)||void 0===n?void 0:n.normAutofit)||void 0===o?void 0:o.lnSpcReduction)&&(b*=1-(null===(i=null===(c=r.bodyProps)||void 0===c?void 0:c.normAutofit)||void 0===i?void 0:i.lnSpcReduction)),w.style.lineHeight=b+\"\",w.style.fontSize=u()+\"px\",p.length){for(var m=0,g=p;m<g.length;m++){var x=g[m];x.isBr?w.appendChild(document.createElement(\"br\")):w.appendChild(X(x,(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},h),{marginTop:Math.floor(.2*u())}),r.bodyProps))}d.buAutoNum?q(w,d,a):d.buChar&&function(t,e){var a=document.createElement(\"span\"),r=t.firstElementChild;a.style.color=r.style.color,a.style.fontSize=r.style.fontSize,a.textContent={n:\"■\",l:\"●\",u:\"◆\",p:\"□\",\"ü\":\"✔\",\"Ø\":\"➢\",\"•\":\"•\"}[e.buChar]||\"■\",a.style.marginRight=\"10px\",t.prepend(a)}(w,d),w.style.paddingLeft=(d.marginLeft||0)+(d.indent||0)+\"px\"}else{var L=document.createElement(\"span\");L.innerHTML=\"&nbsp;\",L.style.fontSize=h.size+\"px\",w.appendChild(L)}return f.appendChild(w),f}var Y=\"http://www.w3.org/2000/svg\";function J(t){return document.createElementNS(Y,t)}function K(t){var e=t.extend;return.16667*Math.min(e.w,e.h)}function $(t,e,a){void 0===a&&(a=0);var r=e.prstGeom,n=((null==r?void 0:r.gd)||[]).find((function(e){return e.name===t}));return n?n.fmla:a}function tt(t,e,a){void 0===a&&(a=0);var r=e.extend,n=e.prstGeom,o=((null==n?void 0:n.gd)||[]).find((function(e){return e.name===t}));return o?Math.min(r.w,r.h)*o.fmla:a}function et(t,e,a){void 0===a&&(a=0);var r=e.extend,n=e.prstGeom,o=((null==n?void 0:n.gd)||[]).find((function(e){return e.name===t}));return o?Math.max(r.w,r.h)*o.fmla:a}function at(t,e){var a=0;switch(t){case\"sm\":a=1*e;break;case\"med\":a=1.5*e;break;case\"lg\":a=2.5*e}return Math.max(a,2)}function rt(t,e,a,r){void 0===r&&(r=!1);var n=t.border,o=void 0===n?{}:n,c=t.uuid,i=o.headEnd,s=o.width,h=o.color,l=o.tailEnd,p=r?i:l,d=p.len,u=void 0===d?\"med\":d,f=p.w,y=void 0===f?\"med\":f,v=at(u,s),w=at(y,s),b=J(\"defs\"),m=J(\"marker\"),g=\"marker-\".concat(c,\"-\").concat(r?\"start\":\"end\");m.setAttribute(\"id\",g),m.setAttribute(\"viewBox\",\"0 0 \".concat(2*v,\" \").concat(2*w)),m.setAttribute(\"refX\",v+\"px\"),m.setAttribute(\"refY\",w+\"px\"),m.setAttribute(\"markerWidth\",2*v+\"px\"),m.setAttribute(\"markerHeight\",2*w+\"px\"),m.setAttribute(\"orient\",\"auto\"),m.setAttribute(\"markerUnits\",\"userSpaceOnUse\");var x=J(\"ellipse\");x.setAttribute(\"cx\",v+\"px\"),x.setAttribute(\"cy\",w+\"px\"),x.setAttribute(\"rx\",v+\"px\"),x.setAttribute(\"ry\",w+\"px\"),x.setAttribute(\"fill\",S(h)||\"transparent\"),m.appendChild(x),b.appendChild(m),e.appendChild(b),a.setAttribute(r?\"marker-start\":\"marker-end\",\"url(#\".concat(g,\")\"))}function nt(t,e,a,r){void 0===r&&(r=!1);var n=t.border,o=void 0===n?{}:n,c=t.uuid,i=o.headEnd,s=o.width,h=o.color,l=o.tailEnd,p=r?i:l,d=p.len,u=void 0===d?\"med\":d,f=p.w,y=void 0===f?\"med\":f,v=at(u,s),w=at(y,s),b=J(\"defs\"),m=J(\"marker\"),g=\"marker-\".concat(c,\"-\").concat(r?\"start\":\"end\");m.setAttribute(\"id\",g),m.setAttribute(\"viewBox\",\"0 0 \".concat(2*v,\" \").concat(2*w)),m.setAttribute(\"refX\",(r?.9*v:1.1*v)+\"px\"),m.setAttribute(\"refY\",w+\"px\"),m.setAttribute(\"markerWidth\",2*v+\"px\"),m.setAttribute(\"markerHeight\",2*w+\"px\"),m.setAttribute(\"orient\",\"auto\"),m.setAttribute(\"markerUnits\",\"userSpaceOnUse\");var x=J(\"path\"),L=r?[\"M \".concat(2*v,\",0\"),\"L 0,\".concat(w),\"L \".concat(2*v,\",\").concat(2*w),\"Z\"].join(\" \"):[\"M 0,0\",\"L \".concat(2*v,\",\").concat(w),\"L 0,\".concat(2*w),\"Z\"].join(\" \");x.setAttribute(\"d\",L),x.setAttribute(\"fill\",S(h)||\"transparent\"),m.appendChild(x),b.appendChild(m),e.appendChild(b),a.setAttribute(r?\"marker-start\":\"marker-end\",\"url(#\".concat(g,\")\"))}function ot(t,e,a,r){void 0===r&&(r=!1);var n=t.border,o=void 0===n?{}:n,c=t.uuid,i=o.headEnd,s=o.width,h=o.color,l=o.tailEnd,p=r?i:l,d=p.len,u=void 0===d?\"med\":d,f=p.w,y=void 0===f?\"med\":f,v=at(u,s),w=at(y,s),b=J(\"defs\"),m=J(\"marker\"),g=\"marker-\".concat(c,\"-\").concat(r?\"start\":\"end\");m.setAttribute(\"id\",g),m.setAttribute(\"viewBox\",\"0 0 \".concat(2*v,\" \").concat(2*w)),m.setAttribute(\"refX\",v+\"px\"),m.setAttribute(\"refY\",w+\"px\"),m.setAttribute(\"markerWidth\",2*v+\"px\"),m.setAttribute(\"markerHeight\",2*w+\"px\"),m.setAttribute(\"orient\",\"auto\"),m.setAttribute(\"markerUnits\",\"userSpaceOnUse\");var x=J(\"path\"),L=[\"M 0,\".concat(w),\"L \".concat(v,\",0\"),\"L \".concat(2*v,\",\").concat(w),\"L \".concat(v,\",\").concat(2*w),\"Z\"].join(\" \");x.setAttribute(\"d\",L),x.setAttribute(\"fill\",S(h)||\"transparent\"),m.appendChild(x),b.appendChild(m),e.appendChild(b),a.setAttribute(r?\"marker-start\":\"marker-end\",\"url(#\".concat(g,\")\"))}function ct(t,e,a,r){void 0===r&&(r=!1);var n=t.border,o=void 0===n?{}:n,c=t.uuid,i=o.headEnd,s=o.width,h=o.color,l=o.tailEnd,p=r?i:l,d=p.len,u=void 0===d?\"med\":d,f=p.w,y=void 0===f?\"med\":f,v=at(u,s),w=at(y,s),b=J(\"defs\"),m=J(\"marker\"),g=\"marker-\".concat(c,\"-\").concat(r?\"start\":\"end\");m.setAttribute(\"id\",g),m.setAttribute(\"viewBox\",\"0 0 \".concat(2*v+2*s,\" \").concat(2*w+2*s));var x=r?\"lg\"===y?2*s:3*s:\"lg\"===y?2*v:2*v-s;m.setAttribute(\"refX\",x+\"px\"),m.setAttribute(\"refY\",w+s+\"px\"),m.setAttribute(\"markerWidth\",2*v+\"px\"),m.setAttribute(\"markerHeight\",2*w+\"px\"),m.setAttribute(\"orient\",\"auto\"),m.setAttribute(\"markerUnits\",\"userSpaceOnUse\");var L=J(\"path\"),M=r?[\"M \".concat(2*v+s,\", \").concat(s),\"L \".concat(s,\",\").concat(w+s),\"L \".concat(2*v+s,\",\").concat(2*w+s)].join(\" \"):[\"M \".concat(s,\", \").concat(s),\"L \".concat(2*v+s,\",\").concat(w+s),\"L \".concat(s,\",\").concat(2*w+s)].join(\" \");L.setAttribute(\"d\",M),L.setAttribute(\"stroke-width\",s+\"px\"),L.setAttribute(\"stroke\",S(h)||\"transparent\"),L.setAttribute(\"fill\",\"transparent\"),L.setAttribute(\"stroke-linecap\",\"round\"),L.setAttribute(\"stroke-linejoin\",\"miter\"),L.style.overflow=\"visible\",m.appendChild(L),b.appendChild(m),e.appendChild(b),a.setAttribute(r?\"marker-start\":\"marker-end\",\"url(#\".concat(g,\")\"))}function it(t,e,a,r){void 0===r&&(r=!1);var n=t.border,o=void 0===n?{}:n,c=t.uuid,i=o.headEnd,s=o.width,h=o.color,l=o.tailEnd,p=r?i:l,d=p.len,u=void 0===d?\"med\":d,f=p.w,y=void 0===f?\"med\":f,v=at(u,s),w=at(y,s),b=J(\"defs\"),m=J(\"marker\"),g=\"marker-\".concat(c,\"-\").concat(r?\"start\":\"end\");m.setAttribute(\"id\",g),m.setAttribute(\"viewBox\",\"0 0 \".concat(2*v,\" \").concat(2*w));var x=r?\"sm\"===y?1.5*s:2*s:\"sm\"===y?2*v-1.5*s:2*v-2*s;m.setAttribute(\"refX\",x+\"px\"),m.setAttribute(\"refY\",w+\"px\"),m.setAttribute(\"markerWidth\",2*v+\"px\"),m.setAttribute(\"markerHeight\",2*w+\"px\"),m.setAttribute(\"orient\",\"auto\"),m.setAttribute(\"markerUnits\",\"userSpaceOnUse\");var L=J(\"path\"),M=r?[\"M 0, \".concat(w),\"L \".concat(2*v,\",0\"),\"L \".concat(v,\",\").concat(w),\"L \".concat(2*v,\",\").concat(2*w),\"Z\"].join(\" \"):[\"M 0,0\",\"L \".concat(2*v,\",\").concat(w),\"L 0,\".concat(2*w),\"L \".concat(v,\",\").concat(w),\"Z\"].join(\" \");L.setAttribute(\"d\",M),L.setAttribute(\"fill\",S(h)||\"transparent\"),L.style.overflow=\"visible\",m.appendChild(L),b.appendChild(m),e.appendChild(b),a.setAttribute(r?\"marker-start\":\"marker-end\",\"url(#\".concat(g,\")\"))}function st(t,e,a){var r=t.border,n=void 0===r?{}:r;t.extend;var o=n.headEnd,c=n.tailEnd;if(o&&\"none\"!==o.type)switch(o.type){case\"triangle\":nt(t,e,a,!0);break;case\"oval\":rt(t,e,a,!0);break;case\"diamond\":ot(t,e,a,!0);break;case\"arrow\":ct(t,e,a,!0);break;case\"stealth\":it(t,e,a,!0)}if(c&&\"none\"!==c.type)switch(c.type){case\"triangle\":nt(t,e,a,!1);break;case\"oval\":rt(t,e,a,!1);break;case\"diamond\":ot(t,e,a,!1);break;case\"arrow\":ct(t,e,a,!1);break;case\"stealth\":it(t,e,a,!1)}}function ht(t){var e=t.extend,a=J(\"rect\");return a.setAttribute(\"x\",\"0\"),a.setAttribute(\"y\",\"0\"),a.setAttribute(\"width\",e.w+\"px\"),a.setAttribute(\"height\",e.h+\"px\"),a}function lt(t,e,a,r,n){var o=t*Math.PI/180;return[0===o||o===2*Math.PI?e+r:o===Math.PI?e-r:o===Math.PI/2||o===3*Math.PI/2?e:o>0&&o<Math.PI/2||o>3*Math.PI/2&&o<2*Math.PI?e+Math.sqrt(1/(1/Math.pow(r,2)+Math.pow(Math.tan(o),2)/Math.pow(n,2))):e-Math.sqrt(1/(1/Math.pow(r,2)+Math.pow(Math.tan(o),2)/Math.pow(n,2))),0===o||o===2*Math.PI||o===Math.PI?a:o===Math.PI/2?a+n:o===3*Math.PI/2?a-n:o>Math.PI&&o<2*Math.PI?a-Math.sqrt(1/(1/Math.pow(n,2)+Math.pow(1/Math.tan(o),2)/Math.pow(r,2))):a+Math.sqrt(1/(1/Math.pow(n,2)+Math.pow(1/Math.tan(o),2)/Math.pow(r,2)))]}function pt(t,e){var a=0;return(e>t&&e-t>180||e<t&&t-e<180)&&(a=1),a}function dt(t){var e=t.extend,a=.146*e.w,r=.146*e.h;return{top:r,bottom:r,left:a,right:a,w:e.w-2*a,h:e.h-2*r}}function ut(e){var a=e.extend,r=e.offset,n=e.border,o=e.background,c=e.rotate,i=e.flipH,s=e.flipV,h=document.createElement(\"div\"),l=r.x,p=r.y;h.className=\"shape-wrapper shape-\".concat(e.shape),h.style.setProperty(\"position\",\"absolute\"),h.style.setProperty(\"width\",(a.w||1)+\"px\"),h.style.setProperty(\"height\",(a.h||1)+\"px\"),h.style.setProperty(\"left\",l+\"px\"),h.style.setProperty(\"top\",p+\"px\");var d,u=document.createElementNS(Y,\"svg\");u.style.setProperty(\"position\",\"absolute\"),u.setAttribute(\"width\",\"100%\"),u.setAttribute(\"height\",\"100%\"),u.style.setProperty(\"left\",\"0\"),u.style.setProperty(\"top\",\"0\"),u.style.overflow=\"visible\";var f={left:0,top:0,bottom:0,w:a.w,h:a.h},y=!0;switch(e.shape){case\"customGeom\":d=function(t){var e=t.prstGeom,a=void 0===e?{}:e,r=t.extend,n=a.pathList,o=a.w,c=a.h,i=J(\"path\"),s={moveTo:\"M\",lnTo:\"L\",cubicBezTo:\"C\",close:\"Z\"},h=r.w/o,l=r.h/c,p=n.map((function(t){var e=s[t.type],a=Array.isArray(t.points)?t.points.map((function(t){return\"\".concat(t[0]*h,\",\").concat(t[1]*l)})).join(\" \"):\"\";return a?\"\".concat(e,\" \").concat(a):\"\".concat(e)})).join(\" \");return i.setAttribute(\"d\",p),i.style.fillRule=\"evenodd\",i}(e),st(e,u,d);break;case\"flowChartProcess\":case\"rect\":d=ht(e);break;case\"snip1Rect\":d=function(t){var e=t.extend,a=J(\"polygon\"),r=tt(\"adj\",t,K(t)),n=[\"0,0\",\"\".concat(e.w-r,\",0\"),\"\".concat(e.w,\",\").concat(r),\"\".concat(e.w,\",\").concat(e.h),\"0,\".concat(e.h)].join(\" \");return a.setAttribute(\"points\",n),a}(e);break;case\"snip2SameRect\":d=function(t){var e=t.extend,a=J(\"polygon\"),r=tt(\"adj1\",t,K(t)),n=tt(\"adj2\",t,0),o=[[r,0],[e.w-r,0],[e.w,r],[e.w,e.h-n],[e.w-n,e.h],[n,e.h],[0,e.h-n],[0,r]].map((function(t){return\"\".concat(t[0],\",\").concat(t[1])})).join(\" \");return a.setAttribute(\"points\",o),a}(e);break;case\"snip2DiagRect\":d=function(t){var e=t.extend,a=J(\"polygon\"),r=tt(\"adj1\",t,0),n=tt(\"adj2\",t,K(t)),o=[[r,0],[e.w-n,0],[e.w,n],[e.w,e.h-r],[e.w-r,e.h],[n,e.h],[0,e.h-n],[0,r]].map((function(t){return\"\".concat(t[0],\",\").concat(t[1])})).join(\" \");return a.setAttribute(\"points\",o),a}(e);break;case\"snipRoundRect\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj1\",t,K(t)),n=tt(\"adj2\",t,K(t)),o=[\"M \".concat(r,\",0\"),\"L \".concat(e.w-n,\",0\"),\"L \".concat(e.w,\",\").concat(n),\"L \".concat(e.w,\",\").concat(e.h),\"L 0,\".concat(e.h),\"L 0,\".concat(r),\"Q 0,0 \".concat(r,\",0\"),\"Z\"].join(\" \");return a.setAttribute(\"d\",o),a}(e);break;case\"roundRect\":d=function(t){var e=ht(t),a=tt(\"adj\",t,K(t));return e.setAttribute(\"rx\",a+\"px\"),e.setAttribute(\"ry\",a+\"px\"),e}(e);break;case\"round1Rect\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj\",t,K(t)),n=[\"M 0,0\",\"L \".concat(e.w-r,\",0\"),\"Q \".concat(e.w,\",0 \").concat(e.w,\",\").concat(r),\"L \".concat(e.w,\",\").concat(e.h),\"L 0,\".concat(e.h),\"Z\"].join(\" \");return a.setAttribute(\"d\",n),a}(e);break;case\"round2SameRect\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj1\",t,K(t)),n=tt(\"adj2\",t,0),o=[\"M \".concat(r,\",0\"),\"L \".concat(e.w-r,\",0\"),\"Q \".concat(e.w,\",0 \").concat(e.w,\",\").concat(r),\"L \".concat(e.w,\",\").concat(e.h-n),\"Q \".concat(e.w,\",\").concat(e.h,\" \").concat(e.w-n,\",\").concat(e.h),\"L \".concat(n,\",\").concat(e.h),\"Q 0,\".concat(e.h,\" 0,\").concat(e.h-n),\"L 0,\".concat(r),\"Q 0,0 \".concat(r,\",0\"),\"Z\"].join(\" \");return a.setAttribute(\"d\",o),a}(e);break;case\"round2DiagRect\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj1\",t,K(t)),n=tt(\"adj2\",t,0),o=[\"M \".concat(r,\",0\"),\"L \".concat(e.w-n,\",0\"),\"Q \".concat(e.w,\",0 \").concat(e.w,\",\").concat(n),\"L \".concat(e.w,\",\").concat(e.h-r),\"Q \".concat(e.w,\",\").concat(e.h,\" \").concat(e.w-r,\",\").concat(e.h),\"L \".concat(n,\",\").concat(e.h),\"Q 0,\".concat(e.h,\" 0,\").concat(e.h-n),\"L 0,\".concat(r),\"Q 0,0 \".concat(r,\",0\"),\"Z\"].join(\" \");return a.setAttribute(\"d\",o),a}(e);break;case\"triangle\":d=function(t){var e=t.extend,a=J(\"polygon\"),r=[\"\".concat(e.w/2,\",0\"),\"0,\".concat(e.h),\"\".concat(e.w,\",\").concat(e.h)].join(\" \");return a.setAttribute(\"points\",r),a}(e);break;case\"rtTriangle\":d=function(t){var e=t.extend,a=J(\"polygon\"),r=[\"0,\".concat(e.h),\"0,0\",\"\".concat(e.w,\",\").concat(e.h)].join(\" \");return a.setAttribute(\"points\",r),a}(e);break;case\"ellipse\":d=function(t){var e=t.extend,a=J(\"ellipse\"),r=e.w/2,n=e.h/2,o=r,c=n;return a.setAttribute(\"cx\",r+\"px\"),a.setAttribute(\"cy\",n+\"px\"),a.setAttribute(\"rx\",o+\"px\"),a.setAttribute(\"ry\",c+\"px\"),a}(e);break;case\"line\":d=function(t){var e=t.extend,a=J(\"path\"),r=[\"M 0,0\",\"L \".concat(e.w,\",\").concat(e.h)].join(\" \");return a.setAttribute(\"d\",r),a}(e),st(e,u,d),y=!1;break;case\"straightConnector1\":d=function(t){var e=t.extend,a=J(\"path\"),r=[\"M 0,0\",\"L \".concat(e.w,\",\").concat(e.h)].join(\" \");return a.setAttribute(\"d\",r),a}(e),st(e,u,d),y=!1;break;case\"bentConnector3\":d=function(t){var e=t.extend,a=J(\"path\"),r=et(\"adj1\",t,.5*Math.max(e.w,e.h)),n=[\"M 0,0\",\"L \".concat(r,\",0\"),\"L \".concat(r,\",\").concat(e.h),\"L \".concat(e.w,\",\").concat(e.h)].join(\" \");return a.setAttribute(\"d\",n),a}(e),st(e,u,d),y=!1;break;case\"curvedConnector3\":d=function(t){var e=t.extend,a=J(\"path\"),r=et(\"adj1\",t,.5*Math.max(e.w,e.h)),n=[\"M0,0\",\"Q\".concat(r,\",0 \").concat(r,\",\").concat(e.h/2),\"T\".concat(e.w,\",\").concat(e.h)].join(\" \");return a.setAttribute(\"d\",n),a}(e),st(e,u,d),y=!1;break;case\"parallelogram\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj\",t,.5*Math.min(e.w,e.h)),n=[\"M\".concat(r,\",0\"),\"L\".concat(e.w,\",0\"),\"L\".concat(e.w-r,\",\").concat(e.h),\"L0,\".concat(e.h),\"Z\"].join(\" \");return a.setAttribute(\"d\",n),a}(e),f=function(t){var e=t.extend,a=tt(\"adj\",t,.5*Math.min(e.w,e.h)),r=.84*(e.w-a),n=.08*e.h+a/e.w*e.h*.42;return{top:n,bottom:n,left:(e.w-r)/2,right:(e.w-r)/2,w:r,h:e.h-2*n}}(e);break;case\"trapezoid\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj\",t,.25*Math.min(e.w,e.h)),n=[\"M\".concat(r,\",0\"),\"L\".concat(e.w-r,\",0\"),\"L\".concat(e.w,\",\").concat(e.h),\"L0,\".concat(e.h),\"Z\"].join(\" \");return a.setAttribute(\"d\",n),a}(e),f=function(t){var e=t.extend,a=tt(\"adj\",t,.5*Math.min(e.w,e.h)),r=a/e.w*.66*e.h,n=.66*a;return{top:r,bottom:0,left:n,right:n,w:e.w-2*n,h:e.h-r}}(e);break;case\"diamond\":d=function(t){var e=t.extend,a=J(\"path\"),r=[\"M\".concat(e.w/2,\",0\"),\"L\".concat(e.w,\",\").concat(e.h/2),\"L\".concat(e.w/2,\",\").concat(e.h),\"L0,\".concat(e.h/2),\"Z\"].join(\" \");return a.setAttribute(\"d\",r),a}(e),f=function(t){var e=t.extend,a=.25*e.h,r=.25*e.w;return{top:a,bottom:a,left:r,right:r,w:.5*e.w,h:.5*e.h}}(e);break;case\"pentagon\":d=function(t){var e=t.extend,a=J(\"path\"),r=[\"M\".concat(e.w/2,\",0\"),\"L\".concat(e.w,\",\").concat(.3771*e.h),\"L\".concat(.808*e.w,\",\").concat(e.h),\"L\".concat(.192*e.w,\",\").concat(e.h),\"L0,\".concat(.3771*e.h),\"Z\"].join(\" \");return a.setAttribute(\"d\",r),a}(e),f=function(t){var e=t.extend,a=.227*e.h,r=.192*e.w;return{top:a,bottom:0,left:r,right:r,w:.616*e.w,h:.773*e.h}}(e);break;case\"hexagon\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj\",t,.25*Math.min(e.w,e.h)),n=[\"M\".concat(r,\",0\"),\"L\".concat(e.w-r,\",0\"),\"L\".concat(e.w,\",\").concat(e.h/2),\"L\".concat(e.w-r,\",\").concat(e.h),\"L\".concat(r,\",\").concat(e.h),\"L0,\".concat(e.h/2),\"Z\"].join(\" \");return a.setAttribute(\"d\",n),a}(e),f=function(t){var e=t.extend,a=tt(\"adj\",t,.25*Math.min(e.w,e.h)),r=.098*e.h+a/e.w*.38*e.h,n=.088*e.w+.422*a;return{top:r,bottom:r,left:n,right:n,w:e.w-2*n,h:e.h-2*r}}(e);break;case\"heptagon\":d=function(t){var e=t.extend,a=J(\"path\"),r=[\"M\".concat(e.w/2,\",0\"),\"L\".concat(.9*e.w,\",\").concat(.2*e.h),\"L\".concat(e.w,\",\").concat(.642*e.h),\"L\".concat(.722*e.w,\",\").concat(e.h),\"L\".concat(.278*e.w,\",\").concat(e.h),\"L0,\".concat(.642*e.h),\"L\".concat(.1*e.w,\",\").concat(.2*e.h),\"Z\"].join(\" \");return a.setAttribute(\"d\",r),a}(e),f=function(t){var e=t.extend;return{top:.2*e.h,bottom:.2*e.h,left:.1*e.w,right:.1*e.w,w:.8*e.w,h:.6*e.h}}(e);break;case\"octagon\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj\",t,.29*Math.min(e.w,e.h)),n=[\"M\".concat(r,\",0\"),\"L\".concat(e.w-r,\",0\"),\"L\".concat(e.w,\",\").concat(r),\"L\".concat(e.w,\",\").concat(e.h-r),\"L\".concat(e.w-r,\",\").concat(e.h),\"L\".concat(r,\",\").concat(e.h),\"L0,\".concat(e.h-r),\"L0,\".concat(r),\"Z\"].join(\" \");return a.setAttribute(\"d\",n),a}(e),f=function(t){var e=t.extend,a=tt(\"adj\",t,.29*Math.min(e.w,e.h));return{top:.5*a,bottom:.5*a,left:.5*a,right:.5*a,w:e.w-a,h:e.h-a}}(e);break;case\"decagon\":d=function(t){var e=t.extend,a=J(\"path\"),r=.344,n=.117,o=.19,c=[\"M\".concat(e.w*r,\",0\"),\"L\".concat(.656*e.w,\",0\"),\"L\".concat(.883*e.w,\",\").concat(e.h*o),\"L\".concat(e.w,\",\").concat(.5*e.h),\"L\".concat(.883*e.w,\",\").concat(.81*e.h),\"L\".concat(.656*e.w,\",\").concat(e.h),\"L\".concat(e.w*r,\",\").concat(e.h),\"L\".concat(e.w*n,\",\").concat(.81*e.h),\"L0,\".concat(.5*e.h),\"L\".concat(e.w*n,\",\").concat(e.h*o),\"Z\"].join(\" \");return a.setAttribute(\"d\",c),a}(e),f=function(t){var e=t.extend,a=.117,r=.19;return{top:e.h*r,bottom:e.h*r,left:e.w*a,right:e.w*a,w:.766*e.w,h:.62*e.h}}(e);break;case\"dodecagon\":d=function(t){var e=t.extend,a=J(\"path\"),r=.364,n=.133,o=.135,c=[\"M\".concat(e.w*r,\",0\"),\"L\".concat(.636*e.w,\",0\"),\"L\".concat(.867*e.w,\",\").concat(e.h*o),\"L\".concat(e.w,\",\").concat(e.h*r),\"L\".concat(e.w,\",\").concat(.636*e.h),\"L\".concat(.867*e.w,\",\").concat(.865*e.h),\"L\".concat(.636*e.w,\",\").concat(e.h),\"L\".concat(e.w*r,\",\").concat(e.h),\"L\".concat(e.w*n,\",\").concat(.865*e.h),\"L0,\".concat(.636*e.h),\"L0,\".concat(e.h*r),\"L\".concat(e.w*n,\",\").concat(e.h*o),\"Z\"].join(\" \");return a.setAttribute(\"d\",c),a}(e),f=function(t){var e=t.extend,a=.133,r=.135;return{top:e.h*r,bottom:e.h*r,left:e.w*a,right:e.w*a,w:.734*e.w,h:.73*e.h}}(e);break;case\"pie\":d=function(t){var e=t.extend,a=J(\"path\"),r=$(\"adj1\",t,360),n=$(\"adj2\",t,270),o=e.w/2,c=e.h/2,i=e.w/2,s=e.h/2,h=lt(r,i,s,o,c),l=h[0],p=h[1],d=lt(n,i,s,o,c),u=d[0],f=d[1],y=pt(r,n),v=\"M\".concat(i,\",\").concat(s,\", L\").concat(l,\" \").concat(p,\" A \").concat(o,\" \").concat(c,\" 0 \").concat(y,\" \").concat(1,\" \").concat(u,\" \").concat(f,\" Z\");return a.setAttribute(\"d\",v),a}(e),f=dt(e);break;case\"arc\":d=function(t){var e=t.extend,a=J(\"path\"),r=$(\"adj1\",t,270),n=$(\"adj2\",t,0),o=e.w/2,c=e.h/2,i=e.w/2,s=e.h/2,h=lt(r,i,s,o,c),l=h[0],p=h[1],d=lt(n,i,s,o,c),u=d[0],f=d[1],y=pt(r,n),v=\"M\".concat(l,\",\").concat(p,\" A \").concat(o,\" \").concat(c,\" 0 \").concat(y,\" \").concat(1,\" \").concat(u,\" \").concat(f);return a.setAttribute(\"d\",v),a}(e),y=!1;break;case\"bracketPair\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj\",t,.16667*Math.min(e.w,e.h)),n=[\"M\".concat(r,\",\").concat(e.h),\"Q\".concat(0,\",\").concat(e.h,\" \").concat(0,\",\").concat(e.h-r),\"L\".concat(0,\",\").concat(r),\"Q\".concat(0,\",\").concat(0,\" \").concat(r,\",\").concat(0),\"M\".concat(e.w-r,\",\").concat(0),\"Q\".concat(e.w,\",\").concat(0,\" \").concat(e.w,\",\").concat(r),\"L\".concat(e.w,\",\").concat(e.h-r),\"Q\".concat(e.w,\",\").concat(e.h,\" \").concat(e.w-r,\",\").concat(e.h)].join(\" \");return a.setAttribute(\"d\",n),a}(e),f=function(t){var e=t.extend,a=.285*tt(\"adj\",t,.16667*Math.min(e.w,e.h));return{top:a,bottom:a,left:a,right:a,w:e.w-2*a,h:e.h-2*a}}(e),y=!1;break;case\"bracePair\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj\",t,.083335*Math.min(e.w,e.h)),n=[\"M\".concat(2*r,\",\").concat(e.h),\"Q\".concat(r,\",\").concat(e.h,\" \").concat(r,\",\").concat(e.h-r),\"L\".concat(r,\",\").concat(e.h/2+r),\"Q\".concat(r,\",\").concat(e.h/2,\" \").concat(0,\",\").concat(e.h/2),\"Q\".concat(r,\",\").concat(e.h/2,\" \").concat(r,\",\").concat(e.h/2-r),\"L\".concat(r,\",\").concat(r),\"Q\".concat(r,\",\").concat(0,\" \").concat(2*r,\",\").concat(0),\"M\".concat(e.w-2*r,\",\").concat(0),\"Q\".concat(e.w-r,\",\").concat(0,\" \").concat(e.w-r,\",\").concat(r),\"L\".concat(e.w-r,\",\").concat(e.h/2-r),\"Q\".concat(e.w-r,\",\").concat(e.h/2,\" \").concat(e.w,\",\").concat(e.h/2),\"Q\".concat(e.w-r,\",\").concat(e.h/2,\" \").concat(e.w-r,\",\").concat(e.h/2+r),\"L\".concat(e.w-r,\",\").concat(e.h-r),\"Q\".concat(e.w-r,\",\").concat(e.h,\" \").concat(e.w-2*r,\",\").concat(e.h)].join(\" \");return a.setAttribute(\"d\",n),a}(e),f=function(t){var e=t.extend,a=.285*tt(\"adj\",t,.16667*Math.min(e.w,e.h));return{top:a,bottom:a,left:a,right:a,w:e.w-2*a,h:e.h-2*a}}(e),y=!1;break;case\"chord\":d=function(t){var e=t.extend,a=J(\"path\"),r=$(\"adj1\",t,45),n=$(\"adj2\",t,270),o=e.w/2,c=e.h/2,i=e.w/2,s=e.h/2,h=lt(r,i,s,o,c),l=h[0],p=h[1],d=lt(n,i,s,o,c),u=d[0],f=d[1],y=pt(r,n),v=\"M\".concat(l,\" \").concat(p,\" A \").concat(o,\" \").concat(c,\" 0 \").concat(y,\" \").concat(1,\" \").concat(u,\" \").concat(f,\" Z\");return a.setAttribute(\"d\",v),a}(e),f=dt(e);break;case\"teardrop\":d=function(t){var e=t.extend,a=J(\"path\"),r=$(\"adj\",t,1),n=e.w/2,o=e.h/2,c=e.w/2,i=e.h/2,s=lt(0,c,i,n,o),h=s[0],l=s[1],p=lt(270,c,i,n,o),d=p[0],u=p[1],f=pt(0,270),y=\"M\".concat(h,\" \").concat(l,\" A \").concat(n,\" \").concat(o,\" 0 \").concat(f,\" \").concat(1,\" \").concat(d,\" \").concat(u),v=n*r,w=c+v,b=i-o*v/(e.w/2),m=(e.w/2+w)/2,g=(e.h/2+b)/2;return y+=\" Q\".concat(m,\",0 \").concat(w,\",\").concat(b),y+=\" Q\".concat(e.w,\",\").concat(g,\" \").concat(c+n,\",\").concat(i),a.setAttribute(\"d\",y),a}(e),f=dt(e);break;case\"frame\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj1\",t,.12*Math.min(e.w,e.h)),n=[\"M0,0\",\"L\".concat(e.w,\",0\"),\"L\".concat(e.w,\",\").concat(e.h),\"L0,\".concat(e.h),\"Z\",\"M\".concat(r,\",\").concat(r),\"L\".concat(r,\",\").concat(e.h-r),\"L\".concat(e.w-r,\",\").concat(e.h-r),\"L\".concat(e.w-r,\",\").concat(r),\"Z\"].join(\" \");return a.setAttribute(\"d\",n),a}(e),f=function(t){var e=t.extend,a=tt(\"adj1\",t,.12*Math.min(e.w,e.h));return{top:a,bottom:a,left:a,right:a,w:e.w-2*a,h:e.h-2*a}}(e);break;case\"halfFrame\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj1\",t,.333*Math.min(e.w,e.h)),n=Math.min(tt(\"adj2\",t,.333*Math.min(e.w,e.h)),e.w*(1-r/e.h)),o=[\"M0,0\",\"L\".concat(e.w,\",0\"),\"L\".concat(e.w*(1-r/e.h),\",\").concat(r),\"L\".concat(n,\",\").concat(r),\"L\".concat(n,\",\").concat(e.h*(1-n/e.w)),\"L0,\".concat(e.h),\"Z\"].join(\" \");return a.setAttribute(\"d\",o),a}(e);break;case\"corner\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),n=tt(\"adj2\",t,.5*Math.min(e.w,e.h)),o=[\"M0,0\",\"L\".concat(n,\",0\"),\"L\".concat(n,\",\").concat(e.h-r),\"L\".concat(e.w,\",\").concat(e.h-r),\"L\".concat(e.w,\",\").concat(e.h),\"L0,\".concat(e.h),\"Z\"].join(\" \");return a.setAttribute(\"d\",o),a}(e),f=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h));return{top:e.h-a,bottom:0,left:0,right:0,w:e.w,h:a}}(e);break;case\"diagStripe\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj\",t,.5*Math.min(e.w,e.h)),n=e.w*r/e.h,o=[\"M\".concat(n,\",0\"),\"L\".concat(e.w,\",0\"),\"L0,\".concat(e.h),\"L0,\".concat(r),\"Z\"].join(\" \");return a.setAttribute(\"d\",o),a}(e),f=function(t){var e=t.extend,a=$(\"adj\",t,.5),r=.5*(1-a)*e.h,n=.5*(1-a)*e.w;return{top:0,bottom:r,left:0,right:n,w:e.w-n,h:e.h-r}}(e);break;case\"plus\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj\",t,.25*Math.min(e.w,e.h)),n=[\"M\".concat(r,\",0\"),\"L\".concat(e.w-r,\",0\"),\"L\".concat(e.w-r,\",\").concat(r),\"L\".concat(e.w,\",\").concat(r),\"L\".concat(e.w,\",\").concat(e.h-r),\"L\".concat(e.w-r,\",\").concat(e.h-r),\"L\".concat(e.w-r,\",\").concat(e.h),\"L\".concat(r,\",\").concat(e.h),\"L\".concat(r,\",\").concat(e.h-r),\"L0,\".concat(e.h-r),\"L0,\".concat(r),\"L\".concat(r,\",\").concat(r),\"Z\"].join(\" \");return a.setAttribute(\"d\",n),a}(e),f=function(t){var e=t.extend,a=tt(\"adj\",t,.25*Math.min(e.w,e.h));return{top:a,bottom:a,left:0,right:0,w:e.w,h:e.h-2*a}}(e);break;case\"plaque\":d=function(t){var e=t.extend,a=J(\"path\"),r=tt(\"adj\",t,.16667*Math.min(e.w,e.h)),n=[\"M\".concat(r,\",0\"),\"L\".concat(e.w-r,\",0\"),\"Q\".concat(e.w-r,\",\").concat(r,\" \").concat(e.w,\",\").concat(r),\"L\".concat(e.w,\",\").concat(e.h-r),\"Q\".concat(e.w-r,\",\").concat(e.h-r,\" \").concat(e.w-r,\",\").concat(e.h),\"L\".concat(r,\",\").concat(e.h),\"Q\".concat(r,\",\").concat(e.h-r,\" 0,\").concat(e.h-r),\"L0,\".concat(r),\"Q\".concat(r,\",\").concat(r,\" \").concat(r,\",0\"),\"Z\"].join(\" \");return a.setAttribute(\"d\",n),a}(e),f=function(t){var e=t.extend,a=tt(\"adj\",t,.16667*Math.min(e.w,e.h));return{top:.72*a,bottom:.72*a,left:.72*a,right:.72*a,w:e.w-1.44*a,h:e.w-1.44*a}}(e);break;case\"can\":d=function(t){var e=t.extend,a=t.background,r=J(\"g\"),n=J(\"path\"),o=tt(\"adj\",t,.25*Math.min(e.w,e.h)),c=[\"M0,\".concat(o/2),\"L0,\".concat(e.h-o/2),\"A\".concat(e.w/2,\",\").concat(o/2,\" 0 0 0 \").concat(e.w,\",\").concat(e.h-o/2),\"L\".concat(e.w,\",\").concat(o/2),\"A\".concat(e.w/2,\",\").concat(o/2,\" 0 0 1 0,\").concat(o/2),\"Z\"].join(\" \");n.setAttribute(\"d\",c);var i=J(\"ellipse\"),s=e.w/2,h=o/2,l=e.w/2,p=o/2;return i.setAttribute(\"cx\",s+\"px\"),i.setAttribute(\"cy\",h+\"px\"),i.setAttribute(\"rx\",l+\"px\"),i.setAttribute(\"ry\",p+\"px\"),\"solidFill\"===(null==a?void 0:a.type)&&i.setAttribute(\"fill\",S(a,{light:.5})||\"transparent\"),r.appendChild(n),r.appendChild(i),r}(e),f=function(t){var e=t.extend,a=tt(\"adj\",t,.25*Math.min(e.w,e.h));return{top:a,bottom:0,left:0,right:0,w:e.w,h:e.h-a}}(e);break;case\"cube\":d=function(t){var e=t.extend,a=t.background,r=tt(\"adj\",t,.25*Math.min(e.w,e.h)),n=J(\"g\"),o=J(\"path\"),c=[\"M0,\".concat(r),\"L\".concat(e.w-r,\",\").concat(r),\"L\".concat(e.w-r,\",\").concat(e.h),\"L0,\".concat(e.h),\"Z\"].join(\" \");o.setAttribute(\"d\",c);var i=J(\"path\"),s=[\"M0,\".concat(r),\"L\".concat(r,\",0\"),\"L\".concat(e.w,\",0\"),\"L\".concat(e.w-r,\",\").concat(r),\"Z\"].join(\" \");i.setAttribute(\"d\",s),\"solidFill\"===(null==a?void 0:a.type)&&i.setAttribute(\"fill\",S(a,{light:.8})||\"transparent\");var h=J(\"path\"),l=[\"M\".concat(e.w,\",0\"),\"L\".concat(e.w-r,\",\").concat(r),\"L\".concat(e.w-r,\",\").concat(e.h),\"L\".concat(e.w,\",\").concat(e.h-r),\"Z\"].join(\" \");return h.setAttribute(\"d\",l),\"solidFill\"===(null==a?void 0:a.type)&&h.setAttribute(\"fill\",S(a,{dark:.6})||\"transparent\"),n.appendChild(o),n.appendChild(i),n.appendChild(h),n}(e),f=function(t){var e=t.extend,a=tt(\"adj\",t,.25*Math.min(e.w,e.h));return{top:a,bottom:0,left:0,right:a,w:e.w-a,h:e.h-a}}(e);break;case\"bevel\":d=function(t){var e=t.extend,a=t.background,r=tt(\"adj\",t,.125*Math.min(e.w,e.h)),n=J(\"g\"),o=J(\"path\"),c=[\"M\".concat(r,\",\").concat(r),\"L\".concat(e.w-r,\",\").concat(r),\"L\".concat(e.w-r,\",\").concat(e.h-r),\"L\".concat(r,\",\").concat(e.h-r),\"Z\"].join(\" \");o.setAttribute(\"d\",c);var i=J(\"path\"),s=[\"M0,0\",\"L\".concat(r,\",\").concat(r),\"L\".concat(e.w-r,\",\").concat(r),\"L\".concat(e.w,\",0\"),\"Z\"].join(\" \");i.setAttribute(\"d\",s),\"solidFill\"===(null==a?void 0:a.type)&&i.setAttribute(\"fill\",S(a,{light:.8})||\"transparent\");var h=J(\"path\"),l=[\"M\".concat(e.w,\",0\"),\"L\".concat(e.w-r,\",\").concat(r),\"L\".concat(e.w-r,\",\").concat(e.h-r),\"L\".concat(e.w,\",\").concat(e.h),\"Z\"].join(\" \");h.setAttribute(\"d\",l),\"solidFill\"===(null==a?void 0:a.type)&&h.setAttribute(\"fill\",S(a,{dark:.6})||\"transparent\");var p=J(\"path\"),d=[\"M\".concat(e.w,\",\").concat(e.h),\"L\".concat(e.w-r,\",\").concat(e.h-r),\"L\".concat(r,\",\").concat(e.h-r),\"L0,\".concat(e.h),\"Z\"].join(\" \");p.setAttribute(\"d\",d),\"solidFill\"===(null==a?void 0:a.type)&&p.setAttribute(\"fill\",S(a,{dark:.625})||\"transparent\");var u=J(\"path\"),f=[\"M0,\".concat(e.h),\"L\".concat(r,\",\").concat(e.h-r),\"L\".concat(r,\",\").concat(r),\"L0,0\",\"Z\"].join(\" \");return u.setAttribute(\"d\",f),\"solidFill\"===(null==a?void 0:a.type)&&u.setAttribute(\"fill\",S(a,{light:.6})||\"transparent\"),n.appendChild(o),n.appendChild(i),n.appendChild(h),n.appendChild(p),n.appendChild(u),n}(e),f=function(t){var e=t.extend,a=tt(\"adj\",t,.125*Math.min(e.w,e.h));return{top:a,bottom:a,left:a,right:a,w:e.w-2*a,h:e.h-2*a}}(e);break;case\"donut\":d=function(t){var e=t.extend,a=tt(\"adj\",t,.25*Math.min(e.w,e.h)),r=J(\"path\"),n=[\"M0,\".concat(e.h/2),\"A\".concat(e.w/2,\",\").concat(e.h/2,\" 0 1,1 0,\").concat(e.h/2+1),\"Z\",\"M\".concat(e.w-a,\",\").concat(e.h/2),\"A\".concat(e.w/2-a,\",\").concat(e.h/2-a,\" 0 1,0 \").concat(e.w-a,\",\").concat(e.h/2+1),\"Z\"].join(\" \");return r.setAttribute(\"d\",n),r}(e),f=dt(e);break;case\"noSmoking\":d=function(t){var e=t.extend,a=tt(\"adj\",t,.25*Math.min(e.w,e.h)),r=J(\"path\"),n=Math.atan(e.h/e.w),o=a/2/Math.sin(n),c=e.w/2,i=e.h/2,s=-1*e.h/e.w,h=e.h*o/e.w,l=e.w/2-a,p=e.h/2-a,d=-2*l*l*s*h,u=Math.sqrt(Math.pow(2*l*l*s*h,2)-4*(p*p+l*l*s*s)*l*l*(h*h-p*p)),f=2*(p*p+l*l*s*s),y=(d-u)/f,v=s*y+h,w=(d+u)/f,b=s*w+h,m=-e.h*o/e.w,g=-2*l*l*s*m,x=Math.sqrt(Math.pow(2*l*l*s*m,2)-4*(p*p+l*l*s*s)*l*l*(m*m-p*p)),L=2*(p*p+l*l*s*s),M=(g-x)/L,A=s*M+m,P=(g+x)/L,k=s*P+m,j=[\"M0,\".concat(e.h/2),\"A\".concat(e.w/2,\",\").concat(e.h/2,\" 0 1,1 0,\").concat(e.h/2+1),\"Z\",\"M\".concat(c+w,\",\").concat(i-b),\"A\".concat(l,\",\").concat(p,\" 0 0 0 \").concat(c+y,\",\").concat(i-v),\"Z\",\"M\".concat(c+M,\",\").concat(i-A),\"A\".concat(l,\",\").concat(p,\" 0 0 0 \").concat(c+P,\",\").concat(i-k),\"Z\"].join(\" \");return r.setAttribute(\"d\",j),r}(e),f=dt(e);break;case\"rightArrow\":d=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.5*Math.min(e.w,e.h)),n=J(\"path\"),o=[\"M0,\".concat(e.h/2-a/2),\"L\".concat(e.w-r,\",\").concat(e.h/2-a/2),\"L\".concat(e.w-r,\",0\"),\"L\".concat(e.w,\",\").concat(e.h/2),\"L\".concat(e.w-r,\",\").concat(e.h),\"L\".concat(e.w-r,\",\").concat(e.h/2+a/2),\"L0,\".concat(e.h/2+a/2),\"Z\"].join(\" \");return n.setAttribute(\"d\",o),n}(e),f=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),r=a*tt(\"adj2\",t,.5*Math.min(e.w,e.h))/e.h;return{top:e.h/2-a/2,bottom:e.h/2-a/2,left:0,right:r,w:e.w-r,h:a}}(e);break;case\"leftArrow\":d=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.5*Math.min(e.w,e.h)),n=J(\"path\"),o=[\"M0,\".concat(e.h/2),\"L\".concat(r,\",0\"),\"L\".concat(r,\",\").concat(e.h/2-a/2),\"L\".concat(e.w,\",\").concat(e.h/2-a/2),\"L\".concat(e.w,\",\").concat(e.h/2+a/2),\"L\".concat(r,\",\").concat(e.h/2+a/2),\"L\".concat(r,\",\").concat(e.h),\"Z\"].join(\" \");return n.setAttribute(\"d\",o),n}(e),f=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),r=a*tt(\"adj2\",t,.5*Math.min(e.w,e.h))/e.h;return{top:e.h/2-a/2,bottom:e.h/2-a/2,left:r,right:0,w:e.w-r,h:a}}(e);break;case\"upArrow\":d=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.5*Math.min(e.w,e.h)),n=J(\"path\"),o=[\"M\".concat(e.w/2,\",0\"),\"L\".concat(e.w,\",\").concat(r),\"L\".concat(e.w/2+a/2,\",\").concat(r),\"L\".concat(e.w/2+a/2,\",\").concat(e.h),\"L\".concat(e.w/2-a/2,\",\").concat(e.h),\"L\".concat(e.w/2-a/2,\",\").concat(r),\"L0,\".concat(r),\"Z\"].join(\" \");return n.setAttribute(\"d\",o),n}(e),f=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),r=a*tt(\"adj2\",t,.5*Math.min(e.w,e.h))/e.w;return{top:r,bottom:0,left:e.w/2-a/2,right:e.w/2-a/2,w:a,h:e.h-r}}(e);break;case\"downArrow\":d=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.5*Math.min(e.w,e.h)),n=J(\"path\"),o=[\"M\".concat(e.w/2,\",\").concat(e.h),\"L0,\".concat(e.h-r),\"L\".concat(e.w/2-a/2,\",\").concat(e.h-r),\"L\".concat(e.w/2-a/2,\",0\"),\"L\".concat(e.w/2+a/2,\",0\"),\"L\".concat(e.w/2+a/2,\",\").concat(e.h-r),\"L\".concat(e.w,\",\").concat(e.h-r),\"Z\"].join(\" \");return n.setAttribute(\"d\",o),n}(e),f=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),r=a*tt(\"adj2\",t,.5*Math.min(e.w,e.h))/e.w;return{top:0,bottom:r,left:e.w/2-a/2,right:e.w/2-a/2,w:a,h:e.h-r}}(e);break;case\"leftRightArrow\":d=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.5*Math.min(e.w,e.h)),n=J(\"path\"),o=[\"M0,\".concat(e.h/2),\"L\".concat(r,\",0\"),\"L\".concat(r,\",\").concat(e.h/2-a/2),\"L\".concat(e.w-r,\",\").concat(e.h/2-a/2),\"L\".concat(e.w-r,\",0\"),\"L\".concat(e.w,\",\").concat(e.h/2),\"L\".concat(e.w-r,\",\").concat(e.h),\"L\".concat(e.w-r,\",\").concat(e.h/2+a/2),\"L\".concat(r,\",\").concat(e.h/2+a/2),\"L\".concat(r,\",\").concat(e.h),\"Z\"].join(\" \");return n.setAttribute(\"d\",o),n}(e),f=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),r=a*tt(\"adj2\",t,.5*Math.min(e.w,e.h))/e.h;return{top:e.h/2-a/2,bottom:e.h/2-a/2,left:r,right:r,w:e.w-2*r,h:a}}(e);break;case\"upDownArrow\":d=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.5*Math.min(e.w,e.h)),n=J(\"path\"),o=[\"M\".concat(e.w/2,\",\").concat(e.h),\"L0,\".concat(e.h-r),\"L\".concat(e.w/2-a/2,\",\").concat(e.h-r),\"L\".concat(e.w/2-a/2,\",\").concat(r),\"L0,\".concat(r),\"L\".concat(e.w/2,\",0\"),\"L\".concat(e.w,\",\").concat(r),\"L\".concat(e.w/2+a/2,\",\").concat(r),\"L\".concat(e.w/2+a/2,\",\").concat(e.h-r),\"L\".concat(e.w,\",\").concat(e.h-r),\"Z\"].join(\" \");return n.setAttribute(\"d\",o),n}(e),f=function(t){var e=t.extend,a=tt(\"adj1\",t,.5*Math.min(e.w,e.h)),r=a*tt(\"adj2\",t,.5*Math.min(e.w,e.h))/e.w;return{top:r,bottom:r,left:e.w/2-a/2,right:e.w/2-a/2,w:a,h:e.h-2*r}}(e);break;case\"quadArrow\":d=function(t){var e=t.extend,a=tt(\"adj1\",t,.225*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.225*Math.min(e.w,e.h)),n=tt(\"adj3\",t,.225*Math.min(e.w,e.h)),o=J(\"path\"),c=[\"M0,\".concat(e.h/2),\"L\".concat(n,\",\").concat(e.h/2-r),\"L\".concat(n,\",\").concat(e.h/2-a/2),\"L\".concat(e.w/2-a/2,\",\").concat(e.h/2-a/2),\"L\".concat(e.w/2-a/2,\",\").concat(n),\"L\".concat(e.w/2-r,\",\").concat(n),\"L\".concat(e.w/2,\",0\"),\"L\".concat(e.w/2+r,\",\").concat(n),\"L\".concat(e.w/2+a/2,\",\").concat(n),\"L\".concat(e.w/2+a/2,\",\").concat(e.h/2-a/2),\"L\".concat(e.w-n,\",\").concat(e.h/2-a/2),\"L\".concat(e.w-n,\",\").concat(e.h/2-r),\"L\".concat(e.w,\",\").concat(e.h/2),\"L\".concat(e.w-n,\",\").concat(e.h/2+r),\"L\".concat(e.w-n,\",\").concat(e.h/2+a/2),\"L\".concat(e.w/2+a/2,\",\").concat(e.h/2+a/2),\"L\".concat(e.w/2+a/2,\",\").concat(e.h-n),\"L\".concat(e.w/2+r,\",\").concat(e.h-n),\"L\".concat(e.w/2,\",\").concat(e.h),\"L\".concat(e.w/2-r,\",\").concat(e.h-n),\"L\".concat(e.w/2-a/2,\",\").concat(e.h-n),\"L\".concat(e.w/2-a/2,\",\").concat(e.h/2+a/2),\"L\".concat(n,\",\").concat(e.h/2+a/2),\"L\".concat(n,\",\").concat(e.h/2+r),\"Z\"].join(\" \");return o.setAttribute(\"d\",c),o}(e),f=function(t){var e=t.extend,a=tt(\"adj1\",t,.225*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.225*Math.min(e.w,e.h)),n=tt(\"adj3\",t,.225*Math.min(e.w,e.h)),o=0===r?0:a*n/r/2;return{top:e.h/2-a/2,bottom:e.h/2-a/2,left:o,right:o,w:e.w-2*o,h:a}}(e);break;case\"leftRightUpArrow\":d=function(t){var e=t.extend,a=tt(\"adj1\",t,.225*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.225*Math.min(e.w,e.h)),n=tt(\"adj3\",t,.225*Math.min(e.w,e.h)),o=J(\"path\");a>2*r&&(a=2*r);var c=[\"M0,\".concat(e.h-r),\"L\".concat(n,\",\").concat(e.h-2*r),\"L\".concat(n,\",\").concat(e.h-r-a/2),\"L\".concat(e.w/2-a/2,\",\").concat(e.h-r-a/2),\"L\".concat(e.w/2-a/2,\",\").concat(n),\"L\".concat(e.w/2-r,\",\").concat(n),\"L\".concat(e.w/2,\",0\"),\"L\".concat(e.w/2+r,\",\").concat(n),\"L\".concat(e.w/2+a/2,\",\").concat(n),\"L\".concat(e.w/2+a/2,\",\").concat(e.h-r-a/2),\"L\".concat(e.w-n,\",\").concat(e.h-r-a/2),\"L\".concat(e.w-n,\",\").concat(e.h-2*r),\"L\".concat(e.w,\",\").concat(e.h-r),\"L\".concat(e.w-n,\",\").concat(e.h),\"L\".concat(e.w-n,\",\").concat(e.h-r+a/2),\"L\".concat(n,\",\").concat(e.h-r+a/2),\"L\".concat(n,\",\").concat(e.h),\"Z\"].join(\" \");return o.setAttribute(\"d\",c),o}(e),f=function(t){var e=t.extend,a=tt(\"adj1\",t,.225*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.225*Math.min(e.w,e.h)),n=tt(\"adj3\",t,.225*Math.min(e.w,e.h)),o=0===r?0:a*n/r/2;return{top:e.h-r-a/2,bottom:r-a/2,left:o,right:o,w:e.w-2*o,h:a}}(e);break;case\"bentArrow\":d=function(t){var e=t.extend,a=tt(\"adj1\",t,.25*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.25*Math.min(e.w,e.h)),n=tt(\"adj3\",t,.25*Math.min(e.w,e.h)),o=tt(\"adj4\",t,.4375*Math.min(e.w,e.h)),c=J(\"path\");a>2*r&&(a=2*r);var i=o-a;i<0&&(i=0);var s=[\"M0,\".concat(e.h),\"L0,\".concat(r-a/2+o),\"A\".concat(o,\" \").concat(o,\" 0 0 1 \").concat(o,\" \").concat(r-a/2),\"L\".concat(e.w-n,\",\").concat(r-a/2),\"L\".concat(e.w-n,\",0\"),\"L\".concat(e.w,\",\").concat(r),\"L\".concat(e.w-n,\",\").concat(2*r),\"L\".concat(e.w-n,\",\").concat(r+a/2),\"L\".concat(a+i,\",\").concat(r+a/2),\"A\".concat(i,\" \").concat(i,\"  0 0 0 \").concat(a,\" \").concat(r+a/2+i),\"L\".concat(a,\",\").concat(e.h),\"Z\"].join(\" \");return c.setAttribute(\"d\",s),c}(e);break;case\"uturnArrow\":d=function(t){var e=t.extend,a=tt(\"adj1\",t,.25*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.25*Math.min(e.w,e.h)),n=tt(\"adj3\",t,.25*Math.min(e.w,e.h)),o=tt(\"adj4\",t,.4375*Math.min(e.w,e.h)),c=tt(\"adj5\",t,.75*Math.min(e.w,e.h)),i=J(\"path\");a>2*r&&(a=2*r),c<n&&(c=n+a),o>c-n&&(o=c-n);var s=o-a;s>c-n-a&&(s=c-n-a),s<0&&(s=0);var h=r-a/2,l=[\"M0,\".concat(e.h),\"L0,\".concat(o),\"A\".concat(o,\" \").concat(o,\" 0 0 1 \").concat(o,\" 0\"),\"L\".concat(e.w-o-h,\",0\"),\"A\".concat(o,\" \").concat(o,\" 0 0 1 \").concat(e.w-h,\" \").concat(o),\"L\".concat(e.w-h,\",\").concat(c-n),\"L\".concat(e.w,\",\").concat(c-n),\"L\".concat(e.w-r,\",\").concat(c),\"L\".concat(e.w-2*r,\",\").concat(c-n),\"L\".concat(e.w-r-a/2,\",\").concat(c-n),\"L\".concat(e.w-r-a/2,\",\").concat(a+s),\"A\".concat(s,\" \").concat(s,\"  0 0 0 \").concat(e.w-s-r-a/2,\" \").concat(a),\"L\".concat(a+s,\",\").concat(a),\"A\".concat(s,\" \").concat(s,\"  0 0 0 \").concat(a,\" \").concat(a+s),\"L\".concat(a,\",\").concat(e.h),\"Z\"].join(\" \");return i.setAttribute(\"d\",l),i}(e);break;case\"leftUpArrow\":d=function(t){var e=t.extend,a=tt(\"adj1\",t,.25*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.25*Math.min(e.w,e.h)),n=tt(\"adj3\",t,.25*Math.min(e.w,e.h));a>2*r&&(a=2*r);var o=Math.min(e.w,e.h)-2*r;n>o&&(n=o),n<0&&(n=0);var c=r-a/2,i=J(\"path\"),s=[\"M0,\".concat(e.h-r),\"L\".concat(n,\",\").concat(e.h-2*r),\"L\".concat(n,\",\").concat(e.h-r-a/2),\"L\".concat(e.w-r-a/2,\",\").concat(e.h-r-a/2),\"L\".concat(e.w-r-a/2,\",\").concat(n),\"L\".concat(e.w-2*r,\",\").concat(n),\"L\".concat(e.w-r,\",0\"),\"L\".concat(e.w,\",\").concat(n),\"L\".concat(e.w-c,\",\").concat(n),\"L\".concat(e.w-c,\",\").concat(e.h-c),\"L\".concat(n,\",\").concat(e.h-c),\"L\".concat(n,\",\").concat(e.h),\"Z\"].join(\" \");return i.setAttribute(\"d\",s),i}(e),f=function(t){var e=t.extend,a=tt(\"adj1\",t,.25*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.25*Math.min(e.w,e.h)),n=tt(\"adj3\",t,.25*Math.min(e.w,e.h));a>2*r&&(a=2*r);var o=Math.min(e.w,e.h)-2*r;n>o&&(n=o),n<0&&(n=0);var c=0===r?0:a*n/r/2;return{top:e.h-r-a/2,bottom:r-a/2,left:c,right:r,w:e.w-c-r,h:a}}(e);break;case\"bentUpArrow\":d=function(t){var e=t.extend,a=tt(\"adj1\",t,.25*Math.min(e.w,e.h)),r=tt(\"adj2\",t,.25*Math.min(e.w,e.h)),n=tt(\"adj3\",t,.25*Math.min(e.w,e.h));a>2*r&&(a=2*r);var o=Math.min(e.w,e.h)-2*r;n>o&&(n=o),n<0&&(n=0);var c=r-a/2,i=J(\"path\"),s=[\"M0,\".concat(e.h),\"L0,\".concat(e.h-a),\"L\".concat(e.w-r-a/2,\",\").concat(e.h-a),\"L\".concat(e.w-r-a/2,\",\").concat(n),\"L\".concat(e.w-2*r,\",\").concat(n),\"L\".concat(e.w-r,\",0\"),\"L\".concat(e.w,\",\").concat(n),\"L\".concat(e.w-c,\",\").concat(n),\"L\".concat(e.w-c,\",\").concat(e.h),\"Z\"].join(\" \");return i.setAttribute(\"d\",s),i}(e),f=function(t){var e=t.extend,a=tt(\"adj1\",t,.25*Math.min(e.w,e.h));return{top:e.h-a,bottom:0,left:0,right:0,w:e.w,h:a}}(e);break;case\"curvedRightArrow\":d=function(t){var e=t.extend,a=t.background,r=tt(\"adj1\",t,.25*Math.min(e.w,e.h)),n=tt(\"adj2\",t,.5*Math.min(e.w,e.h)),o=tt(\"adj3\",t,.25*Math.min(e.w,e.h));r>n&&(r=n);var c=n/2-r/2,i=(e.h-n/2-r/2)/2,s=(e.h-c-r)/2,h=J(\"g\"),l=J(\"path\"),p=[\"M\".concat(e.w,\",0\"),\"A \".concat(e.w,\" \").concat(i,\" 0 0 0 \").concat(0,\" \").concat(i),\"L0,\".concat(s+r),\"A \".concat(e.w,\" \").concat(s,\" 0 0 1 \").concat(e.w,\" \").concat(r),\"Z\"].join(\" \");l.setAttribute(\"d\",p),\"solidFill\"===(null==a?void 0:a.type)&&l.setAttribute(\"fill\",S(a,{dark:.6})||\"transparent\");var d=J(\"path\"),u=[\"M0,\".concat(i),\"A \".concat(e.w,\" \").concat(i,\" 0 0 0 \").concat(e.w-o,\" \").concat(e.h-n/2-r/2),\"L\".concat(e.w-o,\",\").concat(e.h-n),\"L\".concat(e.w,\",\").concat(e.h-n/2),\"L\".concat(e.w-o,\",\").concat(e.h),\"L\".concat(e.w-o,\",\").concat(e.h-c),\"A \".concat(e.w,\" \").concat(s,\" 0 0 1 0 \").concat(r+s),\"Z\"].join(\" \");return d.setAttribute(\"d\",u),h.appendChild(l),h.appendChild(d),h}(e);break;case\"curvedLeftArrow\":d=function(t){var e=t.extend,a=t.background,r=tt(\"adj1\",t,.25*Math.min(e.w,e.h)),n=tt(\"adj2\",t,.5*Math.min(e.w,e.h)),o=tt(\"adj3\",t,.25*Math.min(e.w,e.h));r>n&&(r=n);var c=n/2-r/2,i=(e.h-n/2-r/2)/2,s=(e.h-c-r)/2,h=J(\"g\"),l=J(\"path\"),p=[\"M0,\".concat(e.h-n/2),\"L\".concat(o,\",\").concat(e.h-n),\"L\".concat(o,\",\").concat(e.h-n/2-r/2),\"A\".concat(e.w,\" \").concat(i,\" 0 0 0 \").concat(e.w,\" \").concat(i),\"L\".concat(e.w,\",\").concat(s+r),\"A \".concat(e.w,\" \").concat(s,\" 0 0 1 \").concat(o,\" \").concat(e.h-c),\"L\".concat(o,\",\").concat(e.h),\"Z\"].join(\" \");l.setAttribute(\"d\",p);var d=J(\"path\"),u=[\"M0,0\",\"A \".concat(e.w,\" \").concat(i,\" 0 0 1 \").concat(e.w,\" \").concat(i),\"L\".concat(e.w,\",\").concat(s+r),\"A \".concat(e.w,\" \").concat(s,\" 0 0 0 \").concat(0,\" \").concat(r),\"Z\"].join(\" \");return d.setAttribute(\"d\",u),\"solidFill\"===(null==a?void 0:a.type)&&d.setAttribute(\"fill\",S(a,{dark:.6})||\"transparent\"),h.appendChild(l),h.appendChild(d),h}(e);break;case\"curvedUpArrow\":d=function(t){var e=t.extend,a=t.background,r=tt(\"adj1\",t,.25*Math.min(e.w,e.h)),n=tt(\"adj2\",t,.5*Math.min(e.w,e.h)),o=tt(\"adj3\",t,.25*Math.min(e.w,e.h));r>n&&(r=n);var c=n/2-r/2,i=(e.w-n/2-r/2)/2,s=(e.w-c-r)/2,h=J(\"g\"),l=J(\"path\"),p=[\"M\".concat(e.w-n/2,\",\").concat(0),\"L\".concat(e.w-n,\",\").concat(o),\"L\".concat(e.w-n/2-r/2,\",\").concat(o),\"A\".concat(i,\" \").concat(e.h,\" 0 0 1 \").concat(i,\" \").concat(e.h),\"L\".concat(i+r,\",\").concat(e.h),\"A\".concat(s,\" \").concat(e.h,\" 0 0 0 \").concat(e.w-c,\" \").concat(o),\"L\".concat(e.w,\",\").concat(o),\"Z\"].join(\" \");l.setAttribute(\"d\",p);var d=J(\"path\"),u=[\"M\".concat(r,\",0\"),\"L\".concat(0,\",\").concat(0),\"A \".concat(i,\" \").concat(e.h,\" 0 0 0 \").concat(i,\" \").concat(e.h),\"L\".concat(i+r,\",\").concat(e.h),\"A \".concat(s,\" \").concat(e.h,\" 0 0 1 \").concat(r,\" \").concat(0),\"Z\"].join(\" \");return d.setAttribute(\"d\",u),\"solidFill\"===(null==a?void 0:a.type)&&d.setAttribute(\"fill\",S(a,{dark:.6})||\"transparent\"),h.appendChild(l),h.appendChild(d),h}(e);break;case\"curvedDownArrow\":d=function(t){var e=t.extend,a=t.background,r=tt(\"adj1\",t,.25*Math.min(e.w,e.h)),n=tt(\"adj2\",t,.5*Math.min(e.w,e.h)),o=tt(\"adj3\",t,.25*Math.min(e.w,e.h));r>n&&(r=n);var c=n/2-r/2,i=(e.w-n/2-r/2)/2,s=(e.w-c-r)/2,h=J(\"g\"),l=J(\"path\"),p=[\"M\".concat(0,\",\").concat(e.h),\"L\".concat(r,\",\").concat(e.h),\"A\".concat(s,\" \").concat(e.h,\" 0 0 1 \").concat(s+r,\" \").concat(0),\"L\".concat(i,\",\").concat(0),\"A\".concat(i,\" \").concat(e.h,\" 0 0 0 \").concat(0,\" \").concat(e.h),\"Z\"].join(\" \");l.setAttribute(\"d\",p),\"solidFill\"===(null==a?void 0:a.type)&&l.setAttribute(\"fill\",S(a,{dark:.6})||\"transparent\");var d=J(\"path\"),u=[\"M\".concat(e.w-n/2,\",\").concat(e.h),\"L\".concat(e.w-n,\",\").concat(e.h-o),\"L\".concat(e.w-n/2-r/2,\",\").concat(e.h-o),\"A \".concat(i,\" \").concat(e.h,\" 0 0 0 \").concat(i,\" \").concat(0),\"L\".concat(i+r,\",\").concat(0),\"A \".concat(s,\" \").concat(e.h,\" 0 0 1 \").concat(e.w-c,\" \").concat(e.h-o),\"L\".concat(e.w,\",\").concat(e.h-o),\"Z\"].join(\" \");return d.setAttribute(\"d\",u),h.appendChild(l),h.appendChild(d),h}(e);break;case\"stripedRightArrow\":d=function(t){var e=t.extend,a=J(\"g\"),r=Math.min(e.w,e.h),n=$(\"adj1\",t,.5)*e.h,o=$(\"adj2\",t,.5)*r,c=r/8,i=r/16,s=r/32,h=5*r/32,l=e.h/2-n/2,p=e.h/2+n/2,d=J(\"path\");d.setAttribute(\"d\",[\"M\".concat(0,\",\").concat(l),\"L\".concat(s,\",\").concat(l),\"L\".concat(s,\",\").concat(p),\"L\".concat(0,\",\").concat(p),\"Z\"].join(\" \"));var u=J(\"path\");u.setAttribute(\"d\",[\"M\".concat(i,\",\").concat(l),\"L\".concat(c,\",\").concat(l),\"L\".concat(c,\",\").concat(p),\"L\".concat(i,\",\").concat(p),\"Z\"].join(\" \"));var f=J(\"path\"),y=[\"M\".concat(h,\",\").concat(l),\"L\".concat(e.w-o,\",\").concat(l),\"L\".concat(e.w-o,\",\").concat(0),\"L\".concat(e.w,\",\").concat(e.h/2),\"L\".concat(e.w-o,\",\").concat(e.h),\"L\".concat(e.w-o,\",\").concat(p),\"L\".concat(h,\",\").concat(p),\"Z\"].join(\" \");return f.setAttribute(\"d\",y),a.appendChild(f),a.appendChild(d),a.appendChild(u),a}(e),f=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj1\",t,.5)*e.h,n=r*($(\"adj2\",t,.5)*a)/e.h;return{top:e.h/2-r/2,bottom:e.h/2-r/2,left:0,right:n,w:e.w-n,h:r}}(e);break;case\"notchedRightArrow\":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj1\",t,.5)*e.h,n=$(\"adj2\",t,.5)*a,o=r*n/e.h,c=e.h/2-r/2,i=e.h/2+r/2,s=J(\"path\"),h=[\"M\".concat(0,\",\").concat(c),\"L\".concat(e.w-n,\",\").concat(c),\"L\".concat(e.w-n,\",\").concat(0),\"L\".concat(e.w,\",\").concat(e.h/2),\"L\".concat(e.w-n,\",\").concat(e.h),\"L\".concat(e.w-n,\",\").concat(i),\"L\".concat(0,\",\").concat(i),\"L\".concat(o,\",\").concat(e.h/2),\"Z\"].join(\" \");return s.setAttribute(\"d\",h),s}(e),f=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj1\",t,.5)*e.h,n=$(\"adj2\",t,.5)*a,o=r*n/e.h,c=r*n/e.h;return{top:e.h/2-r/2,bottom:e.h/2-r/2,left:o,right:c,w:e.w-o-c,h:r}}(e);break;case\"homePlate\":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj\",t,.5)*a,n=J(\"path\"),o=[\"M\".concat(0,\",\").concat(0),\"L\".concat(e.w-r,\",\").concat(0),\"L\".concat(e.w,\",\").concat(e.h/2),\"L\".concat(e.w-r,\",\").concat(e.h),\"L\".concat(0,\",\").concat(e.h),\"Z\"].join(\" \");return n.setAttribute(\"d\",o),n}(e),f=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj\",t,.5)*a;return{top:0,bottom:0,left:0,right:r/2,w:e.w-r/2,h:e.h}}(e);break;case\"chevron\":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj\",t,.5)*a,n=J(\"path\"),o=[\"M\".concat(0,\",\").concat(0),\"L\".concat(e.w-r,\",\").concat(0),\"L\".concat(e.w,\",\").concat(e.h/2),\"L\".concat(e.w-r,\",\").concat(e.h),\"L\".concat(0,\",\").concat(e.h),\"L\".concat(r,\",\").concat(e.h/2),\"Z\"].join(\" \");return n.setAttribute(\"d\",o),n}(e),f=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj\",t,.5)*a;return{top:0,bottom:0,left:r,right:r,w:e.w-2*r,h:e.h}}(e);break;case\"blockArc\":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=J(\"path\"),n=$(\"adj1\",t,180),o=$(\"adj2\",t,0),c=tt(\"adj3\",t,.25*a),i=e.w/2,s=e.h/2,h=e.w/2,l=e.h/2,p=e.w/2-c,d=e.h/2-c,u=lt(n,i,s,h,l),f=u[0],y=u[1],v=lt(o,i,s,h,l),w=v[0],b=v[1],m=lt(n,i,s,p,d),g=m[0],x=m[1],L=lt(o,i,s,p,d),M=L[0],A=L[1],P=pt(n,o),k=[\"M\".concat(f,\",\").concat(y),\"A\".concat(h,\" \").concat(l,\" 0 \").concat(P,\" 1 \").concat(w,\" \").concat(b),\"L\".concat(M,\",\").concat(A),\"A\".concat(p,\" \").concat(d,\" 0 \").concat(P,\" 0 \").concat(g,\" \").concat(x),\"Z\"].join(\" \");return r.setAttribute(\"d\",k),r}(e),f=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj1\",t,180),n=$(\"adj2\",t,0),o=tt(\"adj3\",t,.25*a),c=e.w/2,i=e.h/2,s=e.w/2,h=e.h/2,l=e.w/2-o,p=e.h/2-o,d=lt(r,c,i,s,h),u=d[0],f=d[1],y=lt(n,c,i,s,h),v=y[0],w=y[1],b=lt(r,c,i,l,p),m=b[0],g=b[1],x=lt(n,c,i,l,p),L=[[u,f],[v,w],[m,g],[x[0],x[1]]];L.push(),r>n&&L.push([e.w,e.h/2]),(n>180&&n<=360&&r<180||r>n&&n>=0&&n<180&&r<180)&&L.push([0,e.h/2]),(r<n&&r<90&&n>90||r>n&&n>90||r>n&&r<90)&&L.push([e.w/2,e.h]),(r<n&&r<270&&n>270||r>n&&n>270||r>n&&r<270)&&L.push([e.w/2,0]);var M=1/0,A=1/0,P=-1/0,k=-1/0;return L.forEach((function(t){M=Math.min(t[0],M),A=Math.min(t[1],A),P=Math.max(t[0],P),k=Math.max(t[1],k)})),{top:A,bottom:e.h-k,left:M,right:e.w-P,w:P-M,h:k-A}}(e);break;case\"foldedCorner\":d=function(t){var e=t.extend,a=t.background,r=J(\"g\"),n=J(\"path\"),o=tt(\"adj\",t,.16667*Math.min(e.w,e.h)),c=[\"M0,0\",\"L\".concat(e.w,\",0\"),\"L\".concat(e.w,\",\").concat(e.h-o),\"L\".concat(e.w-o,\",\").concat(e.h),\"L\".concat(0,\",\").concat(e.h),\"Z\"].join(\" \");n.setAttribute(\"d\",c);var i=o*Math.cos(Math.PI/4)/Math.cos(Math.PI/6)*Math.cos(75/180*Math.PI),s=J(\"path\"),h=[\"M\".concat(e.w-o+i,\", \").concat(e.h-o+i),\"L\".concat(e.w,\",\").concat(e.h-o),\"L\".concat(e.w-o,\",\").concat(e.h),\"Z\"].join(\" \");return s.setAttribute(\"d\",h),\"solidFill\"===(null==a?void 0:a.type)&&s.setAttribute(\"fill\",S(a,{dark:.6})||\"transparent\"),r.appendChild(n),r.appendChild(s),r}(e),f=function(t){var e=t.extend,a=tt(\"adj\",t,.16667*Math.min(e.w,e.h));return{top:0,bottom:a,left:0,right:0,w:e.w,h:e.h-a}}(e);break;case\"rightArrowCallout\":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj1\",t,.25)*a,n=$(\"adj2\",t,.25)*a,o=$(\"adj3\",t,.25)*a,c=$(\"adj4\",t,.64977)*e.w,i=J(\"path\"),s=[\"M\".concat(0,\",\").concat(0),\"L\".concat(c,\",\").concat(0),\"L\".concat(c,\",\").concat(e.h/2-r/2),\"L\".concat(e.w-o,\",\").concat(e.h/2-r/2),\"L\".concat(e.w-o,\",\").concat(e.h/2-n),\"L\".concat(e.w,\",\").concat(e.h/2),\"L\".concat(e.w-o,\",\").concat(e.h/2+n),\"L\".concat(e.w-o,\",\").concat(e.h/2+r/2),\"L\".concat(c,\",\").concat(e.h/2+r/2),\"L\".concat(c,\",\").concat(e.h),\"L\".concat(0,\",\").concat(e.h),\"Z\"].join(\" \");return i.setAttribute(\"d\",s),i}(e),f=function(t){var e=t.extend,a=$(\"adj4\",t,.64977)*e.w;return{top:0,bottom:0,left:0,right:e.w-a,w:a,h:e.h}}(e);break;case\"leftArrowCallout\":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj1\",t,.25)*a,n=$(\"adj2\",t,.25)*a,o=$(\"adj3\",t,.25)*a,c=$(\"adj4\",t,.64977)*e.w,i=J(\"path\"),s=[\"M\".concat(0,\",\").concat(e.h/2),\"L\".concat(o,\",\").concat(e.h/2-n),\"L\".concat(o,\",\").concat(e.h/2-r/2),\"L\".concat(e.w-c,\",\").concat(e.h/2-r/2),\"L\".concat(e.w-c,\",\").concat(0),\"L\".concat(e.w,\",\").concat(0),\"L\".concat(e.w,\",\").concat(e.h),\"L\".concat(e.w-c,\",\").concat(e.h),\"L\".concat(e.w-c,\",\").concat(e.h/2+r/2),\"L\".concat(o,\",\").concat(e.h/2+r/2),\"L\".concat(o,\",\").concat(e.h/2+n),\"Z\"].join(\" \");return i.setAttribute(\"d\",s),i}(e),f=function(t){var e=t.extend,a=$(\"adj4\",t,.64977)*e.w;return{top:0,bottom:0,left:e.w-a,right:0,w:a,h:e.h}}(e);break;case\"upArrowCallout\":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj1\",t,.25)*a,n=$(\"adj2\",t,.25)*a,o=$(\"adj3\",t,.25)*a,c=$(\"adj4\",t,.64977)*e.h,i=J(\"path\"),s=[\"M\".concat(0,\",\").concat(e.h-c),\"L\".concat(e.w/2-r/2,\",\").concat(e.h-c),\"L\".concat(e.w/2-r/2,\",\").concat(o),\"L\".concat(e.w/2-n,\",\").concat(o),\"L\".concat(e.w/2,\",\").concat(0),\"L\".concat(e.w/2+n,\",\").concat(o),\"L\".concat(e.w/2+r/2,\",\").concat(o),\"L\".concat(e.w/2+r/2,\",\").concat(e.h-c),\"L\".concat(e.w,\",\").concat(e.h-c),\"L\".concat(e.w,\",\").concat(e.h),\"L\".concat(0,\",\").concat(e.h),\"Z\"].join(\" \");return i.setAttribute(\"d\",s),i}(e),f=function(t){var e=t.extend,a=$(\"adj4\",t,.64977)*e.h;return{top:e.h-a,bottom:0,left:0,right:0,w:e.w,h:a}}(e);break;case\"downArrowCallout\":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj1\",t,.25)*a,n=$(\"adj2\",t,.25)*a,o=$(\"adj3\",t,.25)*a,c=$(\"adj4\",t,.64977)*e.h,i=J(\"path\"),s=[\"M\".concat(0,\",\").concat(0),\"L\".concat(e.w,\",\").concat(0),\"L\".concat(e.w,\",\").concat(c),\"L\".concat(e.w/2+r/2,\",\").concat(c),\"L\".concat(e.w/2+r/2,\",\").concat(e.h-o),\"L\".concat(e.w/2+n,\",\").concat(e.h-o),\"L\".concat(e.w/2,\",\").concat(e.h),\"L\".concat(e.w/2-n,\",\").concat(e.h-o),\"L\".concat(e.w/2-r/2,\",\").concat(e.h-o),\"L\".concat(e.w/2-r/2,\",\").concat(c),\"L\".concat(0,\",\").concat(c),\"Z\"].join(\" \");return i.setAttribute(\"d\",s),i}(e),f=function(t){var e=t.extend,a=$(\"adj4\",t,.64977)*e.h;return{top:0,bottom:e.h-a,left:0,right:0,w:e.w,h:a}}(e);break;case\"leftRightArrowCallout\":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj1\",t,.25)*a,n=$(\"adj2\",t,.25)*a,o=$(\"adj3\",t,.25)*a,c=$(\"adj4\",t,.48123)*e.w,i=J(\"path\"),s=[\"M\".concat(0,\",\").concat(e.h/2),\"L\".concat(o,\",\").concat(e.h/2-n),\"L\".concat(o,\",\").concat(e.h/2-r/2),\"L\".concat(e.w/2-c/2,\",\").concat(e.h/2-r/2),\"L\".concat(e.w/2-c/2,\",\").concat(0),\"L\".concat(e.w/2+c/2,\",\").concat(0),\"L\".concat(e.w/2+c/2,\",\").concat(e.h/2-r/2),\"L\".concat(e.w-o,\",\").concat(e.h/2-r/2),\"L\".concat(e.w-o,\",\").concat(e.h/2-n),\"L\".concat(e.w,\",\").concat(e.h/2),\"L\".concat(e.w-o,\",\").concat(e.h/2+n),\"L\".concat(e.w-o,\",\").concat(e.h/2+r/2),\"L\".concat(e.w/2+c/2,\",\").concat(e.h/2+r/2),\"L\".concat(e.w/2+c/2,\",\").concat(e.h),\"L\".concat(e.w/2-c/2,\",\").concat(e.h),\"L\".concat(e.w/2-c/2,\",\").concat(e.h/2+r/2),\"L\".concat(o,\",\").concat(e.h/2+r/2),\"L\".concat(o,\",\").concat(e.h/2+n),\"Z\"].join(\" \");return i.setAttribute(\"d\",s),i}(e),f=function(t){var e=t.extend,a=$(\"adj4\",t,.48123)*e.w;return{top:0,bottom:0,left:e.w/2-a/2,right:e.w/2-a/2,w:a,h:e.h}}(e);break;case\"quadArrowCallout\":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$(\"adj1\",t,.18515)*a,n=$(\"adj2\",t,.18515)*a,o=$(\"adj3\",t,.18515)*a,c=$(\"adj4\",t,.48123)*e.w,i=$(\"adj4\",t,.48123)*e.h,s=J(\"path\"),h=[\"M\".concat(0,\",\").concat(e.h/2),\"L\".concat(o,\",\").concat(e.h/2-n),\"L\".concat(o,\",\").concat(e.h/2-r/2),\"L\".concat(e.w/2-c/2,\",\").concat(e.h/2-r/2),\"L\".concat(e.w/2-c/2,\",\").concat(e.h/2-i/2),\"L\".concat(e.w/2-r/2,\",\").concat(e.h/2-i/2),\"L\".concat(e.w/2-r/2,\",\").concat(o),\"L\".concat(e.w/2-n,\",\").concat(o),\"L\".concat(e.w/2,\",\").concat(0),\"L\".concat(e.w/2+n,\",\").concat(o),\"L\".concat(e.w/2+r/2,\",\").concat(o),\"L\".concat(e.w/2+r/2,\",\").concat(e.h/2-i/2),\"L\".concat(e.w/2+c/2,\",\").concat(e.h/2-i/2),\"L\".concat(e.w/2+c/2,\",\").concat(e.h/2-r/2),\"L\".concat(e.w-o,\",\").concat(e.h/2-r/2),\"L\".concat(e.w-o,\",\").concat(e.h/2-n),\"L\".concat(e.w,\",\").concat(e.h/2),\"L\".concat(e.w-o,\",\").concat(e.h/2+n),\"L\".concat(e.w-o,\",\").concat(e.h/2+r/2),\"L\".concat(e.w/2+c/2,\",\").concat(e.h/2+r/2),\"L\".concat(e.w/2+c/2,\",\").concat(e.h/2+i/2),\"L\".concat(e.w/2+r/2,\",\").concat(e.h/2+i/2),\"L\".concat(e.w/2+r/2,\",\").concat(e.h-o),\"L\".concat(e.w/2+n,\",\").concat(e.h-o),\"L\".concat(e.w/2,\",\").concat(e.h),\"L\".concat(e.w/2-n,\",\").concat(e.h-o),\"L\".concat(e.w/2-r/2,\",\").concat(e.h-o),\"L\".concat(e.w/2-r/2,\",\").concat(e.h/2+i/2),\"L\".concat(e.w/2-c/2,\",\").concat(e.h/2+i/2),\"L\".concat(e.w/2-c/2,\",\").concat(e.h/2+r/2),\"L\".concat(o,\",\").concat(e.h/2+r/2),\"L\".concat(o,\",\").concat(e.h/2+n),\"Z\"].join(\" \");return s.setAttribute(\"d\",h),s}(e),f=function(t){var e=t.extend,a=$(\"adj4\",t,.48123)*e.w,r=$(\"adj4\",t,.48123)*e.h;return{top:e.h/2-r/2,bottom:e.h/2-r/2,left:e.w/2-a/2,right:e.w/2-a/2,w:a,h:r}}(e)}if(d){y?\"blipFill\"===(null==o?void 0:o.type)?function(t,e,a){var r=t.background,n=void 0===r?{}:r,o=t.extend,c=n.base64,i=n.alpha,s=n.fillRect,h=void 0===s?{}:s,l=h.b,p=void 0===l?0:l,d=h.t,u=void 0===d?0:d,f=h.l,y=void 0===f?0:f,v=h.r,w=void 0===v?0:v,b=J(\"defs\"),m=J(\"pattern\");m.setAttribute(\"id\",\"background_\"+t.uuid),m.setAttribute(\"patternUnits\",\"userSpaceOnUse\"),m.setAttribute(\"width\",o.w+\"\"),m.setAttribute(\"height\",o.h+\"\");var g=J(\"image\");g.setAttribute(\"href\",c),g.setAttribute(\"preserveAspectRatio\",\"none\");var x=o.w*y,L=o.h*u,M=o.w*(1-y-w),A=o.h*(1-u-p);g.setAttribute(\"width\",M+\"\"),g.setAttribute(\"height\",A+\"\"),g.setAttribute(\"x\",x+\"\"),g.setAttribute(\"y\",L+\"\"),\"number\"==typeof i&&g.setAttribute(\"opacity\",i+\"\"),m.appendChild(g),b.appendChild(m),e.appendChild(b),a.setAttribute(\"fill\",\"url(#background_\".concat(t.uuid,\")\"))}(e,u,d):\"gradFill\"===(null==o?void 0:o.type)?function(t,e,a){var r=t.background,n=void 0===r?{}:r;t.extend;var o=n.gsList,c=n.lin,i=n.path,s=n.tileRect,h=void 0===s?{}:s,l=J(\"defs\"),p=J(\"circle\"===i?\"radialGradient\":\"linearGradient\");p.setAttribute(\"id\",\"background_grad_fill_\"+t.uuid);var d=o||[];if(d.sort((function(t,e){return t.pos-e.pos})),d.forEach((function(t){var e=J(\"stop\");e.setAttribute(\"offset\",\"\".concat(100*t.pos,\"%\")),e.setAttribute(\"stop-color\",S(t.color)),p.appendChild(e)})),\"circle\"===i){var u=h.r,f=h.l,y=h.t,v=h.b;-1===u?p.setAttribute(\"cx\",\"100%\"):-1===f&&p.setAttribute(\"cx\",\"0%\"),-1===y?p.setAttribute(\"cy\",\"0%\"):-1===v&&p.setAttribute(\"cy\",\"100%\")}else(null==c?void 0:c.ang)&&p.setAttribute(\"gradientTransform\",\"rotate(\".concat(c.ang,\")\"));l.appendChild(p),e.appendChild(l),a.setAttribute(\"fill\",\"url(#background_grad_fill_\".concat(t.uuid,\")\"))}(e,u,d):d.setAttribute(\"fill\",S(o)||\"transparent\"):d.setAttribute(\"fill\",\"transparent\"),n.width?(d.setAttribute(\"stroke-width\",n.width+\"px\"),d.setAttribute(\"stroke\",S(n.color)||\"transparent\")):d.setAttribute(\"stroke-width\",\"0\"),d.setAttribute(\"stroke-dasharray\",function(t){return t&&\"solid\"!==t.type?({sysDot:[1,1],sysDash:[3,1],dash:[4,3],dashDot:[4,3,1,3],lgDash:[8,3],lgDashDot:[8,3,1,3],lgDashDotDot:[8,3,1,3,1,3]}[t.type]||[]).map((function(e){return e*t.width})).join(\",\"):\"\"}(n));d.setAttribute(\"stroke-linecap\",n.cap&&{sq:\"square\",rnd:\"round\",flat:\"butt\"}[n.cap]||\"square\"),d.setAttribute(\"stroke-linejoin\",n.lineJoin||\"round\"),\"miter\"===n.lineJoin&&d.setAttribute(\"stroke-miterlimit\",n.miterLim+\"\"),u.appendChild(d)}var v=[];i&&v.push(\"scaleX(-1)\"),s&&v.push(\"scaleY(-1)\"),u.style.setProperty(\"transform\",v.join(\" \")),h.appendChild(u);var w=function(e,a,r){var n;void 0===r&&(r=!1);var o=e.inheritProps,c=e.props,i=e.paragraphs;if(!i||0===i.length)return null;var s=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},o),c),h=document.createElement(\"div\");switch(h.className=\"text-wrapper\",h.style.boxSizing=\"border-box\",h.style.position=\"absolute\",h.style.left=a.left+\"px\",\"eaVert\"===s.vert?(h.style.writingMode=\"vertical-rl\",h.style.height=a.h+\"px\"):h.style.width=a.w+\"px\",s.anchor){case\"b\":h.style.bottom=a.bottom+\"px\";break;case\"t\":h.style.top=a.top+\"px\";break;case\"ctr\":h.style.top=a.top+a.h/2+\"px\",h.style.transform=\"translateY(-50%)\";break;default:r?h.style.top=a.top+\"px\":(h.style.top=a.top+a.h/2+\"px\",h.style.transform=\"translateY(-50%)\")}var l=[s.hasOwnProperty(\"tIns\")?Math.floor(s.tIns)+\"px\":\"3px\",s.hasOwnProperty(\"rIns\")?Math.floor(s.rIns)+\"px\":\"5px\",s.hasOwnProperty(\"bIns\")?Math.floor(s.bIns)+\"px\":\"3px\",s.hasOwnProperty(\"lIns\")?Math.floor(s.lIns)+\"px\":\"5px\"];h.style.padding=l.join(\" \");for(var p={},d=0,u=function(e){var a=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},e.inheritProps),e.props),r=void 0;if(a.buAutoNum){var o=a.level?+a.level:0;p[o]||(p[o]=0),(null===(n=e.rows)||void 0===n?void 0:n.length)&&p[o]++,Object.keys(p).forEach((function(t){+t>o&&(p[t]=0)})),r=U(e,p[o],{isFirst:0===d,isLast:i.length-1===d,bodyProps:s})}else r=U(e,0,{isFirst:0===d,isLast:i.length-1===d,bodyProps:s});h.appendChild(r),d++},f=0,y=i;f<y.length;f++)u(y[f]);return h}(e.textBody,f,e.isTextBox);return w&&h.appendChild(w),c&&h.style.setProperty(\"transform\",\"rotate(\".concat(c,\"deg)\")),h}function ft(t){var e=document.createElement(\"div\"),a=t.extend;t.chExtend;var r=t.offset;t.chOffset;var n=t.flipV,o=t.flipH,c=t.rotate,i=r.x,s=r.y,h=a.w,l=a.h;e.className=\"group\",e.style.position=\"absolute\",e.style.left=i+\"px\",e.style.top=s+\"px\",e.style.width=h+\"px\",e.style.height=l+\"px\";var p=[];o&&p.push(\"scaleX(-1)\"),n&&p.push(\"scaleY(-1)\"),c&&p.push(\"rotate(\".concat(c,\"deg)\")),e.style.transformOrigin=\"center center\",e.style.transform=p.join(\" \");for(var d=0;d<t.nodes.length;d++){var u=t.nodes[d],f=void 0;u instanceof T?f=V(u):u instanceof R?f=ut(u):u instanceof O&&(f=ft(u)),e.appendChild(f)}return e}var yt={};function vt(t,e){yt[t]||(yt[t]=[]),yt[t].push(e)}function wt(t,e){if(yt[t])for(var a=0;a<yt[t].length;a++)yt[t][a](e)}function bt(t,e){yt?yt[t]&&(yt[t]=yt[t].filter((function(t){return t!==e}))):yt[t]=[]}var mt=function(){function e(t,e,a){this.scale=1,this.wrapper=t,this.pptx=e,this.options=a,this._calcScaleAndRenderPort()}return e.prototype._calcScaleAndRenderPort=function(){var t=this.options.viewPort.width/this.pptx.width;this.scale=t;var e=this.options.viewPort.width,a=this.pptx.height*this.scale;this.renderPort={width:e,height:a,left:0,top:0}},e.prototype.renderSlide=function(t){var e=this.pptx.slides[t],a=document.createElement(\"div\");a.classList.add(\"pptx-preview-slide-wrapper\"),a.classList.add(\"pptx-preview-slide-wrapper-\".concat(t)),a.style.setProperty(\"width\",this.renderPort.width+\"px\"),a.style.setProperty(\"height\",this.renderPort.height+\"px\"),a.style.setProperty(\"position\",\"slide\"===this.options.mode?\"absolute\":\"relative\"),\"slide\"===this.options.mode&&a.style.setProperty(\"top\",(this.options.viewPort.height-this.renderPort.height)/2+\"px\"),a.style.margin=\"0 auto 10px\",a.style.setProperty(\"background\",\"#fff\"),a.style.setProperty(\"overflow\",\"hidden\"),this._renderBackground(e,a),this._renderSlideMaster(e.slideMaster,a),this._renderSlideLayout(e.slideLayout,a),this._renderSlide(e,a),this.wrapper.append(a)},e.prototype._renderSlideMaster=function(t,e){var a=document.createElement(\"div\");a.classList.add(\"slide-master-wrapper\"),a.style.setProperty(\"position\",\"absolute\"),a.style.setProperty(\"left\",\"0\"),a.style.setProperty(\"top\",\"0\"),a.style.setProperty(\"width\",this.pptx.width+\"px\"),a.style.setProperty(\"height\",this.pptx.height+\"px\"),a.style.setProperty(\"transform\",\"scale(\".concat(this.scale,\")\")),a.style.setProperty(\"transform-origin\",\"0 0\");var r=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__spreadArray)([],t.nodes,!0).filter((function(t){return t.userDrawn}));r.sort((function(t,e){return t.order>e.order?1:-1}));for(var o=0;o<r.length;o++){var c=this._renderNode(r[o]);c&&a.append(c)}e.append(a)},e.prototype._renderSlideLayout=function(t,e){var a=document.createElement(\"div\");a.classList.add(\"slide-layout-wrapper\"),a.style.setProperty(\"position\",\"absolute\"),a.style.setProperty(\"left\",\"0\"),a.style.setProperty(\"top\",\"0\"),a.style.setProperty(\"width\",this.pptx.width+\"px\"),a.style.setProperty(\"height\",this.pptx.height+\"px\"),a.style.setProperty(\"transform\",\"scale(\".concat(this.scale,\")\")),a.style.setProperty(\"transform-origin\",\"0 0\");var r=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__spreadArray)([],t.nodes,!0).filter((function(t){return t.userDrawn}));r.sort((function(t,e){return t.order>e.order?1:-1}));for(var o=0;o<r.length;o++){var c=this._renderNode(r[o]);c&&a.append(c)}e.append(a)},e.prototype._renderSlide=function(t,e){var a=document.createElement(\"div\");a.classList.add(\"slide-wrapper\"),a.style.setProperty(\"position\",\"absolute\"),a.style.setProperty(\"left\",\"0\"),a.style.setProperty(\"top\",\"0\"),a.style.setProperty(\"width\",this.pptx.width+\"px\"),a.style.setProperty(\"height\",this.pptx.height+\"px\"),a.style.setProperty(\"transform\",\"scale(\".concat(this.scale,\")\")),a.style.setProperty(\"transform-origin\",\"0 0\");var r=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__spreadArray)([],t.nodes,!0);r.sort((function(t,e){return t.order>e.order?1:-1}));for(var o=0;o<r.length;o++){var c=this._renderNode(r[o]);c&&a.append(c)}e.append(a)},e.prototype._renderNode=function(e){return e instanceof T?V(e):e instanceof R?ut(e):e instanceof O?ft(e):e instanceof E?function(t){var e=document.createElement(\"div\"),a=t.extend,r=t.offset,n=t.flipV,o=t.flipH,c=t.rotate,i=r.x,s=r.y,h=a.w,l=a.h;e.className=\"smart-chart-diagram\",e.style.position=\"absolute\",e.style.left=i+\"px\",e.style.top=s+\"px\",e.style.width=h+\"px\",e.style.height=l+\"px\";var p=[];o&&p.push(\"scaleX(-1)\"),n&&p.push(\"scaleY(-1)\"),c&&p.push(\"rotate(\".concat(c,\"deg)\")),e.style.transformOrigin=\"center center\",e.style.transform=p.join(\" \");for(var d=0;d<t.nodes.length;d++){var u=t.nodes[d],f=void 0;u instanceof T?f=V(u):u instanceof R&&(f=ut(u)),e.appendChild(f)}return e}(e):e instanceof _?function(e){var a=e.extend,r=e.offset,n=e.tr,o=e.tableGrid.gridCol,c=document.createElement(\"div\");c.style.position=\"absolute\",c.style.left=r.x+\"px\",c.style.top=r.y+\"px\",c.style.width=a.w+\"px\",c.style.height=a.h+\"px\";var i=document.createElement(\"table\");return i.style.borderCollapse=\"collapse\",n.forEach((function(e){var a=e.props,r=e.td,n=a.height,c=document.createElement(\"tr\");c.style.height=n+\"px\",r.forEach((function(e,a){var r,n,i=e.props,s=e.inheritTcStyle,h=e.inheritTcTxStyle,l=e.paragraphs;if(!i.vMerge&&!i.hMerge){var p=document.createElement(\"td\");p.style.width=((null===(r=o[a])||void 0===r?void 0:r.width)||30)+\"px\",i.rowSpan&&p.setAttribute(\"rowspan\",i.rowSpan+\"\"),i.gridSpan&&p.setAttribute(\"colspan\",i.gridSpan+\"\");var d=i.background||s.background;d&&(p.style.background=S(d));var u=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},s.border),i.border),f=function(t){return t?t.toLowerCase().includes(\"dash\")?\"dashed\":t.toLowerCase().includes(\"dot\")?\"dotted\":\"solid\":\"solid\"};u.bottom&&(p.style.borderBottom=\"\".concat(u.bottom.width,\"px \").concat(f(u.bottom.type),\" \").concat(S(u.bottom.color))),u.top&&(p.style.borderTop=\"\".concat(u.top.width,\"px \").concat(f(u.top.type),\" \").concat(S(u.top.color))),u.left&&(p.style.borderLeft=\"\".concat(u.left.width,\"px \").concat(f(u.left.type),\" \").concat(S(u.left.color))),u.right&&(p.style.borderRight=\"\".concat(u.right.width,\"px \").concat(f(u.right.type),\" \").concat(S(u.right.color)));var y=document.createElement(\"div\");switch(y.className=\"text-wrapper\",y.style.boxSizing=\"border-box\",y.style.width=\"100%\",y.style.height=\"100%\",y.style.overflow=\"hidden\",y.style.display=\"flex\",y.style.flexDirection=\"column\",i.anchor){case\"b\":y.style.justifyContent=\"flex-end\";break;case\"t\":y.style.justifyContent=\"flex-start\";break;case\"ctr\":y.style.justifyContent=\"center\"}var v=[i.hasOwnProperty(\"marT\")?Math.floor(i.marT)+\"px\":\"3px\",i.hasOwnProperty(\"marR\")?Math.floor(i.marR-1)+\"px\":\"5px\",i.hasOwnProperty(\"marB\")?Math.floor(i.marB)+\"px\":\"3px\",i.hasOwnProperty(\"marL\")?Math.floor(i.marL-1)+\"px\":\"5px\"];y.style.padding=v.join(\" \");for(var w=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},i),b={},m=l.map((function(e){return (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},e),{props:(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({align:\"l\"},e.props),inheritRProps:(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},e.inheritRProps),h)})})),g=0,x=function(e){var a=(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_4__.__assign)({},e.inheritProps),e.props),r=void 0;if(a.buAutoNum){var o=a.level?+a.level:0;b[o]||(b[o]=0),(null===(n=e.rows)||void 0===n?void 0:n.length)&&b[o]++,Object.keys(b).forEach((function(t){+t>o&&(b[t]=0)})),r=U(e,b[o],{isFirst:0===g,isLast:l.length-1===g,bodyProps:w})}else r=U(e,0,{isFirst:0===g,isLast:l.length-1===g,bodyProps:w});y.appendChild(r),g++},L=0,M=m;L<M.length;L++)x(M[L]);p.append(y),c.append(p)}})),i.append(c)})),c.append(i),c}(e):e instanceof N?function(t){var e=document.createElement(\"div\"),a=t.extend,r=t.offset,n=t.flipV,o=t.flipH,c=t.rotate,i=t.options,s=r.x,l=r.y,p=a.w,d=a.h;e.className=\"chart-node\",e.style.position=\"absolute\",e.style.left=s+\"px\",e.style.top=l+\"px\",e.style.width=p+\"px\",e.style.height=d+\"px\";var u=[];return o&&u.push(\"scaleX(-1)\"),n&&u.push(\"scaleY(-1)\"),c&&u.push(\"rotate(\".concat(c,\"deg)\")),e.style.transformOrigin=\"center center\",e.style.transform=u.join(\" \"),setTimeout((function(){var t=echarts__WEBPACK_IMPORTED_MODULE_2__.init(e,null,{renderer:\"svg\"});t.setOption(i),vt(\"destroy\",(function(){t&&t.dispose(),t&&(t=null)})),vt(\"removeSlide\",(function(){t&&t.dispose(),t&&(t=null)}))}),0),e}(e):void 0},e.prototype._renderBackground=function(t,e){var a,r=document.createElement(\"div\");r.classList.add(\"slide-background\"),r.style.setProperty(\"position\",\"absolute\"),r.style.setProperty(\"left\",\"0\"),r.style.setProperty(\"top\",\"0\"),r.style.setProperty(\"width\",\"100%\"),r.style.setProperty(\"height\",\"100%\");var n=t.background;if(\"none\"===n.type&&(n=t.slideLayout.background),\"none\"===n.type&&(n=t.slideMaster.background),\"blipFill\"===n.type){var o=n,c=o.base64,i=o.alpha,s=o.fillRect,h=void 0===s?{}:s,l=h.b,p=void 0===l?0:l,d=h.t,u=void 0===d?0:d,f=h.l,y=void 0===f?0:f,v=h.r,w=void 0===v?0:v,b=this.renderPort.width*y,m=this.renderPort.height*u,g=this.renderPort.width*(1-y-w),x=this.renderPort.height*(1-u-p);r.style.backgroundImage=\"url(\".concat(c,\")\"),r.style.backgroundSize=\"\".concat(g,\" \").concat(x),r.style.backgroundPosition=\"\".concat(b,\"px \").concat(m,\"px\"),i&&(r.style.opacity=i+\"\"),r.style.backgroundRepeat=\"no-repeat\"}else if(\"solidFill\"===n.type){var L=S(t.background)||S(t.slideLayout.background)||S(t.slideMaster.background);L?r.style.setProperty(\"background\",L):r.style.setProperty(\"background\",\"#fff\")}else if(\"gradFill\"===n.type)if(\"circle\"===n.path){var M=n.tileRect||{},A=(p=M.b,u=M.t,y=M.l,\"radial-gradient(circle at \");-1===(w=M.r)?A+=\" right\":-1===y&&(A+=\" left\"),-1===u?A+=\" top\":-1===p&&(A+=\" bottom\"),p||u||y||w||(A+=\" center\"),A+=\",\",A+=n.gsList.map((function(t){return\"\".concat(S(t.color),\" \").concat(100*t.pos+\"%\")})).join(\",\"),r.style.setProperty(\"background\",A)}else{var P=(null===(a=null==n?void 0:n.lin)||void 0===a?void 0:a.ang)||0;A=\"linear-gradient(\".concat(P+90,\"deg,\");A+=n.gsList.map((function(t){return\"\".concat(S(t.color),\" \").concat(100*t.pos+\"%\")})).join(\",\"),r.style.setProperty(\"background\",A)}e.append(r)},e}(),gt=function(){function t(t,e){this.currentIndex=-1,this.dom=t,this.options=e,this._renderWrapper()}return Object.defineProperty(t.prototype,\"slideCount\",{get:function(){var t,e;return null===(e=null===(t=this.pptx)||void 0===t?void 0:t.slides)||void 0===e?void 0:e.length},enumerable:!1,configurable:!0}),t.prototype._renderWrapper=function(){var t=document.createElement(\"div\");t.classList.add(\"pptx-preview-wrapper\"),t.setAttribute(\"position\",\"relative\"),t.style.setProperty(\"background\",\"#000\"),t.style.setProperty(\"width\",this.options.width+\"px\"),this.options.height&&t.style.setProperty(\"height\",this.options.height+\"px\"),t.style.setProperty(\"position\",\"relative\"),t.style.setProperty(\"margin\",\"0 auto\"),this.options.height&&t.style.setProperty(\"overflow-y\",\"auto\"),this.dom.append(t),this.wrapper=t},t.prototype.renderNextButton=function(){var t=document.createElement(\"div\");t.classList.add(\"pptx-preview-wrapper-next\"),t.style.setProperty(\"position\",\"absolute\"),t.style.setProperty(\"bottom\",\"20px\"),t.style.setProperty(\"right\",\"80px\"),t.style.setProperty(\"z-index\",\"100\"),t.style.setProperty(\"cursor\",\"pointer\"),t.style.setProperty(\"width\",\"40px\"),t.style.setProperty(\"height\",\"40px\"),t.style.setProperty(\"background\",\"#666666\"),t.style.setProperty(\"border-radius\",\"100%\");var e=document.createElement(\"div\");return e.style.setProperty(\"width\",\"10px\"),e.style.setProperty(\"height\",\"10px\"),e.style.setProperty(\"border-left\",\"2px solid #fff\"),e.style.setProperty(\"border-bottom\",\"2px solid #fff\"),e.style.setProperty(\"transform\",\"rotate(225deg)\"),e.style.setProperty(\"position\",\"absolute\"),e.style.setProperty(\"top\",\"15px\"),e.style.setProperty(\"left\",\"15px\"),t.append(e),t},t.prototype.renderPreButton=function(){var t=document.createElement(\"div\");t.classList.add(\"pptx-preview-wrapper-next\"),t.style.setProperty(\"position\",\"absolute\"),t.style.setProperty(\"bottom\",\"20px\"),t.style.setProperty(\"right\",\"140px\"),t.style.setProperty(\"z-index\",\"100\"),t.style.setProperty(\"cursor\",\"pointer\"),t.style.setProperty(\"width\",\"40px\"),t.style.setProperty(\"height\",\"40px\"),t.style.setProperty(\"background\",\"#666666\"),t.style.setProperty(\"border-radius\",\"100%\");var e=document.createElement(\"div\");return e.style.setProperty(\"width\",\"10px\"),e.style.setProperty(\"height\",\"10px\"),e.style.setProperty(\"border-left\",\"2px solid #fff\"),e.style.setProperty(\"border-bottom\",\"2px solid #fff\"),e.style.setProperty(\"transform\",\"rotate(45deg)\"),e.style.setProperty(\"position\",\"absolute\"),e.style.setProperty(\"top\",\"15px\"),e.style.setProperty(\"left\",\"15px\"),t.append(e),t},t.prototype.updatePagination=function(){var t=this.wrapper.querySelector(\".pptx-preview-wrapper-pagination\");t&&(t.innerText=\"\".concat(this.currentIndex+1,\"/\").concat(this.slideCount))},t.prototype.renderPagination=function(t){var e=document.createElement(\"div\");e.classList.add(\"pptx-preview-wrapper-pagination\"),e.innerText=\"\".concat(this.currentIndex+1,\"/\").concat(this.slideCount),e.style.setProperty(\"position\",\"absolute\"),e.style.setProperty(\"bottom\",\"33px\"),e.style.setProperty(\"right\",\"20px\"),e.style.setProperty(\"color\",\"#666666\"),e.style.setProperty(\"font-size\",\"14px\"),e.style.setProperty(\"z-index\",\"100\"),t.append(e)},t.prototype.removeCurrentSlide=function(){var t=this.wrapper.querySelector(\".pptx-preview-slide-wrapper-\".concat(this.currentIndex));t&&this.wrapper.removeChild(t),wt(\"removeSlide\")},t.prototype.renderNextSlide=function(){this.removeCurrentSlide(),this.currentIndex=this.currentIndex<this.slideCount-1?this.currentIndex+1:0,this.htmlRender.renderSlide(this.currentIndex),this.updatePagination()},t.prototype.renderPreSlide=function(){this.removeCurrentSlide(),this.currentIndex=this.currentIndex>0?this.currentIndex-1:this.slideCount-1,this.htmlRender.renderSlide(this.currentIndex),this.updatePagination()},t.prototype._addPre=function(t){},t.prototype.preview=function(t){var e=this;return wt(\"destroy\"),bt(\"destroy\"),new Promise((function(a,r){e.wrapper.innerHTML=\"\";var n=e.pptx=new Q;n.load(t).then((function(){try{var t=e.htmlRender=new mt(e.wrapper,n,{viewPort:{width:e.options.width,height:e.options.height},mode:e.options.mode});if(\"slide\"===e.options.mode){var o=e.renderNextButton();o.onclick=function(){e.renderNextSlide()},e.wrapper.append(o);var c=e.renderPreButton();c.onclick=function(){e.renderPreSlide()},e.wrapper.append(c),e.renderPagination(e.wrapper),e._addPre(e.wrapper),e.currentIndex=0,t.renderSlide(0)}else for(var i=0;i<n.slides.length;i++)t.renderSlide(i);a(n)}catch(t){r(t)}})).catch((function(t){r(t)}))}))},t.prototype.load=function(t){var e=this;return wt(\"destroy\"),bt(\"destroy\"),new Promise((function(a,r){e.wrapper.innerHTML=\"\";var n=e.pptx=new Q;n.load(t).then((function(){try{e.htmlRender=new mt(e.wrapper,n,{viewPort:{width:e.options.width,height:e.options.height},mode:e.options.mode}),a(n)}catch(t){r(t)}})).catch((function(t){r(t)}))}))},t.prototype.renderSingleSlide=function(t){this.removeCurrentSlide(),this.currentIndex=t,this.htmlRender.renderSlide(this.currentIndex)},t.prototype.destroy=function(){wt(\"destroy\"),bt(\"destroy\")},t}();function xt(t,e){return new gt(t,e)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pptx-preview/dist/pptx-preview.es.js\n");

/***/ })

};
;