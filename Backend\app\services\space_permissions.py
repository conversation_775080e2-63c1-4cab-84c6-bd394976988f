"""Permission helpers for personal and shared spaces.

This module centralizes authorization logic for user spaces and documents,
offering low-level checks and higher-level gate functions that raise clear
HTTP exceptions suitable for FastAPI routes.
"""

import logging
from typing import Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import joinedload
from fastapi import HTTPException, status

from ..models.user import User
from ..models.user_space import UserSpace, SpaceType
from ..models.space_membership import SpaceMembership, SpaceRole
from ..models.knowledge_base import KnowledgeDocument

logger = logging.getLogger(__name__)


class SpacePermissionError(HTTPException):
    """HTTP 4xx error for space/document authorization failures."""

    def __init__(self, detail: str, status_code: int = status.HTTP_403_FORBIDDEN):
        super().__init__(status_code=status_code, detail=detail)


class SpacePermissions:
    """Static permission checks for spaces and documents.

    These checks do not raise; they only return booleans. Use the
    `require_*` helpers below to enforce and raise on failure.
    """

    @staticmethod
    async def can_view_space(user: User, space: UserSpace, db: AsyncSession) -> bool:
        """Return True if the user can view the space.

        Personal spaces: only the owner may view.
        Shared spaces: owner or any member may view.
        """
        if space.is_personal():
            return space.owner_id == user.id

        if space.owner_id == user.id:
            return True

        # Check membership for shared spaces
        result = await db.execute(
            select(SpaceMembership).where(
                SpaceMembership.space_id == space.id,
                SpaceMembership.user_id == user.id
            )
        )
        membership = result.scalars().first()

        return membership is not None

    @staticmethod
    async def can_edit_space(user: User, space: UserSpace, db: AsyncSession) -> bool:
        """Return True if the user can edit space metadata (name/description).

        Only the owner can edit both personal and shared spaces.
        """
        if space.is_personal():
            return space.owner_id == user.id

        return space.owner_id == user.id

    @staticmethod
    async def can_delete_space(user: User, space: UserSpace, db: AsyncSession) -> bool:
        """Return True if the user can delete the space (owner only)."""
        return space.owner_id == user.id

    @staticmethod
    async def can_upload_documents(user: User, space: UserSpace, db: AsyncSession) -> bool:
        """Return True if the user can upload documents to the space.

        Personal spaces: only the owner.
        Shared spaces: owner or any member may upload.
        """
        if space.is_personal():
            return space.owner_id == user.id

        if space.owner_id == user.id:
            return True

        # Members can upload in shared spaces
        result = await db.execute(
            select(SpaceMembership).where(
                SpaceMembership.space_id == space.id,
                SpaceMembership.user_id == user.id
            )
        )
        membership = result.scalars().first()

        return membership is not None

    @staticmethod
    async def can_query_documents(user: User, space: UserSpace, db: AsyncSession) -> bool:
        """Return True if the user can query documents in the space.

        Uses the same rules as viewing a space.
        """
        return await SpacePermissions.can_view_space(user, space, db)

    @staticmethod
    async def can_delete_document(
        user: User, space: UserSpace, document: KnowledgeDocument, db: AsyncSession
    ) -> bool:
        """Return True if the user can delete a specific document.

        Personal spaces: only the owner.
        Shared spaces: owner can delete any, members may delete documents they created.
        """
        if space.is_personal():
            return space.owner_id == user.id

        if space.owner_id == user.id:
            return True

        return document.created_by == user.id

    @staticmethod
    async def can_manage_members(user: User, space: UserSpace, db: AsyncSession) -> bool:
        """Return True if the user can add/remove members (shared spaces only)."""
        if space.is_personal():
            return False

        return space.owner_id == user.id

    @staticmethod
    def get_user_role_in_space(user: User, space: UserSpace,
                               db: AsyncSession) -> Optional[str]:
        """Return the user's role in the space: "admin", "member", or None.

        - Personal spaces: owner treated as "admin".
        - Shared spaces: owner => "admin", else membership role.
        
        Note: This is synchronous and uses in-memory data. For DB lookups, use require_space_access.
        """
        if space.is_personal():
            return "admin" if space.owner_id == user.id else None

        if space.owner_id == user.id:
            return "admin"

        # Use in-memory memberships if loaded (check __dict__ to avoid lazy load)
        if 'memberships' in space.__dict__ and space.memberships:
            for membership in space.memberships:
                if membership.user_id == user.id:
                    return membership.role
        
        return None

    @staticmethod
    async def get_accessible_spaces(
        user: User, db: AsyncSession, space_type: Optional[str] = None
    ) -> List[UserSpace]:
        """Return a list of active spaces the user can access (owned or member).

        Optionally filter by `space_type` ("personal" or "shared").
        
        Note: This method adds a dynamic `_document_count` attribute to each space
        to avoid lazy loading issues in async contexts.
        """
        # Create subquery for document count
        doc_count_subq = (
            select(func.count(KnowledgeDocument.id))
            .where(KnowledgeDocument.space_id == UserSpace.id)
            .correlate(UserSpace)
            .scalar_subquery()
        )
        
        # Base statement for active spaces with document count and eager-loaded owner
        stmt = (
            select(UserSpace, doc_count_subq.label('doc_count'))
            .where(UserSpace.is_active == True)
            .options(joinedload(UserSpace.owner))
        )

        if space_type:
            stmt = stmt.where(UserSpace.space_type == space_type)

        # Spaces owned by the user
        owned_stmt = stmt.where(UserSpace.owner_id == user.id)
        result = await db.execute(owned_stmt)
        owned_rows = result.all()
        owned_spaces = []
        for space, doc_count in owned_rows:
            space._document_count = doc_count
            owned_spaces.append(space)

        # Spaces where the user is a member (shared only)
        member_space_ids_stmt = select(SpaceMembership.space_id).where(
            SpaceMembership.user_id == user.id
        )

        member_stmt = stmt.where(
            UserSpace.id.in_(member_space_ids_stmt),
            UserSpace.space_type == SpaceType.shared.value
        )
        result = await db.execute(member_stmt)
        member_rows = result.all()
        member_spaces = []
        for space, doc_count in member_rows:
            space._document_count = doc_count
            member_spaces.append(space)

        # Combine and deduplicate by ID while preserving objects
        all_spaces = {space.id: space for space in owned_spaces + member_spaces}
        return list(all_spaces.values())


async def require_space_access(
    user: User, space_id: int, db: AsyncSession, permission: str = "view"
) -> UserSpace:
    """Fetch a space and enforce a specific permission.

    Args:
        user: Current user performing the action.
        space_id: Target space identifier.
        db: AsyncSession for database queries.
        permission: One of {"view", "edit", "delete", "upload", "query", "manage_members"}.

    Returns:
        The `UserSpace` if authorized.

    Raises:
        SpacePermissionError: If space is missing/inactive or permission denied.
        ValueError: If an unknown permission is provided.
    """

    # Treat inactive spaces as not found to avoid information leakage
    result = await db.execute(
        select(UserSpace).where(
            UserSpace.id == space_id,
            UserSpace.is_active == True
        )
    )
    space = result.scalars().first()
    if not space:
        raise SpacePermissionError("Space not found", status.HTTP_404_NOT_FOUND)

    # Map requested permission to the corresponding boolean check
    permission_checks = {
        "view": SpacePermissions.can_view_space,
        "edit": SpacePermissions.can_edit_space,
        "delete": SpacePermissions.can_delete_space,
        "upload": SpacePermissions.can_upload_documents,
        "query": SpacePermissions.can_query_documents,
        "manage_members": SpacePermissions.can_manage_members,
    }

    check_func = permission_checks.get(permission)
    if not check_func:
        raise ValueError(f"Unknown permission type: {permission}")

    if not await check_func(user, space, db):
        # Build a human-readable action for the error message
        action_names = {
            "view": "access", "edit": "edit", "delete": "delete", "upload":
            "upload documents to", "query": "query documents in", "manage_members":
            "manage members of"
        }
        action = action_names.get(permission, permission)
        raise SpacePermissionError(f"You don't have permission to {action} this space")

    return space


async def require_document_access(
    user: User,
    document_id: int,
    db: AsyncSession,
    permission: str = "view"
) -> Tuple[KnowledgeDocument, UserSpace]:
    """Fetch a document and its space, enforcing a document-level permission.

    Args:
        user: Current user performing the action.
        document_id: Target document identifier.
        db: AsyncSession for database queries.
        permission: Either "view" or "delete".

    Returns:
        Tuple of `(document, space)` if authorized.

    Raises:
        SpacePermissionError: If document/space is missing or access denied.
        ValueError: If an unknown permission is provided.
    """

    # Fetch the document eagerly with its associated space to avoid lazy-load in async context
    result = await db.execute(
        select(KnowledgeDocument)
        .options(joinedload(KnowledgeDocument.space))
        .where(KnowledgeDocument.id == document_id)
    )
    document = result.scalars().first()
    if not document:
        raise SpacePermissionError("Document not found", status.HTTP_404_NOT_FOUND)

    space = document.space
    if not space:
        raise SpacePermissionError(
            "Document space not found", status.HTTP_404_NOT_FOUND
        )

    if permission == "view":
        if not await SpacePermissions.can_view_space(user, space, db):
            raise SpacePermissionError(
                "You don't have permission to access this document"
            )
    elif permission == "delete":
        if not await SpacePermissions.can_delete_document(user, space, document, db):
            raise SpacePermissionError(
                "You don't have permission to delete this document"
            )
    else:
        raise ValueError(f"Unknown permission type: {permission}")

    return document, space
