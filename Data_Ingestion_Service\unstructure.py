import os
import shutil
from pathlib import Path
from typing import List
import pandas as pd

# Unstructured imports
from unstructured.partition.pdf import partition_pdf


class PDFContentExtractor:
    def __init__(self, pdf_path: str, max_characters: int = 10000, new_after_n_chars: int = 6000, combine_text_under_n_chars: int = 4000):
        self.pdf_path = pdf_path
        self.text_chunks = []
        self.max_characters = max_characters
        self.new_after_n_chars = new_after_n_chars
        self.combine_text_under_n_chars = combine_text_under_n_chars
        
    def get_text_chunks(self) -> List[str]:
        """Extract and return text chunks from PDF (minimum 100 characters each)"""
        print(f"Processing PDF: {self.pdf_path}")
        
        try:
            # Partition PDF with chunking strategy
            elements = partition_pdf(
                filename=self.pdf_path,
                strategy="hi_res",
                infer_table_structure=True,
                extract_images_in_pdf=False,
                extract_image_block_types=["Image", "Table"],
                chunking_strategy="by_title",
                max_characters=self.max_characters,
                new_after_n_chars=self.new_after_n_chars,
                combine_text_under_n_chars=self.combine_text_under_n_chars,
            )
            
            print(f"Found {len(elements)} elements")
            self._process_text_elements(elements)
            
        except Exception as e:
            print(f"Error processing PDF: {str(e)}")
            # Fallback to basic strategy
            try:
                elements = partition_pdf(
                    filename=self.pdf_path,
                    strategy="fast"
                )
                self._process_text_elements(elements)
            except Exception as e2:
                print(f"Fallback also failed: {str(e2)}")
                return []
        
        # Ensure minimum chunk size of 100 characters
        self._merge_small_chunks()
        
        print(f"Created {len(self.text_chunks)} text chunks")
        return self.text_chunks
    
    def _process_text_elements(self, elements):
        """Process elements and extract text content"""
        for element in elements:
            # Handle chunked elements
            if hasattr(element, 'metadata') and hasattr(element.metadata, 'orig_elements'):
                for orig_element in element.metadata.orig_elements:
                    self._extract_text_from_element(orig_element)
            else:
                # Handle single elements
                self._extract_text_from_element(element)
    
    def _extract_text_from_element(self, element):
        """Extract text from a single element"""
        category = element.category
        
        # Only process text-based elements
        if category in [
            "Text", "Title", "NarrativeText", "Header", "Footer", 
            "ListItem", "FigureCaption", "UncategorizedText", 
            "Address", "Formula"
        ]:
            text_content = element.text.strip()
            if text_content:  # Only add non-empty text
                self.text_chunks.append(text_content)
    
    def _merge_small_chunks(self):
        """Merge chunks smaller than 100 characters with the next chunk"""
        if not self.text_chunks:
            return
        
        merged_chunks = []
        current_chunk = ""
        
        for chunk in self.text_chunks:
            current_chunk += " " + chunk if current_chunk else chunk
            
            # If current chunk is >= 500 characters, add it to merged_chunks
            if len(current_chunk) >= 500:
                merged_chunks.append(current_chunk.strip())
                current_chunk = ""
        
        # Add any remaining chunk (even if < 500 chars)
        if current_chunk.strip():
            if merged_chunks:
                # Merge with the last chunk if possible
                merged_chunks[-1] += " " + current_chunk.strip()
            else:
                # If it's the only chunk, keep it even if < 500 chars
                merged_chunks.append(current_chunk.strip())
        
        self.text_chunks = merged_chunks

