version: "3.9"
services:
  emqx:
    image: emqx/emqx:5.8.7
    container_name: emqx
    restart: unless-stopped
    environment:
      EMQX_DASHBOARD__DEFAULT_USERNAME: admin
      EMQX_DASHBOARD__DEFAULT_PASSWORD: admin@123
      EMQX_CLUSTER__DISCOVERY_STRATEGY: singleton
      EMQX_NODE__COOKIE: e8361c88f8713629b2e3db728d45bbfa529e331622efc5d801686ef344d94a67
      EMQX_LISTENERS__SSL__DEFAULT__ENABLE: "false"
      EMQX_LISTENERS__WSS__DEFAULT__ENABLE: "false"
    volumes:
      - ./emqx/etc/base.hocon:/opt/emqx/etc/base.hocon:ro
      #- ./emqx/etc/certs:/opt/emqx/etc/certs:ro
      - ./emqx/data:/opt/emqx/data
      - ./emqx/log:/opt/emqx/log
    ports:
      - "1883:1883"   # MQTT TCP
      - "8085:8083"   # MQTT over WebSocket (path /mqtt)
      #- "8084:8084"   # MQTT over WSS (enable when certs are set)
      - "18083:18083" # Dashboard
    networks:
      - workplace_slm
      - techsarthai_default

networks:
  techsarthai_default:
    external: true
  workplace_slm:
    driver: bridge