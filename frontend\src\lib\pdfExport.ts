export const generateSingleQueryPDF = (
  query: string,
  response: string,
  agentName: string,
  timestamp?: string
): string => {
  const exportDate = timestamp || new Date().toLocaleString();

  const pageWidth = 612;
  const pageHeight = 792;
  const marginLeft = 50;
  const marginRight = 50;
  const marginTop = 50;
  const marginBottom = 50;
  const contentWidth = pageWidth - marginLeft - marginRight;
  const contentHeight = pageHeight - marginTop - marginBottom;

  const fontSize = 11;
  const lineHeight = 14;
  const titleFontSize = 16;
  const titleLineHeight = 18;

  const pages = [];
  let currentPage = {
    content: '',
    yPos: pageHeight - marginTop - titleLineHeight
  };

  currentPage.content += `BT\n/F1 ${titleFontSize} Tf\n${marginLeft} ${currentPage.yPos} Td\n`;
  const escapedTitle = 'Chat Export'.replace(/[()\\]/g, '\\$&');
  currentPage.content += `(${escapedTitle}) Tj\n`;
  currentPage.yPos -= titleLineHeight + 10;

  currentPage.content += `0 ${-lineHeight} Td\n/F1 ${fontSize} Tf\n`;
  const headerLines = [
    `Agent: ${agentName}`,
    `Date: ${exportDate}`,
    ''
  ];

  headerLines.forEach(line => {
    if (line.trim()) {
      const escapedLine = line.replace(/[()\\]/g, '\\$&');
      currentPage.content += `(${escapedLine}) Tj\n`;
    }
    currentPage.content += `0 ${-lineHeight} Td\n`;
    currentPage.yPos -= lineHeight;
  });

  currentPage.yPos -= 10;

  const addTextWithWrapping = (text: string, role: string) => {
    if (currentPage.yPos < marginBottom + 100) {
      currentPage.content += 'ET\n';
      pages.push(currentPage);
      currentPage = {
        content: `BT\n/F1 ${fontSize} Tf\n${marginLeft} ${pageHeight - marginTop} Td\n`,
        yPos: pageHeight - marginTop
      };
    }

    const escapedRole = `${role}:`.replace(/[()\\]/g, '\\$&');
    currentPage.content += `(${escapedRole}) Tj\n`;
    currentPage.content += `0 ${-lineHeight} Td\n`;
    currentPage.yPos -= lineHeight;

    const words = text.split(/\s+/);
    let currentLine = '';

    words.forEach(word => {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const escapedTest = testLine.replace(/[()\\]/g, '\\$&');

      if (escapedTest.length * 6 > contentWidth - 100) {
        if (currentLine.trim()) {
          const escapedLine = currentLine.replace(/[()\\]/g, '\\$&');
          currentPage.content += `(${escapedLine}) Tj\n`;
          currentPage.content += `0 ${-lineHeight} Td\n`;
          currentPage.yPos -= lineHeight;

          if (currentPage.yPos < marginBottom + lineHeight) {
            currentPage.content += 'ET\n';
            pages.push(currentPage);
            currentPage = {
              content: `BT\n/F1 ${fontSize} Tf\n${marginLeft} ${pageHeight - marginTop} Td\n`,
              yPos: pageHeight - marginTop
            };
          }
        }
        currentLine = word;
      } else {
        currentLine = testLine;
      }
    });

    if (currentLine.trim()) {
      const escapedLine = currentLine.replace(/[()\\]/g, '\\$&');
      currentPage.content += `(${escapedLine}) Tj\n`;
      currentPage.content += `0 ${-lineHeight} Td\n`;
      currentPage.yPos -= lineHeight;
    }

    currentPage.content += `0 ${-lineHeight} Td\n`;
    currentPage.yPos -= lineHeight;

    currentPage.content += `0 ${-lineHeight} Td\n`;
    currentPage.yPos -= lineHeight;
  };

  addTextWithWrapping(query, 'Query');
  addTextWithWrapping(response, 'Response');

  currentPage.content += 'ET\n';
  pages.push(currentPage);

  const pdfHeader = '%PDF-1.4\n';
  const pdfFooter = '%%EOF\n';

  let objects = '';
  let objectCount = 1;

  objectCount++;
  objects += '1 0 obj\n';
  objects += '<<\n';
  objects += '/Type /Catalog\n';
  objects += '/Pages 2 0 R\n';
  objects += '>>\n';
  objects += 'endobj\n\n';

  objectCount++;
  objects += '2 0 obj\n';
  objects += '<<\n';
  objects += '/Type /Pages\n';
  objects += `/Kids [${pages.map((_, i) => `${i + 3} 0 R`).join(' ')}]\n`;
  objects += `/Count ${pages.length}\n`;
  objects += '>>\n';
  objects += 'endobj\n\n';

  pages.forEach((page, index) => {
    const pageObjNum = index + 3;
    objectCount++;

    objects += `${pageObjNum} 0 obj\n`;
    objects += '<<\n';
    objects += '/Type /Page\n';
    objects += '/Parent 2 0 R\n';
    objects += `/MediaBox [0 0 ${pageWidth} ${pageHeight}]\n`;
    objects += `/Contents ${pageObjNum + pages.length} 0 R\n`;
    objects += '/Resources <<\n';
    objects += '/Font <<\n';
    objects += `/F1 ${pages.length + 3} 0 R\n`;
    objects += '>>\n';
    objects += '>>\n';
    objects += '>>\n';
    objects += 'endobj\n\n';
  });

  pages.forEach((page, index) => {
    const contentObjNum = pages.length + 3 + index;
    objectCount++;

    objects += `${contentObjNum} 0 obj\n`;
    objects += `<<\n`;
    objects += `/Length ${page.content.length}\n`;
    objects += `>>\n`;
    objects += `stream\n`;
    objects += page.content;
    objects += `endstream\n`;
    objects += `endobj\n\n`;
  });

  const fontObjNum = pages.length * 2 + 3;
  objectCount++;
  objects += `${fontObjNum} 0 obj\n`;
  objects += '<<\n';
  objects += '/Type /Font\n';
  objects += '/Subtype /Type1\n';
  objects += '/BaseFont /Helvetica\n';
  objects += '>>\n';
  objects += 'endobj\n\n';

  const xrefOffset = pdfHeader.length + objects.length;
  let xref = 'xref\n';
  xref += `0 ${objectCount}\n`;
  xref += '0000000000 65535 f \n';

  for (let i = 1; i < objectCount; i++) {
    xref += '0000000000 00000 n \n';
  }

  let trailer = 'trailer\n';
  trailer += '<<\n';
  trailer += `/Size ${objectCount}\n`;
  trailer += '/Root 1 0 R\n';
  trailer += '>>\n';
  trailer += `startxref\n${xrefOffset}\n`;

  const pdfContent = pdfHeader + objects + xref + trailer + pdfFooter;

  return pdfContent;
};

export const downloadSingleQueryPDF = (
  query: string,
  response: string,
  agentName: string,
  timestamp?: string
) => {
  const pdfContent = generateSingleQueryPDF(query, response, agentName, timestamp);
  const pdfBlob = new Blob([pdfContent], { type: 'application/pdf' });
  const pdfUrl = URL.createObjectURL(pdfBlob);

  const pdfLink = document.createElement('a');
  pdfLink.href = pdfUrl;
  const fileName = `Query_Response_${new Date().toISOString().split('T')[0]}.pdf`;
  pdfLink.download = fileName;
  pdfLink.style.display = 'none';

  document.body.appendChild(pdfLink);
  pdfLink.click();
  document.body.removeChild(pdfLink);

  URL.revokeObjectURL(pdfUrl);
};

