import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Agent, getAgentChatSessions, getAgentChatHistory } from '@/lib/api';
import { useChat } from '@/contexts';
import { API_BASE_URL, getAuthHeaders } from '@/lib/api';
import { LoadingSpinner } from '../ui/LoadingSpinner';

interface ChatSession {
  session_id: string;
  title: string;
  message_count: number;
  last_updated?: string;
}

interface ChatSidebarProps {
  selectedAgent: Agent | null;
  currentSessionId: string;
  onSessionSelect: (sessionId: string) => void;
  onNewChat: () => void;
  isOpen: boolean;
  onToggle: () => void;
}

export default function ChatSidebar({
  selectedAgent,
  currentSessionId,
  onSessionSelect,
  onNewChat,
  isOpen,
  onToggle
}: ChatSidebarProps) {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(380); // Default width: increased for better content display
  const [isResizing, setIsResizing] = useState(false);
  const [activeNavButton, setActiveNavButton] = useState<string | null>(null);
  const [openDropdownSessionId, setOpenDropdownSessionId] = useState<string | null>(null);
  const [renamingSessionId, setRenamingSessionId] = useState<string | null>(null);
  const [renameInputValue, setRenameInputValue] = useState('');
  const sidebarRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef<number>(0);
  const startWidthRef = useRef<number>(0);
  const lastChatLoadingRef = useRef<boolean>(false);
  const initialLoadDoneRef = useRef<boolean>(false);

  // Add mobile detection state
  const [isMobile, setIsMobile] = useState(typeof window !== 'undefined' ? window.innerWidth < 768 : false);

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth < 768);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const { isLoading: isChatLoading, stopProcessing, currentQuerySessionId } = useChat();

  // Listen for global open/close/toggle events from Header (mobile)
  useEffect(() => {
    const open = () => { if (!isOpen) onToggle(); };
    const close = () => { if (isOpen) onToggle(); };
    const toggle = () => onToggle();

    window.addEventListener('chat-sidebar:open', open);
    window.addEventListener('chat-sidebar:close', close);
    window.addEventListener('chat-sidebar:toggle', toggle);

    return () => {
      window.removeEventListener('chat-sidebar:open', open);
      window.removeEventListener('chat-sidebar:close', close);
      window.removeEventListener('chat-sidebar:toggle', toggle);
    };
  }, [isOpen, onToggle]);

  // Lock body scroll on small screens when drawer is open
  useEffect(() => {
    const isMobile = typeof window !== 'undefined' && window.matchMedia('(max-width: 767px)').matches;
    if (!isMobile) return;
    const previous = document.body.style.overflow;
    if (isOpen) document.body.style.overflow = 'hidden';
    return () => { document.body.style.overflow = previous; };
  }, [isOpen]);

  // Close session dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('[data-session-dropdown]')) {
        setOpenDropdownSessionId(null);
      }
    };

    if (openDropdownSessionId) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [openDropdownSessionId]);

  // Load chat sessions only when agent changes, not when currentSessionId changes
  useEffect(() => {
    if (selectedAgent) {
      // Only force refresh on the very first load of the component
      // For all subsequent loads, use the cache when available
      const shouldForceRefresh = !initialLoadDoneRef.current;
      loadChatSessions(shouldForceRefresh);
      
      // Mark initial load as done
      if (!initialLoadDoneRef.current) {
        initialLoadDoneRef.current = true;
      }
    } else {
      setSessions([]);
    }
  }, [selectedAgent]); // Removed currentSessionId dependency
  
  // Reload sessions when chat loading state changes from true to false (query completes)
  useEffect(() => {
    if (selectedAgent && !isChatLoading && lastChatLoadingRef.current) {
      // Only refresh when a chat has completed (loading changed from true to false)
      //console.log('Chat loading completed, refreshing sessions');
      // We don't need to force refresh here - use the cache
      loadChatSessions(false); 
    }
    // Update ref for next comparison
    lastChatLoadingRef.current = isChatLoading;
  }, [isChatLoading, selectedAgent]);

  const loadChatSessions = async (forceRefresh: boolean = false) => {
    if (!selectedAgent) return;

    setIsLoading(true);
    try {
      const response = await getAgentChatSessions(selectedAgent.id, forceRefresh); 
      setSessions(response.sessions || []);
    } catch (error) {
      console.error('Failed to load chat sessions:', error);
      setSessions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewChat = async () => {
    if (isChatLoading && currentQuerySessionId) {
      try {
        // Call cancel API
        const response = await fetch(`${API_BASE_URL}/agents/cancel`, {
          method: 'POST',
          headers: {
            ...getAuthHeaders(),
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ query_session_id: currentQuerySessionId }),
        });

        if (!response.ok) {
          throw new Error('Failed to cancel query');
        }

        stopProcessing();
        //console.log('Query cancelled successfully');
      } catch (error) {
        console.error('Failed to cancel query:', error);
      }
    }

    onNewChat();
    // Reload sessions to include the new one
    // Increase timeout to ensure backend has time to process and update
    // For new chat creation, we should force refresh to ensure the new session appears
    setTimeout(() => {
      //console.log('Reloading chat sessions after new chat');
      loadChatSessions(true); // Force refresh is needed for new chat creation
    }, 1000);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 1) return 'Today';
      if (diffDays === 2) return 'Yesterday';
      if (diffDays <= 7) return `${diffDays} days ago`;
      return date.toLocaleDateString();
    } catch {
      return '';
    }
  };

  const handleExportPDF = async (session: ChatSession) => {
    if (!selectedAgent) return;

    try {
      // Fetch chat history
      const historyData = await getAgentChatHistory(selectedAgent.id, session.session_id);
      const messages = historyData.messages || [];

      // Generate PDF content
      const pdfContent = generatePDFBinaryContent(session, messages);

      // Create PDF blob and download
      const pdfBlob = new Blob([pdfContent], { type: 'application/pdf' });
      const pdfUrl = URL.createObjectURL(pdfBlob);

      const pdfLink = document.createElement('a');
      pdfLink.href = pdfUrl;
      pdfLink.download = `${session.title || 'Chat_Session'}_${new Date().toISOString().split('T')[0]}.pdf`;
      pdfLink.style.display = 'none';

      document.body.appendChild(pdfLink);
      pdfLink.click();
      document.body.removeChild(pdfLink);

      URL.revokeObjectURL(pdfUrl);

      setOpenDropdownSessionId(null);
    } catch (error) {
      console.error('Failed to export chat as PDF:', error);
    }
  };

  const handleRenameSession = async (session: ChatSession, newTitle: string) => {
    if (!selectedAgent || !newTitle.trim()) return;

    try {
      // Call API to rename session
      const response = await fetch(`${API_BASE_URL}/agents/chat-sessions/${selectedAgent.id}/${session.session_id}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify({ title: newTitle.trim() }),
      });

      if (!response.ok) {
        throw new Error('Failed to rename session');
      }

      // Update local state
      setSessions(prevSessions =>
        prevSessions.map(s =>
          s.session_id === session.session_id
            ? { ...s, title: newTitle.trim() }
            : s
        )
      );

      setRenamingSessionId(null);
      setRenameInputValue('');
    } catch (error) {
      console.error('Failed to rename session:', error);
    }
  };

  const startRenaming = (session: ChatSession) => {
    setRenamingSessionId(session.session_id);
    setRenameInputValue(session.title || '');
    setOpenDropdownSessionId(null);
  };

  const cancelRenaming = () => {
    setRenamingSessionId(null);
    setRenameInputValue('');
  };


  const generatePDFBinaryContent = (session: ChatSession, messages: any[]) => {
    const chatTitle = session.title || 'Chat Session';
    const exportDate = new Date().toLocaleString();
    const agentName = selectedAgent?.name || 'Unknown';

    // Page settings
    const pageWidth = 612;  // Letter width in points
    const pageHeight = 792; // Letter height in points
    const marginLeft = 50;
    const marginRight = 50;
    const marginTop = 50;
    const marginBottom = 50;
    const contentWidth = pageWidth - marginLeft - marginRight;
    const contentHeight = pageHeight - marginTop - marginBottom;

    // Font settings
    const fontSize = 11;
    const lineHeight = 14;
    const titleFontSize = 16;
    const titleLineHeight = 18;

    // Create multiple pages as needed
    const pages = [];
    let currentPage = {
      content: '',
      yPos: pageHeight - marginTop - titleLineHeight
    };

    // Add title and header to first page
    currentPage.content += `BT\n/F1 ${titleFontSize} Tf\n${marginLeft} ${currentPage.yPos} Td\n`;
    const escapedTitle = chatTitle.replace(/[()\\]/g, '\\$&');
    currentPage.content += `(${escapedTitle}) Tj\n`;
    currentPage.yPos -= titleLineHeight + 10;

    // Add header info
    currentPage.content += `0 ${-lineHeight} Td\n/F1 ${fontSize} Tf\n`;
    const headerLines = [
      `Agent: ${agentName}`,
      ''
    ];

    headerLines.forEach(line => {
      if (line.trim()) {
        const escapedLine = line.replace(/[()\\]/g, '\\$&');
        currentPage.content += `(${escapedLine}) Tj\n`;
      }
      currentPage.content += `0 ${-lineHeight} Td\n`;
      currentPage.yPos -= lineHeight;
    });

    currentPage.yPos -= 10; // Extra space

    // Process messages
    messages.forEach((message, msgIndex) => {
      const role = message.role === 'user' ? 'You' : agentName;
      const timestamp = message.timestamp ? new Date(message.timestamp).toLocaleString() : '';
      const content = message.content;

      // Check if we need a new page before this message
      if (currentPage.yPos < marginBottom + 100) { // Leave space for at least 7 lines
        currentPage.content += 'ET\n';
        pages.push(currentPage);
        currentPage = {
          content: `BT\n/F1 ${fontSize} Tf\n${marginLeft} ${pageHeight - marginTop} Td\n`,
          yPos: pageHeight - marginTop
        };
      }

      // Add role
      const escapedRole = `${role}:`.replace(/[()\\]/g, '\\$&');
      currentPage.content += `(${escapedRole}) Tj\n`;
      currentPage.content += `0 ${-lineHeight} Td\n`;
      currentPage.yPos -= lineHeight;

      // Add timestamp if available
      if (timestamp) {
        const escapedTimestamp = timestamp.replace(/[()\\]/g, '\\$&');
        currentPage.content += `(${escapedTimestamp}) Tj\n`;
        currentPage.content += `0 ${-lineHeight} Td\n`;
        currentPage.yPos -= lineHeight;
      }

      // Add message content with word wrapping
      const words = content.split(/\s+/);
      let currentLine = '';

      words.forEach(word => {
        const testLine = currentLine ? `${currentLine} ${word}` : word;
        const escapedTest = testLine.replace(/[()\\]/g, '\\$&');

        // Check if line would be too long (rough estimate)
        if (escapedTest.length * 6 > contentWidth - 100) { // Approximate character width
          if (currentLine.trim()) {
            const escapedLine = currentLine.replace(/[()\\]/g, '\\$&');
            currentPage.content += `(${escapedLine}) Tj\n`;
            currentPage.content += `0 ${-lineHeight} Td\n`;
            currentPage.yPos -= lineHeight;

            // Check for page break
            if (currentPage.yPos < marginBottom + lineHeight) {
              currentPage.content += 'ET\n';
              pages.push(currentPage);
              currentPage = {
                content: `BT\n/F1 ${fontSize} Tf\n${marginLeft} ${pageHeight - marginTop} Td\n`,
                yPos: pageHeight - marginTop
              };
            }
          }
          currentLine = word;
        } else {
          currentLine = testLine;
        }
      });

      // Add remaining line
      if (currentLine.trim()) {
        const escapedLine = currentLine.replace(/[()\\]/g, '\\$&');
        currentPage.content += `(${escapedLine}) Tj\n`;
        currentPage.content += `0 ${-lineHeight} Td\n`;
        currentPage.yPos -= lineHeight;
      }

      // Add space between messages
      currentPage.content += `0 ${-lineHeight} Td\n`;
      currentPage.yPos -= lineHeight;

      // Simple separator (blank line for spacing)
      currentPage.content += `0 ${-lineHeight} Td\n`;
      currentPage.yPos -= lineHeight;
    });

    currentPage.content += 'ET\n';
    pages.push(currentPage);

    // Create PDF structure
    const pdfHeader = '%PDF-1.4\n';
    const pdfFooter = '%%EOF\n';

    let objects = '';
    let objectCount = 1;

    // Object 1: Catalog
    objectCount++;
    objects += '1 0 obj\n';
    objects += '<<\n';
    objects += '/Type /Catalog\n';
    objects += '/Pages 2 0 R\n';
    objects += '>>\n';
    objects += 'endobj\n\n';

    // Object 2: Pages
    objectCount++;
    objects += '2 0 obj\n';
    objects += '<<\n';
    objects += '/Type /Pages\n';
    objects += `/Kids [${pages.map((_, i) => `${i + 3} 0 R`).join(' ')}]\n`;
    objects += `/Count ${pages.length}\n`;
    objects += '>>\n';
    objects += 'endobj\n\n';

    // Create page objects
    pages.forEach((page, index) => {
      const pageObjNum = index + 3;
      objectCount++;

      objects += `${pageObjNum} 0 obj\n`;
      objects += '<<\n';
      objects += '/Type /Page\n';
      objects += '/Parent 2 0 R\n';
      objects += `/MediaBox [0 0 ${pageWidth} ${pageHeight}]\n`;
      objects += `/Contents ${pageObjNum + pages.length} 0 R\n`;
      objects += '/Resources <<\n';
      objects += '/Font <<\n';
      objects += `/F1 ${pages.length + 3} 0 R\n`;
      objects += '>>\n';
      objects += '>>\n';
      objects += '>>\n';
      objects += 'endobj\n\n';
    });

    // Create content stream objects
    pages.forEach((page, index) => {
      const contentObjNum = pages.length + 3 + index;
      objectCount++;

      objects += `${contentObjNum} 0 obj\n`;
      objects += `<<\n`;
      objects += `/Length ${page.content.length}\n`;
      objects += `>>\n`;
      objects += `stream\n`;
      objects += page.content;
      objects += `endstream\n`;
      objects += `endobj\n\n`;
    });

    // Object for Font
    const fontObjNum = pages.length * 2 + 3;
    objectCount++;
    objects += `${fontObjNum} 0 obj\n`;
    objects += '<<\n';
    objects += '/Type /Font\n';
    objects += '/Subtype /Type1\n';
    objects += '/BaseFont /Helvetica\n';
    objects += '>>\n';
    objects += 'endobj\n\n';

    // Cross-reference table
    const xrefOffset = pdfHeader.length + objects.length;
    let xref = 'xref\n';
    xref += `0 ${objectCount}\n`;
    xref += '0000000000 65535 f \n';

    // Add entries for each object (simplified)
    for (let i = 1; i < objectCount; i++) {
      xref += '0000000000 00000 n \n';
    }

    // Trailer
    let trailer = 'trailer\n';
    trailer += '<<\n';
    trailer += `/Size ${objectCount}\n`;
    trailer += '/Root 1 0 R\n';
    trailer += '>>\n';
    trailer += `startxref\n${xrefOffset}\n`;

    // Combine all parts
    const pdfContent = pdfHeader + objects + xref + trailer + pdfFooter;

    return pdfContent;
  };

  // Resize functionality (stable; uses refs only)
  const handleMouseMove = useCallback((e: MouseEvent) => {
    const deltaX = e.clientX - startXRef.current;
    const newWidth = Math.max(200, Math.min(500, startWidthRef.current + deltaX));
    setSidebarWidth(newWidth);
  }, []);

  // Prevent context menu during resize
  const preventContextMenu = useCallback((e: Event) => {
    e.preventDefault();
  }, []);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);

    // Remove all event listeners
    document.removeEventListener('mousemove', handleMouseMove, { capture: true });
    document.removeEventListener('mouseup', handleMouseUp, { capture: true });
    document.removeEventListener('contextmenu', preventContextMenu, { capture: true });

    // Reset body styles immediately
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, [handleMouseMove, preventContextMenu]);

  // Emergency reset function for stuck state
  const resetResizeState = useCallback(() => {
    setIsResizing(false);
    document.removeEventListener('mousemove', handleMouseMove, { capture: true });
    document.removeEventListener('mouseup', handleMouseUp, { capture: true });
    document.removeEventListener('contextmenu', preventContextMenu, { capture: true });
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, [handleMouseMove, handleMouseUp, preventContextMenu]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    // Only handle left mouse button
    if (e.button !== 0) return;

    e.preventDefault();
    e.stopPropagation();

    setIsResizing(true);
    startXRef.current = e.clientX;
    startWidthRef.current = sidebarWidth;

    // Immediate UX: show resize cursor, prevent text selection
    document.body.style.cursor = 'ew-resize';
    document.body.style.userSelect = 'none';

    // Add listeners (capture for reliability while dragging)
    document.addEventListener('mousemove', handleMouseMove, { capture: true });
    document.addEventListener('mouseup', handleMouseUp, { capture: true });

    // Prevent accidental context menu while dragging
    document.addEventListener('contextmenu', preventContextMenu, { capture: true });
  }, [sidebarWidth, handleMouseMove, handleMouseUp, preventContextMenu]);

  // Cleanup event listeners on component unmount
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove, { capture: true });
      document.removeEventListener('mouseup', handleMouseUp, { capture: true });
      document.removeEventListener('contextmenu', preventContextMenu, { capture: true });
      // Reset body styles on cleanup
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [handleMouseMove, handleMouseUp, preventContextMenu]);

  // Update cursor when resizing with safety timeout
  useEffect(() => {
    if (isResizing) {
      document.body.style.cursor = 'ew-resize';
      document.body.style.userSelect = 'none';

      // Safety timeout: auto-reset after 10 seconds if stuck
      const timeoutId = setTimeout(() => {
        resetResizeState();
      }, 10000);

      return () => {
        clearTimeout(timeoutId);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    } else {
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    }
  }, [isResizing, resetResizeState]);

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-gray-900/50 backdrop-blur-sm z-40 md:hidden transition-all duration-300"
          onClick={onToggle}
          aria-label="Close chat sidebar overlay"
        />
      )}

      {/* Sidebar */}
      <div
        id="chat-sidebar"
        ref={sidebarRef}
        className={`
          fixed top-0 left-0 h-full bg-gradient-to-b from-gray-50 via-white to-gray-50 text-gray-900 z-50 transform transition-all duration-300 ease-in-out shadow-xl border-r border-gray-200
          md:relative md:transform-none md:z-auto
          ${isOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'}
          ${isCollapsed ? 'w-14' : isMobile ? 'w-[80vw]' : `${sidebarWidth}px`}
        `}
        style={{
          width: isCollapsed ? '56px' : isMobile ? undefined : `${sidebarWidth}px`,  // Don't set width for mobile here, as class handles w-[80vw]
          transition: isResizing ? 'none' : 'all 0.3s ease-in-out',
          borderRight: isResizing ? '2px solid #6b7280' : undefined
        }}
        aria-hidden={!isOpen}
      >
        <div className="flex flex-col h-full">
          {/* Header with collapse button */}
          <div className="p-2 border-b border-gray-200 bg-gray-100">
            <div className="flex items-center justify-between">
              {!isCollapsed && (
                <div className="flex items-center gap-2">
                  <h2 className="text-lg font-bold bg-blue-900 bg-clip-text text-transparent">Sidebar</h2>
                </div>
              )}
              <div className="flex items-center gap-2">
                {/* Collapse/Expand toggle */}
                <button
                  onClick={() => {
                    if (isMobile) {
                      onToggle();  // Close fully on mobile
                    } else {
                      setIsCollapsed(!isCollapsed);
                    }
                  }}
                  className="p-1.5 hover:bg-gray-200 rounded-lg transition-all duration-200 hover:scale-105 group"
                  title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
                  aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
                >
                  <svg className="w-4 h-4 text-gray-600 group-hover:text-gray-800 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {isMobile ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    ) : isCollapsed ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                    )}
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Navigation List */}
          <div className={`${isCollapsed ? 'px-0 py-3' : 'px-3 py-3'} border-b border-gray-200`}>
            <div className="space-y-1">
              {/* New Chat Button */}
              <button
                onClick={() => {
                  if (isChatLoading) {
                    stopProcessing();
                  }
                  handleNewChat();
                }}
                className={`w-full flex items-center rounded-lg border transition-all duration-200 group ${isCollapsed ? 'justify-center px-2 py-3' : 'gap-3 px-3 py-2 text-left'} border-transparent hover:bg-sky-50 hover:border-sky-300 hover:shadow-sm focus:border-sky-400 focus:ring-2 focus:ring-sky-100`}
                title="New Chat"
              >
                <div className="w-8 h-8 bg-blue-900 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm group-hover:scale-105 transition-transform duration-200">
                  <svg className="w-4 h-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M2.6687 11.333V8.66699C2.6687 7.74455 2.66841 7.01205 2.71655 6.42285C2.76533 5.82612 2.86699 5.31731 3.10425 4.85156L3.25854 4.57617C3.64272 3.94975 4.19392 3.43995 4.85229 3.10449L5.02905 3.02149C5.44666 2.84233 5.90133 2.75849 6.42358 2.71582C7.01272 2.66769 7.74445 2.66797 8.66675 2.66797H9.16675C9.53393 2.66797 9.83165 2.96586 9.83179 3.33301C9.83179 3.70028 9.53402 3.99805 9.16675 3.99805H8.66675C7.7226 3.99805 7.05438 3.99834 6.53198 4.04102C6.14611 4.07254 5.87277 4.12568 5.65601 4.20313L5.45581 4.28906C5.01645 4.51293 4.64872 4.85345 4.39233 5.27149L4.28979 5.45508C4.16388 5.7022 4.08381 6.01663 4.04175 6.53125C3.99906 7.05373 3.99878 7.7226 3.99878 8.66699V11.333C3.99878 12.2774 3.99906 12.9463 4.04175 13.4688C4.08381 13.9833 4.16389 14.2978 4.28979 14.5449L4.39233 14.7285C4.64871 15.1465 5.01648 15.4871 5.45581 15.7109L5.65601 15.7969C5.87276 15.8743 6.14614 15.9265 6.53198 15.958C7.05439 16.0007 7.72256 16.002 8.66675 16.002H11.3337C12.2779 16.002 12.9461 16.0007 13.4685 15.958C13.9829 15.916 14.2976 15.8367 14.5447 15.7109L14.7292 15.6074C15.147 15.3511 15.4879 14.9841 15.7117 14.5449L15.7976 14.3447C15.8751 14.128 15.9272 13.8546 15.9587 13.4688C16.0014 12.9463 16.0017 12.2774 16.0017 11.333V10.833C16.0018 10.466 16.2997 10.1681 16.6667 10.168C17.0339 10.168 17.3316 10.4659 17.3318 10.833V11.333C17.3318 12.2555 17.3331 12.9879 17.2849 13.5771C17.2422 14.0993 17.1584 14.5541 16.9792 14.9717L16.8962 15.1484C16.5609 15.8066 16.0507 16.3571 15.4246 16.7412L15.1492 16.8955C14.6833 17.1329 14.1739 17.2354 13.5769 17.2842C12.9878 17.3323 12.256 17.332 11.3337 17.332H8.66675C7.74446 17.332 7.01271 17.3323 6.42358 17.2842C5.90135 17.2415 5.44665 17.1577 5.02905 16.9785L4.85229 16.8955C4.19396 16.5601 3.64271 16.0502 3.25854 15.4238L3.10425 15.1484C2.86697 14.6827 2.76534 14.1739 2.71655 13.5771C2.66841 12.9879 2.6687 12.2555 2.6687 11.333ZM13.4646 3.11328C14.4201 2.334 15.8288 2.38969 16.7195 3.28027L16.8865 3.46485C17.6141 4.35685 17.6143 5.64423 16.8865 6.53613L16.7195 6.7207L11.6726 11.7686C11.1373 12.3039 10.4624 12.6746 9.72827 12.8408L9.41089 12.8994L7.59351 13.1582C7.38637 13.1877 7.17701 13.1187 7.02905 12.9707C6.88112 12.8227 6.81199 12.6134 6.84155 12.4063L7.10132 10.5898L7.15991 10.2715C7.3262 9.53749 7.69692 8.86241 8.23218 8.32715L13.2791 3.28027L13.4646 3.11328ZM15.7791 4.2207C15.3753 3.81702 14.7366 3.79124 14.3035 4.14453L14.2195 4.2207L9.17261 9.26856C8.81541 9.62578 8.56774 10.0756 8.45679 10.5654L8.41772 10.7773L8.28296 11.7158L9.22241 11.582L9.43433 11.543C9.92426 11.432 10.3749 11.1844 10.7322 10.8271L15.7791 5.78027L15.8552 5.69629C16.185 5.29194 16.1852 4.708 15.8552 4.30371L15.7791 4.2207Z" />
                  </svg>
                </div>
                {!isCollapsed && (
                  <div className="flex flex-col">
                    <span className="text-gray-900 font-medium text-sm">New Chat</span>
                    <span className="text-gray-500 text-xs">Start a fresh chat with Workplace SLM</span>
                  </div>
                )}
              </button>
              <button
                onClick={() => {
                  setActiveNavButton(activeNavButton === 'knowledge' ? null : 'knowledge');
                  window.dispatchEvent(new Event('open-knowledge-base'));
                }}
                className={`w-full flex items-center rounded-lg border transition-all duration-200 group ${isCollapsed ? 'justify-center px-2 py-3' : 'gap-3 px-3 py-2 text-left'
                  } ${activeNavButton === 'knowledge'
                    ? 'bg-sky-50 border-sky-400 shadow-md'
                    : 'border-transparent hover:bg-sky-50 hover:border-sky-300 hover:shadow-sm'
                  } focus:border-sky-400 focus:ring-2 focus:ring-sky-100`}
                title="Knowledge Base"
              >
                <div className="w-8 h-8 bg-blue-900 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm group-hover:scale-105 transition-transform duration-200">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                {!isCollapsed && (
                  <div className="flex flex-col">
                    <span className="text-gray-900 font-medium text-sm">Knowledge Base</span>
                    <span className="text-gray-500 text-xs">Manage documents</span>
                  </div>
                )}
              </button>

              <button
                onClick={() => {
                  setActiveNavButton(activeNavButton === 'notes' ? null : 'notes');
                  window.dispatchEvent(new Event('open-notes-panel'));
                }}
                className={`w-full flex items-center rounded-lg border transition-all duration-200 group ${isCollapsed ? 'justify-center px-2 py-3' : 'gap-3 px-3 py-2 text-left'
                  } ${activeNavButton === 'notes'
                    ? 'bg-sky-50 border-sky-400 shadow-md'
                    : 'border-transparent hover:bg-sky-50 hover:border-sky-300 hover:shadow-sm'
                  } focus:border-sky-400 focus:ring-2 focus:ring-sky-100`}
                title="Notes"
              >
                <div className="w-8 h-8 bg-blue-900 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm group-hover:scale-105 transition-transform duration-200">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v8m-4-4h8M5 6a2 2 0 012-2h7l5 5v9a2 2 0 01-2 2H7a2 2 0 01-2-2V6z" />
                  </svg>
                </div>
                {!isCollapsed && (
                  <div className="flex flex-col">
                    <span className="text-gray-900 font-medium text-sm">Notes</span>
                    <span className="text-gray-500 text-xs">Saved snippets</span>
                  </div>
                )}
              </button>

              <button
                onClick={() => {
                  setActiveNavButton(activeNavButton === 'livechat' ? null : 'livechat');
                  window.dispatchEvent(new Event('open-live-chat'));
                }}
                className={`w-full flex items-center rounded-lg border transition-all duration-200 group ${isCollapsed ? 'justify-center px-2 py-3' : 'gap-3 px-3 py-2 text-left'
                  } ${activeNavButton === 'livechat'
                    ? 'bg-sky-50 border-sky-400 shadow-md'
                    : 'border-transparent hover:bg-sky-50 hover:border-sky-300 hover:shadow-sm'
                  } focus:border-sky-400 focus:ring-2 focus:ring-sky-100`}
                title="Live Chat"
              >
                <div className="w-8 h-8 bg-blue-900 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm group-hover:scale-105 transition-transform duration-200">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                {!isCollapsed && (
                  <div className="flex flex-col">
                    <span className="text-gray-900 font-medium text-sm">Live Chat</span>
                    <span className="text-gray-500 text-xs">Team conversations</span>
                  </div>
                )}
              </button>
              

              <button
                onClick={() => {
                  setActiveNavButton(activeNavButton === 'prompts' ? null : 'prompts');
                  window.dispatchEvent(new Event('open-prompt-gallery'));
                }}
                className={`w-full flex items-center rounded-lg border transition-all duration-200 group ${isCollapsed ? 'justify-center px-2 py-3' : 'gap-3 px-3 py-2 text-left'
                  } ${activeNavButton === 'prompts'
                    ? 'bg-sky-50 border-sky-400 shadow-md'
                    : 'border-transparent hover:bg-sky-50 hover:border-sky-300 hover:shadow-sm'
                  } focus:border-sky-400 focus:ring-2 focus:ring-sky-100`}
                title="Prompts Gallery"
              >
                <div className="w-8 h-8 bg-blue-900 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm group-hover:scale-105 transition-transform duration-200">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                {!isCollapsed && (
                  <div className="flex flex-col">
                    <span className="text-gray-900 font-medium text-sm">Prompts Gallery</span>
                    <span className="text-gray-500 text-xs">Quick templates</span>
                  </div>
                )}
              </button>
            </div>
          </div>

          {/* Chat History Section - Hidden when collapsed */}
          {!isCollapsed && (
            <>
              <div className="p-2 border-t border-gray-200">
                <div className="flex items-center justify-between mb-0">
                  <h3 className="text-gray-800 font-semibold text-sm flex items-center">
                    {/* <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg> */}
                    Chat History
                  </h3>
                </div>

               
              </div>

              {/* Chat Sessions List */}
              <div className="flex-1 overflow-y-auto p-2">
                {!selectedAgent ? (
                  <div className="flex flex-col items-center justify-center py-6 text-center">
                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3">
                      <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <p className="text-gray-500 text-sm">Select an agent to view chat history</p>
                  </div>
                ) : isLoading ? (
                  <div className="flex flex-col items-center justify-center py-6 text-center">
                    <LoadingSpinner size="sm" message="Loading chats..." />
                  </div>
                ) : sessions.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-6 text-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-3">
                      <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                    <p className="text-gray-700 text-sm font-medium mb-1">No chat history yet</p>
                    <p className="text-gray-500 text-xs">Start a new conversation!</p>
                  </div>
                ) : (
                  <div className="space-y-1">
                    {sessions.map((session) => (
                      <div
                        key={session.session_id}
                        className={`
                          w-full text-left p-3 rounded-lg transition-all duration-200 group relative bg-gray-50 border
                          ${currentSessionId === session.session_id
                            ? 'bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 shadow-sm'
                            : 'border-gray-200 hover:bg-green-50 hover:shadow-sm'
                          }
                        `}
                      >
                        <div className="flex items-start justify-between">
                          <button
                            onClick={() => {
                              // Only switch session if it's not already the current one and not renaming
                              if (currentSessionId !== session.session_id && renamingSessionId !== session.session_id) {
                                // Just call onSessionSelect without triggering API calls
                                onSessionSelect(session.session_id);
                              }
                            }}
                            className="flex-1 text-left min-w-0"
                          >
                            {renamingSessionId === session.session_id ? (
                              <input
                                type="text"
                                value={renameInputValue}
                                onChange={(e) => setRenameInputValue(e.target.value)}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter') {
                                    handleRenameSession(session, renameInputValue);
                                  } else if (e.key === 'Escape') {
                                    cancelRenaming();
                                  }
                                }}
                                onBlur={() => {
                                  if (renameInputValue.trim() && renameInputValue !== session.title) {
                                    handleRenameSession(session, renameInputValue);
                                  } else {
                                    cancelRenaming();
                                  }
                                }}
                                className="w-full text-sm font-medium bg-white border border-blue-500 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                autoFocus
                              />
                            ) : (
                              <div className={`text-sm font-medium truncate mb-1 ${currentSessionId === session.session_id ? 'text-gray-900' : 'text-gray-700 group-hover:text-gray-900'
                                }`}>
                                {session.title || 'New Chat'}
                              </div>
                            )}
                            <div className={`text-xs flex items-center gap-2 ${currentSessionId === session.session_id ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-600'
                              }`}>
                              <span className="flex items-center gap-1">
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                                {session.message_count} message{session.message_count !== 1 ? 's' : ''}
                              </span>
                              {session.last_updated && (
                                <span className="flex items-center gap-1">
                                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                  </svg>
                                  {formatDate(session.last_updated)}
                                </span>
                              )}
                            </div>
                          </button>

                          <div className="flex items-center gap-2">
                            {/* Three-dot menu button */}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenDropdownSessionId(openDropdownSessionId === session.session_id ? null : session.session_id);
                              }}
                              className="p-1 hover:bg-gray-200 rounded opacity-100 md:opacity-0 md:group-hover:opacity-100 transition-opacity duration-200"
                              data-session-dropdown
                            >
                              <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                              </svg>
                            </button>

                            {/* Active indicator */}
                            {currentSessionId === session.session_id && (
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                <div className="w-5 h-5 bg-blue-100 rounded flex items-center justify-center">
                                  <svg className="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Dropdown menu */}
                        {openDropdownSessionId === session.session_id && (
                          <div
                            className="absolute right-2 top-12 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-48 py-1"
                            data-session-dropdown
                          >
                            <button
                              onClick={() => startRenaming(session)}
                              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                              Rename
                            </button>
                            <button
                              onClick={() => handleExportPDF(session)}
                              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              Export as PDF
                            </button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Footer */}
              {/* <div className="p-3 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50">
                <div className="text-center">
                  <div className="text-xs text-gray-600 mb-1">
                    {selectedAgent ? `${selectedAgent.name} Conversations` : 'AI Assistant'}
                  </div>
                  <div className="flex items-center justify-center gap-1 text-xs text-gray-500">
                    <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                    <span>Active Session</span>
                  </div>
                </div>
              </div> */}
            </>
          )}
        </div>

        {/* Resize Handle */}
        {!isCollapsed && (
          <div
            role="separator"
            aria-orientation="vertical"
            aria-label="Resize chat sidebar"
            tabIndex={0}
            className="absolute top-0 right-[-2px] h-full z-10"
            style={{
              width: '10px',          // comfortable hit-area
              cursor: 'ew-resize',    // show ←→ system cursor on hover
              background: isResizing ? 'rgba(59,130,246,0.08)' : 'transparent',
              borderLeft: isResizing ? '3px solid #3b82f6' : '1px solid transparent',
            }}
            onMouseDown={handleMouseDown}
            onDoubleClick={() => setSidebarWidth(380)}
            onKeyDown={(e) => {
              const STEP = (e as any).shiftKey ? 40 : 20;
              if (e.key === 'ArrowLeft') setSidebarWidth(w => Math.max(200, w - STEP));
              if (e.key === 'ArrowRight') setSidebarWidth(w => Math.min(500, w + STEP));
              if (e.key === 'Home') setSidebarWidth(200);
              if (e.key === 'End') setSidebarWidth(500);
            }}
          >
            {/* optional thin visual line; doesn't affect cursor */}
            <span
              aria-hidden
              className="pointer-events-none absolute inset-y-1/2 -translate-y-1/2 right-1 w-px h-12 bg-gray-300"
            />
          </div>
        )}
      </div>
    </>
  );
}
