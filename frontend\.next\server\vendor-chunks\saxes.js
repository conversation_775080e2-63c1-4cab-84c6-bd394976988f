"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/saxes";
exports.ids = ["vendor-chunks/saxes"];
exports.modules = {

/***/ "(ssr)/./node_modules/saxes/saxes.js":
/*!*************************************!*\
  !*** ./node_modules/saxes/saxes.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst ed5 = __webpack_require__(/*! xmlchars/xml/1.0/ed5 */ \"(ssr)/./node_modules/xmlchars/xml/1.0/ed5.js\");\nconst ed2 = __webpack_require__(/*! xmlchars/xml/1.1/ed2 */ \"(ssr)/./node_modules/xmlchars/xml/1.1/ed2.js\");\nconst NSed3 = __webpack_require__(/*! xmlchars/xmlns/1.0/ed3 */ \"(ssr)/./node_modules/xmlchars/xmlns/1.0/ed3.js\");\nvar isS = ed5.isS;\nvar isChar10 = ed5.isChar;\nvar isNameStartChar = ed5.isNameStartChar;\nvar isNameChar = ed5.isNameChar;\nvar S_LIST = ed5.S_LIST;\nvar NAME_RE = ed5.NAME_RE;\nvar isChar11 = ed2.isChar;\nvar isNCNameStartChar = NSed3.isNCNameStartChar;\nvar isNCNameChar = NSed3.isNCNameChar;\nvar NC_NAME_RE = NSed3.NC_NAME_RE;\nconst XML_NAMESPACE = \"http://www.w3.org/XML/1998/namespace\";\nconst XMLNS_NAMESPACE = \"http://www.w3.org/2000/xmlns/\";\nconst rootNS = {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    __proto__: null,\n    xml: XML_NAMESPACE,\n    xmlns: XMLNS_NAMESPACE,\n};\nconst XML_ENTITIES = {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    __proto__: null,\n    amp: \"&\",\n    gt: \">\",\n    lt: \"<\",\n    quot: \"\\\"\",\n    apos: \"'\",\n};\n// EOC: end-of-chunk\nconst EOC = -1;\nconst NL_LIKE = -2;\nconst S_BEGIN = 0; // Initial state.\nconst S_BEGIN_WHITESPACE = 1; // leading whitespace\nconst S_DOCTYPE = 2; // <!DOCTYPE\nconst S_DOCTYPE_QUOTE = 3; // <!DOCTYPE \"//blah\nconst S_DTD = 4; // <!DOCTYPE \"//blah\" [ ...\nconst S_DTD_QUOTED = 5; // <!DOCTYPE \"//blah\" [ \"foo\nconst S_DTD_OPEN_WAKA = 6;\nconst S_DTD_OPEN_WAKA_BANG = 7;\nconst S_DTD_COMMENT = 8; // <!--\nconst S_DTD_COMMENT_ENDING = 9; // <!-- blah -\nconst S_DTD_COMMENT_ENDED = 10; // <!-- blah --\nconst S_DTD_PI = 11; // <?\nconst S_DTD_PI_ENDING = 12; // <?hi \"there\" ?\nconst S_TEXT = 13; // general stuff\nconst S_ENTITY = 14; // &amp and such\nconst S_OPEN_WAKA = 15; // <\nconst S_OPEN_WAKA_BANG = 16; // <!...\nconst S_COMMENT = 17; // <!--\nconst S_COMMENT_ENDING = 18; // <!-- blah -\nconst S_COMMENT_ENDED = 19; // <!-- blah --\nconst S_CDATA = 20; // <![CDATA[ something\nconst S_CDATA_ENDING = 21; // ]\nconst S_CDATA_ENDING_2 = 22; // ]]\nconst S_PI_FIRST_CHAR = 23; // <?hi, first char\nconst S_PI_REST = 24; // <?hi, rest of the name\nconst S_PI_BODY = 25; // <?hi there\nconst S_PI_ENDING = 26; // <?hi \"there\" ?\nconst S_XML_DECL_NAME_START = 27; // <?xml\nconst S_XML_DECL_NAME = 28; // <?xml foo\nconst S_XML_DECL_EQ = 29; // <?xml foo=\nconst S_XML_DECL_VALUE_START = 30; // <?xml foo=\nconst S_XML_DECL_VALUE = 31; // <?xml foo=\"bar\"\nconst S_XML_DECL_SEPARATOR = 32; // <?xml foo=\"bar\"\nconst S_XML_DECL_ENDING = 33; // <?xml ... ?\nconst S_OPEN_TAG = 34; // <strong\nconst S_OPEN_TAG_SLASH = 35; // <strong /\nconst S_ATTRIB = 36; // <a\nconst S_ATTRIB_NAME = 37; // <a foo\nconst S_ATTRIB_NAME_SAW_WHITE = 38; // <a foo _\nconst S_ATTRIB_VALUE = 39; // <a foo=\nconst S_ATTRIB_VALUE_QUOTED = 40; // <a foo=\"bar\nconst S_ATTRIB_VALUE_CLOSED = 41; // <a foo=\"bar\"\nconst S_ATTRIB_VALUE_UNQUOTED = 42; // <a foo=bar\nconst S_CLOSE_TAG = 43; // </a\nconst S_CLOSE_TAG_SAW_WHITE = 44; // </a   >\nconst TAB = 9;\nconst NL = 0xA;\nconst CR = 0xD;\nconst SPACE = 0x20;\nconst BANG = 0x21;\nconst DQUOTE = 0x22;\nconst AMP = 0x26;\nconst SQUOTE = 0x27;\nconst MINUS = 0x2D;\nconst FORWARD_SLASH = 0x2F;\nconst SEMICOLON = 0x3B;\nconst LESS = 0x3C;\nconst EQUAL = 0x3D;\nconst GREATER = 0x3E;\nconst QUESTION = 0x3F;\nconst OPEN_BRACKET = 0x5B;\nconst CLOSE_BRACKET = 0x5D;\nconst NEL = 0x85;\nconst LS = 0x2028; // Line Separator\nconst isQuote = (c) => c === DQUOTE || c === SQUOTE;\nconst QUOTES = [DQUOTE, SQUOTE];\nconst DOCTYPE_TERMINATOR = [...QUOTES, OPEN_BRACKET, GREATER];\nconst DTD_TERMINATOR = [...QUOTES, LESS, CLOSE_BRACKET];\nconst XML_DECL_NAME_TERMINATOR = [EQUAL, QUESTION, ...S_LIST];\nconst ATTRIB_VALUE_UNQUOTED_TERMINATOR = [...S_LIST, GREATER, AMP, LESS];\nfunction nsPairCheck(parser, prefix, uri) {\n    switch (prefix) {\n        case \"xml\":\n            if (uri !== XML_NAMESPACE) {\n                parser.fail(`xml prefix must be bound to ${XML_NAMESPACE}.`);\n            }\n            break;\n        case \"xmlns\":\n            if (uri !== XMLNS_NAMESPACE) {\n                parser.fail(`xmlns prefix must be bound to ${XMLNS_NAMESPACE}.`);\n            }\n            break;\n        default:\n    }\n    switch (uri) {\n        case XMLNS_NAMESPACE:\n            parser.fail(prefix === \"\" ?\n                `the default namespace may not be set to ${uri}.` :\n                `may not assign a prefix (even \"xmlns\") to the URI \\\n${XMLNS_NAMESPACE}.`);\n            break;\n        case XML_NAMESPACE:\n            switch (prefix) {\n                case \"xml\":\n                    // Assinging the XML namespace to \"xml\" is fine.\n                    break;\n                case \"\":\n                    parser.fail(`the default namespace may not be set to ${uri}.`);\n                    break;\n                default:\n                    parser.fail(\"may not assign the xml namespace to another prefix.\");\n            }\n            break;\n        default:\n    }\n}\nfunction nsMappingCheck(parser, mapping) {\n    for (const local of Object.keys(mapping)) {\n        nsPairCheck(parser, local, mapping[local]);\n    }\n}\nconst isNCName = (name) => NC_NAME_RE.test(name);\nconst isName = (name) => NAME_RE.test(name);\nconst FORBIDDEN_START = 0;\nconst FORBIDDEN_BRACKET = 1;\nconst FORBIDDEN_BRACKET_BRACKET = 2;\n/**\n * The list of supported events.\n */\nexports.EVENTS = [\n    \"xmldecl\",\n    \"text\",\n    \"processinginstruction\",\n    \"doctype\",\n    \"comment\",\n    \"opentagstart\",\n    \"attribute\",\n    \"opentag\",\n    \"closetag\",\n    \"cdata\",\n    \"error\",\n    \"end\",\n    \"ready\",\n];\nconst EVENT_NAME_TO_HANDLER_NAME = {\n    xmldecl: \"xmldeclHandler\",\n    text: \"textHandler\",\n    processinginstruction: \"piHandler\",\n    doctype: \"doctypeHandler\",\n    comment: \"commentHandler\",\n    opentagstart: \"openTagStartHandler\",\n    attribute: \"attributeHandler\",\n    opentag: \"openTagHandler\",\n    closetag: \"closeTagHandler\",\n    cdata: \"cdataHandler\",\n    error: \"errorHandler\",\n    end: \"endHandler\",\n    ready: \"readyHandler\",\n};\nclass SaxesParser {\n    /**\n     * @param opt The parser options.\n     */\n    constructor(opt) {\n        this.opt = opt !== null && opt !== void 0 ? opt : {};\n        this.fragmentOpt = !!this.opt.fragment;\n        const xmlnsOpt = this.xmlnsOpt = !!this.opt.xmlns;\n        this.trackPosition = this.opt.position !== false;\n        this.fileName = this.opt.fileName;\n        if (xmlnsOpt) {\n            // This is the function we use to perform name checks on PIs and entities.\n            // When namespaces are used, colons are not allowed in PI target names or\n            // entity names. So the check depends on whether namespaces are used. See:\n            //\n            // https://www.w3.org/XML/xml-names-19990114-errata.html\n            // NE08\n            //\n            this.nameStartCheck = isNCNameStartChar;\n            this.nameCheck = isNCNameChar;\n            this.isName = isNCName;\n            // eslint-disable-next-line @typescript-eslint/unbound-method\n            this.processAttribs = this.processAttribsNS;\n            // eslint-disable-next-line @typescript-eslint/unbound-method\n            this.pushAttrib = this.pushAttribNS;\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.ns = Object.assign({ __proto__: null }, rootNS);\n            const additional = this.opt.additionalNamespaces;\n            if (additional != null) {\n                nsMappingCheck(this, additional);\n                Object.assign(this.ns, additional);\n            }\n        }\n        else {\n            this.nameStartCheck = isNameStartChar;\n            this.nameCheck = isNameChar;\n            this.isName = isName;\n            // eslint-disable-next-line @typescript-eslint/unbound-method\n            this.processAttribs = this.processAttribsPlain;\n            // eslint-disable-next-line @typescript-eslint/unbound-method\n            this.pushAttrib = this.pushAttribPlain;\n        }\n        //\n        // The order of the members in this table needs to correspond to the state\n        // numbers given to the states that correspond to the methods being recorded\n        // here.\n        //\n        this.stateTable = [\n            /* eslint-disable @typescript-eslint/unbound-method */\n            this.sBegin,\n            this.sBeginWhitespace,\n            this.sDoctype,\n            this.sDoctypeQuote,\n            this.sDTD,\n            this.sDTDQuoted,\n            this.sDTDOpenWaka,\n            this.sDTDOpenWakaBang,\n            this.sDTDComment,\n            this.sDTDCommentEnding,\n            this.sDTDCommentEnded,\n            this.sDTDPI,\n            this.sDTDPIEnding,\n            this.sText,\n            this.sEntity,\n            this.sOpenWaka,\n            this.sOpenWakaBang,\n            this.sComment,\n            this.sCommentEnding,\n            this.sCommentEnded,\n            this.sCData,\n            this.sCDataEnding,\n            this.sCDataEnding2,\n            this.sPIFirstChar,\n            this.sPIRest,\n            this.sPIBody,\n            this.sPIEnding,\n            this.sXMLDeclNameStart,\n            this.sXMLDeclName,\n            this.sXMLDeclEq,\n            this.sXMLDeclValueStart,\n            this.sXMLDeclValue,\n            this.sXMLDeclSeparator,\n            this.sXMLDeclEnding,\n            this.sOpenTag,\n            this.sOpenTagSlash,\n            this.sAttrib,\n            this.sAttribName,\n            this.sAttribNameSawWhite,\n            this.sAttribValue,\n            this.sAttribValueQuoted,\n            this.sAttribValueClosed,\n            this.sAttribValueUnquoted,\n            this.sCloseTag,\n            this.sCloseTagSawWhite,\n        ];\n        this._init();\n    }\n    /**\n     * Indicates whether or not the parser is closed. If ``true``, wait for\n     * the ``ready`` event to write again.\n     */\n    get closed() {\n        return this._closed;\n    }\n    _init() {\n        var _a;\n        this.openWakaBang = \"\";\n        this.text = \"\";\n        this.name = \"\";\n        this.piTarget = \"\";\n        this.entity = \"\";\n        this.q = null;\n        this.tags = [];\n        this.tag = null;\n        this.topNS = null;\n        this.chunk = \"\";\n        this.chunkPosition = 0;\n        this.i = 0;\n        this.prevI = 0;\n        this.carriedFromPrevious = undefined;\n        this.forbiddenState = FORBIDDEN_START;\n        this.attribList = [];\n        // The logic is organized so as to minimize the need to check\n        // this.opt.fragment while parsing.\n        const { fragmentOpt } = this;\n        this.state = fragmentOpt ? S_TEXT : S_BEGIN;\n        // We want these to be all true if we are dealing with a fragment.\n        this.reportedTextBeforeRoot = this.reportedTextAfterRoot = this.closedRoot =\n            this.sawRoot = fragmentOpt;\n        // An XML declaration is intially possible only when parsing whole\n        // documents.\n        this.xmlDeclPossible = !fragmentOpt;\n        this.xmlDeclExpects = [\"version\"];\n        this.entityReturnState = undefined;\n        let { defaultXMLVersion } = this.opt;\n        if (defaultXMLVersion === undefined) {\n            if (this.opt.forceXMLVersion === true) {\n                throw new Error(\"forceXMLVersion set but defaultXMLVersion is not set\");\n            }\n            defaultXMLVersion = \"1.0\";\n        }\n        this.setXMLVersion(defaultXMLVersion);\n        this.positionAtNewLine = 0;\n        this.doctype = false;\n        this._closed = false;\n        this.xmlDecl = {\n            version: undefined,\n            encoding: undefined,\n            standalone: undefined,\n        };\n        this.line = 1;\n        this.column = 0;\n        this.ENTITIES = Object.create(XML_ENTITIES);\n        // eslint-disable-next-line no-unused-expressions\n        (_a = this.readyHandler) === null || _a === void 0 ? void 0 : _a.call(this);\n    }\n    /**\n     * The stream position the parser is currently looking at. This field is\n     * zero-based.\n     *\n     * This field is not based on counting Unicode characters but is to be\n     * interpreted as a plain index into a JavaScript string.\n     */\n    get position() {\n        return this.chunkPosition + this.i;\n    }\n    /**\n     * The column number of the next character to be read by the parser.  *\n     * This field is zero-based. (The first column in a line is 0.)\n     *\n     * This field reports the index at which the next character would be in the\n     * line if the line were represented as a JavaScript string.  Note that this\n     * *can* be different to a count based on the number of *Unicode characters*\n     * due to how JavaScript handles astral plane characters.\n     *\n     * See [[column]] for a number that corresponds to a count of Unicode\n     * characters.\n     */\n    get columnIndex() {\n        return this.position - this.positionAtNewLine;\n    }\n    /**\n     * Set an event listener on an event. The parser supports one handler per\n     * event type. If you try to set an event handler over an existing handler,\n     * the old handler is silently overwritten.\n     *\n     * @param name The event to listen to.\n     *\n     * @param handler The handler to set.\n     */\n    on(name, handler) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this[EVENT_NAME_TO_HANDLER_NAME[name]] = handler;\n    }\n    /**\n     * Unset an event handler.\n     *\n     * @parma name The event to stop listening to.\n     */\n    off(name) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this[EVENT_NAME_TO_HANDLER_NAME[name]] = undefined;\n    }\n    /**\n     * Make an error object. The error object will have a message that contains\n     * the ``fileName`` option passed at the creation of the parser. If position\n     * tracking was turned on, it will also have line and column number\n     * information.\n     *\n     * @param message The message describing the error to report.\n     *\n     * @returns An error object with a properly formatted message.\n     */\n    makeError(message) {\n        var _a;\n        let msg = (_a = this.fileName) !== null && _a !== void 0 ? _a : \"\";\n        if (this.trackPosition) {\n            if (msg.length > 0) {\n                msg += \":\";\n            }\n            msg += `${this.line}:${this.column}`;\n        }\n        if (msg.length > 0) {\n            msg += \": \";\n        }\n        return new Error(msg + message);\n    }\n    /**\n     * Report a parsing error. This method is made public so that client code may\n     * check for issues that are outside the scope of this project and can report\n     * errors.\n     *\n     * @param message The error to report.\n     *\n     * @returns this\n     */\n    fail(message) {\n        const err = this.makeError(message);\n        const handler = this.errorHandler;\n        if (handler === undefined) {\n            throw err;\n        }\n        else {\n            handler(err);\n        }\n        return this;\n    }\n    /**\n     * Write a XML data to the parser.\n     *\n     * @param chunk The XML data to write.\n     *\n     * @returns this\n     */\n    write(chunk) {\n        if (this.closed) {\n            return this.fail(\"cannot write after close; assign an onready handler.\");\n        }\n        let end = false;\n        if (chunk === null) {\n            // We cannot return immediately because carriedFromPrevious may need\n            // processing.\n            end = true;\n            chunk = \"\";\n        }\n        else if (typeof chunk === \"object\") {\n            chunk = chunk.toString();\n        }\n        // We checked if performing a pre-decomposition of the string into an array\n        // of single complete characters (``Array.from(chunk)``) would be faster\n        // than the current repeated calls to ``charCodeAt``. As of August 2018, it\n        // isn't. (There may be Node-specific code that would perform faster than\n        // ``Array.from`` but don't want to be dependent on Node.)\n        if (this.carriedFromPrevious !== undefined) {\n            // The previous chunk had char we must carry over.\n            chunk = `${this.carriedFromPrevious}${chunk}`;\n            this.carriedFromPrevious = undefined;\n        }\n        let limit = chunk.length;\n        const lastCode = chunk.charCodeAt(limit - 1);\n        if (!end &&\n            // A trailing CR or surrogate must be carried over to the next\n            // chunk.\n            (lastCode === CR || (lastCode >= 0xD800 && lastCode <= 0xDBFF))) {\n            // The chunk ends with a character that must be carried over. We cannot\n            // know how to handle it until we get the next chunk or the end of the\n            // stream. So save it for later.\n            this.carriedFromPrevious = chunk[limit - 1];\n            limit--;\n            chunk = chunk.slice(0, limit);\n        }\n        const { stateTable } = this;\n        this.chunk = chunk;\n        this.i = 0;\n        while (this.i < limit) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            stateTable[this.state].call(this);\n        }\n        this.chunkPosition += limit;\n        return end ? this.end() : this;\n    }\n    /**\n     * Close the current stream. Perform final well-formedness checks and reset\n     * the parser tstate.\n     *\n     * @returns this\n     */\n    close() {\n        return this.write(null);\n    }\n    /**\n     * Get a single code point out of the current chunk. This updates the current\n     * position if we do position tracking.\n     *\n     * This is the algorithm to use for XML 1.0.\n     *\n     * @returns The character read.\n     */\n    getCode10() {\n        const { chunk, i } = this;\n        this.prevI = i;\n        // Yes, we do this instead of doing this.i++. Doing it this way, we do not\n        // read this.i again, which is a bit faster.\n        this.i = i + 1;\n        if (i >= chunk.length) {\n            return EOC;\n        }\n        // Using charCodeAt and handling the surrogates ourselves is faster\n        // than using codePointAt.\n        const code = chunk.charCodeAt(i);\n        this.column++;\n        if (code < 0xD800) {\n            if (code >= SPACE || code === TAB) {\n                return code;\n            }\n            switch (code) {\n                case NL:\n                    this.line++;\n                    this.column = 0;\n                    this.positionAtNewLine = this.position;\n                    return NL;\n                case CR:\n                    // We may get NaN if we read past the end of the chunk, which is fine.\n                    if (chunk.charCodeAt(i + 1) === NL) {\n                        // A \\r\\n sequence is converted to \\n so we have to skip over the\n                        // next character. We already know it has a size of 1 so ++ is fine\n                        // here.\n                        this.i = i + 2;\n                    }\n                    // Otherwise, a \\r is just converted to \\n, so we don't have to skip\n                    // ahead.\n                    // In either case, \\r becomes \\n.\n                    this.line++;\n                    this.column = 0;\n                    this.positionAtNewLine = this.position;\n                    return NL_LIKE;\n                default:\n                    // If we get here, then code < SPACE and it is not NL CR or TAB.\n                    this.fail(\"disallowed character.\");\n                    return code;\n            }\n        }\n        if (code > 0xDBFF) {\n            // This is a specialized version of isChar10 that takes into account\n            // that in this context code > 0xDBFF and code <= 0xFFFF. So it does not\n            // test cases that don't need testing.\n            if (!(code >= 0xE000 && code <= 0xFFFD)) {\n                this.fail(\"disallowed character.\");\n            }\n            return code;\n        }\n        const final = 0x10000 + ((code - 0xD800) * 0x400) +\n            (chunk.charCodeAt(i + 1) - 0xDC00);\n        this.i = i + 2;\n        // This is a specialized version of isChar10 that takes into account that in\n        // this context necessarily final >= 0x10000.\n        if (final > 0x10FFFF) {\n            this.fail(\"disallowed character.\");\n        }\n        return final;\n    }\n    /**\n     * Get a single code point out of the current chunk. This updates the current\n     * position if we do position tracking.\n     *\n     * This is the algorithm to use for XML 1.1.\n     *\n     * @returns {number} The character read.\n     */\n    getCode11() {\n        const { chunk, i } = this;\n        this.prevI = i;\n        // Yes, we do this instead of doing this.i++. Doing it this way, we do not\n        // read this.i again, which is a bit faster.\n        this.i = i + 1;\n        if (i >= chunk.length) {\n            return EOC;\n        }\n        // Using charCodeAt and handling the surrogates ourselves is faster\n        // than using codePointAt.\n        const code = chunk.charCodeAt(i);\n        this.column++;\n        if (code < 0xD800) {\n            if ((code > 0x1F && code < 0x7F) || (code > 0x9F && code !== LS) ||\n                code === TAB) {\n                return code;\n            }\n            switch (code) {\n                case NL: // 0xA\n                    this.line++;\n                    this.column = 0;\n                    this.positionAtNewLine = this.position;\n                    return NL;\n                case CR: { // 0xD\n                    // We may get NaN if we read past the end of the chunk, which is\n                    // fine.\n                    const next = chunk.charCodeAt(i + 1);\n                    if (next === NL || next === NEL) {\n                        // A CR NL or CR NEL sequence is converted to NL so we have to skip\n                        // over the next character. We already know it has a size of 1.\n                        this.i = i + 2;\n                    }\n                    // Otherwise, a CR is just converted to NL, no skip.\n                }\n                /* yes, fall through */\n                case NEL: // 0x85\n                case LS: // Ox2028\n                    this.line++;\n                    this.column = 0;\n                    this.positionAtNewLine = this.position;\n                    return NL_LIKE;\n                default:\n                    this.fail(\"disallowed character.\");\n                    return code;\n            }\n        }\n        if (code > 0xDBFF) {\n            // This is a specialized version of isCharAndNotRestricted that takes into\n            // account that in this context code > 0xDBFF and code <= 0xFFFF. So it\n            // does not test cases that don't need testing.\n            if (!(code >= 0xE000 && code <= 0xFFFD)) {\n                this.fail(\"disallowed character.\");\n            }\n            return code;\n        }\n        const final = 0x10000 + ((code - 0xD800) * 0x400) +\n            (chunk.charCodeAt(i + 1) - 0xDC00);\n        this.i = i + 2;\n        // This is a specialized version of isCharAndNotRestricted that takes into\n        // account that in this context necessarily final >= 0x10000.\n        if (final > 0x10FFFF) {\n            this.fail(\"disallowed character.\");\n        }\n        return final;\n    }\n    /**\n     * Like ``getCode`` but with the return value normalized so that ``NL`` is\n     * returned for ``NL_LIKE``.\n     */\n    getCodeNorm() {\n        const c = this.getCode();\n        return c === NL_LIKE ? NL : c;\n    }\n    unget() {\n        this.i = this.prevI;\n        this.column--;\n    }\n    /**\n     * Capture characters into a buffer until encountering one of a set of\n     * characters.\n     *\n     * @param chars An array of codepoints. Encountering a character in the array\n     * ends the capture. (``chars`` may safely contain ``NL``.)\n     *\n     * @return The character code that made the capture end, or ``EOC`` if we hit\n     * the end of the chunk. The return value cannot be NL_LIKE: NL is returned\n     * instead.\n     */\n    captureTo(chars) {\n        let { i: start } = this;\n        const { chunk } = this;\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const c = this.getCode();\n            const isNLLike = c === NL_LIKE;\n            const final = isNLLike ? NL : c;\n            if (final === EOC || chars.includes(final)) {\n                this.text += chunk.slice(start, this.prevI);\n                return final;\n            }\n            if (isNLLike) {\n                this.text += `${chunk.slice(start, this.prevI)}\\n`;\n                start = this.i;\n            }\n        }\n    }\n    /**\n     * Capture characters into a buffer until encountering a character.\n     *\n     * @param char The codepoint that ends the capture. **NOTE ``char`` MAY NOT\n     * CONTAIN ``NL``.** Passing ``NL`` will result in buggy behavior.\n     *\n     * @return ``true`` if we ran into the character. Otherwise, we ran into the\n     * end of the current chunk.\n     */\n    captureToChar(char) {\n        let { i: start } = this;\n        const { chunk } = this;\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            let c = this.getCode();\n            switch (c) {\n                case NL_LIKE:\n                    this.text += `${chunk.slice(start, this.prevI)}\\n`;\n                    start = this.i;\n                    c = NL;\n                    break;\n                case EOC:\n                    this.text += chunk.slice(start);\n                    return false;\n                default:\n            }\n            if (c === char) {\n                this.text += chunk.slice(start, this.prevI);\n                return true;\n            }\n        }\n    }\n    /**\n     * Capture characters that satisfy ``isNameChar`` into the ``name`` field of\n     * this parser.\n     *\n     * @return The character code that made the test fail, or ``EOC`` if we hit\n     * the end of the chunk. The return value cannot be NL_LIKE: NL is returned\n     * instead.\n     */\n    captureNameChars() {\n        const { chunk, i: start } = this;\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const c = this.getCode();\n            if (c === EOC) {\n                this.name += chunk.slice(start);\n                return EOC;\n            }\n            // NL is not a name char so we don't have to test specifically for it.\n            if (!isNameChar(c)) {\n                this.name += chunk.slice(start, this.prevI);\n                return c === NL_LIKE ? NL : c;\n            }\n        }\n    }\n    /**\n     * Skip white spaces.\n     *\n     * @return The character that ended the skip, or ``EOC`` if we hit\n     * the end of the chunk. The return value cannot be NL_LIKE: NL is returned\n     * instead.\n     */\n    skipSpaces() {\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const c = this.getCodeNorm();\n            if (c === EOC || !isS(c)) {\n                return c;\n            }\n        }\n    }\n    setXMLVersion(version) {\n        this.currentXMLVersion = version;\n        /*  eslint-disable @typescript-eslint/unbound-method */\n        if (version === \"1.0\") {\n            this.isChar = isChar10;\n            this.getCode = this.getCode10;\n        }\n        else {\n            this.isChar = isChar11;\n            this.getCode = this.getCode11;\n        }\n        /* eslint-enable @typescript-eslint/unbound-method */\n    }\n    // STATE ENGINE METHODS\n    // This needs to be a state separate from S_BEGIN_WHITESPACE because we want\n    // to be sure never to come back to this state later.\n    sBegin() {\n        // We are essentially peeking at the first character of the chunk. Since\n        // S_BEGIN can be in effect only when we start working on the first chunk,\n        // the index at which we must look is necessarily 0. Note also that the\n        // following test does not depend on decoding surrogates.\n        // If the initial character is 0xFEFF, ignore it.\n        if (this.chunk.charCodeAt(0) === 0xFEFF) {\n            this.i++;\n            this.column++;\n        }\n        this.state = S_BEGIN_WHITESPACE;\n    }\n    sBeginWhitespace() {\n        // We need to know whether we've encountered spaces or not because as soon\n        // as we run into a space, an XML declaration is no longer possible. Rather\n        // than slow down skipSpaces even in places where we don't care whether it\n        // skipped anything or not, we check whether prevI is equal to the value of\n        // i from before we skip spaces.\n        const iBefore = this.i;\n        const c = this.skipSpaces();\n        if (this.prevI !== iBefore) {\n            this.xmlDeclPossible = false;\n        }\n        switch (c) {\n            case LESS:\n                this.state = S_OPEN_WAKA;\n                // We could naively call closeText but in this state, it is not normal\n                // to have text be filled with any data.\n                if (this.text.length !== 0) {\n                    throw new Error(\"no-empty text at start\");\n                }\n                break;\n            case EOC:\n                break;\n            default:\n                this.unget();\n                this.state = S_TEXT;\n                this.xmlDeclPossible = false;\n        }\n    }\n    sDoctype() {\n        var _a;\n        const c = this.captureTo(DOCTYPE_TERMINATOR);\n        switch (c) {\n            case GREATER: {\n                // eslint-disable-next-line no-unused-expressions\n                (_a = this.doctypeHandler) === null || _a === void 0 ? void 0 : _a.call(this, this.text);\n                this.text = \"\";\n                this.state = S_TEXT;\n                this.doctype = true; // just remember that we saw it.\n                break;\n            }\n            case EOC:\n                break;\n            default:\n                this.text += String.fromCodePoint(c);\n                if (c === OPEN_BRACKET) {\n                    this.state = S_DTD;\n                }\n                else if (isQuote(c)) {\n                    this.state = S_DOCTYPE_QUOTE;\n                    this.q = c;\n                }\n        }\n    }\n    sDoctypeQuote() {\n        const q = this.q;\n        if (this.captureToChar(q)) {\n            this.text += String.fromCodePoint(q);\n            this.q = null;\n            this.state = S_DOCTYPE;\n        }\n    }\n    sDTD() {\n        const c = this.captureTo(DTD_TERMINATOR);\n        if (c === EOC) {\n            return;\n        }\n        this.text += String.fromCodePoint(c);\n        if (c === CLOSE_BRACKET) {\n            this.state = S_DOCTYPE;\n        }\n        else if (c === LESS) {\n            this.state = S_DTD_OPEN_WAKA;\n        }\n        else if (isQuote(c)) {\n            this.state = S_DTD_QUOTED;\n            this.q = c;\n        }\n    }\n    sDTDQuoted() {\n        const q = this.q;\n        if (this.captureToChar(q)) {\n            this.text += String.fromCodePoint(q);\n            this.state = S_DTD;\n            this.q = null;\n        }\n    }\n    sDTDOpenWaka() {\n        const c = this.getCodeNorm();\n        this.text += String.fromCodePoint(c);\n        switch (c) {\n            case BANG:\n                this.state = S_DTD_OPEN_WAKA_BANG;\n                this.openWakaBang = \"\";\n                break;\n            case QUESTION:\n                this.state = S_DTD_PI;\n                break;\n            default:\n                this.state = S_DTD;\n        }\n    }\n    sDTDOpenWakaBang() {\n        const char = String.fromCodePoint(this.getCodeNorm());\n        const owb = this.openWakaBang += char;\n        this.text += char;\n        if (owb !== \"-\") {\n            this.state = owb === \"--\" ? S_DTD_COMMENT : S_DTD;\n            this.openWakaBang = \"\";\n        }\n    }\n    sDTDComment() {\n        if (this.captureToChar(MINUS)) {\n            this.text += \"-\";\n            this.state = S_DTD_COMMENT_ENDING;\n        }\n    }\n    sDTDCommentEnding() {\n        const c = this.getCodeNorm();\n        this.text += String.fromCodePoint(c);\n        this.state = c === MINUS ? S_DTD_COMMENT_ENDED : S_DTD_COMMENT;\n    }\n    sDTDCommentEnded() {\n        const c = this.getCodeNorm();\n        this.text += String.fromCodePoint(c);\n        if (c === GREATER) {\n            this.state = S_DTD;\n        }\n        else {\n            this.fail(\"malformed comment.\");\n            // <!-- blah -- bloo --> will be recorded as\n            // a comment of \" blah -- bloo \"\n            this.state = S_DTD_COMMENT;\n        }\n    }\n    sDTDPI() {\n        if (this.captureToChar(QUESTION)) {\n            this.text += \"?\";\n            this.state = S_DTD_PI_ENDING;\n        }\n    }\n    sDTDPIEnding() {\n        const c = this.getCodeNorm();\n        this.text += String.fromCodePoint(c);\n        if (c === GREATER) {\n            this.state = S_DTD;\n        }\n    }\n    sText() {\n        //\n        // We did try a version of saxes where the S_TEXT state was split in two\n        // states: one for text inside the root element, and one for text\n        // outside. This was avoiding having to test this.tags.length to decide\n        // what implementation to actually use.\n        //\n        // Peformance testing on gigabyte-size files did not show any advantage to\n        // using the two states solution instead of the current one. Conversely, it\n        // made the code a bit more complicated elsewhere. For instance, a comment\n        // can appear before the root element so when a comment ended it was\n        // necessary to determine whether to return to the S_TEXT state or to the\n        // new text-outside-root state.\n        //\n        if (this.tags.length !== 0) {\n            this.handleTextInRoot();\n        }\n        else {\n            this.handleTextOutsideRoot();\n        }\n    }\n    sEntity() {\n        // This is essentially a specialized version of captureToChar(SEMICOLON...)\n        let { i: start } = this;\n        const { chunk } = this;\n        // eslint-disable-next-line no-labels, no-restricted-syntax\n        loop: \n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            switch (this.getCode()) {\n                case NL_LIKE:\n                    this.entity += `${chunk.slice(start, this.prevI)}\\n`;\n                    start = this.i;\n                    break;\n                case SEMICOLON: {\n                    const { entityReturnState } = this;\n                    const entity = this.entity + chunk.slice(start, this.prevI);\n                    this.state = entityReturnState;\n                    let parsed;\n                    if (entity === \"\") {\n                        this.fail(\"empty entity name.\");\n                        parsed = \"&;\";\n                    }\n                    else {\n                        parsed = this.parseEntity(entity);\n                        this.entity = \"\";\n                    }\n                    if (entityReturnState !== S_TEXT || this.textHandler !== undefined) {\n                        this.text += parsed;\n                    }\n                    // eslint-disable-next-line no-labels\n                    break loop;\n                }\n                case EOC:\n                    this.entity += chunk.slice(start);\n                    // eslint-disable-next-line no-labels\n                    break loop;\n                default:\n            }\n        }\n    }\n    sOpenWaka() {\n        // Reminder: a state handler is called with at least one character\n        // available in the current chunk. So the first call to get code inside of\n        // a state handler cannot return ``EOC``. That's why we don't test\n        // for it.\n        const c = this.getCode();\n        // either a /, ?, !, or text is coming next.\n        if (isNameStartChar(c)) {\n            this.state = S_OPEN_TAG;\n            this.unget();\n            this.xmlDeclPossible = false;\n        }\n        else {\n            switch (c) {\n                case FORWARD_SLASH:\n                    this.state = S_CLOSE_TAG;\n                    this.xmlDeclPossible = false;\n                    break;\n                case BANG:\n                    this.state = S_OPEN_WAKA_BANG;\n                    this.openWakaBang = \"\";\n                    this.xmlDeclPossible = false;\n                    break;\n                case QUESTION:\n                    this.state = S_PI_FIRST_CHAR;\n                    break;\n                default:\n                    this.fail(\"disallowed character in tag name\");\n                    this.state = S_TEXT;\n                    this.xmlDeclPossible = false;\n            }\n        }\n    }\n    sOpenWakaBang() {\n        this.openWakaBang += String.fromCodePoint(this.getCodeNorm());\n        switch (this.openWakaBang) {\n            case \"[CDATA[\":\n                if (!this.sawRoot && !this.reportedTextBeforeRoot) {\n                    this.fail(\"text data outside of root node.\");\n                    this.reportedTextBeforeRoot = true;\n                }\n                if (this.closedRoot && !this.reportedTextAfterRoot) {\n                    this.fail(\"text data outside of root node.\");\n                    this.reportedTextAfterRoot = true;\n                }\n                this.state = S_CDATA;\n                this.openWakaBang = \"\";\n                break;\n            case \"--\":\n                this.state = S_COMMENT;\n                this.openWakaBang = \"\";\n                break;\n            case \"DOCTYPE\":\n                this.state = S_DOCTYPE;\n                if (this.doctype || this.sawRoot) {\n                    this.fail(\"inappropriately located doctype declaration.\");\n                }\n                this.openWakaBang = \"\";\n                break;\n            default:\n                // 7 happens to be the maximum length of the string that can possibly\n                // match one of the cases above.\n                if (this.openWakaBang.length >= 7) {\n                    this.fail(\"incorrect syntax.\");\n                }\n        }\n    }\n    sComment() {\n        if (this.captureToChar(MINUS)) {\n            this.state = S_COMMENT_ENDING;\n        }\n    }\n    sCommentEnding() {\n        var _a;\n        const c = this.getCodeNorm();\n        if (c === MINUS) {\n            this.state = S_COMMENT_ENDED;\n            // eslint-disable-next-line no-unused-expressions\n            (_a = this.commentHandler) === null || _a === void 0 ? void 0 : _a.call(this, this.text);\n            this.text = \"\";\n        }\n        else {\n            this.text += `-${String.fromCodePoint(c)}`;\n            this.state = S_COMMENT;\n        }\n    }\n    sCommentEnded() {\n        const c = this.getCodeNorm();\n        if (c !== GREATER) {\n            this.fail(\"malformed comment.\");\n            // <!-- blah -- bloo --> will be recorded as\n            // a comment of \" blah -- bloo \"\n            this.text += `--${String.fromCodePoint(c)}`;\n            this.state = S_COMMENT;\n        }\n        else {\n            this.state = S_TEXT;\n        }\n    }\n    sCData() {\n        if (this.captureToChar(CLOSE_BRACKET)) {\n            this.state = S_CDATA_ENDING;\n        }\n    }\n    sCDataEnding() {\n        const c = this.getCodeNorm();\n        if (c === CLOSE_BRACKET) {\n            this.state = S_CDATA_ENDING_2;\n        }\n        else {\n            this.text += `]${String.fromCodePoint(c)}`;\n            this.state = S_CDATA;\n        }\n    }\n    sCDataEnding2() {\n        var _a;\n        const c = this.getCodeNorm();\n        switch (c) {\n            case GREATER: {\n                // eslint-disable-next-line no-unused-expressions\n                (_a = this.cdataHandler) === null || _a === void 0 ? void 0 : _a.call(this, this.text);\n                this.text = \"\";\n                this.state = S_TEXT;\n                break;\n            }\n            case CLOSE_BRACKET:\n                this.text += \"]\";\n                break;\n            default:\n                this.text += `]]${String.fromCodePoint(c)}`;\n                this.state = S_CDATA;\n        }\n    }\n    // We need this separate state to check the first character fo the pi target\n    // with this.nameStartCheck which allows less characters than this.nameCheck.\n    sPIFirstChar() {\n        const c = this.getCodeNorm();\n        // This is first because in the case where the file is well-formed this is\n        // the branch taken. We optimize for well-formedness.\n        if (this.nameStartCheck(c)) {\n            this.piTarget += String.fromCodePoint(c);\n            this.state = S_PI_REST;\n        }\n        else if (c === QUESTION || isS(c)) {\n            this.fail(\"processing instruction without a target.\");\n            this.state = c === QUESTION ? S_PI_ENDING : S_PI_BODY;\n        }\n        else {\n            this.fail(\"disallowed character in processing instruction name.\");\n            this.piTarget += String.fromCodePoint(c);\n            this.state = S_PI_REST;\n        }\n    }\n    sPIRest() {\n        // Capture characters into a piTarget while ``this.nameCheck`` run on the\n        // character read returns true.\n        const { chunk, i: start } = this;\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const c = this.getCodeNorm();\n            if (c === EOC) {\n                this.piTarget += chunk.slice(start);\n                return;\n            }\n            // NL cannot satisfy this.nameCheck so we don't have to test specifically\n            // for it.\n            if (!this.nameCheck(c)) {\n                this.piTarget += chunk.slice(start, this.prevI);\n                const isQuestion = c === QUESTION;\n                if (isQuestion || isS(c)) {\n                    if (this.piTarget === \"xml\") {\n                        if (!this.xmlDeclPossible) {\n                            this.fail(\"an XML declaration must be at the start of the document.\");\n                        }\n                        this.state = isQuestion ? S_XML_DECL_ENDING : S_XML_DECL_NAME_START;\n                    }\n                    else {\n                        this.state = isQuestion ? S_PI_ENDING : S_PI_BODY;\n                    }\n                }\n                else {\n                    this.fail(\"disallowed character in processing instruction name.\");\n                    this.piTarget += String.fromCodePoint(c);\n                }\n                break;\n            }\n        }\n    }\n    sPIBody() {\n        if (this.text.length === 0) {\n            const c = this.getCodeNorm();\n            if (c === QUESTION) {\n                this.state = S_PI_ENDING;\n            }\n            else if (!isS(c)) {\n                this.text = String.fromCodePoint(c);\n            }\n        }\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        else if (this.captureToChar(QUESTION)) {\n            this.state = S_PI_ENDING;\n        }\n    }\n    sPIEnding() {\n        var _a;\n        const c = this.getCodeNorm();\n        if (c === GREATER) {\n            const { piTarget } = this;\n            if (piTarget.toLowerCase() === \"xml\") {\n                this.fail(\"the XML declaration must appear at the start of the document.\");\n            }\n            // eslint-disable-next-line no-unused-expressions\n            (_a = this.piHandler) === null || _a === void 0 ? void 0 : _a.call(this, {\n                target: piTarget,\n                body: this.text,\n            });\n            this.piTarget = this.text = \"\";\n            this.state = S_TEXT;\n        }\n        else if (c === QUESTION) {\n            // We ran into ?? as part of a processing instruction. We initially took\n            // the first ? as a sign that the PI was ending, but it is not. So we have\n            // to add it to the body but we take the new ? as a sign that the PI is\n            // ending.\n            this.text += \"?\";\n        }\n        else {\n            this.text += `?${String.fromCodePoint(c)}`;\n            this.state = S_PI_BODY;\n        }\n        this.xmlDeclPossible = false;\n    }\n    sXMLDeclNameStart() {\n        const c = this.skipSpaces();\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        if (c === QUESTION) {\n            // It is valid to go to S_XML_DECL_ENDING from this state.\n            this.state = S_XML_DECL_ENDING;\n            return;\n        }\n        if (c !== EOC) {\n            this.state = S_XML_DECL_NAME;\n            this.name = String.fromCodePoint(c);\n        }\n    }\n    sXMLDeclName() {\n        const c = this.captureTo(XML_DECL_NAME_TERMINATOR);\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        if (c === QUESTION) {\n            this.state = S_XML_DECL_ENDING;\n            this.name += this.text;\n            this.text = \"\";\n            this.fail(\"XML declaration is incomplete.\");\n            return;\n        }\n        if (!(isS(c) || c === EQUAL)) {\n            return;\n        }\n        this.name += this.text;\n        this.text = \"\";\n        if (!this.xmlDeclExpects.includes(this.name)) {\n            switch (this.name.length) {\n                case 0:\n                    this.fail(\"did not expect any more name/value pairs.\");\n                    break;\n                case 1:\n                    this.fail(`expected the name ${this.xmlDeclExpects[0]}.`);\n                    break;\n                default:\n                    this.fail(`expected one of ${this.xmlDeclExpects.join(\", \")}`);\n            }\n        }\n        this.state = c === EQUAL ? S_XML_DECL_VALUE_START : S_XML_DECL_EQ;\n    }\n    sXMLDeclEq() {\n        const c = this.getCodeNorm();\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        if (c === QUESTION) {\n            this.state = S_XML_DECL_ENDING;\n            this.fail(\"XML declaration is incomplete.\");\n            return;\n        }\n        if (isS(c)) {\n            return;\n        }\n        if (c !== EQUAL) {\n            this.fail(\"value required.\");\n        }\n        this.state = S_XML_DECL_VALUE_START;\n    }\n    sXMLDeclValueStart() {\n        const c = this.getCodeNorm();\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        if (c === QUESTION) {\n            this.state = S_XML_DECL_ENDING;\n            this.fail(\"XML declaration is incomplete.\");\n            return;\n        }\n        if (isS(c)) {\n            return;\n        }\n        if (!isQuote(c)) {\n            this.fail(\"value must be quoted.\");\n            this.q = SPACE;\n        }\n        else {\n            this.q = c;\n        }\n        this.state = S_XML_DECL_VALUE;\n    }\n    sXMLDeclValue() {\n        const c = this.captureTo([this.q, QUESTION]);\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        if (c === QUESTION) {\n            this.state = S_XML_DECL_ENDING;\n            this.text = \"\";\n            this.fail(\"XML declaration is incomplete.\");\n            return;\n        }\n        if (c === EOC) {\n            return;\n        }\n        const value = this.text;\n        this.text = \"\";\n        switch (this.name) {\n            case \"version\": {\n                this.xmlDeclExpects = [\"encoding\", \"standalone\"];\n                const version = value;\n                this.xmlDecl.version = version;\n                // This is the test specified by XML 1.0 but it is fine for XML 1.1.\n                if (!/^1\\.[0-9]+$/.test(version)) {\n                    this.fail(\"version number must match /^1\\\\.[0-9]+$/.\");\n                }\n                // When forceXMLVersion is set, the XML declaration is ignored.\n                else if (!this.opt.forceXMLVersion) {\n                    this.setXMLVersion(version);\n                }\n                break;\n            }\n            case \"encoding\":\n                if (!/^[A-Za-z][A-Za-z0-9._-]*$/.test(value)) {\n                    this.fail(\"encoding value must match \\\n/^[A-Za-z0-9][A-Za-z0-9._-]*$/.\");\n                }\n                this.xmlDeclExpects = [\"standalone\"];\n                this.xmlDecl.encoding = value;\n                break;\n            case \"standalone\":\n                if (value !== \"yes\" && value !== \"no\") {\n                    this.fail(\"standalone value must match \\\"yes\\\" or \\\"no\\\".\");\n                }\n                this.xmlDeclExpects = [];\n                this.xmlDecl.standalone = value;\n                break;\n            default:\n            // We don't need to raise an error here since we've already raised one\n            // when checking what name was expected.\n        }\n        this.name = \"\";\n        this.state = S_XML_DECL_SEPARATOR;\n    }\n    sXMLDeclSeparator() {\n        const c = this.getCodeNorm();\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        if (c === QUESTION) {\n            // It is valid to go to S_XML_DECL_ENDING from this state.\n            this.state = S_XML_DECL_ENDING;\n            return;\n        }\n        if (!isS(c)) {\n            this.fail(\"whitespace required.\");\n            this.unget();\n        }\n        this.state = S_XML_DECL_NAME_START;\n    }\n    sXMLDeclEnding() {\n        var _a;\n        const c = this.getCodeNorm();\n        if (c === GREATER) {\n            if (this.piTarget !== \"xml\") {\n                this.fail(\"processing instructions are not allowed before root.\");\n            }\n            else if (this.name !== \"version\" &&\n                this.xmlDeclExpects.includes(\"version\")) {\n                this.fail(\"XML declaration must contain a version.\");\n            }\n            // eslint-disable-next-line no-unused-expressions\n            (_a = this.xmldeclHandler) === null || _a === void 0 ? void 0 : _a.call(this, this.xmlDecl);\n            this.name = \"\";\n            this.piTarget = this.text = \"\";\n            this.state = S_TEXT;\n        }\n        else {\n            // We got here because the previous character was a ?, but the question\n            // mark character is not valid inside any of the XML declaration\n            // name/value pairs.\n            this.fail(\"The character ? is disallowed anywhere in XML declarations.\");\n        }\n        this.xmlDeclPossible = false;\n    }\n    sOpenTag() {\n        var _a;\n        const c = this.captureNameChars();\n        if (c === EOC) {\n            return;\n        }\n        const tag = this.tag = {\n            name: this.name,\n            attributes: Object.create(null),\n        };\n        this.name = \"\";\n        if (this.xmlnsOpt) {\n            this.topNS = tag.ns = Object.create(null);\n        }\n        // eslint-disable-next-line no-unused-expressions\n        (_a = this.openTagStartHandler) === null || _a === void 0 ? void 0 : _a.call(this, tag);\n        this.sawRoot = true;\n        if (!this.fragmentOpt && this.closedRoot) {\n            this.fail(\"documents may contain only one root.\");\n        }\n        switch (c) {\n            case GREATER:\n                this.openTag();\n                break;\n            case FORWARD_SLASH:\n                this.state = S_OPEN_TAG_SLASH;\n                break;\n            default:\n                if (!isS(c)) {\n                    this.fail(\"disallowed character in tag name.\");\n                }\n                this.state = S_ATTRIB;\n        }\n    }\n    sOpenTagSlash() {\n        if (this.getCode() === GREATER) {\n            this.openSelfClosingTag();\n        }\n        else {\n            this.fail(\"forward-slash in opening tag not followed by >.\");\n            this.state = S_ATTRIB;\n        }\n    }\n    sAttrib() {\n        const c = this.skipSpaces();\n        if (c === EOC) {\n            return;\n        }\n        if (isNameStartChar(c)) {\n            this.unget();\n            this.state = S_ATTRIB_NAME;\n        }\n        else if (c === GREATER) {\n            this.openTag();\n        }\n        else if (c === FORWARD_SLASH) {\n            this.state = S_OPEN_TAG_SLASH;\n        }\n        else {\n            this.fail(\"disallowed character in attribute name.\");\n        }\n    }\n    sAttribName() {\n        const c = this.captureNameChars();\n        if (c === EQUAL) {\n            this.state = S_ATTRIB_VALUE;\n        }\n        else if (isS(c)) {\n            this.state = S_ATTRIB_NAME_SAW_WHITE;\n        }\n        else if (c === GREATER) {\n            this.fail(\"attribute without value.\");\n            this.pushAttrib(this.name, this.name);\n            this.name = this.text = \"\";\n            this.openTag();\n        }\n        else if (c !== EOC) {\n            this.fail(\"disallowed character in attribute name.\");\n        }\n    }\n    sAttribNameSawWhite() {\n        const c = this.skipSpaces();\n        switch (c) {\n            case EOC:\n                return;\n            case EQUAL:\n                this.state = S_ATTRIB_VALUE;\n                break;\n            default:\n                this.fail(\"attribute without value.\");\n                // Should we do this???\n                // this.tag.attributes[this.name] = \"\";\n                this.text = \"\";\n                this.name = \"\";\n                if (c === GREATER) {\n                    this.openTag();\n                }\n                else if (isNameStartChar(c)) {\n                    this.unget();\n                    this.state = S_ATTRIB_NAME;\n                }\n                else {\n                    this.fail(\"disallowed character in attribute name.\");\n                    this.state = S_ATTRIB;\n                }\n        }\n    }\n    sAttribValue() {\n        const c = this.getCodeNorm();\n        if (isQuote(c)) {\n            this.q = c;\n            this.state = S_ATTRIB_VALUE_QUOTED;\n        }\n        else if (!isS(c)) {\n            this.fail(\"unquoted attribute value.\");\n            this.state = S_ATTRIB_VALUE_UNQUOTED;\n            this.unget();\n        }\n    }\n    sAttribValueQuoted() {\n        // We deliberately do not use captureTo here. The specialized code we use\n        // here is faster than using captureTo.\n        const { q, chunk } = this;\n        let { i: start } = this;\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            switch (this.getCode()) {\n                case q:\n                    this.pushAttrib(this.name, this.text + chunk.slice(start, this.prevI));\n                    this.name = this.text = \"\";\n                    this.q = null;\n                    this.state = S_ATTRIB_VALUE_CLOSED;\n                    return;\n                case AMP:\n                    this.text += chunk.slice(start, this.prevI);\n                    this.state = S_ENTITY;\n                    this.entityReturnState = S_ATTRIB_VALUE_QUOTED;\n                    return;\n                case NL:\n                case NL_LIKE:\n                case TAB:\n                    this.text += `${chunk.slice(start, this.prevI)} `;\n                    start = this.i;\n                    break;\n                case LESS:\n                    this.text += chunk.slice(start, this.prevI);\n                    this.fail(\"disallowed character.\");\n                    return;\n                case EOC:\n                    this.text += chunk.slice(start);\n                    return;\n                default:\n            }\n        }\n    }\n    sAttribValueClosed() {\n        const c = this.getCodeNorm();\n        if (isS(c)) {\n            this.state = S_ATTRIB;\n        }\n        else if (c === GREATER) {\n            this.openTag();\n        }\n        else if (c === FORWARD_SLASH) {\n            this.state = S_OPEN_TAG_SLASH;\n        }\n        else if (isNameStartChar(c)) {\n            this.fail(\"no whitespace between attributes.\");\n            this.unget();\n            this.state = S_ATTRIB_NAME;\n        }\n        else {\n            this.fail(\"disallowed character in attribute name.\");\n        }\n    }\n    sAttribValueUnquoted() {\n        // We don't do anything regarding EOL or space handling for unquoted\n        // attributes. We already have failed by the time we get here, and the\n        // contract that saxes upholds states that upon failure, it is not safe to\n        // rely on the data passed to event handlers (other than\n        // ``onerror``). Passing \"bad\" data is not a problem.\n        const c = this.captureTo(ATTRIB_VALUE_UNQUOTED_TERMINATOR);\n        switch (c) {\n            case AMP:\n                this.state = S_ENTITY;\n                this.entityReturnState = S_ATTRIB_VALUE_UNQUOTED;\n                break;\n            case LESS:\n                this.fail(\"disallowed character.\");\n                break;\n            case EOC:\n                break;\n            default:\n                if (this.text.includes(\"]]>\")) {\n                    this.fail(\"the string \\\"]]>\\\" is disallowed in char data.\");\n                }\n                this.pushAttrib(this.name, this.text);\n                this.name = this.text = \"\";\n                if (c === GREATER) {\n                    this.openTag();\n                }\n                else {\n                    this.state = S_ATTRIB;\n                }\n        }\n    }\n    sCloseTag() {\n        const c = this.captureNameChars();\n        if (c === GREATER) {\n            this.closeTag();\n        }\n        else if (isS(c)) {\n            this.state = S_CLOSE_TAG_SAW_WHITE;\n        }\n        else if (c !== EOC) {\n            this.fail(\"disallowed character in closing tag.\");\n        }\n    }\n    sCloseTagSawWhite() {\n        switch (this.skipSpaces()) {\n            case GREATER:\n                this.closeTag();\n                break;\n            case EOC:\n                break;\n            default:\n                this.fail(\"disallowed character in closing tag.\");\n        }\n    }\n    // END OF STATE ENGINE METHODS\n    handleTextInRoot() {\n        // This is essentially a specialized version of captureTo which is optimized\n        // for performing the ]]> check. A previous version of this code, checked\n        // ``this.text`` for the presence of ]]>. It simplified the code but was\n        // very costly when character data contained a lot of entities to be parsed.\n        //\n        // Since we are using a specialized loop, we also keep track of the presence\n        // of ]]> in text data. The sequence ]]> is forbidden to appear as-is.\n        //\n        let { i: start, forbiddenState } = this;\n        const { chunk, textHandler: handler } = this;\n        // eslint-disable-next-line no-labels, no-restricted-syntax\n        scanLoop: \n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            switch (this.getCode()) {\n                case LESS: {\n                    this.state = S_OPEN_WAKA;\n                    if (handler !== undefined) {\n                        const { text } = this;\n                        const slice = chunk.slice(start, this.prevI);\n                        if (text.length !== 0) {\n                            handler(text + slice);\n                            this.text = \"\";\n                        }\n                        else if (slice.length !== 0) {\n                            handler(slice);\n                        }\n                    }\n                    forbiddenState = FORBIDDEN_START;\n                    // eslint-disable-next-line no-labels\n                    break scanLoop;\n                }\n                case AMP:\n                    this.state = S_ENTITY;\n                    this.entityReturnState = S_TEXT;\n                    if (handler !== undefined) {\n                        this.text += chunk.slice(start, this.prevI);\n                    }\n                    forbiddenState = FORBIDDEN_START;\n                    // eslint-disable-next-line no-labels\n                    break scanLoop;\n                case CLOSE_BRACKET:\n                    switch (forbiddenState) {\n                        case FORBIDDEN_START:\n                            forbiddenState = FORBIDDEN_BRACKET;\n                            break;\n                        case FORBIDDEN_BRACKET:\n                            forbiddenState = FORBIDDEN_BRACKET_BRACKET;\n                            break;\n                        case FORBIDDEN_BRACKET_BRACKET:\n                            break;\n                        default:\n                            throw new Error(\"impossible state\");\n                    }\n                    break;\n                case GREATER:\n                    if (forbiddenState === FORBIDDEN_BRACKET_BRACKET) {\n                        this.fail(\"the string \\\"]]>\\\" is disallowed in char data.\");\n                    }\n                    forbiddenState = FORBIDDEN_START;\n                    break;\n                case NL_LIKE:\n                    if (handler !== undefined) {\n                        this.text += `${chunk.slice(start, this.prevI)}\\n`;\n                    }\n                    start = this.i;\n                    forbiddenState = FORBIDDEN_START;\n                    break;\n                case EOC:\n                    if (handler !== undefined) {\n                        this.text += chunk.slice(start);\n                    }\n                    // eslint-disable-next-line no-labels\n                    break scanLoop;\n                default:\n                    forbiddenState = FORBIDDEN_START;\n            }\n        }\n        this.forbiddenState = forbiddenState;\n    }\n    handleTextOutsideRoot() {\n        // This is essentially a specialized version of captureTo which is optimized\n        // for a specialized task. We keep track of the presence of non-space\n        // characters in the text since these are errors when appearing outside the\n        // document root element.\n        let { i: start } = this;\n        const { chunk, textHandler: handler } = this;\n        let nonSpace = false;\n        // eslint-disable-next-line no-labels, no-restricted-syntax\n        outRootLoop: \n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const code = this.getCode();\n            switch (code) {\n                case LESS: {\n                    this.state = S_OPEN_WAKA;\n                    if (handler !== undefined) {\n                        const { text } = this;\n                        const slice = chunk.slice(start, this.prevI);\n                        if (text.length !== 0) {\n                            handler(text + slice);\n                            this.text = \"\";\n                        }\n                        else if (slice.length !== 0) {\n                            handler(slice);\n                        }\n                    }\n                    // eslint-disable-next-line no-labels\n                    break outRootLoop;\n                }\n                case AMP:\n                    this.state = S_ENTITY;\n                    this.entityReturnState = S_TEXT;\n                    if (handler !== undefined) {\n                        this.text += chunk.slice(start, this.prevI);\n                    }\n                    nonSpace = true;\n                    // eslint-disable-next-line no-labels\n                    break outRootLoop;\n                case NL_LIKE:\n                    if (handler !== undefined) {\n                        this.text += `${chunk.slice(start, this.prevI)}\\n`;\n                    }\n                    start = this.i;\n                    break;\n                case EOC:\n                    if (handler !== undefined) {\n                        this.text += chunk.slice(start);\n                    }\n                    // eslint-disable-next-line no-labels\n                    break outRootLoop;\n                default:\n                    if (!isS(code)) {\n                        nonSpace = true;\n                    }\n            }\n        }\n        if (!nonSpace) {\n            return;\n        }\n        // We use the reportedTextBeforeRoot and reportedTextAfterRoot flags\n        // to avoid reporting errors for every single character that is out of\n        // place.\n        if (!this.sawRoot && !this.reportedTextBeforeRoot) {\n            this.fail(\"text data outside of root node.\");\n            this.reportedTextBeforeRoot = true;\n        }\n        if (this.closedRoot && !this.reportedTextAfterRoot) {\n            this.fail(\"text data outside of root node.\");\n            this.reportedTextAfterRoot = true;\n        }\n    }\n    pushAttribNS(name, value) {\n        var _a;\n        const { prefix, local } = this.qname(name);\n        const attr = { name, prefix, local, value };\n        this.attribList.push(attr);\n        // eslint-disable-next-line no-unused-expressions\n        (_a = this.attributeHandler) === null || _a === void 0 ? void 0 : _a.call(this, attr);\n        if (prefix === \"xmlns\") {\n            const trimmed = value.trim();\n            if (this.currentXMLVersion === \"1.0\" && trimmed === \"\") {\n                this.fail(\"invalid attempt to undefine prefix in XML 1.0\");\n            }\n            this.topNS[local] = trimmed;\n            nsPairCheck(this, local, trimmed);\n        }\n        else if (name === \"xmlns\") {\n            const trimmed = value.trim();\n            this.topNS[\"\"] = trimmed;\n            nsPairCheck(this, \"\", trimmed);\n        }\n    }\n    pushAttribPlain(name, value) {\n        var _a;\n        const attr = { name, value };\n        this.attribList.push(attr);\n        // eslint-disable-next-line no-unused-expressions\n        (_a = this.attributeHandler) === null || _a === void 0 ? void 0 : _a.call(this, attr);\n    }\n    /**\n     * End parsing. This performs final well-formedness checks and resets the\n     * parser to a clean state.\n     *\n     * @returns this\n     */\n    end() {\n        var _a, _b;\n        if (!this.sawRoot) {\n            this.fail(\"document must contain a root element.\");\n        }\n        const { tags } = this;\n        while (tags.length > 0) {\n            const tag = tags.pop();\n            this.fail(`unclosed tag: ${tag.name}`);\n        }\n        if ((this.state !== S_BEGIN) && (this.state !== S_TEXT)) {\n            this.fail(\"unexpected end.\");\n        }\n        const { text } = this;\n        if (text.length !== 0) {\n            // eslint-disable-next-line no-unused-expressions\n            (_a = this.textHandler) === null || _a === void 0 ? void 0 : _a.call(this, text);\n            this.text = \"\";\n        }\n        this._closed = true;\n        // eslint-disable-next-line no-unused-expressions\n        (_b = this.endHandler) === null || _b === void 0 ? void 0 : _b.call(this);\n        this._init();\n        return this;\n    }\n    /**\n     * Resolve a namespace prefix.\n     *\n     * @param prefix The prefix to resolve.\n     *\n     * @returns The namespace URI or ``undefined`` if the prefix is not defined.\n     */\n    resolve(prefix) {\n        var _a, _b;\n        let uri = this.topNS[prefix];\n        if (uri !== undefined) {\n            return uri;\n        }\n        const { tags } = this;\n        for (let index = tags.length - 1; index >= 0; index--) {\n            uri = tags[index].ns[prefix];\n            if (uri !== undefined) {\n                return uri;\n            }\n        }\n        uri = this.ns[prefix];\n        if (uri !== undefined) {\n            return uri;\n        }\n        return (_b = (_a = this.opt).resolvePrefix) === null || _b === void 0 ? void 0 : _b.call(_a, prefix);\n    }\n    /**\n     * Parse a qname into its prefix and local name parts.\n     *\n     * @param name The name to parse\n     *\n     * @returns\n     */\n    qname(name) {\n        // This is faster than using name.split(\":\").\n        const colon = name.indexOf(\":\");\n        if (colon === -1) {\n            return { prefix: \"\", local: name };\n        }\n        const local = name.slice(colon + 1);\n        const prefix = name.slice(0, colon);\n        if (prefix === \"\" || local === \"\" || local.includes(\":\")) {\n            this.fail(`malformed name: ${name}.`);\n        }\n        return { prefix, local };\n    }\n    processAttribsNS() {\n        var _a;\n        const { attribList } = this;\n        const tag = this.tag;\n        {\n            // add namespace info to tag\n            const { prefix, local } = this.qname(tag.name);\n            tag.prefix = prefix;\n            tag.local = local;\n            const uri = tag.uri = (_a = this.resolve(prefix)) !== null && _a !== void 0 ? _a : \"\";\n            if (prefix !== \"\") {\n                if (prefix === \"xmlns\") {\n                    this.fail(\"tags may not have \\\"xmlns\\\" as prefix.\");\n                }\n                if (uri === \"\") {\n                    this.fail(`unbound namespace prefix: ${JSON.stringify(prefix)}.`);\n                    tag.uri = prefix;\n                }\n            }\n        }\n        if (attribList.length === 0) {\n            return;\n        }\n        const { attributes } = tag;\n        const seen = new Set();\n        // Note: do not apply default ns to attributes:\n        //   http://www.w3.org/TR/REC-xml-names/#defaulting\n        for (const attr of attribList) {\n            const { name, prefix, local } = attr;\n            let uri;\n            let eqname;\n            if (prefix === \"\") {\n                uri = name === \"xmlns\" ? XMLNS_NAMESPACE : \"\";\n                eqname = name;\n            }\n            else {\n                uri = this.resolve(prefix);\n                // if there's any attributes with an undefined namespace,\n                // then fail on them now.\n                if (uri === undefined) {\n                    this.fail(`unbound namespace prefix: ${JSON.stringify(prefix)}.`);\n                    uri = prefix;\n                }\n                eqname = `{${uri}}${local}`;\n            }\n            if (seen.has(eqname)) {\n                this.fail(`duplicate attribute: ${eqname}.`);\n            }\n            seen.add(eqname);\n            attr.uri = uri;\n            attributes[name] = attr;\n        }\n        this.attribList = [];\n    }\n    processAttribsPlain() {\n        const { attribList } = this;\n        // eslint-disable-next-line prefer-destructuring\n        const attributes = this.tag.attributes;\n        for (const { name, value } of attribList) {\n            if (attributes[name] !== undefined) {\n                this.fail(`duplicate attribute: ${name}.`);\n            }\n            attributes[name] = value;\n        }\n        this.attribList = [];\n    }\n    /**\n     * Handle a complete open tag. This parser code calls this once it has seen\n     * the whole tag. This method checks for well-formeness and then emits\n     * ``onopentag``.\n     */\n    openTag() {\n        var _a;\n        this.processAttribs();\n        const { tags } = this;\n        const tag = this.tag;\n        tag.isSelfClosing = false;\n        // There cannot be any pending text here due to the onopentagstart that was\n        // necessarily emitted before we get here. So we do not check text.\n        // eslint-disable-next-line no-unused-expressions\n        (_a = this.openTagHandler) === null || _a === void 0 ? void 0 : _a.call(this, tag);\n        tags.push(tag);\n        this.state = S_TEXT;\n        this.name = \"\";\n    }\n    /**\n     * Handle a complete self-closing tag. This parser code calls this once it has\n     * seen the whole tag. This method checks for well-formeness and then emits\n     * ``onopentag`` and ``onclosetag``.\n     */\n    openSelfClosingTag() {\n        var _a, _b, _c;\n        this.processAttribs();\n        const { tags } = this;\n        const tag = this.tag;\n        tag.isSelfClosing = true;\n        // There cannot be any pending text here due to the onopentagstart that was\n        // necessarily emitted before we get here. So we do not check text.\n        // eslint-disable-next-line no-unused-expressions\n        (_a = this.openTagHandler) === null || _a === void 0 ? void 0 : _a.call(this, tag);\n        // eslint-disable-next-line no-unused-expressions\n        (_b = this.closeTagHandler) === null || _b === void 0 ? void 0 : _b.call(this, tag);\n        const top = this.tag = (_c = tags[tags.length - 1]) !== null && _c !== void 0 ? _c : null;\n        if (top === null) {\n            this.closedRoot = true;\n        }\n        this.state = S_TEXT;\n        this.name = \"\";\n    }\n    /**\n     * Handle a complete close tag. This parser code calls this once it has seen\n     * the whole tag. This method checks for well-formeness and then emits\n     * ``onclosetag``.\n     */\n    closeTag() {\n        const { tags, name } = this;\n        // Our state after this will be S_TEXT, no matter what, and we can clear\n        // tagName now.\n        this.state = S_TEXT;\n        this.name = \"\";\n        if (name === \"\") {\n            this.fail(\"weird empty close tag.\");\n            this.text += \"</>\";\n            return;\n        }\n        const handler = this.closeTagHandler;\n        let l = tags.length;\n        while (l-- > 0) {\n            const tag = this.tag = tags.pop();\n            this.topNS = tag.ns;\n            // eslint-disable-next-line no-unused-expressions\n            handler === null || handler === void 0 ? void 0 : handler(tag);\n            if (tag.name === name) {\n                break;\n            }\n            this.fail(\"unexpected close tag.\");\n        }\n        if (l === 0) {\n            this.closedRoot = true;\n        }\n        else if (l < 0) {\n            this.fail(`unmatched closing tag: ${name}.`);\n            this.text += `</${name}>`;\n        }\n    }\n    /**\n     * Resolves an entity. Makes any necessary well-formedness checks.\n     *\n     * @param entity The entity to resolve.\n     *\n     * @returns The parsed entity.\n     */\n    parseEntity(entity) {\n        // startsWith would be significantly slower for this test.\n        // eslint-disable-next-line @typescript-eslint/prefer-string-starts-ends-with\n        if (entity[0] !== \"#\") {\n            const defined = this.ENTITIES[entity];\n            if (defined !== undefined) {\n                return defined;\n            }\n            this.fail(this.isName(entity) ? \"undefined entity.\" :\n                \"disallowed character in entity name.\");\n            return `&${entity};`;\n        }\n        let num = NaN;\n        if (entity[1] === \"x\" && /^#x[0-9a-f]+$/i.test(entity)) {\n            num = parseInt(entity.slice(2), 16);\n        }\n        else if (/^#[0-9]+$/.test(entity)) {\n            num = parseInt(entity.slice(1), 10);\n        }\n        // The character reference is required to match the CHAR production.\n        if (!this.isChar(num)) {\n            this.fail(\"malformed character entity.\");\n            return `&${entity};`;\n        }\n        return String.fromCodePoint(num);\n    }\n}\nexports.SaxesParser = SaxesParser;\n//# sourceMappingURL=saxes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc2F4ZXMvc2F4ZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsWUFBWSxtQkFBTyxDQUFDLDBFQUFzQjtBQUMxQyxZQUFZLG1CQUFPLENBQUMsMEVBQXNCO0FBQzFDLGNBQWMsbUJBQU8sQ0FBQyw4RUFBd0I7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CLDhCQUE4QjtBQUM5QixxQkFBcUI7QUFDckIsMkJBQTJCO0FBQzNCLGlCQUFpQjtBQUNqQix3QkFBd0I7QUFDeEI7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QixnQ0FBZ0M7QUFDaEMsZ0NBQWdDO0FBQ2hDLHFCQUFxQjtBQUNyQiw0QkFBNEI7QUFDNUIsbUJBQW1CO0FBQ25CLHFCQUFxQjtBQUNyQix3QkFBd0I7QUFDeEIsNkJBQTZCO0FBQzdCLHNCQUFzQjtBQUN0Qiw2QkFBNkI7QUFDN0IsNEJBQTRCO0FBQzVCLG9CQUFvQjtBQUNwQiwyQkFBMkI7QUFDM0IsNkJBQTZCO0FBQzdCLDRCQUE0QjtBQUM1QixzQkFBc0I7QUFDdEIsc0JBQXNCO0FBQ3RCLHdCQUF3QjtBQUN4QixrQ0FBa0M7QUFDbEMsNEJBQTRCO0FBQzVCLDBCQUEwQjtBQUMxQixtQ0FBbUM7QUFDbkMsNkJBQTZCO0FBQzdCLGlDQUFpQztBQUNqQyw4QkFBOEI7QUFDOUIsdUJBQXVCO0FBQ3ZCLDZCQUE2QjtBQUM3QixxQkFBcUI7QUFDckIsMEJBQTBCO0FBQzFCLG9DQUFvQztBQUNwQywyQkFBMkI7QUFDM0Isa0NBQWtDO0FBQ2xDLGtDQUFrQztBQUNsQyxvQ0FBb0M7QUFDcEMsd0JBQXdCO0FBQ3hCLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsY0FBYztBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RCxnQkFBZ0I7QUFDN0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsSUFBSTtBQUMvRDtBQUNBLEVBQUUsZ0JBQWdCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkVBQTJFLElBQUk7QUFDL0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLGlCQUFpQjtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixjQUFjO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsb0JBQW9CO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLFVBQVUsR0FBRyxZQUFZO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3REFBd0Q7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHlCQUF5QixFQUFFLE1BQU07QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGFBQWE7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFdBQVc7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsUUFBUTtBQUN6QjtBQUNBO0FBQ0EsZ0JBQWdCLFdBQVc7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsV0FBVztBQUN6QixnQkFBZ0IsUUFBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQywrQkFBK0I7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsV0FBVztBQUN6QixnQkFBZ0IsUUFBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLCtCQUErQjtBQUNuRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGtCQUFrQjtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsV0FBVztBQUN6QixnQkFBZ0IsUUFBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsK0JBQStCO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixvQkFBb0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLHdCQUF3QjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsd0JBQXdCO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsd0JBQXdCO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyx3QkFBd0I7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixrQkFBa0I7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFdBQVc7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2Qix3QkFBd0I7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQsdUJBQXVCO0FBQzFFO0FBQ0E7QUFDQSxpREFBaUQsK0JBQStCO0FBQ2hGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFdBQVc7QUFDM0IsY0FBYyxXQUFXO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsZ0NBQWdDO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYywyQkFBMkI7QUFDekMsZ0JBQWdCLDhCQUE4QjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLE9BQU87QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QywrQkFBK0I7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxXQUFXO0FBQ3pCLGdCQUFnQiw4QkFBOEI7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsT0FBTztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLCtCQUErQjtBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGdCQUFnQjtBQUNoQyx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLE9BQU87QUFDdkI7QUFDQTtBQUNBLHVDQUF1QyxTQUFTO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLE9BQU87QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLE9BQU87QUFDdkIsMENBQTBDLFlBQVk7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUMsS0FBSztBQUM5QztBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsYUFBYTtBQUM3QjtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsZ0JBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsdUJBQXVCO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGFBQWE7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isc0JBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsdUJBQXVCO0FBQ2xGO0FBQ0E7QUFDQSwyQkFBMkIsRUFBRSxLQUFLLEVBQUUsTUFBTTtBQUMxQztBQUNBO0FBQ0Esa0RBQWtELE9BQU87QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixhQUFhO0FBQzdCO0FBQ0E7QUFDQSxxQkFBcUIsY0FBYztBQUNuQztBQUNBLGtEQUFrRCxLQUFLO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLE9BQU87QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLE9BQU87QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGFBQWE7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsS0FBSztBQUNyRCw4QkFBOEIsS0FBSztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixRQUFRO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsUUFBUTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3NheGVzL3NheGVzLmpzPzI1NGYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5jb25zdCBlZDUgPSByZXF1aXJlKFwieG1sY2hhcnMveG1sLzEuMC9lZDVcIik7XG5jb25zdCBlZDIgPSByZXF1aXJlKFwieG1sY2hhcnMveG1sLzEuMS9lZDJcIik7XG5jb25zdCBOU2VkMyA9IHJlcXVpcmUoXCJ4bWxjaGFycy94bWxucy8xLjAvZWQzXCIpO1xudmFyIGlzUyA9IGVkNS5pc1M7XG52YXIgaXNDaGFyMTAgPSBlZDUuaXNDaGFyO1xudmFyIGlzTmFtZVN0YXJ0Q2hhciA9IGVkNS5pc05hbWVTdGFydENoYXI7XG52YXIgaXNOYW1lQ2hhciA9IGVkNS5pc05hbWVDaGFyO1xudmFyIFNfTElTVCA9IGVkNS5TX0xJU1Q7XG52YXIgTkFNRV9SRSA9IGVkNS5OQU1FX1JFO1xudmFyIGlzQ2hhcjExID0gZWQyLmlzQ2hhcjtcbnZhciBpc05DTmFtZVN0YXJ0Q2hhciA9IE5TZWQzLmlzTkNOYW1lU3RhcnRDaGFyO1xudmFyIGlzTkNOYW1lQ2hhciA9IE5TZWQzLmlzTkNOYW1lQ2hhcjtcbnZhciBOQ19OQU1FX1JFID0gTlNlZDMuTkNfTkFNRV9SRTtcbmNvbnN0IFhNTF9OQU1FU1BBQ0UgPSBcImh0dHA6Ly93d3cudzMub3JnL1hNTC8xOTk4L25hbWVzcGFjZVwiO1xuY29uc3QgWE1MTlNfTkFNRVNQQUNFID0gXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3htbG5zL1wiO1xuY29uc3Qgcm9vdE5TID0ge1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZXhwbGljaXQtYW55XG4gICAgX19wcm90b19fOiBudWxsLFxuICAgIHhtbDogWE1MX05BTUVTUEFDRSxcbiAgICB4bWxuczogWE1MTlNfTkFNRVNQQUNFLFxufTtcbmNvbnN0IFhNTF9FTlRJVElFUyA9IHtcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLWV4cGxpY2l0LWFueVxuICAgIF9fcHJvdG9fXzogbnVsbCxcbiAgICBhbXA6IFwiJlwiLFxuICAgIGd0OiBcIj5cIixcbiAgICBsdDogXCI8XCIsXG4gICAgcXVvdDogXCJcXFwiXCIsXG4gICAgYXBvczogXCInXCIsXG59O1xuLy8gRU9DOiBlbmQtb2YtY2h1bmtcbmNvbnN0IEVPQyA9IC0xO1xuY29uc3QgTkxfTElLRSA9IC0yO1xuY29uc3QgU19CRUdJTiA9IDA7IC8vIEluaXRpYWwgc3RhdGUuXG5jb25zdCBTX0JFR0lOX1dISVRFU1BBQ0UgPSAxOyAvLyBsZWFkaW5nIHdoaXRlc3BhY2VcbmNvbnN0IFNfRE9DVFlQRSA9IDI7IC8vIDwhRE9DVFlQRVxuY29uc3QgU19ET0NUWVBFX1FVT1RFID0gMzsgLy8gPCFET0NUWVBFIFwiLy9ibGFoXG5jb25zdCBTX0RURCA9IDQ7IC8vIDwhRE9DVFlQRSBcIi8vYmxhaFwiIFsgLi4uXG5jb25zdCBTX0RURF9RVU9URUQgPSA1OyAvLyA8IURPQ1RZUEUgXCIvL2JsYWhcIiBbIFwiZm9vXG5jb25zdCBTX0RURF9PUEVOX1dBS0EgPSA2O1xuY29uc3QgU19EVERfT1BFTl9XQUtBX0JBTkcgPSA3O1xuY29uc3QgU19EVERfQ09NTUVOVCA9IDg7IC8vIDwhLS1cbmNvbnN0IFNfRFREX0NPTU1FTlRfRU5ESU5HID0gOTsgLy8gPCEtLSBibGFoIC1cbmNvbnN0IFNfRFREX0NPTU1FTlRfRU5ERUQgPSAxMDsgLy8gPCEtLSBibGFoIC0tXG5jb25zdCBTX0RURF9QSSA9IDExOyAvLyA8P1xuY29uc3QgU19EVERfUElfRU5ESU5HID0gMTI7IC8vIDw/aGkgXCJ0aGVyZVwiID9cbmNvbnN0IFNfVEVYVCA9IDEzOyAvLyBnZW5lcmFsIHN0dWZmXG5jb25zdCBTX0VOVElUWSA9IDE0OyAvLyAmYW1wIGFuZCBzdWNoXG5jb25zdCBTX09QRU5fV0FLQSA9IDE1OyAvLyA8XG5jb25zdCBTX09QRU5fV0FLQV9CQU5HID0gMTY7IC8vIDwhLi4uXG5jb25zdCBTX0NPTU1FTlQgPSAxNzsgLy8gPCEtLVxuY29uc3QgU19DT01NRU5UX0VORElORyA9IDE4OyAvLyA8IS0tIGJsYWggLVxuY29uc3QgU19DT01NRU5UX0VOREVEID0gMTk7IC8vIDwhLS0gYmxhaCAtLVxuY29uc3QgU19DREFUQSA9IDIwOyAvLyA8IVtDREFUQVsgc29tZXRoaW5nXG5jb25zdCBTX0NEQVRBX0VORElORyA9IDIxOyAvLyBdXG5jb25zdCBTX0NEQVRBX0VORElOR18yID0gMjI7IC8vIF1dXG5jb25zdCBTX1BJX0ZJUlNUX0NIQVIgPSAyMzsgLy8gPD9oaSwgZmlyc3QgY2hhclxuY29uc3QgU19QSV9SRVNUID0gMjQ7IC8vIDw/aGksIHJlc3Qgb2YgdGhlIG5hbWVcbmNvbnN0IFNfUElfQk9EWSA9IDI1OyAvLyA8P2hpIHRoZXJlXG5jb25zdCBTX1BJX0VORElORyA9IDI2OyAvLyA8P2hpIFwidGhlcmVcIiA/XG5jb25zdCBTX1hNTF9ERUNMX05BTUVfU1RBUlQgPSAyNzsgLy8gPD94bWxcbmNvbnN0IFNfWE1MX0RFQ0xfTkFNRSA9IDI4OyAvLyA8P3htbCBmb29cbmNvbnN0IFNfWE1MX0RFQ0xfRVEgPSAyOTsgLy8gPD94bWwgZm9vPVxuY29uc3QgU19YTUxfREVDTF9WQUxVRV9TVEFSVCA9IDMwOyAvLyA8P3htbCBmb289XG5jb25zdCBTX1hNTF9ERUNMX1ZBTFVFID0gMzE7IC8vIDw/eG1sIGZvbz1cImJhclwiXG5jb25zdCBTX1hNTF9ERUNMX1NFUEFSQVRPUiA9IDMyOyAvLyA8P3htbCBmb289XCJiYXJcIlxuY29uc3QgU19YTUxfREVDTF9FTkRJTkcgPSAzMzsgLy8gPD94bWwgLi4uID9cbmNvbnN0IFNfT1BFTl9UQUcgPSAzNDsgLy8gPHN0cm9uZ1xuY29uc3QgU19PUEVOX1RBR19TTEFTSCA9IDM1OyAvLyA8c3Ryb25nIC9cbmNvbnN0IFNfQVRUUklCID0gMzY7IC8vIDxhXG5jb25zdCBTX0FUVFJJQl9OQU1FID0gMzc7IC8vIDxhIGZvb1xuY29uc3QgU19BVFRSSUJfTkFNRV9TQVdfV0hJVEUgPSAzODsgLy8gPGEgZm9vIF9cbmNvbnN0IFNfQVRUUklCX1ZBTFVFID0gMzk7IC8vIDxhIGZvbz1cbmNvbnN0IFNfQVRUUklCX1ZBTFVFX1FVT1RFRCA9IDQwOyAvLyA8YSBmb289XCJiYXJcbmNvbnN0IFNfQVRUUklCX1ZBTFVFX0NMT1NFRCA9IDQxOyAvLyA8YSBmb289XCJiYXJcIlxuY29uc3QgU19BVFRSSUJfVkFMVUVfVU5RVU9URUQgPSA0MjsgLy8gPGEgZm9vPWJhclxuY29uc3QgU19DTE9TRV9UQUcgPSA0MzsgLy8gPC9hXG5jb25zdCBTX0NMT1NFX1RBR19TQVdfV0hJVEUgPSA0NDsgLy8gPC9hICAgPlxuY29uc3QgVEFCID0gOTtcbmNvbnN0IE5MID0gMHhBO1xuY29uc3QgQ1IgPSAweEQ7XG5jb25zdCBTUEFDRSA9IDB4MjA7XG5jb25zdCBCQU5HID0gMHgyMTtcbmNvbnN0IERRVU9URSA9IDB4MjI7XG5jb25zdCBBTVAgPSAweDI2O1xuY29uc3QgU1FVT1RFID0gMHgyNztcbmNvbnN0IE1JTlVTID0gMHgyRDtcbmNvbnN0IEZPUldBUkRfU0xBU0ggPSAweDJGO1xuY29uc3QgU0VNSUNPTE9OID0gMHgzQjtcbmNvbnN0IExFU1MgPSAweDNDO1xuY29uc3QgRVFVQUwgPSAweDNEO1xuY29uc3QgR1JFQVRFUiA9IDB4M0U7XG5jb25zdCBRVUVTVElPTiA9IDB4M0Y7XG5jb25zdCBPUEVOX0JSQUNLRVQgPSAweDVCO1xuY29uc3QgQ0xPU0VfQlJBQ0tFVCA9IDB4NUQ7XG5jb25zdCBORUwgPSAweDg1O1xuY29uc3QgTFMgPSAweDIwMjg7IC8vIExpbmUgU2VwYXJhdG9yXG5jb25zdCBpc1F1b3RlID0gKGMpID0+IGMgPT09IERRVU9URSB8fCBjID09PSBTUVVPVEU7XG5jb25zdCBRVU9URVMgPSBbRFFVT1RFLCBTUVVPVEVdO1xuY29uc3QgRE9DVFlQRV9URVJNSU5BVE9SID0gWy4uLlFVT1RFUywgT1BFTl9CUkFDS0VULCBHUkVBVEVSXTtcbmNvbnN0IERURF9URVJNSU5BVE9SID0gWy4uLlFVT1RFUywgTEVTUywgQ0xPU0VfQlJBQ0tFVF07XG5jb25zdCBYTUxfREVDTF9OQU1FX1RFUk1JTkFUT1IgPSBbRVFVQUwsIFFVRVNUSU9OLCAuLi5TX0xJU1RdO1xuY29uc3QgQVRUUklCX1ZBTFVFX1VOUVVPVEVEX1RFUk1JTkFUT1IgPSBbLi4uU19MSVNULCBHUkVBVEVSLCBBTVAsIExFU1NdO1xuZnVuY3Rpb24gbnNQYWlyQ2hlY2socGFyc2VyLCBwcmVmaXgsIHVyaSkge1xuICAgIHN3aXRjaCAocHJlZml4KSB7XG4gICAgICAgIGNhc2UgXCJ4bWxcIjpcbiAgICAgICAgICAgIGlmICh1cmkgIT09IFhNTF9OQU1FU1BBQ0UpIHtcbiAgICAgICAgICAgICAgICBwYXJzZXIuZmFpbChgeG1sIHByZWZpeCBtdXN0IGJlIGJvdW5kIHRvICR7WE1MX05BTUVTUEFDRX0uYCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSBcInhtbG5zXCI6XG4gICAgICAgICAgICBpZiAodXJpICE9PSBYTUxOU19OQU1FU1BBQ0UpIHtcbiAgICAgICAgICAgICAgICBwYXJzZXIuZmFpbChgeG1sbnMgcHJlZml4IG11c3QgYmUgYm91bmQgdG8gJHtYTUxOU19OQU1FU1BBQ0V9LmApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgfVxuICAgIHN3aXRjaCAodXJpKSB7XG4gICAgICAgIGNhc2UgWE1MTlNfTkFNRVNQQUNFOlxuICAgICAgICAgICAgcGFyc2VyLmZhaWwocHJlZml4ID09PSBcIlwiID9cbiAgICAgICAgICAgICAgICBgdGhlIGRlZmF1bHQgbmFtZXNwYWNlIG1heSBub3QgYmUgc2V0IHRvICR7dXJpfS5gIDpcbiAgICAgICAgICAgICAgICBgbWF5IG5vdCBhc3NpZ24gYSBwcmVmaXggKGV2ZW4gXCJ4bWxuc1wiKSB0byB0aGUgVVJJIFxcXG4ke1hNTE5TX05BTUVTUEFDRX0uYCk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSBYTUxfTkFNRVNQQUNFOlxuICAgICAgICAgICAgc3dpdGNoIChwcmVmaXgpIHtcbiAgICAgICAgICAgICAgICBjYXNlIFwieG1sXCI6XG4gICAgICAgICAgICAgICAgICAgIC8vIEFzc2luZ2luZyB0aGUgWE1MIG5hbWVzcGFjZSB0byBcInhtbFwiIGlzIGZpbmUuXG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgXCJcIjpcbiAgICAgICAgICAgICAgICAgICAgcGFyc2VyLmZhaWwoYHRoZSBkZWZhdWx0IG5hbWVzcGFjZSBtYXkgbm90IGJlIHNldCB0byAke3VyaX0uYCk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgICAgIHBhcnNlci5mYWlsKFwibWF5IG5vdCBhc3NpZ24gdGhlIHhtbCBuYW1lc3BhY2UgdG8gYW5vdGhlciBwcmVmaXguXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgfVxufVxuZnVuY3Rpb24gbnNNYXBwaW5nQ2hlY2socGFyc2VyLCBtYXBwaW5nKSB7XG4gICAgZm9yIChjb25zdCBsb2NhbCBvZiBPYmplY3Qua2V5cyhtYXBwaW5nKSkge1xuICAgICAgICBuc1BhaXJDaGVjayhwYXJzZXIsIGxvY2FsLCBtYXBwaW5nW2xvY2FsXSk7XG4gICAgfVxufVxuY29uc3QgaXNOQ05hbWUgPSAobmFtZSkgPT4gTkNfTkFNRV9SRS50ZXN0KG5hbWUpO1xuY29uc3QgaXNOYW1lID0gKG5hbWUpID0+IE5BTUVfUkUudGVzdChuYW1lKTtcbmNvbnN0IEZPUkJJRERFTl9TVEFSVCA9IDA7XG5jb25zdCBGT1JCSURERU5fQlJBQ0tFVCA9IDE7XG5jb25zdCBGT1JCSURERU5fQlJBQ0tFVF9CUkFDS0VUID0gMjtcbi8qKlxuICogVGhlIGxpc3Qgb2Ygc3VwcG9ydGVkIGV2ZW50cy5cbiAqL1xuZXhwb3J0cy5FVkVOVFMgPSBbXG4gICAgXCJ4bWxkZWNsXCIsXG4gICAgXCJ0ZXh0XCIsXG4gICAgXCJwcm9jZXNzaW5naW5zdHJ1Y3Rpb25cIixcbiAgICBcImRvY3R5cGVcIixcbiAgICBcImNvbW1lbnRcIixcbiAgICBcIm9wZW50YWdzdGFydFwiLFxuICAgIFwiYXR0cmlidXRlXCIsXG4gICAgXCJvcGVudGFnXCIsXG4gICAgXCJjbG9zZXRhZ1wiLFxuICAgIFwiY2RhdGFcIixcbiAgICBcImVycm9yXCIsXG4gICAgXCJlbmRcIixcbiAgICBcInJlYWR5XCIsXG5dO1xuY29uc3QgRVZFTlRfTkFNRV9UT19IQU5ETEVSX05BTUUgPSB7XG4gICAgeG1sZGVjbDogXCJ4bWxkZWNsSGFuZGxlclwiLFxuICAgIHRleHQ6IFwidGV4dEhhbmRsZXJcIixcbiAgICBwcm9jZXNzaW5naW5zdHJ1Y3Rpb246IFwicGlIYW5kbGVyXCIsXG4gICAgZG9jdHlwZTogXCJkb2N0eXBlSGFuZGxlclwiLFxuICAgIGNvbW1lbnQ6IFwiY29tbWVudEhhbmRsZXJcIixcbiAgICBvcGVudGFnc3RhcnQ6IFwib3BlblRhZ1N0YXJ0SGFuZGxlclwiLFxuICAgIGF0dHJpYnV0ZTogXCJhdHRyaWJ1dGVIYW5kbGVyXCIsXG4gICAgb3BlbnRhZzogXCJvcGVuVGFnSGFuZGxlclwiLFxuICAgIGNsb3NldGFnOiBcImNsb3NlVGFnSGFuZGxlclwiLFxuICAgIGNkYXRhOiBcImNkYXRhSGFuZGxlclwiLFxuICAgIGVycm9yOiBcImVycm9ySGFuZGxlclwiLFxuICAgIGVuZDogXCJlbmRIYW5kbGVyXCIsXG4gICAgcmVhZHk6IFwicmVhZHlIYW5kbGVyXCIsXG59O1xuY2xhc3MgU2F4ZXNQYXJzZXIge1xuICAgIC8qKlxuICAgICAqIEBwYXJhbSBvcHQgVGhlIHBhcnNlciBvcHRpb25zLlxuICAgICAqL1xuICAgIGNvbnN0cnVjdG9yKG9wdCkge1xuICAgICAgICB0aGlzLm9wdCA9IG9wdCAhPT0gbnVsbCAmJiBvcHQgIT09IHZvaWQgMCA/IG9wdCA6IHt9O1xuICAgICAgICB0aGlzLmZyYWdtZW50T3B0ID0gISF0aGlzLm9wdC5mcmFnbWVudDtcbiAgICAgICAgY29uc3QgeG1sbnNPcHQgPSB0aGlzLnhtbG5zT3B0ID0gISF0aGlzLm9wdC54bWxucztcbiAgICAgICAgdGhpcy50cmFja1Bvc2l0aW9uID0gdGhpcy5vcHQucG9zaXRpb24gIT09IGZhbHNlO1xuICAgICAgICB0aGlzLmZpbGVOYW1lID0gdGhpcy5vcHQuZmlsZU5hbWU7XG4gICAgICAgIGlmICh4bWxuc09wdCkge1xuICAgICAgICAgICAgLy8gVGhpcyBpcyB0aGUgZnVuY3Rpb24gd2UgdXNlIHRvIHBlcmZvcm0gbmFtZSBjaGVja3Mgb24gUElzIGFuZCBlbnRpdGllcy5cbiAgICAgICAgICAgIC8vIFdoZW4gbmFtZXNwYWNlcyBhcmUgdXNlZCwgY29sb25zIGFyZSBub3QgYWxsb3dlZCBpbiBQSSB0YXJnZXQgbmFtZXMgb3JcbiAgICAgICAgICAgIC8vIGVudGl0eSBuYW1lcy4gU28gdGhlIGNoZWNrIGRlcGVuZHMgb24gd2hldGhlciBuYW1lc3BhY2VzIGFyZSB1c2VkLiBTZWU6XG4gICAgICAgICAgICAvL1xuICAgICAgICAgICAgLy8gaHR0cHM6Ly93d3cudzMub3JnL1hNTC94bWwtbmFtZXMtMTk5OTAxMTQtZXJyYXRhLmh0bWxcbiAgICAgICAgICAgIC8vIE5FMDhcbiAgICAgICAgICAgIC8vXG4gICAgICAgICAgICB0aGlzLm5hbWVTdGFydENoZWNrID0gaXNOQ05hbWVTdGFydENoYXI7XG4gICAgICAgICAgICB0aGlzLm5hbWVDaGVjayA9IGlzTkNOYW1lQ2hhcjtcbiAgICAgICAgICAgIHRoaXMuaXNOYW1lID0gaXNOQ05hbWU7XG4gICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L3VuYm91bmQtbWV0aG9kXG4gICAgICAgICAgICB0aGlzLnByb2Nlc3NBdHRyaWJzID0gdGhpcy5wcm9jZXNzQXR0cmlic05TO1xuICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC91bmJvdW5kLW1ldGhvZFxuICAgICAgICAgICAgdGhpcy5wdXNoQXR0cmliID0gdGhpcy5wdXNoQXR0cmliTlM7XG4gICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLWV4cGxpY2l0LWFueVxuICAgICAgICAgICAgdGhpcy5ucyA9IE9iamVjdC5hc3NpZ24oeyBfX3Byb3RvX186IG51bGwgfSwgcm9vdE5TKTtcbiAgICAgICAgICAgIGNvbnN0IGFkZGl0aW9uYWwgPSB0aGlzLm9wdC5hZGRpdGlvbmFsTmFtZXNwYWNlcztcbiAgICAgICAgICAgIGlmIChhZGRpdGlvbmFsICE9IG51bGwpIHtcbiAgICAgICAgICAgICAgICBuc01hcHBpbmdDaGVjayh0aGlzLCBhZGRpdGlvbmFsKTtcbiAgICAgICAgICAgICAgICBPYmplY3QuYXNzaWduKHRoaXMubnMsIGFkZGl0aW9uYWwpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5uYW1lU3RhcnRDaGVjayA9IGlzTmFtZVN0YXJ0Q2hhcjtcbiAgICAgICAgICAgIHRoaXMubmFtZUNoZWNrID0gaXNOYW1lQ2hhcjtcbiAgICAgICAgICAgIHRoaXMuaXNOYW1lID0gaXNOYW1lO1xuICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC91bmJvdW5kLW1ldGhvZFxuICAgICAgICAgICAgdGhpcy5wcm9jZXNzQXR0cmlicyA9IHRoaXMucHJvY2Vzc0F0dHJpYnNQbGFpbjtcbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvdW5ib3VuZC1tZXRob2RcbiAgICAgICAgICAgIHRoaXMucHVzaEF0dHJpYiA9IHRoaXMucHVzaEF0dHJpYlBsYWluO1xuICAgICAgICB9XG4gICAgICAgIC8vXG4gICAgICAgIC8vIFRoZSBvcmRlciBvZiB0aGUgbWVtYmVycyBpbiB0aGlzIHRhYmxlIG5lZWRzIHRvIGNvcnJlc3BvbmQgdG8gdGhlIHN0YXRlXG4gICAgICAgIC8vIG51bWJlcnMgZ2l2ZW4gdG8gdGhlIHN0YXRlcyB0aGF0IGNvcnJlc3BvbmQgdG8gdGhlIG1ldGhvZHMgYmVpbmcgcmVjb3JkZWRcbiAgICAgICAgLy8gaGVyZS5cbiAgICAgICAgLy9cbiAgICAgICAgdGhpcy5zdGF0ZVRhYmxlID0gW1xuICAgICAgICAgICAgLyogZXNsaW50LWRpc2FibGUgQHR5cGVzY3JpcHQtZXNsaW50L3VuYm91bmQtbWV0aG9kICovXG4gICAgICAgICAgICB0aGlzLnNCZWdpbixcbiAgICAgICAgICAgIHRoaXMuc0JlZ2luV2hpdGVzcGFjZSxcbiAgICAgICAgICAgIHRoaXMuc0RvY3R5cGUsXG4gICAgICAgICAgICB0aGlzLnNEb2N0eXBlUXVvdGUsXG4gICAgICAgICAgICB0aGlzLnNEVEQsXG4gICAgICAgICAgICB0aGlzLnNEVERRdW90ZWQsXG4gICAgICAgICAgICB0aGlzLnNEVERPcGVuV2FrYSxcbiAgICAgICAgICAgIHRoaXMuc0RURE9wZW5XYWthQmFuZyxcbiAgICAgICAgICAgIHRoaXMuc0RURENvbW1lbnQsXG4gICAgICAgICAgICB0aGlzLnNEVERDb21tZW50RW5kaW5nLFxuICAgICAgICAgICAgdGhpcy5zRFREQ29tbWVudEVuZGVkLFxuICAgICAgICAgICAgdGhpcy5zRFREUEksXG4gICAgICAgICAgICB0aGlzLnNEVERQSUVuZGluZyxcbiAgICAgICAgICAgIHRoaXMuc1RleHQsXG4gICAgICAgICAgICB0aGlzLnNFbnRpdHksXG4gICAgICAgICAgICB0aGlzLnNPcGVuV2FrYSxcbiAgICAgICAgICAgIHRoaXMuc09wZW5XYWthQmFuZyxcbiAgICAgICAgICAgIHRoaXMuc0NvbW1lbnQsXG4gICAgICAgICAgICB0aGlzLnNDb21tZW50RW5kaW5nLFxuICAgICAgICAgICAgdGhpcy5zQ29tbWVudEVuZGVkLFxuICAgICAgICAgICAgdGhpcy5zQ0RhdGEsXG4gICAgICAgICAgICB0aGlzLnNDRGF0YUVuZGluZyxcbiAgICAgICAgICAgIHRoaXMuc0NEYXRhRW5kaW5nMixcbiAgICAgICAgICAgIHRoaXMuc1BJRmlyc3RDaGFyLFxuICAgICAgICAgICAgdGhpcy5zUElSZXN0LFxuICAgICAgICAgICAgdGhpcy5zUElCb2R5LFxuICAgICAgICAgICAgdGhpcy5zUElFbmRpbmcsXG4gICAgICAgICAgICB0aGlzLnNYTUxEZWNsTmFtZVN0YXJ0LFxuICAgICAgICAgICAgdGhpcy5zWE1MRGVjbE5hbWUsXG4gICAgICAgICAgICB0aGlzLnNYTUxEZWNsRXEsXG4gICAgICAgICAgICB0aGlzLnNYTUxEZWNsVmFsdWVTdGFydCxcbiAgICAgICAgICAgIHRoaXMuc1hNTERlY2xWYWx1ZSxcbiAgICAgICAgICAgIHRoaXMuc1hNTERlY2xTZXBhcmF0b3IsXG4gICAgICAgICAgICB0aGlzLnNYTUxEZWNsRW5kaW5nLFxuICAgICAgICAgICAgdGhpcy5zT3BlblRhZyxcbiAgICAgICAgICAgIHRoaXMuc09wZW5UYWdTbGFzaCxcbiAgICAgICAgICAgIHRoaXMuc0F0dHJpYixcbiAgICAgICAgICAgIHRoaXMuc0F0dHJpYk5hbWUsXG4gICAgICAgICAgICB0aGlzLnNBdHRyaWJOYW1lU2F3V2hpdGUsXG4gICAgICAgICAgICB0aGlzLnNBdHRyaWJWYWx1ZSxcbiAgICAgICAgICAgIHRoaXMuc0F0dHJpYlZhbHVlUXVvdGVkLFxuICAgICAgICAgICAgdGhpcy5zQXR0cmliVmFsdWVDbG9zZWQsXG4gICAgICAgICAgICB0aGlzLnNBdHRyaWJWYWx1ZVVucXVvdGVkLFxuICAgICAgICAgICAgdGhpcy5zQ2xvc2VUYWcsXG4gICAgICAgICAgICB0aGlzLnNDbG9zZVRhZ1Nhd1doaXRlLFxuICAgICAgICBdO1xuICAgICAgICB0aGlzLl9pbml0KCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEluZGljYXRlcyB3aGV0aGVyIG9yIG5vdCB0aGUgcGFyc2VyIGlzIGNsb3NlZC4gSWYgYGB0cnVlYGAsIHdhaXQgZm9yXG4gICAgICogdGhlIGBgcmVhZHlgYCBldmVudCB0byB3cml0ZSBhZ2Fpbi5cbiAgICAgKi9cbiAgICBnZXQgY2xvc2VkKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xvc2VkO1xuICAgIH1cbiAgICBfaW5pdCgpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICB0aGlzLm9wZW5XYWthQmFuZyA9IFwiXCI7XG4gICAgICAgIHRoaXMudGV4dCA9IFwiXCI7XG4gICAgICAgIHRoaXMubmFtZSA9IFwiXCI7XG4gICAgICAgIHRoaXMucGlUYXJnZXQgPSBcIlwiO1xuICAgICAgICB0aGlzLmVudGl0eSA9IFwiXCI7XG4gICAgICAgIHRoaXMucSA9IG51bGw7XG4gICAgICAgIHRoaXMudGFncyA9IFtdO1xuICAgICAgICB0aGlzLnRhZyA9IG51bGw7XG4gICAgICAgIHRoaXMudG9wTlMgPSBudWxsO1xuICAgICAgICB0aGlzLmNodW5rID0gXCJcIjtcbiAgICAgICAgdGhpcy5jaHVua1Bvc2l0aW9uID0gMDtcbiAgICAgICAgdGhpcy5pID0gMDtcbiAgICAgICAgdGhpcy5wcmV2SSA9IDA7XG4gICAgICAgIHRoaXMuY2FycmllZEZyb21QcmV2aW91cyA9IHVuZGVmaW5lZDtcbiAgICAgICAgdGhpcy5mb3JiaWRkZW5TdGF0ZSA9IEZPUkJJRERFTl9TVEFSVDtcbiAgICAgICAgdGhpcy5hdHRyaWJMaXN0ID0gW107XG4gICAgICAgIC8vIFRoZSBsb2dpYyBpcyBvcmdhbml6ZWQgc28gYXMgdG8gbWluaW1pemUgdGhlIG5lZWQgdG8gY2hlY2tcbiAgICAgICAgLy8gdGhpcy5vcHQuZnJhZ21lbnQgd2hpbGUgcGFyc2luZy5cbiAgICAgICAgY29uc3QgeyBmcmFnbWVudE9wdCB9ID0gdGhpcztcbiAgICAgICAgdGhpcy5zdGF0ZSA9IGZyYWdtZW50T3B0ID8gU19URVhUIDogU19CRUdJTjtcbiAgICAgICAgLy8gV2Ugd2FudCB0aGVzZSB0byBiZSBhbGwgdHJ1ZSBpZiB3ZSBhcmUgZGVhbGluZyB3aXRoIGEgZnJhZ21lbnQuXG4gICAgICAgIHRoaXMucmVwb3J0ZWRUZXh0QmVmb3JlUm9vdCA9IHRoaXMucmVwb3J0ZWRUZXh0QWZ0ZXJSb290ID0gdGhpcy5jbG9zZWRSb290ID1cbiAgICAgICAgICAgIHRoaXMuc2F3Um9vdCA9IGZyYWdtZW50T3B0O1xuICAgICAgICAvLyBBbiBYTUwgZGVjbGFyYXRpb24gaXMgaW50aWFsbHkgcG9zc2libGUgb25seSB3aGVuIHBhcnNpbmcgd2hvbGVcbiAgICAgICAgLy8gZG9jdW1lbnRzLlxuICAgICAgICB0aGlzLnhtbERlY2xQb3NzaWJsZSA9ICFmcmFnbWVudE9wdDtcbiAgICAgICAgdGhpcy54bWxEZWNsRXhwZWN0cyA9IFtcInZlcnNpb25cIl07XG4gICAgICAgIHRoaXMuZW50aXR5UmV0dXJuU3RhdGUgPSB1bmRlZmluZWQ7XG4gICAgICAgIGxldCB7IGRlZmF1bHRYTUxWZXJzaW9uIH0gPSB0aGlzLm9wdDtcbiAgICAgICAgaWYgKGRlZmF1bHRYTUxWZXJzaW9uID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLm9wdC5mb3JjZVhNTFZlcnNpb24gPT09IHRydWUpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJmb3JjZVhNTFZlcnNpb24gc2V0IGJ1dCBkZWZhdWx0WE1MVmVyc2lvbiBpcyBub3Qgc2V0XCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZGVmYXVsdFhNTFZlcnNpb24gPSBcIjEuMFwiO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuc2V0WE1MVmVyc2lvbihkZWZhdWx0WE1MVmVyc2lvbik7XG4gICAgICAgIHRoaXMucG9zaXRpb25BdE5ld0xpbmUgPSAwO1xuICAgICAgICB0aGlzLmRvY3R5cGUgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5fY2xvc2VkID0gZmFsc2U7XG4gICAgICAgIHRoaXMueG1sRGVjbCA9IHtcbiAgICAgICAgICAgIHZlcnNpb246IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIGVuY29kaW5nOiB1bmRlZmluZWQsXG4gICAgICAgICAgICBzdGFuZGFsb25lOiB1bmRlZmluZWQsXG4gICAgICAgIH07XG4gICAgICAgIHRoaXMubGluZSA9IDE7XG4gICAgICAgIHRoaXMuY29sdW1uID0gMDtcbiAgICAgICAgdGhpcy5FTlRJVElFUyA9IE9iamVjdC5jcmVhdGUoWE1MX0VOVElUSUVTKTtcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVudXNlZC1leHByZXNzaW9uc1xuICAgICAgICAoX2EgPSB0aGlzLnJlYWR5SGFuZGxlcikgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNhbGwodGhpcyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFRoZSBzdHJlYW0gcG9zaXRpb24gdGhlIHBhcnNlciBpcyBjdXJyZW50bHkgbG9va2luZyBhdC4gVGhpcyBmaWVsZCBpc1xuICAgICAqIHplcm8tYmFzZWQuXG4gICAgICpcbiAgICAgKiBUaGlzIGZpZWxkIGlzIG5vdCBiYXNlZCBvbiBjb3VudGluZyBVbmljb2RlIGNoYXJhY3RlcnMgYnV0IGlzIHRvIGJlXG4gICAgICogaW50ZXJwcmV0ZWQgYXMgYSBwbGFpbiBpbmRleCBpbnRvIGEgSmF2YVNjcmlwdCBzdHJpbmcuXG4gICAgICovXG4gICAgZ2V0IHBvc2l0aW9uKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5jaHVua1Bvc2l0aW9uICsgdGhpcy5pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBUaGUgY29sdW1uIG51bWJlciBvZiB0aGUgbmV4dCBjaGFyYWN0ZXIgdG8gYmUgcmVhZCBieSB0aGUgcGFyc2VyLiAgKlxuICAgICAqIFRoaXMgZmllbGQgaXMgemVyby1iYXNlZC4gKFRoZSBmaXJzdCBjb2x1bW4gaW4gYSBsaW5lIGlzIDAuKVxuICAgICAqXG4gICAgICogVGhpcyBmaWVsZCByZXBvcnRzIHRoZSBpbmRleCBhdCB3aGljaCB0aGUgbmV4dCBjaGFyYWN0ZXIgd291bGQgYmUgaW4gdGhlXG4gICAgICogbGluZSBpZiB0aGUgbGluZSB3ZXJlIHJlcHJlc2VudGVkIGFzIGEgSmF2YVNjcmlwdCBzdHJpbmcuICBOb3RlIHRoYXQgdGhpc1xuICAgICAqICpjYW4qIGJlIGRpZmZlcmVudCB0byBhIGNvdW50IGJhc2VkIG9uIHRoZSBudW1iZXIgb2YgKlVuaWNvZGUgY2hhcmFjdGVycypcbiAgICAgKiBkdWUgdG8gaG93IEphdmFTY3JpcHQgaGFuZGxlcyBhc3RyYWwgcGxhbmUgY2hhcmFjdGVycy5cbiAgICAgKlxuICAgICAqIFNlZSBbW2NvbHVtbl1dIGZvciBhIG51bWJlciB0aGF0IGNvcnJlc3BvbmRzIHRvIGEgY291bnQgb2YgVW5pY29kZVxuICAgICAqIGNoYXJhY3RlcnMuXG4gICAgICovXG4gICAgZ2V0IGNvbHVtbkluZGV4KCkge1xuICAgICAgICByZXR1cm4gdGhpcy5wb3NpdGlvbiAtIHRoaXMucG9zaXRpb25BdE5ld0xpbmU7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNldCBhbiBldmVudCBsaXN0ZW5lciBvbiBhbiBldmVudC4gVGhlIHBhcnNlciBzdXBwb3J0cyBvbmUgaGFuZGxlciBwZXJcbiAgICAgKiBldmVudCB0eXBlLiBJZiB5b3UgdHJ5IHRvIHNldCBhbiBldmVudCBoYW5kbGVyIG92ZXIgYW4gZXhpc3RpbmcgaGFuZGxlcixcbiAgICAgKiB0aGUgb2xkIGhhbmRsZXIgaXMgc2lsZW50bHkgb3ZlcndyaXR0ZW4uXG4gICAgICpcbiAgICAgKiBAcGFyYW0gbmFtZSBUaGUgZXZlbnQgdG8gbGlzdGVuIHRvLlxuICAgICAqXG4gICAgICogQHBhcmFtIGhhbmRsZXIgVGhlIGhhbmRsZXIgdG8gc2V0LlxuICAgICAqL1xuICAgIG9uKG5hbWUsIGhhbmRsZXIpIHtcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnlcbiAgICAgICAgdGhpc1tFVkVOVF9OQU1FX1RPX0hBTkRMRVJfTkFNRVtuYW1lXV0gPSBoYW5kbGVyO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVbnNldCBhbiBldmVudCBoYW5kbGVyLlxuICAgICAqXG4gICAgICogQHBhcm1hIG5hbWUgVGhlIGV2ZW50IHRvIHN0b3AgbGlzdGVuaW5nIHRvLlxuICAgICAqL1xuICAgIG9mZihuYW1lKSB7XG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZXhwbGljaXQtYW55XG4gICAgICAgIHRoaXNbRVZFTlRfTkFNRV9UT19IQU5ETEVSX05BTUVbbmFtZV1dID0gdW5kZWZpbmVkO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBNYWtlIGFuIGVycm9yIG9iamVjdC4gVGhlIGVycm9yIG9iamVjdCB3aWxsIGhhdmUgYSBtZXNzYWdlIHRoYXQgY29udGFpbnNcbiAgICAgKiB0aGUgYGBmaWxlTmFtZWBgIG9wdGlvbiBwYXNzZWQgYXQgdGhlIGNyZWF0aW9uIG9mIHRoZSBwYXJzZXIuIElmIHBvc2l0aW9uXG4gICAgICogdHJhY2tpbmcgd2FzIHR1cm5lZCBvbiwgaXQgd2lsbCBhbHNvIGhhdmUgbGluZSBhbmQgY29sdW1uIG51bWJlclxuICAgICAqIGluZm9ybWF0aW9uLlxuICAgICAqXG4gICAgICogQHBhcmFtIG1lc3NhZ2UgVGhlIG1lc3NhZ2UgZGVzY3JpYmluZyB0aGUgZXJyb3IgdG8gcmVwb3J0LlxuICAgICAqXG4gICAgICogQHJldHVybnMgQW4gZXJyb3Igb2JqZWN0IHdpdGggYSBwcm9wZXJseSBmb3JtYXR0ZWQgbWVzc2FnZS5cbiAgICAgKi9cbiAgICBtYWtlRXJyb3IobWVzc2FnZSkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIGxldCBtc2cgPSAoX2EgPSB0aGlzLmZpbGVOYW1lKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBcIlwiO1xuICAgICAgICBpZiAodGhpcy50cmFja1Bvc2l0aW9uKSB7XG4gICAgICAgICAgICBpZiAobXNnLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgICBtc2cgKz0gXCI6XCI7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBtc2cgKz0gYCR7dGhpcy5saW5lfToke3RoaXMuY29sdW1ufWA7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG1zZy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBtc2cgKz0gXCI6IFwiO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBuZXcgRXJyb3IobXNnICsgbWVzc2FnZSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlcG9ydCBhIHBhcnNpbmcgZXJyb3IuIFRoaXMgbWV0aG9kIGlzIG1hZGUgcHVibGljIHNvIHRoYXQgY2xpZW50IGNvZGUgbWF5XG4gICAgICogY2hlY2sgZm9yIGlzc3VlcyB0aGF0IGFyZSBvdXRzaWRlIHRoZSBzY29wZSBvZiB0aGlzIHByb2plY3QgYW5kIGNhbiByZXBvcnRcbiAgICAgKiBlcnJvcnMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gbWVzc2FnZSBUaGUgZXJyb3IgdG8gcmVwb3J0LlxuICAgICAqXG4gICAgICogQHJldHVybnMgdGhpc1xuICAgICAqL1xuICAgIGZhaWwobWVzc2FnZSkge1xuICAgICAgICBjb25zdCBlcnIgPSB0aGlzLm1ha2VFcnJvcihtZXNzYWdlKTtcbiAgICAgICAgY29uc3QgaGFuZGxlciA9IHRoaXMuZXJyb3JIYW5kbGVyO1xuICAgICAgICBpZiAoaGFuZGxlciA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBoYW5kbGVyKGVycik7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFdyaXRlIGEgWE1MIGRhdGEgdG8gdGhlIHBhcnNlci5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBjaHVuayBUaGUgWE1MIGRhdGEgdG8gd3JpdGUuXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyB0aGlzXG4gICAgICovXG4gICAgd3JpdGUoY2h1bmspIHtcbiAgICAgICAgaWYgKHRoaXMuY2xvc2VkKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5mYWlsKFwiY2Fubm90IHdyaXRlIGFmdGVyIGNsb3NlOyBhc3NpZ24gYW4gb25yZWFkeSBoYW5kbGVyLlwiKTtcbiAgICAgICAgfVxuICAgICAgICBsZXQgZW5kID0gZmFsc2U7XG4gICAgICAgIGlmIChjaHVuayA9PT0gbnVsbCkge1xuICAgICAgICAgICAgLy8gV2UgY2Fubm90IHJldHVybiBpbW1lZGlhdGVseSBiZWNhdXNlIGNhcnJpZWRGcm9tUHJldmlvdXMgbWF5IG5lZWRcbiAgICAgICAgICAgIC8vIHByb2Nlc3NpbmcuXG4gICAgICAgICAgICBlbmQgPSB0cnVlO1xuICAgICAgICAgICAgY2h1bmsgPSBcIlwiO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHR5cGVvZiBjaHVuayA9PT0gXCJvYmplY3RcIikge1xuICAgICAgICAgICAgY2h1bmsgPSBjaHVuay50b1N0cmluZygpO1xuICAgICAgICB9XG4gICAgICAgIC8vIFdlIGNoZWNrZWQgaWYgcGVyZm9ybWluZyBhIHByZS1kZWNvbXBvc2l0aW9uIG9mIHRoZSBzdHJpbmcgaW50byBhbiBhcnJheVxuICAgICAgICAvLyBvZiBzaW5nbGUgY29tcGxldGUgY2hhcmFjdGVycyAoYGBBcnJheS5mcm9tKGNodW5rKWBgKSB3b3VsZCBiZSBmYXN0ZXJcbiAgICAgICAgLy8gdGhhbiB0aGUgY3VycmVudCByZXBlYXRlZCBjYWxscyB0byBgYGNoYXJDb2RlQXRgYC4gQXMgb2YgQXVndXN0IDIwMTgsIGl0XG4gICAgICAgIC8vIGlzbid0LiAoVGhlcmUgbWF5IGJlIE5vZGUtc3BlY2lmaWMgY29kZSB0aGF0IHdvdWxkIHBlcmZvcm0gZmFzdGVyIHRoYW5cbiAgICAgICAgLy8gYGBBcnJheS5mcm9tYGAgYnV0IGRvbid0IHdhbnQgdG8gYmUgZGVwZW5kZW50IG9uIE5vZGUuKVxuICAgICAgICBpZiAodGhpcy5jYXJyaWVkRnJvbVByZXZpb3VzICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIC8vIFRoZSBwcmV2aW91cyBjaHVuayBoYWQgY2hhciB3ZSBtdXN0IGNhcnJ5IG92ZXIuXG4gICAgICAgICAgICBjaHVuayA9IGAke3RoaXMuY2FycmllZEZyb21QcmV2aW91c30ke2NodW5rfWA7XG4gICAgICAgICAgICB0aGlzLmNhcnJpZWRGcm9tUHJldmlvdXMgPSB1bmRlZmluZWQ7XG4gICAgICAgIH1cbiAgICAgICAgbGV0IGxpbWl0ID0gY2h1bmsubGVuZ3RoO1xuICAgICAgICBjb25zdCBsYXN0Q29kZSA9IGNodW5rLmNoYXJDb2RlQXQobGltaXQgLSAxKTtcbiAgICAgICAgaWYgKCFlbmQgJiZcbiAgICAgICAgICAgIC8vIEEgdHJhaWxpbmcgQ1Igb3Igc3Vycm9nYXRlIG11c3QgYmUgY2FycmllZCBvdmVyIHRvIHRoZSBuZXh0XG4gICAgICAgICAgICAvLyBjaHVuay5cbiAgICAgICAgICAgIChsYXN0Q29kZSA9PT0gQ1IgfHwgKGxhc3RDb2RlID49IDB4RDgwMCAmJiBsYXN0Q29kZSA8PSAweERCRkYpKSkge1xuICAgICAgICAgICAgLy8gVGhlIGNodW5rIGVuZHMgd2l0aCBhIGNoYXJhY3RlciB0aGF0IG11c3QgYmUgY2FycmllZCBvdmVyLiBXZSBjYW5ub3RcbiAgICAgICAgICAgIC8vIGtub3cgaG93IHRvIGhhbmRsZSBpdCB1bnRpbCB3ZSBnZXQgdGhlIG5leHQgY2h1bmsgb3IgdGhlIGVuZCBvZiB0aGVcbiAgICAgICAgICAgIC8vIHN0cmVhbS4gU28gc2F2ZSBpdCBmb3IgbGF0ZXIuXG4gICAgICAgICAgICB0aGlzLmNhcnJpZWRGcm9tUHJldmlvdXMgPSBjaHVua1tsaW1pdCAtIDFdO1xuICAgICAgICAgICAgbGltaXQtLTtcbiAgICAgICAgICAgIGNodW5rID0gY2h1bmsuc2xpY2UoMCwgbGltaXQpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHsgc3RhdGVUYWJsZSB9ID0gdGhpcztcbiAgICAgICAgdGhpcy5jaHVuayA9IGNodW5rO1xuICAgICAgICB0aGlzLmkgPSAwO1xuICAgICAgICB3aGlsZSAodGhpcy5pIDwgbGltaXQpIHtcbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZXhwbGljaXQtYW55XG4gICAgICAgICAgICBzdGF0ZVRhYmxlW3RoaXMuc3RhdGVdLmNhbGwodGhpcyk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jaHVua1Bvc2l0aW9uICs9IGxpbWl0O1xuICAgICAgICByZXR1cm4gZW5kID8gdGhpcy5lbmQoKSA6IHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENsb3NlIHRoZSBjdXJyZW50IHN0cmVhbS4gUGVyZm9ybSBmaW5hbCB3ZWxsLWZvcm1lZG5lc3MgY2hlY2tzIGFuZCByZXNldFxuICAgICAqIHRoZSBwYXJzZXIgdHN0YXRlLlxuICAgICAqXG4gICAgICogQHJldHVybnMgdGhpc1xuICAgICAqL1xuICAgIGNsb3NlKCkge1xuICAgICAgICByZXR1cm4gdGhpcy53cml0ZShudWxsKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGEgc2luZ2xlIGNvZGUgcG9pbnQgb3V0IG9mIHRoZSBjdXJyZW50IGNodW5rLiBUaGlzIHVwZGF0ZXMgdGhlIGN1cnJlbnRcbiAgICAgKiBwb3NpdGlvbiBpZiB3ZSBkbyBwb3NpdGlvbiB0cmFja2luZy5cbiAgICAgKlxuICAgICAqIFRoaXMgaXMgdGhlIGFsZ29yaXRobSB0byB1c2UgZm9yIFhNTCAxLjAuXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyBUaGUgY2hhcmFjdGVyIHJlYWQuXG4gICAgICovXG4gICAgZ2V0Q29kZTEwKCkge1xuICAgICAgICBjb25zdCB7IGNodW5rLCBpIH0gPSB0aGlzO1xuICAgICAgICB0aGlzLnByZXZJID0gaTtcbiAgICAgICAgLy8gWWVzLCB3ZSBkbyB0aGlzIGluc3RlYWQgb2YgZG9pbmcgdGhpcy5pKysuIERvaW5nIGl0IHRoaXMgd2F5LCB3ZSBkbyBub3RcbiAgICAgICAgLy8gcmVhZCB0aGlzLmkgYWdhaW4sIHdoaWNoIGlzIGEgYml0IGZhc3Rlci5cbiAgICAgICAgdGhpcy5pID0gaSArIDE7XG4gICAgICAgIGlmIChpID49IGNodW5rLmxlbmd0aCkge1xuICAgICAgICAgICAgcmV0dXJuIEVPQztcbiAgICAgICAgfVxuICAgICAgICAvLyBVc2luZyBjaGFyQ29kZUF0IGFuZCBoYW5kbGluZyB0aGUgc3Vycm9nYXRlcyBvdXJzZWx2ZXMgaXMgZmFzdGVyXG4gICAgICAgIC8vIHRoYW4gdXNpbmcgY29kZVBvaW50QXQuXG4gICAgICAgIGNvbnN0IGNvZGUgPSBjaHVuay5jaGFyQ29kZUF0KGkpO1xuICAgICAgICB0aGlzLmNvbHVtbisrO1xuICAgICAgICBpZiAoY29kZSA8IDB4RDgwMCkge1xuICAgICAgICAgICAgaWYgKGNvZGUgPj0gU1BBQ0UgfHwgY29kZSA9PT0gVEFCKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGNvZGU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBzd2l0Y2ggKGNvZGUpIHtcbiAgICAgICAgICAgICAgICBjYXNlIE5MOlxuICAgICAgICAgICAgICAgICAgICB0aGlzLmxpbmUrKztcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jb2x1bW4gPSAwO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnBvc2l0aW9uQXROZXdMaW5lID0gdGhpcy5wb3NpdGlvbjtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIE5MO1xuICAgICAgICAgICAgICAgIGNhc2UgQ1I6XG4gICAgICAgICAgICAgICAgICAgIC8vIFdlIG1heSBnZXQgTmFOIGlmIHdlIHJlYWQgcGFzdCB0aGUgZW5kIG9mIHRoZSBjaHVuaywgd2hpY2ggaXMgZmluZS5cbiAgICAgICAgICAgICAgICAgICAgaWYgKGNodW5rLmNoYXJDb2RlQXQoaSArIDEpID09PSBOTCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gQSBcXHJcXG4gc2VxdWVuY2UgaXMgY29udmVydGVkIHRvIFxcbiBzbyB3ZSBoYXZlIHRvIHNraXAgb3ZlciB0aGVcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIG5leHQgY2hhcmFjdGVyLiBXZSBhbHJlYWR5IGtub3cgaXQgaGFzIGEgc2l6ZSBvZiAxIHNvICsrIGlzIGZpbmVcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGhlcmUuXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmkgPSBpICsgMjtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAvLyBPdGhlcndpc2UsIGEgXFxyIGlzIGp1c3QgY29udmVydGVkIHRvIFxcbiwgc28gd2UgZG9uJ3QgaGF2ZSB0byBza2lwXG4gICAgICAgICAgICAgICAgICAgIC8vIGFoZWFkLlxuICAgICAgICAgICAgICAgICAgICAvLyBJbiBlaXRoZXIgY2FzZSwgXFxyIGJlY29tZXMgXFxuLlxuICAgICAgICAgICAgICAgICAgICB0aGlzLmxpbmUrKztcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jb2x1bW4gPSAwO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnBvc2l0aW9uQXROZXdMaW5lID0gdGhpcy5wb3NpdGlvbjtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIE5MX0xJS0U7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAgICAgLy8gSWYgd2UgZ2V0IGhlcmUsIHRoZW4gY29kZSA8IFNQQUNFIGFuZCBpdCBpcyBub3QgTkwgQ1Igb3IgVEFCLlxuICAgICAgICAgICAgICAgICAgICB0aGlzLmZhaWwoXCJkaXNhbGxvd2VkIGNoYXJhY3Rlci5cIik7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBjb2RlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChjb2RlID4gMHhEQkZGKSB7XG4gICAgICAgICAgICAvLyBUaGlzIGlzIGEgc3BlY2lhbGl6ZWQgdmVyc2lvbiBvZiBpc0NoYXIxMCB0aGF0IHRha2VzIGludG8gYWNjb3VudFxuICAgICAgICAgICAgLy8gdGhhdCBpbiB0aGlzIGNvbnRleHQgY29kZSA+IDB4REJGRiBhbmQgY29kZSA8PSAweEZGRkYuIFNvIGl0IGRvZXMgbm90XG4gICAgICAgICAgICAvLyB0ZXN0IGNhc2VzIHRoYXQgZG9uJ3QgbmVlZCB0ZXN0aW5nLlxuICAgICAgICAgICAgaWYgKCEoY29kZSA+PSAweEUwMDAgJiYgY29kZSA8PSAweEZGRkQpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwiZGlzYWxsb3dlZCBjaGFyYWN0ZXIuXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGNvZGU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZmluYWwgPSAweDEwMDAwICsgKChjb2RlIC0gMHhEODAwKSAqIDB4NDAwKSArXG4gICAgICAgICAgICAoY2h1bmsuY2hhckNvZGVBdChpICsgMSkgLSAweERDMDApO1xuICAgICAgICB0aGlzLmkgPSBpICsgMjtcbiAgICAgICAgLy8gVGhpcyBpcyBhIHNwZWNpYWxpemVkIHZlcnNpb24gb2YgaXNDaGFyMTAgdGhhdCB0YWtlcyBpbnRvIGFjY291bnQgdGhhdCBpblxuICAgICAgICAvLyB0aGlzIGNvbnRleHQgbmVjZXNzYXJpbHkgZmluYWwgPj0gMHgxMDAwMC5cbiAgICAgICAgaWYgKGZpbmFsID4gMHgxMEZGRkYpIHtcbiAgICAgICAgICAgIHRoaXMuZmFpbChcImRpc2FsbG93ZWQgY2hhcmFjdGVyLlwiKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZmluYWw7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBhIHNpbmdsZSBjb2RlIHBvaW50IG91dCBvZiB0aGUgY3VycmVudCBjaHVuay4gVGhpcyB1cGRhdGVzIHRoZSBjdXJyZW50XG4gICAgICogcG9zaXRpb24gaWYgd2UgZG8gcG9zaXRpb24gdHJhY2tpbmcuXG4gICAgICpcbiAgICAgKiBUaGlzIGlzIHRoZSBhbGdvcml0aG0gdG8gdXNlIGZvciBYTUwgMS4xLlxuICAgICAqXG4gICAgICogQHJldHVybnMge251bWJlcn0gVGhlIGNoYXJhY3RlciByZWFkLlxuICAgICAqL1xuICAgIGdldENvZGUxMSgpIHtcbiAgICAgICAgY29uc3QgeyBjaHVuaywgaSB9ID0gdGhpcztcbiAgICAgICAgdGhpcy5wcmV2SSA9IGk7XG4gICAgICAgIC8vIFllcywgd2UgZG8gdGhpcyBpbnN0ZWFkIG9mIGRvaW5nIHRoaXMuaSsrLiBEb2luZyBpdCB0aGlzIHdheSwgd2UgZG8gbm90XG4gICAgICAgIC8vIHJlYWQgdGhpcy5pIGFnYWluLCB3aGljaCBpcyBhIGJpdCBmYXN0ZXIuXG4gICAgICAgIHRoaXMuaSA9IGkgKyAxO1xuICAgICAgICBpZiAoaSA+PSBjaHVuay5sZW5ndGgpIHtcbiAgICAgICAgICAgIHJldHVybiBFT0M7XG4gICAgICAgIH1cbiAgICAgICAgLy8gVXNpbmcgY2hhckNvZGVBdCBhbmQgaGFuZGxpbmcgdGhlIHN1cnJvZ2F0ZXMgb3Vyc2VsdmVzIGlzIGZhc3RlclxuICAgICAgICAvLyB0aGFuIHVzaW5nIGNvZGVQb2ludEF0LlxuICAgICAgICBjb25zdCBjb2RlID0gY2h1bmsuY2hhckNvZGVBdChpKTtcbiAgICAgICAgdGhpcy5jb2x1bW4rKztcbiAgICAgICAgaWYgKGNvZGUgPCAweEQ4MDApIHtcbiAgICAgICAgICAgIGlmICgoY29kZSA+IDB4MUYgJiYgY29kZSA8IDB4N0YpIHx8IChjb2RlID4gMHg5RiAmJiBjb2RlICE9PSBMUykgfHxcbiAgICAgICAgICAgICAgICBjb2RlID09PSBUQUIpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gY29kZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHN3aXRjaCAoY29kZSkge1xuICAgICAgICAgICAgICAgIGNhc2UgTkw6IC8vIDB4QVxuICAgICAgICAgICAgICAgICAgICB0aGlzLmxpbmUrKztcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jb2x1bW4gPSAwO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnBvc2l0aW9uQXROZXdMaW5lID0gdGhpcy5wb3NpdGlvbjtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIE5MO1xuICAgICAgICAgICAgICAgIGNhc2UgQ1I6IHsgLy8gMHhEXG4gICAgICAgICAgICAgICAgICAgIC8vIFdlIG1heSBnZXQgTmFOIGlmIHdlIHJlYWQgcGFzdCB0aGUgZW5kIG9mIHRoZSBjaHVuaywgd2hpY2ggaXNcbiAgICAgICAgICAgICAgICAgICAgLy8gZmluZS5cbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV4dCA9IGNodW5rLmNoYXJDb2RlQXQoaSArIDEpO1xuICAgICAgICAgICAgICAgICAgICBpZiAobmV4dCA9PT0gTkwgfHwgbmV4dCA9PT0gTkVMKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBBIENSIE5MIG9yIENSIE5FTCBzZXF1ZW5jZSBpcyBjb252ZXJ0ZWQgdG8gTkwgc28gd2UgaGF2ZSB0byBza2lwXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBvdmVyIHRoZSBuZXh0IGNoYXJhY3Rlci4gV2UgYWxyZWFkeSBrbm93IGl0IGhhcyBhIHNpemUgb2YgMS5cbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuaSA9IGkgKyAyO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIC8vIE90aGVyd2lzZSwgYSBDUiBpcyBqdXN0IGNvbnZlcnRlZCB0byBOTCwgbm8gc2tpcC5cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLyogeWVzLCBmYWxsIHRocm91Z2ggKi9cbiAgICAgICAgICAgICAgICBjYXNlIE5FTDogLy8gMHg4NVxuICAgICAgICAgICAgICAgIGNhc2UgTFM6IC8vIE94MjAyOFxuICAgICAgICAgICAgICAgICAgICB0aGlzLmxpbmUrKztcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jb2x1bW4gPSAwO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnBvc2l0aW9uQXROZXdMaW5lID0gdGhpcy5wb3NpdGlvbjtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIE5MX0xJS0U7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwiZGlzYWxsb3dlZCBjaGFyYWN0ZXIuXCIpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gY29kZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoY29kZSA+IDB4REJGRikge1xuICAgICAgICAgICAgLy8gVGhpcyBpcyBhIHNwZWNpYWxpemVkIHZlcnNpb24gb2YgaXNDaGFyQW5kTm90UmVzdHJpY3RlZCB0aGF0IHRha2VzIGludG9cbiAgICAgICAgICAgIC8vIGFjY291bnQgdGhhdCBpbiB0aGlzIGNvbnRleHQgY29kZSA+IDB4REJGRiBhbmQgY29kZSA8PSAweEZGRkYuIFNvIGl0XG4gICAgICAgICAgICAvLyBkb2VzIG5vdCB0ZXN0IGNhc2VzIHRoYXQgZG9uJ3QgbmVlZCB0ZXN0aW5nLlxuICAgICAgICAgICAgaWYgKCEoY29kZSA+PSAweEUwMDAgJiYgY29kZSA8PSAweEZGRkQpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwiZGlzYWxsb3dlZCBjaGFyYWN0ZXIuXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGNvZGU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZmluYWwgPSAweDEwMDAwICsgKChjb2RlIC0gMHhEODAwKSAqIDB4NDAwKSArXG4gICAgICAgICAgICAoY2h1bmsuY2hhckNvZGVBdChpICsgMSkgLSAweERDMDApO1xuICAgICAgICB0aGlzLmkgPSBpICsgMjtcbiAgICAgICAgLy8gVGhpcyBpcyBhIHNwZWNpYWxpemVkIHZlcnNpb24gb2YgaXNDaGFyQW5kTm90UmVzdHJpY3RlZCB0aGF0IHRha2VzIGludG9cbiAgICAgICAgLy8gYWNjb3VudCB0aGF0IGluIHRoaXMgY29udGV4dCBuZWNlc3NhcmlseSBmaW5hbCA+PSAweDEwMDAwLlxuICAgICAgICBpZiAoZmluYWwgPiAweDEwRkZGRikge1xuICAgICAgICAgICAgdGhpcy5mYWlsKFwiZGlzYWxsb3dlZCBjaGFyYWN0ZXIuXCIpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBmaW5hbDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTGlrZSBgYGdldENvZGVgYCBidXQgd2l0aCB0aGUgcmV0dXJuIHZhbHVlIG5vcm1hbGl6ZWQgc28gdGhhdCBgYE5MYGAgaXNcbiAgICAgKiByZXR1cm5lZCBmb3IgYGBOTF9MSUtFYGAuXG4gICAgICovXG4gICAgZ2V0Q29kZU5vcm0oKSB7XG4gICAgICAgIGNvbnN0IGMgPSB0aGlzLmdldENvZGUoKTtcbiAgICAgICAgcmV0dXJuIGMgPT09IE5MX0xJS0UgPyBOTCA6IGM7XG4gICAgfVxuICAgIHVuZ2V0KCkge1xuICAgICAgICB0aGlzLmkgPSB0aGlzLnByZXZJO1xuICAgICAgICB0aGlzLmNvbHVtbi0tO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDYXB0dXJlIGNoYXJhY3RlcnMgaW50byBhIGJ1ZmZlciB1bnRpbCBlbmNvdW50ZXJpbmcgb25lIG9mIGEgc2V0IG9mXG4gICAgICogY2hhcmFjdGVycy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBjaGFycyBBbiBhcnJheSBvZiBjb2RlcG9pbnRzLiBFbmNvdW50ZXJpbmcgYSBjaGFyYWN0ZXIgaW4gdGhlIGFycmF5XG4gICAgICogZW5kcyB0aGUgY2FwdHVyZS4gKGBgY2hhcnNgYCBtYXkgc2FmZWx5IGNvbnRhaW4gYGBOTGBgLilcbiAgICAgKlxuICAgICAqIEByZXR1cm4gVGhlIGNoYXJhY3RlciBjb2RlIHRoYXQgbWFkZSB0aGUgY2FwdHVyZSBlbmQsIG9yIGBgRU9DYGAgaWYgd2UgaGl0XG4gICAgICogdGhlIGVuZCBvZiB0aGUgY2h1bmsuIFRoZSByZXR1cm4gdmFsdWUgY2Fubm90IGJlIE5MX0xJS0U6IE5MIGlzIHJldHVybmVkXG4gICAgICogaW5zdGVhZC5cbiAgICAgKi9cbiAgICBjYXB0dXJlVG8oY2hhcnMpIHtcbiAgICAgICAgbGV0IHsgaTogc3RhcnQgfSA9IHRoaXM7XG4gICAgICAgIGNvbnN0IHsgY2h1bmsgfSA9IHRoaXM7XG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1jb25zdGFudC1jb25kaXRpb25cbiAgICAgICAgd2hpbGUgKHRydWUpIHtcbiAgICAgICAgICAgIGNvbnN0IGMgPSB0aGlzLmdldENvZGUoKTtcbiAgICAgICAgICAgIGNvbnN0IGlzTkxMaWtlID0gYyA9PT0gTkxfTElLRTtcbiAgICAgICAgICAgIGNvbnN0IGZpbmFsID0gaXNOTExpa2UgPyBOTCA6IGM7XG4gICAgICAgICAgICBpZiAoZmluYWwgPT09IEVPQyB8fCBjaGFycy5pbmNsdWRlcyhmaW5hbCkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnRleHQgKz0gY2h1bmsuc2xpY2Uoc3RhcnQsIHRoaXMucHJldkkpO1xuICAgICAgICAgICAgICAgIHJldHVybiBmaW5hbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpc05MTGlrZSkge1xuICAgICAgICAgICAgICAgIHRoaXMudGV4dCArPSBgJHtjaHVuay5zbGljZShzdGFydCwgdGhpcy5wcmV2SSl9XFxuYDtcbiAgICAgICAgICAgICAgICBzdGFydCA9IHRoaXMuaTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBDYXB0dXJlIGNoYXJhY3RlcnMgaW50byBhIGJ1ZmZlciB1bnRpbCBlbmNvdW50ZXJpbmcgYSBjaGFyYWN0ZXIuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gY2hhciBUaGUgY29kZXBvaW50IHRoYXQgZW5kcyB0aGUgY2FwdHVyZS4gKipOT1RFIGBgY2hhcmBgIE1BWSBOT1RcbiAgICAgKiBDT05UQUlOIGBgTkxgYC4qKiBQYXNzaW5nIGBgTkxgYCB3aWxsIHJlc3VsdCBpbiBidWdneSBiZWhhdmlvci5cbiAgICAgKlxuICAgICAqIEByZXR1cm4gYGB0cnVlYGAgaWYgd2UgcmFuIGludG8gdGhlIGNoYXJhY3Rlci4gT3RoZXJ3aXNlLCB3ZSByYW4gaW50byB0aGVcbiAgICAgKiBlbmQgb2YgdGhlIGN1cnJlbnQgY2h1bmsuXG4gICAgICovXG4gICAgY2FwdHVyZVRvQ2hhcihjaGFyKSB7XG4gICAgICAgIGxldCB7IGk6IHN0YXJ0IH0gPSB0aGlzO1xuICAgICAgICBjb25zdCB7IGNodW5rIH0gPSB0aGlzO1xuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tY29uc3RhbnQtY29uZGl0aW9uXG4gICAgICAgIHdoaWxlICh0cnVlKSB7XG4gICAgICAgICAgICBsZXQgYyA9IHRoaXMuZ2V0Q29kZSgpO1xuICAgICAgICAgICAgc3dpdGNoIChjKSB7XG4gICAgICAgICAgICAgICAgY2FzZSBOTF9MSUtFOlxuICAgICAgICAgICAgICAgICAgICB0aGlzLnRleHQgKz0gYCR7Y2h1bmsuc2xpY2Uoc3RhcnQsIHRoaXMucHJldkkpfVxcbmA7XG4gICAgICAgICAgICAgICAgICAgIHN0YXJ0ID0gdGhpcy5pO1xuICAgICAgICAgICAgICAgICAgICBjID0gTkw7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgRU9DOlxuICAgICAgICAgICAgICAgICAgICB0aGlzLnRleHQgKz0gY2h1bmsuc2xpY2Uoc3RhcnQpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChjID09PSBjaGFyKSB7XG4gICAgICAgICAgICAgICAgdGhpcy50ZXh0ICs9IGNodW5rLnNsaWNlKHN0YXJ0LCB0aGlzLnByZXZJKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBDYXB0dXJlIGNoYXJhY3RlcnMgdGhhdCBzYXRpc2Z5IGBgaXNOYW1lQ2hhcmBgIGludG8gdGhlIGBgbmFtZWBgIGZpZWxkIG9mXG4gICAgICogdGhpcyBwYXJzZXIuXG4gICAgICpcbiAgICAgKiBAcmV0dXJuIFRoZSBjaGFyYWN0ZXIgY29kZSB0aGF0IG1hZGUgdGhlIHRlc3QgZmFpbCwgb3IgYGBFT0NgYCBpZiB3ZSBoaXRcbiAgICAgKiB0aGUgZW5kIG9mIHRoZSBjaHVuay4gVGhlIHJldHVybiB2YWx1ZSBjYW5ub3QgYmUgTkxfTElLRTogTkwgaXMgcmV0dXJuZWRcbiAgICAgKiBpbnN0ZWFkLlxuICAgICAqL1xuICAgIGNhcHR1cmVOYW1lQ2hhcnMoKSB7XG4gICAgICAgIGNvbnN0IHsgY2h1bmssIGk6IHN0YXJ0IH0gPSB0aGlzO1xuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tY29uc3RhbnQtY29uZGl0aW9uXG4gICAgICAgIHdoaWxlICh0cnVlKSB7XG4gICAgICAgICAgICBjb25zdCBjID0gdGhpcy5nZXRDb2RlKCk7XG4gICAgICAgICAgICBpZiAoYyA9PT0gRU9DKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5uYW1lICs9IGNodW5rLnNsaWNlKHN0YXJ0KTtcbiAgICAgICAgICAgICAgICByZXR1cm4gRU9DO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gTkwgaXMgbm90IGEgbmFtZSBjaGFyIHNvIHdlIGRvbid0IGhhdmUgdG8gdGVzdCBzcGVjaWZpY2FsbHkgZm9yIGl0LlxuICAgICAgICAgICAgaWYgKCFpc05hbWVDaGFyKGMpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5uYW1lICs9IGNodW5rLnNsaWNlKHN0YXJ0LCB0aGlzLnByZXZJKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gYyA9PT0gTkxfTElLRSA/IE5MIDogYztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBTa2lwIHdoaXRlIHNwYWNlcy5cbiAgICAgKlxuICAgICAqIEByZXR1cm4gVGhlIGNoYXJhY3RlciB0aGF0IGVuZGVkIHRoZSBza2lwLCBvciBgYEVPQ2BgIGlmIHdlIGhpdFxuICAgICAqIHRoZSBlbmQgb2YgdGhlIGNodW5rLiBUaGUgcmV0dXJuIHZhbHVlIGNhbm5vdCBiZSBOTF9MSUtFOiBOTCBpcyByZXR1cm5lZFxuICAgICAqIGluc3RlYWQuXG4gICAgICovXG4gICAgc2tpcFNwYWNlcygpIHtcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWNvbnN0YW50LWNvbmRpdGlvblxuICAgICAgICB3aGlsZSAodHJ1ZSkge1xuICAgICAgICAgICAgY29uc3QgYyA9IHRoaXMuZ2V0Q29kZU5vcm0oKTtcbiAgICAgICAgICAgIGlmIChjID09PSBFT0MgfHwgIWlzUyhjKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBjO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHNldFhNTFZlcnNpb24odmVyc2lvbikge1xuICAgICAgICB0aGlzLmN1cnJlbnRYTUxWZXJzaW9uID0gdmVyc2lvbjtcbiAgICAgICAgLyogIGVzbGludC1kaXNhYmxlIEB0eXBlc2NyaXB0LWVzbGludC91bmJvdW5kLW1ldGhvZCAqL1xuICAgICAgICBpZiAodmVyc2lvbiA9PT0gXCIxLjBcIikge1xuICAgICAgICAgICAgdGhpcy5pc0NoYXIgPSBpc0NoYXIxMDtcbiAgICAgICAgICAgIHRoaXMuZ2V0Q29kZSA9IHRoaXMuZ2V0Q29kZTEwO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5pc0NoYXIgPSBpc0NoYXIxMTtcbiAgICAgICAgICAgIHRoaXMuZ2V0Q29kZSA9IHRoaXMuZ2V0Q29kZTExO1xuICAgICAgICB9XG4gICAgICAgIC8qIGVzbGludC1lbmFibGUgQHR5cGVzY3JpcHQtZXNsaW50L3VuYm91bmQtbWV0aG9kICovXG4gICAgfVxuICAgIC8vIFNUQVRFIEVOR0lORSBNRVRIT0RTXG4gICAgLy8gVGhpcyBuZWVkcyB0byBiZSBhIHN0YXRlIHNlcGFyYXRlIGZyb20gU19CRUdJTl9XSElURVNQQUNFIGJlY2F1c2Ugd2Ugd2FudFxuICAgIC8vIHRvIGJlIHN1cmUgbmV2ZXIgdG8gY29tZSBiYWNrIHRvIHRoaXMgc3RhdGUgbGF0ZXIuXG4gICAgc0JlZ2luKCkge1xuICAgICAgICAvLyBXZSBhcmUgZXNzZW50aWFsbHkgcGVla2luZyBhdCB0aGUgZmlyc3QgY2hhcmFjdGVyIG9mIHRoZSBjaHVuay4gU2luY2VcbiAgICAgICAgLy8gU19CRUdJTiBjYW4gYmUgaW4gZWZmZWN0IG9ubHkgd2hlbiB3ZSBzdGFydCB3b3JraW5nIG9uIHRoZSBmaXJzdCBjaHVuayxcbiAgICAgICAgLy8gdGhlIGluZGV4IGF0IHdoaWNoIHdlIG11c3QgbG9vayBpcyBuZWNlc3NhcmlseSAwLiBOb3RlIGFsc28gdGhhdCB0aGVcbiAgICAgICAgLy8gZm9sbG93aW5nIHRlc3QgZG9lcyBub3QgZGVwZW5kIG9uIGRlY29kaW5nIHN1cnJvZ2F0ZXMuXG4gICAgICAgIC8vIElmIHRoZSBpbml0aWFsIGNoYXJhY3RlciBpcyAweEZFRkYsIGlnbm9yZSBpdC5cbiAgICAgICAgaWYgKHRoaXMuY2h1bmsuY2hhckNvZGVBdCgwKSA9PT0gMHhGRUZGKSB7XG4gICAgICAgICAgICB0aGlzLmkrKztcbiAgICAgICAgICAgIHRoaXMuY29sdW1uKys7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zdGF0ZSA9IFNfQkVHSU5fV0hJVEVTUEFDRTtcbiAgICB9XG4gICAgc0JlZ2luV2hpdGVzcGFjZSgpIHtcbiAgICAgICAgLy8gV2UgbmVlZCB0byBrbm93IHdoZXRoZXIgd2UndmUgZW5jb3VudGVyZWQgc3BhY2VzIG9yIG5vdCBiZWNhdXNlIGFzIHNvb25cbiAgICAgICAgLy8gYXMgd2UgcnVuIGludG8gYSBzcGFjZSwgYW4gWE1MIGRlY2xhcmF0aW9uIGlzIG5vIGxvbmdlciBwb3NzaWJsZS4gUmF0aGVyXG4gICAgICAgIC8vIHRoYW4gc2xvdyBkb3duIHNraXBTcGFjZXMgZXZlbiBpbiBwbGFjZXMgd2hlcmUgd2UgZG9uJ3QgY2FyZSB3aGV0aGVyIGl0XG4gICAgICAgIC8vIHNraXBwZWQgYW55dGhpbmcgb3Igbm90LCB3ZSBjaGVjayB3aGV0aGVyIHByZXZJIGlzIGVxdWFsIHRvIHRoZSB2YWx1ZSBvZlxuICAgICAgICAvLyBpIGZyb20gYmVmb3JlIHdlIHNraXAgc3BhY2VzLlxuICAgICAgICBjb25zdCBpQmVmb3JlID0gdGhpcy5pO1xuICAgICAgICBjb25zdCBjID0gdGhpcy5za2lwU3BhY2VzKCk7XG4gICAgICAgIGlmICh0aGlzLnByZXZJICE9PSBpQmVmb3JlKSB7XG4gICAgICAgICAgICB0aGlzLnhtbERlY2xQb3NzaWJsZSA9IGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIHN3aXRjaCAoYykge1xuICAgICAgICAgICAgY2FzZSBMRVNTOlxuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX09QRU5fV0FLQTtcbiAgICAgICAgICAgICAgICAvLyBXZSBjb3VsZCBuYWl2ZWx5IGNhbGwgY2xvc2VUZXh0IGJ1dCBpbiB0aGlzIHN0YXRlLCBpdCBpcyBub3Qgbm9ybWFsXG4gICAgICAgICAgICAgICAgLy8gdG8gaGF2ZSB0ZXh0IGJlIGZpbGxlZCB3aXRoIGFueSBkYXRhLlxuICAgICAgICAgICAgICAgIGlmICh0aGlzLnRleHQubGVuZ3RoICE9PSAwKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIm5vLWVtcHR5IHRleHQgYXQgc3RhcnRcIik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSBFT0M6XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIHRoaXMudW5nZXQoKTtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU19URVhUO1xuICAgICAgICAgICAgICAgIHRoaXMueG1sRGVjbFBvc3NpYmxlID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc0RvY3R5cGUoKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgY29uc3QgYyA9IHRoaXMuY2FwdHVyZVRvKERPQ1RZUEVfVEVSTUlOQVRPUik7XG4gICAgICAgIHN3aXRjaCAoYykge1xuICAgICAgICAgICAgY2FzZSBHUkVBVEVSOiB7XG4gICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVudXNlZC1leHByZXNzaW9uc1xuICAgICAgICAgICAgICAgIChfYSA9IHRoaXMuZG9jdHlwZUhhbmRsZXIpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYWxsKHRoaXMsIHRoaXMudGV4dCk7XG4gICAgICAgICAgICAgICAgdGhpcy50ZXh0ID0gXCJcIjtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU19URVhUO1xuICAgICAgICAgICAgICAgIHRoaXMuZG9jdHlwZSA9IHRydWU7IC8vIGp1c3QgcmVtZW1iZXIgdGhhdCB3ZSBzYXcgaXQuXG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXNlIEVPQzpcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgdGhpcy50ZXh0ICs9IFN0cmluZy5mcm9tQ29kZVBvaW50KGMpO1xuICAgICAgICAgICAgICAgIGlmIChjID09PSBPUEVOX0JSQUNLRVQpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfRFREO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmIChpc1F1b3RlKGMpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0RPQ1RZUEVfUVVPVEU7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucSA9IGM7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHNEb2N0eXBlUXVvdGUoKSB7XG4gICAgICAgIGNvbnN0IHEgPSB0aGlzLnE7XG4gICAgICAgIGlmICh0aGlzLmNhcHR1cmVUb0NoYXIocSkpIHtcbiAgICAgICAgICAgIHRoaXMudGV4dCArPSBTdHJpbmcuZnJvbUNvZGVQb2ludChxKTtcbiAgICAgICAgICAgIHRoaXMucSA9IG51bGw7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19ET0NUWVBFO1xuICAgICAgICB9XG4gICAgfVxuICAgIHNEVEQoKSB7XG4gICAgICAgIGNvbnN0IGMgPSB0aGlzLmNhcHR1cmVUbyhEVERfVEVSTUlOQVRPUik7XG4gICAgICAgIGlmIChjID09PSBFT0MpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnRleHQgKz0gU3RyaW5nLmZyb21Db2RlUG9pbnQoYyk7XG4gICAgICAgIGlmIChjID09PSBDTE9TRV9CUkFDS0VUKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19ET0NUWVBFO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGMgPT09IExFU1MpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0RURF9PUEVOX1dBS0E7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoaXNRdW90ZShjKSkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfRFREX1FVT1RFRDtcbiAgICAgICAgICAgIHRoaXMucSA9IGM7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc0RURFF1b3RlZCgpIHtcbiAgICAgICAgY29uc3QgcSA9IHRoaXMucTtcbiAgICAgICAgaWYgKHRoaXMuY2FwdHVyZVRvQ2hhcihxKSkge1xuICAgICAgICAgICAgdGhpcy50ZXh0ICs9IFN0cmluZy5mcm9tQ29kZVBvaW50KHEpO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfRFREO1xuICAgICAgICAgICAgdGhpcy5xID0gbnVsbDtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzRFRET3Blbldha2EoKSB7XG4gICAgICAgIGNvbnN0IGMgPSB0aGlzLmdldENvZGVOb3JtKCk7XG4gICAgICAgIHRoaXMudGV4dCArPSBTdHJpbmcuZnJvbUNvZGVQb2ludChjKTtcbiAgICAgICAgc3dpdGNoIChjKSB7XG4gICAgICAgICAgICBjYXNlIEJBTkc6XG4gICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfRFREX09QRU5fV0FLQV9CQU5HO1xuICAgICAgICAgICAgICAgIHRoaXMub3Blbldha2FCYW5nID0gXCJcIjtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgUVVFU1RJT046XG4gICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfRFREX1BJO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU19EVEQ7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc0RURE9wZW5XYWthQmFuZygpIHtcbiAgICAgICAgY29uc3QgY2hhciA9IFN0cmluZy5mcm9tQ29kZVBvaW50KHRoaXMuZ2V0Q29kZU5vcm0oKSk7XG4gICAgICAgIGNvbnN0IG93YiA9IHRoaXMub3Blbldha2FCYW5nICs9IGNoYXI7XG4gICAgICAgIHRoaXMudGV4dCArPSBjaGFyO1xuICAgICAgICBpZiAob3diICE9PSBcIi1cIikge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IG93YiA9PT0gXCItLVwiID8gU19EVERfQ09NTUVOVCA6IFNfRFREO1xuICAgICAgICAgICAgdGhpcy5vcGVuV2FrYUJhbmcgPSBcIlwiO1xuICAgICAgICB9XG4gICAgfVxuICAgIHNEVERDb21tZW50KCkge1xuICAgICAgICBpZiAodGhpcy5jYXB0dXJlVG9DaGFyKE1JTlVTKSkge1xuICAgICAgICAgICAgdGhpcy50ZXh0ICs9IFwiLVwiO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfRFREX0NPTU1FTlRfRU5ESU5HO1xuICAgICAgICB9XG4gICAgfVxuICAgIHNEVERDb21tZW50RW5kaW5nKCkge1xuICAgICAgICBjb25zdCBjID0gdGhpcy5nZXRDb2RlTm9ybSgpO1xuICAgICAgICB0aGlzLnRleHQgKz0gU3RyaW5nLmZyb21Db2RlUG9pbnQoYyk7XG4gICAgICAgIHRoaXMuc3RhdGUgPSBjID09PSBNSU5VUyA/IFNfRFREX0NPTU1FTlRfRU5ERUQgOiBTX0RURF9DT01NRU5UO1xuICAgIH1cbiAgICBzRFREQ29tbWVudEVuZGVkKCkge1xuICAgICAgICBjb25zdCBjID0gdGhpcy5nZXRDb2RlTm9ybSgpO1xuICAgICAgICB0aGlzLnRleHQgKz0gU3RyaW5nLmZyb21Db2RlUG9pbnQoYyk7XG4gICAgICAgIGlmIChjID09PSBHUkVBVEVSKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19EVEQ7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmZhaWwoXCJtYWxmb3JtZWQgY29tbWVudC5cIik7XG4gICAgICAgICAgICAvLyA8IS0tIGJsYWggLS0gYmxvbyAtLT4gd2lsbCBiZSByZWNvcmRlZCBhc1xuICAgICAgICAgICAgLy8gYSBjb21tZW50IG9mIFwiIGJsYWggLS0gYmxvbyBcIlxuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfRFREX0NPTU1FTlQ7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc0RURFBJKCkge1xuICAgICAgICBpZiAodGhpcy5jYXB0dXJlVG9DaGFyKFFVRVNUSU9OKSkge1xuICAgICAgICAgICAgdGhpcy50ZXh0ICs9IFwiP1wiO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfRFREX1BJX0VORElORztcbiAgICAgICAgfVxuICAgIH1cbiAgICBzRFREUElFbmRpbmcoKSB7XG4gICAgICAgIGNvbnN0IGMgPSB0aGlzLmdldENvZGVOb3JtKCk7XG4gICAgICAgIHRoaXMudGV4dCArPSBTdHJpbmcuZnJvbUNvZGVQb2ludChjKTtcbiAgICAgICAgaWYgKGMgPT09IEdSRUFURVIpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0RURDtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzVGV4dCgpIHtcbiAgICAgICAgLy9cbiAgICAgICAgLy8gV2UgZGlkIHRyeSBhIHZlcnNpb24gb2Ygc2F4ZXMgd2hlcmUgdGhlIFNfVEVYVCBzdGF0ZSB3YXMgc3BsaXQgaW4gdHdvXG4gICAgICAgIC8vIHN0YXRlczogb25lIGZvciB0ZXh0IGluc2lkZSB0aGUgcm9vdCBlbGVtZW50LCBhbmQgb25lIGZvciB0ZXh0XG4gICAgICAgIC8vIG91dHNpZGUuIFRoaXMgd2FzIGF2b2lkaW5nIGhhdmluZyB0byB0ZXN0IHRoaXMudGFncy5sZW5ndGggdG8gZGVjaWRlXG4gICAgICAgIC8vIHdoYXQgaW1wbGVtZW50YXRpb24gdG8gYWN0dWFsbHkgdXNlLlxuICAgICAgICAvL1xuICAgICAgICAvLyBQZWZvcm1hbmNlIHRlc3Rpbmcgb24gZ2lnYWJ5dGUtc2l6ZSBmaWxlcyBkaWQgbm90IHNob3cgYW55IGFkdmFudGFnZSB0b1xuICAgICAgICAvLyB1c2luZyB0aGUgdHdvIHN0YXRlcyBzb2x1dGlvbiBpbnN0ZWFkIG9mIHRoZSBjdXJyZW50IG9uZS4gQ29udmVyc2VseSwgaXRcbiAgICAgICAgLy8gbWFkZSB0aGUgY29kZSBhIGJpdCBtb3JlIGNvbXBsaWNhdGVkIGVsc2V3aGVyZS4gRm9yIGluc3RhbmNlLCBhIGNvbW1lbnRcbiAgICAgICAgLy8gY2FuIGFwcGVhciBiZWZvcmUgdGhlIHJvb3QgZWxlbWVudCBzbyB3aGVuIGEgY29tbWVudCBlbmRlZCBpdCB3YXNcbiAgICAgICAgLy8gbmVjZXNzYXJ5IHRvIGRldGVybWluZSB3aGV0aGVyIHRvIHJldHVybiB0byB0aGUgU19URVhUIHN0YXRlIG9yIHRvIHRoZVxuICAgICAgICAvLyBuZXcgdGV4dC1vdXRzaWRlLXJvb3Qgc3RhdGUuXG4gICAgICAgIC8vXG4gICAgICAgIGlmICh0aGlzLnRhZ3MubGVuZ3RoICE9PSAwKSB7XG4gICAgICAgICAgICB0aGlzLmhhbmRsZVRleHRJblJvb3QoKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuaGFuZGxlVGV4dE91dHNpZGVSb290KCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc0VudGl0eSgpIHtcbiAgICAgICAgLy8gVGhpcyBpcyBlc3NlbnRpYWxseSBhIHNwZWNpYWxpemVkIHZlcnNpb24gb2YgY2FwdHVyZVRvQ2hhcihTRU1JQ09MT04uLi4pXG4gICAgICAgIGxldCB7IGk6IHN0YXJ0IH0gPSB0aGlzO1xuICAgICAgICBjb25zdCB7IGNodW5rIH0gPSB0aGlzO1xuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tbGFiZWxzLCBuby1yZXN0cmljdGVkLXN5bnRheFxuICAgICAgICBsb29wOiBcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWNvbnN0YW50LWNvbmRpdGlvblxuICAgICAgICB3aGlsZSAodHJ1ZSkge1xuICAgICAgICAgICAgc3dpdGNoICh0aGlzLmdldENvZGUoKSkge1xuICAgICAgICAgICAgICAgIGNhc2UgTkxfTElLRTpcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lbnRpdHkgKz0gYCR7Y2h1bmsuc2xpY2Uoc3RhcnQsIHRoaXMucHJldkkpfVxcbmA7XG4gICAgICAgICAgICAgICAgICAgIHN0YXJ0ID0gdGhpcy5pO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIFNFTUlDT0xPTjoge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGVudGl0eVJldHVyblN0YXRlIH0gPSB0aGlzO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBlbnRpdHkgPSB0aGlzLmVudGl0eSArIGNodW5rLnNsaWNlKHN0YXJ0LCB0aGlzLnByZXZJKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IGVudGl0eVJldHVyblN0YXRlO1xuICAgICAgICAgICAgICAgICAgICBsZXQgcGFyc2VkO1xuICAgICAgICAgICAgICAgICAgICBpZiAoZW50aXR5ID09PSBcIlwiKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmZhaWwoXCJlbXB0eSBlbnRpdHkgbmFtZS5cIik7XG4gICAgICAgICAgICAgICAgICAgICAgICBwYXJzZWQgPSBcIiY7XCI7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBwYXJzZWQgPSB0aGlzLnBhcnNlRW50aXR5KGVudGl0eSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVudGl0eSA9IFwiXCI7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgaWYgKGVudGl0eVJldHVyblN0YXRlICE9PSBTX1RFWFQgfHwgdGhpcy50ZXh0SGFuZGxlciAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnRleHQgKz0gcGFyc2VkO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1sYWJlbHNcbiAgICAgICAgICAgICAgICAgICAgYnJlYWsgbG9vcDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBFT0M6XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZW50aXR5ICs9IGNodW5rLnNsaWNlKHN0YXJ0KTtcbiAgICAgICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWxhYmVsc1xuICAgICAgICAgICAgICAgICAgICBicmVhayBsb29wO1xuICAgICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgc09wZW5XYWthKCkge1xuICAgICAgICAvLyBSZW1pbmRlcjogYSBzdGF0ZSBoYW5kbGVyIGlzIGNhbGxlZCB3aXRoIGF0IGxlYXN0IG9uZSBjaGFyYWN0ZXJcbiAgICAgICAgLy8gYXZhaWxhYmxlIGluIHRoZSBjdXJyZW50IGNodW5rLiBTbyB0aGUgZmlyc3QgY2FsbCB0byBnZXQgY29kZSBpbnNpZGUgb2ZcbiAgICAgICAgLy8gYSBzdGF0ZSBoYW5kbGVyIGNhbm5vdCByZXR1cm4gYGBFT0NgYC4gVGhhdCdzIHdoeSB3ZSBkb24ndCB0ZXN0XG4gICAgICAgIC8vIGZvciBpdC5cbiAgICAgICAgY29uc3QgYyA9IHRoaXMuZ2V0Q29kZSgpO1xuICAgICAgICAvLyBlaXRoZXIgYSAvLCA/LCAhLCBvciB0ZXh0IGlzIGNvbWluZyBuZXh0LlxuICAgICAgICBpZiAoaXNOYW1lU3RhcnRDaGFyKGMpKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19PUEVOX1RBRztcbiAgICAgICAgICAgIHRoaXMudW5nZXQoKTtcbiAgICAgICAgICAgIHRoaXMueG1sRGVjbFBvc3NpYmxlID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBzd2l0Y2ggKGMpIHtcbiAgICAgICAgICAgICAgICBjYXNlIEZPUldBUkRfU0xBU0g6XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0NMT1NFX1RBRztcbiAgICAgICAgICAgICAgICAgICAgdGhpcy54bWxEZWNsUG9zc2libGUgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSBCQU5HOlxuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU19PUEVOX1dBS0FfQkFORztcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5vcGVuV2FrYUJhbmcgPSBcIlwiO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnhtbERlY2xQb3NzaWJsZSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIFFVRVNUSU9OOlxuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU19QSV9GSVJTVF9DSEFSO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgICAgICB0aGlzLmZhaWwoXCJkaXNhbGxvd2VkIGNoYXJhY3RlciBpbiB0YWcgbmFtZVwiKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfVEVYVDtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy54bWxEZWNsUG9zc2libGUgPSBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBzT3Blbldha2FCYW5nKCkge1xuICAgICAgICB0aGlzLm9wZW5XYWthQmFuZyArPSBTdHJpbmcuZnJvbUNvZGVQb2ludCh0aGlzLmdldENvZGVOb3JtKCkpO1xuICAgICAgICBzd2l0Y2ggKHRoaXMub3Blbldha2FCYW5nKSB7XG4gICAgICAgICAgICBjYXNlIFwiW0NEQVRBW1wiOlxuICAgICAgICAgICAgICAgIGlmICghdGhpcy5zYXdSb290ICYmICF0aGlzLnJlcG9ydGVkVGV4dEJlZm9yZVJvb3QpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwidGV4dCBkYXRhIG91dHNpZGUgb2Ygcm9vdCBub2RlLlwiKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZXBvcnRlZFRleHRCZWZvcmVSb290ID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuY2xvc2VkUm9vdCAmJiAhdGhpcy5yZXBvcnRlZFRleHRBZnRlclJvb3QpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwidGV4dCBkYXRhIG91dHNpZGUgb2Ygcm9vdCBub2RlLlwiKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZXBvcnRlZFRleHRBZnRlclJvb3QgPSB0cnVlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU19DREFUQTtcbiAgICAgICAgICAgICAgICB0aGlzLm9wZW5XYWthQmFuZyA9IFwiXCI7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBjYXNlIFwiLS1cIjpcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU19DT01NRU5UO1xuICAgICAgICAgICAgICAgIHRoaXMub3Blbldha2FCYW5nID0gXCJcIjtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgXCJET0NUWVBFXCI6XG4gICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfRE9DVFlQRTtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5kb2N0eXBlIHx8IHRoaXMuc2F3Um9vdCkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmZhaWwoXCJpbmFwcHJvcHJpYXRlbHkgbG9jYXRlZCBkb2N0eXBlIGRlY2xhcmF0aW9uLlwiKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy5vcGVuV2FrYUJhbmcgPSBcIlwiO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAvLyA3IGhhcHBlbnMgdG8gYmUgdGhlIG1heGltdW0gbGVuZ3RoIG9mIHRoZSBzdHJpbmcgdGhhdCBjYW4gcG9zc2libHlcbiAgICAgICAgICAgICAgICAvLyBtYXRjaCBvbmUgb2YgdGhlIGNhc2VzIGFib3ZlLlxuICAgICAgICAgICAgICAgIGlmICh0aGlzLm9wZW5XYWthQmFuZy5sZW5ndGggPj0gNykge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmZhaWwoXCJpbmNvcnJlY3Qgc3ludGF4LlwiKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgc0NvbW1lbnQoKSB7XG4gICAgICAgIGlmICh0aGlzLmNhcHR1cmVUb0NoYXIoTUlOVVMpKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19DT01NRU5UX0VORElORztcbiAgICAgICAgfVxuICAgIH1cbiAgICBzQ29tbWVudEVuZGluZygpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICBjb25zdCBjID0gdGhpcy5nZXRDb2RlTm9ybSgpO1xuICAgICAgICBpZiAoYyA9PT0gTUlOVVMpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0NPTU1FTlRfRU5ERUQ7XG4gICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLWV4cHJlc3Npb25zXG4gICAgICAgICAgICAoX2EgPSB0aGlzLmNvbW1lbnRIYW5kbGVyKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbCh0aGlzLCB0aGlzLnRleHQpO1xuICAgICAgICAgICAgdGhpcy50ZXh0ID0gXCJcIjtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMudGV4dCArPSBgLSR7U3RyaW5nLmZyb21Db2RlUG9pbnQoYyl9YDtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0NPTU1FTlQ7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc0NvbW1lbnRFbmRlZCgpIHtcbiAgICAgICAgY29uc3QgYyA9IHRoaXMuZ2V0Q29kZU5vcm0oKTtcbiAgICAgICAgaWYgKGMgIT09IEdSRUFURVIpIHtcbiAgICAgICAgICAgIHRoaXMuZmFpbChcIm1hbGZvcm1lZCBjb21tZW50LlwiKTtcbiAgICAgICAgICAgIC8vIDwhLS0gYmxhaCAtLSBibG9vIC0tPiB3aWxsIGJlIHJlY29yZGVkIGFzXG4gICAgICAgICAgICAvLyBhIGNvbW1lbnQgb2YgXCIgYmxhaCAtLSBibG9vIFwiXG4gICAgICAgICAgICB0aGlzLnRleHQgKz0gYC0tJHtTdHJpbmcuZnJvbUNvZGVQb2ludChjKX1gO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfQ09NTUVOVDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX1RFWFQ7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc0NEYXRhKCkge1xuICAgICAgICBpZiAodGhpcy5jYXB0dXJlVG9DaGFyKENMT1NFX0JSQUNLRVQpKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19DREFUQV9FTkRJTkc7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc0NEYXRhRW5kaW5nKCkge1xuICAgICAgICBjb25zdCBjID0gdGhpcy5nZXRDb2RlTm9ybSgpO1xuICAgICAgICBpZiAoYyA9PT0gQ0xPU0VfQlJBQ0tFVCkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfQ0RBVEFfRU5ESU5HXzI7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnRleHQgKz0gYF0ke1N0cmluZy5mcm9tQ29kZVBvaW50KGMpfWA7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19DREFUQTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzQ0RhdGFFbmRpbmcyKCkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIGNvbnN0IGMgPSB0aGlzLmdldENvZGVOb3JtKCk7XG4gICAgICAgIHN3aXRjaCAoYykge1xuICAgICAgICAgICAgY2FzZSBHUkVBVEVSOiB7XG4gICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVudXNlZC1leHByZXNzaW9uc1xuICAgICAgICAgICAgICAgIChfYSA9IHRoaXMuY2RhdGFIYW5kbGVyKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbCh0aGlzLCB0aGlzLnRleHQpO1xuICAgICAgICAgICAgICAgIHRoaXMudGV4dCA9IFwiXCI7XG4gICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfVEVYVDtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhc2UgQ0xPU0VfQlJBQ0tFVDpcbiAgICAgICAgICAgICAgICB0aGlzLnRleHQgKz0gXCJdXCI7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIHRoaXMudGV4dCArPSBgXV0ke1N0cmluZy5mcm9tQ29kZVBvaW50KGMpfWA7XG4gICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfQ0RBVEE7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLy8gV2UgbmVlZCB0aGlzIHNlcGFyYXRlIHN0YXRlIHRvIGNoZWNrIHRoZSBmaXJzdCBjaGFyYWN0ZXIgZm8gdGhlIHBpIHRhcmdldFxuICAgIC8vIHdpdGggdGhpcy5uYW1lU3RhcnRDaGVjayB3aGljaCBhbGxvd3MgbGVzcyBjaGFyYWN0ZXJzIHRoYW4gdGhpcy5uYW1lQ2hlY2suXG4gICAgc1BJRmlyc3RDaGFyKCkge1xuICAgICAgICBjb25zdCBjID0gdGhpcy5nZXRDb2RlTm9ybSgpO1xuICAgICAgICAvLyBUaGlzIGlzIGZpcnN0IGJlY2F1c2UgaW4gdGhlIGNhc2Ugd2hlcmUgdGhlIGZpbGUgaXMgd2VsbC1mb3JtZWQgdGhpcyBpc1xuICAgICAgICAvLyB0aGUgYnJhbmNoIHRha2VuLiBXZSBvcHRpbWl6ZSBmb3Igd2VsbC1mb3JtZWRuZXNzLlxuICAgICAgICBpZiAodGhpcy5uYW1lU3RhcnRDaGVjayhjKSkge1xuICAgICAgICAgICAgdGhpcy5waVRhcmdldCArPSBTdHJpbmcuZnJvbUNvZGVQb2ludChjKTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX1BJX1JFU1Q7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYyA9PT0gUVVFU1RJT04gfHwgaXNTKGMpKSB7XG4gICAgICAgICAgICB0aGlzLmZhaWwoXCJwcm9jZXNzaW5nIGluc3RydWN0aW9uIHdpdGhvdXQgYSB0YXJnZXQuXCIpO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IGMgPT09IFFVRVNUSU9OID8gU19QSV9FTkRJTkcgOiBTX1BJX0JPRFk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmZhaWwoXCJkaXNhbGxvd2VkIGNoYXJhY3RlciBpbiBwcm9jZXNzaW5nIGluc3RydWN0aW9uIG5hbWUuXCIpO1xuICAgICAgICAgICAgdGhpcy5waVRhcmdldCArPSBTdHJpbmcuZnJvbUNvZGVQb2ludChjKTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX1BJX1JFU1Q7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc1BJUmVzdCgpIHtcbiAgICAgICAgLy8gQ2FwdHVyZSBjaGFyYWN0ZXJzIGludG8gYSBwaVRhcmdldCB3aGlsZSBgYHRoaXMubmFtZUNoZWNrYGAgcnVuIG9uIHRoZVxuICAgICAgICAvLyBjaGFyYWN0ZXIgcmVhZCByZXR1cm5zIHRydWUuXG4gICAgICAgIGNvbnN0IHsgY2h1bmssIGk6IHN0YXJ0IH0gPSB0aGlzO1xuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tY29uc3RhbnQtY29uZGl0aW9uXG4gICAgICAgIHdoaWxlICh0cnVlKSB7XG4gICAgICAgICAgICBjb25zdCBjID0gdGhpcy5nZXRDb2RlTm9ybSgpO1xuICAgICAgICAgICAgaWYgKGMgPT09IEVPQykge1xuICAgICAgICAgICAgICAgIHRoaXMucGlUYXJnZXQgKz0gY2h1bmsuc2xpY2Uoc3RhcnQpO1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIE5MIGNhbm5vdCBzYXRpc2Z5IHRoaXMubmFtZUNoZWNrIHNvIHdlIGRvbid0IGhhdmUgdG8gdGVzdCBzcGVjaWZpY2FsbHlcbiAgICAgICAgICAgIC8vIGZvciBpdC5cbiAgICAgICAgICAgIGlmICghdGhpcy5uYW1lQ2hlY2soYykpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnBpVGFyZ2V0ICs9IGNodW5rLnNsaWNlKHN0YXJ0LCB0aGlzLnByZXZJKTtcbiAgICAgICAgICAgICAgICBjb25zdCBpc1F1ZXN0aW9uID0gYyA9PT0gUVVFU1RJT047XG4gICAgICAgICAgICAgICAgaWYgKGlzUXVlc3Rpb24gfHwgaXNTKGMpKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLnBpVGFyZ2V0ID09PSBcInhtbFwiKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXRoaXMueG1sRGVjbFBvc3NpYmxlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwiYW4gWE1MIGRlY2xhcmF0aW9uIG11c3QgYmUgYXQgdGhlIHN0YXJ0IG9mIHRoZSBkb2N1bWVudC5cIik7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gaXNRdWVzdGlvbiA/IFNfWE1MX0RFQ0xfRU5ESU5HIDogU19YTUxfREVDTF9OQU1FX1NUQVJUO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IGlzUXVlc3Rpb24gPyBTX1BJX0VORElORyA6IFNfUElfQk9EWTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwiZGlzYWxsb3dlZCBjaGFyYWN0ZXIgaW4gcHJvY2Vzc2luZyBpbnN0cnVjdGlvbiBuYW1lLlwiKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5waVRhcmdldCArPSBTdHJpbmcuZnJvbUNvZGVQb2ludChjKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgc1BJQm9keSgpIHtcbiAgICAgICAgaWYgKHRoaXMudGV4dC5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgIGNvbnN0IGMgPSB0aGlzLmdldENvZGVOb3JtKCk7XG4gICAgICAgICAgICBpZiAoYyA9PT0gUVVFU1RJT04pIHtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU19QSV9FTkRJTkc7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICghaXNTKGMpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy50ZXh0ID0gU3RyaW5nLmZyb21Db2RlUG9pbnQoYyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgLy8gVGhlIHF1ZXN0aW9uIG1hcmsgY2hhcmFjdGVyIGlzIG5vdCB2YWxpZCBpbnNpZGUgYW55IG9mIHRoZSBYTUxcbiAgICAgICAgLy8gZGVjbGFyYXRpb24gbmFtZS92YWx1ZSBwYWlycy5cbiAgICAgICAgZWxzZSBpZiAodGhpcy5jYXB0dXJlVG9DaGFyKFFVRVNUSU9OKSkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfUElfRU5ESU5HO1xuICAgICAgICB9XG4gICAgfVxuICAgIHNQSUVuZGluZygpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICBjb25zdCBjID0gdGhpcy5nZXRDb2RlTm9ybSgpO1xuICAgICAgICBpZiAoYyA9PT0gR1JFQVRFUikge1xuICAgICAgICAgICAgY29uc3QgeyBwaVRhcmdldCB9ID0gdGhpcztcbiAgICAgICAgICAgIGlmIChwaVRhcmdldC50b0xvd2VyQ2FzZSgpID09PSBcInhtbFwiKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwidGhlIFhNTCBkZWNsYXJhdGlvbiBtdXN0IGFwcGVhciBhdCB0aGUgc3RhcnQgb2YgdGhlIGRvY3VtZW50LlwiKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby11bnVzZWQtZXhwcmVzc2lvbnNcbiAgICAgICAgICAgIChfYSA9IHRoaXMucGlIYW5kbGVyKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbCh0aGlzLCB7XG4gICAgICAgICAgICAgICAgdGFyZ2V0OiBwaVRhcmdldCxcbiAgICAgICAgICAgICAgICBib2R5OiB0aGlzLnRleHQsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRoaXMucGlUYXJnZXQgPSB0aGlzLnRleHQgPSBcIlwiO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfVEVYVDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChjID09PSBRVUVTVElPTikge1xuICAgICAgICAgICAgLy8gV2UgcmFuIGludG8gPz8gYXMgcGFydCBvZiBhIHByb2Nlc3NpbmcgaW5zdHJ1Y3Rpb24uIFdlIGluaXRpYWxseSB0b29rXG4gICAgICAgICAgICAvLyB0aGUgZmlyc3QgPyBhcyBhIHNpZ24gdGhhdCB0aGUgUEkgd2FzIGVuZGluZywgYnV0IGl0IGlzIG5vdC4gU28gd2UgaGF2ZVxuICAgICAgICAgICAgLy8gdG8gYWRkIGl0IHRvIHRoZSBib2R5IGJ1dCB3ZSB0YWtlIHRoZSBuZXcgPyBhcyBhIHNpZ24gdGhhdCB0aGUgUEkgaXNcbiAgICAgICAgICAgIC8vIGVuZGluZy5cbiAgICAgICAgICAgIHRoaXMudGV4dCArPSBcIj9cIjtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMudGV4dCArPSBgPyR7U3RyaW5nLmZyb21Db2RlUG9pbnQoYyl9YDtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX1BJX0JPRFk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy54bWxEZWNsUG9zc2libGUgPSBmYWxzZTtcbiAgICB9XG4gICAgc1hNTERlY2xOYW1lU3RhcnQoKSB7XG4gICAgICAgIGNvbnN0IGMgPSB0aGlzLnNraXBTcGFjZXMoKTtcbiAgICAgICAgLy8gVGhlIHF1ZXN0aW9uIG1hcmsgY2hhcmFjdGVyIGlzIG5vdCB2YWxpZCBpbnNpZGUgYW55IG9mIHRoZSBYTUxcbiAgICAgICAgLy8gZGVjbGFyYXRpb24gbmFtZS92YWx1ZSBwYWlycy5cbiAgICAgICAgaWYgKGMgPT09IFFVRVNUSU9OKSB7XG4gICAgICAgICAgICAvLyBJdCBpcyB2YWxpZCB0byBnbyB0byBTX1hNTF9ERUNMX0VORElORyBmcm9tIHRoaXMgc3RhdGUuXG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19YTUxfREVDTF9FTkRJTkc7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGMgIT09IEVPQykge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfWE1MX0RFQ0xfTkFNRTtcbiAgICAgICAgICAgIHRoaXMubmFtZSA9IFN0cmluZy5mcm9tQ29kZVBvaW50KGMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHNYTUxEZWNsTmFtZSgpIHtcbiAgICAgICAgY29uc3QgYyA9IHRoaXMuY2FwdHVyZVRvKFhNTF9ERUNMX05BTUVfVEVSTUlOQVRPUik7XG4gICAgICAgIC8vIFRoZSBxdWVzdGlvbiBtYXJrIGNoYXJhY3RlciBpcyBub3QgdmFsaWQgaW5zaWRlIGFueSBvZiB0aGUgWE1MXG4gICAgICAgIC8vIGRlY2xhcmF0aW9uIG5hbWUvdmFsdWUgcGFpcnMuXG4gICAgICAgIGlmIChjID09PSBRVUVTVElPTikge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfWE1MX0RFQ0xfRU5ESU5HO1xuICAgICAgICAgICAgdGhpcy5uYW1lICs9IHRoaXMudGV4dDtcbiAgICAgICAgICAgIHRoaXMudGV4dCA9IFwiXCI7XG4gICAgICAgICAgICB0aGlzLmZhaWwoXCJYTUwgZGVjbGFyYXRpb24gaXMgaW5jb21wbGV0ZS5cIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCEoaXNTKGMpIHx8IGMgPT09IEVRVUFMKSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMubmFtZSArPSB0aGlzLnRleHQ7XG4gICAgICAgIHRoaXMudGV4dCA9IFwiXCI7XG4gICAgICAgIGlmICghdGhpcy54bWxEZWNsRXhwZWN0cy5pbmNsdWRlcyh0aGlzLm5hbWUpKSB7XG4gICAgICAgICAgICBzd2l0Y2ggKHRoaXMubmFtZS5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICBjYXNlIDA6XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZmFpbChcImRpZCBub3QgZXhwZWN0IGFueSBtb3JlIG5hbWUvdmFsdWUgcGFpcnMuXCIpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIDE6XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZmFpbChgZXhwZWN0ZWQgdGhlIG5hbWUgJHt0aGlzLnhtbERlY2xFeHBlY3RzWzBdfS5gKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mYWlsKGBleHBlY3RlZCBvbmUgb2YgJHt0aGlzLnhtbERlY2xFeHBlY3RzLmpvaW4oXCIsIFwiKX1gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aGlzLnN0YXRlID0gYyA9PT0gRVFVQUwgPyBTX1hNTF9ERUNMX1ZBTFVFX1NUQVJUIDogU19YTUxfREVDTF9FUTtcbiAgICB9XG4gICAgc1hNTERlY2xFcSgpIHtcbiAgICAgICAgY29uc3QgYyA9IHRoaXMuZ2V0Q29kZU5vcm0oKTtcbiAgICAgICAgLy8gVGhlIHF1ZXN0aW9uIG1hcmsgY2hhcmFjdGVyIGlzIG5vdCB2YWxpZCBpbnNpZGUgYW55IG9mIHRoZSBYTUxcbiAgICAgICAgLy8gZGVjbGFyYXRpb24gbmFtZS92YWx1ZSBwYWlycy5cbiAgICAgICAgaWYgKGMgPT09IFFVRVNUSU9OKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19YTUxfREVDTF9FTkRJTkc7XG4gICAgICAgICAgICB0aGlzLmZhaWwoXCJYTUwgZGVjbGFyYXRpb24gaXMgaW5jb21wbGV0ZS5cIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGlzUyhjKSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmIChjICE9PSBFUVVBTCkge1xuICAgICAgICAgICAgdGhpcy5mYWlsKFwidmFsdWUgcmVxdWlyZWQuXCIpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuc3RhdGUgPSBTX1hNTF9ERUNMX1ZBTFVFX1NUQVJUO1xuICAgIH1cbiAgICBzWE1MRGVjbFZhbHVlU3RhcnQoKSB7XG4gICAgICAgIGNvbnN0IGMgPSB0aGlzLmdldENvZGVOb3JtKCk7XG4gICAgICAgIC8vIFRoZSBxdWVzdGlvbiBtYXJrIGNoYXJhY3RlciBpcyBub3QgdmFsaWQgaW5zaWRlIGFueSBvZiB0aGUgWE1MXG4gICAgICAgIC8vIGRlY2xhcmF0aW9uIG5hbWUvdmFsdWUgcGFpcnMuXG4gICAgICAgIGlmIChjID09PSBRVUVTVElPTikge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfWE1MX0RFQ0xfRU5ESU5HO1xuICAgICAgICAgICAgdGhpcy5mYWlsKFwiWE1MIGRlY2xhcmF0aW9uIGlzIGluY29tcGxldGUuXCIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmIChpc1MoYykpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIWlzUXVvdGUoYykpIHtcbiAgICAgICAgICAgIHRoaXMuZmFpbChcInZhbHVlIG11c3QgYmUgcXVvdGVkLlwiKTtcbiAgICAgICAgICAgIHRoaXMucSA9IFNQQUNFO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5xID0gYztcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnN0YXRlID0gU19YTUxfREVDTF9WQUxVRTtcbiAgICB9XG4gICAgc1hNTERlY2xWYWx1ZSgpIHtcbiAgICAgICAgY29uc3QgYyA9IHRoaXMuY2FwdHVyZVRvKFt0aGlzLnEsIFFVRVNUSU9OXSk7XG4gICAgICAgIC8vIFRoZSBxdWVzdGlvbiBtYXJrIGNoYXJhY3RlciBpcyBub3QgdmFsaWQgaW5zaWRlIGFueSBvZiB0aGUgWE1MXG4gICAgICAgIC8vIGRlY2xhcmF0aW9uIG5hbWUvdmFsdWUgcGFpcnMuXG4gICAgICAgIGlmIChjID09PSBRVUVTVElPTikge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfWE1MX0RFQ0xfRU5ESU5HO1xuICAgICAgICAgICAgdGhpcy50ZXh0ID0gXCJcIjtcbiAgICAgICAgICAgIHRoaXMuZmFpbChcIlhNTCBkZWNsYXJhdGlvbiBpcyBpbmNvbXBsZXRlLlwiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoYyA9PT0gRU9DKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdmFsdWUgPSB0aGlzLnRleHQ7XG4gICAgICAgIHRoaXMudGV4dCA9IFwiXCI7XG4gICAgICAgIHN3aXRjaCAodGhpcy5uYW1lKSB7XG4gICAgICAgICAgICBjYXNlIFwidmVyc2lvblwiOiB7XG4gICAgICAgICAgICAgICAgdGhpcy54bWxEZWNsRXhwZWN0cyA9IFtcImVuY29kaW5nXCIsIFwic3RhbmRhbG9uZVwiXTtcbiAgICAgICAgICAgICAgICBjb25zdCB2ZXJzaW9uID0gdmFsdWU7XG4gICAgICAgICAgICAgICAgdGhpcy54bWxEZWNsLnZlcnNpb24gPSB2ZXJzaW9uO1xuICAgICAgICAgICAgICAgIC8vIFRoaXMgaXMgdGhlIHRlc3Qgc3BlY2lmaWVkIGJ5IFhNTCAxLjAgYnV0IGl0IGlzIGZpbmUgZm9yIFhNTCAxLjEuXG4gICAgICAgICAgICAgICAgaWYgKCEvXjFcXC5bMC05XSskLy50ZXN0KHZlcnNpb24pKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZmFpbChcInZlcnNpb24gbnVtYmVyIG11c3QgbWF0Y2ggL14xXFxcXC5bMC05XSskLy5cIik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIFdoZW4gZm9yY2VYTUxWZXJzaW9uIGlzIHNldCwgdGhlIFhNTCBkZWNsYXJhdGlvbiBpcyBpZ25vcmVkLlxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKCF0aGlzLm9wdC5mb3JjZVhNTFZlcnNpb24pIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zZXRYTUxWZXJzaW9uKHZlcnNpb24pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhc2UgXCJlbmNvZGluZ1wiOlxuICAgICAgICAgICAgICAgIGlmICghL15bQS1aYS16XVtBLVphLXowLTkuXy1dKiQvLnRlc3QodmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZmFpbChcImVuY29kaW5nIHZhbHVlIG11c3QgbWF0Y2ggXFxcbi9eW0EtWmEtejAtOV1bQS1aYS16MC05Ll8tXSokLy5cIik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMueG1sRGVjbEV4cGVjdHMgPSBbXCJzdGFuZGFsb25lXCJdO1xuICAgICAgICAgICAgICAgIHRoaXMueG1sRGVjbC5lbmNvZGluZyA9IHZhbHVlO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSBcInN0YW5kYWxvbmVcIjpcbiAgICAgICAgICAgICAgICBpZiAodmFsdWUgIT09IFwieWVzXCIgJiYgdmFsdWUgIT09IFwibm9cIikge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmZhaWwoXCJzdGFuZGFsb25lIHZhbHVlIG11c3QgbWF0Y2ggXFxcInllc1xcXCIgb3IgXFxcIm5vXFxcIi5cIik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMueG1sRGVjbEV4cGVjdHMgPSBbXTtcbiAgICAgICAgICAgICAgICB0aGlzLnhtbERlY2wuc3RhbmRhbG9uZSA9IHZhbHVlO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIC8vIFdlIGRvbid0IG5lZWQgdG8gcmFpc2UgYW4gZXJyb3IgaGVyZSBzaW5jZSB3ZSd2ZSBhbHJlYWR5IHJhaXNlZCBvbmVcbiAgICAgICAgICAgIC8vIHdoZW4gY2hlY2tpbmcgd2hhdCBuYW1lIHdhcyBleHBlY3RlZC5cbiAgICAgICAgfVxuICAgICAgICB0aGlzLm5hbWUgPSBcIlwiO1xuICAgICAgICB0aGlzLnN0YXRlID0gU19YTUxfREVDTF9TRVBBUkFUT1I7XG4gICAgfVxuICAgIHNYTUxEZWNsU2VwYXJhdG9yKCkge1xuICAgICAgICBjb25zdCBjID0gdGhpcy5nZXRDb2RlTm9ybSgpO1xuICAgICAgICAvLyBUaGUgcXVlc3Rpb24gbWFyayBjaGFyYWN0ZXIgaXMgbm90IHZhbGlkIGluc2lkZSBhbnkgb2YgdGhlIFhNTFxuICAgICAgICAvLyBkZWNsYXJhdGlvbiBuYW1lL3ZhbHVlIHBhaXJzLlxuICAgICAgICBpZiAoYyA9PT0gUVVFU1RJT04pIHtcbiAgICAgICAgICAgIC8vIEl0IGlzIHZhbGlkIHRvIGdvIHRvIFNfWE1MX0RFQ0xfRU5ESU5HIGZyb20gdGhpcyBzdGF0ZS5cbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX1hNTF9ERUNMX0VORElORztcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIWlzUyhjKSkge1xuICAgICAgICAgICAgdGhpcy5mYWlsKFwid2hpdGVzcGFjZSByZXF1aXJlZC5cIik7XG4gICAgICAgICAgICB0aGlzLnVuZ2V0KCk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zdGF0ZSA9IFNfWE1MX0RFQ0xfTkFNRV9TVEFSVDtcbiAgICB9XG4gICAgc1hNTERlY2xFbmRpbmcoKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgY29uc3QgYyA9IHRoaXMuZ2V0Q29kZU5vcm0oKTtcbiAgICAgICAgaWYgKGMgPT09IEdSRUFURVIpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLnBpVGFyZ2V0ICE9PSBcInhtbFwiKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwicHJvY2Vzc2luZyBpbnN0cnVjdGlvbnMgYXJlIG5vdCBhbGxvd2VkIGJlZm9yZSByb290LlwiKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKHRoaXMubmFtZSAhPT0gXCJ2ZXJzaW9uXCIgJiZcbiAgICAgICAgICAgICAgICB0aGlzLnhtbERlY2xFeHBlY3RzLmluY2x1ZGVzKFwidmVyc2lvblwiKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuZmFpbChcIlhNTCBkZWNsYXJhdGlvbiBtdXN0IGNvbnRhaW4gYSB2ZXJzaW9uLlwiKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby11bnVzZWQtZXhwcmVzc2lvbnNcbiAgICAgICAgICAgIChfYSA9IHRoaXMueG1sZGVjbEhhbmRsZXIpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYWxsKHRoaXMsIHRoaXMueG1sRGVjbCk7XG4gICAgICAgICAgICB0aGlzLm5hbWUgPSBcIlwiO1xuICAgICAgICAgICAgdGhpcy5waVRhcmdldCA9IHRoaXMudGV4dCA9IFwiXCI7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19URVhUO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgLy8gV2UgZ290IGhlcmUgYmVjYXVzZSB0aGUgcHJldmlvdXMgY2hhcmFjdGVyIHdhcyBhID8sIGJ1dCB0aGUgcXVlc3Rpb25cbiAgICAgICAgICAgIC8vIG1hcmsgY2hhcmFjdGVyIGlzIG5vdCB2YWxpZCBpbnNpZGUgYW55IG9mIHRoZSBYTUwgZGVjbGFyYXRpb25cbiAgICAgICAgICAgIC8vIG5hbWUvdmFsdWUgcGFpcnMuXG4gICAgICAgICAgICB0aGlzLmZhaWwoXCJUaGUgY2hhcmFjdGVyID8gaXMgZGlzYWxsb3dlZCBhbnl3aGVyZSBpbiBYTUwgZGVjbGFyYXRpb25zLlwiKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnhtbERlY2xQb3NzaWJsZSA9IGZhbHNlO1xuICAgIH1cbiAgICBzT3BlblRhZygpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICBjb25zdCBjID0gdGhpcy5jYXB0dXJlTmFtZUNoYXJzKCk7XG4gICAgICAgIGlmIChjID09PSBFT0MpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB0YWcgPSB0aGlzLnRhZyA9IHtcbiAgICAgICAgICAgIG5hbWU6IHRoaXMubmFtZSxcbiAgICAgICAgICAgIGF0dHJpYnV0ZXM6IE9iamVjdC5jcmVhdGUobnVsbCksXG4gICAgICAgIH07XG4gICAgICAgIHRoaXMubmFtZSA9IFwiXCI7XG4gICAgICAgIGlmICh0aGlzLnhtbG5zT3B0KSB7XG4gICAgICAgICAgICB0aGlzLnRvcE5TID0gdGFnLm5zID0gT2JqZWN0LmNyZWF0ZShudWxsKTtcbiAgICAgICAgfVxuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLWV4cHJlc3Npb25zXG4gICAgICAgIChfYSA9IHRoaXMub3BlblRhZ1N0YXJ0SGFuZGxlcikgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNhbGwodGhpcywgdGFnKTtcbiAgICAgICAgdGhpcy5zYXdSb290ID0gdHJ1ZTtcbiAgICAgICAgaWYgKCF0aGlzLmZyYWdtZW50T3B0ICYmIHRoaXMuY2xvc2VkUm9vdCkge1xuICAgICAgICAgICAgdGhpcy5mYWlsKFwiZG9jdW1lbnRzIG1heSBjb250YWluIG9ubHkgb25lIHJvb3QuXCIpO1xuICAgICAgICB9XG4gICAgICAgIHN3aXRjaCAoYykge1xuICAgICAgICAgICAgY2FzZSBHUkVBVEVSOlxuICAgICAgICAgICAgICAgIHRoaXMub3BlblRhZygpO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSBGT1JXQVJEX1NMQVNIOlxuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX09QRU5fVEFHX1NMQVNIO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICBpZiAoIWlzUyhjKSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmZhaWwoXCJkaXNhbGxvd2VkIGNoYXJhY3RlciBpbiB0YWcgbmFtZS5cIik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0FUVFJJQjtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzT3BlblRhZ1NsYXNoKCkge1xuICAgICAgICBpZiAodGhpcy5nZXRDb2RlKCkgPT09IEdSRUFURVIpIHtcbiAgICAgICAgICAgIHRoaXMub3BlblNlbGZDbG9zaW5nVGFnKCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmZhaWwoXCJmb3J3YXJkLXNsYXNoIGluIG9wZW5pbmcgdGFnIG5vdCBmb2xsb3dlZCBieSA+LlwiKTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0FUVFJJQjtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzQXR0cmliKCkge1xuICAgICAgICBjb25zdCBjID0gdGhpcy5za2lwU3BhY2VzKCk7XG4gICAgICAgIGlmIChjID09PSBFT0MpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoaXNOYW1lU3RhcnRDaGFyKGMpKSB7XG4gICAgICAgICAgICB0aGlzLnVuZ2V0KCk7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19BVFRSSUJfTkFNRTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChjID09PSBHUkVBVEVSKSB7XG4gICAgICAgICAgICB0aGlzLm9wZW5UYWcoKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChjID09PSBGT1JXQVJEX1NMQVNIKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19PUEVOX1RBR19TTEFTSDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuZmFpbChcImRpc2FsbG93ZWQgY2hhcmFjdGVyIGluIGF0dHJpYnV0ZSBuYW1lLlwiKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzQXR0cmliTmFtZSgpIHtcbiAgICAgICAgY29uc3QgYyA9IHRoaXMuY2FwdHVyZU5hbWVDaGFycygpO1xuICAgICAgICBpZiAoYyA9PT0gRVFVQUwpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0FUVFJJQl9WQUxVRTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpc1MoYykpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0FUVFJJQl9OQU1FX1NBV19XSElURTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChjID09PSBHUkVBVEVSKSB7XG4gICAgICAgICAgICB0aGlzLmZhaWwoXCJhdHRyaWJ1dGUgd2l0aG91dCB2YWx1ZS5cIik7XG4gICAgICAgICAgICB0aGlzLnB1c2hBdHRyaWIodGhpcy5uYW1lLCB0aGlzLm5hbWUpO1xuICAgICAgICAgICAgdGhpcy5uYW1lID0gdGhpcy50ZXh0ID0gXCJcIjtcbiAgICAgICAgICAgIHRoaXMub3BlblRhZygpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGMgIT09IEVPQykge1xuICAgICAgICAgICAgdGhpcy5mYWlsKFwiZGlzYWxsb3dlZCBjaGFyYWN0ZXIgaW4gYXR0cmlidXRlIG5hbWUuXCIpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHNBdHRyaWJOYW1lU2F3V2hpdGUoKSB7XG4gICAgICAgIGNvbnN0IGMgPSB0aGlzLnNraXBTcGFjZXMoKTtcbiAgICAgICAgc3dpdGNoIChjKSB7XG4gICAgICAgICAgICBjYXNlIEVPQzpcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICBjYXNlIEVRVUFMOlxuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0FUVFJJQl9WQUxVRTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwiYXR0cmlidXRlIHdpdGhvdXQgdmFsdWUuXCIpO1xuICAgICAgICAgICAgICAgIC8vIFNob3VsZCB3ZSBkbyB0aGlzPz8/XG4gICAgICAgICAgICAgICAgLy8gdGhpcy50YWcuYXR0cmlidXRlc1t0aGlzLm5hbWVdID0gXCJcIjtcbiAgICAgICAgICAgICAgICB0aGlzLnRleHQgPSBcIlwiO1xuICAgICAgICAgICAgICAgIHRoaXMubmFtZSA9IFwiXCI7XG4gICAgICAgICAgICAgICAgaWYgKGMgPT09IEdSRUFURVIpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5vcGVuVGFnKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGlzTmFtZVN0YXJ0Q2hhcihjKSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnVuZ2V0KCk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0FUVFJJQl9OQU1FO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwiZGlzYWxsb3dlZCBjaGFyYWN0ZXIgaW4gYXR0cmlidXRlIG5hbWUuXCIpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU19BVFRSSUI7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHNBdHRyaWJWYWx1ZSgpIHtcbiAgICAgICAgY29uc3QgYyA9IHRoaXMuZ2V0Q29kZU5vcm0oKTtcbiAgICAgICAgaWYgKGlzUXVvdGUoYykpIHtcbiAgICAgICAgICAgIHRoaXMucSA9IGM7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19BVFRSSUJfVkFMVUVfUVVPVEVEO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKCFpc1MoYykpIHtcbiAgICAgICAgICAgIHRoaXMuZmFpbChcInVucXVvdGVkIGF0dHJpYnV0ZSB2YWx1ZS5cIik7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19BVFRSSUJfVkFMVUVfVU5RVU9URUQ7XG4gICAgICAgICAgICB0aGlzLnVuZ2V0KCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc0F0dHJpYlZhbHVlUXVvdGVkKCkge1xuICAgICAgICAvLyBXZSBkZWxpYmVyYXRlbHkgZG8gbm90IHVzZSBjYXB0dXJlVG8gaGVyZS4gVGhlIHNwZWNpYWxpemVkIGNvZGUgd2UgdXNlXG4gICAgICAgIC8vIGhlcmUgaXMgZmFzdGVyIHRoYW4gdXNpbmcgY2FwdHVyZVRvLlxuICAgICAgICBjb25zdCB7IHEsIGNodW5rIH0gPSB0aGlzO1xuICAgICAgICBsZXQgeyBpOiBzdGFydCB9ID0gdGhpcztcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWNvbnN0YW50LWNvbmRpdGlvblxuICAgICAgICB3aGlsZSAodHJ1ZSkge1xuICAgICAgICAgICAgc3dpdGNoICh0aGlzLmdldENvZGUoKSkge1xuICAgICAgICAgICAgICAgIGNhc2UgcTpcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5wdXNoQXR0cmliKHRoaXMubmFtZSwgdGhpcy50ZXh0ICsgY2h1bmsuc2xpY2Uoc3RhcnQsIHRoaXMucHJldkkpKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5uYW1lID0gdGhpcy50ZXh0ID0gXCJcIjtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5xID0gbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfQVRUUklCX1ZBTFVFX0NMT1NFRDtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIGNhc2UgQU1QOlxuICAgICAgICAgICAgICAgICAgICB0aGlzLnRleHQgKz0gY2h1bmsuc2xpY2Uoc3RhcnQsIHRoaXMucHJldkkpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU19FTlRJVFk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZW50aXR5UmV0dXJuU3RhdGUgPSBTX0FUVFJJQl9WQUxVRV9RVU9URUQ7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICBjYXNlIE5MOlxuICAgICAgICAgICAgICAgIGNhc2UgTkxfTElLRTpcbiAgICAgICAgICAgICAgICBjYXNlIFRBQjpcbiAgICAgICAgICAgICAgICAgICAgdGhpcy50ZXh0ICs9IGAke2NodW5rLnNsaWNlKHN0YXJ0LCB0aGlzLnByZXZJKX0gYDtcbiAgICAgICAgICAgICAgICAgICAgc3RhcnQgPSB0aGlzLmk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgTEVTUzpcbiAgICAgICAgICAgICAgICAgICAgdGhpcy50ZXh0ICs9IGNodW5rLnNsaWNlKHN0YXJ0LCB0aGlzLnByZXZJKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwiZGlzYWxsb3dlZCBjaGFyYWN0ZXIuXCIpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgY2FzZSBFT0M6XG4gICAgICAgICAgICAgICAgICAgIHRoaXMudGV4dCArPSBjaHVuay5zbGljZShzdGFydCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHNBdHRyaWJWYWx1ZUNsb3NlZCgpIHtcbiAgICAgICAgY29uc3QgYyA9IHRoaXMuZ2V0Q29kZU5vcm0oKTtcbiAgICAgICAgaWYgKGlzUyhjKSkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfQVRUUklCO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGMgPT09IEdSRUFURVIpIHtcbiAgICAgICAgICAgIHRoaXMub3BlblRhZygpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGMgPT09IEZPUldBUkRfU0xBU0gpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX09QRU5fVEFHX1NMQVNIO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGlzTmFtZVN0YXJ0Q2hhcihjKSkge1xuICAgICAgICAgICAgdGhpcy5mYWlsKFwibm8gd2hpdGVzcGFjZSBiZXR3ZWVuIGF0dHJpYnV0ZXMuXCIpO1xuICAgICAgICAgICAgdGhpcy51bmdldCgpO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfQVRUUklCX05BTUU7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmZhaWwoXCJkaXNhbGxvd2VkIGNoYXJhY3RlciBpbiBhdHRyaWJ1dGUgbmFtZS5cIik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc0F0dHJpYlZhbHVlVW5xdW90ZWQoKSB7XG4gICAgICAgIC8vIFdlIGRvbid0IGRvIGFueXRoaW5nIHJlZ2FyZGluZyBFT0wgb3Igc3BhY2UgaGFuZGxpbmcgZm9yIHVucXVvdGVkXG4gICAgICAgIC8vIGF0dHJpYnV0ZXMuIFdlIGFscmVhZHkgaGF2ZSBmYWlsZWQgYnkgdGhlIHRpbWUgd2UgZ2V0IGhlcmUsIGFuZCB0aGVcbiAgICAgICAgLy8gY29udHJhY3QgdGhhdCBzYXhlcyB1cGhvbGRzIHN0YXRlcyB0aGF0IHVwb24gZmFpbHVyZSwgaXQgaXMgbm90IHNhZmUgdG9cbiAgICAgICAgLy8gcmVseSBvbiB0aGUgZGF0YSBwYXNzZWQgdG8gZXZlbnQgaGFuZGxlcnMgKG90aGVyIHRoYW5cbiAgICAgICAgLy8gYGBvbmVycm9yYGApLiBQYXNzaW5nIFwiYmFkXCIgZGF0YSBpcyBub3QgYSBwcm9ibGVtLlxuICAgICAgICBjb25zdCBjID0gdGhpcy5jYXB0dXJlVG8oQVRUUklCX1ZBTFVFX1VOUVVPVEVEX1RFUk1JTkFUT1IpO1xuICAgICAgICBzd2l0Y2ggKGMpIHtcbiAgICAgICAgICAgIGNhc2UgQU1QOlxuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0VOVElUWTtcbiAgICAgICAgICAgICAgICB0aGlzLmVudGl0eVJldHVyblN0YXRlID0gU19BVFRSSUJfVkFMVUVfVU5RVU9URUQ7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBjYXNlIExFU1M6XG4gICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwiZGlzYWxsb3dlZCBjaGFyYWN0ZXIuXCIpO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSBFT0M6XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIGlmICh0aGlzLnRleHQuaW5jbHVkZXMoXCJdXT5cIikpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwidGhlIHN0cmluZyBcXFwiXV0+XFxcIiBpcyBkaXNhbGxvd2VkIGluIGNoYXIgZGF0YS5cIik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMucHVzaEF0dHJpYih0aGlzLm5hbWUsIHRoaXMudGV4dCk7XG4gICAgICAgICAgICAgICAgdGhpcy5uYW1lID0gdGhpcy50ZXh0ID0gXCJcIjtcbiAgICAgICAgICAgICAgICBpZiAoYyA9PT0gR1JFQVRFUikge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLm9wZW5UYWcoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0FUVFJJQjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgc0Nsb3NlVGFnKCkge1xuICAgICAgICBjb25zdCBjID0gdGhpcy5jYXB0dXJlTmFtZUNoYXJzKCk7XG4gICAgICAgIGlmIChjID09PSBHUkVBVEVSKSB7XG4gICAgICAgICAgICB0aGlzLmNsb3NlVGFnKCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoaXNTKGMpKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU19DTE9TRV9UQUdfU0FXX1dISVRFO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGMgIT09IEVPQykge1xuICAgICAgICAgICAgdGhpcy5mYWlsKFwiZGlzYWxsb3dlZCBjaGFyYWN0ZXIgaW4gY2xvc2luZyB0YWcuXCIpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHNDbG9zZVRhZ1Nhd1doaXRlKCkge1xuICAgICAgICBzd2l0Y2ggKHRoaXMuc2tpcFNwYWNlcygpKSB7XG4gICAgICAgICAgICBjYXNlIEdSRUFURVI6XG4gICAgICAgICAgICAgICAgdGhpcy5jbG9zZVRhZygpO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSBFT0M6XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIHRoaXMuZmFpbChcImRpc2FsbG93ZWQgY2hhcmFjdGVyIGluIGNsb3NpbmcgdGFnLlwiKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvLyBFTkQgT0YgU1RBVEUgRU5HSU5FIE1FVEhPRFNcbiAgICBoYW5kbGVUZXh0SW5Sb290KCkge1xuICAgICAgICAvLyBUaGlzIGlzIGVzc2VudGlhbGx5IGEgc3BlY2lhbGl6ZWQgdmVyc2lvbiBvZiBjYXB0dXJlVG8gd2hpY2ggaXMgb3B0aW1pemVkXG4gICAgICAgIC8vIGZvciBwZXJmb3JtaW5nIHRoZSBdXT4gY2hlY2suIEEgcHJldmlvdXMgdmVyc2lvbiBvZiB0aGlzIGNvZGUsIGNoZWNrZWRcbiAgICAgICAgLy8gYGB0aGlzLnRleHRgYCBmb3IgdGhlIHByZXNlbmNlIG9mIF1dPi4gSXQgc2ltcGxpZmllZCB0aGUgY29kZSBidXQgd2FzXG4gICAgICAgIC8vIHZlcnkgY29zdGx5IHdoZW4gY2hhcmFjdGVyIGRhdGEgY29udGFpbmVkIGEgbG90IG9mIGVudGl0aWVzIHRvIGJlIHBhcnNlZC5cbiAgICAgICAgLy9cbiAgICAgICAgLy8gU2luY2Ugd2UgYXJlIHVzaW5nIGEgc3BlY2lhbGl6ZWQgbG9vcCwgd2UgYWxzbyBrZWVwIHRyYWNrIG9mIHRoZSBwcmVzZW5jZVxuICAgICAgICAvLyBvZiBdXT4gaW4gdGV4dCBkYXRhLiBUaGUgc2VxdWVuY2UgXV0+IGlzIGZvcmJpZGRlbiB0byBhcHBlYXIgYXMtaXMuXG4gICAgICAgIC8vXG4gICAgICAgIGxldCB7IGk6IHN0YXJ0LCBmb3JiaWRkZW5TdGF0ZSB9ID0gdGhpcztcbiAgICAgICAgY29uc3QgeyBjaHVuaywgdGV4dEhhbmRsZXI6IGhhbmRsZXIgfSA9IHRoaXM7XG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1sYWJlbHMsIG5vLXJlc3RyaWN0ZWQtc3ludGF4XG4gICAgICAgIHNjYW5Mb29wOiBcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWNvbnN0YW50LWNvbmRpdGlvblxuICAgICAgICB3aGlsZSAodHJ1ZSkge1xuICAgICAgICAgICAgc3dpdGNoICh0aGlzLmdldENvZGUoKSkge1xuICAgICAgICAgICAgICAgIGNhc2UgTEVTUzoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU19PUEVOX1dBS0E7XG4gICAgICAgICAgICAgICAgICAgIGlmIChoYW5kbGVyICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgdGV4dCB9ID0gdGhpcztcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHNsaWNlID0gY2h1bmsuc2xpY2Uoc3RhcnQsIHRoaXMucHJldkkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHRleHQubGVuZ3RoICE9PSAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlcih0ZXh0ICsgc2xpY2UpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudGV4dCA9IFwiXCI7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlIGlmIChzbGljZS5sZW5ndGggIT09IDApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVyKHNsaWNlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBmb3JiaWRkZW5TdGF0ZSA9IEZPUkJJRERFTl9TVEFSVDtcbiAgICAgICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWxhYmVsc1xuICAgICAgICAgICAgICAgICAgICBicmVhayBzY2FuTG9vcDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBBTVA6XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0VOVElUWTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lbnRpdHlSZXR1cm5TdGF0ZSA9IFNfVEVYVDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGhhbmRsZXIgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy50ZXh0ICs9IGNodW5rLnNsaWNlKHN0YXJ0LCB0aGlzLnByZXZJKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBmb3JiaWRkZW5TdGF0ZSA9IEZPUkJJRERFTl9TVEFSVDtcbiAgICAgICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWxhYmVsc1xuICAgICAgICAgICAgICAgICAgICBicmVhayBzY2FuTG9vcDtcbiAgICAgICAgICAgICAgICBjYXNlIENMT1NFX0JSQUNLRVQ6XG4gICAgICAgICAgICAgICAgICAgIHN3aXRjaCAoZm9yYmlkZGVuU3RhdGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgRk9SQklEREVOX1NUQVJUOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvcmJpZGRlblN0YXRlID0gRk9SQklEREVOX0JSQUNLRVQ7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYXNlIEZPUkJJRERFTl9CUkFDS0VUOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvcmJpZGRlblN0YXRlID0gRk9SQklEREVOX0JSQUNLRVRfQlJBQ0tFVDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgRk9SQklEREVOX0JSQUNLRVRfQlJBQ0tFVDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiaW1wb3NzaWJsZSBzdGF0ZVwiKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBjYXNlIEdSRUFURVI6XG4gICAgICAgICAgICAgICAgICAgIGlmIChmb3JiaWRkZW5TdGF0ZSA9PT0gRk9SQklEREVOX0JSQUNLRVRfQlJBQ0tFVCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5mYWlsKFwidGhlIHN0cmluZyBcXFwiXV0+XFxcIiBpcyBkaXNhbGxvd2VkIGluIGNoYXIgZGF0YS5cIik7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZm9yYmlkZGVuU3RhdGUgPSBGT1JCSURERU5fU1RBUlQ7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgTkxfTElLRTpcbiAgICAgICAgICAgICAgICAgICAgaWYgKGhhbmRsZXIgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy50ZXh0ICs9IGAke2NodW5rLnNsaWNlKHN0YXJ0LCB0aGlzLnByZXZJKX1cXG5gO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHN0YXJ0ID0gdGhpcy5pO1xuICAgICAgICAgICAgICAgICAgICBmb3JiaWRkZW5TdGF0ZSA9IEZPUkJJRERFTl9TVEFSVDtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgY2FzZSBFT0M6XG4gICAgICAgICAgICAgICAgICAgIGlmIChoYW5kbGVyICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudGV4dCArPSBjaHVuay5zbGljZShzdGFydCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWxhYmVsc1xuICAgICAgICAgICAgICAgICAgICBicmVhayBzY2FuTG9vcDtcbiAgICAgICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgICAgICBmb3JiaWRkZW5TdGF0ZSA9IEZPUkJJRERFTl9TVEFSVDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aGlzLmZvcmJpZGRlblN0YXRlID0gZm9yYmlkZGVuU3RhdGU7XG4gICAgfVxuICAgIGhhbmRsZVRleHRPdXRzaWRlUm9vdCgpIHtcbiAgICAgICAgLy8gVGhpcyBpcyBlc3NlbnRpYWxseSBhIHNwZWNpYWxpemVkIHZlcnNpb24gb2YgY2FwdHVyZVRvIHdoaWNoIGlzIG9wdGltaXplZFxuICAgICAgICAvLyBmb3IgYSBzcGVjaWFsaXplZCB0YXNrLiBXZSBrZWVwIHRyYWNrIG9mIHRoZSBwcmVzZW5jZSBvZiBub24tc3BhY2VcbiAgICAgICAgLy8gY2hhcmFjdGVycyBpbiB0aGUgdGV4dCBzaW5jZSB0aGVzZSBhcmUgZXJyb3JzIHdoZW4gYXBwZWFyaW5nIG91dHNpZGUgdGhlXG4gICAgICAgIC8vIGRvY3VtZW50IHJvb3QgZWxlbWVudC5cbiAgICAgICAgbGV0IHsgaTogc3RhcnQgfSA9IHRoaXM7XG4gICAgICAgIGNvbnN0IHsgY2h1bmssIHRleHRIYW5kbGVyOiBoYW5kbGVyIH0gPSB0aGlzO1xuICAgICAgICBsZXQgbm9uU3BhY2UgPSBmYWxzZTtcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWxhYmVscywgbm8tcmVzdHJpY3RlZC1zeW50YXhcbiAgICAgICAgb3V0Um9vdExvb3A6IFxuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tY29uc3RhbnQtY29uZGl0aW9uXG4gICAgICAgIHdoaWxlICh0cnVlKSB7XG4gICAgICAgICAgICBjb25zdCBjb2RlID0gdGhpcy5nZXRDb2RlKCk7XG4gICAgICAgICAgICBzd2l0Y2ggKGNvZGUpIHtcbiAgICAgICAgICAgICAgICBjYXNlIExFU1M6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFNfT1BFTl9XQUtBO1xuICAgICAgICAgICAgICAgICAgICBpZiAoaGFuZGxlciAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB7IHRleHQgfSA9IHRoaXM7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzbGljZSA9IGNodW5rLnNsaWNlKHN0YXJ0LCB0aGlzLnByZXZJKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICh0ZXh0Lmxlbmd0aCAhPT0gMCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZXIodGV4dCArIHNsaWNlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnRleHQgPSBcIlwiO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAoc2xpY2UubGVuZ3RoICE9PSAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlcihzbGljZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWxhYmVsc1xuICAgICAgICAgICAgICAgICAgICBicmVhayBvdXRSb290TG9vcDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBBTVA6XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTX0VOVElUWTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lbnRpdHlSZXR1cm5TdGF0ZSA9IFNfVEVYVDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGhhbmRsZXIgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy50ZXh0ICs9IGNodW5rLnNsaWNlKHN0YXJ0LCB0aGlzLnByZXZJKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBub25TcGFjZSA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1sYWJlbHNcbiAgICAgICAgICAgICAgICAgICAgYnJlYWsgb3V0Um9vdExvb3A7XG4gICAgICAgICAgICAgICAgY2FzZSBOTF9MSUtFOlxuICAgICAgICAgICAgICAgICAgICBpZiAoaGFuZGxlciAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnRleHQgKz0gYCR7Y2h1bmsuc2xpY2Uoc3RhcnQsIHRoaXMucHJldkkpfVxcbmA7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgc3RhcnQgPSB0aGlzLmk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIGNhc2UgRU9DOlxuICAgICAgICAgICAgICAgICAgICBpZiAoaGFuZGxlciAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnRleHQgKz0gY2h1bmsuc2xpY2Uoc3RhcnQpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1sYWJlbHNcbiAgICAgICAgICAgICAgICAgICAgYnJlYWsgb3V0Um9vdExvb3A7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFpc1MoY29kZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5vblNwYWNlID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmICghbm9uU3BhY2UpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICAvLyBXZSB1c2UgdGhlIHJlcG9ydGVkVGV4dEJlZm9yZVJvb3QgYW5kIHJlcG9ydGVkVGV4dEFmdGVyUm9vdCBmbGFnc1xuICAgICAgICAvLyB0byBhdm9pZCByZXBvcnRpbmcgZXJyb3JzIGZvciBldmVyeSBzaW5nbGUgY2hhcmFjdGVyIHRoYXQgaXMgb3V0IG9mXG4gICAgICAgIC8vIHBsYWNlLlxuICAgICAgICBpZiAoIXRoaXMuc2F3Um9vdCAmJiAhdGhpcy5yZXBvcnRlZFRleHRCZWZvcmVSb290KSB7XG4gICAgICAgICAgICB0aGlzLmZhaWwoXCJ0ZXh0IGRhdGEgb3V0c2lkZSBvZiByb290IG5vZGUuXCIpO1xuICAgICAgICAgICAgdGhpcy5yZXBvcnRlZFRleHRCZWZvcmVSb290ID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5jbG9zZWRSb290ICYmICF0aGlzLnJlcG9ydGVkVGV4dEFmdGVyUm9vdCkge1xuICAgICAgICAgICAgdGhpcy5mYWlsKFwidGV4dCBkYXRhIG91dHNpZGUgb2Ygcm9vdCBub2RlLlwiKTtcbiAgICAgICAgICAgIHRoaXMucmVwb3J0ZWRUZXh0QWZ0ZXJSb290ID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBwdXNoQXR0cmliTlMobmFtZSwgdmFsdWUpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICBjb25zdCB7IHByZWZpeCwgbG9jYWwgfSA9IHRoaXMucW5hbWUobmFtZSk7XG4gICAgICAgIGNvbnN0IGF0dHIgPSB7IG5hbWUsIHByZWZpeCwgbG9jYWwsIHZhbHVlIH07XG4gICAgICAgIHRoaXMuYXR0cmliTGlzdC5wdXNoKGF0dHIpO1xuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLWV4cHJlc3Npb25zXG4gICAgICAgIChfYSA9IHRoaXMuYXR0cmlidXRlSGFuZGxlcikgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNhbGwodGhpcywgYXR0cik7XG4gICAgICAgIGlmIChwcmVmaXggPT09IFwieG1sbnNcIikge1xuICAgICAgICAgICAgY29uc3QgdHJpbW1lZCA9IHZhbHVlLnRyaW0oKTtcbiAgICAgICAgICAgIGlmICh0aGlzLmN1cnJlbnRYTUxWZXJzaW9uID09PSBcIjEuMFwiICYmIHRyaW1tZWQgPT09IFwiXCIpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmZhaWwoXCJpbnZhbGlkIGF0dGVtcHQgdG8gdW5kZWZpbmUgcHJlZml4IGluIFhNTCAxLjBcIik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLnRvcE5TW2xvY2FsXSA9IHRyaW1tZWQ7XG4gICAgICAgICAgICBuc1BhaXJDaGVjayh0aGlzLCBsb2NhbCwgdHJpbW1lZCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAobmFtZSA9PT0gXCJ4bWxuc1wiKSB7XG4gICAgICAgICAgICBjb25zdCB0cmltbWVkID0gdmFsdWUudHJpbSgpO1xuICAgICAgICAgICAgdGhpcy50b3BOU1tcIlwiXSA9IHRyaW1tZWQ7XG4gICAgICAgICAgICBuc1BhaXJDaGVjayh0aGlzLCBcIlwiLCB0cmltbWVkKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBwdXNoQXR0cmliUGxhaW4obmFtZSwgdmFsdWUpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICBjb25zdCBhdHRyID0geyBuYW1lLCB2YWx1ZSB9O1xuICAgICAgICB0aGlzLmF0dHJpYkxpc3QucHVzaChhdHRyKTtcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVudXNlZC1leHByZXNzaW9uc1xuICAgICAgICAoX2EgPSB0aGlzLmF0dHJpYnV0ZUhhbmRsZXIpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYWxsKHRoaXMsIGF0dHIpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBFbmQgcGFyc2luZy4gVGhpcyBwZXJmb3JtcyBmaW5hbCB3ZWxsLWZvcm1lZG5lc3MgY2hlY2tzIGFuZCByZXNldHMgdGhlXG4gICAgICogcGFyc2VyIHRvIGEgY2xlYW4gc3RhdGUuXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyB0aGlzXG4gICAgICovXG4gICAgZW5kKCkge1xuICAgICAgICB2YXIgX2EsIF9iO1xuICAgICAgICBpZiAoIXRoaXMuc2F3Um9vdCkge1xuICAgICAgICAgICAgdGhpcy5mYWlsKFwiZG9jdW1lbnQgbXVzdCBjb250YWluIGEgcm9vdCBlbGVtZW50LlwiKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB7IHRhZ3MgfSA9IHRoaXM7XG4gICAgICAgIHdoaWxlICh0YWdzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGNvbnN0IHRhZyA9IHRhZ3MucG9wKCk7XG4gICAgICAgICAgICB0aGlzLmZhaWwoYHVuY2xvc2VkIHRhZzogJHt0YWcubmFtZX1gKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoKHRoaXMuc3RhdGUgIT09IFNfQkVHSU4pICYmICh0aGlzLnN0YXRlICE9PSBTX1RFWFQpKSB7XG4gICAgICAgICAgICB0aGlzLmZhaWwoXCJ1bmV4cGVjdGVkIGVuZC5cIik7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgeyB0ZXh0IH0gPSB0aGlzO1xuICAgICAgICBpZiAodGV4dC5sZW5ndGggIT09IDApIHtcbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby11bnVzZWQtZXhwcmVzc2lvbnNcbiAgICAgICAgICAgIChfYSA9IHRoaXMudGV4dEhhbmRsZXIpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYWxsKHRoaXMsIHRleHQpO1xuICAgICAgICAgICAgdGhpcy50ZXh0ID0gXCJcIjtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9jbG9zZWQgPSB0cnVlO1xuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLWV4cHJlc3Npb25zXG4gICAgICAgIChfYiA9IHRoaXMuZW5kSGFuZGxlcikgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmNhbGwodGhpcyk7XG4gICAgICAgIHRoaXMuX2luaXQoKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlc29sdmUgYSBuYW1lc3BhY2UgcHJlZml4LlxuICAgICAqXG4gICAgICogQHBhcmFtIHByZWZpeCBUaGUgcHJlZml4IHRvIHJlc29sdmUuXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyBUaGUgbmFtZXNwYWNlIFVSSSBvciBgYHVuZGVmaW5lZGBgIGlmIHRoZSBwcmVmaXggaXMgbm90IGRlZmluZWQuXG4gICAgICovXG4gICAgcmVzb2x2ZShwcmVmaXgpIHtcbiAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgbGV0IHVyaSA9IHRoaXMudG9wTlNbcHJlZml4XTtcbiAgICAgICAgaWYgKHVyaSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICByZXR1cm4gdXJpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHsgdGFncyB9ID0gdGhpcztcbiAgICAgICAgZm9yIChsZXQgaW5kZXggPSB0YWdzLmxlbmd0aCAtIDE7IGluZGV4ID49IDA7IGluZGV4LS0pIHtcbiAgICAgICAgICAgIHVyaSA9IHRhZ3NbaW5kZXhdLm5zW3ByZWZpeF07XG4gICAgICAgICAgICBpZiAodXJpICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdXJpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHVyaSA9IHRoaXMubnNbcHJlZml4XTtcbiAgICAgICAgaWYgKHVyaSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICByZXR1cm4gdXJpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiAoX2IgPSAoX2EgPSB0aGlzLm9wdCkucmVzb2x2ZVByZWZpeCkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmNhbGwoX2EsIHByZWZpeCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFBhcnNlIGEgcW5hbWUgaW50byBpdHMgcHJlZml4IGFuZCBsb2NhbCBuYW1lIHBhcnRzLlxuICAgICAqXG4gICAgICogQHBhcmFtIG5hbWUgVGhlIG5hbWUgdG8gcGFyc2VcbiAgICAgKlxuICAgICAqIEByZXR1cm5zXG4gICAgICovXG4gICAgcW5hbWUobmFtZSkge1xuICAgICAgICAvLyBUaGlzIGlzIGZhc3RlciB0aGFuIHVzaW5nIG5hbWUuc3BsaXQoXCI6XCIpLlxuICAgICAgICBjb25zdCBjb2xvbiA9IG5hbWUuaW5kZXhPZihcIjpcIik7XG4gICAgICAgIGlmIChjb2xvbiA9PT0gLTEpIHtcbiAgICAgICAgICAgIHJldHVybiB7IHByZWZpeDogXCJcIiwgbG9jYWw6IG5hbWUgfTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBsb2NhbCA9IG5hbWUuc2xpY2UoY29sb24gKyAxKTtcbiAgICAgICAgY29uc3QgcHJlZml4ID0gbmFtZS5zbGljZSgwLCBjb2xvbik7XG4gICAgICAgIGlmIChwcmVmaXggPT09IFwiXCIgfHwgbG9jYWwgPT09IFwiXCIgfHwgbG9jYWwuaW5jbHVkZXMoXCI6XCIpKSB7XG4gICAgICAgICAgICB0aGlzLmZhaWwoYG1hbGZvcm1lZCBuYW1lOiAke25hbWV9LmApO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB7IHByZWZpeCwgbG9jYWwgfTtcbiAgICB9XG4gICAgcHJvY2Vzc0F0dHJpYnNOUygpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICBjb25zdCB7IGF0dHJpYkxpc3QgfSA9IHRoaXM7XG4gICAgICAgIGNvbnN0IHRhZyA9IHRoaXMudGFnO1xuICAgICAgICB7XG4gICAgICAgICAgICAvLyBhZGQgbmFtZXNwYWNlIGluZm8gdG8gdGFnXG4gICAgICAgICAgICBjb25zdCB7IHByZWZpeCwgbG9jYWwgfSA9IHRoaXMucW5hbWUodGFnLm5hbWUpO1xuICAgICAgICAgICAgdGFnLnByZWZpeCA9IHByZWZpeDtcbiAgICAgICAgICAgIHRhZy5sb2NhbCA9IGxvY2FsO1xuICAgICAgICAgICAgY29uc3QgdXJpID0gdGFnLnVyaSA9IChfYSA9IHRoaXMucmVzb2x2ZShwcmVmaXgpKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBcIlwiO1xuICAgICAgICAgICAgaWYgKHByZWZpeCAhPT0gXCJcIikge1xuICAgICAgICAgICAgICAgIGlmIChwcmVmaXggPT09IFwieG1sbnNcIikge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmZhaWwoXCJ0YWdzIG1heSBub3QgaGF2ZSBcXFwieG1sbnNcXFwiIGFzIHByZWZpeC5cIik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICh1cmkgPT09IFwiXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mYWlsKGB1bmJvdW5kIG5hbWVzcGFjZSBwcmVmaXg6ICR7SlNPTi5zdHJpbmdpZnkocHJlZml4KX0uYCk7XG4gICAgICAgICAgICAgICAgICAgIHRhZy51cmkgPSBwcmVmaXg7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChhdHRyaWJMaXN0Lmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHsgYXR0cmlidXRlcyB9ID0gdGFnO1xuICAgICAgICBjb25zdCBzZWVuID0gbmV3IFNldCgpO1xuICAgICAgICAvLyBOb3RlOiBkbyBub3QgYXBwbHkgZGVmYXVsdCBucyB0byBhdHRyaWJ1dGVzOlxuICAgICAgICAvLyAgIGh0dHA6Ly93d3cudzMub3JnL1RSL1JFQy14bWwtbmFtZXMvI2RlZmF1bHRpbmdcbiAgICAgICAgZm9yIChjb25zdCBhdHRyIG9mIGF0dHJpYkxpc3QpIHtcbiAgICAgICAgICAgIGNvbnN0IHsgbmFtZSwgcHJlZml4LCBsb2NhbCB9ID0gYXR0cjtcbiAgICAgICAgICAgIGxldCB1cmk7XG4gICAgICAgICAgICBsZXQgZXFuYW1lO1xuICAgICAgICAgICAgaWYgKHByZWZpeCA9PT0gXCJcIikge1xuICAgICAgICAgICAgICAgIHVyaSA9IG5hbWUgPT09IFwieG1sbnNcIiA/IFhNTE5TX05BTUVTUEFDRSA6IFwiXCI7XG4gICAgICAgICAgICAgICAgZXFuYW1lID0gbmFtZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHVyaSA9IHRoaXMucmVzb2x2ZShwcmVmaXgpO1xuICAgICAgICAgICAgICAgIC8vIGlmIHRoZXJlJ3MgYW55IGF0dHJpYnV0ZXMgd2l0aCBhbiB1bmRlZmluZWQgbmFtZXNwYWNlLFxuICAgICAgICAgICAgICAgIC8vIHRoZW4gZmFpbCBvbiB0aGVtIG5vdy5cbiAgICAgICAgICAgICAgICBpZiAodXJpID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mYWlsKGB1bmJvdW5kIG5hbWVzcGFjZSBwcmVmaXg6ICR7SlNPTi5zdHJpbmdpZnkocHJlZml4KX0uYCk7XG4gICAgICAgICAgICAgICAgICAgIHVyaSA9IHByZWZpeDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZXFuYW1lID0gYHske3VyaX19JHtsb2NhbH1gO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHNlZW4uaGFzKGVxbmFtZSkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmZhaWwoYGR1cGxpY2F0ZSBhdHRyaWJ1dGU6ICR7ZXFuYW1lfS5gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHNlZW4uYWRkKGVxbmFtZSk7XG4gICAgICAgICAgICBhdHRyLnVyaSA9IHVyaTtcbiAgICAgICAgICAgIGF0dHJpYnV0ZXNbbmFtZV0gPSBhdHRyO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuYXR0cmliTGlzdCA9IFtdO1xuICAgIH1cbiAgICBwcm9jZXNzQXR0cmlic1BsYWluKCkge1xuICAgICAgICBjb25zdCB7IGF0dHJpYkxpc3QgfSA9IHRoaXM7XG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBwcmVmZXItZGVzdHJ1Y3R1cmluZ1xuICAgICAgICBjb25zdCBhdHRyaWJ1dGVzID0gdGhpcy50YWcuYXR0cmlidXRlcztcbiAgICAgICAgZm9yIChjb25zdCB7IG5hbWUsIHZhbHVlIH0gb2YgYXR0cmliTGlzdCkge1xuICAgICAgICAgICAgaWYgKGF0dHJpYnV0ZXNbbmFtZV0gIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgIHRoaXMuZmFpbChgZHVwbGljYXRlIGF0dHJpYnV0ZTogJHtuYW1lfS5gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGF0dHJpYnV0ZXNbbmFtZV0gPSB2YWx1ZTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmF0dHJpYkxpc3QgPSBbXTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogSGFuZGxlIGEgY29tcGxldGUgb3BlbiB0YWcuIFRoaXMgcGFyc2VyIGNvZGUgY2FsbHMgdGhpcyBvbmNlIGl0IGhhcyBzZWVuXG4gICAgICogdGhlIHdob2xlIHRhZy4gVGhpcyBtZXRob2QgY2hlY2tzIGZvciB3ZWxsLWZvcm1lbmVzcyBhbmQgdGhlbiBlbWl0c1xuICAgICAqIGBgb25vcGVudGFnYGAuXG4gICAgICovXG4gICAgb3BlblRhZygpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICB0aGlzLnByb2Nlc3NBdHRyaWJzKCk7XG4gICAgICAgIGNvbnN0IHsgdGFncyB9ID0gdGhpcztcbiAgICAgICAgY29uc3QgdGFnID0gdGhpcy50YWc7XG4gICAgICAgIHRhZy5pc1NlbGZDbG9zaW5nID0gZmFsc2U7XG4gICAgICAgIC8vIFRoZXJlIGNhbm5vdCBiZSBhbnkgcGVuZGluZyB0ZXh0IGhlcmUgZHVlIHRvIHRoZSBvbm9wZW50YWdzdGFydCB0aGF0IHdhc1xuICAgICAgICAvLyBuZWNlc3NhcmlseSBlbWl0dGVkIGJlZm9yZSB3ZSBnZXQgaGVyZS4gU28gd2UgZG8gbm90IGNoZWNrIHRleHQuXG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby11bnVzZWQtZXhwcmVzc2lvbnNcbiAgICAgICAgKF9hID0gdGhpcy5vcGVuVGFnSGFuZGxlcikgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNhbGwodGhpcywgdGFnKTtcbiAgICAgICAgdGFncy5wdXNoKHRhZyk7XG4gICAgICAgIHRoaXMuc3RhdGUgPSBTX1RFWFQ7XG4gICAgICAgIHRoaXMubmFtZSA9IFwiXCI7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEhhbmRsZSBhIGNvbXBsZXRlIHNlbGYtY2xvc2luZyB0YWcuIFRoaXMgcGFyc2VyIGNvZGUgY2FsbHMgdGhpcyBvbmNlIGl0IGhhc1xuICAgICAqIHNlZW4gdGhlIHdob2xlIHRhZy4gVGhpcyBtZXRob2QgY2hlY2tzIGZvciB3ZWxsLWZvcm1lbmVzcyBhbmQgdGhlbiBlbWl0c1xuICAgICAqIGBgb25vcGVudGFnYGAgYW5kIGBgb25jbG9zZXRhZ2BgLlxuICAgICAqL1xuICAgIG9wZW5TZWxmQ2xvc2luZ1RhZygpIHtcbiAgICAgICAgdmFyIF9hLCBfYiwgX2M7XG4gICAgICAgIHRoaXMucHJvY2Vzc0F0dHJpYnMoKTtcbiAgICAgICAgY29uc3QgeyB0YWdzIH0gPSB0aGlzO1xuICAgICAgICBjb25zdCB0YWcgPSB0aGlzLnRhZztcbiAgICAgICAgdGFnLmlzU2VsZkNsb3NpbmcgPSB0cnVlO1xuICAgICAgICAvLyBUaGVyZSBjYW5ub3QgYmUgYW55IHBlbmRpbmcgdGV4dCBoZXJlIGR1ZSB0byB0aGUgb25vcGVudGFnc3RhcnQgdGhhdCB3YXNcbiAgICAgICAgLy8gbmVjZXNzYXJpbHkgZW1pdHRlZCBiZWZvcmUgd2UgZ2V0IGhlcmUuIFNvIHdlIGRvIG5vdCBjaGVjayB0ZXh0LlxuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLWV4cHJlc3Npb25zXG4gICAgICAgIChfYSA9IHRoaXMub3BlblRhZ0hhbmRsZXIpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYWxsKHRoaXMsIHRhZyk7XG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby11bnVzZWQtZXhwcmVzc2lvbnNcbiAgICAgICAgKF9iID0gdGhpcy5jbG9zZVRhZ0hhbmRsZXIpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5jYWxsKHRoaXMsIHRhZyk7XG4gICAgICAgIGNvbnN0IHRvcCA9IHRoaXMudGFnID0gKF9jID0gdGFnc1t0YWdzLmxlbmd0aCAtIDFdKSAhPT0gbnVsbCAmJiBfYyAhPT0gdm9pZCAwID8gX2MgOiBudWxsO1xuICAgICAgICBpZiAodG9wID09PSBudWxsKSB7XG4gICAgICAgICAgICB0aGlzLmNsb3NlZFJvb3QgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuc3RhdGUgPSBTX1RFWFQ7XG4gICAgICAgIHRoaXMubmFtZSA9IFwiXCI7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEhhbmRsZSBhIGNvbXBsZXRlIGNsb3NlIHRhZy4gVGhpcyBwYXJzZXIgY29kZSBjYWxscyB0aGlzIG9uY2UgaXQgaGFzIHNlZW5cbiAgICAgKiB0aGUgd2hvbGUgdGFnLiBUaGlzIG1ldGhvZCBjaGVja3MgZm9yIHdlbGwtZm9ybWVuZXNzIGFuZCB0aGVuIGVtaXRzXG4gICAgICogYGBvbmNsb3NldGFnYGAuXG4gICAgICovXG4gICAgY2xvc2VUYWcoKSB7XG4gICAgICAgIGNvbnN0IHsgdGFncywgbmFtZSB9ID0gdGhpcztcbiAgICAgICAgLy8gT3VyIHN0YXRlIGFmdGVyIHRoaXMgd2lsbCBiZSBTX1RFWFQsIG5vIG1hdHRlciB3aGF0LCBhbmQgd2UgY2FuIGNsZWFyXG4gICAgICAgIC8vIHRhZ05hbWUgbm93LlxuICAgICAgICB0aGlzLnN0YXRlID0gU19URVhUO1xuICAgICAgICB0aGlzLm5hbWUgPSBcIlwiO1xuICAgICAgICBpZiAobmFtZSA9PT0gXCJcIikge1xuICAgICAgICAgICAgdGhpcy5mYWlsKFwid2VpcmQgZW1wdHkgY2xvc2UgdGFnLlwiKTtcbiAgICAgICAgICAgIHRoaXMudGV4dCArPSBcIjwvPlwiO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGhhbmRsZXIgPSB0aGlzLmNsb3NlVGFnSGFuZGxlcjtcbiAgICAgICAgbGV0IGwgPSB0YWdzLmxlbmd0aDtcbiAgICAgICAgd2hpbGUgKGwtLSA+IDApIHtcbiAgICAgICAgICAgIGNvbnN0IHRhZyA9IHRoaXMudGFnID0gdGFncy5wb3AoKTtcbiAgICAgICAgICAgIHRoaXMudG9wTlMgPSB0YWcubnM7XG4gICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLWV4cHJlc3Npb25zXG4gICAgICAgICAgICBoYW5kbGVyID09PSBudWxsIHx8IGhhbmRsZXIgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGhhbmRsZXIodGFnKTtcbiAgICAgICAgICAgIGlmICh0YWcubmFtZSA9PT0gbmFtZSkge1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5mYWlsKFwidW5leHBlY3RlZCBjbG9zZSB0YWcuXCIpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChsID09PSAwKSB7XG4gICAgICAgICAgICB0aGlzLmNsb3NlZFJvb3QgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGwgPCAwKSB7XG4gICAgICAgICAgICB0aGlzLmZhaWwoYHVubWF0Y2hlZCBjbG9zaW5nIHRhZzogJHtuYW1lfS5gKTtcbiAgICAgICAgICAgIHRoaXMudGV4dCArPSBgPC8ke25hbWV9PmA7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVzb2x2ZXMgYW4gZW50aXR5LiBNYWtlcyBhbnkgbmVjZXNzYXJ5IHdlbGwtZm9ybWVkbmVzcyBjaGVja3MuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gZW50aXR5IFRoZSBlbnRpdHkgdG8gcmVzb2x2ZS5cbiAgICAgKlxuICAgICAqIEByZXR1cm5zIFRoZSBwYXJzZWQgZW50aXR5LlxuICAgICAqL1xuICAgIHBhcnNlRW50aXR5KGVudGl0eSkge1xuICAgICAgICAvLyBzdGFydHNXaXRoIHdvdWxkIGJlIHNpZ25pZmljYW50bHkgc2xvd2VyIGZvciB0aGlzIHRlc3QuXG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvcHJlZmVyLXN0cmluZy1zdGFydHMtZW5kcy13aXRoXG4gICAgICAgIGlmIChlbnRpdHlbMF0gIT09IFwiI1wiKSB7XG4gICAgICAgICAgICBjb25zdCBkZWZpbmVkID0gdGhpcy5FTlRJVElFU1tlbnRpdHldO1xuICAgICAgICAgICAgaWYgKGRlZmluZWQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBkZWZpbmVkO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5mYWlsKHRoaXMuaXNOYW1lKGVudGl0eSkgPyBcInVuZGVmaW5lZCBlbnRpdHkuXCIgOlxuICAgICAgICAgICAgICAgIFwiZGlzYWxsb3dlZCBjaGFyYWN0ZXIgaW4gZW50aXR5IG5hbWUuXCIpO1xuICAgICAgICAgICAgcmV0dXJuIGAmJHtlbnRpdHl9O2A7XG4gICAgICAgIH1cbiAgICAgICAgbGV0IG51bSA9IE5hTjtcbiAgICAgICAgaWYgKGVudGl0eVsxXSA9PT0gXCJ4XCIgJiYgL14jeFswLTlhLWZdKyQvaS50ZXN0KGVudGl0eSkpIHtcbiAgICAgICAgICAgIG51bSA9IHBhcnNlSW50KGVudGl0eS5zbGljZSgyKSwgMTYpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKC9eI1swLTldKyQvLnRlc3QoZW50aXR5KSkge1xuICAgICAgICAgICAgbnVtID0gcGFyc2VJbnQoZW50aXR5LnNsaWNlKDEpLCAxMCk7XG4gICAgICAgIH1cbiAgICAgICAgLy8gVGhlIGNoYXJhY3RlciByZWZlcmVuY2UgaXMgcmVxdWlyZWQgdG8gbWF0Y2ggdGhlIENIQVIgcHJvZHVjdGlvbi5cbiAgICAgICAgaWYgKCF0aGlzLmlzQ2hhcihudW0pKSB7XG4gICAgICAgICAgICB0aGlzLmZhaWwoXCJtYWxmb3JtZWQgY2hhcmFjdGVyIGVudGl0eS5cIik7XG4gICAgICAgICAgICByZXR1cm4gYCYke2VudGl0eX07YDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gU3RyaW5nLmZyb21Db2RlUG9pbnQobnVtKTtcbiAgICB9XG59XG5leHBvcnRzLlNheGVzUGFyc2VyID0gU2F4ZXNQYXJzZXI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zYXhlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/saxes/saxes.js\n");

/***/ })

};
;