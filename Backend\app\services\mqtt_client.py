"""
MQTT Client Service Module

This module provides an MQTT client service for handling real-time chat events
and presence updates in the application. It connects to an EMQX broker, subscribes
to command and presence topics, routes incoming messages to appropriate handlers,
and publishes events to users.

Classes:
- MQTTClientWrapper: Wrapper around aiomqtt Client for publishing events.
- MQTTClientService: Main service for MQTT connectivity and message handling.

Dependencies:
- aiomqtt: Async MQTT client library.
- jose: For JWT token creation.
- SQLAlchemy: For database interactions in presence handling.
"""

import asyncio
import json
import logging
import sys
import uuid
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from aiomqtt import Client
from .. import config
from . import chat_handlers
from ..database import async_session

logger = logging.getLogger(__name__)


class MQTTClientWrapper:
    """Wrapper around aiomqtt Client for simplified event publishing.

    Provides a convenient interface for chat handlers to publish events
    using an existing MQTT client connection.
    """

    def __init__(self, client: Client, mqtt_service):
        """Initialize the wrapper.

        Args:
            client: The aiomqtt Client instance.
            mqtt_service: Reference to the MQTTClientService for publishing.
        """
        self.client = client
        self.mqtt_service = mqtt_service

    async def publish_event(self, user_id: int, event_data: Dict[str, Any]):
        """Publish an event to a specific user.

        Args:
            user_id: Target user ID.
            event_data: Event payload dictionary.
        """
        await self.mqtt_service._publish_event_via_client(
            self.client, user_id, event_data
        )


class MQTTClientService:
    """Main MQTT client service for handling real-time chat and presence.

    Manages connection to EMQX broker, subscribes to topics, processes incoming
    messages, and publishes events. Includes reconnection logic and JWT authentication.
    """

    def __init__(self):
        """Initialize the MQTT service."""
        self.client_id = f"{config.MQTT_CLIENT_ID_PREFIX}-service"
        self.is_connected = False
        self.connection_task: Optional[asyncio.Task] = None
        self.client_config: Optional[Dict[str, Any]] = None
        self._shutdown_event = asyncio.Event()

    async def initialize(self):
        """Initialize the MQTT client and start connection maintenance.

        Creates JWT for authentication, sets up client configuration,
        and starts the background connection task.

        Raises:
            Exception: If initialization fails.
        """
        try:
            broker_jwt = self._create_backend_jwt()

            self.client_config = {
                "hostname": config.MQTT_HOST,
                "port": config.MQTT_PORT,
                "identifier": self.client_id,
                "keepalive": config.MQTT_KEEPALIVE,
                "username": "system",
                "password": broker_jwt,
                "clean_session": True,  # Start fresh session each time
            }

            self.connection_task = asyncio.create_task(self._maintain_connection())
            logger.info(f"MQTT client initialized with ID: {self.client_id}")

        except Exception as e:
            logger.error(f"Failed to initialize MQTT client: {e}")
            raise

    async def _maintain_connection(self):
        """Maintain persistent MQTT connection with automatic reconnection.

        Runs in a loop, connects to broker, subscribes to topics,
        processes messages, and reconnects on errors.
        """
        reconnect_delay = config.MQTT_RECONNECT_DELAY

        while not self._shutdown_event.is_set():
            try:
                logger.info(
                    f"Connecting to MQTT broker at {config.MQTT_HOST}:{config.MQTT_PORT}"
                )

                # Refresh JWT before each (re)connect to avoid using expired tokens
                try:
                    if self.client_config is None:
                        self.client_config = {}
                    self.client_config["password"] = self._create_backend_jwt()
                except Exception as _e:
                    logger.error(f"Failed to refresh MQTT broker JWT: {_e}")

                async with Client(**self.client_config) as client:
                    self.is_connected = True
                    logger.info(
                        f"Connected to MQTT broker at {config.MQTT_HOST}:{config.MQTT_PORT}"
                    )

                    await client.subscribe(f"{config.MQTT_TOPIC_PREFIX}/chat/cmd/+")
                    logger.info("Subscribed to chat command topics")

                    await client.subscribe(f"{config.MQTT_TOPIC_PREFIX}/presence/+")
                    logger.info("Subscribed to presence topics")

                    async for message in client.messages:
                        if self._shutdown_event.is_set():
                            break
                        await self._handle_message(client, message)

            except NotImplementedError as e:
                # Windows-specific event loop issue; retry after delay
                self.is_connected = False
                if not self._shutdown_event.is_set():
                    logger.error(f"Event loop error (Windows compatibility issue): {e}")
                    logger.info(
                        f"Attempting to reconnect in {reconnect_delay} seconds..."
                    )
                    try:
                        await asyncio.wait_for(
                            self._shutdown_event.wait(), timeout=reconnect_delay
                        )
                        break  # Shutdown requested
                    except asyncio.TimeoutError:
                        continue  # Retry connection
            except Exception as e:
                # General connection error; retry after delay
                self.is_connected = False
                if not self._shutdown_event.is_set():
                    logger.error(f"MQTT connection lost: {e}")
                    logger.info(
                        f"Attempting to reconnect in {reconnect_delay} seconds..."
                    )
                    try:
                        await asyncio.wait_for(
                            self._shutdown_event.wait(), timeout=reconnect_delay
                        )
                        break  # Shutdown requested
                    except asyncio.TimeoutError:
                        continue  # Retry connection

        self.is_connected = False

    async def _handle_message(self, client: Client, message):
        """Parse and route incoming MQTT messages based on topic.

        Extracts user_id from topic, deserializes payload, and routes
        to appropriate handler (chat commands or presence updates).

        Args:
            client: MQTT client instance.
            message: Incoming MQTT message.
        """
        try:
            topic_parts = message.topic.value.split('/')
            if len(topic_parts) < 4:
                return  # Invalid topic format

            # Topic format: prefix/chat/cmd/user_id or prefix/presence/user_id
            if topic_parts[-3] == 'chat' and topic_parts[-2] == 'cmd':
                user_id = int(topic_parts[-1])
                payload = json.loads(message.payload.decode())

                await self._route_chat_command(client, user_id, payload)

            elif topic_parts[-2] == 'presence':
                user_id = int(topic_parts[-1])
                payload = json.loads(message.payload.decode())

                await self._handle_presence_message(user_id, payload)

        except Exception as e:
            logger.error(f"Error handling MQTT message: {e}")

    async def _route_chat_command(
        self, client: Client, user_id: int, payload: Dict[str, Any]
    ):
        """Route chat command payloads to appropriate handler functions.

        Based on the 'type' field in payload, calls the corresponding
        chat handler and publishes resulting events.

        Args:
            client: MQTT client instance.
            user_id: User who sent the command.
            payload: Command payload with 'type' field.
        """
        try:
            command = payload.get('type')

            mqtt_client_wrapper = MQTTClientWrapper(client, self)

            if command == 'message':
                events = await chat_handlers.handle_message(
                    user_id, payload, mqtt_client_wrapper
                )
            elif command == 'group_message':
                events = await chat_handlers.handle_group_message(
                    user_id, payload, mqtt_client_wrapper
                )
            elif command == 'typing':
                events = await chat_handlers.handle_typing(
                    user_id, payload, mqtt_client_wrapper
                )
            elif command == 'read_receipt':
                events = await chat_handlers.handle_read_receipt(
                    user_id, payload, mqtt_client_wrapper
                )
            elif command == 'edit_message':
                events = await chat_handlers.handle_edit_message(
                    user_id, payload, mqtt_client_wrapper
                )
            elif command == 'delete_message':
                events = await chat_handlers.handle_delete_message(
                    user_id, payload, mqtt_client_wrapper
                )
            elif command == 'hide_message':
                events = await chat_handlers.handle_hide_message(
                    user_id, payload, mqtt_client_wrapper
                )
            elif command == 'group_edit_message':
                events = await chat_handlers.handle_group_edit_message(
                    user_id, payload, mqtt_client_wrapper
                )
            elif command == 'group_delete_message':
                events = await chat_handlers.handle_group_delete_message(
                    user_id, payload, mqtt_client_wrapper
                )
            elif command == 'group_hide_message':
                events = await chat_handlers.handle_group_hide_message(
                    user_id, payload, mqtt_client_wrapper
                )
            else:
                return

            for event in events or []:
                target_user_id = event.get('target_user_id')
                if target_user_id:
                    await self._publish_event_via_client(client, target_user_id, event)

        except Exception as e:
            logger.error(f"Error routing chat command: {e}")

    async def _handle_presence_message(self, user_id: int, payload: Dict[str, Any]):
        """Handle user presence updates (online/offline status).

        Updates Redis session cache and database UserStatus based on
        presence events from clients.

        Args:
            user_id: User whose presence changed.
            payload: Presence payload with 'status' and optional 'last_seen'.
        """
        try:
            from ..services.websocket_session_cache import ws_session_cache
            from ..models.chat import UserStatus
            from ..models.user import User

            status = payload.get('status', 'offline')
            last_seen = payload.get('last_seen')

            # Update Redis session cache for real-time presence
            if status == 'online':
                session_data = {
                    'user_id': user_id, 'status': 'online', 'last_activity':
                    datetime.utcnow().isoformat(), 'connection_type': 'mqtt'
                }
                await ws_session_cache.save_session(user_id, session_data)

            elif status == 'offline':
                await ws_session_cache.delete_session(user_id)

            # Update persistent UserStatus in database
            async with async_session() as db:
                try:
                    from sqlalchemy import select
                    result = await db.execute(
                        select(UserStatus).where(UserStatus.user_id == user_id)
                    )
                    user_status = result.scalars().first()
                    if not user_status:
                        user_status = UserStatus(
                            user_id=user_id,
                            is_online=(status == 'online'),
                            last_seen=datetime.fromisoformat(last_seen)
                            if last_seen else datetime.utcnow()
                        )
                        db.add(user_status)
                    else:
                        user_status.is_online = (status == 'online')
                        user_status.last_seen = datetime.fromisoformat(
                            last_seen
                        ) if last_seen else datetime.utcnow()

                    await db.commit()

                except Exception as db_error:
                    logger.error(
                        f"Error updating UserStatus for user {user_id}: {db_error}"
                    )
                    await db.rollback()

        except Exception as e:
            logger.error(f"Error handling presence message for user {user_id}: {e}")

    async def _publish_event_via_client(
        self, client: Client, user_id: int, event_data: Dict[str, Any]
    ):
        """Publish an event to a specific user's events topic.

        Args:
            client: MQTT client instance to use for publishing.
            user_id: Target user ID.
            event_data: Event payload to serialize and publish.
        """
        try:
            topic = f"{config.MQTT_TOPIC_PREFIX}/chat/users/{user_id}/events"
            payload = json.dumps(event_data)
            await client.publish(topic, payload, qos=1)
        except Exception as e:
            logger.error(f"Error publishing event to user {user_id}: {e}")

    async def publish_event(self, user_id: int, event_data: Dict[str, Any]):
        """Publish an event to a user by creating a new client connection.

        Used for one-off publishing when not in the main connection loop.

        Args:
            user_id: Target user ID.
            event_data: Event payload dictionary.
        """
        if not self.is_connected:
            logger.warning(f"Cannot publish event - MQTT not connected")
            return

        try:
            # Use a fresh JWT for each ad-hoc publish connection to ensure validity
            fresh_config = dict(self.client_config or {})
            fresh_config["password"] = self._create_backend_jwt()
            async with Client(**fresh_config) as client:
                await self._publish_event_via_client(client, user_id, event_data)
        except Exception as e:
            logger.error(f"Error publishing event to user {user_id}: {e}")

    async def publish_to_group(
        self,
        group_id: int,
        event_data: Dict[str, Any],
        exclude_user_id: Optional[int] = None
    ):
        """Publish an event to all members of a group.

        Queries database for group members and sends event to each,
        optionally excluding one user (e.g., the sender).

        Args:
            group_id: Target group ID.
            event_data: Event payload dictionary.
            exclude_user_id: Optional user ID to exclude from publishing.
        """
        try:
            from ..models.group_chat import GroupMember
            from sqlalchemy import select

            async with async_session() as db:
                result = await db.execute(
                    select(GroupMember.user_id).where(GroupMember.group_id == group_id)
                )
                members = result.scalars().all()
                member_ids = list(members)

            for member_id in member_ids:
                if exclude_user_id and member_id == exclude_user_id:
                    continue
                await self.publish_event(member_id, event_data)

        except Exception as e:
            logger.error(f"Error publishing to group {group_id}: {e}")

    def _create_backend_jwt(self) -> str:
        """Create JWT token for backend service MQTT authentication.

        Generates a JWT with ACL permissions for subscribing to chat commands,
        publishing to user events, and presence topics.

        Returns:
            Encoded JWT string for broker authentication.
        """
        from jose import jwt

        # Define permissions for backend service
        acl = [
            {
                "permission": "allow", "action": "subscribe", "topic":
                f"{config.MQTT_TOPIC_PREFIX}/chat/cmd/+"
            }, {
                "permission": "allow", "action": "publish", "topic":
                f"{config.MQTT_TOPIC_PREFIX}/chat/users/+/events"
            }, {
                "permission": "allow", "action": "publish", "topic":
                f"{config.MQTT_TOPIC_PREFIX}/presence/+"
            },
            {
                "permission": "allow", "action": "subscribe", "topic":
                f"{config.MQTT_TOPIC_PREFIX}/presence/+"
            }
        ]

        payload = {
            "sub":
            "backend-service",
            "user_id":
            0,  # Special ID for backend service
            "username":
            "backend-service",
            "acl":
            acl,  # Access Control List embedded in JWT
            "exp":
            datetime.utcnow() +
            timedelta(minutes=config.MQTT_BROKER_JWT_EXPIRE_MINUTES),
            "iat":
            datetime.utcnow()
        }

        return jwt.encode(
            payload,
            config.MQTT_BROKER_JWT_SECRET,
            algorithm=config.MQTT_BROKER_JWT_ALGORITHM
        )

    async def shutdown(self):
        """Gracefully shutdown the MQTT client service.

        Sets shutdown event, cancels connection task, and waits for completion.
        """
        logger.info("Shutting down MQTT client")
        self._shutdown_event.set()

        if self.connection_task and not self.connection_task.done():
            self.connection_task.cancel()
            try:
                await self.connection_task
            except asyncio.CancelledError:
                pass

        self.is_connected = False
        logger.info("MQTT client shutdown completed")


mqtt_client = MQTTClientService()
