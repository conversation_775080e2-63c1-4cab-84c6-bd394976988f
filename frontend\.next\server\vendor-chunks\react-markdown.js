"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-markdown";
exports.ids = ["vendor-chunks/react-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-markdown/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/react-markdown/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Markdown: () => (/* binding */ Markdown),\n/* harmony export */   MarkdownAsync: () => (/* binding */ MarkdownAsync),\n/* harmony export */   MarkdownHooks: () => (/* binding */ MarkdownHooks),\n/* harmony export */   defaultUrlTransform: () => (/* binding */ defaultUrlTransform)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! hast-util-to-jsx-runtime */ \"(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js\");\n/* harmony import */ var html_url_attributes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! html-url-attributes */ \"(ssr)/./node_modules/html-url-attributes/lib/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var remark_parse__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remark-parse */ \"(ssr)/./node_modules/remark-parse/lib/index.js\");\n/* harmony import */ var remark_rehype__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remark-rehype */ \"(ssr)/./node_modules/remark-rehype/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unified */ \"(ssr)/./node_modules/unified/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/**\n * @import {Element, ElementContent, Nodes, Parents, Root} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {ComponentProps, ElementType, ReactElement} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList, Processor} from 'unified'\n */\n\n/**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */\n\n/**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */\n\n/**\n * @typedef {{\n *   [Key in Extract<ElementType, string>]?: ElementType<ComponentProps<Key> & ExtraProps>\n * }} Components\n *   Map tag names to components.\n */\n\n/**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {string | null | undefined} [className]\n *   Wrap in a `div` with this class name.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */\n\n/**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */\n\n\n\n\n\n\n\n\n\n\n\n\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/** @type {PluggableList} */\nconst emptyPlugins = []\n/** @type {Readonly<RemarkRehypeOptions>} */\nconst emptyRemarkRehypeOptions = {allowDangerousHtml: true}\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i\n\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */\nconst deprecations = [\n  {from: 'astPlugins', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'allowDangerousHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {\n    from: 'allowNode',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowElement'\n  },\n  {\n    from: 'allowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowedElements'\n  },\n  {\n    from: 'disallowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'disallowedElements'\n  },\n  {from: 'escapeHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'includeElementIndex', id: '#remove-includeelementindex'},\n  {\n    from: 'includeNodeIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  },\n  {from: 'linkTarget', id: 'remove-linktarget'},\n  {from: 'plugins', id: 'change-plugins-to-remarkplugins', to: 'remarkPlugins'},\n  {from: 'rawSourcePos', id: '#remove-rawsourcepos'},\n  {from: 'renderers', id: 'change-renderers-to-components', to: 'components'},\n  {from: 'source', id: 'change-source-to-children', to: 'children'},\n  {from: 'sourcePos', id: '#remove-sourcepos'},\n  {from: 'transformImageUri', id: '#add-urltransform', to: 'urlTransform'},\n  {from: 'transformLinkUri', id: '#add-urltransform', to: 'urlTransform'}\n]\n\n/**\n * Component to render markdown.\n *\n * This is a synchronous component.\n * When using async plugins,\n * see {@linkcode MarkdownAsync} or {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction Markdown(options) {\n  const processor = createProcessor(options)\n  const file = createFile(options)\n  return post(processor.runSync(processor.parse(file), file), options)\n}\n\n/**\n * Component to render markdown with support for async plugins\n * through async/await.\n *\n * Components returning promises are supported on the server.\n * For async support on the client,\n * see {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Promise<ReactElement>}\n *   Promise to a React element.\n */\nasync function MarkdownAsync(options) {\n  const processor = createProcessor(options)\n  const file = createFile(options)\n  const tree = await processor.run(processor.parse(file), file)\n  return post(tree, options)\n}\n\n/**\n * Component to render markdown with support for async plugins through hooks.\n *\n * This uses `useEffect` and `useState` hooks.\n * Hooks run on the client and do not immediately render something.\n * For async support on the server,\n * see {@linkcode MarkdownAsync}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction MarkdownHooks(options) {\n  const processor = createProcessor(options)\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\n    /** @type {Error | undefined} */ (undefined)\n  )\n  const [tree, setTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(/** @type {Root | undefined} */ (undefined))\n\n  ;(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(\n    /* c8 ignore next 7 -- hooks are client-only. */\n    function () {\n      const file = createFile(options)\n      processor.run(processor.parse(file), file, function (error, tree) {\n        setError(error)\n        setTree(tree)\n      })\n    },\n    [\n      options.children,\n      options.rehypePlugins,\n      options.remarkPlugins,\n      options.remarkRehypeOptions\n    ]\n  )\n\n  /* c8 ignore next -- hooks are client-only. */\n  if (error) throw error\n\n  /* c8 ignore next -- hooks are client-only. */\n  return tree ? post(tree, options) : (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment)\n}\n\n/**\n * Set up the `unified` processor.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Processor<MdastRoot, MdastRoot, Root, undefined, undefined>}\n *   Result.\n */\nfunction createProcessor(options) {\n  const rehypePlugins = options.rehypePlugins || emptyPlugins\n  const remarkPlugins = options.remarkPlugins || emptyPlugins\n  const remarkRehypeOptions = options.remarkRehypeOptions\n    ? {...options.remarkRehypeOptions, ...emptyRemarkRehypeOptions}\n    : emptyRemarkRehypeOptions\n\n  const processor = (0,unified__WEBPACK_IMPORTED_MODULE_2__.unified)()\n    .use(remark_parse__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n    .use(remarkPlugins)\n    .use(remark_rehype__WEBPACK_IMPORTED_MODULE_4__[\"default\"], remarkRehypeOptions)\n    .use(rehypePlugins)\n\n  return processor\n}\n\n/**\n * Set up the virtual file.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {VFile}\n *   Result.\n */\nfunction createFile(options) {\n  const children = options.children || ''\n  const file = new vfile__WEBPACK_IMPORTED_MODULE_5__.VFile()\n\n  if (typeof children === 'string') {\n    file.value = children\n  } else {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\n      'Unexpected value `' +\n        children +\n        '` for `children` prop, expected `string`'\n    )\n  }\n\n  return file\n}\n\n/**\n * Process the result from unified some more.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction post(tree, options) {\n  const allowedElements = options.allowedElements\n  const allowElement = options.allowElement\n  const components = options.components\n  const disallowedElements = options.disallowedElements\n  const skipHtml = options.skipHtml\n  const unwrapDisallowed = options.unwrapDisallowed\n  const urlTransform = options.urlTransform || defaultUrlTransform\n\n  for (const deprecation of deprecations) {\n    if (Object.hasOwn(options, deprecation.from)) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\n        'Unexpected `' +\n          deprecation.from +\n          '` prop, ' +\n          (deprecation.to\n            ? 'use `' + deprecation.to + '` instead'\n            : 'remove it') +\n          ' (see <' +\n          changelog +\n          '#' +\n          deprecation.id +\n          '> for more info)'\n      )\n    }\n  }\n\n  if (allowedElements && disallowedElements) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\n      'Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other'\n    )\n  }\n\n  // Wrap in `div` if there’s a class name.\n  if (options.className) {\n    tree = {\n      type: 'element',\n      tagName: 'div',\n      properties: {className: options.className},\n      // Assume no doctypes.\n      children: /** @type {Array<ElementContent>} */ (\n        tree.type === 'root' ? tree.children : [tree]\n      )\n    }\n  }\n\n  (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_7__.visit)(tree, transform)\n\n  return (0,hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.toJsxRuntime)(tree, {\n    Fragment: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment,\n    // @ts-expect-error\n    // React components are allowed to return numbers,\n    // but not according to the types in hast-util-to-jsx-runtime\n    components,\n    ignoreInvalidStyle: true,\n    jsx: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx,\n    jsxs: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs,\n    passKeys: true,\n    passNode: true\n  })\n\n  /** @type {BuildVisitor<Root>} */\n  function transform(node, index, parent) {\n    if (node.type === 'raw' && parent && typeof index === 'number') {\n      if (skipHtml) {\n        parent.children.splice(index, 1)\n      } else {\n        parent.children[index] = {type: 'text', value: node.value}\n      }\n\n      return index\n    }\n\n    if (node.type === 'element') {\n      /** @type {string} */\n      let key\n\n      for (key in html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes) {\n        if (\n          Object.hasOwn(html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes, key) &&\n          Object.hasOwn(node.properties, key)\n        ) {\n          const value = node.properties[key]\n          const test = html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes[key]\n          if (test === null || test.includes(node.tagName)) {\n            node.properties[key] = urlTransform(String(value || ''), key, node)\n          }\n        }\n      }\n    }\n\n    if (node.type === 'element') {\n      let remove = allowedElements\n        ? !allowedElements.includes(node.tagName)\n        : disallowedElements\n          ? disallowedElements.includes(node.tagName)\n          : false\n\n      if (!remove && allowElement && typeof index === 'number') {\n        remove = !allowElement(node, index, parent)\n      }\n\n      if (remove && parent && typeof index === 'number') {\n        if (unwrapDisallowed && node.children) {\n          parent.children.splice(index, 1, ...node.children)\n        } else {\n          parent.children.splice(index, 1)\n        }\n\n        return index\n      }\n    }\n  }\n}\n\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */\nfunction defaultUrlTransform(value) {\n  // Same as:\n  // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n  // But without the `encode` part.\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon === -1 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash !== -1 && colon > slash) ||\n    (questionMark !== -1 && colon > questionMark) ||\n    (numberSign !== -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    safeProtocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-markdown/lib/index.js\n");

/***/ })

};
;