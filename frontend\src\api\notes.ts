/**
 * Notes API functions
 */

import { API_BASE_URL, getAuthHeaders } from '@/lib/api';
import { NoteItem, CreateNotePayload, UpdateNotePayload, ListNotesParams } from '@/types/note';

/**
 * Create a new note
 */
export async function createNote(data: CreateNotePayload): Promise<NoteItem> {
  const response = await fetch(`${API_BASE_URL}/notes`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `Failed to create note: ${response.status}`);
  }
  
  return await response.json();
}

/**
 * List all notes with optional filters
 */
export async function listNotes(params: ListNotesParams = {}): Promise<NoteItem[]> {
  const query = new URLSearchParams();
  if (params.search) query.set('search', params.search);
  if (params.page) query.set('page', String(params.page));
  if (params.page_size) query.set('page_size', String(params.page_size));
  
  const url = `${API_BASE_URL}/notes${query.toString() ? `?${query.toString()}` : ''}`;
  const response = await fetch(url, { headers: getAuthHeaders() });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `Failed to fetch notes: ${response.status}`);
  }
  
  return await response.json();
}

/**
 * Get a specific note by ID
 */
export async function getNote(noteId: number): Promise<NoteItem> {
  const response = await fetch(`${API_BASE_URL}/notes/${noteId}`, { 
    headers: getAuthHeaders() 
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `Failed to fetch note: ${response.status}`);
  }
  
  return await response.json();
}

/**
 * Update an existing note
 */
export async function updateNote(noteId: number, data: UpdateNotePayload): Promise<NoteItem> {
  const response = await fetch(`${API_BASE_URL}/notes/${noteId}`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `Failed to update note: ${response.status}`);
  }
  
  return await response.json();
}

/**
 * Delete a note
 */
export async function deleteNote(noteId: number): Promise<{ message: string }> {
  const response = await fetch(`${API_BASE_URL}/notes/${noteId}`, {
    method: 'DELETE',
    headers: getAuthHeaders(),
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || `Failed to delete note: ${response.status}`);
  }
  
  return await response.json();
}
