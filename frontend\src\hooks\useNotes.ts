/**
 * Hook for managing notes operations
 */

import { useState, useCallback } from 'react';
import { 
  createNote as apiCreateNote, 
  listNotes as apiListNotes, 
  updateNote as apiUpdateNote, 
  deleteNote as apiDeleteNote,
  getNote as apiGetNote
} from '@/api/notes';
import { 
  NoteItem, 
  CreateNotePayload, 
  UpdateNotePayload, 
  ListNotesParams 
} from '@/types/note';

export function useNotes() {
  const [notes, setNotes] = useState<NoteItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch all notes
  const fetchNotes = useCallback(async (params: ListNotesParams = {}) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const fetchedNotes = await apiListNotes(params);
      setNotes(fetchedNotes);
      return fetchedNotes;
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to fetch notes';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Create a new note
  const createNote = useCallback(async (data: CreateNotePayload) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const newNote = await apiCreateNote(data);
      setNotes(prev => [newNote, ...prev]);
      return newNote;
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to create note';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get a single note
  const getNote = useCallback(async (noteId: number) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const note = await apiGetNote(noteId);
      return note;
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to fetch note';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update an existing note
  const updateNote = useCallback(async (noteId: number, data: UpdateNotePayload) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const updatedNote = await apiUpdateNote(noteId, data);
      setNotes(prev => prev.map(note => note.id === noteId ? updatedNote : note));
      return updatedNote;
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to update note';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Delete a note
  const deleteNote = useCallback(async (noteId: number) => {
    setIsLoading(true);
    setError(null);
    
    try {
      await apiDeleteNote(noteId);
      setNotes(prev => prev.filter(note => note.id !== noteId));
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to delete note';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    notes,
    isLoading,
    error,
    fetchNotes,
    createNote,
    getNote,
    updateNote,
    deleteNote,
  };
}
