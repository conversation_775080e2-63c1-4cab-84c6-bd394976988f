from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import List, Optional
from ..database import get_db
from ..models.user import User
from ..schemas.user import UserResponse
from ..auth import require_admin
from ..services.redis_service import redis_service, cached, invalidate_cache, invalidate_admin_cache
from .. import config

router = APIRouter(prefix="/account-requests", tags=["Account Requests"])

@router.get("/", response_model=List[UserResponse])
@cached(
    prefix=f"{config.CACHE_PREFIX_ADMIN}:account_requests",
    ttl=config.CACHE_TTL_ADMIN_DASHBOARD
)
async def list_account_requests(
    status_filter: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    List all account requests (admin only)
    Optionally filter by status: 'pending', 'approved', 'rejected'
    """
    stmt = select(User)
    
    # Apply status filter if provided
    if status_filter:
        if status_filter not in ['pending', 'approved', 'rejected']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid status filter. Must be one of: pending, approved, rejected"
            )
        stmt = stmt.where(User.status == status_filter)
    
    stmt = stmt.offset(skip).limit(limit)
    result = await db.execute(stmt)
    users = result.scalars().all()
    return users

    
@router.put("/{user_id}/approve", response_model=UserResponse)
@invalidate_cache(f"{config.CACHE_PREFIX_ADMIN}:*")
async def approve_account(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    Approve a user account (admin only)
    """
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Prevent changing status of admin accounts or self
    if user.role == "admin":
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Cannot modify admin accounts")
    if user.id == current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Cannot modify your own account")
    
    user.status = "approved"
    await db.commit()
    await db.refresh(user)
    
    # Invalidate admin cache
    await invalidate_admin_cache()
    
    return user

@router.put("/{user_id}/reject", response_model=UserResponse)
@invalidate_cache(f"{config.CACHE_PREFIX_ADMIN}:*")
async def reject_account(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    Reject a user account (admin only)
    """
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Prevent changing status of admin accounts or self
    if user.role == "admin":
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Cannot modify admin accounts")
    if user.id == current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Cannot modify your own account")
    
    user.status = "rejected"
    await db.commit()
    await db.refresh(user)
    
    # Invalidate admin cache
    await invalidate_admin_cache()
    
    return user

@router.get("/count", response_model=dict)
@cached(
    prefix=f"{config.CACHE_PREFIX_ADMIN}:account_counts",
    ttl=config.CACHE_TTL_ADMIN_DASHBOARD
)
async def get_account_request_counts(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    Get counts of users by status (admin only)
    """
    # Count users by status
    pending_count = await db.scalar(select(func.count(User.id)).where(User.status == "pending"))
    approved_count = await db.scalar(select(func.count(User.id)).where(User.status == "approved"))
    rejected_count = await db.scalar(select(func.count(User.id)).where(User.status == "rejected"))
    
    return {
        "pending": pending_count,
        "approved": approved_count,
        "rejected": rejected_count,
        "total": pending_count + approved_count + rejected_count
    }
