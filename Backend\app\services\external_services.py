"""
External Services Manager

Handles communication with GPU-based data ingestion and retrieval services.
Uses direct HTTP API calls to separate FastAPI services instead of importing local modules.
Includes direct access to embedding and reranking services.
"""

import os
import json
import logging
import requests
import tempfile
import sys
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from ..config import GEMINI_API_KEY, DATABASE_URL, QDRANT_URL, GRPC_PORT, QDRANT_API_KEY, DATA_INGESTION_SERVICE_URL, DATA_RETRIEVER_SERVICE_URL, EMBEDDING_SERVICE_URL, RERANKING_SERVICE_URL
from .minio_service import minio_service
_ingestion_functions = None
_retrieval_functions = None

ENV = os.getenv("ENVIRONMENT")
def _load_direct_clients():
    """Lazy load direct client functions"""
    global _ingestion_functions, _retrieval_functions
    
    if _ingestion_functions is None or _retrieval_functions is None:
        try:
            import time
            if os.getenv("ENABLE_IMPORT_TIMING", "0") == "1":
                logger.info("IMPORT_TIMING: Starting HTTP-based retriever function setup...")
            retriever_start = time.perf_counter()
            
            # Define HTTP-based functions for data ingestion
            def http_upload_and_process_files(files, username, collection_name, processing_config, gemini_api_key, space_id=None, **kwargs):
                """HTTP-based upload and process files function"""
                try:
                    import requests
                    from io import BytesIO

                    # Validate required parameters
                    if not username or not username.strip():
                        return {
                            "success": False,
                            "error": "Username is required and cannot be empty",
                            "processed_documents": 0,
                            "failed_documents": len(files)
                        }
                    if not collection_name or not collection_name.strip():
                        return {
                            "success": False,
                            "error": "Collection name is required and cannot be empty",
                            "processed_documents": 0,
                            "failed_documents": len(files)
                        }
                    if not gemini_api_key or not gemini_api_key.strip():
                        return {
                            "success": False,
                            "error": "Gemini API key is required and cannot be empty",
                            "processed_documents": 0,
                            "failed_documents": len(files)
                        }

                    # Get the service URL
                    service_url = DATA_INGESTION_SERVICE_URL
                    endpoint_url = f"{service_url}/ingest"

                    # Prepare files for multipart upload
                    files_data = []
                    for i, file_obj in enumerate(files):
                        try:
                            if hasattr(file_obj, 'filename'):
                                # FastAPI UploadFile - read content
                                if hasattr(file_obj, 'file') and file_obj.file:
                                    content = file_obj.file.read()
                                    # Reset file pointer safely
                                    try:
                                        file_obj.file.seek(0)
                                    except (AttributeError, OSError):
                                        pass  # Ignore seek errors
                                elif hasattr(file_obj, 'read'):
                                    content = file_obj.read()
                                    # Reset file pointer safely
                                    try:
                                        if hasattr(file_obj, 'seek'):
                                            file_obj.seek(0)
                                    except (AttributeError, OSError):
                                        pass  # Ignore seek errors
                                else:
                                    continue

                                files_data.append(('files', (file_obj.filename, BytesIO(content), getattr(file_obj, 'content_type', 'application/octet-stream'))))

                            elif isinstance(file_obj, dict) and 'stream' in file_obj:
                                # MinIO stream dict
                                filename = file_obj.get('filename', f'file_{i+1}')
                                stream = file_obj['stream']

                                if hasattr(stream, 'read'):
                                    content = stream.read()
                                    # Reset stream pointer safely
                                    try:
                                        if hasattr(stream, 'seek'):
                                            stream.seek(0)
                                    except (AttributeError, OSError):
                                        pass  # Ignore seek errors
                                else:
                                    content = stream

                                files_data.append(('files', (filename, BytesIO(content), 'application/octet-stream')))
                        except Exception as file_error:
                            logger.error(f"Error processing file {i}: {file_error}")
                            continue

                    if not files_data:
                        return {
                            "success": False,
                            "error": "No valid files provided",
                            "processed_documents": 0,
                            "failed_documents": len(files)
                        }

                    # Prepare form data - ensure all required fields are present
                    form_data = {
                        'username': username,
                        'collection_name': collection_name,
                        'gemini_api_key': gemini_api_key,
                        'qdrant_url': processing_config.get('qdrant_url'),
                        'qdrant_api_key': processing_config.get('qdrant_api_key'),
                        'DATABASE_URL': processing_config.get('DATABASE_URL'),
                        'embedding_api_url': processing_config.get('embedding_api_url'),
                        'chunk_size': processing_config.get('chunk_size'),
                        'chunk_overlap': processing_config.get('chunk_overlap'),
                        'use_custom_splitter': processing_config.get('use_custom_splitter'),
                        'use_unstructured': processing_config.get('use_unstructured'),
                        'space_id': space_id,
                        'environment': ENV  # Pass the backend's ENVIRONMENT variable
                    }

                    # Make HTTP request
                    response = requests.post(
                        endpoint_url,
                        files=files_data,
                        data=form_data,
                        timeout=300  # 5 minutes timeout
                    )

                    response.raise_for_status()
                    return response.json()

                except requests.exceptions.HTTPError as e:
                    logger.error(f"HTTP upload_and_process_files failed with status {e.response.status_code}: {e.response.text}")
                    return {
                        "success": False,
                        "error": f"HTTP {e.response.status_code}: {e.response.text}",
                        "processed_documents": 0,
                        "failed_documents": len(files)
                    }
                except Exception as e:
                    logger.error(f"HTTP upload_and_process_files failed: {e}")
                    return {
                        "success": False,
                        "error": f"Request failed: {str(e)}",
                        "processed_documents": 0,
                        "failed_documents": len(files)
                    }

            def http_ingestion_health_check():
                """HTTP-based health check function"""
                try:
                    import requests
                    service_url = DATA_INGESTION_SERVICE_URL
                    response = requests.get(f"{service_url}/health", timeout=10)
                    response.raise_for_status()
                    return response.json()
                except Exception as e:
                    logger.error(f"HTTP health check failed: {e}")
                    return {"status": "unhealthy", "error": str(e)}

            # Define HTTP-based functions for data retrieval
            def http_query_documents(query: str,
                                   collection_name: str,
                                   username: str,
                                   retriever_config: Dict[str, Any],
                                   query_params: Dict[str, Any],
                                   gemini_api_key: str,
                                   chat_history: List[Dict[str, Any]] = None) -> Dict[str, Any]:
                """HTTP-based query documents function"""
                try:
                    import requests

                    # Get the service URL
                    service_url = DATA_RETRIEVER_SERVICE_URL
                    endpoint_url = f"{service_url}/query"

                    # Prepare request payload
                    payload = {
                        "query": query,
                        "collection_name": collection_name,
                        "username": username,
                        "retriever_config": retriever_config,
                        "query_params": query_params,
                        "gemini_api_key": gemini_api_key,
                        "chat_history": chat_history or []
                    }

                    # Make HTTP request
                    response = requests.post(
                        endpoint_url,
                        json=payload,
                        timeout=300  # 5 minutes timeout
                    )

                    response.raise_for_status()
                    result = response.json()
                    
                    # Debug logging
                    logger.info(f"Retriever service response status: {response.status_code}")
                    logger.info(f"Retriever service response keys: {list(result.keys()) if isinstance(result, dict) else type(result)}")
                    logger.info(f"Retriever service success field: {result.get('success')}")
                    if not result.get("success"):
                        logger.error(f"Retriever service returned success=False, error: {result.get('error')}")
                        logger.error(f"Full response: {result}")

                    # Return the data directly if successful, otherwise return error
                    if result.get("success"):
                        return result.get("data", {})
                    else:
                        return {
                            "success": False,
                            "error": result.get("error", "Unknown error from retriever service")
                        }

                except requests.exceptions.HTTPError as e:
                    logger.error(f"HTTP query_documents failed with status {e.response.status_code}: {e.response.text}")
                    return {
                        "success": False,
                        "error": f"HTTP {e.response.status_code}: {e.response.text}"
                    }
                except Exception as e:
                    logger.error(f"HTTP query_documents failed: {e}")
                    return {
                        "success": False,
                        "error": f"Request failed: {str(e)}"
                    }

            def http_retrieval_health_check():
                """HTTP-based retrieval health check function"""
                try:
                    import requests
                    service_url = DATA_RETRIEVER_SERVICE_URL
                    response = requests.get(f"{service_url}/health", timeout=10)
                    response.raise_for_status()
                    return response.json()
                except Exception as e:
                    logger.error(f"HTTP retrieval health check failed: {e}")
                    return {"status": "unhealthy", "error": str(e)}

            _ingestion_functions = {
                'upload_and_process_files': http_upload_and_process_files,
                'health_check': http_ingestion_health_check
            }

            _retrieval_functions = {
                'query_documents': http_query_documents,
                'health_check': http_retrieval_health_check
            }
            
            logger.info("HTTP-based client functions loaded successfully")
            return True
        except ImportError as e:
            logger.warning(f"Could not import direct clients: {e}. Falling back to HTTP calls.")
            return False
    return True

logger = logging.getLogger(__name__)

# --- DOCX/PDF helpers ---
EXT_TO_CT = {
    ".pdf":  "application/pdf",
    ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ".txt":  "text/plain",
}

def resolve_name_suffix_and_ct(doc: Dict[str, Any], i: int):
    """
    Decide filename, extension, and content_type for a doc.
    Priority:
      1) use provided filename (add ext if missing)
      2) else infer from document_type
      3) else fall back to .txt
    """
    filename = doc.get("filename")
    if filename:
        ext = Path(filename).suffix.lower()
        if not ext:
            dt = (doc.get("document_type") or "").lower()
            ext = ".docx" if dt == "docx" else ".pdf" if dt == "pdf" else ".txt"
            filename = f"{filename}{ext}"
    else:
        dt = (doc.get("document_type") or "").lower()
        ext = ".docx" if dt == "docx" else ".pdf" if dt == "pdf" else ".txt"
        title = doc.get("title") or f"document_{i+1}"
        filename = f"{title}{ext}"

    content_type = doc.get("content_type") or EXT_TO_CT.get(ext, "application/octet-stream")
    return filename, content_type


class ExternalServicesManager:
    """
    Manager for communicating with external GPU services.

    Handles data ingestion and document retrieval through HTTP API calls
    to separate FastAPI services. Uses HTTP-based communication for all services.
    """
    
    def __init__(self,
                 embedding_service_url: str = None,
                 reranking_service_url: str = None,
                 data_ingestion_service_url: str = None,
                 data_retriever_service_url: str = None,
                 gemini_api_key: str = None):
        """
        Initialize external services manager

        Args:
            embedding_service_url: URL of the embedding service
            reranking_service_url: URL of the reranking service
            data_ingestion_service_url: URL of the data ingestion service
            data_retriever_service_url: URL of the data retriever service
            gemini_api_key: Gemini API key for LLM operations
        """
        self.embedding_service_url = EMBEDDING_SERVICE_URL.rstrip('/')
        self.reranking_service_url = RERANKING_SERVICE_URL.rstrip('/')
        self.data_ingestion_service_url = DATA_INGESTION_SERVICE_URL
        self.data_retriever_service_url = DATA_RETRIEVER_SERVICE_URL
        self.gemini_api_key = GEMINI_API_KEY

        logger.info(f"External services manager initialized")
        logger.info(f"Using HTTP calls for data ingestion and retrieval")
        logger.info(f"Data ingestion service URL: {self.data_ingestion_service_url}")
        logger.info(f"Data retriever service URL: {self.data_retriever_service_url}")
        logger.info(f"Embedding service URL: {self.embedding_service_url}")
        logger.info(f"Reranking service URL: {self.reranking_service_url}")
    
    def check_services_health(self) -> Dict[str, bool]:
        """
        Check health status of all services
        
        Returns:
            Dict with service health status
        """
        health_status = {
            "data_ingestion": False,
            "data_retriever": False,
            "embedding": False,
            "reranking": False
        }
        
        # Check data ingestion service using direct HTTP call
        try:
            import requests
            response = requests.get(f"{self.data_ingestion_service_url}/health", timeout=10)
            if response.status_code == 200:
                health_result = response.json()
                if health_result.get("status") in ["healthy", "partial"]:
                    health_status["data_ingestion"] = True
                    logger.info("Data ingestion service is healthy")
        except Exception as e:
            logger.error(f"Data ingestion service health check failed: {e}")

        # Check data retriever service via HTTP
        try:
            response = requests.get(f"{self.data_retriever_service_url}/health", timeout=10)
            if response.status_code == 200:
                health_result = response.json()
                if health_result.get("status") in ["healthy", "partial"]:
                    health_status["data_retriever"] = True
                    logger.info("Data retriever service is healthy")
        except Exception as e:
            logger.error(f"Data retriever service health check failed: {e}")

        # Check embedding service via HTTP
        try:
            response = requests.get(f"{self.embedding_service_url}/health", timeout=10)
            if response.status_code == 200:
                health_status["embedding"] = True
                logger.info("Embedding service is healthy")
        except Exception as e:
            logger.error(f"Embedding service health check failed: {e}")

        # Check reranking service via HTTP
        try:
            response = requests.get(f"{self.reranking_service_url}/health", timeout=10)
            if response.status_code == 200:
                health_status["reranking"] = True
                logger.info("Reranking service is healthy")
        except Exception as e:
            logger.error(f"Reranking service health check failed: {e}")
        
        return health_status
    
    async def ingest_documents(self, 
                        documents: List[Dict[str, Any]], 
                        username: str,
                        collection_name: str,
                        user_id: Optional[int] = None,
                        space_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Ingest documents using the external data ingestion service
        
        Args:
            documents: List of documents with content and metadata
            username: Username for collection targeting
            collection_name: Qdrant collection name
            
        Returns:
            Ingestion result dictionary
        """
        try:
            logger.info(f"Starting document ingestion for {len(documents)} documents for user {username}")
            files_data = []
            for i, doc in enumerate(documents):
                filename, content_type = resolve_name_suffix_and_ct(doc, i) 
               # A) MinIO-backed files - DIRECT STREAMING (No temp files)

                try:
                    file_stream = minio_service.get_file_stream(doc['minio_bucket'], doc['minio_object_key'])
                    setattr(file_stream, "_filename", filename)
                    files_data.append({
                        'filename': filename,
                        'stream': file_stream,  # Direct stream object
                        'content_type': content_type,
                        'source': 'stream',  # Mark as streaming source
                        'space_id': space_id
                    })
                except Exception as e:
                    logger.error(f"Error creating MinIO stream: {e}")
                    continue

            if not files_data:
                logger.warning("No files prepared for ingestion")
                return {
                    "success": False,
                    "error": "No files to upload",
                    "processed_documents": 0,
                    "failed_documents": len(documents)
                }
                
            # Handle both tuple and dict entries safely
            def get_filename(entry):
                if isinstance(entry, dict):
                    return entry.get('filename', '').lower()
                elif isinstance(entry, (list, tuple)) and len(entry) >= 1:
                    return str(entry[0]).lower()
                return ''

            contains_docx = any(get_filename(e).endswith('.docx') for e in files_data)

                            # Prepare processing configuration for direct client call
            processing_config = {
                "qdrant_url": QDRANT_URL,
                "prefer_grpc": True,
                "grpc_port": GRPC_PORT,
                "qdrant_api_key": QDRANT_API_KEY,
                "DATABASE_URL": DATABASE_URL,
                "chunk_size": 750,
                "chunk_overlap": 150,
                "use_custom_splitter": False,
                "use_unstructured": True if contains_docx else False,
                "embedding_api_url": self.embedding_service_url,
                "llm_model": "gemini-2.5-flash-lite",
                "vision_model": "gemini-2.5-flash"
            }
            logger.info(f"Processing {len(files_data)} files using HTTP-based ingestion service")

            try:
                # Use HTTP-based ingestion function
                if not _load_direct_clients() or not _ingestion_functions:
                    return {
                        "success": False,
                        "error": "Data ingestion service not available. Please check service configuration.",
                        "processed_documents": 0,
                        "failed_documents": len(documents)
                    }

                # Call the HTTP-based function
                result = _ingestion_functions['upload_and_process_files'](
                    files=files_data,
                    username=username,
                    collection_name=collection_name,
                    processing_config=processing_config,
                    gemini_api_key=self.gemini_api_key,
                    space_id=space_id,
                    user_id=user_id
                )

                if result.get("success"):
                    logger.info(f"Successfully ingested {len(documents)} documents for {username}")
                    # Database indexing status update is handled by the knowledge_base router
                    return result
                else:
                    error_message = result.get("error", "Unknown error")
                    logger.error(f"Data Ingestion failed: {error_message}")

                    return {
                        "success": False,
                        "error": error_message,
                        "processed_documents": result.get("processed_documents", 0),
                        "failed_documents": result.get("failed_documents", len(documents))
                    }

            except Exception as e:
                logger.error(f"Error during file processing: {e}")
                return {
                    "success": False,
                    "error": str(e),
                    "processed_documents": 0,
                    "failed_documents": len(documents)
                }

        except Exception as e:
            logger.error(f"Error ingesting documents: {e}")
            return {
                "success": False,
                "error": str(e),
                "processed_documents": 0,
                "failed_documents": len(documents)
            }
        
    
    def query_documents(self,
                       query: str,
                       collection_name: str,
                       username: str,
                       agent_instructions: List[str] = None,
                       allowed_files: Optional[List[str]] = None,
                       chat_history: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """
        Query documents using the external retrieval service
        
        Args:
            query: User query
            collection_name: Qdrant collection name
            username: Username for collection targeting
            agent_instructions: Instructions for response generation
            allowed_files: List of files to filter by
            chat_history: Chat history for context
            
        Returns:
            Query response dictionary
        """
        try:
            # Prepare retriever configuration for direct client call
            retriever_config = {
                "qdrant_url": QDRANT_URL,
                "prefer_grpc": True,
                "grpc_port": GRPC_PORT,
                "qdrant_api_key": QDRANT_API_KEY,
                "database_url": DATABASE_URL,
                "response_instructions": agent_instructions or [
                    f"Provide helpful and accurate information for {username} queries",
                    "Be professional and concise",
                    "Reference specific documents when possible"
                ],
                "query_llm": "gemini-2.5-flash-lite",
                "response_model": "gemini-2.5-flash-lite",
                "embedding_api_url": self.embedding_service_url,
                "reranking_service_url": self.reranking_service_url
            }
            
            # Prepare query parameters
            query_params = {
                "use_query_transform": True,
                "use_multi_query_generation": True,
                "force_rag": False,
                "allowed_files": allowed_files
            }
            
            # Call the direct client function if available, otherwise fall back to error
            if not _load_direct_clients() or not _retrieval_functions:
                return {
                    "success": False,
                    "agent_name": f"{username.title()} Assistant",
                    "query": query,
                    "answer": "Direct client functions not available. Please check service configuration.",
                    "used_rag": False,
                    "document_sources": [],
                    "error": "Direct client functions not available"
                }
            
            result = _retrieval_functions['query_documents'](
                query=query,
                collection_name=collection_name,
                username=username,
                retriever_config=retriever_config,
                query_params=query_params,
                gemini_api_key=self.gemini_api_key,
                chat_history=chat_history or []
            )
            
            if result.get("success"):
                logger.info(f"Successfully processed query for {username}")
                return result
            else:
                logger.error(f"Query processing failed: {result.get('error', 'Unknown error')}")
                return {
                    "success": False,
                    "agent_name": f"{username.title()} Assistant",
                    "query": query,
                    "answer": f"I encountered an error while processing your query. Please try again later.",
                    "used_rag": False,
                    "document_sources": [],
                    "error": result.get("error", "Unknown error")
                }
        
        except Exception as e:
            logger.error(f"Error querying documents: {e}")
            return {
                "success": False,
                "agent_name": f"{username.title()} Assistant",
                "query": query,
                "answer": f"I encountered an error while processing your query: {str(e)}",
                "used_rag": False,
                "document_sources": [],
                "error": str(e)
            }
    
    def get_embedding(self, texts: List[str], embedding_type: str = "dense", normalize: bool = True) -> Dict[str, Any]:
        """
        Get embeddings from the embedding service
        
        Args:
            texts: List of texts to embed
            embedding_type: Type of embedding ("dense", "sparse", or "hybrid")
            normalize: Whether to normalize embeddings
            
        Returns:
            Embedding response dictionary
        """
        try:
            payload = {
                "texts": texts,
                "embedding_type": embedding_type,
                "normalize_embeddings": normalize
            }
            
            response = requests.post(
                f"{self.embedding_service_url}/embed",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Embedding service error: {response.status_code} - {response.text}")
                return {"error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            logger.error(f"Error getting embeddings: {e}")
            return {"error": str(e)}
    
    def rerank_documents(self, query: str, documents: List[Dict[str, Any]], top_n: int = 5) -> Dict[str, Any]:
        """
        Rerank documents using the reranking service
        
        Args:
            query: Query string
            documents: List of documents to rerank
            top_n: Number of top documents to return
            
        Returns:
            Reranking response dictionary
        """
        try:
            payload = {
                "query": query,
                "documents": documents,
                "top_n": top_n
            }
            
            response = requests.post(
                f"{self.reranking_service_url}/rerank",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Reranking service error: {response.status_code} - {response.text}")
                return {"error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            logger.error(f"Error reranking documents: {e}")
            return {"error": str(e)}
    
    def delete_document_chunks(self, filename: str, collection_name: str, space_id: int) -> Dict[str, Any]:
        """
        Delete document chunks from Qdrant and PostgreSQL
        
        Args:
            filename: Name of the file whose chunks to delete
            collection_name: Qdrant collection name
            space_id: Space ID for filtering
            
        Returns:
            Dictionary with deletion results
        """
        try:
            logger.info(f"Deleting chunks for file '{filename}' in collection '{collection_name}', space_id={space_id}")
            
            deleted_from_qdrant = 0
            deleted_from_postgres = 0
            
            # Delete from Qdrant
            try:
                from qdrant_client import QdrantClient
                from qdrant_client.models import Filter, FieldCondition, MatchValue
                
                qdrant_client = QdrantClient(
                    url=QDRANT_URL,
                    api_key=QDRANT_API_KEY,
                    prefer_grpc=True,
                    grpc_port=GRPC_PORT
                )
                
                # Build filter for filename only (space_id not stored in Qdrant metadata)
                # Collection names are already user-specific, so filtering by filename is sufficient
                filter_condition = Filter(
                    must=[
                        FieldCondition(
                            key="metadata.source",
                            match=MatchValue(value=filename)
                        )
                    ]
                )
                
                # First, get the points to count them
                scroll_result = qdrant_client.scroll(
                    collection_name=collection_name,
                    scroll_filter=filter_condition,
                    limit=10000,
                    with_payload=False,
                    with_vectors=False
                )
                
                points_to_delete = scroll_result[0]
                deleted_from_qdrant = len(points_to_delete)
                
                if deleted_from_qdrant > 0:
                    # Delete the points
                    point_ids = [point.id for point in points_to_delete]
                    qdrant_client.delete(
                        collection_name=collection_name,
                        points_selector=point_ids
                    )
                    logger.info(f"Deleted {deleted_from_qdrant} points from Qdrant collection '{collection_name}'")
                else:
                    logger.info(f"No points found in Qdrant for file '{filename}' in collection '{collection_name}'")
                    
            except Exception as e:
                logger.error(f"Error deleting from Qdrant: {e}")
                return {
                    "success": False,
                    "error": f"Failed to delete from Qdrant: {str(e)}",
                    "deleted_from_qdrant": 0,
                    "deleted_from_postgres": 0
                }
            
            # Delete from PostgreSQL (using synchronous connection)
            try:
                import psycopg2
                
                # Convert SQLAlchemy-style URL to standard PostgreSQL DSN
                # psycopg2 doesn't understand the +asyncpg driver specification
                postgres_dsn = DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
                
                # Use synchronous psycopg2 instead of asyncpg to avoid event loop conflicts
                conn = psycopg2.connect(postgres_dsn)
                try:
                    cursor = conn.cursor()
                    cursor.execute(
                        """
                        DELETE FROM raw_documents 
                        WHERE source = %s AND space_id = %s
                        """,
                        (filename, space_id)
                    )
                    deleted_from_postgres = cursor.rowcount
                    conn.commit()
                    cursor.close()
                    logger.info(f"Deleted {deleted_from_postgres} rows from PostgreSQL raw_documents table")
                finally:
                    conn.close()
                    
            except Exception as e:
                logger.error(f"Error deleting from PostgreSQL: {e}")
                return {
                    "success": False,
                    "error": f"Failed to delete from PostgreSQL: {str(e)}",
                    "deleted_from_qdrant": deleted_from_qdrant,
                    "deleted_from_postgres": 0
                }
            
            return {
                "success": True,
                "deleted_from_qdrant": deleted_from_qdrant,
                "deleted_from_postgres": deleted_from_postgres,
                "message": f"Deleted {deleted_from_qdrant} chunks from Qdrant and {deleted_from_postgres} rows from PostgreSQL"
            }
            
        except Exception as e:
            logger.error(f"Error in delete_document_chunks: {e}")
            return {
                "success": False,
                "error": str(e),
                "deleted_from_qdrant": 0,
                "deleted_from_postgres": 0
            }


# Global external services manager instance
external_services = ExternalServicesManager() 