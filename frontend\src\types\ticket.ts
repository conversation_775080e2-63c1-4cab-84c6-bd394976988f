/**
 * Ticket-related TypeScript type definitions
 * Centralized ticket types for consistent usage across the application
 */

// Ticket attachment interface
export interface TicketAttachment {
  id: number;
  filename: string;
  original_filename: string;
  file_size: number;
  content_type: string;
  minio_bucket: string;
  minio_object_key: string;
  uploaded_by: number;
  uploader_username?: string;
  uploaded_at: string;
}

// Core Ticket interface
export interface Ticket {
  id: number;
  title: string;
  description: string;
  category: TicketCategory;
  priority: TicketPriority;
  status: TicketStatus;
  created_by: number;
  assigned_to?: number;
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  admin_notes?: string;
  creator_username?: string;
  creator_email?: string;
  assignee_username?: string;
  attachments?: TicketAttachment[];
}

// Ticket status types
export type TicketStatus = 'open' | 'in_progress' | 'resolved' | 'closed';

// Ticket priority types
export type TicketPriority = 'low' | 'medium' | 'high' | 'urgent';

// Ticket category types
export type TicketCategory = 'bug' | 'feature' | 'support' | 'other';

// API Response interfaces
export interface TicketsResponse {
  tickets: Ticket[];
  total: number;
  page: number;
  per_page: number;
}

export interface TicketSummary {
  total: number;
  open: number;
  in_progress: number;
  resolved: number;
  closed: number;
}

// Form data interfaces
export interface TicketCreationData {
  title: string;
  description: string;
  category: string;
  priority: string;
  images?: File[];
}

export interface TicketUpdateData {
  status?: string;
  admin_notes?: string;
  assigned_to?: number;
}

// UI Configuration types
export interface TicketStatusConfig {
  color: string;
  label: string;
}

export interface TicketPriorityConfig {
  color: string;
  label: string;
}

export interface TicketCategoryConfig {
  color: string;
  label: string;
}

// Configuration objects with proper typing
export const TICKET_STATUSES: Record<TicketStatus, TicketStatusConfig> = {
  open: { color: 'bg-yellow-100 text-yellow-800', label: 'Open' },
  in_progress: { color: 'bg-blue-100 text-blue-800', label: 'In Progress' },
  resolved: { color: 'bg-green-100 text-green-800', label: 'Resolved' },
  closed: { color: 'bg-gray-100 text-gray-800', label: 'Closed' }
};

export const TICKET_PRIORITIES: Record<TicketPriority, TicketPriorityConfig> = {
  low: { color: 'bg-gray-100 text-gray-800', label: 'Low' },
  medium: { color: 'bg-blue-100 text-blue-800', label: 'Medium' },
  high: { color: 'bg-orange-100 text-orange-800', label: 'High' },
  urgent: { color: 'bg-red-100 text-red-800', label: 'Urgent' }
};

export const TICKET_CATEGORIES: Record<TicketCategory, TicketCategoryConfig> = {
  bug: { color: 'bg-red-100 text-red-800', label: 'Bug' },
  feature: { color: 'bg-green-100 text-green-800', label: 'Feature' },
  support: { color: 'bg-blue-100 text-blue-800', label: 'Support' },
  other: { color: 'bg-gray-100 text-gray-800', label: 'Other' }
};

// Filter options for UI
export const TICKET_STATUS_FILTERS = [
  { value: 'all', label: 'All Tickets' },
  { value: 'open', label: 'Open' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'resolved', label: 'Resolved' },
  { value: 'closed', label: 'Closed' }
] as const;

export const TICKET_CATEGORY_OPTIONS = [
  { value: 'bug', label: 'Bug Report' },
  { value: 'feature', label: 'Feature Request' },
  { value: 'support', label: 'Support' },
  { value: 'other', label: 'Other' }
] as const;

export const TICKET_PRIORITY_OPTIONS = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'urgent', label: 'Urgent' }
] as const;
