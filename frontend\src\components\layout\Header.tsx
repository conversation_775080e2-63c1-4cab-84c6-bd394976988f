'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import UserProfileMenu from '../profile/UserProfileMenu';
import NotificationBadge from '../notifications/NotificationBadge';
import { BiMenuAltLeft } from "react-icons/bi";
import { HiDotsVertical } from "react-icons/hi";
import ProfileModal from '../profile/ProfileModal';

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  industry: string;
}

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [showProfileCard, setShowProfileCard] = useState(false);
  const [fabOpen, setFabOpen] = useState(false);
  const fabRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();
  const router = useRouter();
  const isChatPage = pathname === '/chat';
  const isHomePage = pathname === '/';

  useEffect(() => {
    // Check if user is logged in
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('user');
    setUser(null);
    setIsMenuOpen(false);
    router.push('/');
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    // Close FAB dropdown when hamburger menu opens
    if (!isMenuOpen) {
      setFabOpen(false);
    }
  };

  const handleLogin = () => {
    router.push('/login');
  };

  const handleGetStarted = () => {
    if (user) {
      router.push('/chat');
    } else {
      router.push('/login');
    }
  };

  // Handle outside clicks for FAB dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (fabRef.current && !fabRef.current.contains(event.target as Node)) {
        setFabOpen(false);
      }
    };

    if (fabOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [fabOpen]);

  const isActive = (path: string) => pathname === path;

  // Navigation items based on user login status
  const getNavigationItems = () => {
    const baseItems = [
      { href: '/', label: 'Home' },
      { href: '/about', label: 'About' },
      { href: '/whats-new', label: 'What\'s New' }
    ];

    if (user) {
      // Add authenticated user items
      const authItems = [
        { href: '/chat', label: 'Expert Chat' }
      ];

      // Show user dashboard only for non-admins
      if (user.role !== 'admin') {
        authItems.push({ href: '/activity', label: 'Dashboard' });
      }

      // Add admin-only items
      if (user.role === 'admin') {
        authItems.push({ href: '/dashboard', label: 'Dashboard' });
      }

      return [...baseItems, ...authItems];
    }

    return baseItems;
  };

  const navigationItems = getNavigationItems();

  // Render HOME PAGE header (simple design)
  if (isHomePage) {
    return (
      <nav className="w-full bg-white shadow border-b border-gray-300 sticky top-0 z-50">
        {/* Full-width container */}
        <div className="flex justify-between items-center h-20 px-4 sm:px-6 lg:px-8 mx-auto">
          {/* Logo and text content on left */}
          <div className="flex items-center">
            <img
              src="/logo.svg"
              alt="Logo"
              className="h-12 w-auto"
              style={{ maxHeight: "48px" }}
            />
            <div className="flex flex-col items-start">
              <p className="text-purple-900 text-xl sm:text-2xl ">
                <span className="text-purple-900  text-xl sm:text-2xl ">Workplace</span>
                <span className="text-orange-400  text-xl sm:text-2xl ">SLM</span>
              </p>
              <h2 className="text-purple-900  text-sm">
                Powered by <span className="bg-orange-400 bg-clip-text text-transparent  text-sm underline">ProcessVenue</span>
              </h2>
            </div>
          </div>

          {/* Center: Desktop Menu */}
          <div className="hidden md:flex text-black space-x-12 bg-white px-4 py-2 rounded-full border border-gray-300 shadow-md">
            <Link href="/faq" className="hover:text-gray-500 transition-colors">
              FAQ
            </Link>
            <Link href="/about" className="hover:text-gray-500 transition-colors">
              About
            </Link>
            <Link href="/whats-new" className="hover:text-gray-500 transition-colors">
              What&apos;s New
            </Link>
          </div>

          {/* Right: Login & Get Started */}
          <div className="hidden md:flex items-center space-x-4">
            {user ? (
              <>
                {/* Logged in user - Show profile and logout */}
                <button
                  onClick={() => setShowProfileCard(true)}
                  className="flex items-center  text-black gap-2 px-4 py-2 hover:text-blue-900 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="w-5 h-5"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18.685 19.097A9.723 9.723 0 0 0 21.75 12c0-5.385-4.365-9.75-9.75-9.75S2.25 6.615 2.25 12a9.723 9.723 0 0 0 3.065 7.097A9.716 9.716 0 0 0 12 21.75a9.716 9.716 0 0 0 6.685-2.653Zm-12.54-1.285A7.486 7.486 0 0 1 12 15a7.486 7.486 0 0 1 5.855 2.812A8.224 8.224 0 0 1 12 20.25a8.224 8.224 0 0 1-5.855-2.438ZM15.75 9a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {user.username}
                </button>

                {/* Vertical Line */}
                <div className="w-px h-16 bg-gray-300"></div>

                <button
                  onClick={handleLogout}
                  className="text-red-600 px-4 py-2 hover:text-red-700 transition-colors font-medium"
                >
                  Logout
                </button>
              </>
            ) : (
              <>
                {/* Not logged in - Show login and get started */}
                <button
                  onClick={handleLogin}
                  className="flex items-center text-black gap-2 px-4 py-2 hover:text-blue-900 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="w-5 h-5"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18.685 19.097A9.723 9.723 0 0 0 21.75 12c0-5.385-4.365-9.75-9.75-9.75S2.25 6.615 2.25 12a9.723 9.723 0 0 0 3.065 7.097A9.716 9.716 0 0 0 12 21.75a9.716 9.716 0 0 0 6.685-2.653Zm-12.54-1.285A7.486 7.486 0 0 1 12 15a7.486 7.486 0 0 1 5.855 2.812A8.224 8.224 0 0 1 12 20.25a8.224 8.224 0 0 1-5.855-2.438ZM15.75 9a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Login
                </button>

                {/* Vertical Line */}
                <div className="w-px h-16 bg-gray-300"></div>

                <button
                  onClick={handleGetStarted}
                  className="group flex items-center text-black px-6 py-2 hover:text-blue-900 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-6 h-6 mr-2 transition-transform duration-300 group-hover:rotate-90"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                    />
                  </svg>
                  Get Started
                </button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="focus:outline-none focus:ring-2 focus:ring-blue-600"
            >
              {isMenuOpen ? (
                <svg
                  className="h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              ) : (
                <svg
                  className="h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden px-2 pt-2 pb-3 space-y-1 bg-gray-100 w-full">
            <Link
              href="/faq"
              onClick={() => setIsMenuOpen(false)}
              className="block px-3 py-2 rounded hover:bg-gray-200 transition-colors"
            >
              FAQ
            </Link>
            <Link
              href="/about"
              onClick={() => setIsMenuOpen(false)}
              className="block px-3 py-2 rounded hover:bg-gray-200 transition-colors"
            >
              About
            </Link>
            <Link
              href="/whats-new"
              onClick={() => setIsMenuOpen(false)}
              className="block px-3 py-2 rounded hover:bg-gray-200 transition-colors"
            >
              What&apos;s New
            </Link>

            {user ? (
              <>
                <button
                  onClick={() => {
                    setIsMenuOpen(false);
                    setShowProfileCard(true);
                  }}
                  className="w-full text-left block px-3 py-2 rounded hover:bg-gray-200 transition-colors"
                >
                  View Profile ({user.username})
                </button>
                <button
                  onClick={handleLogout}
                  className="w-full text-left block px-3 py-2 rounded bg-red-600 text-white hover:bg-red-700 transition-colors"
                >
                  Logout
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => {
                    setIsMenuOpen(false);
                    handleLogin();
                  }}
                  className="w-full text-left block px-3 py-2 rounded hover:bg-gray-200 transition-colors"
                >
                  Log In
                </button>
                <button
                  onClick={() => {
                    setIsMenuOpen(false);
                    handleGetStarted();
                  }}
                  className="w-full text-left block px-3 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                >
                  Get Started
                </button>
              </>
            )}
          </div>
        )}

        {/* Profile Modal */}
        {showProfileCard && user && (
          <ProfileModal
            user={user}
            open={showProfileCard}
            onClose={() => setShowProfileCard(false)}
          />
        )}
      </nav>
    );
  }

  // Render OTHER PAGES header (feature-rich design)
  return (
    <header className="bg-white/95 backdrop-blur-sm shadow-lg border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-9xl  px-6 sm:px-4 lg:px-8">
        <div className="flex items-center justify-between h-16 md:h-[5rem]">
          {/* NEW: Mobile 'open sidebar' button visible on chat page */}
          {/* Wrap button and logo in a flex with minimal gap: */}
          <div className="flex items-center space-x-2">
            {isChatPage && (
              <button
                type="button"
                onClick={() => window.dispatchEvent(new CustomEvent('chat-sidebar:toggle'))}
                className="md:hidden inline-flex items-center justify-center p-1 rounded-md bg-white text-gray-700 shadow-sm hover:bg-gray-50 hover:text-blue-700 active:scale-95 transition"
                aria-controls="chat-sidebar"
              >
                <BiMenuAltLeft
                  className="w-9 h-9"
                  style={{ imageRendering: 'pixelated', strokeWidth: 0.2 }}
                />
              </button>
            )}

            <img
              src="/logo.svg"
              alt="Logo"
              className="h-10 w-auto"
              style={{ maxHeight: "40px" }}
            />
            <div className="flex flex-col items-start">
              <p className="text-purple-900 text-xl sm:text-2xl ">
                <span className="text-purple-900  text-xl sm:text-2xl ">Workplace</span>
                <span className="text-orange-400  text-xl sm:text-2xl ">SLM</span>
              </p>
              <h2 className="text-purple-900  text-sm">
                Powered by <span className="bg-orange-400 bg-clip-text text-transparent  text-sm underline">ProcessVenue</span>
              </h2>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex justify-end space-x-4">
            {navigationItems.map(({ href, label }) => (
              <Link
                key={href}
                href={href}
                className={`relative transition-all duration-300 px-4 lg:px-5 py-3 rounded-xl text-sm lg:text-base font-medium ${isActive(href) ? 'bg-blue-50 text-blue-900' : 'text-slate-600 hover:text-blue-900 hover:bg-blue-50'
                  }`}
              >
                {label}
              </Link>
            ))}
            {!user && (
              <Link
                href="/login"
                className="bg-blue-900 hover:bg-blue-700 text-white px-4 lg:px-6 py-3 rounded-xl transition-all duration-300 font-medium text-sm lg:text-base shadow-lg hover:shadow-xl hover:-translate-y-0.5"
              >
                Sign In
              </Link>
            )}
            {user && (
              <>
                <NotificationBadge />
                <UserProfileMenu user={user} />
              </>
            )}
          </nav>

          {/* Mobile buttons container */}
          <div className="md:hidden flex items-center space-x-2">


            {/* Hamburger menu button */}
            <button
              onClick={toggleMenu}
              className={`inline-flex items-center justify-center p-2 rounded-md text-gray-500 bg-white transition-all duration-200 ${isMenuOpen ? 'border border-blue-900' : 'border border-transparent hover:border-white'
                }`}
              aria-expanded={isMenuOpen}
              aria-controls="mobile-nav"
            >
              <span className="sr-only">Open main menu</span>
              <HiDotsVertical className={`${isMenuOpen ? 'hidden' : 'block'} w-6 h-6`} />
              <svg className={`${isMenuOpen ? 'block' : 'hidden'} w-6 h-6`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />

              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMenuOpen && (
          <>
            <div
              className="fixed inset-0 top-[5rem] bg-black/20 backdrop-blur-[1px] z-40"
              onClick={() => setIsMenuOpen(false)}
            />
            <div
              id="mobile-nav"
              className="md:hidden fixed top-[5rem] inset-x-0 max-h-[calc(100vh-5rem)] overflow-y-auto border-t border-gray-200 bg-white shadow-2xl z-50"
            >
              {/* Navigation Section */}
              <div className="py-2">
                <h3 className="px-4 text-[11px] font-semibold text-gray-500 uppercase tracking-wider mb-1">Navigation</h3>
                {navigationItems.map(({ href, label }) => (
                  <Link
                    key={href}
                    href={href}
                    onClick={() => setIsMenuOpen(false)}
                    className={`block px-5 py-2.5 text-[15px] font-medium transition-colors ${isActive(href) ? 'bg-blue-50 text-blue-900 border-l-2 border-blue-900' : 'text-gray-700 hover:bg-gray-50'
                      }`}
                  >
                    {label}
                  </Link>
                ))}
              </div>

              {user && (
                <>
                  {/* User Section */}
                  <div className="border-t border-gray-100 py-2">
                    <h3 className="px-4 text-[11px] font-semibold text-gray-500 uppercase tracking-wider mb-1">Account</h3>

                    <button
                      onClick={() => {
                        setIsMenuOpen(false);
                        setShowProfileCard(true);
                      }}
                      className="w-full text-left block px-6 py-2.5 text-[15px] font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      View Profile
                    </button>
                  </div>

                  {/* Logout Section */}
                  <div className="border-t border-gray-100 py-2">
                    <button
                      onClick={handleLogout}
                      className="w-full text-left block px-5 py-2.5 text-[15px] font-medium text-red-600 hover:bg-red-50 transition-colors"
                    >
                      Sign Out
                    </button>
                  </div>
                </>
              )}

              {!user && (
                <div className="border-t border-gray-100 py-2">
                  <Link
                    href="/login"
                    onClick={() => setIsMenuOpen(false)}
                    className="block mx-4 py-2.5 bg-blue-900 text-white rounded-lg text-center font-medium hover:bg-blue-700 transition-colors"
                  >
                    Sign In
                  </Link>
                </div>
              )}
            </div>
          </>
        )}
      </div>

      {showProfileCard && user && (
        <ProfileModal user={user} open={showProfileCard} onClose={() => setShowProfileCard(false)} />
      )}
    </header>
  );
}