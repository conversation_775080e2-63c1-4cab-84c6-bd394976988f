#!/usr/bin/env python3

import psycopg2
import csv
from datetime import datetime
import os

def export_chat_data_to_csv():
    # Connection parameters
    db_params = {
        'dbname': 'tsai_prod',
        'user': 'tsai_kanwar',
        'password': 'tsai@880',
        'host': '**************',
        'port': '5432'
    }
    
    # Create timestamp for unique filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"chat_export_user_52_{timestamp}.csv"
    
    try:
        # Connect to the database
        print(f"Connecting to the PostgreSQL database...")
        connection = psycopg2.connect(**db_params)
        cursor = connection.cursor()
        
        # Execute the query
        query = """
        SELECT *
        FROM expert_chat_interactions
        WHERE user_id = 52
        ORDER BY created_at DESC
        """
        
        print(f"Executing query for user_id = 52...")
        cursor.execute(query)
        
        # Fetch all rows
        rows = cursor.fetchall()
        
        if not rows:
            print("No data found for user_id = 52")
            return
        
        # Get column names
        column_names = [desc[0] for desc in cursor.description]
        
        # Write to CSV
        print(f"Writing data to {output_file}...")
        with open(output_file, 'w', newline='', encoding='utf-8') as csv_file:
            csv_writer = csv.writer(csv_file)
            
            # Write header
            csv_writer.writerow(column_names)
            
            # Write data rows
            csv_writer.writerows(rows)
        
        print(f"Successfully exported {len(rows)} records to {output_file}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Close connection
        if connection:
            cursor.close()
            connection.close()
            print("Database connection closed.")

if __name__ == "__main__":
    print("Starting export process...")
    export_chat_data_to_csv()
    print("Process completed.")
