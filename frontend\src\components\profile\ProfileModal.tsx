import React, { useState } from 'react';
import { <PERSON><PERSON>ye, FiEyeOff } from 'react-icons/fi';
import { API_BASE_URL } from '@/lib/api';

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  industry?: string;
}

interface Props {
  user: User;
  open?: boolean; // kept for backward compatibility
  onClose?: () => void;
  variant?: 'modal' | 'inline';
}

export default function ProfileModal({ user, open = true, onClose = () => {}, variant = 'modal' }: Props) {
  // In modal variant respect 'open' prop, in inline always render content
  if (variant === 'modal' && !open) return null;

  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [changingPassword, setChangingPassword] = useState(false);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
  const [passwordData, setPasswordData] = useState({
    current_password: '',
    new_password: '',
    confirm_password: '',
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const handlePasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({ ...prev, [name]: value }));
  };

  const togglePasswordVisibility = (
    field: 'current' | 'new' | 'confirm',
  ) => {
    setShowPasswords((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  const togglePasswordForm = () => {
    setShowPasswordForm((prev) => !prev);
    if (!showPasswordForm) {
      setPasswordError(null);
      setPasswordSuccess(null);
    }
  };

  const handlePasswordSubmit = async () => {
    setChangingPassword(true);
    setPasswordError(null);
    setPasswordSuccess(null);
    try {
      const token = localStorage.getItem('access_token');
      const res = await fetch(`${API_BASE_URL}/auth/me/change-password`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(passwordData),
      });
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.detail || 'Failed to change password');
      }
      const result = await res.json();
      setPasswordSuccess(result.message || 'Password changed successfully!');
      setPasswordData({ current_password: '', new_password: '', confirm_password: '' });
      // force logout
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      setTimeout(() => {
        window.location.href = '/login';
      }, 1500);
    } catch (err: any) {
      setPasswordError(err.message || 'Error changing password');
    } finally {
      setChangingPassword(false);
    }
  };

  const content = (
      <div
        className={`${variant === 'inline' ? 'p-0' : 'bg-white rounded-lg shadow-xl p-4 sm:p-6 w-full max-w-sm sm:max-w-md mx-4 my-8 max-h-[90vh]'} overflow-y-auto`}
      >
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          {variant === 'modal' && (
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
            </button>
          )}
        </div>
        {/* Profile info */}
        <div className="mb-8">
          {/* Profile Header with Avatar */}
          <div className="flex items-center gap-6 mb-6">
            <div className="relative">
              <div className="w-20 h-20 sm:w-24 sm:h-24 rounded-full bg-blue-900 flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-3xl sm:text-4xl">
                  {user.username.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="absolute -bottom-1 -right-1 w-7 h-7 bg-blue-500 rounded-full border-4 border-white"></div>
            </div>
            
            <div className="flex-1">
              <h3 className="text-2xl font-bold text-gray-900 mb-1">{user.username}</h3>
              <p className="text-sm text-gray-600 mb-3">{user.email}</p>
              <div className="flex flex-wrap gap-2">
                {user.role && (
                  <span className="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                    <svg className="w-3.5 h-3.5 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                    {user.role}
                  </span>
                )}
                {user.industry && (
                  <span className="inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                    <svg className="w-3.5 h-3.5 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clipRule="evenodd" />
                    </svg>
                    {user.industry}
                  </span>
                )}
              </div>
            </div>
          </div>
          </div>
        
        <div className="border-t border-gray-200 pt-6 mb-6"></div>
        {/* Security Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-base font-semibold text-gray-900">Security Settings</h4>
              <p className="text-sm text-gray-500">Manage your account security</p>
            </div>
          </div>
          
          {!showPasswordForm && (
            <button
              onClick={togglePasswordForm}
              className="px-4 py-3 bg-blue-900 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 flex items-center justify-center space-x-2 shadow-sm hover:shadow"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
              </svg>
              <span className="font-medium">Change Password</span>
            </button>
          )}
        </div>
        {/* Password Form */}
        {showPasswordForm && (
          <div className="space-y-4 bg-gray-50 rounded-lg p-6 border border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-900 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Change Password</h3>
                  <p className="text-xs text-gray-500">Update your account password</p>
                </div>
              </div>
              <button
                onClick={togglePasswordForm}
                className="text-gray-400 hover:text-gray-600 p-1 rounded-lg hover:bg-white transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {passwordError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg flex items-start gap-2">
                <svg className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <p className="text-red-700 text-sm font-medium">{passwordError}</p>
              </div>
            )}
            
            {passwordSuccess && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg flex items-start gap-2">
                <svg className="w-5 h-5 text-blue-900 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <p className="text-blue-700 text-sm font-medium">{passwordSuccess}</p>
              </div>
            )}
            
            <div className="space-y-4">
              {(['current', 'new', 'confirm'] as const).map((field) => (
                <div key={field}>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    {field === 'current' ? 'Current Password' : field === 'new' ? 'New Password' : 'Confirm New Password'}
                  </label>
                  <div className="relative">
                    <input
                      name={field === 'current' ? 'current_password' : field === 'new' ? 'new_password' : 'confirm_password'}
                      type={showPasswords[field] ? 'text' : 'password'}
                      value={passwordData[
                        field === 'current'
                          ? 'current_password'
                          : field === 'new'
                          ? 'new_password'
                          : 'confirm_password'
                      ]}
                      onChange={handlePasswordChange}
                      className="w-full px-4 py-3 pr-12 bg-white border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none transition-all text-sm"
                      placeholder={
                        field === 'current'
                          ? 'Enter current password'
                          : field === 'new'
                          ? 'Min. 8 characters'
                          : 'Re-enter new password'
                      }
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility(field)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPasswords[field] ? (
                        <FiEyeOff className="h-5 w-5" />
                      ) : (
                        <FiEye className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>
              ))}
              
              <div className="pt-2">
                <button
                  onClick={handlePasswordSubmit}
                  disabled={changingPassword}
                  className="w-full px-4 py-3 bg-blue-900 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm hover:shadow flex items-center justify-center gap-2"
                >
                  {changingPassword ? (
                    <>
                      <svg className="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Updating Password...</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>Update Password</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
  );

  if (variant === 'inline') {
    return content;
  }

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 min-h-screen"
      onClick={onClose}
    >
      {/* stop propagation handled in content wrapper */}
      <div onClick={(e)=>e.stopPropagation()}>{content}</div>
    </div>
  );
}
