import requests
import logging
from typing import List, Dict, Any
import time

logger = logging.getLogger(__name__)


class ImageEmbeddingClient:
    """Client for Image Embedding Service (SigLIP-2) - Retriever Version"""
    
    def __init__(self, service_url: str = "http://**************:8019", timeout: int = 60, max_retries: int = 3):
        """
        Initialize Image Embedding Client
        
        Args:
            service_url: URL of the image embedding service
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
        """
        self.service_url = service_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        logger.info(f"Initializing Image Embedding Client (Retriever) with URL: {self.service_url}")
        
        # Check service health on initialization
        try:
            health = self.health_check()
            if health.get('status') == 'healthy':
                logger.info(f"Connected to Image Embedding Service - Model: {health.get('model')}")
            else:
                logger.warning(f"Image Embedding Service health check returned: {health}")
        except Exception as e:
            logger.error(f"Failed to connect to Image Embedding Service: {e}")
            # Don't raise - allow retriever to continue without image search
    
    def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        try:
            response = requests.get(f"{self.service_url}/health", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {"status": "error", "message": str(e)}
    
    def _make_request(self, endpoint: str, payload: Any) -> Dict[str, Any]:
        """Make HTTP request with retry logic"""
        url = f"{self.service_url}{endpoint}"
        
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"Making request to {endpoint} (attempt {attempt + 1}/{self.max_retries})")
                response = requests.post(
                    url,
                    json=payload,
                    timeout=self.timeout
                )
                response.raise_for_status()
                return response.json()
                
            except requests.exceptions.Timeout:
                logger.warning(f"Request to {endpoint} timed out (attempt {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    raise
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Request to {endpoint} failed: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    raise
    
    def embed_text(self, texts: List[str]) -> Dict[str, Any]:
        """
        Generate embeddings for text queries (for image-text retrieval)
        
        Args:
            texts: List of text queries
            
        Returns:
            Dictionary with embeddings and metadata
        """
        if not texts:
            logger.warning("Empty texts list provided to embed_text")
            return {"embeddings": [], "dimension": 1152, "processing_time": 0.0}
        
        logger.info(f"Embedding {len(texts)} text queries for image retrieval")
        
        try:
            result = self._make_request("/embed-text", texts)
            logger.info(f"Successfully embedded {len(texts)} texts in {result.get('processing_time', 0):.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error embedding text: {e}")
            raise

