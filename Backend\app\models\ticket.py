from sqlalchemy import Column, Integer, String, Text, DateTime, <PERSON>olean, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

class Ticket(Base):
    __tablename__ = "tickets"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    category = Column(String(50), nullable=False)  # 'bug', 'feature', 'support', 'other'
    priority = Column(String(20), default='medium', nullable=False)  # 'low', 'medium', 'high', 'urgent'
    status = Column(String(20), default='open', nullable=False)  # 'open', 'in_progress', 'resolved', 'closed'
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    assigned_to = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    resolved_at = Column(DateTime, nullable=True)
    admin_notes = Column(Text, nullable=True)

    # Relationships
    creator = relationship("User", foreign_keys=[created_by], back_populates="created_tickets")
    assignee = relationship("User", foreign_keys=[assigned_to], back_populates="assigned_tickets")
    attachments = relationship("TicketAttachment", back_populates="ticket", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Ticket {self.id}: {self.title}>"
