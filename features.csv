"Module","Feature","UI","API","Roles","Notes"
"Authentication & Accounts","Register user","register/page.tsx","POST /auth/register","Public","Enforce min password length; unique username/email; status pending; invalidates account-requests cache"
"Authentication & Accounts","Login (JSON)","login/page.tsx","POST /auth/login","Approved users","Pending/rejected blocked; returns JWT + user; session cached in Redis"
"Authentication & Accounts","Login (OAuth2 form)","login/page.tsx","POST /auth/token","Approved users","Same as JSON; OAuth2 compatible"
"Authentication & Accounts","Get current user","UserProfileMenu.tsx","GET /auth/me","Authenticated","Returns profile; cached for 1h"
"Authentication & Accounts","Update profile","ProfileModal.tsx","PUT /auth/me","Authenticated","Non-admin cannot change role"
"Authentication & Accounts","Change password","ProfileModal.tsx","PUT /auth/me/change-password","Authenticated","Verify current; min length; new!=current; clears session; forces re-login"
"Authentication & Accounts","Logout","Header menu","POST /auth/logout","Authenticated","Clears sessions and caches; closes live sessions"
"Authentication & Accounts","Admin create user","admin/AddUserModal.tsx","POST /auth/admin-create-user","Admin","Creates approved user"
"Authentication & Accounts","List users","admin panels","GET /auth/users?skip=&limit=","Admin","Paginated"
"Authentication & Accounts","Get user by id","admin panels","GET /auth/users/{user_id}","Admin","404 if not found"
"Authentication & Accounts","Update user","admin panels","PUT /auth/users/{user_id}","Admin","Updates fields"
"Account Requests","List account requests","admin/AccountRequestsPanel.tsx","GET /account-requests?status_filter=&skip=&limit=","Admin","Filters pending/approved/rejected; cached"
"Account Requests","Approve account","admin/AccountRequestCard.tsx","PUT /account-requests/{user_id}/approve","Admin","Cannot modify admin or self; invalidates admin cache"
"Account Requests","Reject account","admin/AccountRequestCard.tsx","PUT /account-requests/{user_id}/reject","Admin","Cannot modify admin or self; invalidates admin cache"
"Account Requests","Request counts","dashboard cards","GET /account-requests/count","Admin","Cached status counts"
"User Spaces","Create space","KnowledgeBaseSidebar.tsx","POST /spaces","Authenticated","Unique name per user; caches updated"
"User Spaces","List my spaces","KnowledgeBaseSidebar.tsx","GET /spaces","Authenticated","Cached per user"
"User Spaces","Get space","KnowledgeBaseSidebar.tsx","GET /spaces/{space_id}","Owner","Owner-only access"
"User Spaces","Update space","KnowledgeBaseSidebar.tsx","PUT /spaces/{space_id}","Owner","Name uniqueness enforced"
"User Spaces","Delete space (deactivate)","KnowledgeBaseSidebar.tsx","DELETE /spaces/{space_id}","Owner","Soft delete; caches updated"
"User Spaces","Admin list all spaces","admin panels","GET /spaces/admin/all","Admin","Includes inactive"
"Knowledge Base","Create text document","KnowledgeBaseSidebar.tsx","POST /knowledge","Owner","document_type=text; content required; uploads to MinIO; invalidates space cache"
"Knowledge Base","Upload single file","KnowledgeBaseSidebar.tsx","POST /knowledge/upload/single","Owner","Types: pdf, docx; max 10MB; duplicate warning unless allow_duplicate=true"
"Knowledge Base","Bulk upload files","KnowledgeBaseSidebar.tsx","POST /knowledge/upload/bulk","Owner","Per-file validations; returns uploaded/failed/duplicates; dedupe naming in batch"
"Knowledge Base","List space documents","KnowledgeBaseSidebar.tsx","GET /knowledge/space/{space_id}/documents?search=&limit=&offset=","Owner/Admin","Search over title/description/content; paginated"
"Knowledge Base","Get document","KnowledgeBaseSidebar.tsx","GET /knowledge/{document_id}","Owner/Admin/Creator","Access if space owner OR creator OR admin; caches per user"
"Knowledge Base","Download inline","KnowledgeBaseSidebar.tsx","GET /knowledge/{document_id}/download[?token]","Owner/Admin","Inline Content-Disposition; MinIO"
"Knowledge Base","Download attachment","KnowledgeBaseSidebar.tsx","GET /knowledge/{document_id}/download_attachment[?token]","Owner/Admin","Attachment disposition"
"Knowledge Base","Update document","KnowledgeBaseSidebar.tsx","PUT /knowledge/{document_id}","Creator/Admin","Title/description/space; invalidates caches (old/new space)"
"Knowledge Base","Delete document","KnowledgeBaseSidebar.tsx","DELETE /knowledge/{document_id}","Creator/Admin","Deletes MinIO object; invalidates caches"
"Knowledge Base","Index space documents","KnowledgeBaseSidebar.tsx","POST /knowledge/space/{space_id}/index","Owner/Admin","Calls ingestion service; marks indexed; invalidates caches"
"Knowledge Base","Bulk index documents","Admin KB","POST /documents/bulk-index?department=","Admin","Indexes all non-indexed (optional department); invalidates caches"
"Knowledge Base","Document index status","KnowledgeBaseSidebar.tsx","GET /documents/index-status/{document_id}","Authenticated","Returns indexed flag and time"
"Knowledge Base","Documents service health","KnowledgeBaseSidebar.tsx","GET /documents/health","Authenticated","External services status info"
"AI Assistant","List agents","AgentSelector.tsx","GET /agents/","Authenticated","Returns 'general' agent"
"AI Assistant","Ask query","ChatPageContent.tsx","POST /agents/query","Authenticated","Exactly one of web/document/deep true; supports allowed_files; returns response_id, sources, etc."
"AI Assistant","Cancel active query","ChatSidebar.tsx","POST /agents/cancel","Authenticated","Cancels by query_session_id"
"AI Assistant","Agent chat history","ChatSidebar.tsx","GET /agents/chat-history/{agent_type}?chat_session_id=","Authenticated","Returns messages and metadata"
"AI Assistant","Clear agent chat history","ChatSidebar.tsx","DELETE /agents/chat-history/{agent_type}?chat_session_id=","Authenticated","Clears Redis history"
"AI Assistant","Agent chat sessions","ChatSidebar.tsx","GET /agents/chat-sessions/{agent_type}[?force_refresh=true]","Authenticated","Cached list with session titles/counts"
"AI Assistant","Generate follow-ups","FeedbackWidget/after answer","POST /agents/followups","Authenticated","Stores suggestions on interaction by response_id"
"Live Chat (1-to-1)","List conversations","LiveChatSidebar.tsx; ChatInterface.tsx","GET /chat/conversations?skip=&limit=","Authenticated","Cached; ordered by updated_at"
"Live Chat (1-to-1)","Create conversation","LiveChatSidebar.tsx; ChatInterface.tsx","POST /chat/conversations","Authenticated","Cannot chat with self; reactivates if existed"
"Live Chat (1-to-1)","Get conversation with messages","LiveChatSidebar.tsx; ChatInterface.tsx","GET /chat/conversations/{conversation_id}?skip=&limit=","Participant","Excludes messages hidden by requester"
"Live Chat (1-to-1)","Send message","LiveChatSidebar.tsx; ChatInterface.tsx","POST /chat/conversations/{conversation_id}/messages","Participant","message_type text/image/file/shared_ai_response/teamviewer_request"
"Live Chat (1-to-1)","Mark conversation read","LiveChatSidebar.tsx; ChatInterface.tsx","PUT /chat/conversations/{conversation_id}/read","Participant","Marks others' messages as read"
"Live Chat (1-to-1)","Deactivate conversation","LiveChatSidebar.tsx; ChatInterface.tsx","DELETE /chat/conversations/{conversation_id}","Participant","Soft delete"
"Live Chat (1-to-1)","Clear history (local)","LiveChatSidebar.tsx; ChatInterface.tsx","DELETE /chat/conversations/{conversation_id}/clear-history","Participant","Hides all messages for requester only"
"Live Chat (1-to-1)","Unread counts and messages","LiveChatSidebar.tsx","GET /chat/unread-messages","Authenticated","Returns total and per-conversation with messages"
"Live Chat (1-to-1)","Search messages (local)","LiveChatSidebar.tsx; ChatInterface.tsx","-","Authenticated","Client-side filter of loaded messages"
"Live Chat (1-to-1)","Online users list","LiveChatSidebar.tsx","GET /chat/users/online","Authenticated","Redis-backed presence; excludes self"
"Live Chat (1-to-1)","All users with status","LiveChatSidebar.tsx","GET /chat/users/all","Authenticated","Includes online flag and last_seen"
"Live Chat (1-to-1)","Find user","LiveChatSidebar.tsx","GET /chat/find-user/{username}","Authenticated","404 if not found"
"Live Chat (1-to-1)","Upload attachment","AttachmentButton.tsx","POST /chat/attachments","Authenticated","Uploads to MinIO; returns metadata"
"Live Chat (1-to-1)","Download attachment","AttachmentPreview.tsx","GET /chat/attachments/download?bucket_name=&object_key=&token=","Authenticated/Participant","Token or header; enforces membership"
"Live Chat (1-to-1)","Conversation media","MediaLinksDrawer.tsx","GET /chat/conversations/{conversation_id}/media?skip=&limit=","Participant","Paginated"
"Live Chat (1-to-1)","Conversation links","MediaLinksDrawer.tsx","GET /chat/conversations/{conversation_id}/links?skip=&limit=","Participant","Paginated"
"Live Chat (1-to-1)","MQTT broker token","ChatContext.tsx","GET /chat/mqtt-token","Authenticated","Returns JWT/host/ports for live transport"
"Live Chat (1-to-1)","Star/archive conversation (local)","LiveChatSidebar.tsx; ChatInterface.tsx","-","Authenticated","LocalStorage-only UI state"
"Group Chat","Create group","GroupCreationModal.tsx","POST /chat/groups/","Authenticated","Creator added as admin; invites sent"
"Group Chat","List my groups","LiveChatSidebar.tsx; ChatInterface.tsx","GET /chat/groups/?skip=&limit=","Member","Cached per user; includes members"
"Group Chat","Rename group","ChatInterface.tsx","PUT /chat/groups/{group_id}","Group admin","Only admin can rename"
"Group Chat","Delete group","ChatInterface.tsx","DELETE /chat/groups/{group_id}","Group admin","Soft delete; invalidates caches"
"Group Chat","Add members","AddMembersModal.tsx","POST /chat/groups/{group_id}/members","Group admin","Adds members; system message; notifies via MQTT"
"Group Chat","Remove member / Leave group","ViewMembersModal.tsx","DELETE /chat/groups/{group_id}/members/{user_id}","Admin/Member","Member can leave self; admin removes others"
"Group Chat","Get group messages","LiveChatSidebar.tsx; ChatInterface.tsx","GET /chat/groups/{group_id}/messages?skip=&limit=","Member","Excludes messages the user hid"
"Group Chat","Send group message","LiveChatSidebar.tsx; ChatInterface.tsx","POST /chat/groups/{group_id}/messages","Member","message_type text/image/file/system/shared_ai_response"
"Group Chat","Clear group history (local)","ChatInterface.tsx","DELETE /chat/groups/{group_id}/clear-history","Member","Hides messages for requester only"
"Group Chat","Archive group (local)","LiveChatSidebar.tsx; ChatInterface.tsx","-","Member","LocalStorage-only UI state"
"Notes","Create note","NotesModal.tsx","POST /notes","Authenticated","Optional response_id to link interaction; idempotent per user+interaction"
"Notes","List notes","NotesSidebarPanel.tsx","GET /notes?search=&page=&page_size=","Authenticated","Cached; search title/content"
"Notes","Get note","NotesSidebarPanel.tsx","GET /notes/{note_id}","Owner","Cached per user+id"
"Notes","Update note","NotesSidebarPanel.tsx","PUT /notes/{note_id}","Owner","Title required; trims; 255 max"
"Notes","Delete note","NotesSidebarPanel.tsx","DELETE /notes/{note_id}","Owner","Invalidates caches"
"Feedback & Sharing","Submit feedback","FeedbackWidget.tsx","POST /feedback/submit","Authenticated","Helpful→rating 6–10; Not helpful→1–5; validates"
"Feedback & Sharing","My feedback list","Profile/Activity","GET /feedback/my-feedback","Authenticated","Lists my feedback"
"Feedback & Sharing","Feedback stats","Admin dashboard","GET /feedback/stats","Admin","Aggregated metrics"
"Feedback & Sharing","Share AI response","Share UI","POST /feedback/share-response","Authenticated","Sends persistent chat/group message; MQTT notifications"
"Feedback & Sharing","Shared responses list","History","GET /feedback/shared-responses[?shared_to_type=user&shared_to_id=]","Authenticated","Shared by me plus to me (if filtered)"
"Feedback & Sharing","Export current chat","Export menu","POST /feedback/export-chat?format=json|csv|pdf","Authenticated","PDF requires reportlab; JSON/CSV always"
"Feedback & Sharing","Export AI history","Profile","GET /feedback/export-history?format=json|csv&limit=","Authenticated","Exports last N interactions"
"Dashboard & Activity","Admin summary stats","dashboard/DashboardSummary.tsx","GET /dashboard/summary-stats","Admin","Cached; user/queries/docs/feedback/shared/online"
"Dashboard & Activity","All user metrics","dashboard/UserMetricsList.tsx","GET /dashboard/user-metrics","Admin","Cached; per-user aggregates"
"Dashboard & Activity","Single user metrics","dashboard/UserMetricsModal.tsx","GET /dashboard/user-metrics/{user_id}","Admin or Same user","Permission guard for non-admin"
"Dashboard & Activity","My summary","activity/page.tsx","GET /dashboard/me/summary","Authenticated","Chats 7d/30d; notes; feedback; last active"
"Dashboard & Activity","My metrics","User menu/profile","GET /dashboard/me/metrics","Authenticated","Presence and last_seen included"
"Dashboard & Activity","My activity feed","activity/page.tsx; RecentActivityFeed.tsx","GET /dashboard/me/activity?limit=&offset=","Authenticated","Merged chat/note/feedback; paginated"
"Dashboard & Activity","Activity timeline","Admin dashboard","GET /dashboard/activity-timeline?days=","Admin","Max 365; cached"
"Tickets","Create ticket","TicketCreationModal.tsx","POST /tickets/","Authenticated","category in [bug,feature,support,other]; priority in [low,medium,high,urgent]"
"Tickets","My tickets","UserTicketsPanel.tsx","GET /tickets/my?page=&per_page=&status_filter=","Authenticated","Paginated; filter by status"
"Tickets","Ticket summary","admin/TicketsPanel.tsx","GET /tickets/summary","Admin","Cached counts by status"
"Tickets","List all tickets","admin/TicketsPanel.tsx","GET /tickets/?page=&per_page=&status_filter=&category_filter=&priority_filter=","Admin","Paginated; filters optional"
"Tickets","Update ticket","TicketModal.tsx","PUT /tickets/{ticket_id}","Owner or Admin","Non-admin cannot set admin fields or status; validates enums; resolved_at handling"
"User Deletion","Delete user account (preserve data)","admin/UserDeletionModal.tsx","DELETE /user-deletion/{user_id}/delete","Admin","Cannot delete admin or self; preserves documents/chats/notes/files"
"User Deletion","Deletion preview","admin/UserDeletionModal.tsx","GET /user-deletion/{user_id}/delete-preview","Admin","Shows what will be deleted; same guards"
"Prompt Gallery","Prompt gallery panel","promptGallery.tsx","-","Authenticated","Open via 'open-prompt-gallery' event; resizable; categories"
"Prompt Gallery","Quick insert prompt","promptGallery.tsx","-","Authenticated","Click inserts text into composer via onSelect"
"Search Modes","Web search toggle","WebSearchToggle.tsx","(via POST /agents/query)","Authenticated","Mutually exclusive with other modes; backend validation 400"
"Search Modes","Document search toggle","DocumentSearchToggle.tsx","(via POST /agents/query)","Authenticated","Supports allowed_files; mutually exclusive"
"Search Modes","Deep search toggle","DeepSearchToggle.tsx","(via POST /agents/query)","Authenticated","Mutually exclusive"
"Search Modes","Sources rendering","SourcesDisplay.tsx; SharedResponseDisplay.tsx","-","Authenticated","Renders web/doc chunk sources"
"Static Pages","Home","app/page.tsx","-","Public","App landing"
"Static Pages","About","app/about/page.tsx","-","Public","Static page"
"Static Pages","FAQ","app/faq/page.tsx","-","Public","Static from faq_data.ts"
"Static Pages","What’s New","app/whats-new/page.tsx","-","Public","Reads public changelogs"
"Static Pages","Activity","app/activity/page.tsx","-","Authenticated","Uses dashboard me APIs"
"Static Pages","Account Requests","app/account-requests/page.tsx","-","Admin","Admin panel"
"Static Pages","Chat","app/chat/page.tsx","-","Authenticated","Main workspace"
"Static Pages","Dashboard","app/dashboard/page.tsx","-","Admin","Admin dashboard"
