# Frontend Architecture Structure

## Current Status: Phase 2 Complete - Barrel Exports Implementation

frontend/src/
│
├── app/                              # [CURRENT] Next.js 13+ App Router
│   ├── (protected)/                 # Protected routes (requires auth)
│   │   ├── chat/page.tsx
│   │   ├── dashboard/page.tsx
│   │   └── activity/page.tsx
│   │
│   ├── (unprotected)/               # Public routes
│   │   ├── login/page.tsx
│   │   ├── about/page.tsx
│   │   ├── faq/page.tsx
│   │   └── whats-new/page.tsx
│   │
│   ├── layout.tsx                   # Root layout
│   ├── page.tsx                     # Home page
│   ├── globals.css                  # Global styles
│   └── icon.ico                     # Favicon
│
├── components/                       # React components
│   │
│   ├── ai-chat/                     # [CURRENT] AI chat components with barrel export
│   │   ├── ChatBox.tsx
│   │   ├── ChatInput.tsx
│   │   ├── ChatSidebar.tsx
│   │   ├── ChatHistoryManager.tsx
│   │   ├── ChatPageContent.tsx
│   │   ├── AgentSelector.tsx
│   │   ├── PromptGallery.tsx
│   │   ├── FeedbackWidget.tsx
│   │   ├── SourcesDisplay.tsx
│   │   ├── ReferenceParser.tsx
│   │   ├── SharedResponseDisplay.tsx
│   │   └── index.ts
│   │
│   ├── live-chat/                   # [CURRENT] Live chat components
│   │   ├── ChatInterface.tsx
│   │   ├── LiveChatSidebar.tsx
│   │   ├── MessageActions.tsx
│   │   ├── EmojiPicker.tsx
│   │   ├── AttachmentButton.tsx
│   │   ├── AttachmentModal.tsx
│   │   ├── AttachmentPreview.tsx
│   │   ├── MediaLinksDrawer.tsx
│   │   ├── GroupCreationModal.tsx
│   │   ├── AddMembersModal.tsx
│   │   ├── ViewMembersModal.tsx
│   │   └── index.ts                # [NEED TO DO- PLANNED] Add barrel export
│   │
│   ├── KnowledgeBaseSidebar.tsx    # [CURRENT] Refactored, uses api/knowledge-base
│   ├── FileSelector.tsx             # [CURRENT] Refactored, uses api/knowledge-base
│   │# [NEED TO DO- PLANNED] Move to knowledge/ folder Hooks and Context Creation
│   │
│   ├── notes/                       # [CURRENT] Notes components with barrel export
│   │   ├── NotesModal.tsx
│   │   ├── NotesSidebarPanel.tsx
│   │   ├── NoteContentPreview.tsx
│   │   ├── NoteFullPreviewModal.tsx
│   │   ├── NoteSourcesList.tsx
│   │   └── index.ts
│   │
│   ├── tickets/                     # [CURRENT] Tickets components with barrel export
│   │   ├── TicketCard.tsx
│   │   ├── TicketList.tsx
│   │   ├── TicketModal.tsx
│   │   ├── TicketCreationModal.tsx
│   │   ├── TicketFilters.tsx
│   │   ├── TicketStatusBadge.tsx
│   │   ├── TicketEmptyState.tsx
│   │   ├── UserTicketsPanel.tsx
│   │   └── index.ts
│   │
│   ├── dashboard/                   # [COMPLETE] Dashboard components with barrel export
│   │   ├── DashboardSummary.tsx
│   │   ├── RecentActivityFeed.tsx
│   │   ├── UserMetricsCard.tsx
│   │   ├── UserMetricsList.tsx
│   │   ├── UserMetricsModal.tsx
│   │   ├── UserOverviewGrid.tsx
│   │   └── index.ts
│   │
│   ├── admin/                       # [COMPLETE] Admin components with barrel export
│   │   ├── AccountRequestCard.tsx
│   │   ├── AccountRequestModal.tsx
│   │   ├── AccountRequestsPanel.tsx
│   │   ├── AddUserModal.tsx
│   │   ├── TicketsPanel.tsx
│   │   ├── UserDeletionModal.tsx
│   │   └── index.ts
│   │
│   ├── profile/                     # [CURRENT] Profile components with barrel export
│   │   ├── ProfileModal.tsx
│   │   ├── UserProfileMenu.tsx
│   │   └── index.ts
│   │
│   ├── toggles/                     # [CURRENT] Search toggle components with barrel export
│   │   ├── WebSearchToggle.tsx
│   │   ├── DocumentSearchToggle.tsx
│   │   ├── DeepSearchToggle.tsx
│   │   └── index.ts
│   │
│   ├── notifications/               # [COMPLETE] Notification components with barrel export
│   │   ├── NotificationBadge.tsx
│   │   ├── Toast.tsx
│   │   ├── ToastContainer.tsx
│   │   └── index.ts
│   │
│   ├── layout/                      # [COMPLETE] Layout components with barrel export
│   │   ├── MainLayout.tsx
│   │   ├── Header.tsx
│   │   ├── Footer.tsx
│   │   └── index.ts
│   │
│   ├── ui/                          # [COMPLETE] Reusable UI components with barrel export
│   │   ├── Badge.tsx
│   │   ├── EmptyState.tsx
│   │   ├── ErrorBanner.tsx
│   │   ├── LoadingSpinner.tsx
│   │   ├── SearchToggle.tsx
│   │   ├── Tabs.tsx
│   │   └── index.ts
│   │
│   ├── ErrorBoundary.tsx            # [CURRENT] Error boundary wrapper
│   └── Providers.tsx                # [CURRENT] Context providers wrapper
│
├── contexts/                         # [COMPLETE] React context providers with barrel export
│   ├── ChatContext.tsx              # AI chat state management
│   ├── Livechatcontext.tsx          # Live chat state management
│   ├── NotificationContext.tsx      # Notifications state
│   ├── TicketContext.tsx            # Tickets state management
│   └── index.ts
│
├── hooks/                            # [COMPLETE] Custom React hooks with barrel export
│   ├── useAgents.ts                 # Agent operations hook
│   ├── useBlobUrl.ts                # Blob URL management
│   ├── useNoteModal.ts              # Notes modal state
│   ├── useNotes.ts                  # Notes CRUD operations
│   ├── useSecureAuth.ts             # Authentication utilities
│   ├── useTicketActions.ts          # Ticket operations
│   ├── useTickets.ts                # Tickets data management
│   ├── useTicketSummary.ts          # Ticket summary data
│   └── index.ts
│
├── api/                              # API service layer
│   ├── knowledge-base.ts            # [COMPLETE] 17 functions - KB API
│   ├── notes.ts                     # [CURRENT] Notes API
│   ├── index.ts                     # [CURRENT] Barrel exports
│   ├── tickets.ts                   # [NEED TO DO- PLANNED] Tickets API consolidation
│   └── agents.ts                    # [NEED TO DO- PLANNED] Agents API consolidation
│
├── lib/                              # [CURRENT] Core utilities and helpers
│   ├── api.ts                       # Core API client and legacy functions
│   ├── mqttManager.ts               # MQTT connection management
│   └── utils.ts                     # [NEED TO DO- PLANNED] Common utility functions
│
├── types/                            # TypeScript type definitions
│   ├── index.ts                     # [CURRENT] Barrel exports and user types
│   ├── knowledge-base.ts            # [COMPLETE] 8 interfaces - KB types
│   ├── note.ts                      # [CURRENT] Notes type definitions
│   ├── ticket.ts                    # [CURRENT] Tickets type definitions
│   └── chat.ts                      # [NEED TO DO- PLANNED] Chat types consolidation
│
└── data/                             # [CURRENT] Static data and constants
    ├── faq_data.ts                  # FAQ content
    └── prompts.ts                   # AI prompt templates