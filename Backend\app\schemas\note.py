from typing import Optional, Any, List
from pydantic import BaseModel, Field, validator
from datetime import datetime


class NoteBase(BaseModel):
    title: str = Field(..., max_length=255)
    combined_text: Optional[str] = None
    extra_text: Optional[str] = None
    sources: Optional[Any] = None
    # agent_type removed
    session_id: Optional[str] = Field(None, max_length=100)
    # tags removed
    # pin/archive removed


class NoteCreate(NoteBase):
    response_id: Optional[str] = Field(None, description="UUID of the AI response to link")
    interaction_id: Optional[int] = Field(None, description="Direct interaction id if known")

    @validator('title')
    def validate_title(cls, v: str) -> str:
        v = (v or '').strip()
        if not v:
            raise ValueError('Title is required')
        return v


class NoteUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=255)
    extra_text: Optional[str] = None
    combined_text: Optional[str] = None
    # tags removed


class NoteResponse(BaseModel):
    id: int
    user_id: int
    interaction_id: Optional[int]
    title: str
    combined_text: Optional[str]
    extra_text: Optional[str]
    sources: Optional[Any]
    # agent_type removed
    session_id: Optional[str]
    # tags removed
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


