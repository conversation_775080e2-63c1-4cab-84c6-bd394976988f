/**
 * Hook for managing note modal state and logic
 */

import { useState, useEffect, useMemo, useCallback } from 'react';
import { NotePreset, CreateNotePayload, ParsedContent } from '@/types/note';
import { createNote } from '@/api/notes';

interface UseNoteModalProps {
  isOpen: boolean;
  preset?: NotePreset;
  onSaved?: (noteId: number) => void;
  onClose: () => void;
}

export function useNoteModal({ isOpen, preset, onSaved, onClose }: UseNoteModalProps) {
  const [title, setTitle] = useState<string>('');
  const [extraText, setExtraText] = useState<string>('');
  const [combined, setCombined] = useState<string>('');
  const [saving, setSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isFullPreviewOpen, setIsFullPreviewOpen] = useState<boolean>(false);

  // Extract source links (URLs only) from preset.sources
  const sourceLinks = useMemo(() => {
    try {
      const raw = preset?.sources as any;
      const urls: string[] = [];
      
      const visit = (item: any) => {
        if (Array.isArray(item)) return item.forEach(visit);
        if (typeof item === 'string') {
          if (/^https?:\/\//i.test(item)) urls.push(item);
          return;
        }
        if (item && typeof item === 'object' && typeof item.url === 'string' && /^https?:\/\//i.test(item.url)) {
          urls.push(item.url);
        }
      };
      
      visit(raw);
      return Array.from(new Set(urls));
    } catch {
      return [] as string[];
    }
  }, [preset]);

  // Reset form when modal opens
  useEffect(() => {
    if (!isOpen) return;
    
    setError(null);
    setSaving(false);
    setTitle(preset?.title || deriveTitleFromCombined(preset?.combinedText || ''));
    setExtraText('');
    setCombined(preset?.combinedText || '');
    setIsEditing(false);
  }, [isOpen, preset]);

  // Validation
  const canSave = useMemo(() => {
    return Boolean(title && combined.trim());
  }, [title, combined]);

  // Parse combined text into query and answer
  const parsedContent = useMemo((): ParsedContent => {
    return parseCombined(combined);
  }, [combined]);

  // Save note
  const handleSave = useCallback(async () => {
    if (!canSave || saving) return;
    
    setSaving(true);
    setError(null);

    const payload: CreateNotePayload = {
      response_id: preset?.responseId,
      interaction_id: preset?.interactionId,
      title: (title || '').slice(0, 255),
      extra_text: extraText || undefined,
      combined_text: combined,
      sources: preset?.sources,
      agent_type: preset?.agentType,
      session_id: preset?.sessionId,
    };

    try {
      const note = await createNote(payload);
      onSaved?.(note.id);
      onClose();
    } catch (e: any) {
      setError(e?.message || 'Failed to save note');
    } finally {
      setSaving(false);
    }
  }, [canSave, saving, preset, title, extraText, combined, onSaved, onClose]);

  return {
    // Form state
    title,
    setTitle,
    extraText,
    setExtraText,
    combined,
    setCombined,
    
    // UI state
    saving,
    error,
    isEditing,
    setIsEditing,
    isFullPreviewOpen,
    setIsFullPreviewOpen,
    
    // Computed values
    canSave,
    sourceLinks,
    parsedContent,
    
    // Actions
    handleSave,
  };
}

// Utility functions
function deriveTitleFromCombined(combined: string): string {
  const base = combined.trim();
  return base.length > 60 ? `${base.slice(0, 60)}…` : base;
}

function parseCombined(combined: string): ParsedContent {
  try {
    const userMatch = combined.match(/(^|\n)User:\n([\s\S]*?)(\n\n|$)/);
    const aiMatch = combined.match(/(^|\n)AI:\n([\s\S]*?)$/);
    const query = userMatch ? userMatch[2].trim() : '';
    const answer = aiMatch ? aiMatch[2].trim() : '';
    return { query, answer };
  } catch {
    return { query: '', answer: '' };
  }
}
