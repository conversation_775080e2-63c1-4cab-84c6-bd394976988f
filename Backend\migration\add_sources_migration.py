#!/usr/bin/env python3
"""
Database migration to add sources field to expert_chat_interactions table
"""

import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.config import DATABASE_URL

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_add_sources_field():
    """Add sources JSON field to expert_chat_interactions table"""
    
    if not DATABASE_URL:
        logger.error("DATABASE_URL not found in environment variables")
        return False
    
    try:
        logger.info(f"Using DATABASE_URL for migration: {DATABASE_URL}")
        # Create engine and session
        engine = create_engine(DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        logger.info("Starting migration: Adding sources field to expert_chat_interactions")
        
        # Check if column already exists
        check_column_sql = """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'expert_chat_interactions' 
        AND column_name = 'sources'
        """
        
        result = db.execute(text(check_column_sql)).fetchone()
        
        if result:
            logger.info("Sources column already exists, skipping migration")
            return True
        
        # Add the sources column
        alter_sql = """
        ALTER TABLE expert_chat_interactions 
        ADD COLUMN sources JSON NULL
        """
        
        db.execute(text(alter_sql))
        db.commit()
        
        logger.info("✅ Successfully added sources column to expert_chat_interactions table")
        
        # Verify the column was added
        verify_sql = """
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'expert_chat_interactions' 
        AND column_name = 'sources'
        """
        
        result = db.execute(text(verify_sql)).fetchone()
        if result:
            logger.info(f"✅ Verification successful: sources column added with type {result[1]}")
        else:
            logger.error("❌ Verification failed: sources column not found after migration")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        if 'db' in locals():
            db.rollback()
        return False
        
    finally:
        if 'db' in locals():
            db.close()

if __name__ == "__main__":
    print("🔄 Starting database migration...")
    print("Adding sources field to expert_chat_interactions table")
    
    success = migrate_add_sources_field()
    
    if success:
        print("✅ Migration completed successfully!")
        print("Web sources will now be stored permanently in the database.")
    else:
        print("❌ Migration failed. Please check the logs above.")
        exit(1)
