"""
Deep Research Tool Package
A comprehensive AI-powered research and analysis tool with multi-agent orchestration.

This package provides an intelligent research pipeline that can:
- Analyze and reformulate user queries
- Route queries to appropriate specialized agents
- Orchestrate multi-agent teams for comprehensive research
- Synthesize results from multiple sources
- Generate clear, actionable responses

Main Classes:
- RouterAgent: Analyzes queries and routes them to appropriate agents
- EnhancedSearchPipeline: Orchestrates the complete research process
- DeepResearchTool: High-level interface for the entire system
"""

from .deep_research_tool import RouterAgent, QueryResponse, QueryAnalysis, AgentSelection
from .pipeline import EnhancedSearchPipeline
from .agents_config import *
import logging
import traceback
from typing import Dict, Any, List
from datetime import datetime

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',

)
logger = logging.getLogger(__name__)

__version__ = "1.0.0"


class DeepResearchTool:
    """
    High-level interface for the Deep Research Tool.
    
    This class provides a simple API for conducting comprehensive research
    using the multi-agent system.
    """
    
    def __init__(self, debug: bool = False):
        """
        Initialize the Deep Research Tool.
        
        Args:
            debug: Enable debug logging for detailed operation tracking
        """
        print(f"[DEBUG] Initializing DeepResearchTool with debug={debug}")
        self.debug = debug
        self.tool_results_dict = {}
        self.agent_reasoning = []
        
        try:
            print("[DEBUG] Creating RouterAgent...")
            self.router = RouterAgent(debug=debug)
            print("[DEBUG] ✓ RouterAgent created successfully")
            
            print("[DEBUG] Creating EnhancedSearchPipeline...")
            self.pipeline = EnhancedSearchPipeline(self.router, self.tool_execution_hook)
            print("[DEBUG] ✓ EnhancedSearchPipeline created successfully")
            
            print("[DEBUG] ✓ Deep Research Tool initialized successfully")
            logger.info("Deep Research Tool initialized")
            
        except Exception as e:
            error_msg = f"Failed to initialize Deep Research Tool: {e}"
            print(f"[ERROR] {error_msg}")
            logger.error(error_msg)
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
            raise RuntimeError(error_msg)
    
    def tool_execution_hook(self, function_name: str, function_call, arguments: Dict[str, Any]):
        """Hook that captures tool execution and results for deep research"""
        from datetime import datetime
        
        logger.info(f"🔧 Deep Research Tool: {function_name} with args: {arguments}")
        
        # Record start time
        start_time = datetime.now()
        
        # Execute the actual function
        try:
            result = function_call(**arguments)
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # Store the raw result in our instance dictionary
            tool_call_id = f"{function_name}_{start_time.timestamp()}"
            self.tool_results_dict[tool_call_id] = {
                'function_name': function_name,
                'arguments': arguments,
                'result': result,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'execution_time_seconds': execution_time,
                'status': 'success'
            }
            
            logger.info(f"✅ Deep Research Tool: {function_name} completed successfully")
            return result
            
        except Exception as e:
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # Store error information
            tool_call_id = f"{function_name}_{start_time.timestamp()}_error"
            self.tool_results_dict[tool_call_id] = {
                'function_name': function_name,
                'arguments': arguments,
                'error': str(e),
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'execution_time_seconds': execution_time,
                'status': 'error'
            }
            
            logger.error(f"❌ Deep Research Tool: {function_name} failed with error: {e}")
            raise
    
    def research(self, query: str, progress_callback=None) -> str:
        """
        Conduct comprehensive research on a query.
        
        Args:
            query: The research question or task
            progress_callback: Optional callback function for progress updates
            
        Returns:
            Comprehensive research response as a string
        """
        print(f"[DEBUG] ========== STARTING RESEARCH ==========")
        print(f"[DEBUG] Query: {query}")
        logger.info(f"Starting research for query: {query}")
        
        # Clear previous results for new query
        self.tool_results_dict = {}
        self.agent_reasoning = []
        
        try:
            # 🔬 DEEP RESEARCH CANCELLATION CHECK 1
            if hasattr(self, '_cancellation_event') and self._cancellation_event and self._cancellation_event.is_set():
                print("[DEBUG] 🔬 DEEP RESEARCH CANCELLED: Before start")
                logger.info("🔬 Deep Research: Query cancelled before initialization")
                from ..agents.base import QueryCancelledException
                raise QueryCancelledException()
            
            if progress_callback:
                progress_callback(f"We are processing '{query}'")
            
            # 🔬 DEEP RESEARCH CANCELLATION CHECK 2
            if hasattr(self, '_cancellation_event') and self._cancellation_event and self._cancellation_event.is_set():
                print("[DEBUG] 🔬 DEEP RESEARCH CANCELLED: After progress callback")
                logger.info("🔬 Deep Research: Query cancelled after progress callback")
                from ..agents.base import QueryCancelledException
                raise QueryCancelledException()
            
            print("[DEBUG] Running complete pipeline...")
            logger.info("🔬 Deep Research: Starting pipeline execution")
            
            if progress_callback:
                progress_callback("Report being generated, We will notify you")
            
            # 🔬 DEEP RESEARCH CANCELLATION CHECK 3
            if hasattr(self, '_cancellation_event') and self._cancellation_event and self._cancellation_event.is_set():
                print("[DEBUG] 🔬 DEEP RESEARCH CANCELLED: Before pipeline setup")
                logger.info("🔬 Deep Research: Query cancelled before pipeline setup")
                from ..agents.base import QueryCancelledException
                raise QueryCancelledException()
            
            # Set up the pipeline to capture detailed execution information
            self.pipeline.set_capture_callback(self._capture_agent_execution)
            
            # Pass cancellation event to router and pipeline
            if hasattr(self, '_cancellation_event'):
                self.router.cancellation_event = self._cancellation_event
                self.pipeline.cancellation_event = self._cancellation_event
                logger.info("🔬 Deep Research: Cancellation event passed to router and pipeline")
            
            # 🔬 DEEP RESEARCH CANCELLATION CHECK 4
            if hasattr(self, '_cancellation_event') and self._cancellation_event and self._cancellation_event.is_set():
                print("[DEBUG] 🔬 DEEP RESEARCH CANCELLED: Before pipeline execution")
                logger.info("🔬 Deep Research: Query cancelled before pipeline execution")
                from ..agents.base import QueryCancelledException
                raise QueryCancelledException()
            
            logger.info("🔬 Deep Research: Calling pipeline.run_pipeline()")
            # Run the complete pipeline with cancellation event
            response = self.pipeline.run_pipeline(query, getattr(self, '_cancellation_event', None))
            logger.info("🔬 Deep Research: Pipeline execution completed")
            
            # Extract the content from the response
            if hasattr(response, 'content'):
                result = response.content
                print(f"[DEBUG] ✓ Research completed successfully")
                print(f"[DEBUG] Response length: {len(result)} characters")
                
                # Capture reasoning if available
                if hasattr(response, 'reasoning_content') and response.reasoning_content:
                    self.agent_reasoning.append({
                        'agent': 'Final Pipeline Response',
                        'reasoning': response.reasoning_content
                    })
                    print(f"[DEBUG] Captured reasoning from final pipeline response")
                    
            else:
                result = str(response)
                print(f"[DEBUG] ✓ Research completed (converted to string)")
                print(f"[DEBUG] Response length: {len(result)} characters")
            
            if progress_callback:
                progress_callback("Research completed! Processing results...")
                
            # Check for cancellation before processing results
            if hasattr(self, '_cancellation_event') and self._cancellation_event and self._cancellation_event.is_set():
                print("[DEBUG] Deep research cancelled before processing results")
                from ..agents.base import QueryCancelledException
                raise QueryCancelledException()
            
            # Extract additional information from pipeline
            try:
                pipeline_summary = self.pipeline.get_pipeline_summary()
                if pipeline_summary.get('execution_details'):
                    for detail in pipeline_summary['execution_details']:
                        if detail.get('agent_name') and (detail.get('reasoning') or detail.get('content')):
                            reasoning_entry = {
                                'agent_name': detail['agent_name'],
                                'agent': detail['agent_name'],  # Keep both for compatibility
                                'reasoning': detail.get('reasoning') or detail.get('content', 'No reasoning content'),
                                'content': detail.get('content'),
                                'response_type': detail.get('response_type'),
                                'has_reasoning': detail.get('has_reasoning', False),
                                'tool_calls': len([k for k in self.tool_results_dict.keys() if detail['agent_name'] in k])
                            }
                            
                            # Add tool results for this agent
                            agent_tool_results = []
                            for tool_id, tool_result in self.tool_results_dict.items():
                                if detail['agent_name'] in tool_id or (tool_result.get('agent') == detail['agent_name']):
                                    agent_tool_results.append(tool_result)
                            
                            if agent_tool_results:
                                reasoning_entry['tool_results'] = agent_tool_results
                            
                            self.agent_reasoning.append(reasoning_entry)
                            print(f"[DEBUG] Added reasoning entry for {detail['agent_name']}")
            except Exception as e:
                print(f"[DEBUG] Could not extract pipeline summary: {e}")
            
            logger.info("Research completed successfully")
            print(f"[DEBUG] ========== RESEARCH COMPLETED ==========")
            print(f"[DEBUG] Captured {len(self.tool_results_dict)} tool executions")
            print(f"[DEBUG] Captured {len(self.agent_reasoning)} agent reasoning entries")
            return result
            
        except Exception as e:
            if hasattr(e, 'is_cancelled') and e.is_cancelled:
                logger.info("🔬 Deep Research: Research cancelled by user (expected behavior)")
                return "Query was cancelled by user."
            else:
                error_msg = f"Error during research: {e}"
                print(f"[ERROR] {error_msg}")
                logger.error(error_msg)
                print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                
                if progress_callback:
                    progress_callback(f"Research encountered an error: {error_msg}")
                
                # Return a helpful error message instead of raising
                fallback_response = f"""
                I encountered an error while conducting the research: {error_msg}
                
                This might be due to:
                - Network connectivity issues
                - API rate limits
                - Model availability issues
                
                Please try again in a few moments. If the problem persists, please contact support.
                
                Original query: {query}
                """
                
                print(f"[DEBUG] Returning fallback response due to error")
                return fallback_response.strip()
    
    def _capture_agent_execution(self, agent_name: str, response_obj, query: str):
        """
        Callback to capture individual agent execution details.
        
        Args:
            agent_name: Name of the agent
            response_obj: Response object from agent execution
            query: Query that was processed
        """
        print(f"[DEBUG] Capturing execution from agent: {agent_name}")
        
        try:
            # Capture reasoning content if available
            reasoning_content = None
            content = None
            
            if hasattr(response_obj, 'reasoning_content') and response_obj.reasoning_content:
                reasoning_content = response_obj.reasoning_content
                print(f"[DEBUG] Found reasoning_content from {agent_name}")
            
            if hasattr(response_obj, 'content') and response_obj.content:
                content = response_obj.content
                print(f"[DEBUG] Found content from {agent_name}")
            
            # Create reasoning entry if we have any content
            if reasoning_content or content:
                reasoning_entry = {
                    'agent_name': agent_name,
                    'agent': agent_name,  # Keep both for compatibility
                    'reasoning': reasoning_content or content,
                    'content': content,
                    'response_type': type(response_obj).__name__,
                    'has_reasoning': bool(reasoning_content),
                    'query': query
                }
                
                # Count tool calls for this agent
                tool_count = len([k for k in self.tool_results_dict.keys() if agent_name in k])
                reasoning_entry['tool_calls'] = tool_count
                
                self.agent_reasoning.append(reasoning_entry)
                print(f"[DEBUG] ✓ Captured reasoning entry from {agent_name}")
            
            # Capture tool calls if available
            if hasattr(response_obj, 'tool_calls') and response_obj.tool_calls:
                for i, tool_call in enumerate(response_obj.tool_calls):
                    tool_id = f"{agent_name}_{tool_call.get('name', 'unknown_tool')}_{i}_{len(self.tool_results_dict)}"
                    self.tool_results_dict[tool_id] = {
                        'function_name': tool_call.get('name', 'unknown_tool'),
                        'arguments': tool_call.get('arguments', {}),
                        'result': tool_call.get('result', 'No result'),
                        'agent': agent_name,
                        'status': 'success',
                        'start_time': datetime.now().isoformat(),
                        'execution_time_seconds': 0.0  # Placeholder since we don't have timing info
                    }
                print(f"[DEBUG] Captured {len(response_obj.tool_calls)} tool calls from {agent_name}")
            
        except Exception as e:
            print(f"[DEBUG] Error capturing execution from {agent_name}: {e}")
            print(f"[DEBUG] Response object type: {type(response_obj)}")
            print(f"[DEBUG] Response object attributes: {dir(response_obj)}")
            
            # Add agent reasoning with error note but still try to get some content
            try:
                fallback_content = str(response_obj) if response_obj else "No response content"
                self.agent_reasoning.append({
                    'agent_name': agent_name,
                    'agent': agent_name,
                    'reasoning': f"Agent executed but reasoning capture failed: {e}. Response: {fallback_content[:200]}{'...' if len(fallback_content) > 200 else ''}",
                    'content': fallback_content,
                    'has_reasoning': False,
                    'tool_calls': 0,
                    'error': str(e)
                })
            except Exception as e2:
                print(f"[DEBUG] Failed to create fallback reasoning entry: {e2}")
                self.agent_reasoning.append({
                    'agent_name': agent_name,
                    'agent': agent_name,
                    'reasoning': f"Agent execution capture completely failed: {e}",
                    'has_reasoning': False,
                    'tool_calls': 0,
                    'error': str(e)
                })
    
    def get_tool_results(self) -> Dict[str, Any]:
        """
        Get the tool results from the last research execution.
        
        Returns:
            Dictionary with tool execution results
        """
        return self.tool_results_dict
    
    def get_agent_reasoning(self) -> List[Dict[str, Any]]:
        """
        Get the reasoning content from agents used in the last research execution.
        
        Returns:
            List of dictionaries with agent reasoning
        """
        return self.agent_reasoning
    
    def get_pipeline_summary(self) -> dict:
        """
        Get a summary of the last pipeline execution.
        
        Returns:
            Dictionary with pipeline execution summary
        """
        print("[DEBUG] Getting pipeline summary...")
        
        try:
            summary = self.pipeline.get_pipeline_summary()
            print(f"[DEBUG] ✓ Pipeline summary retrieved")
            print(f"[DEBUG] Summary keys: {list(summary.keys())}")
            return summary
            
        except Exception as e:
            error_msg = f"Error getting pipeline summary: {e}"
            print(f"[ERROR] {error_msg}")
            logger.error(error_msg)
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
            raise e

def run_example():
    """
    Example function showing how to use the Deep Research Tool.
    """
    print("[DEBUG] ========== RUNNING EXAMPLE ==========")
    
    try:
        # Initialize the tool
        print("[DEBUG] Initializing DeepResearchTool for example...")
        tool = DeepResearchTool(debug=True)
        
        # Example simple query
        simple_query = "What are the latest developments in quantum computing?"
        
        print(f"Processing query: {simple_query}\n")
        print("="*80)
        
        try:
        
            print("[DEBUG] Running full research...")
            response = tool.research(simple_query)
            
            # Display results
            print("\n" + "="*80)
            print("RESEARCH RESULTS:")
            print("="*80)
            print(response)
            
            # Show pipeline summary
            print("\n" + "="*80)
            print("PIPELINE SUMMARY:")
            print("="*80)
            summary = tool.get_pipeline_summary()
            for key, value in summary.items():
                if value is not None:
                    print(f"{key}: {value}")
                    
            print("[DEBUG] ✓ Example completed successfully")
            return response
                    
        except Exception as e:
            error_msg = f"Error in research example: {e}"
            print(f"[ERROR] {error_msg}")
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
            return None
            
    except Exception as e:
        error_msg = f"Error in example setup: {e}"
        print(f"[ERROR] {error_msg}")
        print(f"[DEBUG] Traceback: {traceback.format_exc()}")
        print("Note: This example requires the agno package and API keys to be set up.")
        return None

# Export main classes and functions
__all__ = [
    'DeepResearchTool',
    'RouterAgent', 
    'EnhancedSearchPipeline',
    'QueryResponse',
    'QueryAnalysis', 
    'AgentSelection',
    'run_example'
] 