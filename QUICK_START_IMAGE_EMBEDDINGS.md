# Quick Start Guide: Image Embeddings

## What Was Added

Your codebase now has complete image embedding capabilities using SigLIP-2:

### 3 New Files Created:
1. `Data_Ingestion_Service/image_embedding_client.py` - Image embedding client for ingestion
2. `Data_Retriever_Service/image_embedding_client.py` - Text embedding client for retrieval
3. `Data_Retriever_Service/image_retrieval.py` - Complete image retrieval system

### 4 Files Modified:
1. `Data_Ingestion_Service/pipeline.py` - Added ImageEmbeddingClient initialization
2. `Data_Retriever_Service/config.py` - Added image_embedding_service_url config
3. `Data_Retriever_Service/agent_pipeline.py` - Added ImageRetriever initialization
4. `Backend/app/utils/image_processing.py` - Enhanced Windows compatibility

### 5 Files Cleaned Up:
Removed duplicate files from root directory after moving to correct locations.

## How It Works

### Step 1: Document Upload
When you upload a PDF/DOC/etc:
```python
# Automatically happens in pipeline.py
1. PyMuPDF extracts images from document
2. SigLIP-2 generates embeddings (1152-dim)
3. SigLIP-2 generates captions for images
4. Images stored in Qdrant + PostgreSQL
```

### Step 2: Image Search
Search for images using natural language:
```python
# Example usage in agent
images = self.image_retriever.retrieve_images_by_text(
    query="show me charts and graphs",
    collection_name="my_collection",
    top_k=5
)

# Returns:
# [
#   {
#     'id': 'uuid',
#     'score': 0.85,
#     'caption': 'A bar chart showing sales data',
#     'page_number': 3,
#     'source': 'report.pdf'
#   },
#   ...
# ]
```

## Configuration

### SigLIP-2 Service URL
Default: `http://**************:8019`

To change:
1. **For Ingestion**: Edit `Data_Ingestion_Service/pipeline.py` line 214
2. **For Retrieval**: Set `image_embedding_service_url` in `RetrieverConfig`

### Database Configuration
Ensure `database_url` is set in your config for PostgreSQL image storage.

## Testing

### 1. Test SigLIP-2 Service Health
```python
from Data_Ingestion_Service.image_embedding_client import ImageEmbeddingClient

client = ImageEmbeddingClient()
health = client.health_check()
print(health)
# Expected: {'status': 'healthy', 'model': 'SigLIP-2', 'device': 'cuda'}
```

### 2. Test Image Retrieval
```python
from Data_Retriever_Service.image_retrieval import ImageRetriever
from qdrant_client import QdrantClient

qdrant_client = QdrantClient(url="your_qdrant_url")
retriever = ImageRetriever(
    image_embedding_service_url="http://**************:8019",
    qdrant_client=qdrant_client,
    database_url="your_db_url"
)

images = retriever.retrieve_images_by_text(
    query="flowchart",
    collection_name="your_collection",
    top_k=3
)
print(f"Found {len(images)} images")
```

### 3. Test Full Pipeline
```python
# Upload a PDF with images through your normal upload endpoint
# The pipeline will automatically:
# - Extract images
# - Generate embeddings
# - Store in Qdrant
# - Store base64 in PostgreSQL

# Then search:
# images = agent.image_retriever.retrieve_images_by_text("diagram", collection_name)
```

## Features

### Automatic Image Processing
- Extracts images from PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX
- Filters out small images (<100x100 pixels)
- Preserves metadata: page number, bounding box, dimensions

### Cross-Modal Search
- Search images using natural language text
- Example queries:
  - "show me charts and graphs"
  - "find diagrams explaining the process"
  - "images with tables"
  - "photos of products"

### Smart Storage
- **Qdrant**: Fast vector search with embeddings
- **PostgreSQL**: Raw base64 image data (fetched only when needed)
- Efficient: Embeddings + metadata in Qdrant, large data in PostgreSQL

### Graceful Degradation
- If SigLIP-2 service is unavailable:
  - Ingestion: Logs error and continues (no images indexed)
  - Retrieval: Agent continues without image search capability

## Architecture

```
Document → PyMuPDF → Images → SigLIP-2 → Embeddings → Qdrant
                                        → Captions  → PostgreSQL (with base64)

Query Text → SigLIP-2 → Text Embedding → Search Qdrant → Results
                                                       → Fetch base64 (optional)
```

## Troubleshooting

### Issue: "Failed to connect to Image Embedding Service"
**Solution**: Ensure SigLIP-2 service is running at configured URL

### Issue: "No images found in search"
**Solution**: 
1. Check if images were extracted during ingestion (check logs)
2. Verify Qdrant collection has 'image' vector
3. Ensure image type filter is working

### Issue: "Images not appearing in results"
**Solution**: Use `retrieve_images_with_base64()` to fetch base64 data from PostgreSQL

### Issue: "Collection schema error"
**Solution**: Collection needs 'image' vector. Pipeline will recreate collection automatically.

## Performance Tips

1. **Batch Processing**: SigLIP-2 supports batch embedding generation
2. **Lazy Base64 Loading**: Only fetch base64 when displaying images
3. **Filter Small Images**: Pipeline already filters <100x100 images
4. **Monitor Service**: Check SigLIP-2 service health regularly

## Next Steps

1. Verify SigLIP-2 service is running
2. Upload a test document with images
3. Check logs for image extraction
4. Test image search with queries
5. Verify images appear in Qdrant and PostgreSQL

## API Reference

### ImageEmbeddingClient (Ingestion)
```python
client = ImageEmbeddingClient(service_url="http://...")
client.health_check() → Dict
client.embed_images(images_base64: List[str]) → Dict
client.caption_images(images_base64: List[str], max_length: int) → Dict
client.embed_text(texts: List[str]) → Dict
```

### ImageRetriever
```python
retriever = ImageRetriever(service_url, qdrant_client, database_url)
retriever.retrieve_images_by_text(query: str, collection: str, top_k: int) → List[Dict]
retriever.retrieve_images_with_base64(query: str, collection: str, top_k: int) → List[Dict]
retriever.fetch_image_from_pg(image_id: str) → Dict
```

## Support

For issues or questions:
1. Check logs in `Data_Ingestion_Service` and `Data_Retriever_Service`
2. Verify SigLIP-2 service health
3. Check Qdrant collection schema
4. Review `IMAGE_EMBEDDING_INTEGRATION_SUMMARY.md` for details

