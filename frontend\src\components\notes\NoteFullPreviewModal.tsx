/**
 * Full screen preview modal for note content
 */

'use client';

import React from 'react';
import { ParsedContent } from '@/types/note';

interface NoteFullPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  combined: string;
  parsedContent: ParsedContent;
}

export default function NoteFullPreviewModal({ 
  isOpen, 
  onClose, 
  combined,
  parsedContent 
}: NoteFullPreviewModalProps) {
  if (!isOpen) return null;

  const { query, answer } = parsedContent;

  return (
    <div 
      className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center" 
      onClick={onClose}
    >
      <div 
        className="bg-white rounded-lg shadow-2xl w-full max-w-5xl max-h-[85vh] mx-4 flex flex-col" 
        onClick={(e) => e.stopPropagation()}
      >
        <div className="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
          <h4 className="text-sm font-semibold text-gray-800">Full Content</h4>
          <button 
            className="text-gray-600 hover:text-gray-800" 
            aria-label="Close" 
            onClick={onClose}
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-5 w-5" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="p-5 overflow-y-auto text-sm">
          {query && (
            <div className="mb-4">
              <p className="font-medium text-gray-700">Query:</p>
              <p className="text-gray-700 whitespace-pre-wrap">{query}</p>
            </div>
          )}
          {answer && (
            <div>
              <p className="font-medium text-gray-700">Response:</p>
              <p className="text-gray-700 whitespace-pre-wrap">{answer}</p>
            </div>
          )}
          {!query && !answer && (
            <p className="text-gray-700 whitespace-pre-wrap">{combined}</p>
          )}
        </div>
      </div>
    </div>
  );
}
