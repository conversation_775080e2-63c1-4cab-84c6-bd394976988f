import React from 'react';
import { EmptyState } from '@/components/ui';
import { TicketStatus } from '@/types/ticket';

interface TicketEmptyStateProps {
  hasAnyTickets: boolean | null;
  activeTab: 'all' | TicketStatus;
  onCreateTicket?: () => void;
}

export function TicketEmptyState({ 
  hasAnyTickets, 
  activeTab,
  onCreateTicket 
}: TicketEmptyStateProps) {
  // User has never submitted any tickets
  if (hasAnyTickets === false) {
    return (
      <EmptyState
        icon={
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
            />
          </svg>
        }
        title="No tickets yet"
        description="You haven't submitted any support tickets yet."
        action={onCreateTicket ? {
          label: 'Create Your First Ticket',
          onClick: onCreateTicket
        } : undefined}
      />
    );
  }

  // User has tickets but none for current filter
  if (hasAnyTickets === true) {
    return (
      <EmptyState
        icon={
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" 
            />
          </svg>
        }
        title="No tickets found"
        description={
          activeTab === 'all' 
            ? 'No tickets to display.' 
            : `No ${activeTab.replace('_', ' ')} tickets found.`
        }
      />
    );
  }

  // Still loading/hasAnyTickets is null
  return (
    <EmptyState
      icon={
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
          />
        </svg>
      }
      title="No tickets found"
      description={
        activeTab === 'all' 
          ? "You haven't submitted any tickets yet." 
          : `No ${activeTab.replace('_', ' ')} tickets found.`
      }
    />
  );
}
