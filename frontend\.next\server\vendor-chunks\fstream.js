/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fstream";
exports.ids = ["vendor-chunks/fstream"];
exports.modules = {

/***/ "(ssr)/./node_modules/fstream/fstream.js":
/*!*****************************************!*\
  !*** ./node_modules/fstream/fstream.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.Abstract = __webpack_require__(/*! ./lib/abstract.js */ \"(ssr)/./node_modules/fstream/lib/abstract.js\")\nexports.Reader = __webpack_require__(/*! ./lib/reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\nexports.Writer = __webpack_require__(/*! ./lib/writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\n\nexports.File = {\n  Reader: __webpack_require__(/*! ./lib/file-reader.js */ \"(ssr)/./node_modules/fstream/lib/file-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/file-writer.js */ \"(ssr)/./node_modules/fstream/lib/file-writer.js\")\n}\n\nexports.Dir = {\n  Reader: __webpack_require__(/*! ./lib/dir-reader.js */ \"(ssr)/./node_modules/fstream/lib/dir-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/dir-writer.js */ \"(ssr)/./node_modules/fstream/lib/dir-writer.js\")\n}\n\nexports.Link = {\n  Reader: __webpack_require__(/*! ./lib/link-reader.js */ \"(ssr)/./node_modules/fstream/lib/link-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/link-writer.js */ \"(ssr)/./node_modules/fstream/lib/link-writer.js\")\n}\n\nexports.Proxy = {\n  Reader: __webpack_require__(/*! ./lib/proxy-reader.js */ \"(ssr)/./node_modules/fstream/lib/proxy-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/proxy-writer.js */ \"(ssr)/./node_modules/fstream/lib/proxy-writer.js\")\n}\n\nexports.Reader.Dir = exports.DirReader = exports.Dir.Reader\nexports.Reader.File = exports.FileReader = exports.File.Reader\nexports.Reader.Link = exports.LinkReader = exports.Link.Reader\nexports.Reader.Proxy = exports.ProxyReader = exports.Proxy.Reader\n\nexports.Writer.Dir = exports.DirWriter = exports.Dir.Writer\nexports.Writer.File = exports.FileWriter = exports.File.Writer\nexports.Writer.Link = exports.LinkWriter = exports.Link.Writer\nexports.Writer.Proxy = exports.ProxyWriter = exports.Proxy.Writer\n\nexports.collect = __webpack_require__(/*! ./lib/collect.js */ \"(ssr)/./node_modules/fstream/lib/collect.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/fstream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/abstract.js":
/*!**********************************************!*\
  !*** ./node_modules/fstream/lib/abstract.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// the parent class for all fstreams.\n\nmodule.exports = Abstract\n\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream)\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\n\nfunction Abstract () {\n  Stream.call(this)\n}\n\ninherits(Abstract, Stream)\n\nAbstract.prototype.on = function (ev, fn) {\n  if (ev === 'ready' && this.ready) {\n    process.nextTick(fn.bind(this))\n  } else {\n    Stream.prototype.on.call(this, ev, fn)\n  }\n  return this\n}\n\nAbstract.prototype.abort = function () {\n  this._aborted = true\n  this.emit('abort')\n}\n\nAbstract.prototype.destroy = function () {}\n\nAbstract.prototype.warn = function (msg, code) {\n  var self = this\n  var er = decorate(msg, code, self)\n  if (!self.listeners('warn')) {\n    console.error('%s %s\\n' +\n    'path = %s\\n' +\n    'syscall = %s\\n' +\n    'fstream_type = %s\\n' +\n    'fstream_path = %s\\n' +\n    'fstream_unc_path = %s\\n' +\n    'fstream_class = %s\\n' +\n    'fstream_stack =\\n%s\\n',\n      code || 'UNKNOWN',\n      er.stack,\n      er.path,\n      er.syscall,\n      er.fstream_type,\n      er.fstream_path,\n      er.fstream_unc_path,\n      er.fstream_class,\n      er.fstream_stack.join('\\n'))\n  } else {\n    self.emit('warn', er)\n  }\n}\n\nAbstract.prototype.info = function (msg, code) {\n  this.emit('info', msg, code)\n}\n\nAbstract.prototype.error = function (msg, code, th) {\n  var er = decorate(msg, code, this)\n  if (th) throw er\n  else this.emit('error', er)\n}\n\nfunction decorate (er, code, self) {\n  if (!(er instanceof Error)) er = new Error(er)\n  er.code = er.code || code\n  er.path = er.path || self.path\n  er.fstream_type = er.fstream_type || self.type\n  er.fstream_path = er.fstream_path || self.path\n  if (self._path !== self.path) {\n    er.fstream_unc_path = er.fstream_unc_path || self._path\n  }\n  if (self.linkpath) {\n    er.fstream_linkpath = er.fstream_linkpath || self.linkpath\n  }\n  er.fstream_class = er.fstream_class || self.constructor.name\n  er.fstream_stack = er.fstream_stack ||\n    new Error().stack.split(/\\n/).slice(3).map(function (s) {\n      return s.replace(/^ {4}at /, '')\n    })\n\n  return er\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvYWJzdHJhY3QuanMiLCJtYXBwaW5ncyI6IkFBQUE7O0FBRUE7O0FBRUEsYUFBYSxvREFBd0I7QUFDckMsZUFBZSxtQkFBTyxDQUFDLDJEQUFVOztBQUVqQztBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLEVBQUU7QUFDN0IsS0FBSzs7QUFFTDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvYWJzdHJhY3QuanM/NjY5NiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyB0aGUgcGFyZW50IGNsYXNzIGZvciBhbGwgZnN0cmVhbXMuXG5cbm1vZHVsZS5leHBvcnRzID0gQWJzdHJhY3RcblxudmFyIFN0cmVhbSA9IHJlcXVpcmUoJ3N0cmVhbScpLlN0cmVhbVxudmFyIGluaGVyaXRzID0gcmVxdWlyZSgnaW5oZXJpdHMnKVxuXG5mdW5jdGlvbiBBYnN0cmFjdCAoKSB7XG4gIFN0cmVhbS5jYWxsKHRoaXMpXG59XG5cbmluaGVyaXRzKEFic3RyYWN0LCBTdHJlYW0pXG5cbkFic3RyYWN0LnByb3RvdHlwZS5vbiA9IGZ1bmN0aW9uIChldiwgZm4pIHtcbiAgaWYgKGV2ID09PSAncmVhZHknICYmIHRoaXMucmVhZHkpIHtcbiAgICBwcm9jZXNzLm5leHRUaWNrKGZuLmJpbmQodGhpcykpXG4gIH0gZWxzZSB7XG4gICAgU3RyZWFtLnByb3RvdHlwZS5vbi5jYWxsKHRoaXMsIGV2LCBmbilcbiAgfVxuICByZXR1cm4gdGhpc1xufVxuXG5BYnN0cmFjdC5wcm90b3R5cGUuYWJvcnQgPSBmdW5jdGlvbiAoKSB7XG4gIHRoaXMuX2Fib3J0ZWQgPSB0cnVlXG4gIHRoaXMuZW1pdCgnYWJvcnQnKVxufVxuXG5BYnN0cmFjdC5wcm90b3R5cGUuZGVzdHJveSA9IGZ1bmN0aW9uICgpIHt9XG5cbkFic3RyYWN0LnByb3RvdHlwZS53YXJuID0gZnVuY3Rpb24gKG1zZywgY29kZSkge1xuICB2YXIgc2VsZiA9IHRoaXNcbiAgdmFyIGVyID0gZGVjb3JhdGUobXNnLCBjb2RlLCBzZWxmKVxuICBpZiAoIXNlbGYubGlzdGVuZXJzKCd3YXJuJykpIHtcbiAgICBjb25zb2xlLmVycm9yKCclcyAlc1xcbicgK1xuICAgICdwYXRoID0gJXNcXG4nICtcbiAgICAnc3lzY2FsbCA9ICVzXFxuJyArXG4gICAgJ2ZzdHJlYW1fdHlwZSA9ICVzXFxuJyArXG4gICAgJ2ZzdHJlYW1fcGF0aCA9ICVzXFxuJyArXG4gICAgJ2ZzdHJlYW1fdW5jX3BhdGggPSAlc1xcbicgK1xuICAgICdmc3RyZWFtX2NsYXNzID0gJXNcXG4nICtcbiAgICAnZnN0cmVhbV9zdGFjayA9XFxuJXNcXG4nLFxuICAgICAgY29kZSB8fCAnVU5LTk9XTicsXG4gICAgICBlci5zdGFjayxcbiAgICAgIGVyLnBhdGgsXG4gICAgICBlci5zeXNjYWxsLFxuICAgICAgZXIuZnN0cmVhbV90eXBlLFxuICAgICAgZXIuZnN0cmVhbV9wYXRoLFxuICAgICAgZXIuZnN0cmVhbV91bmNfcGF0aCxcbiAgICAgIGVyLmZzdHJlYW1fY2xhc3MsXG4gICAgICBlci5mc3RyZWFtX3N0YWNrLmpvaW4oJ1xcbicpKVxuICB9IGVsc2Uge1xuICAgIHNlbGYuZW1pdCgnd2FybicsIGVyKVxuICB9XG59XG5cbkFic3RyYWN0LnByb3RvdHlwZS5pbmZvID0gZnVuY3Rpb24gKG1zZywgY29kZSkge1xuICB0aGlzLmVtaXQoJ2luZm8nLCBtc2csIGNvZGUpXG59XG5cbkFic3RyYWN0LnByb3RvdHlwZS5lcnJvciA9IGZ1bmN0aW9uIChtc2csIGNvZGUsIHRoKSB7XG4gIHZhciBlciA9IGRlY29yYXRlKG1zZywgY29kZSwgdGhpcylcbiAgaWYgKHRoKSB0aHJvdyBlclxuICBlbHNlIHRoaXMuZW1pdCgnZXJyb3InLCBlcilcbn1cblxuZnVuY3Rpb24gZGVjb3JhdGUgKGVyLCBjb2RlLCBzZWxmKSB7XG4gIGlmICghKGVyIGluc3RhbmNlb2YgRXJyb3IpKSBlciA9IG5ldyBFcnJvcihlcilcbiAgZXIuY29kZSA9IGVyLmNvZGUgfHwgY29kZVxuICBlci5wYXRoID0gZXIucGF0aCB8fCBzZWxmLnBhdGhcbiAgZXIuZnN0cmVhbV90eXBlID0gZXIuZnN0cmVhbV90eXBlIHx8IHNlbGYudHlwZVxuICBlci5mc3RyZWFtX3BhdGggPSBlci5mc3RyZWFtX3BhdGggfHwgc2VsZi5wYXRoXG4gIGlmIChzZWxmLl9wYXRoICE9PSBzZWxmLnBhdGgpIHtcbiAgICBlci5mc3RyZWFtX3VuY19wYXRoID0gZXIuZnN0cmVhbV91bmNfcGF0aCB8fCBzZWxmLl9wYXRoXG4gIH1cbiAgaWYgKHNlbGYubGlua3BhdGgpIHtcbiAgICBlci5mc3RyZWFtX2xpbmtwYXRoID0gZXIuZnN0cmVhbV9saW5rcGF0aCB8fCBzZWxmLmxpbmtwYXRoXG4gIH1cbiAgZXIuZnN0cmVhbV9jbGFzcyA9IGVyLmZzdHJlYW1fY2xhc3MgfHwgc2VsZi5jb25zdHJ1Y3Rvci5uYW1lXG4gIGVyLmZzdHJlYW1fc3RhY2sgPSBlci5mc3RyZWFtX3N0YWNrIHx8XG4gICAgbmV3IEVycm9yKCkuc3RhY2suc3BsaXQoL1xcbi8pLnNsaWNlKDMpLm1hcChmdW5jdGlvbiAocykge1xuICAgICAgcmV0dXJuIHMucmVwbGFjZSgvXiB7NH1hdCAvLCAnJylcbiAgICB9KVxuXG4gIHJldHVybiBlclxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/abstract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/collect.js":
/*!*********************************************!*\
  !*** ./node_modules/fstream/lib/collect.js ***!
  \*********************************************/
/***/ ((module) => {

eval("module.exports = collect\n\nfunction collect (stream) {\n  if (stream._collected) return\n\n  if (stream._paused) return stream.on('resume', collect.bind(null, stream))\n\n  stream._collected = true\n  stream.pause()\n\n  stream.on('data', save)\n  stream.on('end', save)\n  var buf = []\n  function save (b) {\n    if (typeof b === 'string') b = new Buffer(b)\n    if (Buffer.isBuffer(b) && !b.length) return\n    buf.push(b)\n  }\n\n  stream.on('entry', saveEntry)\n  var entryBuffer = []\n  function saveEntry (e) {\n    collect(e)\n    entryBuffer.push(e)\n  }\n\n  stream.on('proxy', proxyPause)\n  function proxyPause (p) {\n    p.pause()\n  }\n\n  // replace the pipe method with a new version that will\n  // unlock the buffered stuff.  if you just call .pipe()\n  // without a destination, then it'll re-play the events.\n  stream.pipe = (function (orig) {\n    return function (dest) {\n      // console.error(' === open the pipes', dest && dest.path)\n\n      // let the entries flow through one at a time.\n      // Once they're all done, then we can resume completely.\n      var e = 0\n      ;(function unblockEntry () {\n        var entry = entryBuffer[e++]\n        // console.error(\" ==== unblock entry\", entry && entry.path)\n        if (!entry) return resume()\n        entry.on('end', unblockEntry)\n        if (dest) dest.add(entry)\n        else stream.emit('entry', entry)\n      })()\n\n      function resume () {\n        stream.removeListener('entry', saveEntry)\n        stream.removeListener('data', save)\n        stream.removeListener('end', save)\n\n        stream.pipe = orig\n        if (dest) stream.pipe(dest)\n\n        buf.forEach(function (b) {\n          if (b) stream.emit('data', b)\n          else stream.emit('end')\n        })\n\n        stream.resume()\n      }\n\n      return dest\n    }\n  })(stream.pipe)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/collect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/dir-reader.js":
/*!************************************************!*\
  !*** ./node_modules/fstream/lib/dir-reader.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// A thing that emits \"entry\" events with Reader objects\n// Pausing it causes it to stop emitting entry events, and also\n// pauses the current entry if there is one.\n\nmodule.exports = DirReader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\nvar assert = (__webpack_require__(/*! assert */ \"assert\").ok)\n\ninherits(DirReader, Reader)\n\nfunction DirReader (props) {\n  var self = this\n  if (!(self instanceof DirReader)) {\n    throw new Error('DirReader must be called as constructor.')\n  }\n\n  // should already be established as a Directory type\n  if (props.type !== 'Directory' || !props.Directory) {\n    throw new Error('Non-directory type ' + props.type)\n  }\n\n  self.entries = null\n  self._index = -1\n  self._paused = false\n  self._length = -1\n\n  if (props.sort) {\n    this.sort = props.sort\n  }\n\n  Reader.call(this, props)\n}\n\nDirReader.prototype._getEntries = function () {\n  var self = this\n\n  // race condition.  might pause() before calling _getEntries,\n  // and then resume, and try to get them a second time.\n  if (self._gotEntries) return\n  self._gotEntries = true\n\n  fs.readdir(self._path, function (er, entries) {\n    if (er) return self.error(er)\n\n    self.entries = entries\n\n    self.emit('entries', entries)\n    if (self._paused) self.once('resume', processEntries)\n    else processEntries()\n\n    function processEntries () {\n      self._length = self.entries.length\n      if (typeof self.sort === 'function') {\n        self.entries = self.entries.sort(self.sort.bind(self))\n      }\n      self._read()\n    }\n  })\n}\n\n// start walking the dir, and emit an \"entry\" event for each one.\nDirReader.prototype._read = function () {\n  var self = this\n\n  if (!self.entries) return self._getEntries()\n\n  if (self._paused || self._currentEntry || self._aborted) {\n    // console.error('DR paused=%j, current=%j, aborted=%j', self._paused, !!self._currentEntry, self._aborted)\n    return\n  }\n\n  self._index++\n  if (self._index >= self.entries.length) {\n    if (!self._ended) {\n      self._ended = true\n      self.emit('end')\n      self.emit('close')\n    }\n    return\n  }\n\n  // ok, handle this one, then.\n\n  // save creating a proxy, by stat'ing the thing now.\n  var p = path.resolve(self._path, self.entries[self._index])\n  assert(p !== self._path)\n  assert(self.entries[self._index])\n\n  // set this to prevent trying to _read() again in the stat time.\n  self._currentEntry = p\n  fs[ self.props.follow ? 'stat' : 'lstat' ](p, function (er, stat) {\n    if (er) return self.error(er)\n\n    var who = self._proxy || self\n\n    stat.path = p\n    stat.basename = path.basename(p)\n    stat.dirname = path.dirname(p)\n    var childProps = self.getChildProps.call(who, stat)\n    childProps.path = p\n    childProps.basename = path.basename(p)\n    childProps.dirname = path.dirname(p)\n\n    var entry = Reader(childProps, stat)\n\n    // console.error(\"DR Entry\", p, stat.size)\n\n    self._currentEntry = entry\n\n    // \"entry\" events are for direct entries in a specific dir.\n    // \"child\" events are for any and all children at all levels.\n    // This nomenclature is not completely final.\n\n    entry.on('pause', function (who) {\n      if (!self._paused && !entry._disowned) {\n        self.pause(who)\n      }\n    })\n\n    entry.on('resume', function (who) {\n      if (self._paused && !entry._disowned) {\n        self.resume(who)\n      }\n    })\n\n    entry.on('stat', function (props) {\n      self.emit('_entryStat', entry, props)\n      if (entry._aborted) return\n      if (entry._paused) {\n        entry.once('resume', function () {\n          self.emit('entryStat', entry, props)\n        })\n      } else self.emit('entryStat', entry, props)\n    })\n\n    entry.on('ready', function EMITCHILD () {\n      // console.error(\"DR emit child\", entry._path)\n      if (self._paused) {\n        // console.error(\"  DR emit child - try again later\")\n        // pause the child, and emit the \"entry\" event once we drain.\n        // console.error(\"DR pausing child entry\")\n        entry.pause(self)\n        return self.once('resume', EMITCHILD)\n      }\n\n      // skip over sockets.  they can't be piped around properly,\n      // so there's really no sense even acknowledging them.\n      // if someone really wants to see them, they can listen to\n      // the \"socket\" events.\n      if (entry.type === 'Socket') {\n        self.emit('socket', entry)\n      } else {\n        self.emitEntry(entry)\n      }\n    })\n\n    var ended = false\n    entry.on('close', onend)\n    entry.on('disown', onend)\n    function onend () {\n      if (ended) return\n      ended = true\n      self.emit('childEnd', entry)\n      self.emit('entryEnd', entry)\n      self._currentEntry = null\n      if (!self._paused) {\n        self._read()\n      }\n    }\n\n    // XXX Remove this.  Works in node as of 0.6.2 or so.\n    // Long filenames should not break stuff.\n    entry.on('error', function (er) {\n      if (entry._swallowErrors) {\n        self.warn(er)\n        entry.emit('end')\n        entry.emit('close')\n      } else {\n        self.emit('error', er)\n      }\n    })\n\n    // proxy up some events.\n    ;[\n      'child',\n      'childEnd',\n      'warn'\n    ].forEach(function (ev) {\n      entry.on(ev, self.emit.bind(self, ev))\n    })\n  })\n}\n\nDirReader.prototype.disown = function (entry) {\n  entry.emit('beforeDisown')\n  entry._disowned = true\n  entry.parent = entry.root = null\n  if (entry === this._currentEntry) {\n    this._currentEntry = null\n  }\n  entry.emit('disown')\n}\n\nDirReader.prototype.getChildProps = function () {\n  return {\n    depth: this.depth + 1,\n    root: this.root || this,\n    parent: this,\n    follow: this.follow,\n    filter: this.filter,\n    sort: this.props.sort,\n    hardlinks: this.props.hardlinks\n  }\n}\n\nDirReader.prototype.pause = function (who) {\n  var self = this\n  if (self._paused) return\n  who = who || self\n  self._paused = true\n  if (self._currentEntry && self._currentEntry.pause) {\n    self._currentEntry.pause(who)\n  }\n  self.emit('pause', who)\n}\n\nDirReader.prototype.resume = function (who) {\n  var self = this\n  if (!self._paused) return\n  who = who || self\n\n  self._paused = false\n  // console.error('DR Emit Resume', self._path)\n  self.emit('resume', who)\n  if (self._paused) {\n    // console.error('DR Re-paused', self._path)\n    return\n  }\n\n  if (self._currentEntry) {\n    if (self._currentEntry.resume) self._currentEntry.resume(who)\n  } else self._read()\n}\n\nDirReader.prototype.emitEntry = function (entry) {\n  this.emit('entry', entry)\n  this.emit('child', entry)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvZGlyLXJlYWRlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsU0FBUyxtQkFBTyxDQUFDLG9FQUFhO0FBQzlCLGVBQWUsbUJBQU8sQ0FBQywyREFBVTtBQUNqQyxXQUFXLG1CQUFPLENBQUMsa0JBQU07QUFDekIsYUFBYSxtQkFBTyxDQUFDLCtEQUFhO0FBQ2xDLGFBQWEsZ0RBQW9COztBQUVqQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsUUFBUTtBQUNSLEtBQUs7O0FBRUw7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsSUFBSTtBQUNKOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvZGlyLXJlYWRlci5qcz85NzI4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEEgdGhpbmcgdGhhdCBlbWl0cyBcImVudHJ5XCIgZXZlbnRzIHdpdGggUmVhZGVyIG9iamVjdHNcbi8vIFBhdXNpbmcgaXQgY2F1c2VzIGl0IHRvIHN0b3AgZW1pdHRpbmcgZW50cnkgZXZlbnRzLCBhbmQgYWxzb1xuLy8gcGF1c2VzIHRoZSBjdXJyZW50IGVudHJ5IGlmIHRoZXJlIGlzIG9uZS5cblxubW9kdWxlLmV4cG9ydHMgPSBEaXJSZWFkZXJcblxudmFyIGZzID0gcmVxdWlyZSgnZ3JhY2VmdWwtZnMnKVxudmFyIGluaGVyaXRzID0gcmVxdWlyZSgnaW5oZXJpdHMnKVxudmFyIHBhdGggPSByZXF1aXJlKCdwYXRoJylcbnZhciBSZWFkZXIgPSByZXF1aXJlKCcuL3JlYWRlci5qcycpXG52YXIgYXNzZXJ0ID0gcmVxdWlyZSgnYXNzZXJ0Jykub2tcblxuaW5oZXJpdHMoRGlyUmVhZGVyLCBSZWFkZXIpXG5cbmZ1bmN0aW9uIERpclJlYWRlciAocHJvcHMpIHtcbiAgdmFyIHNlbGYgPSB0aGlzXG4gIGlmICghKHNlbGYgaW5zdGFuY2VvZiBEaXJSZWFkZXIpKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdEaXJSZWFkZXIgbXVzdCBiZSBjYWxsZWQgYXMgY29uc3RydWN0b3IuJylcbiAgfVxuXG4gIC8vIHNob3VsZCBhbHJlYWR5IGJlIGVzdGFibGlzaGVkIGFzIGEgRGlyZWN0b3J5IHR5cGVcbiAgaWYgKHByb3BzLnR5cGUgIT09ICdEaXJlY3RvcnknIHx8ICFwcm9wcy5EaXJlY3RvcnkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ05vbi1kaXJlY3RvcnkgdHlwZSAnICsgcHJvcHMudHlwZSlcbiAgfVxuXG4gIHNlbGYuZW50cmllcyA9IG51bGxcbiAgc2VsZi5faW5kZXggPSAtMVxuICBzZWxmLl9wYXVzZWQgPSBmYWxzZVxuICBzZWxmLl9sZW5ndGggPSAtMVxuXG4gIGlmIChwcm9wcy5zb3J0KSB7XG4gICAgdGhpcy5zb3J0ID0gcHJvcHMuc29ydFxuICB9XG5cbiAgUmVhZGVyLmNhbGwodGhpcywgcHJvcHMpXG59XG5cbkRpclJlYWRlci5wcm90b3R5cGUuX2dldEVudHJpZXMgPSBmdW5jdGlvbiAoKSB7XG4gIHZhciBzZWxmID0gdGhpc1xuXG4gIC8vIHJhY2UgY29uZGl0aW9uLiAgbWlnaHQgcGF1c2UoKSBiZWZvcmUgY2FsbGluZyBfZ2V0RW50cmllcyxcbiAgLy8gYW5kIHRoZW4gcmVzdW1lLCBhbmQgdHJ5IHRvIGdldCB0aGVtIGEgc2Vjb25kIHRpbWUuXG4gIGlmIChzZWxmLl9nb3RFbnRyaWVzKSByZXR1cm5cbiAgc2VsZi5fZ290RW50cmllcyA9IHRydWVcblxuICBmcy5yZWFkZGlyKHNlbGYuX3BhdGgsIGZ1bmN0aW9uIChlciwgZW50cmllcykge1xuICAgIGlmIChlcikgcmV0dXJuIHNlbGYuZXJyb3IoZXIpXG5cbiAgICBzZWxmLmVudHJpZXMgPSBlbnRyaWVzXG5cbiAgICBzZWxmLmVtaXQoJ2VudHJpZXMnLCBlbnRyaWVzKVxuICAgIGlmIChzZWxmLl9wYXVzZWQpIHNlbGYub25jZSgncmVzdW1lJywgcHJvY2Vzc0VudHJpZXMpXG4gICAgZWxzZSBwcm9jZXNzRW50cmllcygpXG5cbiAgICBmdW5jdGlvbiBwcm9jZXNzRW50cmllcyAoKSB7XG4gICAgICBzZWxmLl9sZW5ndGggPSBzZWxmLmVudHJpZXMubGVuZ3RoXG4gICAgICBpZiAodHlwZW9mIHNlbGYuc29ydCA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICBzZWxmLmVudHJpZXMgPSBzZWxmLmVudHJpZXMuc29ydChzZWxmLnNvcnQuYmluZChzZWxmKSlcbiAgICAgIH1cbiAgICAgIHNlbGYuX3JlYWQoKVxuICAgIH1cbiAgfSlcbn1cblxuLy8gc3RhcnQgd2Fsa2luZyB0aGUgZGlyLCBhbmQgZW1pdCBhbiBcImVudHJ5XCIgZXZlbnQgZm9yIGVhY2ggb25lLlxuRGlyUmVhZGVyLnByb3RvdHlwZS5fcmVhZCA9IGZ1bmN0aW9uICgpIHtcbiAgdmFyIHNlbGYgPSB0aGlzXG5cbiAgaWYgKCFzZWxmLmVudHJpZXMpIHJldHVybiBzZWxmLl9nZXRFbnRyaWVzKClcblxuICBpZiAoc2VsZi5fcGF1c2VkIHx8IHNlbGYuX2N1cnJlbnRFbnRyeSB8fCBzZWxmLl9hYm9ydGVkKSB7XG4gICAgLy8gY29uc29sZS5lcnJvcignRFIgcGF1c2VkPSVqLCBjdXJyZW50PSVqLCBhYm9ydGVkPSVqJywgc2VsZi5fcGF1c2VkLCAhIXNlbGYuX2N1cnJlbnRFbnRyeSwgc2VsZi5fYWJvcnRlZClcbiAgICByZXR1cm5cbiAgfVxuXG4gIHNlbGYuX2luZGV4KytcbiAgaWYgKHNlbGYuX2luZGV4ID49IHNlbGYuZW50cmllcy5sZW5ndGgpIHtcbiAgICBpZiAoIXNlbGYuX2VuZGVkKSB7XG4gICAgICBzZWxmLl9lbmRlZCA9IHRydWVcbiAgICAgIHNlbGYuZW1pdCgnZW5kJylcbiAgICAgIHNlbGYuZW1pdCgnY2xvc2UnKVxuICAgIH1cbiAgICByZXR1cm5cbiAgfVxuXG4gIC8vIG9rLCBoYW5kbGUgdGhpcyBvbmUsIHRoZW4uXG5cbiAgLy8gc2F2ZSBjcmVhdGluZyBhIHByb3h5LCBieSBzdGF0J2luZyB0aGUgdGhpbmcgbm93LlxuICB2YXIgcCA9IHBhdGgucmVzb2x2ZShzZWxmLl9wYXRoLCBzZWxmLmVudHJpZXNbc2VsZi5faW5kZXhdKVxuICBhc3NlcnQocCAhPT0gc2VsZi5fcGF0aClcbiAgYXNzZXJ0KHNlbGYuZW50cmllc1tzZWxmLl9pbmRleF0pXG5cbiAgLy8gc2V0IHRoaXMgdG8gcHJldmVudCB0cnlpbmcgdG8gX3JlYWQoKSBhZ2FpbiBpbiB0aGUgc3RhdCB0aW1lLlxuICBzZWxmLl9jdXJyZW50RW50cnkgPSBwXG4gIGZzWyBzZWxmLnByb3BzLmZvbGxvdyA/ICdzdGF0JyA6ICdsc3RhdCcgXShwLCBmdW5jdGlvbiAoZXIsIHN0YXQpIHtcbiAgICBpZiAoZXIpIHJldHVybiBzZWxmLmVycm9yKGVyKVxuXG4gICAgdmFyIHdobyA9IHNlbGYuX3Byb3h5IHx8IHNlbGZcblxuICAgIHN0YXQucGF0aCA9IHBcbiAgICBzdGF0LmJhc2VuYW1lID0gcGF0aC5iYXNlbmFtZShwKVxuICAgIHN0YXQuZGlybmFtZSA9IHBhdGguZGlybmFtZShwKVxuICAgIHZhciBjaGlsZFByb3BzID0gc2VsZi5nZXRDaGlsZFByb3BzLmNhbGwod2hvLCBzdGF0KVxuICAgIGNoaWxkUHJvcHMucGF0aCA9IHBcbiAgICBjaGlsZFByb3BzLmJhc2VuYW1lID0gcGF0aC5iYXNlbmFtZShwKVxuICAgIGNoaWxkUHJvcHMuZGlybmFtZSA9IHBhdGguZGlybmFtZShwKVxuXG4gICAgdmFyIGVudHJ5ID0gUmVhZGVyKGNoaWxkUHJvcHMsIHN0YXQpXG5cbiAgICAvLyBjb25zb2xlLmVycm9yKFwiRFIgRW50cnlcIiwgcCwgc3RhdC5zaXplKVxuXG4gICAgc2VsZi5fY3VycmVudEVudHJ5ID0gZW50cnlcblxuICAgIC8vIFwiZW50cnlcIiBldmVudHMgYXJlIGZvciBkaXJlY3QgZW50cmllcyBpbiBhIHNwZWNpZmljIGRpci5cbiAgICAvLyBcImNoaWxkXCIgZXZlbnRzIGFyZSBmb3IgYW55IGFuZCBhbGwgY2hpbGRyZW4gYXQgYWxsIGxldmVscy5cbiAgICAvLyBUaGlzIG5vbWVuY2xhdHVyZSBpcyBub3QgY29tcGxldGVseSBmaW5hbC5cblxuICAgIGVudHJ5Lm9uKCdwYXVzZScsIGZ1bmN0aW9uICh3aG8pIHtcbiAgICAgIGlmICghc2VsZi5fcGF1c2VkICYmICFlbnRyeS5fZGlzb3duZWQpIHtcbiAgICAgICAgc2VsZi5wYXVzZSh3aG8pXG4gICAgICB9XG4gICAgfSlcblxuICAgIGVudHJ5Lm9uKCdyZXN1bWUnLCBmdW5jdGlvbiAod2hvKSB7XG4gICAgICBpZiAoc2VsZi5fcGF1c2VkICYmICFlbnRyeS5fZGlzb3duZWQpIHtcbiAgICAgICAgc2VsZi5yZXN1bWUod2hvKVxuICAgICAgfVxuICAgIH0pXG5cbiAgICBlbnRyeS5vbignc3RhdCcsIGZ1bmN0aW9uIChwcm9wcykge1xuICAgICAgc2VsZi5lbWl0KCdfZW50cnlTdGF0JywgZW50cnksIHByb3BzKVxuICAgICAgaWYgKGVudHJ5Ll9hYm9ydGVkKSByZXR1cm5cbiAgICAgIGlmIChlbnRyeS5fcGF1c2VkKSB7XG4gICAgICAgIGVudHJ5Lm9uY2UoJ3Jlc3VtZScsIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICBzZWxmLmVtaXQoJ2VudHJ5U3RhdCcsIGVudHJ5LCBwcm9wcylcbiAgICAgICAgfSlcbiAgICAgIH0gZWxzZSBzZWxmLmVtaXQoJ2VudHJ5U3RhdCcsIGVudHJ5LCBwcm9wcylcbiAgICB9KVxuXG4gICAgZW50cnkub24oJ3JlYWR5JywgZnVuY3Rpb24gRU1JVENISUxEICgpIHtcbiAgICAgIC8vIGNvbnNvbGUuZXJyb3IoXCJEUiBlbWl0IGNoaWxkXCIsIGVudHJ5Ll9wYXRoKVxuICAgICAgaWYgKHNlbGYuX3BhdXNlZCkge1xuICAgICAgICAvLyBjb25zb2xlLmVycm9yKFwiICBEUiBlbWl0IGNoaWxkIC0gdHJ5IGFnYWluIGxhdGVyXCIpXG4gICAgICAgIC8vIHBhdXNlIHRoZSBjaGlsZCwgYW5kIGVtaXQgdGhlIFwiZW50cnlcIiBldmVudCBvbmNlIHdlIGRyYWluLlxuICAgICAgICAvLyBjb25zb2xlLmVycm9yKFwiRFIgcGF1c2luZyBjaGlsZCBlbnRyeVwiKVxuICAgICAgICBlbnRyeS5wYXVzZShzZWxmKVxuICAgICAgICByZXR1cm4gc2VsZi5vbmNlKCdyZXN1bWUnLCBFTUlUQ0hJTEQpXG4gICAgICB9XG5cbiAgICAgIC8vIHNraXAgb3ZlciBzb2NrZXRzLiAgdGhleSBjYW4ndCBiZSBwaXBlZCBhcm91bmQgcHJvcGVybHksXG4gICAgICAvLyBzbyB0aGVyZSdzIHJlYWxseSBubyBzZW5zZSBldmVuIGFja25vd2xlZGdpbmcgdGhlbS5cbiAgICAgIC8vIGlmIHNvbWVvbmUgcmVhbGx5IHdhbnRzIHRvIHNlZSB0aGVtLCB0aGV5IGNhbiBsaXN0ZW4gdG9cbiAgICAgIC8vIHRoZSBcInNvY2tldFwiIGV2ZW50cy5cbiAgICAgIGlmIChlbnRyeS50eXBlID09PSAnU29ja2V0Jykge1xuICAgICAgICBzZWxmLmVtaXQoJ3NvY2tldCcsIGVudHJ5KVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2VsZi5lbWl0RW50cnkoZW50cnkpXG4gICAgICB9XG4gICAgfSlcblxuICAgIHZhciBlbmRlZCA9IGZhbHNlXG4gICAgZW50cnkub24oJ2Nsb3NlJywgb25lbmQpXG4gICAgZW50cnkub24oJ2Rpc293bicsIG9uZW5kKVxuICAgIGZ1bmN0aW9uIG9uZW5kICgpIHtcbiAgICAgIGlmIChlbmRlZCkgcmV0dXJuXG4gICAgICBlbmRlZCA9IHRydWVcbiAgICAgIHNlbGYuZW1pdCgnY2hpbGRFbmQnLCBlbnRyeSlcbiAgICAgIHNlbGYuZW1pdCgnZW50cnlFbmQnLCBlbnRyeSlcbiAgICAgIHNlbGYuX2N1cnJlbnRFbnRyeSA9IG51bGxcbiAgICAgIGlmICghc2VsZi5fcGF1c2VkKSB7XG4gICAgICAgIHNlbGYuX3JlYWQoKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFhYWCBSZW1vdmUgdGhpcy4gIFdvcmtzIGluIG5vZGUgYXMgb2YgMC42LjIgb3Igc28uXG4gICAgLy8gTG9uZyBmaWxlbmFtZXMgc2hvdWxkIG5vdCBicmVhayBzdHVmZi5cbiAgICBlbnRyeS5vbignZXJyb3InLCBmdW5jdGlvbiAoZXIpIHtcbiAgICAgIGlmIChlbnRyeS5fc3dhbGxvd0Vycm9ycykge1xuICAgICAgICBzZWxmLndhcm4oZXIpXG4gICAgICAgIGVudHJ5LmVtaXQoJ2VuZCcpXG4gICAgICAgIGVudHJ5LmVtaXQoJ2Nsb3NlJylcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNlbGYuZW1pdCgnZXJyb3InLCBlcilcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgLy8gcHJveHkgdXAgc29tZSBldmVudHMuXG4gICAgO1tcbiAgICAgICdjaGlsZCcsXG4gICAgICAnY2hpbGRFbmQnLFxuICAgICAgJ3dhcm4nXG4gICAgXS5mb3JFYWNoKGZ1bmN0aW9uIChldikge1xuICAgICAgZW50cnkub24oZXYsIHNlbGYuZW1pdC5iaW5kKHNlbGYsIGV2KSlcbiAgICB9KVxuICB9KVxufVxuXG5EaXJSZWFkZXIucHJvdG90eXBlLmRpc293biA9IGZ1bmN0aW9uIChlbnRyeSkge1xuICBlbnRyeS5lbWl0KCdiZWZvcmVEaXNvd24nKVxuICBlbnRyeS5fZGlzb3duZWQgPSB0cnVlXG4gIGVudHJ5LnBhcmVudCA9IGVudHJ5LnJvb3QgPSBudWxsXG4gIGlmIChlbnRyeSA9PT0gdGhpcy5fY3VycmVudEVudHJ5KSB7XG4gICAgdGhpcy5fY3VycmVudEVudHJ5ID0gbnVsbFxuICB9XG4gIGVudHJ5LmVtaXQoJ2Rpc293bicpXG59XG5cbkRpclJlYWRlci5wcm90b3R5cGUuZ2V0Q2hpbGRQcm9wcyA9IGZ1bmN0aW9uICgpIHtcbiAgcmV0dXJuIHtcbiAgICBkZXB0aDogdGhpcy5kZXB0aCArIDEsXG4gICAgcm9vdDogdGhpcy5yb290IHx8IHRoaXMsXG4gICAgcGFyZW50OiB0aGlzLFxuICAgIGZvbGxvdzogdGhpcy5mb2xsb3csXG4gICAgZmlsdGVyOiB0aGlzLmZpbHRlcixcbiAgICBzb3J0OiB0aGlzLnByb3BzLnNvcnQsXG4gICAgaGFyZGxpbmtzOiB0aGlzLnByb3BzLmhhcmRsaW5rc1xuICB9XG59XG5cbkRpclJlYWRlci5wcm90b3R5cGUucGF1c2UgPSBmdW5jdGlvbiAod2hvKSB7XG4gIHZhciBzZWxmID0gdGhpc1xuICBpZiAoc2VsZi5fcGF1c2VkKSByZXR1cm5cbiAgd2hvID0gd2hvIHx8IHNlbGZcbiAgc2VsZi5fcGF1c2VkID0gdHJ1ZVxuICBpZiAoc2VsZi5fY3VycmVudEVudHJ5ICYmIHNlbGYuX2N1cnJlbnRFbnRyeS5wYXVzZSkge1xuICAgIHNlbGYuX2N1cnJlbnRFbnRyeS5wYXVzZSh3aG8pXG4gIH1cbiAgc2VsZi5lbWl0KCdwYXVzZScsIHdobylcbn1cblxuRGlyUmVhZGVyLnByb3RvdHlwZS5yZXN1bWUgPSBmdW5jdGlvbiAod2hvKSB7XG4gIHZhciBzZWxmID0gdGhpc1xuICBpZiAoIXNlbGYuX3BhdXNlZCkgcmV0dXJuXG4gIHdobyA9IHdobyB8fCBzZWxmXG5cbiAgc2VsZi5fcGF1c2VkID0gZmFsc2VcbiAgLy8gY29uc29sZS5lcnJvcignRFIgRW1pdCBSZXN1bWUnLCBzZWxmLl9wYXRoKVxuICBzZWxmLmVtaXQoJ3Jlc3VtZScsIHdobylcbiAgaWYgKHNlbGYuX3BhdXNlZCkge1xuICAgIC8vIGNvbnNvbGUuZXJyb3IoJ0RSIFJlLXBhdXNlZCcsIHNlbGYuX3BhdGgpXG4gICAgcmV0dXJuXG4gIH1cblxuICBpZiAoc2VsZi5fY3VycmVudEVudHJ5KSB7XG4gICAgaWYgKHNlbGYuX2N1cnJlbnRFbnRyeS5yZXN1bWUpIHNlbGYuX2N1cnJlbnRFbnRyeS5yZXN1bWUod2hvKVxuICB9IGVsc2Ugc2VsZi5fcmVhZCgpXG59XG5cbkRpclJlYWRlci5wcm90b3R5cGUuZW1pdEVudHJ5ID0gZnVuY3Rpb24gKGVudHJ5KSB7XG4gIHRoaXMuZW1pdCgnZW50cnknLCBlbnRyeSlcbiAgdGhpcy5lbWl0KCdjaGlsZCcsIGVudHJ5KVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/dir-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/dir-writer.js":
/*!************************************************!*\
  !*** ./node_modules/fstream/lib/dir-writer.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// It is expected that, when .add() returns false, the consumer\n// of the DirWriter will pause until a \"drain\" event occurs. Note\n// that this is *almost always going to be the case*, unless the\n// thing being written is some sort of unsupported type, and thus\n// skipped over.\n\nmodule.exports = DirWriter\n\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar mkdir = __webpack_require__(/*! mkdirp */ \"(ssr)/./node_modules/mkdirp/index.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar collect = __webpack_require__(/*! ./collect.js */ \"(ssr)/./node_modules/fstream/lib/collect.js\")\n\ninherits(DirWriter, Writer)\n\nfunction DirWriter (props) {\n  var self = this\n  if (!(self instanceof DirWriter)) {\n    self.error('DirWriter must be called as constructor.', null, true)\n  }\n\n  // should already be established as a Directory type\n  if (props.type !== 'Directory' || !props.Directory) {\n    self.error('Non-directory type ' + props.type + ' ' +\n      JSON.stringify(props), null, true)\n  }\n\n  Writer.call(this, props)\n}\n\nDirWriter.prototype._create = function () {\n  var self = this\n  mkdir(self._path, Writer.dirmode, function (er) {\n    if (er) return self.error(er)\n    // ready to start getting entries!\n    self.ready = true\n    self.emit('ready')\n    self._process()\n  })\n}\n\n// a DirWriter has an add(entry) method, but its .write() doesn't\n// do anything.  Why a no-op rather than a throw?  Because this\n// leaves open the door for writing directory metadata for\n// gnu/solaris style dumpdirs.\nDirWriter.prototype.write = function () {\n  return true\n}\n\nDirWriter.prototype.end = function () {\n  this._ended = true\n  this._process()\n}\n\nDirWriter.prototype.add = function (entry) {\n  var self = this\n\n  // console.error('\\tadd', entry._path, '->', self._path)\n  collect(entry)\n  if (!self.ready || self._currentEntry) {\n    self._buffer.push(entry)\n    return false\n  }\n\n  // create a new writer, and pipe the incoming entry into it.\n  if (self._ended) {\n    return self.error('add after end')\n  }\n\n  self._buffer.push(entry)\n  self._process()\n\n  return this._buffer.length === 0\n}\n\nDirWriter.prototype._process = function () {\n  var self = this\n\n  // console.error('DW Process p=%j', self._processing, self.basename)\n\n  if (self._processing) return\n\n  var entry = self._buffer.shift()\n  if (!entry) {\n    // console.error(\"DW Drain\")\n    self.emit('drain')\n    if (self._ended) self._finish()\n    return\n  }\n\n  self._processing = true\n  // console.error(\"DW Entry\", entry._path)\n\n  self.emit('entry', entry)\n\n  // ok, add this entry\n  //\n  // don't allow recursive copying\n  var p = entry\n  var pp\n  do {\n    pp = p._path || p.path\n    if (pp === self.root._path || pp === self._path ||\n      (pp && pp.indexOf(self._path) === 0)) {\n      // console.error('DW Exit (recursive)', entry.basename, self._path)\n      self._processing = false\n      if (entry._collected) entry.pipe()\n      return self._process()\n    }\n    p = p.parent\n  } while (p)\n\n  // console.error(\"DW not recursive\")\n\n  // chop off the entry's root dir, replace with ours\n  var props = {\n    parent: self,\n    root: self.root || self,\n    type: entry.type,\n    depth: self.depth + 1\n  }\n\n  pp = entry._path || entry.path || entry.props.path\n  if (entry.parent) {\n    pp = pp.substr(entry.parent._path.length + 1)\n  }\n  // get rid of any ../../ shenanigans\n  props.path = path.join(self.path, path.join('/', pp))\n\n  // if i have a filter, the child should inherit it.\n  props.filter = self.filter\n\n  // all the rest of the stuff, copy over from the source.\n  Object.keys(entry.props).forEach(function (k) {\n    if (!props.hasOwnProperty(k)) {\n      props[k] = entry.props[k]\n    }\n  })\n\n  // not sure at this point what kind of writer this is.\n  var child = self._currentChild = new Writer(props)\n  child.on('ready', function () {\n    // console.error(\"DW Child Ready\", child.type, child._path)\n    // console.error(\"  resuming\", entry._path)\n    entry.pipe(child)\n    entry.resume()\n  })\n\n  // XXX Make this work in node.\n  // Long filenames should not break stuff.\n  child.on('error', function (er) {\n    if (child._swallowErrors) {\n      self.warn(er)\n      child.emit('end')\n      child.emit('close')\n    } else {\n      self.emit('error', er)\n    }\n  })\n\n  // we fire _end internally *after* end, so that we don't move on\n  // until any \"end\" listeners have had their chance to do stuff.\n  child.on('close', onend)\n  var ended = false\n  function onend () {\n    if (ended) return\n    ended = true\n    // console.error(\"* DW Child end\", child.basename)\n    self._currentChild = null\n    self._processing = false\n    self._process()\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvZGlyLXdyaXRlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLGFBQWEsbUJBQU8sQ0FBQywrREFBYTtBQUNsQyxlQUFlLG1CQUFPLENBQUMsMkRBQVU7QUFDakMsWUFBWSxtQkFBTyxDQUFDLG9EQUFRO0FBQzVCLFdBQVcsbUJBQU8sQ0FBQyxrQkFBTTtBQUN6QixjQUFjLG1CQUFPLENBQUMsaUVBQWM7O0FBRXBDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJOztBQUVKOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2ZzdHJlYW0vbGliL2Rpci13cml0ZXIuanM/YTgzMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBJdCBpcyBleHBlY3RlZCB0aGF0LCB3aGVuIC5hZGQoKSByZXR1cm5zIGZhbHNlLCB0aGUgY29uc3VtZXJcbi8vIG9mIHRoZSBEaXJXcml0ZXIgd2lsbCBwYXVzZSB1bnRpbCBhIFwiZHJhaW5cIiBldmVudCBvY2N1cnMuIE5vdGVcbi8vIHRoYXQgdGhpcyBpcyAqYWxtb3N0IGFsd2F5cyBnb2luZyB0byBiZSB0aGUgY2FzZSosIHVubGVzcyB0aGVcbi8vIHRoaW5nIGJlaW5nIHdyaXR0ZW4gaXMgc29tZSBzb3J0IG9mIHVuc3VwcG9ydGVkIHR5cGUsIGFuZCB0aHVzXG4vLyBza2lwcGVkIG92ZXIuXG5cbm1vZHVsZS5leHBvcnRzID0gRGlyV3JpdGVyXG5cbnZhciBXcml0ZXIgPSByZXF1aXJlKCcuL3dyaXRlci5qcycpXG52YXIgaW5oZXJpdHMgPSByZXF1aXJlKCdpbmhlcml0cycpXG52YXIgbWtkaXIgPSByZXF1aXJlKCdta2RpcnAnKVxudmFyIHBhdGggPSByZXF1aXJlKCdwYXRoJylcbnZhciBjb2xsZWN0ID0gcmVxdWlyZSgnLi9jb2xsZWN0LmpzJylcblxuaW5oZXJpdHMoRGlyV3JpdGVyLCBXcml0ZXIpXG5cbmZ1bmN0aW9uIERpcldyaXRlciAocHJvcHMpIHtcbiAgdmFyIHNlbGYgPSB0aGlzXG4gIGlmICghKHNlbGYgaW5zdGFuY2VvZiBEaXJXcml0ZXIpKSB7XG4gICAgc2VsZi5lcnJvcignRGlyV3JpdGVyIG11c3QgYmUgY2FsbGVkIGFzIGNvbnN0cnVjdG9yLicsIG51bGwsIHRydWUpXG4gIH1cblxuICAvLyBzaG91bGQgYWxyZWFkeSBiZSBlc3RhYmxpc2hlZCBhcyBhIERpcmVjdG9yeSB0eXBlXG4gIGlmIChwcm9wcy50eXBlICE9PSAnRGlyZWN0b3J5JyB8fCAhcHJvcHMuRGlyZWN0b3J5KSB7XG4gICAgc2VsZi5lcnJvcignTm9uLWRpcmVjdG9yeSB0eXBlICcgKyBwcm9wcy50eXBlICsgJyAnICtcbiAgICAgIEpTT04uc3RyaW5naWZ5KHByb3BzKSwgbnVsbCwgdHJ1ZSlcbiAgfVxuXG4gIFdyaXRlci5jYWxsKHRoaXMsIHByb3BzKVxufVxuXG5EaXJXcml0ZXIucHJvdG90eXBlLl9jcmVhdGUgPSBmdW5jdGlvbiAoKSB7XG4gIHZhciBzZWxmID0gdGhpc1xuICBta2RpcihzZWxmLl9wYXRoLCBXcml0ZXIuZGlybW9kZSwgZnVuY3Rpb24gKGVyKSB7XG4gICAgaWYgKGVyKSByZXR1cm4gc2VsZi5lcnJvcihlcilcbiAgICAvLyByZWFkeSB0byBzdGFydCBnZXR0aW5nIGVudHJpZXMhXG4gICAgc2VsZi5yZWFkeSA9IHRydWVcbiAgICBzZWxmLmVtaXQoJ3JlYWR5JylcbiAgICBzZWxmLl9wcm9jZXNzKClcbiAgfSlcbn1cblxuLy8gYSBEaXJXcml0ZXIgaGFzIGFuIGFkZChlbnRyeSkgbWV0aG9kLCBidXQgaXRzIC53cml0ZSgpIGRvZXNuJ3Rcbi8vIGRvIGFueXRoaW5nLiAgV2h5IGEgbm8tb3AgcmF0aGVyIHRoYW4gYSB0aHJvdz8gIEJlY2F1c2UgdGhpc1xuLy8gbGVhdmVzIG9wZW4gdGhlIGRvb3IgZm9yIHdyaXRpbmcgZGlyZWN0b3J5IG1ldGFkYXRhIGZvclxuLy8gZ251L3NvbGFyaXMgc3R5bGUgZHVtcGRpcnMuXG5EaXJXcml0ZXIucHJvdG90eXBlLndyaXRlID0gZnVuY3Rpb24gKCkge1xuICByZXR1cm4gdHJ1ZVxufVxuXG5EaXJXcml0ZXIucHJvdG90eXBlLmVuZCA9IGZ1bmN0aW9uICgpIHtcbiAgdGhpcy5fZW5kZWQgPSB0cnVlXG4gIHRoaXMuX3Byb2Nlc3MoKVxufVxuXG5EaXJXcml0ZXIucHJvdG90eXBlLmFkZCA9IGZ1bmN0aW9uIChlbnRyeSkge1xuICB2YXIgc2VsZiA9IHRoaXNcblxuICAvLyBjb25zb2xlLmVycm9yKCdcXHRhZGQnLCBlbnRyeS5fcGF0aCwgJy0+Jywgc2VsZi5fcGF0aClcbiAgY29sbGVjdChlbnRyeSlcbiAgaWYgKCFzZWxmLnJlYWR5IHx8IHNlbGYuX2N1cnJlbnRFbnRyeSkge1xuICAgIHNlbGYuX2J1ZmZlci5wdXNoKGVudHJ5KVxuICAgIHJldHVybiBmYWxzZVxuICB9XG5cbiAgLy8gY3JlYXRlIGEgbmV3IHdyaXRlciwgYW5kIHBpcGUgdGhlIGluY29taW5nIGVudHJ5IGludG8gaXQuXG4gIGlmIChzZWxmLl9lbmRlZCkge1xuICAgIHJldHVybiBzZWxmLmVycm9yKCdhZGQgYWZ0ZXIgZW5kJylcbiAgfVxuXG4gIHNlbGYuX2J1ZmZlci5wdXNoKGVudHJ5KVxuICBzZWxmLl9wcm9jZXNzKClcblxuICByZXR1cm4gdGhpcy5fYnVmZmVyLmxlbmd0aCA9PT0gMFxufVxuXG5EaXJXcml0ZXIucHJvdG90eXBlLl9wcm9jZXNzID0gZnVuY3Rpb24gKCkge1xuICB2YXIgc2VsZiA9IHRoaXNcblxuICAvLyBjb25zb2xlLmVycm9yKCdEVyBQcm9jZXNzIHA9JWonLCBzZWxmLl9wcm9jZXNzaW5nLCBzZWxmLmJhc2VuYW1lKVxuXG4gIGlmIChzZWxmLl9wcm9jZXNzaW5nKSByZXR1cm5cblxuICB2YXIgZW50cnkgPSBzZWxmLl9idWZmZXIuc2hpZnQoKVxuICBpZiAoIWVudHJ5KSB7XG4gICAgLy8gY29uc29sZS5lcnJvcihcIkRXIERyYWluXCIpXG4gICAgc2VsZi5lbWl0KCdkcmFpbicpXG4gICAgaWYgKHNlbGYuX2VuZGVkKSBzZWxmLl9maW5pc2goKVxuICAgIHJldHVyblxuICB9XG5cbiAgc2VsZi5fcHJvY2Vzc2luZyA9IHRydWVcbiAgLy8gY29uc29sZS5lcnJvcihcIkRXIEVudHJ5XCIsIGVudHJ5Ll9wYXRoKVxuXG4gIHNlbGYuZW1pdCgnZW50cnknLCBlbnRyeSlcblxuICAvLyBvaywgYWRkIHRoaXMgZW50cnlcbiAgLy9cbiAgLy8gZG9uJ3QgYWxsb3cgcmVjdXJzaXZlIGNvcHlpbmdcbiAgdmFyIHAgPSBlbnRyeVxuICB2YXIgcHBcbiAgZG8ge1xuICAgIHBwID0gcC5fcGF0aCB8fCBwLnBhdGhcbiAgICBpZiAocHAgPT09IHNlbGYucm9vdC5fcGF0aCB8fCBwcCA9PT0gc2VsZi5fcGF0aCB8fFxuICAgICAgKHBwICYmIHBwLmluZGV4T2Yoc2VsZi5fcGF0aCkgPT09IDApKSB7XG4gICAgICAvLyBjb25zb2xlLmVycm9yKCdEVyBFeGl0IChyZWN1cnNpdmUpJywgZW50cnkuYmFzZW5hbWUsIHNlbGYuX3BhdGgpXG4gICAgICBzZWxmLl9wcm9jZXNzaW5nID0gZmFsc2VcbiAgICAgIGlmIChlbnRyeS5fY29sbGVjdGVkKSBlbnRyeS5waXBlKClcbiAgICAgIHJldHVybiBzZWxmLl9wcm9jZXNzKClcbiAgICB9XG4gICAgcCA9IHAucGFyZW50XG4gIH0gd2hpbGUgKHApXG5cbiAgLy8gY29uc29sZS5lcnJvcihcIkRXIG5vdCByZWN1cnNpdmVcIilcblxuICAvLyBjaG9wIG9mZiB0aGUgZW50cnkncyByb290IGRpciwgcmVwbGFjZSB3aXRoIG91cnNcbiAgdmFyIHByb3BzID0ge1xuICAgIHBhcmVudDogc2VsZixcbiAgICByb290OiBzZWxmLnJvb3QgfHwgc2VsZixcbiAgICB0eXBlOiBlbnRyeS50eXBlLFxuICAgIGRlcHRoOiBzZWxmLmRlcHRoICsgMVxuICB9XG5cbiAgcHAgPSBlbnRyeS5fcGF0aCB8fCBlbnRyeS5wYXRoIHx8IGVudHJ5LnByb3BzLnBhdGhcbiAgaWYgKGVudHJ5LnBhcmVudCkge1xuICAgIHBwID0gcHAuc3Vic3RyKGVudHJ5LnBhcmVudC5fcGF0aC5sZW5ndGggKyAxKVxuICB9XG4gIC8vIGdldCByaWQgb2YgYW55IC4uLy4uLyBzaGVuYW5pZ2Fuc1xuICBwcm9wcy5wYXRoID0gcGF0aC5qb2luKHNlbGYucGF0aCwgcGF0aC5qb2luKCcvJywgcHApKVxuXG4gIC8vIGlmIGkgaGF2ZSBhIGZpbHRlciwgdGhlIGNoaWxkIHNob3VsZCBpbmhlcml0IGl0LlxuICBwcm9wcy5maWx0ZXIgPSBzZWxmLmZpbHRlclxuXG4gIC8vIGFsbCB0aGUgcmVzdCBvZiB0aGUgc3R1ZmYsIGNvcHkgb3ZlciBmcm9tIHRoZSBzb3VyY2UuXG4gIE9iamVjdC5rZXlzKGVudHJ5LnByb3BzKS5mb3JFYWNoKGZ1bmN0aW9uIChrKSB7XG4gICAgaWYgKCFwcm9wcy5oYXNPd25Qcm9wZXJ0eShrKSkge1xuICAgICAgcHJvcHNba10gPSBlbnRyeS5wcm9wc1trXVxuICAgIH1cbiAgfSlcblxuICAvLyBub3Qgc3VyZSBhdCB0aGlzIHBvaW50IHdoYXQga2luZCBvZiB3cml0ZXIgdGhpcyBpcy5cbiAgdmFyIGNoaWxkID0gc2VsZi5fY3VycmVudENoaWxkID0gbmV3IFdyaXRlcihwcm9wcylcbiAgY2hpbGQub24oJ3JlYWR5JywgZnVuY3Rpb24gKCkge1xuICAgIC8vIGNvbnNvbGUuZXJyb3IoXCJEVyBDaGlsZCBSZWFkeVwiLCBjaGlsZC50eXBlLCBjaGlsZC5fcGF0aClcbiAgICAvLyBjb25zb2xlLmVycm9yKFwiICByZXN1bWluZ1wiLCBlbnRyeS5fcGF0aClcbiAgICBlbnRyeS5waXBlKGNoaWxkKVxuICAgIGVudHJ5LnJlc3VtZSgpXG4gIH0pXG5cbiAgLy8gWFhYIE1ha2UgdGhpcyB3b3JrIGluIG5vZGUuXG4gIC8vIExvbmcgZmlsZW5hbWVzIHNob3VsZCBub3QgYnJlYWsgc3R1ZmYuXG4gIGNoaWxkLm9uKCdlcnJvcicsIGZ1bmN0aW9uIChlcikge1xuICAgIGlmIChjaGlsZC5fc3dhbGxvd0Vycm9ycykge1xuICAgICAgc2VsZi53YXJuKGVyKVxuICAgICAgY2hpbGQuZW1pdCgnZW5kJylcbiAgICAgIGNoaWxkLmVtaXQoJ2Nsb3NlJylcbiAgICB9IGVsc2Uge1xuICAgICAgc2VsZi5lbWl0KCdlcnJvcicsIGVyKVxuICAgIH1cbiAgfSlcblxuICAvLyB3ZSBmaXJlIF9lbmQgaW50ZXJuYWxseSAqYWZ0ZXIqIGVuZCwgc28gdGhhdCB3ZSBkb24ndCBtb3ZlIG9uXG4gIC8vIHVudGlsIGFueSBcImVuZFwiIGxpc3RlbmVycyBoYXZlIGhhZCB0aGVpciBjaGFuY2UgdG8gZG8gc3R1ZmYuXG4gIGNoaWxkLm9uKCdjbG9zZScsIG9uZW5kKVxuICB2YXIgZW5kZWQgPSBmYWxzZVxuICBmdW5jdGlvbiBvbmVuZCAoKSB7XG4gICAgaWYgKGVuZGVkKSByZXR1cm5cbiAgICBlbmRlZCA9IHRydWVcbiAgICAvLyBjb25zb2xlLmVycm9yKFwiKiBEVyBDaGlsZCBlbmRcIiwgY2hpbGQuYmFzZW5hbWUpXG4gICAgc2VsZi5fY3VycmVudENoaWxkID0gbnVsbFxuICAgIHNlbGYuX3Byb2Nlc3NpbmcgPSBmYWxzZVxuICAgIHNlbGYuX3Byb2Nlc3MoKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/dir-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/file-reader.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/file-reader.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Basically just a wrapper around an fs.ReadStream\n\nmodule.exports = FileReader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\nvar EOF = {EOF: true}\nvar CLOSE = {CLOSE: true}\n\ninherits(FileReader, Reader)\n\nfunction FileReader (props) {\n  // console.error(\"    FR create\", props.path, props.size, new Error().stack)\n  var self = this\n  if (!(self instanceof FileReader)) {\n    throw new Error('FileReader must be called as constructor.')\n  }\n\n  // should already be established as a File type\n  // XXX Todo: preserve hardlinks by tracking dev+inode+nlink,\n  // with a HardLinkReader class.\n  if (!((props.type === 'Link' && props.Link) ||\n    (props.type === 'File' && props.File))) {\n    throw new Error('Non-file type ' + props.type)\n  }\n\n  self._buffer = []\n  self._bytesEmitted = 0\n  Reader.call(self, props)\n}\n\nFileReader.prototype._getStream = function () {\n  var self = this\n  var stream = self._stream = fs.createReadStream(self._path, self.props)\n\n  if (self.props.blksize) {\n    stream.bufferSize = self.props.blksize\n  }\n\n  stream.on('open', self.emit.bind(self, 'open'))\n\n  stream.on('data', function (c) {\n    // console.error('\\t\\t%d %s', c.length, self.basename)\n    self._bytesEmitted += c.length\n    // no point saving empty chunks\n    if (!c.length) {\n      return\n    } else if (self._paused || self._buffer.length) {\n      self._buffer.push(c)\n      self._read()\n    } else self.emit('data', c)\n  })\n\n  stream.on('end', function () {\n    if (self._paused || self._buffer.length) {\n      // console.error('FR Buffering End', self._path)\n      self._buffer.push(EOF)\n      self._read()\n    } else {\n      self.emit('end')\n    }\n\n    if (self._bytesEmitted !== self.props.size) {\n      self.error(\"Didn't get expected byte count\\n\" +\n        'expect: ' + self.props.size + '\\n' +\n        'actual: ' + self._bytesEmitted)\n    }\n  })\n\n  stream.on('close', function () {\n    if (self._paused || self._buffer.length) {\n      // console.error('FR Buffering Close', self._path)\n      self._buffer.push(CLOSE)\n      self._read()\n    } else {\n      // console.error('FR close 1', self._path)\n      self.emit('close')\n    }\n  })\n\n  stream.on('error', function (e) {\n    self.emit('error', e)\n  })\n\n  self._read()\n}\n\nFileReader.prototype._read = function () {\n  var self = this\n  // console.error('FR _read', self._path)\n  if (self._paused) {\n    // console.error('FR _read paused', self._path)\n    return\n  }\n\n  if (!self._stream) {\n    // console.error('FR _getStream calling', self._path)\n    return self._getStream()\n  }\n\n  // clear out the buffer, if there is one.\n  if (self._buffer.length) {\n    // console.error('FR _read has buffer', self._buffer.length, self._path)\n    var buf = self._buffer\n    for (var i = 0, l = buf.length; i < l; i++) {\n      var c = buf[i]\n      if (c === EOF) {\n        // console.error('FR Read emitting buffered end', self._path)\n        self.emit('end')\n      } else if (c === CLOSE) {\n        // console.error('FR Read emitting buffered close', self._path)\n        self.emit('close')\n      } else {\n        // console.error('FR Read emitting buffered data', self._path)\n        self.emit('data', c)\n      }\n\n      if (self._paused) {\n        // console.error('FR Read Re-pausing at '+i, self._path)\n        self._buffer = buf.slice(i)\n        return\n      }\n    }\n    self._buffer.length = 0\n  }\n// console.error(\"FR _read done\")\n// that's about all there is to it.\n}\n\nFileReader.prototype.pause = function (who) {\n  var self = this\n  // console.error('FR Pause', self._path)\n  if (self._paused) return\n  who = who || self\n  self._paused = true\n  if (self._stream) self._stream.pause()\n  self.emit('pause', who)\n}\n\nFileReader.prototype.resume = function (who) {\n  var self = this\n  // console.error('FR Resume', self._path)\n  if (!self._paused) return\n  who = who || self\n  self.emit('resume', who)\n  self._paused = false\n  if (self._stream) self._stream.resume()\n  self._read()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/file-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/file-writer.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/file-writer.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = FileWriter\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar EOF = {}\n\ninherits(FileWriter, Writer)\n\nfunction FileWriter (props) {\n  var self = this\n  if (!(self instanceof FileWriter)) {\n    throw new Error('FileWriter must be called as constructor.')\n  }\n\n  // should already be established as a File type\n  if (props.type !== 'File' || !props.File) {\n    throw new Error('Non-file type ' + props.type)\n  }\n\n  self._buffer = []\n  self._bytesWritten = 0\n\n  Writer.call(this, props)\n}\n\nFileWriter.prototype._create = function () {\n  var self = this\n  if (self._stream) return\n\n  var so = {}\n  if (self.props.flags) so.flags = self.props.flags\n  so.mode = Writer.filemode\n  if (self._old && self._old.blksize) so.bufferSize = self._old.blksize\n\n  self._stream = fs.createWriteStream(self._path, so)\n\n  self._stream.on('open', function () {\n    // console.error(\"FW open\", self._buffer, self._path)\n    self.ready = true\n    self._buffer.forEach(function (c) {\n      if (c === EOF) self._stream.end()\n      else self._stream.write(c)\n    })\n    self.emit('ready')\n    // give this a kick just in case it needs it.\n    self.emit('drain')\n  })\n\n  self._stream.on('error', function (er) { self.emit('error', er) })\n\n  self._stream.on('drain', function () { self.emit('drain') })\n\n  self._stream.on('close', function () {\n    // console.error('\\n\\nFW Stream Close', self._path, self.size)\n    self._finish()\n  })\n}\n\nFileWriter.prototype.write = function (c) {\n  var self = this\n\n  self._bytesWritten += c.length\n\n  if (!self.ready) {\n    if (!Buffer.isBuffer(c) && typeof c !== 'string') {\n      throw new Error('invalid write data')\n    }\n    self._buffer.push(c)\n    return false\n  }\n\n  var ret = self._stream.write(c)\n  // console.error('\\t-- fw wrote, _stream says', ret, self._stream._queue.length)\n\n  // allow 2 buffered writes, because otherwise there's just too\n  // much stop and go bs.\n  if (ret === false && self._stream._queue) {\n    return self._stream._queue.length <= 2\n  } else {\n    return ret\n  }\n}\n\nFileWriter.prototype.end = function (c) {\n  var self = this\n\n  if (c) self.write(c)\n\n  if (!self.ready) {\n    self._buffer.push(EOF)\n    return false\n  }\n\n  return self._stream.end()\n}\n\nFileWriter.prototype._finish = function () {\n  var self = this\n  if (typeof self.size === 'number' && self._bytesWritten !== self.size) {\n    self.error(\n      'Did not get expected byte count.\\n' +\n      'expect: ' + self.size + '\\n' +\n      'actual: ' + self._bytesWritten)\n  }\n  Writer.prototype._finish.call(self)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/file-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/get-type.js":
/*!**********************************************!*\
  !*** ./node_modules/fstream/lib/get-type.js ***!
  \**********************************************/
/***/ ((module) => {

eval("module.exports = getType\n\nfunction getType (st) {\n  var types = [\n    'Directory',\n    'File',\n    'SymbolicLink',\n    'Link', // special for hardlinks from tarballs\n    'BlockDevice',\n    'CharacterDevice',\n    'FIFO',\n    'Socket'\n  ]\n  var type\n\n  if (st.type && types.indexOf(st.type) !== -1) {\n    st[st.type] = true\n    return st.type\n  }\n\n  for (var i = 0, l = types.length; i < l; i++) {\n    type = types[i]\n    var is = st[type] || st['is' + type]\n    if (typeof is === 'function') is = is.call(st)\n    if (is) {\n      st[type] = true\n      st.type = type\n      return type\n    }\n  }\n\n  return null\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvZ2V0LXR5cGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG9DQUFvQyxPQUFPO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tcGFueS1hc3Npc3RhbnQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvZ2V0LXR5cGUuanM/YTg0NiJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGdldFR5cGVcblxuZnVuY3Rpb24gZ2V0VHlwZSAoc3QpIHtcbiAgdmFyIHR5cGVzID0gW1xuICAgICdEaXJlY3RvcnknLFxuICAgICdGaWxlJyxcbiAgICAnU3ltYm9saWNMaW5rJyxcbiAgICAnTGluaycsIC8vIHNwZWNpYWwgZm9yIGhhcmRsaW5rcyBmcm9tIHRhcmJhbGxzXG4gICAgJ0Jsb2NrRGV2aWNlJyxcbiAgICAnQ2hhcmFjdGVyRGV2aWNlJyxcbiAgICAnRklGTycsXG4gICAgJ1NvY2tldCdcbiAgXVxuICB2YXIgdHlwZVxuXG4gIGlmIChzdC50eXBlICYmIHR5cGVzLmluZGV4T2Yoc3QudHlwZSkgIT09IC0xKSB7XG4gICAgc3Rbc3QudHlwZV0gPSB0cnVlXG4gICAgcmV0dXJuIHN0LnR5cGVcbiAgfVxuXG4gIGZvciAodmFyIGkgPSAwLCBsID0gdHlwZXMubGVuZ3RoOyBpIDwgbDsgaSsrKSB7XG4gICAgdHlwZSA9IHR5cGVzW2ldXG4gICAgdmFyIGlzID0gc3RbdHlwZV0gfHwgc3RbJ2lzJyArIHR5cGVdXG4gICAgaWYgKHR5cGVvZiBpcyA9PT0gJ2Z1bmN0aW9uJykgaXMgPSBpcy5jYWxsKHN0KVxuICAgIGlmIChpcykge1xuICAgICAgc3RbdHlwZV0gPSB0cnVlXG4gICAgICBzdC50eXBlID0gdHlwZVxuICAgICAgcmV0dXJuIHR5cGVcbiAgICB9XG4gIH1cblxuICByZXR1cm4gbnVsbFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/get-type.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/link-reader.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/link-reader.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Basically just a wrapper around an fs.readlink\n//\n// XXX: Enhance this to support the Link type, by keeping\n// a lookup table of {<dev+inode>:<path>}, so that hardlinks\n// can be preserved in tarballs.\n\nmodule.exports = LinkReader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\n\ninherits(LinkReader, Reader)\n\nfunction LinkReader (props) {\n  var self = this\n  if (!(self instanceof LinkReader)) {\n    throw new Error('LinkReader must be called as constructor.')\n  }\n\n  if (!((props.type === 'Link' && props.Link) ||\n    (props.type === 'SymbolicLink' && props.SymbolicLink))) {\n    throw new Error('Non-link type ' + props.type)\n  }\n\n  Reader.call(self, props)\n}\n\n// When piping a LinkReader into a LinkWriter, we have to\n// already have the linkpath property set, so that has to\n// happen *before* the \"ready\" event, which means we need to\n// override the _stat method.\nLinkReader.prototype._stat = function (currentStat) {\n  var self = this\n  fs.readlink(self._path, function (er, linkpath) {\n    if (er) return self.error(er)\n    self.linkpath = self.props.linkpath = linkpath\n    self.emit('linkpath', linkpath)\n    Reader.prototype._stat.call(self, currentStat)\n  })\n}\n\nLinkReader.prototype._read = function () {\n  var self = this\n  if (self._paused) return\n  // basically just a no-op, since we got all the info we need\n  // from the _stat method\n  if (!self._ended) {\n    self.emit('end')\n    self.emit('close')\n    self._ended = true\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/link-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/link-writer.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/link-writer.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = LinkWriter\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar rimraf = __webpack_require__(/*! rimraf */ \"(ssr)/./node_modules/fstream/node_modules/rimraf/rimraf.js\")\n\ninherits(LinkWriter, Writer)\n\nfunction LinkWriter (props) {\n  var self = this\n  if (!(self instanceof LinkWriter)) {\n    throw new Error('LinkWriter must be called as constructor.')\n  }\n\n  // should already be established as a Link type\n  if (!((props.type === 'Link' && props.Link) ||\n    (props.type === 'SymbolicLink' && props.SymbolicLink))) {\n    throw new Error('Non-link type ' + props.type)\n  }\n\n  if (props.linkpath === '') props.linkpath = '.'\n  if (!props.linkpath) {\n    self.error('Need linkpath property to create ' + props.type)\n  }\n\n  Writer.call(this, props)\n}\n\nLinkWriter.prototype._create = function () {\n  // console.error(\" LW _create\")\n  var self = this\n  var hard = self.type === 'Link' || process.platform === 'win32'\n  var link = hard ? 'link' : 'symlink'\n  var lp = hard ? path.resolve(self.dirname, self.linkpath) : self.linkpath\n\n  // can only change the link path by clobbering\n  // For hard links, let's just assume that's always the case, since\n  // there's no good way to read them if we don't already know.\n  if (hard) return clobber(self, lp, link)\n\n  fs.readlink(self._path, function (er, p) {\n    // only skip creation if it's exactly the same link\n    if (p && p === lp) return finish(self)\n    clobber(self, lp, link)\n  })\n}\n\nfunction clobber (self, lp, link) {\n  rimraf(self._path, function (er) {\n    if (er) return self.error(er)\n    create(self, lp, link)\n  })\n}\n\nfunction create (self, lp, link) {\n  fs[link](lp, self._path, function (er) {\n    // if this is a hard link, and we're in the process of writing out a\n    // directory, it's very possible that the thing we're linking to\n    // doesn't exist yet (especially if it was intended as a symlink),\n    // so swallow ENOENT errors here and just soldier in.\n    // Additionally, an EPERM or EACCES can happen on win32 if it's trying\n    // to make a link to a directory.  Again, just skip it.\n    // A better solution would be to have fs.symlink be supported on\n    // windows in some nice fashion.\n    if (er) {\n      if ((er.code === 'ENOENT' ||\n        er.code === 'EACCES' ||\n        er.code === 'EPERM') && process.platform === 'win32') {\n        self.ready = true\n        self.emit('ready')\n        self.emit('end')\n        self.emit('close')\n        self.end = self._finish = function () {}\n      } else return self.error(er)\n    }\n    finish(self)\n  })\n}\n\nfunction finish (self) {\n  self.ready = true\n  self.emit('ready')\n  if (self._ended && !self._finished) self._finish()\n}\n\nLinkWriter.prototype.end = function () {\n  // console.error(\"LW finish in end\")\n  this._ended = true\n  if (this.ready) {\n    this._finished = true\n    this._finish()\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/link-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/proxy-reader.js":
/*!**************************************************!*\
  !*** ./node_modules/fstream/lib/proxy-reader.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// A reader for when we don't yet know what kind of thing\n// the thing is.\n\nmodule.exports = ProxyReader\n\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(ssr)/./node_modules/fstream/lib/get-type.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\n\ninherits(ProxyReader, Reader)\n\nfunction ProxyReader (props) {\n  var self = this\n  if (!(self instanceof ProxyReader)) {\n    throw new Error('ProxyReader must be called as constructor.')\n  }\n\n  self.props = props\n  self._buffer = []\n  self.ready = false\n\n  Reader.call(self, props)\n}\n\nProxyReader.prototype._stat = function () {\n  var self = this\n  var props = self.props\n  // stat the thing to see what the proxy should be.\n  var stat = props.follow ? 'stat' : 'lstat'\n\n  fs[stat](props.path, function (er, current) {\n    var type\n    if (er || !current) {\n      type = 'File'\n    } else {\n      type = getType(current)\n    }\n\n    props[type] = true\n    props.type = self.type = type\n\n    self._old = current\n    self._addProxy(Reader(props, current))\n  })\n}\n\nProxyReader.prototype._addProxy = function (proxy) {\n  var self = this\n  if (self._proxyTarget) {\n    return self.error('proxy already set')\n  }\n\n  self._proxyTarget = proxy\n  proxy._proxy = self\n\n  ;[\n    'error',\n    'data',\n    'end',\n    'close',\n    'linkpath',\n    'entry',\n    'entryEnd',\n    'child',\n    'childEnd',\n    'warn',\n    'stat'\n  ].forEach(function (ev) {\n    // console.error('~~ proxy event', ev, self.path)\n    proxy.on(ev, self.emit.bind(self, ev))\n  })\n\n  self.emit('proxy', proxy)\n\n  proxy.on('ready', function () {\n    // console.error(\"~~ proxy is ready!\", self.path)\n    self.ready = true\n    self.emit('ready')\n  })\n\n  var calls = self._buffer\n  self._buffer.length = 0\n  calls.forEach(function (c) {\n    proxy[c[0]].apply(proxy, c[1])\n  })\n}\n\nProxyReader.prototype.pause = function () {\n  return this._proxyTarget ? this._proxyTarget.pause() : false\n}\n\nProxyReader.prototype.resume = function () {\n  return this._proxyTarget ? this._proxyTarget.resume() : false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/proxy-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/proxy-writer.js":
/*!**************************************************!*\
  !*** ./node_modules/fstream/lib/proxy-writer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// A writer for when we don't know what kind of thing\n// the thing is.  That is, it's not explicitly set,\n// so we're going to make it whatever the thing already\n// is, or \"File\"\n//\n// Until then, collect all events.\n\nmodule.exports = ProxyWriter\n\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(ssr)/./node_modules/fstream/lib/get-type.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar collect = __webpack_require__(/*! ./collect.js */ \"(ssr)/./node_modules/fstream/lib/collect.js\")\nvar fs = __webpack_require__(/*! fs */ \"fs\")\n\ninherits(ProxyWriter, Writer)\n\nfunction ProxyWriter (props) {\n  var self = this\n  if (!(self instanceof ProxyWriter)) {\n    throw new Error('ProxyWriter must be called as constructor.')\n  }\n\n  self.props = props\n  self._needDrain = false\n\n  Writer.call(self, props)\n}\n\nProxyWriter.prototype._stat = function () {\n  var self = this\n  var props = self.props\n  // stat the thing to see what the proxy should be.\n  var stat = props.follow ? 'stat' : 'lstat'\n\n  fs[stat](props.path, function (er, current) {\n    var type\n    if (er || !current) {\n      type = 'File'\n    } else {\n      type = getType(current)\n    }\n\n    props[type] = true\n    props.type = self.type = type\n\n    self._old = current\n    self._addProxy(Writer(props, current))\n  })\n}\n\nProxyWriter.prototype._addProxy = function (proxy) {\n  // console.error(\"~~ set proxy\", this.path)\n  var self = this\n  if (self._proxy) {\n    return self.error('proxy already set')\n  }\n\n  self._proxy = proxy\n  ;[\n    'ready',\n    'error',\n    'close',\n    'pipe',\n    'drain',\n    'warn'\n  ].forEach(function (ev) {\n    proxy.on(ev, self.emit.bind(self, ev))\n  })\n\n  self.emit('proxy', proxy)\n\n  var calls = self._buffer\n  calls.forEach(function (c) {\n    // console.error(\"~~ ~~ proxy buffered call\", c[0], c[1])\n    proxy[c[0]].apply(proxy, c[1])\n  })\n  self._buffer.length = 0\n  if (self._needsDrain) self.emit('drain')\n}\n\nProxyWriter.prototype.add = function (entry) {\n  // console.error(\"~~ proxy add\")\n  collect(entry)\n\n  if (!this._proxy) {\n    this._buffer.push(['add', [entry]])\n    this._needDrain = true\n    return false\n  }\n  return this._proxy.add(entry)\n}\n\nProxyWriter.prototype.write = function (c) {\n  // console.error('~~ proxy write')\n  if (!this._proxy) {\n    this._buffer.push(['write', [c]])\n    this._needDrain = true\n    return false\n  }\n  return this._proxy.write(c)\n}\n\nProxyWriter.prototype.end = function (c) {\n  // console.error('~~ proxy end')\n  if (!this._proxy) {\n    this._buffer.push(['end', [c]])\n    return false\n  }\n  return this._proxy.end(c)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvcHJveHktd3JpdGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxhQUFhLG1CQUFPLENBQUMsK0RBQWE7QUFDbEMsY0FBYyxtQkFBTyxDQUFDLG1FQUFlO0FBQ3JDLGVBQWUsbUJBQU8sQ0FBQywyREFBVTtBQUNqQyxjQUFjLG1CQUFPLENBQUMsaUVBQWM7QUFDcEMsU0FBUyxtQkFBTyxDQUFDLGNBQUk7O0FBRXJCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9mc3RyZWFtL2xpYi9wcm94eS13cml0ZXIuanM/NTZiNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBBIHdyaXRlciBmb3Igd2hlbiB3ZSBkb24ndCBrbm93IHdoYXQga2luZCBvZiB0aGluZ1xuLy8gdGhlIHRoaW5nIGlzLiAgVGhhdCBpcywgaXQncyBub3QgZXhwbGljaXRseSBzZXQsXG4vLyBzbyB3ZSdyZSBnb2luZyB0byBtYWtlIGl0IHdoYXRldmVyIHRoZSB0aGluZyBhbHJlYWR5XG4vLyBpcywgb3IgXCJGaWxlXCJcbi8vXG4vLyBVbnRpbCB0aGVuLCBjb2xsZWN0IGFsbCBldmVudHMuXG5cbm1vZHVsZS5leHBvcnRzID0gUHJveHlXcml0ZXJcblxudmFyIFdyaXRlciA9IHJlcXVpcmUoJy4vd3JpdGVyLmpzJylcbnZhciBnZXRUeXBlID0gcmVxdWlyZSgnLi9nZXQtdHlwZS5qcycpXG52YXIgaW5oZXJpdHMgPSByZXF1aXJlKCdpbmhlcml0cycpXG52YXIgY29sbGVjdCA9IHJlcXVpcmUoJy4vY29sbGVjdC5qcycpXG52YXIgZnMgPSByZXF1aXJlKCdmcycpXG5cbmluaGVyaXRzKFByb3h5V3JpdGVyLCBXcml0ZXIpXG5cbmZ1bmN0aW9uIFByb3h5V3JpdGVyIChwcm9wcykge1xuICB2YXIgc2VsZiA9IHRoaXNcbiAgaWYgKCEoc2VsZiBpbnN0YW5jZW9mIFByb3h5V3JpdGVyKSkge1xuICAgIHRocm93IG5ldyBFcnJvcignUHJveHlXcml0ZXIgbXVzdCBiZSBjYWxsZWQgYXMgY29uc3RydWN0b3IuJylcbiAgfVxuXG4gIHNlbGYucHJvcHMgPSBwcm9wc1xuICBzZWxmLl9uZWVkRHJhaW4gPSBmYWxzZVxuXG4gIFdyaXRlci5jYWxsKHNlbGYsIHByb3BzKVxufVxuXG5Qcm94eVdyaXRlci5wcm90b3R5cGUuX3N0YXQgPSBmdW5jdGlvbiAoKSB7XG4gIHZhciBzZWxmID0gdGhpc1xuICB2YXIgcHJvcHMgPSBzZWxmLnByb3BzXG4gIC8vIHN0YXQgdGhlIHRoaW5nIHRvIHNlZSB3aGF0IHRoZSBwcm94eSBzaG91bGQgYmUuXG4gIHZhciBzdGF0ID0gcHJvcHMuZm9sbG93ID8gJ3N0YXQnIDogJ2xzdGF0J1xuXG4gIGZzW3N0YXRdKHByb3BzLnBhdGgsIGZ1bmN0aW9uIChlciwgY3VycmVudCkge1xuICAgIHZhciB0eXBlXG4gICAgaWYgKGVyIHx8ICFjdXJyZW50KSB7XG4gICAgICB0eXBlID0gJ0ZpbGUnXG4gICAgfSBlbHNlIHtcbiAgICAgIHR5cGUgPSBnZXRUeXBlKGN1cnJlbnQpXG4gICAgfVxuXG4gICAgcHJvcHNbdHlwZV0gPSB0cnVlXG4gICAgcHJvcHMudHlwZSA9IHNlbGYudHlwZSA9IHR5cGVcblxuICAgIHNlbGYuX29sZCA9IGN1cnJlbnRcbiAgICBzZWxmLl9hZGRQcm94eShXcml0ZXIocHJvcHMsIGN1cnJlbnQpKVxuICB9KVxufVxuXG5Qcm94eVdyaXRlci5wcm90b3R5cGUuX2FkZFByb3h5ID0gZnVuY3Rpb24gKHByb3h5KSB7XG4gIC8vIGNvbnNvbGUuZXJyb3IoXCJ+fiBzZXQgcHJveHlcIiwgdGhpcy5wYXRoKVxuICB2YXIgc2VsZiA9IHRoaXNcbiAgaWYgKHNlbGYuX3Byb3h5KSB7XG4gICAgcmV0dXJuIHNlbGYuZXJyb3IoJ3Byb3h5IGFscmVhZHkgc2V0JylcbiAgfVxuXG4gIHNlbGYuX3Byb3h5ID0gcHJveHlcbiAgO1tcbiAgICAncmVhZHknLFxuICAgICdlcnJvcicsXG4gICAgJ2Nsb3NlJyxcbiAgICAncGlwZScsXG4gICAgJ2RyYWluJyxcbiAgICAnd2FybidcbiAgXS5mb3JFYWNoKGZ1bmN0aW9uIChldikge1xuICAgIHByb3h5Lm9uKGV2LCBzZWxmLmVtaXQuYmluZChzZWxmLCBldikpXG4gIH0pXG5cbiAgc2VsZi5lbWl0KCdwcm94eScsIHByb3h5KVxuXG4gIHZhciBjYWxscyA9IHNlbGYuX2J1ZmZlclxuICBjYWxscy5mb3JFYWNoKGZ1bmN0aW9uIChjKSB7XG4gICAgLy8gY29uc29sZS5lcnJvcihcIn5+IH5+IHByb3h5IGJ1ZmZlcmVkIGNhbGxcIiwgY1swXSwgY1sxXSlcbiAgICBwcm94eVtjWzBdXS5hcHBseShwcm94eSwgY1sxXSlcbiAgfSlcbiAgc2VsZi5fYnVmZmVyLmxlbmd0aCA9IDBcbiAgaWYgKHNlbGYuX25lZWRzRHJhaW4pIHNlbGYuZW1pdCgnZHJhaW4nKVxufVxuXG5Qcm94eVdyaXRlci5wcm90b3R5cGUuYWRkID0gZnVuY3Rpb24gKGVudHJ5KSB7XG4gIC8vIGNvbnNvbGUuZXJyb3IoXCJ+fiBwcm94eSBhZGRcIilcbiAgY29sbGVjdChlbnRyeSlcblxuICBpZiAoIXRoaXMuX3Byb3h5KSB7XG4gICAgdGhpcy5fYnVmZmVyLnB1c2goWydhZGQnLCBbZW50cnldXSlcbiAgICB0aGlzLl9uZWVkRHJhaW4gPSB0cnVlXG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cbiAgcmV0dXJuIHRoaXMuX3Byb3h5LmFkZChlbnRyeSlcbn1cblxuUHJveHlXcml0ZXIucHJvdG90eXBlLndyaXRlID0gZnVuY3Rpb24gKGMpIHtcbiAgLy8gY29uc29sZS5lcnJvcignfn4gcHJveHkgd3JpdGUnKVxuICBpZiAoIXRoaXMuX3Byb3h5KSB7XG4gICAgdGhpcy5fYnVmZmVyLnB1c2goWyd3cml0ZScsIFtjXV0pXG4gICAgdGhpcy5fbmVlZERyYWluID0gdHJ1ZVxuICAgIHJldHVybiBmYWxzZVxuICB9XG4gIHJldHVybiB0aGlzLl9wcm94eS53cml0ZShjKVxufVxuXG5Qcm94eVdyaXRlci5wcm90b3R5cGUuZW5kID0gZnVuY3Rpb24gKGMpIHtcbiAgLy8gY29uc29sZS5lcnJvcignfn4gcHJveHkgZW5kJylcbiAgaWYgKCF0aGlzLl9wcm94eSkge1xuICAgIHRoaXMuX2J1ZmZlci5wdXNoKFsnZW5kJywgW2NdXSlcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuICByZXR1cm4gdGhpcy5fcHJveHkuZW5kKGMpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/proxy-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/reader.js":
/*!********************************************!*\
  !*** ./node_modules/fstream/lib/reader.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = Reader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream)\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(ssr)/./node_modules/fstream/lib/get-type.js\")\nvar hardLinks = Reader.hardLinks = {}\nvar Abstract = __webpack_require__(/*! ./abstract.js */ \"(ssr)/./node_modules/fstream/lib/abstract.js\")\n\n// Must do this *before* loading the child classes\ninherits(Reader, Abstract)\n\nvar LinkReader = __webpack_require__(/*! ./link-reader.js */ \"(ssr)/./node_modules/fstream/lib/link-reader.js\")\n\nfunction Reader (props, currentStat) {\n  var self = this\n  if (!(self instanceof Reader)) return new Reader(props, currentStat)\n\n  if (typeof props === 'string') {\n    props = { path: props }\n  }\n\n  // polymorphism.\n  // call fstream.Reader(dir) to get a DirReader object, etc.\n  // Note that, unlike in the Writer case, ProxyReader is going\n  // to be the *normal* state of affairs, since we rarely know\n  // the type of a file prior to reading it.\n\n  var type\n  var ClassType\n\n  if (props.type && typeof props.type === 'function') {\n    type = props.type\n    ClassType = type\n  } else {\n    type = getType(props)\n    ClassType = Reader\n  }\n\n  if (currentStat && !type) {\n    type = getType(currentStat)\n    props[type] = true\n    props.type = type\n  }\n\n  switch (type) {\n    case 'Directory':\n      ClassType = __webpack_require__(/*! ./dir-reader.js */ \"(ssr)/./node_modules/fstream/lib/dir-reader.js\")\n      break\n\n    case 'Link':\n    // XXX hard links are just files.\n    // However, it would be good to keep track of files' dev+inode\n    // and nlink values, and create a HardLinkReader that emits\n    // a linkpath value of the original copy, so that the tar\n    // writer can preserve them.\n    // ClassType = HardLinkReader\n    // break\n\n    case 'File':\n      ClassType = __webpack_require__(/*! ./file-reader.js */ \"(ssr)/./node_modules/fstream/lib/file-reader.js\")\n      break\n\n    case 'SymbolicLink':\n      ClassType = LinkReader\n      break\n\n    case 'Socket':\n      ClassType = __webpack_require__(/*! ./socket-reader.js */ \"(ssr)/./node_modules/fstream/lib/socket-reader.js\")\n      break\n\n    case null:\n      ClassType = __webpack_require__(/*! ./proxy-reader.js */ \"(ssr)/./node_modules/fstream/lib/proxy-reader.js\")\n      break\n  }\n\n  if (!(self instanceof ClassType)) {\n    return new ClassType(props)\n  }\n\n  Abstract.call(self)\n\n  if (!props.path) {\n    self.error('Must provide a path', null, true)\n  }\n\n  self.readable = true\n  self.writable = false\n\n  self.type = type\n  self.props = props\n  self.depth = props.depth = props.depth || 0\n  self.parent = props.parent || null\n  self.root = props.root || (props.parent && props.parent.root) || self\n\n  self._path = self.path = path.resolve(props.path)\n  if (process.platform === 'win32') {\n    self.path = self._path = self.path.replace(/\\?/g, '_')\n    if (self._path.length >= 260) {\n      // how DOES one create files on the moon?\n      // if the path has spaces in it, then UNC will fail.\n      self._swallowErrors = true\n      // if (self._path.indexOf(\" \") === -1) {\n      self._path = '\\\\\\\\?\\\\' + self.path.replace(/\\//g, '\\\\')\n    // }\n    }\n  }\n  self.basename = props.basename = path.basename(self.path)\n  self.dirname = props.dirname = path.dirname(self.path)\n\n  // these have served their purpose, and are now just noisy clutter\n  props.parent = props.root = null\n\n  // console.error(\"\\n\\n\\n%s setting size to\", props.path, props.size)\n  self.size = props.size\n  self.filter = typeof props.filter === 'function' ? props.filter : null\n  if (props.sort === 'alpha') props.sort = alphasort\n\n  // start the ball rolling.\n  // this will stat the thing, and then call self._read()\n  // to start reading whatever it is.\n  // console.error(\"calling stat\", props.path, currentStat)\n  self._stat(currentStat)\n}\n\nfunction alphasort (a, b) {\n  return a === b ? 0\n    : a.toLowerCase() > b.toLowerCase() ? 1\n      : a.toLowerCase() < b.toLowerCase() ? -1\n        : a > b ? 1\n          : -1\n}\n\nReader.prototype._stat = function (currentStat) {\n  var self = this\n  var props = self.props\n  var stat = props.follow ? 'stat' : 'lstat'\n  // console.error(\"Reader._stat\", self._path, currentStat)\n  if (currentStat) process.nextTick(statCb.bind(null, null, currentStat))\n  else fs[stat](self._path, statCb)\n\n  function statCb (er, props_) {\n    // console.error(\"Reader._stat, statCb\", self._path, props_, props_.nlink)\n    if (er) return self.error(er)\n\n    Object.keys(props_).forEach(function (k) {\n      props[k] = props_[k]\n    })\n\n    // if it's not the expected size, then abort here.\n    if (undefined !== self.size && props.size !== self.size) {\n      return self.error('incorrect size')\n    }\n    self.size = props.size\n\n    var type = getType(props)\n    var handleHardlinks = props.hardlinks !== false\n\n    // special little thing for handling hardlinks.\n    if (handleHardlinks && type !== 'Directory' && props.nlink && props.nlink > 1) {\n      var k = props.dev + ':' + props.ino\n      // console.error(\"Reader has nlink\", self._path, k)\n      if (hardLinks[k] === self._path || !hardLinks[k]) {\n        hardLinks[k] = self._path\n      } else {\n        // switch into hardlink mode.\n        type = self.type = self.props.type = 'Link'\n        self.Link = self.props.Link = true\n        self.linkpath = self.props.linkpath = hardLinks[k]\n        // console.error(\"Hardlink detected, switching mode\", self._path, self.linkpath)\n        // Setting __proto__ would arguably be the \"correct\"\n        // approach here, but that just seems too wrong.\n        self._stat = self._read = LinkReader.prototype._read\n      }\n    }\n\n    if (self.type && self.type !== type) {\n      self.error('Unexpected type: ' + type)\n    }\n\n    // if the filter doesn't pass, then just skip over this one.\n    // still have to emit end so that dir-walking can move on.\n    if (self.filter) {\n      var who = self._proxy || self\n      // special handling for ProxyReaders\n      if (!self.filter.call(who, who, props)) {\n        if (!self._disowned) {\n          self.abort()\n          self.emit('end')\n          self.emit('close')\n        }\n        return\n      }\n    }\n\n    // last chance to abort or disown before the flow starts!\n    var events = ['_stat', 'stat', 'ready']\n    var e = 0\n    ;(function go () {\n      if (self._aborted) {\n        self.emit('end')\n        self.emit('close')\n        return\n      }\n\n      if (self._paused && self.type !== 'Directory') {\n        self.once('resume', go)\n        return\n      }\n\n      var ev = events[e++]\n      if (!ev) {\n        return self._read()\n      }\n      self.emit(ev, props)\n      go()\n    })()\n  }\n}\n\nReader.prototype.pipe = function (dest) {\n  var self = this\n  if (typeof dest.add === 'function') {\n    // piping to a multi-compatible, and we've got directory entries.\n    self.on('entry', function (entry) {\n      var ret = dest.add(entry)\n      if (ret === false) {\n        self.pause()\n      }\n    })\n  }\n\n  // console.error(\"R Pipe apply Stream Pipe\")\n  return Stream.prototype.pipe.apply(this, arguments)\n}\n\nReader.prototype.pause = function (who) {\n  this._paused = true\n  who = who || this\n  this.emit('pause', who)\n  if (this._stream) this._stream.pause(who)\n}\n\nReader.prototype.resume = function (who) {\n  this._paused = false\n  who = who || this\n  this.emit('resume', who)\n  if (this._stream) this._stream.resume(who)\n  this._read()\n}\n\nReader.prototype._read = function () {\n  this.error('Cannot read unknown type: ' + this.type)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/socket-reader.js":
/*!***************************************************!*\
  !*** ./node_modules/fstream/lib/socket-reader.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Just get the stats, and then don't do anything.\n// You can't really \"read\" from a socket.  You \"connect\" to it.\n// Mostly, this is here so that reading a dir with a socket in it\n// doesn't blow up.\n\nmodule.exports = SocketReader\n\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\n\ninherits(SocketReader, Reader)\n\nfunction SocketReader (props) {\n  var self = this\n  if (!(self instanceof SocketReader)) {\n    throw new Error('SocketReader must be called as constructor.')\n  }\n\n  if (!(props.type === 'Socket' && props.Socket)) {\n    throw new Error('Non-socket type ' + props.type)\n  }\n\n  Reader.call(self, props)\n}\n\nSocketReader.prototype._read = function () {\n  var self = this\n  if (self._paused) return\n  // basically just a no-op, since we got all the info we have\n  // from the _stat method\n  if (!self._ended) {\n    self.emit('end')\n    self.emit('close')\n    self._ended = true\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvc29ja2V0LXJlYWRlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxlQUFlLG1CQUFPLENBQUMsMkRBQVU7QUFDakMsYUFBYSxtQkFBTyxDQUFDLCtEQUFhOztBQUVsQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9mc3RyZWFtL2xpYi9zb2NrZXQtcmVhZGVyLmpzPzdmNzEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gSnVzdCBnZXQgdGhlIHN0YXRzLCBhbmQgdGhlbiBkb24ndCBkbyBhbnl0aGluZy5cbi8vIFlvdSBjYW4ndCByZWFsbHkgXCJyZWFkXCIgZnJvbSBhIHNvY2tldC4gIFlvdSBcImNvbm5lY3RcIiB0byBpdC5cbi8vIE1vc3RseSwgdGhpcyBpcyBoZXJlIHNvIHRoYXQgcmVhZGluZyBhIGRpciB3aXRoIGEgc29ja2V0IGluIGl0XG4vLyBkb2Vzbid0IGJsb3cgdXAuXG5cbm1vZHVsZS5leHBvcnRzID0gU29ja2V0UmVhZGVyXG5cbnZhciBpbmhlcml0cyA9IHJlcXVpcmUoJ2luaGVyaXRzJylcbnZhciBSZWFkZXIgPSByZXF1aXJlKCcuL3JlYWRlci5qcycpXG5cbmluaGVyaXRzKFNvY2tldFJlYWRlciwgUmVhZGVyKVxuXG5mdW5jdGlvbiBTb2NrZXRSZWFkZXIgKHByb3BzKSB7XG4gIHZhciBzZWxmID0gdGhpc1xuICBpZiAoIShzZWxmIGluc3RhbmNlb2YgU29ja2V0UmVhZGVyKSkge1xuICAgIHRocm93IG5ldyBFcnJvcignU29ja2V0UmVhZGVyIG11c3QgYmUgY2FsbGVkIGFzIGNvbnN0cnVjdG9yLicpXG4gIH1cblxuICBpZiAoIShwcm9wcy50eXBlID09PSAnU29ja2V0JyAmJiBwcm9wcy5Tb2NrZXQpKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdOb24tc29ja2V0IHR5cGUgJyArIHByb3BzLnR5cGUpXG4gIH1cblxuICBSZWFkZXIuY2FsbChzZWxmLCBwcm9wcylcbn1cblxuU29ja2V0UmVhZGVyLnByb3RvdHlwZS5fcmVhZCA9IGZ1bmN0aW9uICgpIHtcbiAgdmFyIHNlbGYgPSB0aGlzXG4gIGlmIChzZWxmLl9wYXVzZWQpIHJldHVyblxuICAvLyBiYXNpY2FsbHkganVzdCBhIG5vLW9wLCBzaW5jZSB3ZSBnb3QgYWxsIHRoZSBpbmZvIHdlIGhhdmVcbiAgLy8gZnJvbSB0aGUgX3N0YXQgbWV0aG9kXG4gIGlmICghc2VsZi5fZW5kZWQpIHtcbiAgICBzZWxmLmVtaXQoJ2VuZCcpXG4gICAgc2VsZi5lbWl0KCdjbG9zZScpXG4gICAgc2VsZi5fZW5kZWQgPSB0cnVlXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/socket-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/writer.js":
/*!********************************************!*\
  !*** ./node_modules/fstream/lib/writer.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = Writer\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar rimraf = __webpack_require__(/*! rimraf */ \"(ssr)/./node_modules/fstream/node_modules/rimraf/rimraf.js\")\nvar mkdir = __webpack_require__(/*! mkdirp */ \"(ssr)/./node_modules/mkdirp/index.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar umask = process.platform === 'win32' ? 0 : process.umask()\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(ssr)/./node_modules/fstream/lib/get-type.js\")\nvar Abstract = __webpack_require__(/*! ./abstract.js */ \"(ssr)/./node_modules/fstream/lib/abstract.js\")\n\n// Must do this *before* loading the child classes\ninherits(Writer, Abstract)\n\nWriter.dirmode = parseInt('0777', 8) & (~umask)\nWriter.filemode = parseInt('0666', 8) & (~umask)\n\nvar DirWriter = __webpack_require__(/*! ./dir-writer.js */ \"(ssr)/./node_modules/fstream/lib/dir-writer.js\")\nvar LinkWriter = __webpack_require__(/*! ./link-writer.js */ \"(ssr)/./node_modules/fstream/lib/link-writer.js\")\nvar FileWriter = __webpack_require__(/*! ./file-writer.js */ \"(ssr)/./node_modules/fstream/lib/file-writer.js\")\nvar ProxyWriter = __webpack_require__(/*! ./proxy-writer.js */ \"(ssr)/./node_modules/fstream/lib/proxy-writer.js\")\n\n// props is the desired state.  current is optionally the current stat,\n// provided here so that subclasses can avoid statting the target\n// more than necessary.\nfunction Writer (props, current) {\n  var self = this\n\n  if (typeof props === 'string') {\n    props = { path: props }\n  }\n\n  // polymorphism.\n  // call fstream.Writer(dir) to get a DirWriter object, etc.\n  var type = getType(props)\n  var ClassType = Writer\n\n  switch (type) {\n    case 'Directory':\n      ClassType = DirWriter\n      break\n    case 'File':\n      ClassType = FileWriter\n      break\n    case 'Link':\n    case 'SymbolicLink':\n      ClassType = LinkWriter\n      break\n    case null:\n    default:\n      // Don't know yet what type to create, so we wrap in a proxy.\n      ClassType = ProxyWriter\n      break\n  }\n\n  if (!(self instanceof ClassType)) return new ClassType(props)\n\n  // now get down to business.\n\n  Abstract.call(self)\n\n  if (!props.path) self.error('Must provide a path', null, true)\n\n  // props is what we want to set.\n  // set some convenience properties as well.\n  self.type = props.type\n  self.props = props\n  self.depth = props.depth || 0\n  self.clobber = props.clobber === false ? props.clobber : true\n  self.parent = props.parent || null\n  self.root = props.root || (props.parent && props.parent.root) || self\n\n  self._path = self.path = path.resolve(props.path)\n  if (process.platform === 'win32') {\n    self.path = self._path = self.path.replace(/\\?/g, '_')\n    if (self._path.length >= 260) {\n      self._swallowErrors = true\n      self._path = '\\\\\\\\?\\\\' + self.path.replace(/\\//g, '\\\\')\n    }\n  }\n  self.basename = path.basename(props.path)\n  self.dirname = path.dirname(props.path)\n  self.linkpath = props.linkpath || null\n\n  props.parent = props.root = null\n\n  // console.error(\"\\n\\n\\n%s setting size to\", props.path, props.size)\n  self.size = props.size\n\n  if (typeof props.mode === 'string') {\n    props.mode = parseInt(props.mode, 8)\n  }\n\n  self.readable = false\n  self.writable = true\n\n  // buffer until ready, or while handling another entry\n  self._buffer = []\n  self.ready = false\n\n  self.filter = typeof props.filter === 'function' ? props.filter : null\n\n  // start the ball rolling.\n  // this checks what's there already, and then calls\n  // self._create() to call the impl-specific creation stuff.\n  self._stat(current)\n}\n\n// Calling this means that it's something we can't create.\n// Just assert that it's already there, otherwise raise a warning.\nWriter.prototype._create = function () {\n  var self = this\n  fs[self.props.follow ? 'stat' : 'lstat'](self._path, function (er) {\n    if (er) {\n      return self.warn('Cannot create ' + self._path + '\\n' +\n        'Unsupported type: ' + self.type, 'ENOTSUP')\n    }\n    self._finish()\n  })\n}\n\nWriter.prototype._stat = function (current) {\n  var self = this\n  var props = self.props\n  var stat = props.follow ? 'stat' : 'lstat'\n  var who = self._proxy || self\n\n  if (current) statCb(null, current)\n  else fs[stat](self._path, statCb)\n\n  function statCb (er, current) {\n    if (self.filter && !self.filter.call(who, who, current)) {\n      self._aborted = true\n      self.emit('end')\n      self.emit('close')\n      return\n    }\n\n    // if it's not there, great.  We'll just create it.\n    // if it is there, then we'll need to change whatever differs\n    if (er || !current) {\n      return create(self)\n    }\n\n    self._old = current\n    var currentType = getType(current)\n\n    // if it's a type change, then we need to clobber or error.\n    // if it's not a type change, then let the impl take care of it.\n    if (currentType !== self.type || self.type === 'File' && current.nlink > 1) {\n      return rimraf(self._path, function (er) {\n        if (er) return self.error(er)\n        self._old = null\n        create(self)\n      })\n    }\n\n    // otherwise, just handle in the app-specific way\n    // this creates a fs.WriteStream, or mkdir's, or whatever\n    create(self)\n  }\n}\n\nfunction create (self) {\n  // console.error(\"W create\", self._path, Writer.dirmode)\n\n  // XXX Need to clobber non-dirs that are in the way,\n  // unless { clobber: false } in the props.\n  mkdir(path.dirname(self._path), Writer.dirmode, function (er, made) {\n    // console.error(\"W created\", path.dirname(self._path), er)\n    if (er) return self.error(er)\n\n    // later on, we have to set the mode and owner for these\n    self._madeDir = made\n    return self._create()\n  })\n}\n\nfunction endChmod (self, want, current, path, cb) {\n  var wantMode = want.mode\n  var chmod = want.follow || self.type !== 'SymbolicLink'\n    ? 'chmod' : 'lchmod'\n\n  if (!fs[chmod]) return cb()\n  if (typeof wantMode !== 'number') return cb()\n\n  var curMode = current.mode & parseInt('0777', 8)\n  wantMode = wantMode & parseInt('0777', 8)\n  if (wantMode === curMode) return cb()\n\n  fs[chmod](path, wantMode, cb)\n}\n\nfunction endChown (self, want, current, path, cb) {\n  // Don't even try it unless root.  Too easy to EPERM.\n  if (process.platform === 'win32') return cb()\n  if (!process.getuid || process.getuid() !== 0) return cb()\n  if (typeof want.uid !== 'number' &&\n    typeof want.gid !== 'number') return cb()\n\n  if (current.uid === want.uid &&\n    current.gid === want.gid) return cb()\n\n  var chown = (self.props.follow || self.type !== 'SymbolicLink')\n    ? 'chown' : 'lchown'\n  if (!fs[chown]) return cb()\n\n  if (typeof want.uid !== 'number') want.uid = current.uid\n  if (typeof want.gid !== 'number') want.gid = current.gid\n\n  fs[chown](path, want.uid, want.gid, cb)\n}\n\nfunction endUtimes (self, want, current, path, cb) {\n  if (!fs.utimes || process.platform === 'win32') return cb()\n\n  var utimes = (want.follow || self.type !== 'SymbolicLink')\n    ? 'utimes' : 'lutimes'\n\n  if (utimes === 'lutimes' && !fs[utimes]) {\n    utimes = 'utimes'\n  }\n\n  if (!fs[utimes]) return cb()\n\n  var curA = current.atime\n  var curM = current.mtime\n  var meA = want.atime\n  var meM = want.mtime\n\n  if (meA === undefined) meA = curA\n  if (meM === undefined) meM = curM\n\n  if (!isDate(meA)) meA = new Date(meA)\n  if (!isDate(meM)) meA = new Date(meM)\n\n  if (meA.getTime() === curA.getTime() &&\n    meM.getTime() === curM.getTime()) return cb()\n\n  fs[utimes](path, meA, meM, cb)\n}\n\n// XXX This function is beastly.  Break it up!\nWriter.prototype._finish = function () {\n  var self = this\n\n  if (self._finishing) return\n  self._finishing = true\n\n  // console.error(\" W Finish\", self._path, self.size)\n\n  // set up all the things.\n  // At this point, we're already done writing whatever we've gotta write,\n  // adding files to the dir, etc.\n  var todo = 0\n  var errState = null\n  var done = false\n\n  if (self._old) {\n    // the times will almost *certainly* have changed.\n    // adds the utimes syscall, but remove another stat.\n    self._old.atime = new Date(0)\n    self._old.mtime = new Date(0)\n    // console.error(\" W Finish Stale Stat\", self._path, self.size)\n    setProps(self._old)\n  } else {\n    var stat = self.props.follow ? 'stat' : 'lstat'\n    // console.error(\" W Finish Stating\", self._path, self.size)\n    fs[stat](self._path, function (er, current) {\n      // console.error(\" W Finish Stated\", self._path, self.size, current)\n      if (er) {\n        // if we're in the process of writing out a\n        // directory, it's very possible that the thing we're linking to\n        // doesn't exist yet (especially if it was intended as a symlink),\n        // so swallow ENOENT errors here and just soldier on.\n        if (er.code === 'ENOENT' &&\n          (self.type === 'Link' || self.type === 'SymbolicLink') &&\n          process.platform === 'win32') {\n          self.ready = true\n          self.emit('ready')\n          self.emit('end')\n          self.emit('close')\n          self.end = self._finish = function () {}\n          return\n        } else return self.error(er)\n      }\n      setProps(self._old = current)\n    })\n  }\n\n  return\n\n  function setProps (current) {\n    todo += 3\n    endChmod(self, self.props, current, self._path, next('chmod'))\n    endChown(self, self.props, current, self._path, next('chown'))\n    endUtimes(self, self.props, current, self._path, next('utimes'))\n  }\n\n  function next (what) {\n    return function (er) {\n      // console.error(\"   W Finish\", what, todo)\n      if (errState) return\n      if (er) {\n        er.fstream_finish_call = what\n        return self.error(errState = er)\n      }\n      if (--todo > 0) return\n      if (done) return\n      done = true\n\n      // we may still need to set the mode/etc. on some parent dirs\n      // that were created previously.  delay end/close until then.\n      if (!self._madeDir) return end()\n      else endMadeDir(self, self._path, end)\n\n      function end (er) {\n        if (er) {\n          er.fstream_finish_call = 'setupMadeDir'\n          return self.error(er)\n        }\n        // all the props have been set, so we're completely done.\n        self.emit('end')\n        self.emit('close')\n      }\n    }\n  }\n}\n\nfunction endMadeDir (self, p, cb) {\n  var made = self._madeDir\n  // everything *between* made and path.dirname(self._path)\n  // needs to be set up.  Note that this may just be one dir.\n  var d = path.dirname(p)\n\n  endMadeDir_(self, d, function (er) {\n    if (er) return cb(er)\n    if (d === made) {\n      return cb()\n    }\n    endMadeDir(self, d, cb)\n  })\n}\n\nfunction endMadeDir_ (self, p, cb) {\n  var dirProps = {}\n  Object.keys(self.props).forEach(function (k) {\n    dirProps[k] = self.props[k]\n\n    // only make non-readable dirs if explicitly requested.\n    if (k === 'mode' && self.type !== 'Directory') {\n      dirProps[k] = dirProps[k] | parseInt('0111', 8)\n    }\n  })\n\n  var todo = 3\n  var errState = null\n  fs.stat(p, function (er, current) {\n    if (er) return cb(errState = er)\n    endChmod(self, dirProps, current, p, next)\n    endChown(self, dirProps, current, p, next)\n    endUtimes(self, dirProps, current, p, next)\n  })\n\n  function next (er) {\n    if (errState) return\n    if (er) return cb(errState = er)\n    if (--todo === 0) return cb()\n  }\n}\n\nWriter.prototype.pipe = function () {\n  this.error(\"Can't pipe from writable stream\")\n}\n\nWriter.prototype.add = function () {\n  this.error(\"Can't add to non-Directory type\")\n}\n\nWriter.prototype.write = function () {\n  return true\n}\n\nfunction objectToString (d) {\n  return Object.prototype.toString.call(d)\n}\n\nfunction isDate (d) {\n  return typeof d === 'object' && objectToString(d) === '[object Date]'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/node_modules/glob/common.js":
/*!**********************************************************!*\
  !*** ./node_modules/fstream/node_modules/glob/common.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.setopts = setopts\nexports.ownProp = ownProp\nexports.makeAbs = makeAbs\nexports.finish = finish\nexports.mark = mark\nexports.isIgnored = isIgnored\nexports.childrenIgnored = childrenIgnored\n\nfunction ownProp (obj, field) {\n  return Object.prototype.hasOwnProperty.call(obj, field)\n}\n\nvar fs = __webpack_require__(/*! fs */ \"fs\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar minimatch = __webpack_require__(/*! minimatch */ \"(ssr)/./node_modules/minimatch/minimatch.js\")\nvar isAbsolute = __webpack_require__(/*! path-is-absolute */ \"(ssr)/./node_modules/path-is-absolute/index.js\")\nvar Minimatch = minimatch.Minimatch\n\nfunction alphasort (a, b) {\n  return a.localeCompare(b, 'en')\n}\n\nfunction setupIgnores (self, options) {\n  self.ignore = options.ignore || []\n\n  if (!Array.isArray(self.ignore))\n    self.ignore = [self.ignore]\n\n  if (self.ignore.length) {\n    self.ignore = self.ignore.map(ignoreMap)\n  }\n}\n\n// ignore patterns are always in dot:true mode.\nfunction ignoreMap (pattern) {\n  var gmatcher = null\n  if (pattern.slice(-3) === '/**') {\n    var gpattern = pattern.replace(/(\\/\\*\\*)+$/, '')\n    gmatcher = new Minimatch(gpattern, { dot: true })\n  }\n\n  return {\n    matcher: new Minimatch(pattern, { dot: true }),\n    gmatcher: gmatcher\n  }\n}\n\nfunction setopts (self, pattern, options) {\n  if (!options)\n    options = {}\n\n  // base-matching: just use globstar for that.\n  if (options.matchBase && -1 === pattern.indexOf(\"/\")) {\n    if (options.noglobstar) {\n      throw new Error(\"base matching requires globstar\")\n    }\n    pattern = \"**/\" + pattern\n  }\n\n  self.silent = !!options.silent\n  self.pattern = pattern\n  self.strict = options.strict !== false\n  self.realpath = !!options.realpath\n  self.realpathCache = options.realpathCache || Object.create(null)\n  self.follow = !!options.follow\n  self.dot = !!options.dot\n  self.mark = !!options.mark\n  self.nodir = !!options.nodir\n  if (self.nodir)\n    self.mark = true\n  self.sync = !!options.sync\n  self.nounique = !!options.nounique\n  self.nonull = !!options.nonull\n  self.nosort = !!options.nosort\n  self.nocase = !!options.nocase\n  self.stat = !!options.stat\n  self.noprocess = !!options.noprocess\n  self.absolute = !!options.absolute\n  self.fs = options.fs || fs\n\n  self.maxLength = options.maxLength || Infinity\n  self.cache = options.cache || Object.create(null)\n  self.statCache = options.statCache || Object.create(null)\n  self.symlinks = options.symlinks || Object.create(null)\n\n  setupIgnores(self, options)\n\n  self.changedCwd = false\n  var cwd = process.cwd()\n  if (!ownProp(options, \"cwd\"))\n    self.cwd = cwd\n  else {\n    self.cwd = path.resolve(options.cwd)\n    self.changedCwd = self.cwd !== cwd\n  }\n\n  self.root = options.root || path.resolve(self.cwd, \"/\")\n  self.root = path.resolve(self.root)\n  if (process.platform === \"win32\")\n    self.root = self.root.replace(/\\\\/g, \"/\")\n\n  // TODO: is an absolute `cwd` supposed to be resolved against `root`?\n  // e.g. { cwd: '/test', root: __dirname } === path.join(__dirname, '/test')\n  self.cwdAbs = isAbsolute(self.cwd) ? self.cwd : makeAbs(self, self.cwd)\n  if (process.platform === \"win32\")\n    self.cwdAbs = self.cwdAbs.replace(/\\\\/g, \"/\")\n  self.nomount = !!options.nomount\n\n  // disable comments and negation in Minimatch.\n  // Note that they are not supported in Glob itself anyway.\n  options.nonegate = true\n  options.nocomment = true\n  // always treat \\ in patterns as escapes, not path separators\n  options.allowWindowsEscape = false\n\n  self.minimatch = new Minimatch(pattern, options)\n  self.options = self.minimatch.options\n}\n\nfunction finish (self) {\n  var nou = self.nounique\n  var all = nou ? [] : Object.create(null)\n\n  for (var i = 0, l = self.matches.length; i < l; i ++) {\n    var matches = self.matches[i]\n    if (!matches || Object.keys(matches).length === 0) {\n      if (self.nonull) {\n        // do like the shell, and spit out the literal glob\n        var literal = self.minimatch.globSet[i]\n        if (nou)\n          all.push(literal)\n        else\n          all[literal] = true\n      }\n    } else {\n      // had matches\n      var m = Object.keys(matches)\n      if (nou)\n        all.push.apply(all, m)\n      else\n        m.forEach(function (m) {\n          all[m] = true\n        })\n    }\n  }\n\n  if (!nou)\n    all = Object.keys(all)\n\n  if (!self.nosort)\n    all = all.sort(alphasort)\n\n  // at *some* point we statted all of these\n  if (self.mark) {\n    for (var i = 0; i < all.length; i++) {\n      all[i] = self._mark(all[i])\n    }\n    if (self.nodir) {\n      all = all.filter(function (e) {\n        var notDir = !(/\\/$/.test(e))\n        var c = self.cache[e] || self.cache[makeAbs(self, e)]\n        if (notDir && c)\n          notDir = c !== 'DIR' && !Array.isArray(c)\n        return notDir\n      })\n    }\n  }\n\n  if (self.ignore.length)\n    all = all.filter(function(m) {\n      return !isIgnored(self, m)\n    })\n\n  self.found = all\n}\n\nfunction mark (self, p) {\n  var abs = makeAbs(self, p)\n  var c = self.cache[abs]\n  var m = p\n  if (c) {\n    var isDir = c === 'DIR' || Array.isArray(c)\n    var slash = p.slice(-1) === '/'\n\n    if (isDir && !slash)\n      m += '/'\n    else if (!isDir && slash)\n      m = m.slice(0, -1)\n\n    if (m !== p) {\n      var mabs = makeAbs(self, m)\n      self.statCache[mabs] = self.statCache[abs]\n      self.cache[mabs] = self.cache[abs]\n    }\n  }\n\n  return m\n}\n\n// lotta situps...\nfunction makeAbs (self, f) {\n  var abs = f\n  if (f.charAt(0) === '/') {\n    abs = path.join(self.root, f)\n  } else if (isAbsolute(f) || f === '') {\n    abs = f\n  } else if (self.changedCwd) {\n    abs = path.resolve(self.cwd, f)\n  } else {\n    abs = path.resolve(f)\n  }\n\n  if (process.platform === 'win32')\n    abs = abs.replace(/\\\\/g, '/')\n\n  return abs\n}\n\n\n// Return true, if pattern ends with globstar '**', for the accompanying parent directory.\n// Ex:- If node_modules/** is the pattern, add 'node_modules' to ignore list along with it's contents\nfunction isIgnored (self, path) {\n  if (!self.ignore.length)\n    return false\n\n  return self.ignore.some(function(item) {\n    return item.matcher.match(path) || !!(item.gmatcher && item.gmatcher.match(path))\n  })\n}\n\nfunction childrenIgnored (self, path) {\n  if (!self.ignore.length)\n    return false\n\n  return self.ignore.some(function(item) {\n    return !!(item.gmatcher && item.gmatcher.match(path))\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/node_modules/glob/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/node_modules/glob/glob.js":
/*!********************************************************!*\
  !*** ./node_modules/fstream/node_modules/glob/glob.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Approach:\n//\n// 1. Get the minimatch set\n// 2. For each pattern in the set, PROCESS(pattern, false)\n// 3. Store matches per-set, then uniq them\n//\n// PROCESS(pattern, inGlobStar)\n// Get the first [n] items from pattern that are all strings\n// Join these together.  This is PREFIX.\n//   If there is no more remaining, then stat(PREFIX) and\n//   add to matches if it succeeds.  END.\n//\n// If inGlobStar and PREFIX is symlink and points to dir\n//   set ENTRIES = []\n// else readdir(PREFIX) as ENTRIES\n//   If fail, END\n//\n// with ENTRIES\n//   If pattern[n] is GLOBSTAR\n//     // handle the case where the globstar match is empty\n//     // by pruning it out, and testing the resulting pattern\n//     PROCESS(pattern[0..n] + pattern[n+1 .. $], false)\n//     // handle other cases.\n//     for ENTRY in ENTRIES (not dotfiles)\n//       // attach globstar + tail onto the entry\n//       // Mark that this entry is a globstar match\n//       PROCESS(pattern[0..n] + ENTRY + pattern[n .. $], true)\n//\n//   else // not globstar\n//     for ENTRY in ENTRIES (not dotfiles, unless pattern[n] is dot)\n//       Test ENTRY against pattern[n]\n//       If fails, continue\n//       If passes, PROCESS(pattern[0..n] + item + pattern[n+1 .. $])\n//\n// Caveat:\n//   Cache all stats and readdirs results to minimize syscall.  Since all\n//   we ever care about is existence and directory-ness, we can just keep\n//   `true` for files, and [children,...] for directories, or `false` for\n//   things that don't exist.\n\nmodule.exports = glob\n\nvar rp = __webpack_require__(/*! fs.realpath */ \"(ssr)/./node_modules/fs.realpath/index.js\")\nvar minimatch = __webpack_require__(/*! minimatch */ \"(ssr)/./node_modules/minimatch/minimatch.js\")\nvar Minimatch = minimatch.Minimatch\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar EE = (__webpack_require__(/*! events */ \"events\").EventEmitter)\nvar path = __webpack_require__(/*! path */ \"path\")\nvar assert = __webpack_require__(/*! assert */ \"assert\")\nvar isAbsolute = __webpack_require__(/*! path-is-absolute */ \"(ssr)/./node_modules/path-is-absolute/index.js\")\nvar globSync = __webpack_require__(/*! ./sync.js */ \"(ssr)/./node_modules/fstream/node_modules/glob/sync.js\")\nvar common = __webpack_require__(/*! ./common.js */ \"(ssr)/./node_modules/fstream/node_modules/glob/common.js\")\nvar setopts = common.setopts\nvar ownProp = common.ownProp\nvar inflight = __webpack_require__(/*! inflight */ \"(ssr)/./node_modules/inflight/inflight.js\")\nvar util = __webpack_require__(/*! util */ \"util\")\nvar childrenIgnored = common.childrenIgnored\nvar isIgnored = common.isIgnored\n\nvar once = __webpack_require__(/*! once */ \"(ssr)/./node_modules/once/once.js\")\n\nfunction glob (pattern, options, cb) {\n  if (typeof options === 'function') cb = options, options = {}\n  if (!options) options = {}\n\n  if (options.sync) {\n    if (cb)\n      throw new TypeError('callback provided to sync glob')\n    return globSync(pattern, options)\n  }\n\n  return new Glob(pattern, options, cb)\n}\n\nglob.sync = globSync\nvar GlobSync = glob.GlobSync = globSync.GlobSync\n\n// old api surface\nglob.glob = glob\n\nfunction extend (origin, add) {\n  if (add === null || typeof add !== 'object') {\n    return origin\n  }\n\n  var keys = Object.keys(add)\n  var i = keys.length\n  while (i--) {\n    origin[keys[i]] = add[keys[i]]\n  }\n  return origin\n}\n\nglob.hasMagic = function (pattern, options_) {\n  var options = extend({}, options_)\n  options.noprocess = true\n\n  var g = new Glob(pattern, options)\n  var set = g.minimatch.set\n\n  if (!pattern)\n    return false\n\n  if (set.length > 1)\n    return true\n\n  for (var j = 0; j < set[0].length; j++) {\n    if (typeof set[0][j] !== 'string')\n      return true\n  }\n\n  return false\n}\n\nglob.Glob = Glob\ninherits(Glob, EE)\nfunction Glob (pattern, options, cb) {\n  if (typeof options === 'function') {\n    cb = options\n    options = null\n  }\n\n  if (options && options.sync) {\n    if (cb)\n      throw new TypeError('callback provided to sync glob')\n    return new GlobSync(pattern, options)\n  }\n\n  if (!(this instanceof Glob))\n    return new Glob(pattern, options, cb)\n\n  setopts(this, pattern, options)\n  this._didRealPath = false\n\n  // process each pattern in the minimatch set\n  var n = this.minimatch.set.length\n\n  // The matches are stored as {<filename>: true,...} so that\n  // duplicates are automagically pruned.\n  // Later, we do an Object.keys() on these.\n  // Keep them as a list so we can fill in when nonull is set.\n  this.matches = new Array(n)\n\n  if (typeof cb === 'function') {\n    cb = once(cb)\n    this.on('error', cb)\n    this.on('end', function (matches) {\n      cb(null, matches)\n    })\n  }\n\n  var self = this\n  this._processing = 0\n\n  this._emitQueue = []\n  this._processQueue = []\n  this.paused = false\n\n  if (this.noprocess)\n    return this\n\n  if (n === 0)\n    return done()\n\n  var sync = true\n  for (var i = 0; i < n; i ++) {\n    this._process(this.minimatch.set[i], i, false, done)\n  }\n  sync = false\n\n  function done () {\n    --self._processing\n    if (self._processing <= 0) {\n      if (sync) {\n        process.nextTick(function () {\n          self._finish()\n        })\n      } else {\n        self._finish()\n      }\n    }\n  }\n}\n\nGlob.prototype._finish = function () {\n  assert(this instanceof Glob)\n  if (this.aborted)\n    return\n\n  if (this.realpath && !this._didRealpath)\n    return this._realpath()\n\n  common.finish(this)\n  this.emit('end', this.found)\n}\n\nGlob.prototype._realpath = function () {\n  if (this._didRealpath)\n    return\n\n  this._didRealpath = true\n\n  var n = this.matches.length\n  if (n === 0)\n    return this._finish()\n\n  var self = this\n  for (var i = 0; i < this.matches.length; i++)\n    this._realpathSet(i, next)\n\n  function next () {\n    if (--n === 0)\n      self._finish()\n  }\n}\n\nGlob.prototype._realpathSet = function (index, cb) {\n  var matchset = this.matches[index]\n  if (!matchset)\n    return cb()\n\n  var found = Object.keys(matchset)\n  var self = this\n  var n = found.length\n\n  if (n === 0)\n    return cb()\n\n  var set = this.matches[index] = Object.create(null)\n  found.forEach(function (p, i) {\n    // If there's a problem with the stat, then it means that\n    // one or more of the links in the realpath couldn't be\n    // resolved.  just return the abs value in that case.\n    p = self._makeAbs(p)\n    rp.realpath(p, self.realpathCache, function (er, real) {\n      if (!er)\n        set[real] = true\n      else if (er.syscall === 'stat')\n        set[p] = true\n      else\n        self.emit('error', er) // srsly wtf right here\n\n      if (--n === 0) {\n        self.matches[index] = set\n        cb()\n      }\n    })\n  })\n}\n\nGlob.prototype._mark = function (p) {\n  return common.mark(this, p)\n}\n\nGlob.prototype._makeAbs = function (f) {\n  return common.makeAbs(this, f)\n}\n\nGlob.prototype.abort = function () {\n  this.aborted = true\n  this.emit('abort')\n}\n\nGlob.prototype.pause = function () {\n  if (!this.paused) {\n    this.paused = true\n    this.emit('pause')\n  }\n}\n\nGlob.prototype.resume = function () {\n  if (this.paused) {\n    this.emit('resume')\n    this.paused = false\n    if (this._emitQueue.length) {\n      var eq = this._emitQueue.slice(0)\n      this._emitQueue.length = 0\n      for (var i = 0; i < eq.length; i ++) {\n        var e = eq[i]\n        this._emitMatch(e[0], e[1])\n      }\n    }\n    if (this._processQueue.length) {\n      var pq = this._processQueue.slice(0)\n      this._processQueue.length = 0\n      for (var i = 0; i < pq.length; i ++) {\n        var p = pq[i]\n        this._processing--\n        this._process(p[0], p[1], p[2], p[3])\n      }\n    }\n  }\n}\n\nGlob.prototype._process = function (pattern, index, inGlobStar, cb) {\n  assert(this instanceof Glob)\n  assert(typeof cb === 'function')\n\n  if (this.aborted)\n    return\n\n  this._processing++\n  if (this.paused) {\n    this._processQueue.push([pattern, index, inGlobStar, cb])\n    return\n  }\n\n  //console.error('PROCESS %d', this._processing, pattern)\n\n  // Get the first [n] parts of pattern that are all strings.\n  var n = 0\n  while (typeof pattern[n] === 'string') {\n    n ++\n  }\n  // now n is the index of the first one that is *not* a string.\n\n  // see if there's anything else\n  var prefix\n  switch (n) {\n    // if not, then this is rather simple\n    case pattern.length:\n      this._processSimple(pattern.join('/'), index, cb)\n      return\n\n    case 0:\n      // pattern *starts* with some non-trivial item.\n      // going to readdir(cwd), but not include the prefix in matches.\n      prefix = null\n      break\n\n    default:\n      // pattern has some string bits in the front.\n      // whatever it starts with, whether that's 'absolute' like /foo/bar,\n      // or 'relative' like '../baz'\n      prefix = pattern.slice(0, n).join('/')\n      break\n  }\n\n  var remain = pattern.slice(n)\n\n  // get the list of entries.\n  var read\n  if (prefix === null)\n    read = '.'\n  else if (isAbsolute(prefix) ||\n      isAbsolute(pattern.map(function (p) {\n        return typeof p === 'string' ? p : '[*]'\n      }).join('/'))) {\n    if (!prefix || !isAbsolute(prefix))\n      prefix = '/' + prefix\n    read = prefix\n  } else\n    read = prefix\n\n  var abs = this._makeAbs(read)\n\n  //if ignored, skip _processing\n  if (childrenIgnored(this, read))\n    return cb()\n\n  var isGlobStar = remain[0] === minimatch.GLOBSTAR\n  if (isGlobStar)\n    this._processGlobStar(prefix, read, abs, remain, index, inGlobStar, cb)\n  else\n    this._processReaddir(prefix, read, abs, remain, index, inGlobStar, cb)\n}\n\nGlob.prototype._processReaddir = function (prefix, read, abs, remain, index, inGlobStar, cb) {\n  var self = this\n  this._readdir(abs, inGlobStar, function (er, entries) {\n    return self._processReaddir2(prefix, read, abs, remain, index, inGlobStar, entries, cb)\n  })\n}\n\nGlob.prototype._processReaddir2 = function (prefix, read, abs, remain, index, inGlobStar, entries, cb) {\n\n  // if the abs isn't a dir, then nothing can match!\n  if (!entries)\n    return cb()\n\n  // It will only match dot entries if it starts with a dot, or if\n  // dot is set.  Stuff like @(.foo|.bar) isn't allowed.\n  var pn = remain[0]\n  var negate = !!this.minimatch.negate\n  var rawGlob = pn._glob\n  var dotOk = this.dot || rawGlob.charAt(0) === '.'\n\n  var matchedEntries = []\n  for (var i = 0; i < entries.length; i++) {\n    var e = entries[i]\n    if (e.charAt(0) !== '.' || dotOk) {\n      var m\n      if (negate && !prefix) {\n        m = !e.match(pn)\n      } else {\n        m = e.match(pn)\n      }\n      if (m)\n        matchedEntries.push(e)\n    }\n  }\n\n  //console.error('prd2', prefix, entries, remain[0]._glob, matchedEntries)\n\n  var len = matchedEntries.length\n  // If there are no matched entries, then nothing matches.\n  if (len === 0)\n    return cb()\n\n  // if this is the last remaining pattern bit, then no need for\n  // an additional stat *unless* the user has specified mark or\n  // stat explicitly.  We know they exist, since readdir returned\n  // them.\n\n  if (remain.length === 1 && !this.mark && !this.stat) {\n    if (!this.matches[index])\n      this.matches[index] = Object.create(null)\n\n    for (var i = 0; i < len; i ++) {\n      var e = matchedEntries[i]\n      if (prefix) {\n        if (prefix !== '/')\n          e = prefix + '/' + e\n        else\n          e = prefix + e\n      }\n\n      if (e.charAt(0) === '/' && !this.nomount) {\n        e = path.join(this.root, e)\n      }\n      this._emitMatch(index, e)\n    }\n    // This was the last one, and no stats were needed\n    return cb()\n  }\n\n  // now test all matched entries as stand-ins for that part\n  // of the pattern.\n  remain.shift()\n  for (var i = 0; i < len; i ++) {\n    var e = matchedEntries[i]\n    var newPattern\n    if (prefix) {\n      if (prefix !== '/')\n        e = prefix + '/' + e\n      else\n        e = prefix + e\n    }\n    this._process([e].concat(remain), index, inGlobStar, cb)\n  }\n  cb()\n}\n\nGlob.prototype._emitMatch = function (index, e) {\n  if (this.aborted)\n    return\n\n  if (isIgnored(this, e))\n    return\n\n  if (this.paused) {\n    this._emitQueue.push([index, e])\n    return\n  }\n\n  var abs = isAbsolute(e) ? e : this._makeAbs(e)\n\n  if (this.mark)\n    e = this._mark(e)\n\n  if (this.absolute)\n    e = abs\n\n  if (this.matches[index][e])\n    return\n\n  if (this.nodir) {\n    var c = this.cache[abs]\n    if (c === 'DIR' || Array.isArray(c))\n      return\n  }\n\n  this.matches[index][e] = true\n\n  var st = this.statCache[abs]\n  if (st)\n    this.emit('stat', e, st)\n\n  this.emit('match', e)\n}\n\nGlob.prototype._readdirInGlobStar = function (abs, cb) {\n  if (this.aborted)\n    return\n\n  // follow all symlinked directories forever\n  // just proceed as if this is a non-globstar situation\n  if (this.follow)\n    return this._readdir(abs, false, cb)\n\n  var lstatkey = 'lstat\\0' + abs\n  var self = this\n  var lstatcb = inflight(lstatkey, lstatcb_)\n\n  if (lstatcb)\n    self.fs.lstat(abs, lstatcb)\n\n  function lstatcb_ (er, lstat) {\n    if (er && er.code === 'ENOENT')\n      return cb()\n\n    var isSym = lstat && lstat.isSymbolicLink()\n    self.symlinks[abs] = isSym\n\n    // If it's not a symlink or a dir, then it's definitely a regular file.\n    // don't bother doing a readdir in that case.\n    if (!isSym && lstat && !lstat.isDirectory()) {\n      self.cache[abs] = 'FILE'\n      cb()\n    } else\n      self._readdir(abs, false, cb)\n  }\n}\n\nGlob.prototype._readdir = function (abs, inGlobStar, cb) {\n  if (this.aborted)\n    return\n\n  cb = inflight('readdir\\0'+abs+'\\0'+inGlobStar, cb)\n  if (!cb)\n    return\n\n  //console.error('RD %j %j', +inGlobStar, abs)\n  if (inGlobStar && !ownProp(this.symlinks, abs))\n    return this._readdirInGlobStar(abs, cb)\n\n  if (ownProp(this.cache, abs)) {\n    var c = this.cache[abs]\n    if (!c || c === 'FILE')\n      return cb()\n\n    if (Array.isArray(c))\n      return cb(null, c)\n  }\n\n  var self = this\n  self.fs.readdir(abs, readdirCb(this, abs, cb))\n}\n\nfunction readdirCb (self, abs, cb) {\n  return function (er, entries) {\n    if (er)\n      self._readdirError(abs, er, cb)\n    else\n      self._readdirEntries(abs, entries, cb)\n  }\n}\n\nGlob.prototype._readdirEntries = function (abs, entries, cb) {\n  if (this.aborted)\n    return\n\n  // if we haven't asked to stat everything, then just\n  // assume that everything in there exists, so we can avoid\n  // having to stat it a second time.\n  if (!this.mark && !this.stat) {\n    for (var i = 0; i < entries.length; i ++) {\n      var e = entries[i]\n      if (abs === '/')\n        e = abs + e\n      else\n        e = abs + '/' + e\n      this.cache[e] = true\n    }\n  }\n\n  this.cache[abs] = entries\n  return cb(null, entries)\n}\n\nGlob.prototype._readdirError = function (f, er, cb) {\n  if (this.aborted)\n    return\n\n  // handle errors, and cache the information\n  switch (er.code) {\n    case 'ENOTSUP': // https://github.com/isaacs/node-glob/issues/205\n    case 'ENOTDIR': // totally normal. means it *does* exist.\n      var abs = this._makeAbs(f)\n      this.cache[abs] = 'FILE'\n      if (abs === this.cwdAbs) {\n        var error = new Error(er.code + ' invalid cwd ' + this.cwd)\n        error.path = this.cwd\n        error.code = er.code\n        this.emit('error', error)\n        this.abort()\n      }\n      break\n\n    case 'ENOENT': // not terribly unusual\n    case 'ELOOP':\n    case 'ENAMETOOLONG':\n    case 'UNKNOWN':\n      this.cache[this._makeAbs(f)] = false\n      break\n\n    default: // some unusual error.  Treat as failure.\n      this.cache[this._makeAbs(f)] = false\n      if (this.strict) {\n        this.emit('error', er)\n        // If the error is handled, then we abort\n        // if not, we threw out of here\n        this.abort()\n      }\n      if (!this.silent)\n        console.error('glob error', er)\n      break\n  }\n\n  return cb()\n}\n\nGlob.prototype._processGlobStar = function (prefix, read, abs, remain, index, inGlobStar, cb) {\n  var self = this\n  this._readdir(abs, inGlobStar, function (er, entries) {\n    self._processGlobStar2(prefix, read, abs, remain, index, inGlobStar, entries, cb)\n  })\n}\n\n\nGlob.prototype._processGlobStar2 = function (prefix, read, abs, remain, index, inGlobStar, entries, cb) {\n  //console.error('pgs2', prefix, remain[0], entries)\n\n  // no entries means not a dir, so it can never have matches\n  // foo.txt/** doesn't match foo.txt\n  if (!entries)\n    return cb()\n\n  // test without the globstar, and with every child both below\n  // and replacing the globstar.\n  var remainWithoutGlobStar = remain.slice(1)\n  var gspref = prefix ? [ prefix ] : []\n  var noGlobStar = gspref.concat(remainWithoutGlobStar)\n\n  // the noGlobStar pattern exits the inGlobStar state\n  this._process(noGlobStar, index, false, cb)\n\n  var isSym = this.symlinks[abs]\n  var len = entries.length\n\n  // If it's a symlink, and we're in a globstar, then stop\n  if (isSym && inGlobStar)\n    return cb()\n\n  for (var i = 0; i < len; i++) {\n    var e = entries[i]\n    if (e.charAt(0) === '.' && !this.dot)\n      continue\n\n    // these two cases enter the inGlobStar state\n    var instead = gspref.concat(entries[i], remainWithoutGlobStar)\n    this._process(instead, index, true, cb)\n\n    var below = gspref.concat(entries[i], remain)\n    this._process(below, index, true, cb)\n  }\n\n  cb()\n}\n\nGlob.prototype._processSimple = function (prefix, index, cb) {\n  // XXX review this.  Shouldn't it be doing the mounting etc\n  // before doing stat?  kinda weird?\n  var self = this\n  this._stat(prefix, function (er, exists) {\n    self._processSimple2(prefix, index, er, exists, cb)\n  })\n}\nGlob.prototype._processSimple2 = function (prefix, index, er, exists, cb) {\n\n  //console.error('ps2', prefix, exists)\n\n  if (!this.matches[index])\n    this.matches[index] = Object.create(null)\n\n  // If it doesn't exist, then just mark the lack of results\n  if (!exists)\n    return cb()\n\n  if (prefix && isAbsolute(prefix) && !this.nomount) {\n    var trail = /[\\/\\\\]$/.test(prefix)\n    if (prefix.charAt(0) === '/') {\n      prefix = path.join(this.root, prefix)\n    } else {\n      prefix = path.resolve(this.root, prefix)\n      if (trail)\n        prefix += '/'\n    }\n  }\n\n  if (process.platform === 'win32')\n    prefix = prefix.replace(/\\\\/g, '/')\n\n  // Mark this as a match\n  this._emitMatch(index, prefix)\n  cb()\n}\n\n// Returns either 'DIR', 'FILE', or false\nGlob.prototype._stat = function (f, cb) {\n  var abs = this._makeAbs(f)\n  var needDir = f.slice(-1) === '/'\n\n  if (f.length > this.maxLength)\n    return cb()\n\n  if (!this.stat && ownProp(this.cache, abs)) {\n    var c = this.cache[abs]\n\n    if (Array.isArray(c))\n      c = 'DIR'\n\n    // It exists, but maybe not how we need it\n    if (!needDir || c === 'DIR')\n      return cb(null, c)\n\n    if (needDir && c === 'FILE')\n      return cb()\n\n    // otherwise we have to stat, because maybe c=true\n    // if we know it exists, but not what it is.\n  }\n\n  var exists\n  var stat = this.statCache[abs]\n  if (stat !== undefined) {\n    if (stat === false)\n      return cb(null, stat)\n    else {\n      var type = stat.isDirectory() ? 'DIR' : 'FILE'\n      if (needDir && type === 'FILE')\n        return cb()\n      else\n        return cb(null, type, stat)\n    }\n  }\n\n  var self = this\n  var statcb = inflight('stat\\0' + abs, lstatcb_)\n  if (statcb)\n    self.fs.lstat(abs, statcb)\n\n  function lstatcb_ (er, lstat) {\n    if (lstat && lstat.isSymbolicLink()) {\n      // If it's a symlink, then treat it as the target, unless\n      // the target does not exist, then treat it as a file.\n      return self.fs.stat(abs, function (er, stat) {\n        if (er)\n          self._stat2(f, abs, null, lstat, cb)\n        else\n          self._stat2(f, abs, er, stat, cb)\n      })\n    } else {\n      self._stat2(f, abs, er, lstat, cb)\n    }\n  }\n}\n\nGlob.prototype._stat2 = function (f, abs, er, stat, cb) {\n  if (er && (er.code === 'ENOENT' || er.code === 'ENOTDIR')) {\n    this.statCache[abs] = false\n    return cb()\n  }\n\n  var needDir = f.slice(-1) === '/'\n  this.statCache[abs] = stat\n\n  if (abs.slice(-1) === '/' && stat && !stat.isDirectory())\n    return cb(null, false, stat)\n\n  var c = true\n  if (stat)\n    c = stat.isDirectory() ? 'DIR' : 'FILE'\n  this.cache[abs] = this.cache[abs] || c\n\n  if (needDir && c === 'FILE')\n    return cb()\n\n  return cb(null, c, stat)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/node_modules/glob/glob.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/node_modules/glob/sync.js":
/*!********************************************************!*\
  !*** ./node_modules/fstream/node_modules/glob/sync.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = globSync\nglobSync.GlobSync = GlobSync\n\nvar rp = __webpack_require__(/*! fs.realpath */ \"(ssr)/./node_modules/fs.realpath/index.js\")\nvar minimatch = __webpack_require__(/*! minimatch */ \"(ssr)/./node_modules/minimatch/minimatch.js\")\nvar Minimatch = minimatch.Minimatch\nvar Glob = (__webpack_require__(/*! ./glob.js */ \"(ssr)/./node_modules/fstream/node_modules/glob/glob.js\").Glob)\nvar util = __webpack_require__(/*! util */ \"util\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar assert = __webpack_require__(/*! assert */ \"assert\")\nvar isAbsolute = __webpack_require__(/*! path-is-absolute */ \"(ssr)/./node_modules/path-is-absolute/index.js\")\nvar common = __webpack_require__(/*! ./common.js */ \"(ssr)/./node_modules/fstream/node_modules/glob/common.js\")\nvar setopts = common.setopts\nvar ownProp = common.ownProp\nvar childrenIgnored = common.childrenIgnored\nvar isIgnored = common.isIgnored\n\nfunction globSync (pattern, options) {\n  if (typeof options === 'function' || arguments.length === 3)\n    throw new TypeError('callback provided to sync glob\\n'+\n                        'See: https://github.com/isaacs/node-glob/issues/167')\n\n  return new GlobSync(pattern, options).found\n}\n\nfunction GlobSync (pattern, options) {\n  if (!pattern)\n    throw new Error('must provide pattern')\n\n  if (typeof options === 'function' || arguments.length === 3)\n    throw new TypeError('callback provided to sync glob\\n'+\n                        'See: https://github.com/isaacs/node-glob/issues/167')\n\n  if (!(this instanceof GlobSync))\n    return new GlobSync(pattern, options)\n\n  setopts(this, pattern, options)\n\n  if (this.noprocess)\n    return this\n\n  var n = this.minimatch.set.length\n  this.matches = new Array(n)\n  for (var i = 0; i < n; i ++) {\n    this._process(this.minimatch.set[i], i, false)\n  }\n  this._finish()\n}\n\nGlobSync.prototype._finish = function () {\n  assert.ok(this instanceof GlobSync)\n  if (this.realpath) {\n    var self = this\n    this.matches.forEach(function (matchset, index) {\n      var set = self.matches[index] = Object.create(null)\n      for (var p in matchset) {\n        try {\n          p = self._makeAbs(p)\n          var real = rp.realpathSync(p, self.realpathCache)\n          set[real] = true\n        } catch (er) {\n          if (er.syscall === 'stat')\n            set[self._makeAbs(p)] = true\n          else\n            throw er\n        }\n      }\n    })\n  }\n  common.finish(this)\n}\n\n\nGlobSync.prototype._process = function (pattern, index, inGlobStar) {\n  assert.ok(this instanceof GlobSync)\n\n  // Get the first [n] parts of pattern that are all strings.\n  var n = 0\n  while (typeof pattern[n] === 'string') {\n    n ++\n  }\n  // now n is the index of the first one that is *not* a string.\n\n  // See if there's anything else\n  var prefix\n  switch (n) {\n    // if not, then this is rather simple\n    case pattern.length:\n      this._processSimple(pattern.join('/'), index)\n      return\n\n    case 0:\n      // pattern *starts* with some non-trivial item.\n      // going to readdir(cwd), but not include the prefix in matches.\n      prefix = null\n      break\n\n    default:\n      // pattern has some string bits in the front.\n      // whatever it starts with, whether that's 'absolute' like /foo/bar,\n      // or 'relative' like '../baz'\n      prefix = pattern.slice(0, n).join('/')\n      break\n  }\n\n  var remain = pattern.slice(n)\n\n  // get the list of entries.\n  var read\n  if (prefix === null)\n    read = '.'\n  else if (isAbsolute(prefix) ||\n      isAbsolute(pattern.map(function (p) {\n        return typeof p === 'string' ? p : '[*]'\n      }).join('/'))) {\n    if (!prefix || !isAbsolute(prefix))\n      prefix = '/' + prefix\n    read = prefix\n  } else\n    read = prefix\n\n  var abs = this._makeAbs(read)\n\n  //if ignored, skip processing\n  if (childrenIgnored(this, read))\n    return\n\n  var isGlobStar = remain[0] === minimatch.GLOBSTAR\n  if (isGlobStar)\n    this._processGlobStar(prefix, read, abs, remain, index, inGlobStar)\n  else\n    this._processReaddir(prefix, read, abs, remain, index, inGlobStar)\n}\n\n\nGlobSync.prototype._processReaddir = function (prefix, read, abs, remain, index, inGlobStar) {\n  var entries = this._readdir(abs, inGlobStar)\n\n  // if the abs isn't a dir, then nothing can match!\n  if (!entries)\n    return\n\n  // It will only match dot entries if it starts with a dot, or if\n  // dot is set.  Stuff like @(.foo|.bar) isn't allowed.\n  var pn = remain[0]\n  var negate = !!this.minimatch.negate\n  var rawGlob = pn._glob\n  var dotOk = this.dot || rawGlob.charAt(0) === '.'\n\n  var matchedEntries = []\n  for (var i = 0; i < entries.length; i++) {\n    var e = entries[i]\n    if (e.charAt(0) !== '.' || dotOk) {\n      var m\n      if (negate && !prefix) {\n        m = !e.match(pn)\n      } else {\n        m = e.match(pn)\n      }\n      if (m)\n        matchedEntries.push(e)\n    }\n  }\n\n  var len = matchedEntries.length\n  // If there are no matched entries, then nothing matches.\n  if (len === 0)\n    return\n\n  // if this is the last remaining pattern bit, then no need for\n  // an additional stat *unless* the user has specified mark or\n  // stat explicitly.  We know they exist, since readdir returned\n  // them.\n\n  if (remain.length === 1 && !this.mark && !this.stat) {\n    if (!this.matches[index])\n      this.matches[index] = Object.create(null)\n\n    for (var i = 0; i < len; i ++) {\n      var e = matchedEntries[i]\n      if (prefix) {\n        if (prefix.slice(-1) !== '/')\n          e = prefix + '/' + e\n        else\n          e = prefix + e\n      }\n\n      if (e.charAt(0) === '/' && !this.nomount) {\n        e = path.join(this.root, e)\n      }\n      this._emitMatch(index, e)\n    }\n    // This was the last one, and no stats were needed\n    return\n  }\n\n  // now test all matched entries as stand-ins for that part\n  // of the pattern.\n  remain.shift()\n  for (var i = 0; i < len; i ++) {\n    var e = matchedEntries[i]\n    var newPattern\n    if (prefix)\n      newPattern = [prefix, e]\n    else\n      newPattern = [e]\n    this._process(newPattern.concat(remain), index, inGlobStar)\n  }\n}\n\n\nGlobSync.prototype._emitMatch = function (index, e) {\n  if (isIgnored(this, e))\n    return\n\n  var abs = this._makeAbs(e)\n\n  if (this.mark)\n    e = this._mark(e)\n\n  if (this.absolute) {\n    e = abs\n  }\n\n  if (this.matches[index][e])\n    return\n\n  if (this.nodir) {\n    var c = this.cache[abs]\n    if (c === 'DIR' || Array.isArray(c))\n      return\n  }\n\n  this.matches[index][e] = true\n\n  if (this.stat)\n    this._stat(e)\n}\n\n\nGlobSync.prototype._readdirInGlobStar = function (abs) {\n  // follow all symlinked directories forever\n  // just proceed as if this is a non-globstar situation\n  if (this.follow)\n    return this._readdir(abs, false)\n\n  var entries\n  var lstat\n  var stat\n  try {\n    lstat = this.fs.lstatSync(abs)\n  } catch (er) {\n    if (er.code === 'ENOENT') {\n      // lstat failed, doesn't exist\n      return null\n    }\n  }\n\n  var isSym = lstat && lstat.isSymbolicLink()\n  this.symlinks[abs] = isSym\n\n  // If it's not a symlink or a dir, then it's definitely a regular file.\n  // don't bother doing a readdir in that case.\n  if (!isSym && lstat && !lstat.isDirectory())\n    this.cache[abs] = 'FILE'\n  else\n    entries = this._readdir(abs, false)\n\n  return entries\n}\n\nGlobSync.prototype._readdir = function (abs, inGlobStar) {\n  var entries\n\n  if (inGlobStar && !ownProp(this.symlinks, abs))\n    return this._readdirInGlobStar(abs)\n\n  if (ownProp(this.cache, abs)) {\n    var c = this.cache[abs]\n    if (!c || c === 'FILE')\n      return null\n\n    if (Array.isArray(c))\n      return c\n  }\n\n  try {\n    return this._readdirEntries(abs, this.fs.readdirSync(abs))\n  } catch (er) {\n    this._readdirError(abs, er)\n    return null\n  }\n}\n\nGlobSync.prototype._readdirEntries = function (abs, entries) {\n  // if we haven't asked to stat everything, then just\n  // assume that everything in there exists, so we can avoid\n  // having to stat it a second time.\n  if (!this.mark && !this.stat) {\n    for (var i = 0; i < entries.length; i ++) {\n      var e = entries[i]\n      if (abs === '/')\n        e = abs + e\n      else\n        e = abs + '/' + e\n      this.cache[e] = true\n    }\n  }\n\n  this.cache[abs] = entries\n\n  // mark and cache dir-ness\n  return entries\n}\n\nGlobSync.prototype._readdirError = function (f, er) {\n  // handle errors, and cache the information\n  switch (er.code) {\n    case 'ENOTSUP': // https://github.com/isaacs/node-glob/issues/205\n    case 'ENOTDIR': // totally normal. means it *does* exist.\n      var abs = this._makeAbs(f)\n      this.cache[abs] = 'FILE'\n      if (abs === this.cwdAbs) {\n        var error = new Error(er.code + ' invalid cwd ' + this.cwd)\n        error.path = this.cwd\n        error.code = er.code\n        throw error\n      }\n      break\n\n    case 'ENOENT': // not terribly unusual\n    case 'ELOOP':\n    case 'ENAMETOOLONG':\n    case 'UNKNOWN':\n      this.cache[this._makeAbs(f)] = false\n      break\n\n    default: // some unusual error.  Treat as failure.\n      this.cache[this._makeAbs(f)] = false\n      if (this.strict)\n        throw er\n      if (!this.silent)\n        console.error('glob error', er)\n      break\n  }\n}\n\nGlobSync.prototype._processGlobStar = function (prefix, read, abs, remain, index, inGlobStar) {\n\n  var entries = this._readdir(abs, inGlobStar)\n\n  // no entries means not a dir, so it can never have matches\n  // foo.txt/** doesn't match foo.txt\n  if (!entries)\n    return\n\n  // test without the globstar, and with every child both below\n  // and replacing the globstar.\n  var remainWithoutGlobStar = remain.slice(1)\n  var gspref = prefix ? [ prefix ] : []\n  var noGlobStar = gspref.concat(remainWithoutGlobStar)\n\n  // the noGlobStar pattern exits the inGlobStar state\n  this._process(noGlobStar, index, false)\n\n  var len = entries.length\n  var isSym = this.symlinks[abs]\n\n  // If it's a symlink, and we're in a globstar, then stop\n  if (isSym && inGlobStar)\n    return\n\n  for (var i = 0; i < len; i++) {\n    var e = entries[i]\n    if (e.charAt(0) === '.' && !this.dot)\n      continue\n\n    // these two cases enter the inGlobStar state\n    var instead = gspref.concat(entries[i], remainWithoutGlobStar)\n    this._process(instead, index, true)\n\n    var below = gspref.concat(entries[i], remain)\n    this._process(below, index, true)\n  }\n}\n\nGlobSync.prototype._processSimple = function (prefix, index) {\n  // XXX review this.  Shouldn't it be doing the mounting etc\n  // before doing stat?  kinda weird?\n  var exists = this._stat(prefix)\n\n  if (!this.matches[index])\n    this.matches[index] = Object.create(null)\n\n  // If it doesn't exist, then just mark the lack of results\n  if (!exists)\n    return\n\n  if (prefix && isAbsolute(prefix) && !this.nomount) {\n    var trail = /[\\/\\\\]$/.test(prefix)\n    if (prefix.charAt(0) === '/') {\n      prefix = path.join(this.root, prefix)\n    } else {\n      prefix = path.resolve(this.root, prefix)\n      if (trail)\n        prefix += '/'\n    }\n  }\n\n  if (process.platform === 'win32')\n    prefix = prefix.replace(/\\\\/g, '/')\n\n  // Mark this as a match\n  this._emitMatch(index, prefix)\n}\n\n// Returns either 'DIR', 'FILE', or false\nGlobSync.prototype._stat = function (f) {\n  var abs = this._makeAbs(f)\n  var needDir = f.slice(-1) === '/'\n\n  if (f.length > this.maxLength)\n    return false\n\n  if (!this.stat && ownProp(this.cache, abs)) {\n    var c = this.cache[abs]\n\n    if (Array.isArray(c))\n      c = 'DIR'\n\n    // It exists, but maybe not how we need it\n    if (!needDir || c === 'DIR')\n      return c\n\n    if (needDir && c === 'FILE')\n      return false\n\n    // otherwise we have to stat, because maybe c=true\n    // if we know it exists, but not what it is.\n  }\n\n  var exists\n  var stat = this.statCache[abs]\n  if (!stat) {\n    var lstat\n    try {\n      lstat = this.fs.lstatSync(abs)\n    } catch (er) {\n      if (er && (er.code === 'ENOENT' || er.code === 'ENOTDIR')) {\n        this.statCache[abs] = false\n        return false\n      }\n    }\n\n    if (lstat && lstat.isSymbolicLink()) {\n      try {\n        stat = this.fs.statSync(abs)\n      } catch (er) {\n        stat = lstat\n      }\n    } else {\n      stat = lstat\n    }\n  }\n\n  this.statCache[abs] = stat\n\n  var c = true\n  if (stat)\n    c = stat.isDirectory() ? 'DIR' : 'FILE'\n\n  this.cache[abs] = this.cache[abs] || c\n\n  if (needDir && c === 'FILE')\n    return false\n\n  return c\n}\n\nGlobSync.prototype._mark = function (p) {\n  return common.mark(this, p)\n}\n\nGlobSync.prototype._makeAbs = function (f) {\n  return common.makeAbs(this, f)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/node_modules/glob/sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/node_modules/rimraf/rimraf.js":
/*!************************************************************!*\
  !*** ./node_modules/fstream/node_modules/rimraf/rimraf.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = rimraf\nrimraf.sync = rimrafSync\n\nvar assert = __webpack_require__(/*! assert */ \"assert\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar fs = __webpack_require__(/*! fs */ \"fs\")\nvar glob = undefined\ntry {\n  glob = __webpack_require__(/*! glob */ \"(ssr)/./node_modules/fstream/node_modules/glob/glob.js\")\n} catch (_err) {\n  // treat glob as optional.\n}\nvar _0666 = parseInt('666', 8)\n\nvar defaultGlobOpts = {\n  nosort: true,\n  silent: true\n}\n\n// for EMFILE handling\nvar timeout = 0\n\nvar isWindows = (process.platform === \"win32\")\n\nfunction defaults (options) {\n  var methods = [\n    'unlink',\n    'chmod',\n    'stat',\n    'lstat',\n    'rmdir',\n    'readdir'\n  ]\n  methods.forEach(function(m) {\n    options[m] = options[m] || fs[m]\n    m = m + 'Sync'\n    options[m] = options[m] || fs[m]\n  })\n\n  options.maxBusyTries = options.maxBusyTries || 3\n  options.emfileWait = options.emfileWait || 1000\n  if (options.glob === false) {\n    options.disableGlob = true\n  }\n  if (options.disableGlob !== true && glob === undefined) {\n    throw Error('glob dependency not found, set `options.disableGlob = true` if intentional')\n  }\n  options.disableGlob = options.disableGlob || false\n  options.glob = options.glob || defaultGlobOpts\n}\n\nfunction rimraf (p, options, cb) {\n  if (typeof options === 'function') {\n    cb = options\n    options = {}\n  }\n\n  assert(p, 'rimraf: missing path')\n  assert.equal(typeof p, 'string', 'rimraf: path should be a string')\n  assert.equal(typeof cb, 'function', 'rimraf: callback function required')\n  assert(options, 'rimraf: invalid options argument provided')\n  assert.equal(typeof options, 'object', 'rimraf: options should be object')\n\n  defaults(options)\n\n  var busyTries = 0\n  var errState = null\n  var n = 0\n\n  if (options.disableGlob || !glob.hasMagic(p))\n    return afterGlob(null, [p])\n\n  options.lstat(p, function (er, stat) {\n    if (!er)\n      return afterGlob(null, [p])\n\n    glob(p, options.glob, afterGlob)\n  })\n\n  function next (er) {\n    errState = errState || er\n    if (--n === 0)\n      cb(errState)\n  }\n\n  function afterGlob (er, results) {\n    if (er)\n      return cb(er)\n\n    n = results.length\n    if (n === 0)\n      return cb()\n\n    results.forEach(function (p) {\n      rimraf_(p, options, function CB (er) {\n        if (er) {\n          if ((er.code === \"EBUSY\" || er.code === \"ENOTEMPTY\" || er.code === \"EPERM\") &&\n              busyTries < options.maxBusyTries) {\n            busyTries ++\n            var time = busyTries * 100\n            // try again, with the same exact callback as this one.\n            return setTimeout(function () {\n              rimraf_(p, options, CB)\n            }, time)\n          }\n\n          // this one won't happen if graceful-fs is used.\n          if (er.code === \"EMFILE\" && timeout < options.emfileWait) {\n            return setTimeout(function () {\n              rimraf_(p, options, CB)\n            }, timeout ++)\n          }\n\n          // already gone\n          if (er.code === \"ENOENT\") er = null\n        }\n\n        timeout = 0\n        next(er)\n      })\n    })\n  }\n}\n\n// Two possible strategies.\n// 1. Assume it's a file.  unlink it, then do the dir stuff on EPERM or EISDIR\n// 2. Assume it's a directory.  readdir, then do the file stuff on ENOTDIR\n//\n// Both result in an extra syscall when you guess wrong.  However, there\n// are likely far more normal files in the world than directories.  This\n// is based on the assumption that a the average number of files per\n// directory is >= 1.\n//\n// If anyone ever complains about this, then I guess the strategy could\n// be made configurable somehow.  But until then, YAGNI.\nfunction rimraf_ (p, options, cb) {\n  assert(p)\n  assert(options)\n  assert(typeof cb === 'function')\n\n  // sunos lets the root user unlink directories, which is... weird.\n  // so we have to lstat here and make sure it's not a dir.\n  options.lstat(p, function (er, st) {\n    if (er && er.code === \"ENOENT\")\n      return cb(null)\n\n    // Windows can EPERM on stat.  Life is suffering.\n    if (er && er.code === \"EPERM\" && isWindows)\n      fixWinEPERM(p, options, er, cb)\n\n    if (st && st.isDirectory())\n      return rmdir(p, options, er, cb)\n\n    options.unlink(p, function (er) {\n      if (er) {\n        if (er.code === \"ENOENT\")\n          return cb(null)\n        if (er.code === \"EPERM\")\n          return (isWindows)\n            ? fixWinEPERM(p, options, er, cb)\n            : rmdir(p, options, er, cb)\n        if (er.code === \"EISDIR\")\n          return rmdir(p, options, er, cb)\n      }\n      return cb(er)\n    })\n  })\n}\n\nfunction fixWinEPERM (p, options, er, cb) {\n  assert(p)\n  assert(options)\n  assert(typeof cb === 'function')\n  if (er)\n    assert(er instanceof Error)\n\n  options.chmod(p, _0666, function (er2) {\n    if (er2)\n      cb(er2.code === \"ENOENT\" ? null : er)\n    else\n      options.stat(p, function(er3, stats) {\n        if (er3)\n          cb(er3.code === \"ENOENT\" ? null : er)\n        else if (stats.isDirectory())\n          rmdir(p, options, er, cb)\n        else\n          options.unlink(p, cb)\n      })\n  })\n}\n\nfunction fixWinEPERMSync (p, options, er) {\n  assert(p)\n  assert(options)\n  if (er)\n    assert(er instanceof Error)\n\n  try {\n    options.chmodSync(p, _0666)\n  } catch (er2) {\n    if (er2.code === \"ENOENT\")\n      return\n    else\n      throw er\n  }\n\n  try {\n    var stats = options.statSync(p)\n  } catch (er3) {\n    if (er3.code === \"ENOENT\")\n      return\n    else\n      throw er\n  }\n\n  if (stats.isDirectory())\n    rmdirSync(p, options, er)\n  else\n    options.unlinkSync(p)\n}\n\nfunction rmdir (p, options, originalEr, cb) {\n  assert(p)\n  assert(options)\n  if (originalEr)\n    assert(originalEr instanceof Error)\n  assert(typeof cb === 'function')\n\n  // try to rmdir first, and only readdir on ENOTEMPTY or EEXIST (SunOS)\n  // if we guessed wrong, and it's not a directory, then\n  // raise the original error.\n  options.rmdir(p, function (er) {\n    if (er && (er.code === \"ENOTEMPTY\" || er.code === \"EEXIST\" || er.code === \"EPERM\"))\n      rmkids(p, options, cb)\n    else if (er && er.code === \"ENOTDIR\")\n      cb(originalEr)\n    else\n      cb(er)\n  })\n}\n\nfunction rmkids(p, options, cb) {\n  assert(p)\n  assert(options)\n  assert(typeof cb === 'function')\n\n  options.readdir(p, function (er, files) {\n    if (er)\n      return cb(er)\n    var n = files.length\n    if (n === 0)\n      return options.rmdir(p, cb)\n    var errState\n    files.forEach(function (f) {\n      rimraf(path.join(p, f), options, function (er) {\n        if (errState)\n          return\n        if (er)\n          return cb(errState = er)\n        if (--n === 0)\n          options.rmdir(p, cb)\n      })\n    })\n  })\n}\n\n// this looks simpler, and is strictly *faster*, but will\n// tie up the JavaScript thread and fail on excessively\n// deep directory trees.\nfunction rimrafSync (p, options) {\n  options = options || {}\n  defaults(options)\n\n  assert(p, 'rimraf: missing path')\n  assert.equal(typeof p, 'string', 'rimraf: path should be a string')\n  assert(options, 'rimraf: missing options')\n  assert.equal(typeof options, 'object', 'rimraf: options should be object')\n\n  var results\n\n  if (options.disableGlob || !glob.hasMagic(p)) {\n    results = [p]\n  } else {\n    try {\n      options.lstatSync(p)\n      results = [p]\n    } catch (er) {\n      results = glob.sync(p, options.glob)\n    }\n  }\n\n  if (!results.length)\n    return\n\n  for (var i = 0; i < results.length; i++) {\n    var p = results[i]\n\n    try {\n      var st = options.lstatSync(p)\n    } catch (er) {\n      if (er.code === \"ENOENT\")\n        return\n\n      // Windows can EPERM on stat.  Life is suffering.\n      if (er.code === \"EPERM\" && isWindows)\n        fixWinEPERMSync(p, options, er)\n    }\n\n    try {\n      // sunos lets the root user unlink directories, which is... weird.\n      if (st && st.isDirectory())\n        rmdirSync(p, options, null)\n      else\n        options.unlinkSync(p)\n    } catch (er) {\n      if (er.code === \"ENOENT\")\n        return\n      if (er.code === \"EPERM\")\n        return isWindows ? fixWinEPERMSync(p, options, er) : rmdirSync(p, options, er)\n      if (er.code !== \"EISDIR\")\n        throw er\n\n      rmdirSync(p, options, er)\n    }\n  }\n}\n\nfunction rmdirSync (p, options, originalEr) {\n  assert(p)\n  assert(options)\n  if (originalEr)\n    assert(originalEr instanceof Error)\n\n  try {\n    options.rmdirSync(p)\n  } catch (er) {\n    if (er.code === \"ENOENT\")\n      return\n    if (er.code === \"ENOTDIR\")\n      throw originalEr\n    if (er.code === \"ENOTEMPTY\" || er.code === \"EEXIST\" || er.code === \"EPERM\")\n      rmkidsSync(p, options)\n  }\n}\n\nfunction rmkidsSync (p, options) {\n  assert(p)\n  assert(options)\n  options.readdirSync(p).forEach(function (f) {\n    rimrafSync(path.join(p, f), options)\n  })\n\n  // We only end up here once we got ENOTEMPTY at least once, and\n  // at this point, we are guaranteed to have removed all the kids.\n  // So, we know that it won't be ENOENT or ENOTDIR or anything else.\n  // try really hard to delete stuff on windows, because it has a\n  // PROFOUNDLY annoying habit of not closing handles promptly when\n  // files are deleted, resulting in spurious ENOTEMPTY errors.\n  var retries = isWindows ? 100 : 1\n  var i = 0\n  do {\n    var threw = true\n    try {\n      var ret = options.rmdirSync(p, options)\n      threw = false\n      return ret\n    } finally {\n      if (++i < retries && threw)\n        continue\n    }\n  } while (true)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9ub2RlX21vZHVsZXMvcmltcmFmL3JpbXJhZi5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBOztBQUVBLGFBQWEsbUJBQU8sQ0FBQyxzQkFBUTtBQUM3QixXQUFXLG1CQUFPLENBQUMsa0JBQU07QUFDekIsU0FBUyxtQkFBTyxDQUFDLGNBQUk7QUFDckI7QUFDQTtBQUNBLFNBQVMsbUJBQU8sQ0FBQyxvRUFBTTtBQUN2QixFQUFFO0FBQ0Y7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsa0JBQWtCLG9CQUFvQjtBQUN0Qzs7QUFFQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBhbnktYXNzaXN0YW50LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2ZzdHJlYW0vbm9kZV9tb2R1bGVzL3JpbXJhZi9yaW1yYWYuanM/NmNjNCJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJpbXJhZlxucmltcmFmLnN5bmMgPSByaW1yYWZTeW5jXG5cbnZhciBhc3NlcnQgPSByZXF1aXJlKFwiYXNzZXJ0XCIpXG52YXIgcGF0aCA9IHJlcXVpcmUoXCJwYXRoXCIpXG52YXIgZnMgPSByZXF1aXJlKFwiZnNcIilcbnZhciBnbG9iID0gdW5kZWZpbmVkXG50cnkge1xuICBnbG9iID0gcmVxdWlyZShcImdsb2JcIilcbn0gY2F0Y2ggKF9lcnIpIHtcbiAgLy8gdHJlYXQgZ2xvYiBhcyBvcHRpb25hbC5cbn1cbnZhciBfMDY2NiA9IHBhcnNlSW50KCc2NjYnLCA4KVxuXG52YXIgZGVmYXVsdEdsb2JPcHRzID0ge1xuICBub3NvcnQ6IHRydWUsXG4gIHNpbGVudDogdHJ1ZVxufVxuXG4vLyBmb3IgRU1GSUxFIGhhbmRsaW5nXG52YXIgdGltZW91dCA9IDBcblxudmFyIGlzV2luZG93cyA9IChwcm9jZXNzLnBsYXRmb3JtID09PSBcIndpbjMyXCIpXG5cbmZ1bmN0aW9uIGRlZmF1bHRzIChvcHRpb25zKSB7XG4gIHZhciBtZXRob2RzID0gW1xuICAgICd1bmxpbmsnLFxuICAgICdjaG1vZCcsXG4gICAgJ3N0YXQnLFxuICAgICdsc3RhdCcsXG4gICAgJ3JtZGlyJyxcbiAgICAncmVhZGRpcidcbiAgXVxuICBtZXRob2RzLmZvckVhY2goZnVuY3Rpb24obSkge1xuICAgIG9wdGlvbnNbbV0gPSBvcHRpb25zW21dIHx8IGZzW21dXG4gICAgbSA9IG0gKyAnU3luYydcbiAgICBvcHRpb25zW21dID0gb3B0aW9uc1ttXSB8fCBmc1ttXVxuICB9KVxuXG4gIG9wdGlvbnMubWF4QnVzeVRyaWVzID0gb3B0aW9ucy5tYXhCdXN5VHJpZXMgfHwgM1xuICBvcHRpb25zLmVtZmlsZVdhaXQgPSBvcHRpb25zLmVtZmlsZVdhaXQgfHwgMTAwMFxuICBpZiAob3B0aW9ucy5nbG9iID09PSBmYWxzZSkge1xuICAgIG9wdGlvbnMuZGlzYWJsZUdsb2IgPSB0cnVlXG4gIH1cbiAgaWYgKG9wdGlvbnMuZGlzYWJsZUdsb2IgIT09IHRydWUgJiYgZ2xvYiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgRXJyb3IoJ2dsb2IgZGVwZW5kZW5jeSBub3QgZm91bmQsIHNldCBgb3B0aW9ucy5kaXNhYmxlR2xvYiA9IHRydWVgIGlmIGludGVudGlvbmFsJylcbiAgfVxuICBvcHRpb25zLmRpc2FibGVHbG9iID0gb3B0aW9ucy5kaXNhYmxlR2xvYiB8fCBmYWxzZVxuICBvcHRpb25zLmdsb2IgPSBvcHRpb25zLmdsb2IgfHwgZGVmYXVsdEdsb2JPcHRzXG59XG5cbmZ1bmN0aW9uIHJpbXJhZiAocCwgb3B0aW9ucywgY2IpIHtcbiAgaWYgKHR5cGVvZiBvcHRpb25zID09PSAnZnVuY3Rpb24nKSB7XG4gICAgY2IgPSBvcHRpb25zXG4gICAgb3B0aW9ucyA9IHt9XG4gIH1cblxuICBhc3NlcnQocCwgJ3JpbXJhZjogbWlzc2luZyBwYXRoJylcbiAgYXNzZXJ0LmVxdWFsKHR5cGVvZiBwLCAnc3RyaW5nJywgJ3JpbXJhZjogcGF0aCBzaG91bGQgYmUgYSBzdHJpbmcnKVxuICBhc3NlcnQuZXF1YWwodHlwZW9mIGNiLCAnZnVuY3Rpb24nLCAncmltcmFmOiBjYWxsYmFjayBmdW5jdGlvbiByZXF1aXJlZCcpXG4gIGFzc2VydChvcHRpb25zLCAncmltcmFmOiBpbnZhbGlkIG9wdGlvbnMgYXJndW1lbnQgcHJvdmlkZWQnKVxuICBhc3NlcnQuZXF1YWwodHlwZW9mIG9wdGlvbnMsICdvYmplY3QnLCAncmltcmFmOiBvcHRpb25zIHNob3VsZCBiZSBvYmplY3QnKVxuXG4gIGRlZmF1bHRzKG9wdGlvbnMpXG5cbiAgdmFyIGJ1c3lUcmllcyA9IDBcbiAgdmFyIGVyclN0YXRlID0gbnVsbFxuICB2YXIgbiA9IDBcblxuICBpZiAob3B0aW9ucy5kaXNhYmxlR2xvYiB8fCAhZ2xvYi5oYXNNYWdpYyhwKSlcbiAgICByZXR1cm4gYWZ0ZXJHbG9iKG51bGwsIFtwXSlcblxuICBvcHRpb25zLmxzdGF0KHAsIGZ1bmN0aW9uIChlciwgc3RhdCkge1xuICAgIGlmICghZXIpXG4gICAgICByZXR1cm4gYWZ0ZXJHbG9iKG51bGwsIFtwXSlcblxuICAgIGdsb2IocCwgb3B0aW9ucy5nbG9iLCBhZnRlckdsb2IpXG4gIH0pXG5cbiAgZnVuY3Rpb24gbmV4dCAoZXIpIHtcbiAgICBlcnJTdGF0ZSA9IGVyclN0YXRlIHx8IGVyXG4gICAgaWYgKC0tbiA9PT0gMClcbiAgICAgIGNiKGVyclN0YXRlKVxuICB9XG5cbiAgZnVuY3Rpb24gYWZ0ZXJHbG9iIChlciwgcmVzdWx0cykge1xuICAgIGlmIChlcilcbiAgICAgIHJldHVybiBjYihlcilcblxuICAgIG4gPSByZXN1bHRzLmxlbmd0aFxuICAgIGlmIChuID09PSAwKVxuICAgICAgcmV0dXJuIGNiKClcblxuICAgIHJlc3VsdHMuZm9yRWFjaChmdW5jdGlvbiAocCkge1xuICAgICAgcmltcmFmXyhwLCBvcHRpb25zLCBmdW5jdGlvbiBDQiAoZXIpIHtcbiAgICAgICAgaWYgKGVyKSB7XG4gICAgICAgICAgaWYgKChlci5jb2RlID09PSBcIkVCVVNZXCIgfHwgZXIuY29kZSA9PT0gXCJFTk9URU1QVFlcIiB8fCBlci5jb2RlID09PSBcIkVQRVJNXCIpICYmXG4gICAgICAgICAgICAgIGJ1c3lUcmllcyA8IG9wdGlvbnMubWF4QnVzeVRyaWVzKSB7XG4gICAgICAgICAgICBidXN5VHJpZXMgKytcbiAgICAgICAgICAgIHZhciB0aW1lID0gYnVzeVRyaWVzICogMTAwXG4gICAgICAgICAgICAvLyB0cnkgYWdhaW4sIHdpdGggdGhlIHNhbWUgZXhhY3QgY2FsbGJhY2sgYXMgdGhpcyBvbmUuXG4gICAgICAgICAgICByZXR1cm4gc2V0VGltZW91dChmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgIHJpbXJhZl8ocCwgb3B0aW9ucywgQ0IpXG4gICAgICAgICAgICB9LCB0aW1lKVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIHRoaXMgb25lIHdvbid0IGhhcHBlbiBpZiBncmFjZWZ1bC1mcyBpcyB1c2VkLlxuICAgICAgICAgIGlmIChlci5jb2RlID09PSBcIkVNRklMRVwiICYmIHRpbWVvdXQgPCBvcHRpb25zLmVtZmlsZVdhaXQpIHtcbiAgICAgICAgICAgIHJldHVybiBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgICAgcmltcmFmXyhwLCBvcHRpb25zLCBDQilcbiAgICAgICAgICAgIH0sIHRpbWVvdXQgKyspXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gYWxyZWFkeSBnb25lXG4gICAgICAgICAgaWYgKGVyLmNvZGUgPT09IFwiRU5PRU5UXCIpIGVyID0gbnVsbFxuICAgICAgICB9XG5cbiAgICAgICAgdGltZW91dCA9IDBcbiAgICAgICAgbmV4dChlcilcbiAgICAgIH0pXG4gICAgfSlcbiAgfVxufVxuXG4vLyBUd28gcG9zc2libGUgc3RyYXRlZ2llcy5cbi8vIDEuIEFzc3VtZSBpdCdzIGEgZmlsZS4gIHVubGluayBpdCwgdGhlbiBkbyB0aGUgZGlyIHN0dWZmIG9uIEVQRVJNIG9yIEVJU0RJUlxuLy8gMi4gQXNzdW1lIGl0J3MgYSBkaXJlY3RvcnkuICByZWFkZGlyLCB0aGVuIGRvIHRoZSBmaWxlIHN0dWZmIG9uIEVOT1RESVJcbi8vXG4vLyBCb3RoIHJlc3VsdCBpbiBhbiBleHRyYSBzeXNjYWxsIHdoZW4geW91IGd1ZXNzIHdyb25nLiAgSG93ZXZlciwgdGhlcmVcbi8vIGFyZSBsaWtlbHkgZmFyIG1vcmUgbm9ybWFsIGZpbGVzIGluIHRoZSB3b3JsZCB0aGFuIGRpcmVjdG9yaWVzLiAgVGhpc1xuLy8gaXMgYmFzZWQgb24gdGhlIGFzc3VtcHRpb24gdGhhdCBhIHRoZSBhdmVyYWdlIG51bWJlciBvZiBmaWxlcyBwZXJcbi8vIGRpcmVjdG9yeSBpcyA+PSAxLlxuLy9cbi8vIElmIGFueW9uZSBldmVyIGNvbXBsYWlucyBhYm91dCB0aGlzLCB0aGVuIEkgZ3Vlc3MgdGhlIHN0cmF0ZWd5IGNvdWxkXG4vLyBiZSBtYWRlIGNvbmZpZ3VyYWJsZSBzb21laG93LiAgQnV0IHVudGlsIHRoZW4sIFlBR05JLlxuZnVuY3Rpb24gcmltcmFmXyAocCwgb3B0aW9ucywgY2IpIHtcbiAgYXNzZXJ0KHApXG4gIGFzc2VydChvcHRpb25zKVxuICBhc3NlcnQodHlwZW9mIGNiID09PSAnZnVuY3Rpb24nKVxuXG4gIC8vIHN1bm9zIGxldHMgdGhlIHJvb3QgdXNlciB1bmxpbmsgZGlyZWN0b3JpZXMsIHdoaWNoIGlzLi4uIHdlaXJkLlxuICAvLyBzbyB3ZSBoYXZlIHRvIGxzdGF0IGhlcmUgYW5kIG1ha2Ugc3VyZSBpdCdzIG5vdCBhIGRpci5cbiAgb3B0aW9ucy5sc3RhdChwLCBmdW5jdGlvbiAoZXIsIHN0KSB7XG4gICAgaWYgKGVyICYmIGVyLmNvZGUgPT09IFwiRU5PRU5UXCIpXG4gICAgICByZXR1cm4gY2IobnVsbClcblxuICAgIC8vIFdpbmRvd3MgY2FuIEVQRVJNIG9uIHN0YXQuICBMaWZlIGlzIHN1ZmZlcmluZy5cbiAgICBpZiAoZXIgJiYgZXIuY29kZSA9PT0gXCJFUEVSTVwiICYmIGlzV2luZG93cylcbiAgICAgIGZpeFdpbkVQRVJNKHAsIG9wdGlvbnMsIGVyLCBjYilcblxuICAgIGlmIChzdCAmJiBzdC5pc0RpcmVjdG9yeSgpKVxuICAgICAgcmV0dXJuIHJtZGlyKHAsIG9wdGlvbnMsIGVyLCBjYilcblxuICAgIG9wdGlvbnMudW5saW5rKHAsIGZ1bmN0aW9uIChlcikge1xuICAgICAgaWYgKGVyKSB7XG4gICAgICAgIGlmIChlci5jb2RlID09PSBcIkVOT0VOVFwiKVxuICAgICAgICAgIHJldHVybiBjYihudWxsKVxuICAgICAgICBpZiAoZXIuY29kZSA9PT0gXCJFUEVSTVwiKVxuICAgICAgICAgIHJldHVybiAoaXNXaW5kb3dzKVxuICAgICAgICAgICAgPyBmaXhXaW5FUEVSTShwLCBvcHRpb25zLCBlciwgY2IpXG4gICAgICAgICAgICA6IHJtZGlyKHAsIG9wdGlvbnMsIGVyLCBjYilcbiAgICAgICAgaWYgKGVyLmNvZGUgPT09IFwiRUlTRElSXCIpXG4gICAgICAgICAgcmV0dXJuIHJtZGlyKHAsIG9wdGlvbnMsIGVyLCBjYilcbiAgICAgIH1cbiAgICAgIHJldHVybiBjYihlcilcbiAgICB9KVxuICB9KVxufVxuXG5mdW5jdGlvbiBmaXhXaW5FUEVSTSAocCwgb3B0aW9ucywgZXIsIGNiKSB7XG4gIGFzc2VydChwKVxuICBhc3NlcnQob3B0aW9ucylcbiAgYXNzZXJ0KHR5cGVvZiBjYiA9PT0gJ2Z1bmN0aW9uJylcbiAgaWYgKGVyKVxuICAgIGFzc2VydChlciBpbnN0YW5jZW9mIEVycm9yKVxuXG4gIG9wdGlvbnMuY2htb2QocCwgXzA2NjYsIGZ1bmN0aW9uIChlcjIpIHtcbiAgICBpZiAoZXIyKVxuICAgICAgY2IoZXIyLmNvZGUgPT09IFwiRU5PRU5UXCIgPyBudWxsIDogZXIpXG4gICAgZWxzZVxuICAgICAgb3B0aW9ucy5zdGF0KHAsIGZ1bmN0aW9uKGVyMywgc3RhdHMpIHtcbiAgICAgICAgaWYgKGVyMylcbiAgICAgICAgICBjYihlcjMuY29kZSA9PT0gXCJFTk9FTlRcIiA/IG51bGwgOiBlcilcbiAgICAgICAgZWxzZSBpZiAoc3RhdHMuaXNEaXJlY3RvcnkoKSlcbiAgICAgICAgICBybWRpcihwLCBvcHRpb25zLCBlciwgY2IpXG4gICAgICAgIGVsc2VcbiAgICAgICAgICBvcHRpb25zLnVubGluayhwLCBjYilcbiAgICAgIH0pXG4gIH0pXG59XG5cbmZ1bmN0aW9uIGZpeFdpbkVQRVJNU3luYyAocCwgb3B0aW9ucywgZXIpIHtcbiAgYXNzZXJ0KHApXG4gIGFzc2VydChvcHRpb25zKVxuICBpZiAoZXIpXG4gICAgYXNzZXJ0KGVyIGluc3RhbmNlb2YgRXJyb3IpXG5cbiAgdHJ5IHtcbiAgICBvcHRpb25zLmNobW9kU3luYyhwLCBfMDY2NilcbiAgfSBjYXRjaCAoZXIyKSB7XG4gICAgaWYgKGVyMi5jb2RlID09PSBcIkVOT0VOVFwiKVxuICAgICAgcmV0dXJuXG4gICAgZWxzZVxuICAgICAgdGhyb3cgZXJcbiAgfVxuXG4gIHRyeSB7XG4gICAgdmFyIHN0YXRzID0gb3B0aW9ucy5zdGF0U3luYyhwKVxuICB9IGNhdGNoIChlcjMpIHtcbiAgICBpZiAoZXIzLmNvZGUgPT09IFwiRU5PRU5UXCIpXG4gICAgICByZXR1cm5cbiAgICBlbHNlXG4gICAgICB0aHJvdyBlclxuICB9XG5cbiAgaWYgKHN0YXRzLmlzRGlyZWN0b3J5KCkpXG4gICAgcm1kaXJTeW5jKHAsIG9wdGlvbnMsIGVyKVxuICBlbHNlXG4gICAgb3B0aW9ucy51bmxpbmtTeW5jKHApXG59XG5cbmZ1bmN0aW9uIHJtZGlyIChwLCBvcHRpb25zLCBvcmlnaW5hbEVyLCBjYikge1xuICBhc3NlcnQocClcbiAgYXNzZXJ0KG9wdGlvbnMpXG4gIGlmIChvcmlnaW5hbEVyKVxuICAgIGFzc2VydChvcmlnaW5hbEVyIGluc3RhbmNlb2YgRXJyb3IpXG4gIGFzc2VydCh0eXBlb2YgY2IgPT09ICdmdW5jdGlvbicpXG5cbiAgLy8gdHJ5IHRvIHJtZGlyIGZpcnN0LCBhbmQgb25seSByZWFkZGlyIG9uIEVOT1RFTVBUWSBvciBFRVhJU1QgKFN1bk9TKVxuICAvLyBpZiB3ZSBndWVzc2VkIHdyb25nLCBhbmQgaXQncyBub3QgYSBkaXJlY3RvcnksIHRoZW5cbiAgLy8gcmFpc2UgdGhlIG9yaWdpbmFsIGVycm9yLlxuICBvcHRpb25zLnJtZGlyKHAsIGZ1bmN0aW9uIChlcikge1xuICAgIGlmIChlciAmJiAoZXIuY29kZSA9PT0gXCJFTk9URU1QVFlcIiB8fCBlci5jb2RlID09PSBcIkVFWElTVFwiIHx8IGVyLmNvZGUgPT09IFwiRVBFUk1cIikpXG4gICAgICBybWtpZHMocCwgb3B0aW9ucywgY2IpXG4gICAgZWxzZSBpZiAoZXIgJiYgZXIuY29kZSA9PT0gXCJFTk9URElSXCIpXG4gICAgICBjYihvcmlnaW5hbEVyKVxuICAgIGVsc2VcbiAgICAgIGNiKGVyKVxuICB9KVxufVxuXG5mdW5jdGlvbiBybWtpZHMocCwgb3B0aW9ucywgY2IpIHtcbiAgYXNzZXJ0KHApXG4gIGFzc2VydChvcHRpb25zKVxuICBhc3NlcnQodHlwZW9mIGNiID09PSAnZnVuY3Rpb24nKVxuXG4gIG9wdGlvbnMucmVhZGRpcihwLCBmdW5jdGlvbiAoZXIsIGZpbGVzKSB7XG4gICAgaWYgKGVyKVxuICAgICAgcmV0dXJuIGNiKGVyKVxuICAgIHZhciBuID0gZmlsZXMubGVuZ3RoXG4gICAgaWYgKG4gPT09IDApXG4gICAgICByZXR1cm4gb3B0aW9ucy5ybWRpcihwLCBjYilcbiAgICB2YXIgZXJyU3RhdGVcbiAgICBmaWxlcy5mb3JFYWNoKGZ1bmN0aW9uIChmKSB7XG4gICAgICByaW1yYWYocGF0aC5qb2luKHAsIGYpLCBvcHRpb25zLCBmdW5jdGlvbiAoZXIpIHtcbiAgICAgICAgaWYgKGVyclN0YXRlKVxuICAgICAgICAgIHJldHVyblxuICAgICAgICBpZiAoZXIpXG4gICAgICAgICAgcmV0dXJuIGNiKGVyclN0YXRlID0gZXIpXG4gICAgICAgIGlmICgtLW4gPT09IDApXG4gICAgICAgICAgb3B0aW9ucy5ybWRpcihwLCBjYilcbiAgICAgIH0pXG4gICAgfSlcbiAgfSlcbn1cblxuLy8gdGhpcyBsb29rcyBzaW1wbGVyLCBhbmQgaXMgc3RyaWN0bHkgKmZhc3RlciosIGJ1dCB3aWxsXG4vLyB0aWUgdXAgdGhlIEphdmFTY3JpcHQgdGhyZWFkIGFuZCBmYWlsIG9uIGV4Y2Vzc2l2ZWx5XG4vLyBkZWVwIGRpcmVjdG9yeSB0cmVlcy5cbmZ1bmN0aW9uIHJpbXJhZlN5bmMgKHAsIG9wdGlvbnMpIHtcbiAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge31cbiAgZGVmYXVsdHMob3B0aW9ucylcblxuICBhc3NlcnQocCwgJ3JpbXJhZjogbWlzc2luZyBwYXRoJylcbiAgYXNzZXJ0LmVxdWFsKHR5cGVvZiBwLCAnc3RyaW5nJywgJ3JpbXJhZjogcGF0aCBzaG91bGQgYmUgYSBzdHJpbmcnKVxuICBhc3NlcnQob3B0aW9ucywgJ3JpbXJhZjogbWlzc2luZyBvcHRpb25zJylcbiAgYXNzZXJ0LmVxdWFsKHR5cGVvZiBvcHRpb25zLCAnb2JqZWN0JywgJ3JpbXJhZjogb3B0aW9ucyBzaG91bGQgYmUgb2JqZWN0JylcblxuICB2YXIgcmVzdWx0c1xuXG4gIGlmIChvcHRpb25zLmRpc2FibGVHbG9iIHx8ICFnbG9iLmhhc01hZ2ljKHApKSB7XG4gICAgcmVzdWx0cyA9IFtwXVxuICB9IGVsc2Uge1xuICAgIHRyeSB7XG4gICAgICBvcHRpb25zLmxzdGF0U3luYyhwKVxuICAgICAgcmVzdWx0cyA9IFtwXVxuICAgIH0gY2F0Y2ggKGVyKSB7XG4gICAgICByZXN1bHRzID0gZ2xvYi5zeW5jKHAsIG9wdGlvbnMuZ2xvYilcbiAgICB9XG4gIH1cblxuICBpZiAoIXJlc3VsdHMubGVuZ3RoKVxuICAgIHJldHVyblxuXG4gIGZvciAodmFyIGkgPSAwOyBpIDwgcmVzdWx0cy5sZW5ndGg7IGkrKykge1xuICAgIHZhciBwID0gcmVzdWx0c1tpXVxuXG4gICAgdHJ5IHtcbiAgICAgIHZhciBzdCA9IG9wdGlvbnMubHN0YXRTeW5jKHApXG4gICAgfSBjYXRjaCAoZXIpIHtcbiAgICAgIGlmIChlci5jb2RlID09PSBcIkVOT0VOVFwiKVxuICAgICAgICByZXR1cm5cblxuICAgICAgLy8gV2luZG93cyBjYW4gRVBFUk0gb24gc3RhdC4gIExpZmUgaXMgc3VmZmVyaW5nLlxuICAgICAgaWYgKGVyLmNvZGUgPT09IFwiRVBFUk1cIiAmJiBpc1dpbmRvd3MpXG4gICAgICAgIGZpeFdpbkVQRVJNU3luYyhwLCBvcHRpb25zLCBlcilcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8gc3Vub3MgbGV0cyB0aGUgcm9vdCB1c2VyIHVubGluayBkaXJlY3Rvcmllcywgd2hpY2ggaXMuLi4gd2VpcmQuXG4gICAgICBpZiAoc3QgJiYgc3QuaXNEaXJlY3RvcnkoKSlcbiAgICAgICAgcm1kaXJTeW5jKHAsIG9wdGlvbnMsIG51bGwpXG4gICAgICBlbHNlXG4gICAgICAgIG9wdGlvbnMudW5saW5rU3luYyhwKVxuICAgIH0gY2F0Y2ggKGVyKSB7XG4gICAgICBpZiAoZXIuY29kZSA9PT0gXCJFTk9FTlRcIilcbiAgICAgICAgcmV0dXJuXG4gICAgICBpZiAoZXIuY29kZSA9PT0gXCJFUEVSTVwiKVxuICAgICAgICByZXR1cm4gaXNXaW5kb3dzID8gZml4V2luRVBFUk1TeW5jKHAsIG9wdGlvbnMsIGVyKSA6IHJtZGlyU3luYyhwLCBvcHRpb25zLCBlcilcbiAgICAgIGlmIChlci5jb2RlICE9PSBcIkVJU0RJUlwiKVxuICAgICAgICB0aHJvdyBlclxuXG4gICAgICBybWRpclN5bmMocCwgb3B0aW9ucywgZXIpXG4gICAgfVxuICB9XG59XG5cbmZ1bmN0aW9uIHJtZGlyU3luYyAocCwgb3B0aW9ucywgb3JpZ2luYWxFcikge1xuICBhc3NlcnQocClcbiAgYXNzZXJ0KG9wdGlvbnMpXG4gIGlmIChvcmlnaW5hbEVyKVxuICAgIGFzc2VydChvcmlnaW5hbEVyIGluc3RhbmNlb2YgRXJyb3IpXG5cbiAgdHJ5IHtcbiAgICBvcHRpb25zLnJtZGlyU3luYyhwKVxuICB9IGNhdGNoIChlcikge1xuICAgIGlmIChlci5jb2RlID09PSBcIkVOT0VOVFwiKVxuICAgICAgcmV0dXJuXG4gICAgaWYgKGVyLmNvZGUgPT09IFwiRU5PVERJUlwiKVxuICAgICAgdGhyb3cgb3JpZ2luYWxFclxuICAgIGlmIChlci5jb2RlID09PSBcIkVOT1RFTVBUWVwiIHx8IGVyLmNvZGUgPT09IFwiRUVYSVNUXCIgfHwgZXIuY29kZSA9PT0gXCJFUEVSTVwiKVxuICAgICAgcm1raWRzU3luYyhwLCBvcHRpb25zKVxuICB9XG59XG5cbmZ1bmN0aW9uIHJta2lkc1N5bmMgKHAsIG9wdGlvbnMpIHtcbiAgYXNzZXJ0KHApXG4gIGFzc2VydChvcHRpb25zKVxuICBvcHRpb25zLnJlYWRkaXJTeW5jKHApLmZvckVhY2goZnVuY3Rpb24gKGYpIHtcbiAgICByaW1yYWZTeW5jKHBhdGguam9pbihwLCBmKSwgb3B0aW9ucylcbiAgfSlcblxuICAvLyBXZSBvbmx5IGVuZCB1cCBoZXJlIG9uY2Ugd2UgZ290IEVOT1RFTVBUWSBhdCBsZWFzdCBvbmNlLCBhbmRcbiAgLy8gYXQgdGhpcyBwb2ludCwgd2UgYXJlIGd1YXJhbnRlZWQgdG8gaGF2ZSByZW1vdmVkIGFsbCB0aGUga2lkcy5cbiAgLy8gU28sIHdlIGtub3cgdGhhdCBpdCB3b24ndCBiZSBFTk9FTlQgb3IgRU5PVERJUiBvciBhbnl0aGluZyBlbHNlLlxuICAvLyB0cnkgcmVhbGx5IGhhcmQgdG8gZGVsZXRlIHN0dWZmIG9uIHdpbmRvd3MsIGJlY2F1c2UgaXQgaGFzIGFcbiAgLy8gUFJPRk9VTkRMWSBhbm5veWluZyBoYWJpdCBvZiBub3QgY2xvc2luZyBoYW5kbGVzIHByb21wdGx5IHdoZW5cbiAgLy8gZmlsZXMgYXJlIGRlbGV0ZWQsIHJlc3VsdGluZyBpbiBzcHVyaW91cyBFTk9URU1QVFkgZXJyb3JzLlxuICB2YXIgcmV0cmllcyA9IGlzV2luZG93cyA/IDEwMCA6IDFcbiAgdmFyIGkgPSAwXG4gIGRvIHtcbiAgICB2YXIgdGhyZXcgPSB0cnVlXG4gICAgdHJ5IHtcbiAgICAgIHZhciByZXQgPSBvcHRpb25zLnJtZGlyU3luYyhwLCBvcHRpb25zKVxuICAgICAgdGhyZXcgPSBmYWxzZVxuICAgICAgcmV0dXJuIHJldFxuICAgIH0gZmluYWxseSB7XG4gICAgICBpZiAoKytpIDwgcmV0cmllcyAmJiB0aHJldylcbiAgICAgICAgY29udGludWVcbiAgICB9XG4gIH0gd2hpbGUgKHRydWUpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/node_modules/rimraf/rimraf.js\n");

/***/ })

};
;