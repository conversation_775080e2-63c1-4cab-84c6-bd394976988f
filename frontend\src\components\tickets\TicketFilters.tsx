import React from 'react';
import { Tabs, Tab } from '@/components/ui';
import { TICKET_STATUS_FILTERS, TicketStatus } from '@/types/ticket';

interface TicketFiltersProps {
  activeFilter: 'all' | TicketStatus;
  onFilterChange: (filter: 'all' | TicketStatus) => void;
  counts?: Record<string, number>;
  className?: string;
}

export function TicketFilters({ 
  activeFilter, 
  onFilterChange, 
  counts,
  className = '' 
}: TicketFiltersProps) {
  const tabs: Tab[] = TICKET_STATUS_FILTERS.map(filter => ({
    value: filter.value,
    label: filter.label,
    count: counts?.[filter.value]
  }));

  return (
    <Tabs
      tabs={tabs}
      activeTab={activeFilter}
      onChange={(value) => onFilterChange(value as 'all' | TicketStatus)}
      className={className}
    />
  );
}
