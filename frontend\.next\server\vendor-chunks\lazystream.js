/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lazystream";
exports.ids = ["vendor-chunks/lazystream"];
exports.modules = {

/***/ "(ssr)/./node_modules/lazystream/lib/lazystream.js":
/*!***************************************************!*\
  !*** ./node_modules/lazystream/lib/lazystream.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var util = __webpack_require__(/*! util */ \"util\");\nvar PassThrough = __webpack_require__(/*! readable-stream/passthrough */ \"(ssr)/./node_modules/readable-stream/passthrough.js\");\n\nmodule.exports = {\n  Readable: Readable,\n  Writable: Writable\n};\n\nutil.inherits(Readable, PassThrough);\nutil.inherits(Writable, PassThrough);\n\n// Patch the given method of instance so that the callback\n// is executed once, before the actual method is called the\n// first time.\nfunction beforeFirstCall(instance, method, callback) {\n  instance[method] = function() {\n    delete instance[method];\n    callback.apply(this, arguments);\n    return this[method].apply(this, arguments);\n  };\n}\n\nfunction Readable(fn, options) {\n  if (!(this instanceof Readable))\n    return new Readable(fn, options);\n\n  PassThrough.call(this, options);\n\n  beforeFirstCall(this, '_read', function() {\n    var source = fn.call(this, options);\n    var emit = this.emit.bind(this, 'error');\n    source.on('error', emit);\n    source.pipe(this);\n  });\n\n  this.emit('readable');\n}\n\nfunction Writable(fn, options) {\n  if (!(this instanceof Writable))\n    return new Writable(fn, options);\n\n  PassThrough.call(this, options);\n\n  beforeFirstCall(this, '_write', function() {\n    var destination = fn.call(this, options);\n    var emit = this.emit.bind(this, 'error');\n    destination.on('error', emit);\n    this.pipe(destination);\n  });\n\n  this.emit('writable');\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lazystream/lib/lazystream.js\n");

/***/ })

};
;