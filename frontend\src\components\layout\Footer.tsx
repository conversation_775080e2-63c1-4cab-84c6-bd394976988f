'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FaTwitter, FaFacebook, FaLinkedin } from 'react-icons/fa';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const pathname = usePathname();
  const isHomePage = pathname === '/';
  
  // For home page: show full footer with contact info
  if (isHomePage) {
    return (
      <footer className="w-full py-8 text-black bg-white border-t border-gray-200">
        <div className="container mx-auto px-4 flex flex-col md:flex-row justify-between items-center gap-6">
          {/* Left Side - Contact Info & Social */}
          <div className="text-left mb-4 md:mb-0">
            <p className="text-sm md:text-base font-medium"><EMAIL> </p>
            <p className="text-sm md:text-base font-medium">Tel: ************</p>
            <div className="flex space-x-4 mt-3">
              <Link 
                href="https://twitter.com" 
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-gray-500 transition-colors"
                aria-label="Follow us on Twitter"
              >
                <FaTwitter size={20} />
              </Link>
              <Link 
                href="https://facebook.com" 
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-gray-500 transition-colors"
                aria-label="Follow us on Facebook"
              >
                <FaFacebook size={20} />
              </Link>
              <Link 
                href="https://linkedin.com" 
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-gray-500 transition-colors"
                aria-label="Follow us on LinkedIn"
              >
                <FaLinkedin size={20} />
              </Link>
            </div>
          </div>

          {/* Right Side - Legal & Copyright */}
          <div className="text-center md:text-right">
            <div className="flex flex-col md:flex-row gap-4 md:gap-6 mb-3">
              <Link 
                href="/terms" 
                className="text-sm md:text-base hover:text-gray-500 transition-colors"
              >
                Terms & Conditions
              </Link>
              <Link 
                href="/privacy" 
                className="text-sm md:text-base hover:text-gray-500 transition-colors"
              >
                Privacy Policy
              </Link>
            </div>
            <p className="text-xs md:text-sm text-gray-400">
              &copy; {currentYear} WorkplaceSLM - All rights reserved
            </p>
          </div>
        </div>
      </footer>
    );
  }
  
  // For other pages: show only privacy message
  return (
    <footer className="text-black bg-white border-t border-gray-200">
    </footer>
  );
}