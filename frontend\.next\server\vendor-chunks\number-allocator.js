/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/number-allocator";
exports.ids = ["vendor-chunks/number-allocator"];
exports.modules = {

/***/ "(ssr)/./node_modules/number-allocator/index.js":
/*!************************************************!*\
  !*** ./node_modules/number-allocator/index.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright Takatoshi Kondo 2021\n//\n// Distributed under the MIT License\n\nconst NumberAllocator = __webpack_require__(/*! ./lib/number-allocator.js */ \"(ssr)/./node_modules/number-allocator/lib/number-allocator.js\")\n\nmodule.exports.NumberAllocator = NumberAllocator\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbnVtYmVyLWFsbG9jYXRvci9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7O0FBRUEsd0JBQXdCLG1CQUFPLENBQUMsZ0dBQTJCOztBQUUzRCw4QkFBOEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wYW55LWFzc2lzdGFudC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9udW1iZXItYWxsb2NhdG9yL2luZGV4LmpzPzlkOTkiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IFRha2F0b3NoaSBLb25kbyAyMDIxXG4vL1xuLy8gRGlzdHJpYnV0ZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlXG5cbmNvbnN0IE51bWJlckFsbG9jYXRvciA9IHJlcXVpcmUoJy4vbGliL251bWJlci1hbGxvY2F0b3IuanMnKVxuXG5tb2R1bGUuZXhwb3J0cy5OdW1iZXJBbGxvY2F0b3IgPSBOdW1iZXJBbGxvY2F0b3JcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/number-allocator/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/number-allocator/lib/number-allocator.js":
/*!***************************************************************!*\
  !*** ./node_modules/number-allocator/lib/number-allocator.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("// Copyright Takatoshi Kondo 2021\n//\n// Distributed under the MIT License\n\n\n\nconst SortedSet = (__webpack_require__(/*! js-sdsl */ \"(ssr)/./node_modules/js-sdsl/dist/esm/index.js\").OrderedSet)\nconst debugTrace = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\")('number-allocator:trace')\nconst debugError = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\")('number-allocator:error')\n/**\n * Interval constructor\n * @constructor\n * @param {Number} low  - The lowest value of the interval\n * @param {Number} high - The highest value of the interval\n */\nfunction Interval (low, high) {\n  this.low = low\n  this.high = high\n}\n\nInterval.prototype.equals = function (other) {\n  return this.low === other.low && this.high === other.high\n}\n\nInterval.prototype.compare = function (other) {\n  if (this.low < other.low && this.high < other.low) return -1\n  if (other.low < this.low && other.high < this.low) return 1\n  return 0\n}\n\n/**\n * NumberAllocator constructor.\n * The all numbers are set to vacant status.\n * Time Complexity O(1)\n * @constructor\n * @param {Number} min  - The maximum number of allocatable. The number must be integer.\n * @param {Number} maxh - The minimum number of allocatable. The number must be integer.\n */\nfunction NumberAllocator (min, max) {\n  if (!(this instanceof NumberAllocator)) {\n    return new NumberAllocator(min, max)\n  }\n\n  this.min = min\n  this.max = max\n\n  this.ss = new SortedSet(\n    [],\n    (lhs, rhs) => {\n      return lhs.compare(rhs)\n    }\n  )\n  debugTrace('Create')\n  this.clear()\n}\n\n/**\n * Get the first vacant number. The status of the number is not updated.\n * Time Complexity O(1)\n * @return {Number} - The first vacant number. If all numbers are occupied, return null.\n *                    When alloc() is called then the same value will be allocated.\n */\nNumberAllocator.prototype.firstVacant = function () {\n  if (this.ss.size() === 0) return null\n  return this.ss.front().low\n}\n\n/**\n * Allocate the first vacant number. The number become occupied status.\n * Time Complexity O(1)\n * @return {Number} - The first vacant number. If all numbers are occupied, return null.\n */\nNumberAllocator.prototype.alloc = function () {\n  if (this.ss.size() === 0) {\n    debugTrace('alloc():empty')\n    return null\n  }\n  const it = this.ss.begin()\n  const low = it.pointer.low\n  const high = it.pointer.high\n  const num = low\n  if (num + 1 <= high) {\n    // x|----|\n    this.ss.updateKeyByIterator(it, new Interval(low + 1, high))\n  } else {\n    this.ss.eraseElementByPos(0)\n  }\n  debugTrace('alloc():' + num)\n  return num\n}\n\n/**\n * Use the number. The number become occupied status.\n * If the number has already been occupied, then return false.\n * Time Complexity O(logN) : N is the number of intervals (not numbers)\n * @param {Number} num - The number to request use.\n * @return {Boolean} - If `num` was not occupied, then return true, otherwise return false.\n */\nNumberAllocator.prototype.use = function (num) {\n  const key = new Interval(num, num)\n  const it = this.ss.lowerBound(key)\n  if (!it.equals(this.ss.end())) {\n    const low = it.pointer.low\n    const high = it.pointer.high\n    if (it.pointer.equals(key)) {\n      // |x|\n      this.ss.eraseElementByIterator(it)\n      debugTrace('use():' + num)\n      return true\n    }\n\n    // x |-----|\n    if (low > num) return false\n\n    // |x----|\n    if (low === num) {\n      // x|----|\n      this.ss.updateKeyByIterator(it, new Interval(low + 1, high))\n      debugTrace('use():' + num)\n      return true\n    }\n\n    // |----x|\n    if (high === num) {\n      // |----|x\n      this.ss.updateKeyByIterator(it, new Interval(low, high - 1))\n      debugTrace('use():' + num)\n      return true\n    }\n\n    // |--x--|\n    // x|--|\n    this.ss.updateKeyByIterator(it, new Interval(num + 1, high))\n    // |--|x|--|\n    this.ss.insert(new Interval(low, num - 1))\n    debugTrace('use():' + num)\n    return true\n  }\n\n  debugTrace('use():failed')\n  return false\n}\n\n/**\n * Deallocate the number. The number become vacant status.\n * Time Complexity O(logN) : N is the number of intervals (not numbers)\n * @param {Number} num - The number to deallocate. The number must be occupied status.\n *                       In other words, the number must be allocated by alloc() or occupied be use().\n */\nNumberAllocator.prototype.free = function (num) {\n  if (num < this.min || num > this.max) {\n    debugError('free():' + num + ' is out of range')\n    return\n  }\n  const key = new Interval(num, num)\n  const it = this.ss.upperBound(key)\n  if (it.equals(this.ss.end())) {\n    // ....v\n    if (it.equals(this.ss.begin())) {\n      // Insert new interval\n      this.ss.insert(key)\n      return\n    }\n    it.pre()\n    const low = it.pointer.high\n    const high = it.pointer.high\n    if (high + 1 === num) {\n      // Concat to left\n      this.ss.updateKeyByIterator(it, new Interval(low, num))\n    } else {\n      // Insert new interval\n      this.ss.insert(key)\n    }\n  } else {\n    if (it.equals(this.ss.begin())) {\n      // v....\n      if (num + 1 === it.pointer.low) {\n        // Concat to right\n        const high = it.pointer.high\n        this.ss.updateKeyByIterator(it, new Interval(num, high))\n      } else {\n        // Insert new interval\n        this.ss.insert(key)\n      }\n    } else {\n      // ..v..\n      const rLow = it.pointer.low\n      const rHigh = it.pointer.high\n      it.pre()\n      const lLow = it.pointer.low\n      const lHigh = it.pointer.high\n      if (lHigh + 1 === num) {\n        if (num + 1 === rLow) {\n          // Concat to left and right\n          this.ss.eraseElementByIterator(it)\n          this.ss.updateKeyByIterator(it, new Interval(lLow, rHigh))\n        } else {\n          // Concat to left\n          this.ss.updateKeyByIterator(it, new Interval(lLow, num))\n        }\n      } else {\n        if (num + 1 === rLow) {\n          // Concat to right\n          this.ss.eraseElementByIterator(it.next())\n          this.ss.insert(new Interval(num, rHigh))\n        } else {\n          // Insert new interval\n          this.ss.insert(key)\n        }\n      }\n    }\n  }\n  debugTrace('free():' + num)\n}\n\n/**\n * Clear all occupied numbers.\n * The all numbers are set to vacant status.\n * Time Complexity O(1)\n */\nNumberAllocator.prototype.clear = function () {\n  debugTrace('clear()')\n  this.ss.clear()\n  this.ss.insert(new Interval(this.min, this.max))\n}\n\n/**\n * Get the number of intervals. Interval is internal structure of this library.\n * This function is for debugging.\n * Time Complexity O(1)\n * @return {Number} - The number of intervals.\n */\nNumberAllocator.prototype.intervalCount = function () {\n  return this.ss.size()\n}\n\n/**\n * Dump the internal structor of the library.\n * This function is for debugging.\n * Time Complexity O(N) : N is the number of intervals (not numbers)\n */\nNumberAllocator.prototype.dump = function () {\n  console.log('length:' + this.ss.size())\n  for (const element of this.ss) {\n    console.log(element)\n  }\n}\n\nmodule.exports = NumberAllocator\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/number-allocator/lib/number-allocator.js\n");

/***/ })

};
;