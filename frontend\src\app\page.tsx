'use client';

import React from 'react';
import { MainLayout } from '@/components/layout';
import {
  HomeHero,
  HomeVideo,
  HomeNextGen,
  HomeUseCase,
  KnowledgeToAction,
  Certification,
  GetstartedVideo,
} from '@/components/home';

/**
 * Home Page Component
 * 
 * Main landing page for WorkplaceSLM application
 * Features: Hero section, product demo, features showcase, use cases, and certifications
 * 
 * <AUTHOR>
 * @returns {JSX.Element} The home page with all sections
 */
export default function Home(): JSX.Element {
  return (
    <MainLayout>
      <main 
        className="bg-white"
        aria-label="Main content"
        role="main"
      >
        {/* Hero Banner Section */}
        <section aria-label="Hero banner">
          <HomeHero />
        </section>
        
        {/* Video Demo Section */}
        <section aria-label="Product demonstration">
          <HomeVideo />
        </section>
        
        {/* Next-Gen Communication Section */}
        <section aria-label="How does it works">
          <HomeNextGen />
        </section>
        
        {/* Use Cases Section */}
        <section aria-label="Use cases">
          <HomeUseCase />
        </section>
        
        {/* Transform Knowledge into Action Section */}
        <section aria-label="Features">
          <KnowledgeToAction />
        </section>
        
        {/* Certification Section */}
        <section aria-label="Certifications and compliance">
          <Certification />
        </section>
        
        {/* Get Started Video CTA Section */}
        <section aria-label="Get started">
          <GetstartedVideo />
        </section>
      </main>
    </MainLayout>
  );
}
