from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, func, Float
from sqlalchemy.orm import joinedload
from typing import Optional, Dict, Any, List
from datetime import datetime
import logging
from collections import Counter
import uuid

from ..models.feedback import QueryFeedback, ExpertChatInteraction, SharedResponse
from ..schemas.feedback import FeedbackCreate, ResponseShare, SharedResponseCreate, FeedbackStats

logger = logging.getLogger(__name__)

class FeedbackService:
    """Service for handling feedback collection and analysis"""
    
    @staticmethod
    async def create_feedback(
        db: AsyncSession, 
        feedback_data: FeedbackCreate, 
        user_id: int
    ) -> dict:
        """Create or update feedback entry for expert chat interaction.

        Returns a payload matching FeedbackResponse schema.
        """
        # Find the interaction by response_id
        interaction = await ExpertChatInteraction.get_by_response_id(db, feedback_data.response_id)

        if not interaction:
            raise ValueError(f"No expert chat interaction found for response_id: {feedback_data.response_id}")

        # Verify that the user owns this interaction
        if interaction.user_id != user_id:
            raise ValueError("User can only provide feedback on their own interactions")

        # Create or update feedback using the upsert method
        feedback = await QueryFeedback.upsert_feedback(
            db=db,
            user_id=user_id,
            interaction_id=interaction.id,
            is_helpful=feedback_data.is_helpful,
            rating=feedback_data.rating,
            reasons=feedback_data.reasons,
            comment=feedback_data.comment
        )
        
        await db.commit()
        await db.refresh(feedback)

        logger.info(f"{'Updated' if hasattr(feedback, 'updated_at') else 'Created'} feedback {feedback.id} for interaction {interaction.id} by user {user_id}")

        # Return the feedback data in FeedbackResponse format
        return feedback.to_dict()
    
    @staticmethod
    async def get_user_feedback(db: AsyncSession, user_id: int) -> List[dict]:
        """Get all feedback from a specific user.

        Returns items in FeedbackResponse format.
        """
        feedback_list = await QueryFeedback.get_user_feedback(db, user_id)
        return [feedback.to_dict() for feedback in feedback_list]
    
    @staticmethod
    async def get_feedback_stats(db: AsyncSession) -> FeedbackStats:
        """Get comprehensive feedback statistics from QueryFeedback table"""
        # Total expert chat interactions
        total_interactions = await db.scalar(select(func.count(ExpertChatInteraction.id)))
        
        # Total feedback entries
        total_feedback = await db.scalar(select(func.count(QueryFeedback.id)))
        
        # Helpful feedback (positive)
        helpful_feedback = await db.scalar(
            select(func.count(QueryFeedback.id)).where(
                QueryFeedback.is_helpful == True
            )
        )
        
        helpful_percentage = (helpful_feedback / total_feedback * 100) if total_feedback > 0 else 0
        
        # Average rating
        avg_rating_result = await db.scalar(select(func.avg(QueryFeedback.rating)))
        average_rating = float(avg_rating_result) if avg_rating_result else 0.0
        
        # Rating distribution
        result = await db.execute(
            select(
                QueryFeedback.rating,
                func.count(QueryFeedback.rating).label('count')
            ).group_by(QueryFeedback.rating)
        )
        rating_counts = result.all()
        
        rating_distribution = {rating: count for rating, count in rating_counts}
        
        # Most helpful queries (high ratings 9-10)
        result = await db.execute(
            select(QueryFeedback).join(
                ExpertChatInteraction, QueryFeedback.interaction_id == ExpertChatInteraction.id
            ).where(
                QueryFeedback.rating >= 9
            ).order_by(desc(QueryFeedback.rating), desc(QueryFeedback.created_at)).limit(10)
        )
        most_helpful_feedback = result.scalars().all()
        
        # Least helpful queries (low ratings 1-2)
        result = await db.execute(
            select(QueryFeedback).join(
                ExpertChatInteraction, QueryFeedback.interaction_id == ExpertChatInteraction.id
            ).where(
                QueryFeedback.rating <= 2
            ).order_by(QueryFeedback.rating, desc(QueryFeedback.created_at)).limit(10)
        )
        least_helpful_feedback = result.scalars().all()
        
        # Popular agent types with average ratings
        result = await db.execute(
            select(
                ExpertChatInteraction.agent_type,
                func.count(QueryFeedback.id).label('feedback_count'),
                func.avg(QueryFeedback.rating).label('avg_rating')
            ).join(
                QueryFeedback, ExpertChatInteraction.id == QueryFeedback.interaction_id
            ).where(
                ExpertChatInteraction.agent_type.isnot(None)
            ).group_by(ExpertChatInteraction.agent_type).order_by(desc('feedback_count'))
        )
        popular_agents = result.all()
        
        # Common reasons
        all_reasons = []
        result = await db.execute(
            select(QueryFeedback).where(
                QueryFeedback.reasons.isnot(None)
            )
        )
        feedback_with_reasons = result.scalars().all()
        
        for feedback in feedback_with_reasons:
            if feedback.reasons:
                all_reasons.extend(feedback.reasons)
        
        common_reasons = dict(Counter(all_reasons).most_common(10))
        
        return FeedbackStats(
            total_interactions=total_interactions,
            total_feedback=total_feedback,
            helpful_percentage=helpful_percentage,
            average_rating=average_rating,
            rating_distribution=rating_distribution,
            most_helpful_queries=[{
                'query': feedback.interaction.query if feedback.interaction else '',
                'rating': feedback.rating,
                'reasons': feedback.reasons or [],
                'created_at': feedback.created_at.isoformat()
            } for feedback in most_helpful_feedback],
            least_helpful_queries=[{
                'query': feedback.interaction.query if feedback.interaction else '',
                'rating': feedback.rating,
                'reasons': feedback.reasons or [],
                'created_at': feedback.created_at.isoformat()
            } for feedback in least_helpful_feedback],
            popular_agent_types=[{
                'agent_type': agent[0],
                'feedback_count': agent[1],
                'avg_rating': float(agent[2]) if agent[2] else 0.0
            } for agent in popular_agents],
            common_reasons=common_reasons
        )

# QueryCacheService has been removed - no more caching for expert chat

class ResponseSharingService:
    """Service for sharing AI responses with colleagues"""
    
    @staticmethod
    async def share_response(
        db: AsyncSession,
        share_data: ResponseShare,
        shared_by_user_id: int
    ) -> SharedResponse:
        """Share an AI response with a user or group"""
        
        # Find the interaction by response_id (expecting this to be passed in share_data)
        response_id = share_data.response_data.get('response_id')
        if not response_id:
            # If no response_id provided, try to find by query and user
            result = await db.execute(
                select(ExpertChatInteraction).where(
                    ExpertChatInteraction.query == share_data.query,
                    ExpertChatInteraction.response == share_data.response,
                    ExpertChatInteraction.user_id == shared_by_user_id
                ).order_by(desc(ExpertChatInteraction.created_at))
            )
            interaction = result.scalars().first()
        else:
            interaction = await ExpertChatInteraction.get_by_response_id(db, response_id)
        
        if not interaction:
            # Create a new interaction for sharing purposes
            interaction = ExpertChatInteraction(
                user_id=shared_by_user_id,
                agent_type=share_data.agent_type,
                query=share_data.query,
                response=share_data.response,
                response_id=str(uuid.uuid4())
            )
            db.add(interaction)
            await db.commit()
            await db.refresh(interaction)
            logger.info(f"Created new interaction for sharing: {interaction.id}")
        
        # Create sharing record
        shared_response = SharedResponse(
            interaction_id=interaction.id,
            shared_by_user_id=shared_by_user_id,
            shared_to_type=share_data.share_to_type,
            shared_to_id=share_data.share_to_id,
            share_context=share_data.share_context
        )
        
        db.add(shared_response)
        await db.commit()
        await db.refresh(shared_response)
        
        # Eager-load relationships to avoid lazy loading issues
        result = await db.execute(
            select(SharedResponse)
            .options(
                joinedload(SharedResponse.interaction),
                joinedload(SharedResponse.shared_by_user)
            )
            .where(SharedResponse.id == shared_response.id)
        )
        shared_response = result.unique().scalars().first()
        
        logger.info(f"Response shared by user {shared_by_user_id} to {share_data.share_to_type} {share_data.share_to_id}")
        return shared_response
    
    @staticmethod
    async def get_shared_responses(
        db: AsyncSession,
        user_id: Optional[int] = None,
        shared_to_id: Optional[int] = None,
        shared_to_type: Optional[str] = None
    ) -> List[SharedResponse]:
        """Get shared responses based on filters"""
        stmt = select(SharedResponse)
        
        if user_id:
            stmt = stmt.where(SharedResponse.shared_by_user_id == user_id)
        
        if shared_to_id and shared_to_type:
            stmt = stmt.where(
                SharedResponse.shared_to_id == shared_to_id,
                SharedResponse.shared_to_type == shared_to_type
            )
        
        stmt = stmt.order_by(desc(SharedResponse.created_at))
        result = await db.execute(stmt)
        return result.scalars().all()
    
    @staticmethod
    def format_shared_response_for_chat(
        interaction: ExpertChatInteraction,
        share_context: Optional[str] = None,
        shared_by_user_id: Optional[int] = None,
        shared_by_username: Optional[str] = None
    ) -> Dict[str, Any]:
        """Format an expert chat interaction for sharing in chat"""
        
        # Log for debugging
        logger.info(f"Formatting shared response - Interaction ID: {interaction.id}, Response length: {len(interaction.response)}")
        
        # Quality score – neutral placeholder (interaction.feedback no longer exists)
        quality_score = 0.5
        
        return {
            "type": "shared_ai_response",
            "message_type": "shared_ai_response",
            "shared_response": {
                "query": interaction.query,
                "response": interaction.response,
                "agent_type": interaction.agent_type,
                "sources": [],  # Sources not stored in simplified model
                "interaction_id": interaction.id,
                "response_id": interaction.response_id,
                "quality_score": quality_score,
                "created_at": interaction.created_at.isoformat() if interaction.created_at else None
            },
            "share_context": share_context,
            "shared_by_user_id": shared_by_user_id,
            "shared_by_username": shared_by_username,
            "timestamp": datetime.utcnow().isoformat()
        } 