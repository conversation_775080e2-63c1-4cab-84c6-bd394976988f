#!/usr/bin/env python3
"""
Retrieval Client for AISaarthi

Direct client for document retrieval and response generation.
Handles query processing, document search, and response generation with pipeline caching.
"""

import os
import sys
import logging
import traceback
from typing import List, Dict, Any, Optional
from datetime import datetime
from threading import Lock

# Using proper relative imports now

# Import existing pipeline components
from enhanced_pipeline import EnhancedMultiModalRAGPipeline
from config import RetrieverConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Removed global pipeline caching for stateless architecture
# Pipelines are now created per request to avoid memory exhaustion

class PipelineManager:
    """
    Creates fresh RAG pipeline instances for each request (stateless).
    Ensures no memory buildup from cached pipeline instances.
    """
    
    def get_pipeline(self, 
                    retriever_config: Dict[str, Any],
                    gemini_api_key: str) -> EnhancedMultiModalRAGPipeline:
        """
        Create a fresh pipeline instance for each request (stateless).
        No caching to avoid memory exhaustion with multiple users.
        """
        
        logger.info(f"Creating fresh pipeline instance (stateless)")
        logger.info(f"🔧 Using reranking service URL: {retriever_config.get('reranking_service_url')}")

        # Add detailed component timing
        from performance_logger import ComponentTimer
        import time
        
        try:
            # Create retriever config for this request
            with ComponentTimer.time_component("RetrieverConfig_Creation"):
                config = RetrieverConfig(
                    gemini_api_key=gemini_api_key,
                    qdrant_url=retriever_config.get('qdrant_url'),
                    prefer_grpc=retriever_config.get('prefer_grpc', True),
                    grpc_port=retriever_config.get('grpc_port'),
                    qdrant_api_key=retriever_config.get('qdrant_api_key'),
                    embedding_api_url=retriever_config.get('embedding_api_url'),
                    reranking_service_url=retriever_config.get('reranking_service_url'),
                    query_llm=retriever_config.get('query_llm'),
                    response_model=retriever_config.get('response_model')
                )

            logger.info(f"🔧 Config created with reranking_service_url: {config.reranking_service_url}")
            
            # Initialize pipeline (fresh instance per request)
            with ComponentTimer.time_component("EnhancedMultiModalRAGPipeline_Init"):
                pipeline = EnhancedMultiModalRAGPipeline(config)
            
            logger.info(f"Successfully created fresh pipeline instance")
            return pipeline
            
        except Exception as e:
            logger.error(f"Failed to create pipeline: {e}")
            logger.error(traceback.format_exc())
            raise e
    
    # Removed caching methods - no longer needed with stateless architecture

# Global pipeline manager instance
pipeline_manager = PipelineManager()

def query_documents(query: str,
                   collection_name: str,
                   username: str,
                   retriever_config: Dict[str, Any],
                   query_params: Dict[str, Any],
                   gemini_api_key: str,
                   chat_history: List[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Process a query through the enhanced retrieval pipeline
    """
    start_time = datetime.now()
    
    try:
        if chat_history is None:
            chat_history = []
            
        logger.info(f"Processing query: {query[:100]}...")
        logger.info(f"Collection: {collection_name}")
        logger.info(f"Username: {username}")
        
        # Create fresh pipeline instance with performance tracking
        from performance_logger import measure_time, log_memory_usage, log_request_metrics
        import time
        
        # Log memory before pipeline creation
        log_memory_usage()
        
        pipeline_start = time.perf_counter()
        
        with measure_time("RETRIEVAL_PIPELINE_FULL_CREATION") as timer:
            pipeline = pipeline_manager.get_pipeline(
                retriever_config=retriever_config,
                gemini_api_key=gemini_api_key
            )
        
        pipeline_creation_time = time.perf_counter() - pipeline_start
        
        # Log memory after pipeline creation
        log_memory_usage()
        
        # Run the pipeline with dynamic parameters
        result = pipeline.run_pipeline(
            query=query,
            chat_history=chat_history,
            collection_name=collection_name,
            db_path=None,
            response_instructions=retriever_config.get('response_instructions', []),
            use_query_transform=query_params.get('use_query_transform', True),
            use_multi_query_generation=query_params.get('use_multi_query_generation', True),
            force_rag=query_params.get('force_rag', False),
            allowed_files=query_params.get('allowed_files')
        )
        
        # Debug logging
        logger.info(f"Pipeline result keys: {list(result.keys()) if isinstance(result, dict) else type(result)}")
        logger.info(f"Pipeline result answer: {result.get('answer', 'NOT_FOUND') if isinstance(result, dict) else 'NOT_DICT'}")
        logger.info(f"Pipeline result final_response: {result.get('final_response', 'NOT_FOUND') if isinstance(result, dict) else 'NOT_DICT'}")
        logger.info(f"Pipeline result used_rag: {result.get('used_rag', 'NOT_FOUND') if isinstance(result, dict) else 'NOT_DICT'}")
        logger.info(f"Pipeline result retrieved_docs count: {len(result.get('retrieved_docs', [])) if isinstance(result, dict) else 'NOT_DICT'}")
        logger.info(f"Pipeline result reranked_docs count: {len(result.get('reranked_docs', [])) if isinstance(result, dict) else 'NOT_DICT'}")
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        processing_time = total_time - pipeline_creation_time
        
        # Log comprehensive request metrics
        request_id = f"retrieval_{username}_{int(start_time.timestamp())}"
        log_request_metrics(
            request_id=request_id,
            operation="document_retrieval", 
            total_time=total_time,
            pipeline_time=pipeline_creation_time,
            processing_time=processing_time
        )
        
        # Extract document sources and IDs from the enhanced pipeline result
        document_sources = []
        document_ids: List[str] = []
        
        # Enhanced pipeline stores sources in raw_docs
        raw_docs = result.get("raw_docs", [])
        reranked_docs = result.get("reranked_docs", [])
        
        if raw_docs:
            # Use raw_docs which contain the actual document content and sources
            for i, doc in enumerate(raw_docs):
                if isinstance(doc, dict):
                    # Collect document ID if present
                    if doc.get("id"):
                        document_ids.append(doc.get("id"))
                    # Get relevance score from corresponding reranked doc if available
                    relevance_score = 0.0
                    if i < len(reranked_docs) and hasattr(reranked_docs[i], 'metadata'):
                        # Try multiple ways to get the confidence/relevance score
                        metadata = reranked_docs[i].metadata
                        relevance_score = (
                            metadata.get('confidence_score') or 
                            metadata.get('relevance_score') or 
                            getattr(reranked_docs[i], 'relevance_score', 0.0)
                        )
                    
                    # Log the confidence score being assigned
                    logger.info(f"Document {i+1}: Source={doc.get('source', 'Unknown')}, Confidence={relevance_score}")
                    
                    document_sources.append({
                        "source": doc.get("source", "Unknown"),
                        "page_number": doc.get("page_number"),
                        "relevance_score": relevance_score,
                        "content_snippet": doc.get("content", "")  # Truncate for response size
                    })
        elif reranked_docs:
            # Fallback to reranked_docs if raw_docs not available
            for doc in reranked_docs:
                if isinstance(doc, dict):
                    # Collect document ID from metadata if present
                    meta = doc.get("metadata", {}) if isinstance(doc, dict) else {}
                    if meta.get("id"):
                        document_ids.append(meta.get("id"))
                    document_sources.append({
                        "source": doc.get("source", doc.get("metadata", {}).get("source", "Unknown")),
                        "page_number": doc.get("page_number", doc.get("metadata", {}).get("page_number")),
                        "relevance_score": doc.get("relevance_score", doc.get("score", 0.0)),
                        "content_snippet": doc.get("content_snippet", doc.get("content", ""))
                    })
                else:
                    # Handle LangChain Document objects
                    meta = getattr(doc, 'metadata', {})
                    doc_id = meta.get('id') if isinstance(meta, dict) else None
                    if doc_id:
                        document_ids.append(doc_id)
                    document_sources.append({
                        "source": getattr(doc, 'metadata', {}).get('source', 'Unknown'),
                        "page_number": getattr(doc, 'metadata', {}).get('page_number'),
                        "relevance_score": getattr(doc, 'relevance_score', 0.0),
                        "content_snippet": str(doc.page_content) if hasattr(doc, 'page_content') else str(doc)
                    })
        
        # Create processing stats
        processing_stats = {
            "total_time": total_time,
            "retrieval_time": result.get("retrieval_time"),
            "generation_time": result.get("generation_time"),
            "reranking_time": result.get("reranking_time")
        }
        
        logger.info(f"Query processed successfully in {total_time:.2f}s")
        
        return {
            "success": True,
            "agent_name": result.get("agent_name", f"{username.title()} Assistant"),
            "query": query,
            "answer": result.get("final_response", result.get("answer", "No answer generated")),
            "used_rag": result.get("used_rag", False),
            "classification_confidence": result.get("classification_confidence"),
            "document_sources": document_sources,
            "document_ids": list(dict.fromkeys(document_ids)),
            "processing_stats": processing_stats
        }
        
    except Exception as e:
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        
        logger.error(f"Error processing query: {e}")
        logger.error(traceback.format_exc())
        
        # Return error response
        return {
            "success": False,
            "query": query,
            "answer": f"Error processing query: {str(e)}",
            "used_rag": False,
            "document_sources": [],
            "processing_stats": {"total_time": total_time}
        }

def health_check() -> Dict[str, Any]:
    """Health check function"""
    try:
        import requests

        # Check embedding service health
        embedding_healthy = False
        try:
            embedding_url = f"{retriever_config.get('embedding_api_url')}/health"
            response = requests.get(embedding_url, timeout=10)
            embedding_healthy = response.status_code == 200
        except Exception as e:
            logger.warning(f"Embedding service health check failed: {e}")

        # Check reranking service health
        reranking_healthy = False
        try:
            reranking_url = f"{retriever_config.get('reranking_service_url')}/health"
            response = requests.get(reranking_url, timeout=10)
            reranking_healthy = response.status_code == 200
        except Exception as e:
            logger.warning(f"Reranking service health check failed: {e}")

        # Check pipeline status - we now use a shared pipeline
        cached_pipelines = pipeline_manager.get_cached_pipelines()
        active_pipelines = len(cached_pipelines)

        # Check if shared pipeline is loaded
        models_loaded = "shared_pipeline" in cached_pipelines

        # Overall status based on external services
        status = "healthy" if (embedding_healthy and reranking_healthy) else "partial"

        return {
            "status": status,
            "models_loaded": models_loaded,
            "active_pipelines": active_pipelines,
            "embedding_service": embedding_healthy,
            "reranking_service": reranking_healthy
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "models_loaded": False,
            "active_pipelines": 0,
            "embedding_service": False,
            "reranking_service": False
        }

def get_cached_pipelines() -> Dict[str, Any]:
    """Get information about cached pipelines"""
    try:
        cached_pipelines = pipeline_manager.get_cached_pipelines()
        return {
            "active_pipelines": cached_pipelines,
            "total_count": len(cached_pipelines)
        }
    except Exception as e:
        logger.error(f"Error getting pipeline info: {e}")
        return {"error": str(e)}