'use client';

import React from 'react';
import { TicketProvider, useTicketContext, useTicketAdmin } from '@/contexts';
import { TicketFilters } from '../tickets/TicketFilters';
import { TicketList } from '../tickets/TicketList';
import TicketModal from '../tickets/TicketModal';
import { Pagination } from '../tickets/Pagination';
import { ErrorBanner } from '@/components/ui';
import { ErrorBoundary } from '../ErrorBoundary';

/**
 * Summary Statistics Component
 */
function TicketSummaryCards() {
  const { summary } = useTicketAdmin();

  if (!summary) return null;

  const stats = [
    { label: 'Total', value: summary.total, color: 'text-gray-900' },
    { label: 'Open', value: summary.open, color: 'text-yellow-600' },
    { label: 'In Progress', value: summary.in_progress, color: 'text-blue-900' },
    { label: 'Resolved', value: summary.resolved, color: 'text-green-600' },
    { label: 'Closed', value: summary.closed, color: 'text-gray-600' },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
      {stats.map((stat) => (
        <div key={stat.label} className="bg-white p-4 rounded-lg shadow-sm border">
          <div className={`text-2xl font-bold ${stat.color}`}>{stat.value}</div>
          <div className="text-sm text-gray-600">{stat.label}</div>
        </div>
      ))}
    </div>
  );
}

/**
 * Internal component that uses the TicketContext
 */
function TicketsPanelContent() {
  const {
    tickets,
    loading,
    error,
    total,
    currentPage,
    perPage,
    hasAnyTickets,
    activeFilter,
    setActiveFilter,
    setPage,
    selectedTicket,
    isViewModalOpen,
    openViewModal,
    closeViewModal,
    refreshTickets,
  } = useTicketContext();

  const { updateTicketStatus, summary } = useTicketAdmin();

  const handleStatusChange = async (ticketId: number, newStatus: string) => {
    try {
      await updateTicketStatus(ticketId, newStatus);
    } catch (err) {
      console.error('Error updating ticket status:', err);
    }
  };

  // Calculate filter counts from summary
  const filterCounts = summary ? {
    all: summary.total,
    open: summary.open,
    in_progress: summary.in_progress,
    resolved: summary.resolved,
    closed: summary.closed,
  } : undefined;

  return (
    <section className="mt-8">
      {/* Header */}
      <div className="text-left mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Support Tickets</h2>
        <p className="mt-1 text-sm text-gray-600">Manage user support tickets</p>
      </div>

      {/* Error Banner */}
      {error && <ErrorBanner message={error} />}

      {/* Summary Cards */}
      <TicketSummaryCards />

      {/* Filters with Counts */}
      <TicketFilters
        activeFilter={activeFilter}
        onFilterChange={setActiveFilter}
        counts={filterCounts}
      />

      {/* Ticket List */}
      <TicketList
        tickets={tickets}
        loading={loading}
        hasAnyTickets={hasAnyTickets}
        activeFilter={activeFilter}
        onTicketClick={openViewModal}
        isAdmin={true}
      />

      {/* Pagination */}
      {!loading && total > perPage && (
        <Pagination
          currentPage={currentPage}
          totalItems={total}
          itemsPerPage={perPage}
          onPageChange={setPage}
          className="mt-6"
        />
      )}

      {/* View/Edit Modal */}
      <TicketModal
        isOpen={isViewModalOpen}
        onClose={closeViewModal}
        ticket={selectedTicket}
        isAdmin={true}
        onStatusChange={handleStatusChange}
        onRefresh={refreshTickets}
      />
    </section>
  );
}

/**
 * Main component with Provider wrapper
 */
export default function TicketsPanel() {
  return (
    <ErrorBoundary>
      <TicketProvider isAdmin={true}>
        <TicketsPanelContent />
      </TicketProvider>
    </ErrorBoundary>
  );
}