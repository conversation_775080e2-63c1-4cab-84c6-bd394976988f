'use client';

import React, { useEffect, useMemo, useRef, useState, useCallback } from 'react';
import { listNotes, deleteNote, getNote, updateNote } from '@/api/notes';
import { NoteItem } from '@/types/note';

interface NotesPanelProps {
  onSidebarToggle?: (isOpen: boolean) => void;
}

export default function NotesPanel({ onSidebarToggle }: NotesPanelProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [notes, setNotes] = useState<NoteItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Cache last load params to avoid re-fetching on every open (mirror KnowledgeBaseSidebar's one-time loads)
  const lastLoadKeyRef = useRef<string>('');
  const hasInitialLoadRef = useRef<boolean>(false);
  // Token to force re-run of loader when a refresh is requested
  const [refreshToken, setRefreshToken] = useState<number>(0);

  // Search & pagination
  const [search, setSearch] = useState<string>('');
  const [debouncedSearch, setDebouncedSearch] = useState<string>('');
  const [page, setPage] = useState<number>(1);
  const pageSize = 20;

  // Edit modal state
  const [isEditOpen, setIsEditOpen] = useState<boolean>(false);
  const [editingNoteId, setEditingNoteId] = useState<number | null>(null);
  const [editTitle, setEditTitle] = useState<string>('');
  const [editExtra, setEditExtra] = useState<string>('');
  const [editCombined, setEditCombined] = useState<string>('');
  // Removed pin/archive options per requirements
  const [savingEdit, setSavingEdit] = useState<boolean>(false);
  const [editError, setEditError] = useState<string | null>(null);

  // View modal state
  const [isViewOpen, setIsViewOpen] = useState<boolean>(false);
  const [viewNote, setViewNote] = useState<NoteItem | null>(null);

  // Notify parent when visibility changes 
  useEffect(() => {
    if (onSidebarToggle) onSidebarToggle(isOpen);
  }, [isOpen, onSidebarToggle]);

  // Auto-open if URL contains a flag (e.g., /chat?openNotes=1)
  useEffect(() => {
    try {
      const url = new URL(window.location.href);
      const flag = url.searchParams.get('openNotes');
      if (flag && ['1', 'true', 'yes'].includes(flag.toLowerCase())) {
        setIsOpen(true);
        // Clean up the URL so it does not keep re-opening on refresh
        url.searchParams.delete('openNotes');
        window.history.replaceState(null, '', url.toString());
      }
    } catch {}
  }, []);

  // Open via events (support both new and legacy names)
  useEffect(() => {
    const open = () => setIsOpen(true);
    window.addEventListener('open-notes', open);
    window.addEventListener('open-notes-panel', open);
    // Optional manual refresh
    const refresh = () => {
      // Bust the skip-cache key and trigger a refresh cycle even if already open
      lastLoadKeyRef.current = '';
      setRefreshToken((v) => v + 1);
      setIsOpen(true);
    };
    window.addEventListener('refresh-notes', refresh as EventListener);
    return () => {
      window.removeEventListener('open-notes', open);
      window.removeEventListener('open-notes-panel', open);
      window.removeEventListener('refresh-notes', refresh as EventListener);
    };
  }, []);

  // Close via event so it can coordinate with other sidebars
  useEffect(() => {
    const close = () => setIsOpen(false);
    window.addEventListener('close-notes', close);
    return () => window.removeEventListener('close-notes', close);
  }, []);

  // Debounce search
  useEffect(() => {
    const t = setTimeout(() => setDebouncedSearch(search.trim()), 300);
    return () => clearTimeout(t);
  }, [search]);

  

  // Initial one-time load on mount 
  useEffect(() => {
    if (hasInitialLoadRef.current) return;
    const load = async () => {
      try {
        setLoading(true);
        const data = await listNotes({ search: undefined, page: 1, page_size: pageSize });
        setNotes(data);
        lastLoadKeyRef.current = `|1`; // empty search, page 1
        hasInitialLoadRef.current = true;
      } catch (e: any) {
        setError(e?.message || 'Failed to load notes');
      } finally {
        setLoading(false);
      }
    };
    load();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Load when opened and on search/pagination changes (skip if params unchanged)
  useEffect(() => {
    if (!isOpen) return;
    const load = async () => {
      setLoading(true);
      setError(null);
      try {
        // Skip fetch if parameters unchanged
        const key = `${debouncedSearch}|${page}`;
        if (notes.length > 0 && lastLoadKeyRef.current === key) return;
        const data = await listNotes({ search: debouncedSearch || undefined, page, page_size: pageSize });
        setNotes(data);
        lastLoadKeyRef.current = key;
      } catch (e: any) {
        setError(e?.message || 'Failed to load notes');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [isOpen, debouncedSearch, page, refreshToken]);

  const openEditForNote = async (noteId: number) => {
    try {
      setEditError(null);
      const note = await getNote(noteId);
      setEditingNoteId(note.id);
      setEditTitle(note.title || '');
      setEditExtra(note.extra_text || '');
      setEditCombined(note.combined_text || '');
      // pin/archive removed
      setIsEditOpen(true);
    } catch (e: any) {
      setEditError(e?.message || 'Failed to load note');
      setIsEditOpen(true);
    }
  };

  const handleSaveEdit = async () => {
    if (!editingNoteId) return;
    if (!editTitle.trim()) {
      setEditError('Title is required');
      return;
    }
    setSavingEdit(true);
    setEditError(null);
    try {
      await updateNote(editingNoteId, {
        title: editTitle.trim().slice(0, 255),
        extra_text: editExtra,
        combined_text: editCombined,
      });
      const data = await listNotes({ search: debouncedSearch || undefined, page, page_size: pageSize });
      setNotes(data);
      lastLoadKeyRef.current = `${debouncedSearch}|${page}`;
      setIsEditOpen(false);
      setEditingNoteId(null);
    } catch (e: any) {
      setEditError(e?.message || 'Failed to save changes');
    } finally {
      setSavingEdit(false);
    }
  };

  return (
    <>
      {/* Sidebar */}
      <div 
        className={`fixed top-[5rem] left-0 h-[calc(100%-5rem)] bg-white shadow-xl z-40 transition-all duration-300 ease-in-out overflow-hidden ${
          isOpen ? 'w-full md:w-[380px]' : 'w-0'
        }`}
      >
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="bg-gray-300 text-black/80 p-4 flex items-center justify-between border-b border-gray-300">
            <div className="flex flex-col items-start space-y-1">
              <h2 className="text-lg font-semibold">Your Notes</h2>
              <span className="text-sm text-slate-700 font-normal">
                Save snippets of important information for quick reference.
              </span>
            </div>
            <button 
              onClick={() => setIsOpen(false)}
              className="text-gray-700 hover:text-gray-900"
              aria-label="Close notes panel"
              title="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {/* Search */}
            <div className="mb-3">
              <label htmlFor="notes-search" className="block text-xs text-gray-600 mb-1">Search notes</label>
              <input
                id="notes-search"
                type="text"
                value={search}
                onChange={(e) => { setSearch(e.target.value); setPage(1); }}
                placeholder="Search by title or content..."
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                aria-label="Search notes"
              />
            </div>
            {loading ? (
              <div className="text-sm text-gray-500">Loading…</div>
            ) : error ? (
              <div className="text-sm text-red-600" role="alert" aria-live="polite">{error}</div>
            ) : notes.length === 0 ? (
              <div className="text-sm text-gray-500">No notes yet. Save an answer to get started.</div>
            ) : (
              <div className="space-y-3">
                {notes.map((note) => (
                  <div key={note.id} className="border rounded-lg p-3">
                    <div className="flex items-start justify-between gap-2">
                      <div>
                        <div
                          className="text-sm font-medium text-gray-900 max-w-[240px] overflow-hidden"
                          style={{ display: '-webkit-box', WebkitLineClamp: '2', WebkitBoxOrient: 'vertical' }}
                        >
                          {note.title}
                        </div>
                        <div className="text-xs text-gray-500">{new Date(note.created_at).toLocaleString()}</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          className="text-xs text-gray-700 hover:text-gray-900"
                          onClick={() => { setViewNote(note); setIsViewOpen(true); }}
                          aria-label={`View note ${note.title}`}
                          title="View"
                        >
                          View
                        </button>
                        <button
                          className="text-xs text-blue-900 hover:text-blue-800"
                          onClick={() => openEditForNote(note.id)}
                          aria-label={`Edit note ${note.title}`}
                          title="Edit"
                        >
                          Edit
                        </button>
                        
                        <button
                          className="text-xs text-red-600 hover:text-red-800"
                          onClick={async () => {
                            if (!confirm('Delete this note?')) return;
                            try {
                              await deleteNote(note.id);
                              setNotes((prev) => prev.filter((n) => n.id !== note.id));
                            } catch (e: any) {
                              setError(e?.message || 'Failed to delete');
                            }
                          }}
                          aria-label={`Delete note ${note.title}`}
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          {/* Pagination */}
          <div className="border-t border-gray-200 p-3 flex items-center justify-between">
            <button
              className="px-3 py-1 text-sm rounded-md border border-gray-300 disabled:opacity-50"
              onClick={() => setPage((p) => Math.max(1, p - 1))}
              disabled={page === 1}
              aria-label="Previous page"
              title="Previous"
            >
              Previous
            </button>
            <div className="text-xs text-gray-600" aria-live="polite">Page {page}</div>
            <button
              className="px-3 py-1 text-sm rounded-md border border-gray-300 disabled:opacity-50"
              onClick={() => setPage((p) => p + 1)}
              disabled={notes.length < pageSize}
              aria-label="Next page"
              title="Next"
            >
              Next
            </button>
          </div>
        </div>
      </div>
      {/* Edit Modal */}
      {isEditOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" role="dialog" aria-modal="true" aria-label="Edit note">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 shadow-2xl max-h-[90dvh] sm:max-h-[85dvh] flex flex-col" onClick={(e) => e.stopPropagation()}>
            <div className="pb-3 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-base font-semibold text-gray-800">Edit Note</h3>
              <button
                onClick={() => { setIsEditOpen(false); setEditingNoteId(null); }}
                className="text-gray-500 hover:text-gray-700"
                aria-label="Close edit note"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="p-4 space-y-4 flex-1 min-h-0 overflow-y-auto">
              <div>
                <label className="block text-sm text-gray-700 mb-1">Title</label>
                <input
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                  maxLength={255}
                  placeholder="Brief title"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-1">Content</label>
                <textarea
                  value={editCombined}
                  onChange={(e) => setEditCombined(e.target.value)}
                  rows={8}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 whitespace-pre-wrap"
                  placeholder="Edit saved content (query + response)"
                />
              </div>

              <div>
                <label className="block text-sm text-gray-700 mb-1">Additional notes</label>
                <textarea
                  value={editExtra}
                  onChange={(e) => setEditExtra(e.target.value)}
                  rows={4}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add your own context or annotations"
                />
              </div>

              {/* Pin/Archive options removed */}

              {editError && <div className="text-sm text-red-600" role="alert">{editError}</div>}
            </div>

            <div className="pt-4 border-t border-gray-200 flex items-center justify-end gap-3">
              <button
                onClick={() => { setIsEditOpen(false); setEditingNoteId(null); }}
                className="px-4 py-2 bg-gray-100 text-gray-600 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveEdit}
                disabled={savingEdit}
                className="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Save changes"
              >
                {savingEdit ? 'Saving…' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}

      

      {isViewOpen && viewNote && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" role="dialog" aria-modal="true" aria-label="View note">
          <div className="bg-white rounded-lg p-6 w-full max-w-3xl mx-4 shadow-2xl max-h-[90dvh] sm:max-h-[85dvh] flex flex-col" onClick={(e) => e.stopPropagation()}>
            <div className="pb-3 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-base font-semibold text-gray-800 truncate">{viewNote.title}</h3>
              <button onClick={() => setIsViewOpen(false)} className="text-gray-600 hover:text-gray-800" aria-label="Close view">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="text-xs text-gray-500 mt-2">Created {new Date(viewNote.created_at).toLocaleString()}</div>

            <div className="mt-3 mb-3 rounded-lg text-sm max-h-[45vh] overflow-y-auto border border-gray-200 bg-gray-50 p-3 whitespace-pre-wrap">
              {viewNote.combined_text || '(No content)'}
            </div>

            {viewNote.extra_text && (
              <div className="mb-3">
                <div className="text-sm font-medium text-gray-700 mb-1">Additional notes</div>
                <div className="rounded-lg text-sm max-h-[20vh] overflow-y-auto border border-gray-200 bg-gray-50 p-3 whitespace-pre-wrap">{viewNote.extra_text}</div>
              </div>
            )}

            {(() => {
              const raw: any = (viewNote as any).sources;
              const urls: string[] = [];
              const visit = (item: any) => {
                if (Array.isArray(item)) return item.forEach(visit);
                if (typeof item === 'string') { if (/^https?:\/\//i.test(item)) urls.push(item); return; }
                if (item && typeof item === 'object' && typeof item.url === 'string' && /^https?:\/\//i.test(item.url)) urls.push(item.url);
              };
              visit(raw);
              const unique = Array.from(new Set(urls));
              if (unique.length === 0) return null;
              return (
                <div>
                  <div className="text-sm font-medium text-gray-700 mb-1">Sources</div>
                  <div className="rounded-lg text-sm max-h-[18vh] overflow-y-auto border border-gray-200 bg-gray-50">
                    <ul className="space-y-1 p-2">
                      {unique.map((u) => (
                        <li key={u} className="flex items-center justify-between gap-3 rounded-md border border-gray-200 bg-white p-2">
                          <div className="text-xs text-gray-600 break-all flex-1">{u}</div>
                          <a href={u} target="_blank" rel="noopener noreferrer" className="text-blue-900 hover:text-blue-800 text-xs flex items-center gap-1" title="Visit">
                            Visit
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor"><path d="M12.293 2.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414L9.414 16H5a1 1 0 01-1-1v-4.414l8.293-8.293z"/><path d="M11 3l6 6"/></svg>
                          </a>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              );
            })()}

            <div className="pt-4 border-t border-gray-200 flex items-center justify-end gap-3 mt-3">
              <button onClick={() => setIsViewOpen(false)} className="px-4 py-2 bg-gray-100 text-gray-600 rounded-md hover:bg-gray-200">Close</button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

