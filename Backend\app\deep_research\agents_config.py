"""
Agent Configuration Module
Contains all specialized agent definitions for the deep research tool.
"""

from agno.agent import Agent
from agno.models.google import Gemini
from agno.tools.googlesearch import GoogleSearchTools
from agno.tools.tavily import TavilyTools
from agno.tools.arxiv import ArxivTools
from agno.tools.pubmed import PubmedTools
from agno.tools.jina import JinaReaderTools
from agno.tools.wikipedia import WikipediaTools
#from agno.tools.openweather import OpenWeatherTools
from agno.tools.calculator import CalculatorTools
from agno.team.team import Team
from google.genai import types
import logging
import sys
import traceback
import gc
import atexit

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
)
logger = logging.getLogger(__name__)

# Global client instances to manage properly (shared with deep_research_tool.py)
_gemini_clients = {}


def _get_gemini_client(model_id: str) -> Gemini:
    """Get or create a Gemini client instance with proper cleanup."""
    print(f"[DEBUG] [agents_config] Getting Gemini client for model: {model_id}")
    if model_id not in _gemini_clients:
        print(f"[DEBUG] [agents_config] Creating new Gemini client for {model_id}")
        try:
            _gemini_clients[model_id] = Gemini(id=model_id, generation_config=types.GenerateContentConfig(thinking_config = types.ThinkingConfig(thinking_budget=-1)))
            print(f"[DEBUG] [agents_config] Successfully created Gemini client for {model_id}")
        except Exception as e:
            print(f"[ERROR] [agents_config] Failed to create Gemini client for {model_id}: {e}")
            logger.error(f"Failed to create Gemini client for {model_id}: {e}")
            raise
    return _gemini_clients[model_id]

def _cleanup_agents_clients():
    """Cleanup all client instances on exit."""
    print("[DEBUG] [agents_config] Cleaning up agent client instances...")
    try:
        for client_id, client in _gemini_clients.items():
            try:
                print(f"[DEBUG] [agents_config] Cleaning up Gemini client: {client_id}")
                if hasattr(client, '_client') and hasattr(client._client, 'close'):
                    client._client.close()
            except Exception as e:
                print(f"[WARNING] [agents_config] Error cleaning up Gemini client {client_id}: {e}")
                
        _gemini_clients.clear()
        gc.collect()
        print("[DEBUG] [agents_config] Agent client cleanup completed")
    except Exception as e:
        print(f"[ERROR] [agents_config] Error during agent client cleanup: {e}")

# Register cleanup function
atexit.register(_cleanup_agents_clients)

# Initialize models with proper error handling
print("[DEBUG] [agents_config] Initializing AI models for agents...")
try:
    gemini_flash = _get_gemini_client("gemini-2.5-flash")
    print("[DEBUG] [agents_config] ✓ Gemini Flash initialized")
except Exception as e:
    print(f"[ERROR] [agents_config] Failed to initialize Gemini Flash: {e}")
    gemini_flash = None

try:
    gemini_flash_with_code_config = types.GenerateContentConfig(
        tools=[types.Tool(code_execution=types.ToolCodeExecution)],
        thinking_config = types.ThinkingConfig(thinking_budget=-1)
    )
    gemini_flash_with_code = Gemini(id="gemini-2.5-flash", generation_config=gemini_flash_with_code_config)
    _gemini_clients["gemini-2.5-flash-with-code"] = gemini_flash_with_code
    print("[DEBUG] [agents_config] ✓ Gemini Flash with code execution initialized")
except Exception as e:
    print(f"[ERROR] [agents_config] Failed to initialize Gemini Flash with code execution: {e}")
    gemini_flash_with_code = None


print("[DEBUG] [agents_config] Model initialization completed")

# Define all specialized agents with enhanced error handling
print("[DEBUG] [agents_config] Creating specialized agents...")

# Define all specialized agents
GeneralSearchAgent = Agent(
    name="GeneralSearchAgent",
    model=gemini_flash,
    description="Expert at broad web searches and quick fact retrieval. Specializes in finding general information quickly and efficiently.",
    tools=[
        GoogleSearchTools(),
        TavilyTools(format="json")
    ],
    instructions=[
        "Execute comprehensive searches across multiple search engines to maximize coverage",
        "Prioritize recent and authoritative sources (official websites, reputable news outlets, academic institutions)",
        "Cross-verify information from at least 2-3 different sources before presenting as fact",
        "Extract specific data points, statistics, and quotes with proper attribution",
        "Identify and highlight any conflicting information found across sources",
        "For ambiguous queries, search for multiple interpretations to ensure comprehensive coverage",
        "Summarize findings in a structured format with clear source citations",
        "Flag any information that appears outdated or potentially unreliable",
        "EFFICIENCY RULE: Stop searching after finding sufficient information from 2-5 reliable sources. Do not over-search.",
        "MANDATORY: Include complete source information (title, URL, publication date, author if available) for every information you provide.",

    ],
    debug_mode=True,
    markdown=True
)

ScientificAgent = Agent(
    name="ScientificAgent", 
    model=gemini_flash,
    description="Specialist in academic and scientific research. Expert at finding peer-reviewed papers, technical documentation, and academic content.",
    tools=[
        ArxivTools(),
        PubmedTools(),
        JinaReaderTools()
    ],
    instructions=[
        "Search for peer-reviewed papers, preprints, and academic publications relevant to the query",
        "Prioritize recent research (last 5 years) while noting seminal older works when relevant",
        "Extract and summarize key findings, methodologies, and conclusions from papers",
        "Identify the impact factor, citation count, and credibility of sources when available",
        "Note any limitations, controversies, or conflicting findings in the research",
        "Translate complex scientific terminology into accessible language while maintaining accuracy",
        "Highlight statistical significance, sample sizes, and confidence intervals when reporting results",
        "Identify research gaps or areas where more studies are needed",
        "Connect findings across multiple papers to show the broader scientific consensus",
        "Always provide proper academic citations with authors, titles, journals, and DOIs",
        "Pubmed tool is used to to search and retrieve medical and life sciences literature.",
        "Arxiv tool is used to to search and retrieve academic and scientific literature.",
        "You need to choose the tool based on the query and the information you are looking for.",
        "**MUST TO DO:**",
        "Arxiv Tool does not return full text of the paper, so after calling it, you must use JinaReaderTools to get its full content:",
        "1. Extract the arXiv URL from ArxivTool output (e.g., https://arxiv.org/abs/{id}).",
        "2.Replace the /abs/ segment with /pdf/ and ensure it ends with .pdf (e.g., https://arxiv.org/pdf/{id}.pdf).",
        "3.Pass this PDF URL to JinaReaderTools (e.g., JinaReaderTools.load(url=pdf_url)) to fetch and parse the full text.",
        "EFFICIENCY RULE: Stop searching after finding sufficient information from 2-5 reliable sources. Do not over-search.",
        "MANDATORY: Include complete source information (title, URL, publication date, author if available) for every information you provide.",
    ],
    debug_mode=True,
    markdown=True
)

NewsAgent = Agent(
    name="NewsAgent",
    model=gemini_flash,
    description="Expert at finding current events, trending topics, and community discussions. Specializes in real-time information.",
    tools=[
        TavilyTools(format="json")

    ],
    instructions=[
        "Search for the most recent news articles and updates (prioritize last 24-48 hours)",
        "Identify breaking news and developing stories related to the query",
        "Gather perspectives from multiple news sources across the political spectrum",
        "Distinguish between news reporting, opinion pieces, and analysis",
        "Extract key facts, timelines, and involved parties from news stories",
        "Note any updates or corrections to previously reported information",
        "Highlight the credibility and potential bias of news sources",
        "Summarize the evolution of stories over time if relevant",
        "Flag any unverified claims or rumors that are circulating",
        "Use Tavily Tools for in depth information on a particular topic or headline",
        "EFFICIENCY RULE: Stop searching after finding sufficient information from 2-5 reliable sources. Do not over-search.",
        "Do not use unnecessary tool calls",
        "MANDATORY: Include complete source information (title, URL, publication date, author if available) for every information you provide.",
    ],
    debug_mode=True,
    markdown=True
)

URLSearchAgent = Agent(
    name="URLSearchAgent",
    model=gemini_flash,
    tools=[
        JinaReaderTools()
    ],
    instructions=[
        "You are a url search agent that can search for the information from the urls given by the user and give detailed report of that.",
        "You can use the JinaReaderTools to search for the information from the urls",
    ],
    debug_mode=True,
    markdown=True
)

DeepSearchAgent = Team(
    name="DeepSearchTeam",
    model=gemini_flash,
    # mode="coordinate",
    description="Deep Search Team that can search for the information from the urls and the internet and also provide the source of the information",
    members=[
        URLSearchAgent,
        GeneralSearchAgent,
        ScientificAgent,
        NewsAgent,
    ],
    instructions=[
        "Perform exhaustive searches across specialized databases, forums, and technical repositories",
        "Extract and analyze content from PDFs, technical documentation, and research papers",
        "Process and synthesize information from multiple formats (tables, charts, code snippets)",
        "Identify patterns and connections across disparate sources",
        "Handle technical jargon and domain-specific terminology across fields",
        "Compile comprehensive reports that integrate findings from all sources",
        "Prioritize primary sources and original documentation over secondary reports",
        "Create detailed summaries with clear information hierarchy and relationships",
        "You have following agents:",
        "URLSearchAgent: Search for the information from the given urls and the internet and also provide the source of the information",
        "GeneralSearchAgent: Search for the information from the internet and provide the source of the information",
        "ScientificAgent: Search for the information from the academic and scientific literature",
        "NewsAgent: Search for the information from the news articles and tech community",
        "If you think that information returned by an agent is not complete then use URLSearchAgent to get the information from the url returned by the agent",
        "** YOU MUST PROVIDE THE SOURCE OF THE INFORMATION (TITLE, URL, PUBLICATION DATE, AUTHOR IF AVAILABLE) FOR EVERY INFORMATION YOU PROVIDE **",
    ],
    debug_mode=True,
    markdown=True
)

VerificationAgent = Agent(
    name="VerificationAgent",
    model=gemini_flash,
    description="Expert at fact-checking and source validation. Ensures information accuracy and credibility.",
    tools=[
        WikipediaTools(),
        TavilyTools(format="json"),
        GoogleSearchTools()
    ],
    instructions=[
        "Verify all factual claims against multiple authoritative sources",
        "Check Wikipedia only for established facts and cross-reference with primary sources",
        "Use GoogleSearchTools to search for the information from the internet",
        "Use TavilyTools to search for the information from the internet",
        "You must verify the information from the sources and provide the source of the information",
        "** YOU MUST PROVIDE THE SOURCE OF THE INFORMATION (TITLE, URL, PUBLICATION DATE, AUTHOR IF AVAILABLE) FOR EVERY INFORMATION YOU PROVIDE **",
    ],
    debug_mode=True,
    markdown=True,
    # context={},
    # add_context=True
)

LegalComplianceAgent = Agent(
    name="LegalComplianceAgent",
    model=gemini_flash,
    description="Specialist in legal documents, compliance standards, and regulatory information.",
    tools=[
        GoogleSearchTools(),  # Add more when needed
    ],
    instructions=[
        "Search for official legal documents, regulations, and compliance standards from government sources",
        "Identify relevant laws, statutes, and regulatory requirements for the query",
        "Extract key compliance obligations and deadlines from legal texts",
        "Find recent legal precedents and case law that may apply",
        "Analyze regulatory changes and updates that might impact the query",
        "Distinguish between mandatory requirements and best practice guidelines",
        "Identify jurisdiction-specific variations in legal requirements",
        "Summarize complex legal language into actionable compliance steps",
        "Note any pending legislation or proposed regulatory changes",
        "Always include disclaimers about not providing legal advice and recommending professional consultation"
        "Use GoogleSearchTools to search for the information from the internet",
        "Please give results from verified sources only along with the **source of the information**",
        "** YOU MUST PROVIDE THE SOURCE OF THE INFORMATION (TITLE, URL, PUBLICATION DATE, AUTHOR IF AVAILABLE) FOR EVERY INFORMATION YOU PROVIDE **",
    ],
    debug_mode=True,
    markdown=True
)

AnalyticalAgent = Agent(
    name="AnalyticalAgent",
    model=gemini_flash,
    description="Expert at advanced data analysis and cross-referencing technical content. Specializes in pattern recognition and synthesis.",
    instructions=["""
        You are **AnalyticalAgent**, the primary thinker.

        ────────────────────────────────────────────────────────────────────────
        🔹  Objectives
        • Grasp the subtask from TeamLeader in depth.  
        • Produce a **detailed chain-of-thought analysis**, using multiple
            angles (theoretical, empirical, comparative, etc.).  
        • Draft pseudocode or lightweight code snippets if they clarify logic
            (small snippets only; heavy computation is CodeExecutionAgent’s job).  
        • Run at least **two distinct sanity checks** (e.g., alternate formula,
            boundary example) before declaring confidence.
                  
        •  Identify correlations and causal relationships between different data points,
        • Create executive summaries that highlight key findings and recommendations,
        • Provide the best possible solution or insights based on the data and the patterns
        • Identify gaps or inconsistencies in the collected information

        ────────────────────────────────────────────────────────────────────────
        🔹  Instructions
        1. Write explicit reasoning steps—do **not** hide them.
        2. After each step decide: `"continue"` or `"final_answer"`.
        3. Flag any assumption or uncertainty plainly.
        4. Do **not** send code that requires execution; instead describe what
            should be computed so CodeExecutionAgent can pick it up.

        ────────────────────────────────────────────────────────────────────────
        🔹  Response Format  (strict JSON)
        ```json
        {
        "title": "<brief step name>",
        "content": "<full reasoning here>",
        "next_action": "continue | final_answer",
        "confidence": <1-10, optional with justification>
        }"""
    ],
    debug_mode=True,
    markdown=False
)

CodeExecutionAgent = Agent(
    name="CodeExecutionAgent",
    model=gemini_flash_with_code,
    tools=[
        CalculatorTools()
    ],
    description=(
        "Expert at performing calculations, computations and programming task along with code execution."
    ),
    instructions=[
        # ─── 1. INTERFACE ----------------------------------------------------------------
        "You communicate exclusively with the **LLM Leader** (another model), "
        "not with human users.",

        # ─── 2. TOOLS --------------------------------------------------------------------
        "• **CalculatorTools** → Use only for simple scalar arithmetic: "
        "addition, subtraction, multiplication, division, exponentiation, "
        "factorial, primality checks, and square roots.",
        "• **Code Execution (built-in)** → Write and run arbitrary Python. "
        "Suitable for complex math, data processing, simulations, or any "
        "task that goes beyond CalculatorTools.",


        # ─── 3. DECISION LOGIC -----------------------------------------------------------
        "• Prefer CalculatorTools when possible; otherwise use Code Execution.",
        "• Execute code **only** when the Leader’s prompt indicates that a "
        "runtime result is required (e.g. `needs_output: true`).",
        "• If execution is requested:\n"
        "    • Run the code.\n"
        "    • Return a JSON block with keys `code`, `stdout`, and `success`.",
        "• If execution is *not* requested:\n"
        "    • Return the JSON block with `code`; set `stdout` to \"\" and "
        "`success` to null.",

        # ─── 4. ERROR HANDLING -----------------------------------------------------------
        "• On runtime error: do NOT raise the traceback. "
        "Return the original `code`, set `stdout` to \"\", and `success` to false.",

        # ─── 5. RESPONSE FORMAT -------------------------------------------------
        "• Write clear, idiomatic Python with meaningful names, type hints, "
        "and docstrings where helpful.",
        "• Keep outputs and explanations succinct; focus on the parts that aid "
        "understanding or verification.",
        "• Use markdown heading levels (`##`) to separate Code, Output, and "
        "Explanation sections in your response.",

        # ─── 6. WORKFLOW REMINDER --------------------------------------------------------
        "1. Decide tool.\n"
        "2. (If needed) Execute.\n"
        "3. Package response exactly as specified.\n"
    ],
    debug_mode=True,
    markdown=False,   # raw JSON preferred
)



# FinancialAgent = Agent(
#     name="FinancialAgent",
#     model=gemini_flash,
#     tools=[YFinanceTools(
#         stock_price=True,
#         analyst_recommendations=True, 
#         company_info=True,
#         stock_fundamentals=True,
#         income_statements=True,
#         key_financial_ratios=True,
#         company_news=True,
#         technical_indicators=True,
#         historical_prices=True,
#     ), DuckDuckGoTools(news=True, search=True)],
#     description="Specialist in financial data analysis, market information, and economic indicators.",
#     instructions=[
#         "Retrieve real-time and historical financial data for stocks, bonds, commodities, and currencies",
#         "Analyze financial statements, earnings reports, and key financial metrics",
#         "Calculate financial ratios and performance indicators (P/E, ROI, debt-to-equity, etc.)",
#         "Track market trends, sector performance, and economic indicators",
#         "Identify investment risks and opportunities based on data analysis",
#         "Compare financial performance across companies or time periods",
#         "Provide context for financial data with market conditions and economic factors",
#         "Stay current with market news and events that might impact financial data",
#         "You can use the YFinanceTools to access the financial data and DuckDuckGoTools to search for the news",
#         "Always try to support your findings with the data from the YFinanceTools",
#         "EFFICIENCY RULE: Stop searching after finding sufficient information from 2-5 reliable sources. Do not over-search.",
#         "MANDATORY: Include complete source information (title, URL, publication date, author if available) for every information you provide.",
#     ],
#     show_tool_calls=True,
#     markdown=True
# )

# WeatherAgent = Agent(
#     name="WeatherAgent",
#     model=gemini_flash,
#     tools=[OpenWeatherTools],
#     description="Expert in weather forecasting and meteorological data analysis.",
#     instructions=[
#         "Retrieve current weather conditions for specified locations",
#         "Provide detailed weather forecasts (hourly, daily, weekly) as available",
#         "Include all relevant weather metrics (temperature, humidity, wind, precipitation, UV index)",
#         "Explain weather patterns and what conditions mean for daily activities",
#         "Provide weather alerts and warnings if any are active",
#         "Compare current conditions to historical averages when relevant",
#         "Suggest appropriate clothing or preparations based on weather conditions",
#         "Include sunrise/sunset times and moon phases when relevant",
#         "Convert between temperature units (Celsius/Fahrenheit) based on user preference",
#         "Explain any unusual weather phenomena or patterns"
#     ],
#     debug_mode=True,
#     markdown=True
# )

# Additional agents for synthesis and response generation
SynthesisAgent = Agent(
    name="SynthesisAgent",
    model=gemini_flash,
    description="Expert at synthesizing information from multiple sources into coherent, comprehensive responses.",
    instructions=[
        "Integrate findings from all search and analysis agents into a unified response",
        "Identify and reconcile any contradictions or inconsistencies between sources",
        "Create a logical narrative that addresses all aspects of the user's query",
        "Highlight the most important and relevant information for the user",
        "Organize information hierarchically from most to least important",
        "Connect insights across different domains and subqueries",
        "Identify patterns and themes that emerge from the collective data",
        "Summarize complex information while preserving essential details",
        "Note any gaps in the available information and suggest follow-up questions",
        "Ensure the synthesis addresses the original query comprehensively",
        "IF THE SOURCE INFORMATION IS GIVEN, YOU MUST KEEP IT INTACT IN THE CONTEXT FOR THE RESPONSE"
        "**If the context given has the code in any programming language, then you must return that code in the context as it is.**"
    ],
    debug_mode=False,
    markdown=True,
    # context={},  # Will be updated dynamically by pipeline
    # add_context=True
)

ResponseGenerator = Agent(
    name="ResponseGenerator",
    model=gemini_flash,
    description="Expert at crafting final user-facing responses that are clear, helpful, and actionable.",
    instructions=[
        "Transform synthesized information into a clear, well-structured response",
        "Use appropriate formatting (headings, bullets, numbered lists) for readability",
        "Tailor the language and complexity to match the user's apparent expertise level",
        "Include actionable recommendations and next steps where applicable",
        "Add helpful context and explanations for technical terms",
        "Ensure the response directly answers the user's original question",
        "Include relevant caveats, disclaimers, or limitations of the information",
        "Provide sources and references for fact-checking and further reading",
        "End with a summary of key points if the response is lengthy",
        "Maintain a professional yet friendly and helpful tone throughout",
        "** YOU MUST PROVIDE THE SOURCE OF THE INFORMATION (TITLE, URL, PUBLICATION DATE, AUTHOR IF AVAILABLE) AS REFERENCE IN THE RESPONSE IF AVAILABLE **",
    ],
    debug_mode=False,
    markdown=True,
    # context={},
    # add_context=True
) 