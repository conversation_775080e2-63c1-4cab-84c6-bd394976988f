"""
Stateless State Manager using Redis

Manages pipeline configurations and lightweight state in Redis instead of memory.
Ensures pipelines remain stateless while preserving necessary configuration data.
"""

import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import hashlib
from .connection_pools import connection_pools
from .. import config

logger = logging.getLogger(__name__)

class StatelessStateManager:
    """
    Manages pipeline state in Redis for stateless architecture.
    Stores configurations, cache lightweight data, and manage session state.
    """
    
    def __init__(self, redis_prefix: str):
        self.redis_prefix = redis_prefix
        self.default_ttl = config.CACHE_TTL_STATELESS_DEFAULT
        self.config_ttl = config.CACHE_TTL_STATELESS_CONFIG
        self.session_ttl = config.CACHE_TTL_STATELESS_SESSION
        
        logger.info("Stateless State Manager initialized")
    
    def _get_key(self, key_type: str, identifier: str) -> str:
        """Generate Redis key with prefix"""
        return f"{self.redis_prefix}:{key_type}:{identifier}"
    
    def _hash_config(self, config: Dict[str, Any]) -> str:
        """Generate hash for configuration to use as cache key"""
        # Sort config for consistent hashing
        config_str = json.dumps(config, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()[:16]
    
    async def store_pipeline_config(self, config: Dict[str, Any], 
                                  config_type: str = "pipeline") -> str:
        """
        Store pipeline configuration in Redis and return cache key.
        
        Args:
            config: Configuration dictionary
            config_type: Type of configuration (pipeline, ingestion, retrieval)
            
        Returns:
            Cache key for stored configuration
        """
        try:
            config_hash = self._hash_config(config)
            cache_key = self._get_key(f"config_{config_type}", config_hash)
            
            redis_client = await connection_pools.get_redis_connection()
            
            # Store config with TTL
            await redis_client.setex(
                cache_key,
                self.config_ttl,
                json.dumps(config)
            )
            
            logger.debug(f"Stored {config_type} config with key: {config_hash}")
            return config_hash
            
        except Exception as e:
            logger.error(f"Error storing pipeline config: {e}")
            raise
    
    async def get_pipeline_config(self, config_hash: str, 
                                config_type: str = "pipeline") -> Optional[Dict[str, Any]]:
        """
        Retrieve pipeline configuration from Redis.
        
        Args:
            config_hash: Configuration hash key
            config_type: Type of configuration
            
        Returns:
            Configuration dictionary or None if not found
        """
        try:
            cache_key = self._get_key(f"config_{config_type}", config_hash)
            redis_client = await connection_pools.get_redis_connection()
            
            config_data = await redis_client.get(cache_key)
            if config_data:
                config = json.loads(config_data)
                logger.debug(f"Retrieved {config_type} config: {config_hash}")
                return config
            
            logger.debug(f"Config not found: {config_hash}")
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving pipeline config: {e}")
            return None
    
    async def store_processing_session(self, session_id: str, 
                                     session_data: Dict[str, Any]) -> bool:
        """
        Store processing session data for tracking.
        
        Args:
            session_id: Unique session identifier
            session_data: Session information
            
        Returns:
            Success status
        """
        try:
            cache_key = self._get_key("session", session_id)
            redis_client = await connection_pools.get_redis_connection()
            
            # Add timestamp to session data
            session_data["created_at"] = datetime.utcnow().isoformat()
            session_data["status"] = "active"
            
            await redis_client.setex(
                cache_key,
                self.session_ttl,
                json.dumps(session_data)
            )
            
            logger.debug(f"Stored processing session: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing processing session: {e}")
            return False
    
    async def get_processing_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve processing session data.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session data or None if not found
        """
        try:
            cache_key = self._get_key("session", session_id)
            redis_client = await connection_pools.get_redis_connection()
            
            session_data = await redis_client.get(cache_key)
            if session_data:
                return json.loads(session_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving processing session: {e}")
            return None
    
    async def update_session_status(self, session_id: str, status: str, 
                                  extra_data: Dict[str, Any] = None) -> bool:
        """
        Update processing session status.
        
        Args:
            session_id: Session identifier
            status: New status (active, completed, failed)
            extra_data: Additional data to store
            
        Returns:
            Success status
        """
        try:
            cache_key = self._get_key("session", session_id)
            redis_client = await connection_pools.get_redis_connection()
            
            # Get existing session data
            session_data = await redis_client.get(cache_key)
            if session_data:
                session_dict = json.loads(session_data)
            else:
                session_dict = {}
            
            # Update status and timestamp
            session_dict["status"] = status
            session_dict["updated_at"] = datetime.utcnow().isoformat()
            
            if extra_data:
                session_dict.update(extra_data)
            
            # Store updated session
            await redis_client.setex(
                cache_key,
                self.session_ttl,
                json.dumps(session_dict)
            )
            
            logger.debug(f"Updated session {session_id} status to: {status}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating session status: {e}")
            return False
    
    async def store_temporary_data(self, data_key: str, data: Any, 
                                 ttl: int = None) -> bool:
        """
        Store temporary data with TTL.
        
        Args:
            data_key: Unique key for data
            data: Data to store
            ttl: Time to live in seconds
            
        Returns:
            Success status
        """
        try:
            cache_key = self._get_key("temp", data_key)
            redis_client = await connection_pools.get_redis_connection()
            
            ttl = ttl or self.default_ttl
            
            await redis_client.setex(
                cache_key,
                ttl,
                json.dumps(data) if isinstance(data, (dict, list)) else str(data)
            )
            
            logger.debug(f"Stored temporary data: {data_key} (TTL: {ttl}s)")
            return True
            
        except Exception as e:
            logger.error(f"Error storing temporary data: {e}")
            return False
    
    async def get_temporary_data(self, data_key: str) -> Any:
        """
        Retrieve temporary data.
        
        Args:
            data_key: Data key
            
        Returns:
            Stored data or None if not found
        """
        try:
            cache_key = self._get_key("temp", data_key)
            redis_client = await connection_pools.get_redis_connection()
            
            data = await redis_client.get(cache_key)
            if data:
                try:
                    return json.loads(data)
                except json.JSONDecodeError:
                    return data.decode() if isinstance(data, bytes) else data
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving temporary data: {e}")
            return None
    
    async def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions and return count of cleaned items.
        """
        try:
            redis_client = await connection_pools.get_redis_connection()
            pattern = self._get_key("session", "*")
            
            keys = await redis_client.keys(pattern)
            cleaned_count = 0
            
            for key in keys:
                session_data = await redis_client.get(key)
                if session_data:
                    try:
                        session = json.loads(session_data)
                        created_at = datetime.fromisoformat(session.get("created_at", ""))
                        
                        # Remove sessions older than 1 hour
                        if datetime.utcnow() - created_at > timedelta(hours=1):
                            await redis_client.delete(key)
                            cleaned_count += 1
                    except (json.JSONDecodeError, ValueError):
                        # Remove corrupted session data
                        await redis_client.delete(key)
                        cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} expired sessions")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {e}")
            return 0
    
    async def get_active_sessions(self) -> List[Dict[str, Any]]:
        """
        Get list of all active processing sessions.
        """
        try:
            redis_client = await connection_pools.get_redis_connection()
            pattern = self._get_key("session", "*")
            
            keys = await redis_client.keys(pattern)
            active_sessions = []
            
            for key in keys:
                session_data = await redis_client.get(key)
                if session_data:
                    try:
                        session = json.loads(session_data)
                        if session.get("status") == "active":
                            session["session_id"] = key.split(":")[-1]
                            active_sessions.append(session)
                    except json.JSONDecodeError:
                        continue
            
            return active_sessions
            
        except Exception as e:
            logger.error(f"Error getting active sessions: {e}")
            return []
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about stored state.
        """
        try:
            redis_client = await connection_pools.get_redis_connection()
            
            # Count different types of keys
            config_keys = await redis_client.keys(self._get_key("config_*", "*"))
            session_keys = await redis_client.keys(self._get_key("session", "*"))
            temp_keys = await redis_client.keys(self._get_key("temp", "*"))
            
            return {
                "config_count": len(config_keys),
                "active_sessions": len(session_keys),
                "temporary_data": len(temp_keys),
                "default_ttl": self.default_ttl,
                "config_ttl": self.config_ttl,
                "session_ttl": self.session_ttl
            }
            
        except Exception as e:
            logger.error(f"Error getting state stats: {e}")
            return {}

# Global state manager instance
stateless_state = StatelessStateManager(redis_prefix=config.CACHE_PREFIX_STATELESS)
