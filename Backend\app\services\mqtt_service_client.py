"""
MQTT Service Client

HTTP client for communicating with the standalone MQTT service.
This module provides an interface that mimics the original MQTT client
but uses HTTP requests to communicate with the separate MQTT microservice.

The client handles:
- Publishing events to individual users
- Publishing events to groups (with optional user exclusion)
- Health checking the MQTT service
- Error handling and timeouts for reliable communication

This enables the main API to send real-time notifications without direct
MQTT broker connections, maintaining clean microservice separation.
"""

import asyncio
import logging
from typing import Dict, Any, Optional
import httpx
from .. import config

logger = logging.getLogger(__name__)


class MQTTServiceClient:
    """
    HTTP client for the standalone MQTT service.

    This class provides an interface that mimics direct MQTT publishing
    but uses HTTP requests to communicate with the separate MQTT microservice.
    It handles connection management, error recovery, and maintains the same
    API as the original MQTT client for seamless integration.

    The client is designed for reliability with timeout handling and
    graceful error recovery, ensuring the main API remains responsive
    even if the MQTT service is temporarily unavailable.
    """


    def __init__(self, mqtt_service_url: str = "http://127.0.0.1:8002"):
        """
        Initialize the MQTT service client.

        Args:
            mqtt_service_url: Base URL of the MQTT service (default: http://127.0.0.1:8002)
        """
        # Clean and store the base URL, removing trailing slashes
        self.mqtt_service_url = mqtt_service_url.rstrip('/')

        # Construct endpoint URLs for different MQTT service operations
        self.publish_event_url = f"{self.mqtt_service_url}/api/v1/mqtt/publish-event"
        self.publish_group_event_url = f"{self.mqtt_service_url}/api/v1/mqtt/publish-group-event"
        self.status_url = f"{self.mqtt_service_url}/api/v1/mqtt/status"

        # Configure HTTP timeout for all requests (5 seconds for reliability)
        self.timeout = httpx.Timeout(5.0)  
    
    async def publish_event(self, user_id: int, event_data: Dict[str, Any]) -> bool:
        """
        Publish an event to a specific user via the MQTT service.

        This method sends an HTTP POST request to the MQTT service's publish-event endpoint,
        which then handles the actual MQTT publishing to the user. This enables real-time
        notifications without the main API having direct MQTT broker access.

        Args:
            user_id: The target user ID to receive the event
            event_data: Dictionary containing the event payload (type, message, etc.)

        Returns:
            bool: True if the event was successfully queued, False otherwise

        Note:
            Returns True if the HTTP request succeeds (event queued), not if MQTT delivery succeeds.
            The MQTT service handles the actual event delivery asynchronously.
        """
        try:
            # Use httpx async client with configured timeout for reliability
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    self.publish_event_url,
                    json={
                        "user_id": user_id,
                        "event_data": event_data
                    }
                )

                # Success: HTTP 200 means event was queued successfully
                if response.status_code == 200:
                    return True
                else:
                    # Log non-200 responses but don't fail hard - service might recover
                    logger.warning(f"MQTT service returned {response.status_code} for user {user_id}")
                    return False

        except httpx.TimeoutException:
            # MQTT service is slow/unresponsive - log and return failure
            logger.error(f"Timeout publishing event to user {user_id}")
            return False
        except httpx.ConnectError:
            # Cannot reach MQTT service - service might be down
            logger.error(f"Failed to connect to MQTT service for user {user_id}")
            return False
        except Exception as e:
            # Unexpected error - log for debugging
            logger.error(f"Unexpected error publishing event to user {user_id}: {e}")
            return False
    
    async def publish_to_group(self, group_id: int, event_data: Dict[str, Any], exclude_user_id: Optional[int] = None) -> bool:
        """
        Publish an event to all members of a group via the MQTT service.

        This method broadcasts an event to multiple users by sending an HTTP POST request
        to the MQTT service's group publish endpoint. The MQTT service then queries the
        database for group members and publishes to each user (optionally excluding one).

        Args:
            group_id: The target group ID for broadcasting
            event_data: Dictionary containing the event payload to send to all members
            exclude_user_id: Optional user ID to exclude from receiving the event
                           (typically the sender to avoid echo notifications)

        Returns:
            bool: True if the event was successfully queued for group broadcast, False otherwise

        Note:
            The MQTT service handles the database query to find group members and
            individual event publishing asynchronously after the HTTP response.
        """
        try:
            # Build request payload with required group_id and event_data
            payload = {
                "group_id": group_id,
                "event_data": event_data
            }

            # Optionally exclude a user (e.g., sender shouldn't receive their own message)
            if exclude_user_id is not None:
                payload["exclude_user_id"] = exclude_user_id

            # Send HTTP request to MQTT service for group broadcasting
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    self.publish_group_event_url,
                    json=payload
                )

                if response.status_code == 200:
                    return True
                else:
                    logger.warning(f"MQTT service returned {response.status_code} for group {group_id}")
                    return False

        except httpx.TimeoutException:
            logger.error(f"Timeout publishing event to group {group_id}")
            return False
        except httpx.ConnectError:
            logger.error(f"Failed to connect to MQTT service for group {group_id}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error publishing event to group {group_id}: {e}")
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check the health and status of the MQTT service.

        This method queries the MQTT service's status endpoint to determine if the service
        is running, initialized, and connected to the MQTT broker. It's used for monitoring
        and determining service availability before attempting to publish events.

        Returns:
            Dict[str, Any]: Status information containing:
                - initialized: bool - Whether the MQTT service has been properly initialized
                - connected: bool - Whether the service is connected to the MQTT broker
                - client_id: str - The MQTT client identifier (if available)
                - message: str - Human-readable status message
                - error: str - Error description (only present if there was an error)

        Note:
            This method returns a normalized status dict regardless of whether the MQTT
            service is reachable or returns an error. This ensures consistent error handling.
        """
        try:
            # Query the MQTT service's detailed status endpoint
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(self.status_url)

                if response.status_code == 200:
                    # Service is responding - return the actual status data
                    return response.json()
                else:
                    # Service responded but with error status
                    return {
                        "initialized": False,
                        "connected": False,
                        "error": f"HTTP {response.status_code}"
                    }

        except httpx.TimeoutException:
            # MQTT service is not responding within timeout
            return {
                "initialized": False,
                "connected": False,
                "error": "Timeout"
            }
        except httpx.ConnectError:
            # Cannot establish connection to MQTT service
            return {
                "initialized": False,
                "connected": False,
                "error": "Connection failed"
            }
        except Exception as e:
            # Unexpected error during health check
            return {
                "initialized": False,
                "connected": False,
                "error": str(e)
            }
    
# Global instance for easy import and use throughout the application
# This maintains backward compatibility with existing code that imports mqtt_client
mqtt_client = MQTTServiceClient()
