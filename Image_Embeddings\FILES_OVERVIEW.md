# Files Overview - Image Embedding Service

## Core Service Files

### `image_embedding_service.py`
Main FastAPI service implementation with SigLIP-2 model integration.

**Key Components:**
- `ImageEmbeddingService` class - Core service logic
- FastAPI endpoints for embeddings and captions
- GPU acceleration support
- Batch processing
- Health checks and monitoring

**Endpoints:**
- `GET /` - Service info
- `GET /health` - Health check
- `POST /embed` - Generate image embeddings
- `POST /caption` - Generate image captions
- `POST /embed-text` - Generate text embeddings
- `POST /upload-embed` - Upload and embed images
- `GET /model-info` - Model information

### `config.py`
Configuration management using environment variables and dataclasses.

**Configuration Options:**
- `PORT` - Service port (default: 8019)
- `HOST` - Service host (default: 0.0.0.0)
- `MODEL_NAME` - SigLIP model variant
- `DEVICE` - cuda/cpu
- `BATCH_SIZE` - Batch processing size
- `MAX_CAPTION_LENGTH` - Maximum caption length
- `CACHE_DIR` - Model cache directory
- `LOG_LEVEL` - Logging level

### `client.py`
Python client library for easy integration with the service.

**Key Methods:**
- `health_check()` - Check service health
- `embed_images()` - Generate embeddings from base64 images
- `embed_image_files()` - Generate embeddings from file paths
- `caption_images()` - Generate captions from base64 images
- `caption_image_files()` - Generate captions from file paths
- `embed_text()` - Generate text embeddings
- `upload_and_embed()` - Upload files and generate embeddings
- `get_model_info()` - Get model information

**Usage Example:**
```python
from client import ImageEmbeddingClient

client = ImageEmbeddingClient("http://localhost:8019")
result = client.embed_image_files(["image.jpg"])
embeddings = result['embeddings']
```

## Docker Files

### `Dockerfile`
Multi-stage Docker build with CUDA support.

**Features:**
- Based on `nvidia/cuda:12.1.0-cudnn8-runtime-ubuntu22.04`
- Python 3.11
- UV package manager for fast installs
- Model caching support
- Health checks
- Optimized for GPU

**Build:**
```bash
docker build -t image-embedding-service .
```

### `docker-compose.yml`
Docker Compose configuration for easy deployment.

**Features:**
- GPU resource allocation
- Volume mounts for cache and logs
- Environment configuration
- Health checks
- Auto-restart policy

**Usage:**
```bash
docker-compose up -d --build
```

### `.dockerignore`
Files to exclude from Docker build context.

## Configuration Files

### `requirements.txt`
Python dependencies for the service.

**Key Dependencies:**
- `fastapi` - Web framework
- `uvicorn` - ASGI server
- `torch` - PyTorch for GPU acceleration
- `transformers` - HuggingFace models
- `pillow` - Image processing
- `pydantic` - Data validation

### `env_example.txt`
Example environment configuration file.

**Usage:**
```bash
cp env_example.txt .env
# Edit .env with your configuration
```

## Testing Files

### `test_service.py`
Comprehensive test suite for the service.

**Test Coverage:**
- Health check endpoint
- Model info endpoint
- Image embedding generation
- Image caption generation
- Text embedding generation
- Batch processing
- Performance benchmarks

**Usage:**
```bash
python test_service.py
```

**Expected Output:**
```
=== Testing Health Endpoint ===
Status Code: 200
Response: {'status': 'healthy', ...}
...
Total: 6/6 tests passed
```

## Documentation Files

### `README.md`
Main documentation with:
- Quick start guide
- API reference
- Configuration options
- Usage examples
- Troubleshooting
- Performance benchmarks

### `DEPLOYMENT.md`
Production deployment guide with:
- GPU setup instructions
- Docker deployment
- Kubernetes manifests
- Monitoring setup
- Security best practices
- Scaling strategies
- Cost optimization

### `FILES_OVERVIEW.md` (this file)
Overview of all files in the project.

## Utility Files

### `__init__.py`
Package initialization file.

Exports:
- `ImageEmbeddingClient` - Main client class

### `.gitignore`
Git ignore patterns for:
- Python cache files
- Virtual environments
- Model cache
- Logs
- IDE files

## Directory Structure

```
Image_Embeddings/
├── __init__.py                    # Package init
├── image_embedding_service.py     # Main service
├── config.py                      # Configuration
├── client.py                      # Python client
├── requirements.txt               # Dependencies
├── Dockerfile                     # Docker build
├── docker-compose.yml             # Docker Compose config
├── .dockerignore                  # Docker ignore patterns
├── .gitignore                     # Git ignore patterns
├── env_example.txt                # Example environment config
├── test_service.py                # Test suite
├── README.md                      # Main documentation
├── DEPLOYMENT.md                  # Deployment guide
└── FILES_OVERVIEW.md             # This file
```

## Volumes and Data

### `model_cache/` (created at runtime)
Directory for caching downloaded models.

**Purpose:**
- Avoid re-downloading models
- Faster startup after first run
- Persistent across container restarts

**Size:** ~2-5 GB depending on model

### `logs/` (optional)
Directory for application logs.

**Contents:**
- Service logs
- Error logs
- Access logs

## Environment Variables

### Required
- None (all have defaults)

### Optional
- `PORT` - Service port
- `HOST` - Bind address
- `MODEL_NAME` - Model to use
- `DEVICE` - cuda or cpu
- `BATCH_SIZE` - Batch size
- `MAX_CAPTION_LENGTH` - Max caption length
- `CACHE_DIR` - Cache directory
- `LOG_LEVEL` - Logging level

## Integration Points

### With Data Ingestion Service

```python
# In Data_Ingestion_Service/pipeline.py
from Image_Embeddings.client import ImageEmbeddingClient

class DocumentProcessor:
    def __init__(self, config):
        self.image_client = ImageEmbeddingClient("http://localhost:8019")
    
    def process_images(self, images_base64):
        # Get embeddings
        result = self.image_client.embed_images(images_base64)
        embeddings = result['embeddings']
        
        # Get captions
        captions_result = self.image_client.caption_images(images_base64)
        captions = captions_result['captions']
        
        return embeddings, captions
```

### With Qdrant

```python
# Store image embeddings in Qdrant
from qdrant_client.models import PointStruct

points = [
    PointStruct(
        id=str(uuid.uuid4()),
        vector={"image": embedding},
        payload={
            "type": "image",
            "caption": caption,
            "source": source_file
        }
    )
    for embedding, caption in zip(embeddings, captions)
]

qdrant_client.upsert(
    collection_name=collection_name,
    points=points
)
```

## Performance Characteristics

### Embedding Generation
- **GPU (RTX 3090):** ~50-100ms per image
- **CPU:** ~300-500ms per image
- **Batch (8 images):** ~400ms total (~50ms per image)

### Caption Generation
- **GPU:** ~800ms-1.5s per image
- **CPU:** ~2-3s per image

### Memory Usage
- **GPU Memory:** ~4-6 GB
- **RAM:** ~2-4 GB
- **Model Size:** ~1.5-2 GB

### Throughput
- **Embeddings:** 80-100 images/sec (GPU, batched)
- **Captions:** 5-10 images/sec (GPU)

## Troubleshooting

### Common Issues

1. **GPU not detected**
   - Check: `nvidia-smi`
   - Verify Docker GPU access
   - Install NVIDIA Container Toolkit

2. **Out of memory**
   - Reduce `BATCH_SIZE`
   - Use smaller model
   - Monitor with `nvidia-smi`

3. **Slow performance**
   - Verify GPU is being used
   - Increase batch size
   - Check GPU utilization

4. **Model download fails**
   - Check internet connection
   - Verify HuggingFace access
   - Use mirror if needed

## Future Enhancements

### Planned Features
- [ ] Model quantization for faster inference
- [ ] Multiple model support
- [ ] Caching layer (Redis)
- [ ] Prometheus metrics
- [ ] Rate limiting
- [ ] API authentication
- [ ] Batch endpoint optimization
- [ ] WebSocket support for streaming
- [ ] Model A/B testing
- [ ] Auto-scaling support

### Optimization Ideas
- Use TorchScript for faster inference
- Implement model ensemble
- Add result caching
- Use mixed precision (FP16)
- Implement request queuing
- Add load balancing

## Version History

### v1.0.0 (Current)
- Initial release
- SigLIP-2 integration
- GPU acceleration
- Batch processing
- REST API
- Docker support
- Comprehensive documentation

## License

This service uses open-source models and libraries:
- SigLIP-2: Google Research
- PyTorch: BSD License
- FastAPI: MIT License
- Transformers: Apache 2.0

## Contributing

To contribute:
1. Fork the repository
2. Create a feature branch
3. Make changes
4. Add tests
5. Submit pull request

## Support

For issues:
1. Check README.md
2. Review DEPLOYMENT.md
3. Run test_service.py
4. Check logs
5. Verify GPU setup

