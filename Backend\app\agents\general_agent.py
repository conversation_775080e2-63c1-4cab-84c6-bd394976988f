from .base import BaseAgent
import logging
from agno.tools.googlesearch import GoogleSearchTools
from agno.tools.tavily import TavilyTools
from agno.agent import Agent
from agno.models.google import Gemini
from ..config import GEMINI_API_KEY

# Configure module-level logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GeneralAgent(BaseAgent):
    """A single universal agent that can answer questions using web search, document
    search, and deep-research capabilities."""

    def __init__(self,
                 use_web_search: bool = False,
                 use_document_search: bool = False,
                 use_deep_search: bool = False):
        # Generic instructions useful for all kinds of corporate queries
        base_instructions = [
            "You are a helpful and knowledgeable Workspace assistant for a company.",
            "Answer questions about company policies, benefits, leave, HR procedures, IT issues, Finance issues, Legal issues, etc.",
            "Be concise, accurate, and professional in your responses.",
            "If you don't know the answer, acknowledge that and suggest contacting the relevant department.",
            "Maintain confidentiality and privacy in all interactions.",
            "Format your responses in a clear, structured manner.",
            "Always provide actionable next steps when applicable.",
        ]

        description = (
            "General purpose AI assistant capable of leveraging internal knowledge "
            "bases as well as external web/deep-research tools to answer a broad "
        )

        super().__init__(
            name="WorkplaceSLM",
            description=description,
            instructions=base_instructions,
            collection_name=None,
            use_web_search=use_web_search,
            use_document_search=use_document_search,
            use_deep_search=use_deep_search,
            tools=None,
        )

        # For compatibility with code that expects a department attribute
        self.department: str = "general"

        # Immediately configure tools and instructions based on the initial flags
        self.update_config(
            use_web_search=use_web_search,
            use_document_search=use_document_search,
            use_deep_search=use_deep_search,
            tools=None,
            department=self.department,
            allowed_files=None,
        )

        logger.info("GeneralAgent initialised (web_search=%s, document_search=%s, deep_search=%s)",
                    use_web_search, use_document_search, use_deep_search)

    def update_config(self, use_web_search=None, use_document_search=None, use_deep_search=None,
                      collection_name=None, db_path=None, tools=None,
                      department=None, allowed_files=None):
        """Update General agent configuration, injecting web-search behaviour when enabled."""
        # Mirror the pattern used by other specialised agents
        # Import inside method to avoid circulars (already imported at top, but safe)

        # Apply incoming flags while preserving existing defaults
        if use_web_search is not None:
            self.use_web_search = use_web_search
        if use_document_search is not None:
            self.use_document_search = use_document_search
        if use_deep_search is not None:
            self.use_deep_search = use_deep_search
        if collection_name is not None:
            self.collection_name = collection_name
        if db_path is not None:
            self.db_path = db_path
        if tools is not None:
            self.user_tools = tools
        if department is not None:
            self.department = department
        if allowed_files is not None:
            self.allowed_files = allowed_files

        # Build a fresh instruction list each time we reconfigure
        general_instructions = [
            "You are a helpful and knowledgeable Workspace assistant for a company.",            
            "Be concise, accurate, and professional in your responses.",
            "If you don't know the answer, acknowledge that and suggest contacting the relevant department.",
            "Maintain confidentiality and privacy in all interactions.",
            "Format your responses in a clear, structured manner.",
            "Always provide actionable next steps when applicable.",
        ]

        # Inject web-search guidance when enabled
        if self.use_web_search:
            general_instructions.extend([
                "ALWAYS perform a web search for EVERY query when web search is enabled.",
                "You MUST search the web before answering any question when web search is enabled.",
                "First search for up-to-date information, then provide your response.",
                "Always verify information from web searches and cite sources when appropriate.",
                "Use the following tools for web search: GoogleSearchTools, TavilyTools.",
                "YOU MUST PROVIDE THE SOURCE OF THE INFORMATION (TITLE, URL, PUBLICATION DATE, AUTHOR IF AVAILABLE) AS REFERENCE IN THE RESPONSE IF AVAILABLE.",
            ])

        # Update the instance instructions
        self.instructions = general_instructions

        # Clear previous tool results whenever we change configuration
        self.tool_results_dict = {}

        # Re-initialise the underlying Agno Agent depending on capability flags
        if self.use_web_search:
            # Assign default tools if the caller hasn't provided any
            if tools is not None:
                self.tools = tools.copy()
                self.user_tools = self.tools.copy()
            else:
                self.tools = [
                    GoogleSearchTools(),
                    TavilyTools(format="json"),
                ]
                self.user_tools = self.tools.copy()

            self.agent = Agent(
                name=self.name,
                model=Gemini(id="gemini-2.5-flash", api_key=GEMINI_API_KEY),
                description=self.description,
                instructions=self.instructions,
                tools=self.tools,
                debug_mode=True,
                markdown=True,
                tool_hooks=[self.tool_execution_hook],
            )
            logger.info("GeneralAgent updated with web-search tools and instructions")

        elif self.use_document_search or self.use_deep_search:
            # Delegate to BaseAgent logic for these modes (no local re-init needed here)
            super().update_config(
                use_web_search=self.use_web_search,
                use_document_search=self.use_document_search,
                use_deep_search=self.use_deep_search,
                collection_name=self.collection_name,
                db_path=self.db_path,
                tools=self.user_tools,
                department=self.department,
                allowed_files=getattr(self, "allowed_files", None),
            )

        else:
            # Basic conversational model without external search
            self.agent = Agent(
                name=self.name,
                model=Gemini(id="gemini-2.5-flash-lite", api_key=GEMINI_API_KEY),
                description=self.description,
                instructions=self.instructions,
                tools=[],
                debug_mode=True,
                markdown=True,
            )
            logger.info("GeneralAgent configured without web search or RAG capabilities") 
