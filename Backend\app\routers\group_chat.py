import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from sqlalchemy import select, desc, delete, update, func
from typing import List
from datetime import datetime
import json

from ..database import get_db
from ..models.user import User
from ..models.group_chat import Group, GroupMember, GroupMessage, MemberRole
from ..models.deleted_message import DeletedGroupMessage
from ..models.space_membership import SpaceMembership, SpaceRole
from ..schemas.group_chat import (
    GroupCreate, GroupUpdate, GroupResponse,
    GroupMessageCreate, GroupMessageResponse,
    GroupMembersAdd
)
from ..auth import get_current_user
from ..services.mqtt_service_client import mqtt_client
from ..services.redis_service import redis_service, cached, invalidate_cache, invalidate_group_cache, invalidate_user_cache
from .. import config

router = APIRouter(prefix="/chat/groups", tags=["Chat-Groups"])

logger = logging.getLogger(__name__)

# -----------------------------
# Groups CRUD
# -----------------------------

def _user_groups_cache_key(*args, **kwargs):
    """Deterministic, user-scoped cache key for the list_groups endpoint.

    The key encodes pagination and the caller's user id so that targeted
    invalidation in invalidate_user_cache() reliably clears the right keys.
    """
    # Extract values regardless of how FastAPI passed them through
    skip = kwargs.get('skip') if 'skip' in kwargs else (args[0] if len(args) > 0 else 0)
    limit = kwargs.get('limit') if 'limit' in kwargs else (args[1] if len(args) > 1 else 50)
    current_user = kwargs.get('current_user') if 'current_user' in kwargs else (args[2] if len(args) > 2 else None)
    user_id = getattr(current_user, 'id', 0)
    return f"{config.CACHE_PREFIX_GROUP}:user_groups:skip:{skip}:limit:{limit}:current_user:{user_id}"

@router.post("/", response_model=GroupResponse, status_code=status.HTTP_201_CREATED)
@invalidate_cache(f"{config.CACHE_PREFIX_GROUP}:user_groups:*")
async def create_group(
    group_data: GroupCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    # Create group
    new_group = Group(
        name=group_data.name,
        creator_id=current_user.id,
        space_id=group_data.space_id
    )
    db.add(new_group)
    await db.flush()  # get group id

    # Add creator as admin
    creator_member = GroupMember(
        group_id=new_group.id,
        user_id=current_user.id,
        role=MemberRole.admin
    )
    db.add(creator_member)

    # Add other members
    for uid in set(group_data.member_ids):
        if uid == current_user.id:
            continue
        result = await db.execute(
            select(User).where(User.id == uid, User.is_active == True)
        )
        member = result.scalars().first()
        if member:
            db.add(GroupMember(group_id=new_group.id, user_id=uid, role=MemberRole.member))

    await db.commit()
    await db.refresh(new_group)

    # Load with relations
    result = await db.execute(
        select(Group).options(joinedload(Group.members).joinedload(GroupMember.user)).where(Group.id == new_group.id)
    )
    group = result.unique().scalars().first()

    # Notify members via MQTT
    for m in group.members:
        if m.user_id != current_user.id:
            await mqtt_client.publish_event(
                user_id=m.user_id,
                event_data={
                    "type": "group_invite",
                    "group": group.to_dict(include_members=True),
                    "adder_name": current_user.username
                }
            )

    return group.to_dict(include_members=True)

@router.get("/", response_model=List[GroupResponse])
@cached(
    prefix=f"{config.CACHE_PREFIX_GROUP}:user_groups",
    ttl=config.CACHE_TTL_MEDIUM,
    key_builder=_user_groups_cache_key
)
async def list_groups(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    result = await db.execute(
        select(Group).join(GroupMember).where(
            GroupMember.user_id == current_user.id,
            Group.is_active == True
        ).options(
            joinedload(Group.members).joinedload(GroupMember.user)
        ).order_by(desc(Group.updated_at)).offset(skip).limit(limit)
    )
    groups = result.unique().scalars().all()
    return [g.to_dict(include_members=True) for g in groups]

@router.put("/{group_id}", response_model=GroupResponse)
async def update_group(
    group_id: int,
    updates: GroupUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    result = await db.execute(
        select(Group).options(joinedload(Group.members)).where(Group.id == group_id, Group.is_active == True)
    )
    group = result.unique().scalars().first()
    if not group:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Group not found")

    # Only admins can update
    result = await db.execute(select(GroupMember).where(GroupMember.group_id == group_id, GroupMember.user_id == current_user.id))
    member_rec = result.scalars().first()
    if not member_rec or member_rec.role != MemberRole.admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized")

    if updates.name:
        group.name = updates.name
        group.updated_at = datetime.utcnow()

    await db.commit()
    await db.refresh(group)
    
    # Invalidate group cache
    await invalidate_group_cache(group_id)
    # Invalidate cached group lists for all members
    for m in group.members:
        await invalidate_user_cache(m.user_id)
    # Broadcast group update to all members for realtime UI sync
    try:
        for m in group.members:
            await mqtt_client.publish_event(m.user_id, {
                "type": "group_update",
                "group": group.to_dict(include_members=True)
            })
    except Exception:
        pass
    return group.to_dict(include_members=True)

@router.delete("/{group_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_group(
    group_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    result = await db.execute(
        select(Group).options(joinedload(Group.members)).where(Group.id == group_id, Group.is_active == True)
    )
    group = result.unique().scalars().first()
    if not group:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Group not found")

    result = await db.execute(select(GroupMember).where(GroupMember.group_id == group_id, GroupMember.user_id == current_user.id))
    member_rec = result.scalars().first()
    if not member_rec or member_rec.role != MemberRole.admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized")

    # Capture members before deactivation
    member_ids = [m.user_id for m in group.members]
    group.is_active = False
    group.updated_at = datetime.utcnow()
    await db.commit()

    # Invalidate group and member caches
    await invalidate_group_cache(group_id)
    for uid in member_ids:
        await invalidate_user_cache(uid)

    # Notify members that group was deleted
    try:
        for uid in member_ids:
            await mqtt_client.publish_event(uid, {
                "type": "group_deleted",
                "group_id": group_id
            })
    except Exception:
        pass
    return

# -----------------------------
# Group Messages
# -----------------------------

def _group_messages_cache_key(*args, **kwargs):
    try:
        gid = kwargs.get('group_id') if 'group_id' in kwargs else args[0]
        skip = kwargs.get('skip') if 'skip' in kwargs else args[1]
        limit = kwargs.get('limit') if 'limit' in kwargs else args[2]
        current_user = kwargs.get('current_user') if 'current_user' in kwargs else args[3]
        uid = getattr(current_user, 'id', 0)
        return f"{config.CACHE_PREFIX_GROUP}:messages:group:{gid}:user:{uid}:skip:{skip}:limit:{limit}"
    except Exception:
        # Fallback minimal key (ensures cache but may over-invalidate)
        return f"{config.CACHE_PREFIX_GROUP}:messages:fallback"

@router.get("/{group_id}/messages", response_model=List[GroupMessageResponse])
@cached(
    prefix=f"{config.CACHE_PREFIX_GROUP}:messages",
    ttl=config.CACHE_TTL_CHAT_MESSAGES,
    key_builder=_group_messages_cache_key
)
async def get_group_messages(
    group_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    # Verify membership
    result = await db.execute(select(GroupMember).where(GroupMember.group_id == group_id, GroupMember.user_id == current_user.id))
    membership = result.scalars().first()
    if not membership:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not a group member")

    result = await db.execute(
        select(GroupMessage)
        .options(joinedload(GroupMessage.sender))
        .outerjoin(DeletedGroupMessage, (
            (DeletedGroupMessage.group_message_id == GroupMessage.id) & (DeletedGroupMessage.user_id == current_user.id)
        ))
        .where(
            GroupMessage.group_id == group_id,
            DeletedGroupMessage.id == None  # noqa: E711 – intentional
        )
        .order_by(desc(GroupMessage.created_at))
        .offset(skip)
        .limit(limit)
    )
    messages = result.scalars().all()

    messages.reverse()

    return [m.to_dict() for m in messages]

@router.post("/{group_id}/messages", response_model=GroupMessageResponse, status_code=status.HTTP_201_CREATED)
async def send_group_message(
    group_id: int,
    message_data: GroupMessageCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    # Verify membership
    result = await db.execute(select(GroupMember).where(GroupMember.group_id == group_id, GroupMember.user_id == current_user.id))
    membership = result.scalars().first()
    if not membership:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not a group member")

    new_msg = GroupMessage(
        group_id=group_id,
        sender_id=current_user.id,
        content=message_data.content,
        message_type=message_data.message_type
    )
    db.add(new_msg)

    # update group updated_at
    result = await db.execute(
        select(Group).options(joinedload(Group.members)).where(Group.id == group_id)
    )
    group = result.unique().scalars().first()
    group.updated_at = datetime.utcnow()
    await db.commit()
    await db.refresh(new_msg)

    response = new_msg.to_dict()

    # Invalidate group cache
    await invalidate_group_cache(group_id)
    
    # Broadcast via MQTT to other members
    notify = {
        "type": "group_new_message",
        "group_id": group_id,
        "message": response
    }
    for m in group.members:
        if m.user_id != current_user.id:
            await mqtt_client.publish_event(m.user_id, notify)

    return response 

# Add members
@router.post("/{group_id}/members", response_model=GroupResponse)
async def add_group_members(
    group_id: int,
    members: GroupMembersAdd,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    result = await db.execute(
        select(Group).options(joinedload(Group.members)).where(Group.id == group_id, Group.is_active == True)
    )
    group = result.unique().scalars().first()
    if not group:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Group not found")
    result = await db.execute(select(GroupMember).where(GroupMember.group_id == group_id, GroupMember.user_id == current_user.id))
    admin_rec = result.scalars().first()
    if not admin_rec or admin_rec.role != MemberRole.admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized")
    
    for uid in members.member_ids:
        result = await db.execute(select(GroupMember).where(GroupMember.group_id == group_id, GroupMember.user_id == uid))
        exists = result.scalars().first()
        if not exists:
            db.add(GroupMember(group_id=group_id, user_id=uid, role=MemberRole.member))
            
            # If group is linked to a space, add member to space as well
            if group.space_id:
                result = await db.execute(select(SpaceMembership).where(
                    SpaceMembership.space_id == group.space_id,
                    SpaceMembership.user_id == uid
                ))
                space_membership = result.scalars().first()
                
                if not space_membership:
                    # Add to space with member role
                    space_membership = SpaceMembership(
                        user_id=uid,
                        space_id=group.space_id,
                        role=SpaceRole.member.value,
                        created_by=current_user.id
                    )
                    db.add(space_membership)
    
    group.updated_at = datetime.utcnow()
    await db.commit()

    # Invalidate caches for all newly added users so their sidebars refresh
    try:
        for uid in members.member_ids:
            await invalidate_user_cache(uid)
    except Exception:
        pass

    # Notify newly added users
    for uid in members.member_ids:
        if uid != current_user.id:
            await mqtt_client.publish_event(uid, {
                "type": "group_invite",
                "group": group.to_dict(include_members=True),
                "adder_name": current_user.username
            })

    # Create system message inside group chat
    from ..models.group_chat import GroupMessage
    result = await db.execute(select(User).where(User.id.in_(members.member_ids)))
    added_users = result.scalars().all()
    added_names = ", ".join([u.username for u in added_users])
    sys_msg = GroupMessage(
        group_id=group_id,
        sender_id=current_user.id,
        content=f"{current_user.username} added {added_names} to the group",
        message_type="system"
    )
    db.add(sys_msg)
    await db.commit()
    await db.refresh(sys_msg)

    # Broadcast system message
    for m in group.members:
        await mqtt_client.publish_event(m.user_id, {
            "type": "group_new_message",
            "group_id": group_id,
            "message": sys_msg.to_dict()
        })

    # broadcast group update
    for m in group.members:
        await mqtt_client.publish_event(m.user_id, {
            "type": "group_update",
            "group": group.to_dict(include_members=True)
        })

    # Personal notification to every member
    for m in group.members:
        await mqtt_client.publish_event(m.user_id, {
            "type": "group_member_added",
            "group_id": group_id,
            "group_name": group.name,
            "adder_name": current_user.username,
            "added_names": added_names
        })

    await db.refresh(group)
    # Invalidate group cache and member group lists
    await invalidate_group_cache(group_id)
    for m in group.members:
        await invalidate_user_cache(m.user_id)
    
    # Broadcast group update to members
    for m in group.members:
        await mqtt_client.publish_event(m.user_id, {
            "type": "group_update",
            "group": group.to_dict(include_members=True)
        })
    result = await db.execute(select(Group).options(joinedload(Group.members).joinedload(GroupMember.user)).where(Group.id == group_id))
    group = result.unique().scalars().first()
    return group.to_dict(include_members=True)

# Remove member / leave group
@router.delete("/{group_id}/members/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_group_member(
    group_id: int,
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    result = await db.execute(
        select(Group).options(joinedload(Group.members)).where(Group.id == group_id, Group.is_active == True)
    )
    group = result.unique().scalars().first()
    if not group:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Group not found")
    result = await db.execute(select(GroupMember).where(GroupMember.group_id == group_id, GroupMember.user_id == user_id))
    member_rec = result.scalars().first()
    if not member_rec:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Member not in group")
    # If current user is removing someone else, must be admin
    if user_id != current_user.id:
        result = await db.execute(
            select(GroupMember).where(
                GroupMember.group_id == group_id,
                GroupMember.user_id == current_user.id
            )
        )
        admin_rec = result.scalars().first()
        if not admin_rec or admin_rec.role != MemberRole.admin:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized")
    
    await db.delete(member_rec)
    
    # If group is linked to a space, remove member from space as well
    if group.space_id:
        # Don't remove space owner from space
        from ..models.user_space import UserSpace
        result = await db.execute(select(UserSpace).where(UserSpace.id == group.space_id))
        space = result.scalars().first()
        if space and user_id != space.owner_id:
            result = await db.execute(select(SpaceMembership).where(
                SpaceMembership.space_id == group.space_id,
                SpaceMembership.user_id == user_id
            ))
            space_membership = result.scalars().first()
            
            if space_membership:
                await db.delete(space_membership)
    
    group.updated_at = datetime.utcnow()
    await db.commit()

    # Invalidate caches so sidebars refresh for the removed user and remaining members
    try:
        await invalidate_user_cache(user_id)
        for m in group.members:
            await invalidate_user_cache(m.user_id)
    except Exception:
        pass

    # Create system message
    from ..models.group_chat import GroupMessage
    result = await db.execute(select(User).where(User.id == user_id))
    target_user = result.scalars().first()
    if target_user:
        if user_id == current_user.id:
            content = f"{current_user.username} left the group"
        else:
            content = f"{current_user.username} removed {target_user.username} from the group"
        sys_msg = GroupMessage(group_id=group_id, sender_id=current_user.id, content=content, message_type="text")
        sys_msg.message_type = "system"
        db.add(sys_msg)
        await db.commit()
        await db.refresh(sys_msg)

        for m in group.members:
            await mqtt_client.publish_event(m.user_id, {
                "type": "group_new_message",
                "group_id": group_id,
                "message": sys_msg.to_dict()
            })
        # Inform remaining members about membership change
        for m in group.members:
            await mqtt_client.publish_event(m.user_id, {
                "type": "group_member_removed",
                "group_id": group_id,
                "user_id": user_id
            })
        # Notify the removed user explicitly so they can drop group from UI
        try:
            await mqtt_client.publish_event(user_id, {
                "type": "group_member_removed",
                "group_id": group_id,
                "user_id": user_id
            })
        except Exception:
            pass
        # Inform the removed user as well
        await mqtt_client.publish_event(user_id, {
            "type": "group_left",
            "group_id": group_id
        })
    # Invalidate group cache and member group lists (including removed user)
    await invalidate_group_cache(group_id)
    unique_user_ids = {m.user_id for m in group.members}
    unique_user_ids.add(user_id)
    for uid in unique_user_ids:
        await invalidate_user_cache(uid)
    # If no members remain, deactivate group and notify removed user
    remaining = await db.scalar(select(func.count(GroupMember.id)).where(GroupMember.group_id == group_id))
    if remaining == 0:
        result = await db.execute(select(Group).where(Group.id == group_id))
        group = result.unique().scalars().first()
        if group and group.is_active:
            group.is_active = False
            group.updated_at = datetime.utcnow()
            await db.commit()
        try:
            await mqtt_client.publish_event(user_id, {
                "type": "group_deleted",
                "group_id": group_id
            })
        except Exception:
            pass
    return 

# -----------------------------
# Clear group chat history (hide all messages for current user)
# -----------------------------


@router.delete("/{group_id}/clear-history", status_code=status.HTTP_204_NO_CONTENT)
async def clear_group_history(
    group_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Hide all group messages in a group for **this** user only (local clear history)."""

    # Verify membership
    result = await db.execute(select(GroupMember).where(
        GroupMember.group_id == group_id,
        GroupMember.user_id == current_user.id,
    ))
    membership = result.scalars().first()

    if not membership:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not a group member")

    # Fetch all message ids in the group
    result = await db.execute(select(GroupMessage.id).where(GroupMessage.group_id == group_id))
    message_ids = result.scalars().all()

    # Insert deletion records if missing
    to_create = []
    for mid in message_ids:
        result = await db.execute(
            select(DeletedGroupMessage).where(
                DeletedGroupMessage.user_id == current_user.id,
                DeletedGroupMessage.group_message_id == mid
            )
        )
        exists = result.scalars().first()
        if not exists:
            to_create.append(DeletedGroupMessage(user_id=current_user.id, group_message_id=mid))

    if to_create:
        for obj in to_create:
            db.add(obj)
        await db.commit()

    # Invalidate message caches for this group
    await invalidate_group_cache(group_id)

    return  # 204 