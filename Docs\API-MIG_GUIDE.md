# API Migration Visual Guide

## Current vs. Proposed Structure

### BEFORE: Monolithic Structure
```
frontend/src/lib/
└── api.ts (960 lines) [Not Good]
    ├── Config & Auth utilities
    ├── Agent APIs
    ├── Knowledge Base APIs
    ├── Dashboard APIs
    ├── Notes APIs
    ├── Activity APIs
    ├── Spaces APIs
    ├── Users APIs
    └── Generic utilities
```

### AFTER: Modular Structure
```
frontend/src/
├── lib/api/ 
│   ├── config.ts (30 lines)
│   ├── client.ts (180 lines)
│   ├── auth.api.ts (40 lines)
│   ├── agents.api.ts (100 lines)
│   ├── knowledge.api.ts (120 lines)
│   ├── dashboard.api.ts (80 lines)
│   ├── notes.api.ts (70 lines)
│   ├── spaces.api.ts (90 lines)
│   ├── users.api.ts (40 lines)
│   └── index.ts (20 lines)
│
├── hooks/api/ 
│   ├── useAuth.ts (60 lines)
│   ├── useAgents.ts (150 lines)
│   ├── useKnowledge.ts (180 lines)
│   ├── useDashboard.ts (160 lines)
│   ├── useNotes.ts (140 lines)
│   ├── useSpaces.ts (150 lines)
│   ├── useUsers.ts (80 lines)
│   └── index.ts (10 lines)
│
└── types/api/ 
    ├── common.ts (30 lines)
    ├── agent.types.ts (100 lines)
    ├── knowledge.types.ts (60 lines)
    ├── dashboard.types.ts (80 lines)
    ├── note.types.ts (50 lines)
    ├── space.types.ts (70 lines)
    └── index.ts (10 lines)
```

---

##  Code Transformation Examples

### Example 1: Agent Query

#### BEFORE (Direct API call)
```typescript
// In component file
import { queryAgent, QueryRequest, QueryResponse } from '@/lib/api';

function ChatComponent() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleQuery = async (query: string) => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access_token'); // [Not Good] Manual auth
      if (!token) throw new Error('Not authenticated');
      
      const response = await fetch(`${API_BASE_URL}/agents/query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          agent_type: 'general',
          query: query,
        }),
      });
      
      if (!response.ok) throw new Error('Query failed');
      const data = await response.json();
      // Handle response
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
}
```

#### AFTER (Using Hook)
```typescript
// In component file
import { useAgents } from '@/hooks/api';

function ChatComponent() {
  const { queryAgent, loading, error, clearError } = useAgents();

  const handleQuery = async (query: string) => {
    try {
      const response = await queryAgent({ //  Auth handled automatically
        agent_type: 'general',
        query: query,
      });
      // Handle response
    } catch (err) {
      // Error already tracked in hook
    }
  };
}
```

**Benefits:**
-  80% less boilerplate code
-  Automatic auth handling
-  Centralized error management
-  Better TypeScript support
-  Easier testing

---

### Example 2: Document Upload

#### BEFORE
```typescript
import { uploadDocument, KnowledgeDocument } from '@/lib/api';

function UploadComponent() {
  const handleUpload = async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const params = new URLSearchParams({
        title: file.name,
        description: '',
        department: 'general',
      });
      
      const response = await fetch(
        `${API_BASE_URL}/knowledge/upload?${params.toString()}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`
          },
          body: formData,
        }
      );
      
      if (!response.ok) {
        throw new Error('Upload failed');
      }
      
      const result = await response.json();
      // Handle success
    } catch (err) {
      // Handle error
    }
  };
}
```

#### AFTER
```typescript
import { useKnowledge } from '@/hooks/api';

function UploadComponent() {
  const { uploadDocument, uploadProgress, loading, error } = useKnowledge();

  const handleUpload = async (file: File) => {
    try {
      const result = await uploadDocument({
        file,
        title: file.name,
        description: '',
        department: 'general',
      });
      // Handle success
    } catch (err) {
      // Error handled by hook
    }
  };
  
  return (
    <div>
      {loading && <ProgressBar value={uploadProgress} />}
      {error && <ErrorMessage message={error} />}
      {/* Upload UI */}
    </div>
  );
}
```

**Benefits:**
-  Built-in progress tracking
-  Cleaner API
-  Automatic error handling
-  Upload progress included

---

### Example 3: Multiple API Calls

#### BEFORE
```typescript
import { 
  getDashboardSummary, 
  getAllUserMetrics, 
  getMyActivity 
} from '@/lib/api';

function DashboardPage() {
  const [summary, setSummary] = useState(null);
  const [users, setUsers] = useState([]);
  const [activity, setActivity] = useState([]);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [summaryData, usersData, activityData] = await Promise.all([
          getDashboardSummary(),
          getAllUserMetrics(),
          getMyActivity(),
        ]);
        setSummary(summaryData);
        setUsers(usersData);
        setActivity(activityData);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);
}
```

#### AFTER
```typescript
import { useDashboard } from '@/hooks/api';

function DashboardPage() {
  const {
    summary,
    userMetrics,
    activity,
    loading,
    error,
    fetchSummary,
    fetchAllUserMetrics,
    fetchMyActivity,
  } = useDashboard();
  
  useEffect(() => {
    Promise.all([
      fetchSummary(),
      fetchAllUserMetrics(),
      fetchMyActivity(),
    ]);
  }, []);
  
  // Data automatically tracked in hook state
}
```

**Benefits:**
-  Single hook for related operations
-  Automatic state management
-  Unified loading/error states
-  Less useState boilerplate

---

## 🎨 Architecture Layers

```
┌─────────────────────────────────────────────────────────┐
│                    COMPONENTS LAYER                      │
│  (React Components consume hooks and display UI)        │
│                                                          │
│  Example: ChatComponent, UploadDialog, Dashboard        │
└──────────────────────┬───────────────────────────────────┘
                       │ imports
                       ▼
┌─────────────────────────────────────────────────────────┐
│                   HOOKS LAYER (NEW)                      │
│  (React hooks provide state management + API calls)     │
│                                                          │
│  useAgents() → { queryAgent, loading, error }           │
│  useKnowledge() → { uploadDocument, documents }         │
│  useDashboard() → { summary, fetchSummary }             │
└──────────────────────┬───────────────────────────────────┘
                       │ calls
                       ▼
┌─────────────────────────────────────────────────────────┐
│                   API LAYER (NEW)                        │
│  (Pure functions for API calls, no React)               │
│                                                          │
│  agentsApi.query() → Promise<QueryResponse>             │
│  knowledgeApi.upload() → Promise<Document>              │
│  dashboardApi.getSummary() → Promise<Summary>           │
└──────────────────────┬───────────────────────────────────┘
                       │ uses
                       ▼
┌─────────────────────────────────────────────────────────┐
│                  CLIENT LAYER (NEW)                      │
│  (HTTP client with auth, error handling, interceptors)  │
│                                                          │
│  apiRequest(), apiGet(), apiPost(), apiUpload()         │
│  getAuthHeaders(), error handling, retries              │
└──────────────────────┬───────────────────────────────────┘
                       │ sends requests to
                       ▼
┌─────────────────────────────────────────────────────────┐
│                   BACKEND API                            │
│  (FastAPI endpoints)                                     │
└─────────────────────────────────────────────────────────┘
```

---

## 🗂️ File Organization Pattern

### Domain-Driven Design

Each domain gets **3 files**:

```
1️⃣ types/api/[domain].types.ts
   └── Type definitions only

2️⃣ lib/api/[domain].api.ts  
   └── API service functions (pure, no React)

3️⃣ hooks/api/use[Domain].ts
   └── React hook (state + API integration)
```

### Example: Notes Domain

```typescript
// 1. types/api/note.types.ts
export interface NoteItem { id: number; title: string; /* ... */ }
export interface CreateNotePayload { title: string; /* ... */ }

// 2. lib/api/notes.api.ts
export const notesApi = {
  create: (data: CreateNotePayload) => apiPost<NoteItem>('/notes', data),
  list: () => apiGet<NoteItem[]>('/notes'),
  // ...
};

// 3. hooks/api/useNotes.ts
export const useNotes = () => {
  const [notes, setNotes] = useState<NoteItem[]>([]);
  const [loading, setLoading] = useState(false);
  
  const createNote = async (data: CreateNotePayload) => {
    setLoading(true);
    const result = await notesApi.create(data);
    setNotes(prev => [result, ...prev]);
    setLoading(false);
    return result;
  };
  
  return { notes, loading, createNote };
};
```

---

## 📦 Import Patterns

###  Recommended Import Patterns

```typescript
// In React components - USE HOOKS
import { useAgents, useKnowledge, useNotes } from '@/hooks/api';

// In utility functions - USE API SERVICES
import { agentsApi, knowledgeApi } from '@/lib/api';

// In type definitions
import type { Agent, QueryRequest, QueryResponse } from '@/types/api';

// Barrel imports (convenient)
import { useAgents, agentsApi, type Agent } from '@/hooks/api';
```

### [Not Good] Anti-Patterns (Avoid)

```typescript
// Don't mix concerns
import { useAgents } from '@/hooks/api';
import { agentsApi } from '@/lib/api';
// Then using both in same component - pick one!

// Don't bypass the layers
import { apiRequest } from '@/lib/api/client';
// Use domain-specific API or hook instead

// Don't import from deep paths
import { agentsApi } from '@/lib/api/agents.api';
// Use barrel export: '@/lib/api'
```

---

## 🚦 Migration Phases

### Phase 1: Foundation (Week 1)
```
✓ Create lib/api/config.ts
✓ Create lib/api/client.ts
✓ Create types/api/ directory
✓ No breaking changes
```

### Phase 2: API Services (Week 1-2)
```
✓ Create lib/api/[domain].api.ts files
✓ Move API functions from api.ts
✓ Add unit tests
✓ Keep api.ts as compatibility layer
```

### Phase 3: React Hooks (Week 2-3)
```
✓ Create hooks/api/use[Domain].ts files
✓ Integrate with API services
✓ Add integration tests
```

### Phase 4: Component Migration (Week 3-4)
```
✓ Update 5-10 components per day
✓ Test each component thoroughly
✓ Monitor for regressions
```

### Phase 5: Cleanup (Week 4)
```
✓ Remove old api.ts file
✓ Update all imports
✓ Remove unused code
✓ Final testing
```

---

## 📈 Migration Tracking

### Components Using Old API
```typescript
// Search for direct api.ts imports
// Find: import .* from '@/lib/api'
// Replace with new hooks

// Priority order:
1. Critical: Chat, Dashboard (high usage)
2. Medium: Documents, Spaces
3. Low: Notes, Activity
```

### Migration Checklist Per Component
- [ ] Identify all API calls
- [ ] Find corresponding hook
- [ ] Replace with hook usage
- [ ] Remove manual state management
- [ ] Test functionality
- [ ] Test error cases
- [ ] Update tests
- [ ] Code review

---

## 🧪 Testing Strategy

### Unit Tests (API Services)
```typescript
// lib/api/agents.api.test.ts
describe('agentsApi', () => {
  it('should query agent successfully', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ answer: 'test' }),
    });
    
    const result = await agentsApi.query({
      agent_type: 'general',
      query: 'test',
    });
    
    expect(result.answer).toBe('test');
  });
});
```

### Integration Tests (Hooks)
```typescript
// hooks/api/useAgents.test.ts
import { renderHook, act } from '@testing-library/react-hooks';
import { useAgents } from './useAgents';

describe('useAgents', () => {
  it('should fetch agents', async () => {
    const { result } = renderHook(() => useAgents());
    
    await act(async () => {
      await result.current.fetchAgents();
    });
    
    expect(result.current.agents).toHaveLength(2);
    expect(result.current.loading).toBe(false);
  });
});
```

---

## 💡 Pro Tips

### 1. **Use TypeScript Strictly**
```typescript
//  Good - Full type safety
const { queryAgent } = useAgents();
const response: QueryResponse = await queryAgent(request);

// [Not Good] Bad - Lost type safety
const result: any = await queryAgent(request);
```

### 2. **Error Handling**
```typescript
//  Good - Use hook's error state
const { createNote, error, clearError } = useNotes();

useEffect(() => {
  if (error) {
    showToast(error);
    clearError();
  }
}, [error]);

// [Not Good] Bad - Manual try-catch everywhere
try {
  await createNote(data);
} catch (err) {
  console.error(err);
}
```

### 3. **Loading States**
```typescript
//  Good - Centralized loading
const { uploadDocument, loading, uploadProgress } = useKnowledge();

if (loading) return <Spinner progress={uploadProgress} />;

// [Not Good] Bad - Separate loading state
const [uploading, setUploading] = useState(false);
const { uploadDocument } = useKnowledge();
```

### 4. **Optimistic Updates**
```typescript
//  Good - Update UI immediately
const { createNote } = useNotes();

const handleCreate = async (data) => {
  const tempNote = { id: 'temp', ...data };
  setNotes(prev => [tempNote, ...prev]); // Optimistic
  
  try {
    const realNote = await createNote(data);
    setNotes(prev => prev.map(n => n.id === 'temp' ? realNote : n));
  } catch {
    setNotes(prev => prev.filter(n => n.id !== 'temp')); // Rollback
  }
};
```

---

## 🎯 Success Metrics

-  **60% less boilerplate** in components
-  **100% type coverage** for all APIs
-  **Zero runtime errors** from auth issues
-  **50% faster** new feature development
-  **90% test coverage** on API layer
-  **<200 lines** per file average
-  **3-5 second** file navigation time

---

## 📚 Quick Reference

### Common Tasks

| Task | Hook | Method |
|------|------|--------|
| Query agent | `useAgents()` | `queryAgent(request)` |
| Upload document | `useKnowledge()` | `uploadDocument(params)` |
| Create note | `useNotes()` | `createNote(data)` |
| Get dashboard | `useDashboard()` | `fetchSummary()` |
| Create space | `useSpaces()` | `createSpace(data)` |
| Find user | `useUsers()` | `findUser(username)` |

### Error Handling

```typescript
const { error, clearError } = useAnyHook();

// Display error
{error && <ErrorBanner message={error} onClose={clearError} />}

// Check in effect
useEffect(() => {
  if (error) {
    logError(error);
    clearError();
  }
}, [error]);
```

### Loading States

```typescript
const { loading } = useAnyHook();

if (loading) return <LoadingSpinner />;

// Or inline
<Button disabled={loading}>
  {loading ? 'Saving...' : 'Save'}
</Button>
```

---

## 🔗 Related Documents

- [API_REFACTORING_ARCHITECTURE.md](./API_REFACTORING_ARCHITECTURE.md) - Full technical architecture
- [PRODUCTION_ARCHITECTURE_GUIDE.md](./PRODUCTION_ARCHITECTURE_GUIDE.md) - Overall app architecture
- [PRODUCTION_TICKET_SYSTEM.md](./PRODUCTION_TICKET_SYSTEM.md) - Ticket system architecture

---

**Questions? Suggestions?**

Review these guides and provide feedback before starting the migration!

