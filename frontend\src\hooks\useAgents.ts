import { useState, useEffect } from 'react';
import { useChat } from '../contexts';
import { getAgents, Agent } from '../lib/api';

export function useAgents() {
  const { selectedAgent, setSelectedAgent, clearChat } = useChat();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadAgents() {
      try {
        setIsLoading(true);
        
        const fetchedAgents = await getAgents();
        setAgents(fetchedAgents);
        
        // Set default agent if none selected
        if (!selectedAgent && fetchedAgents.length > 0) {
          setSelectedAgent(fetchedAgents[0]);
        }
        
        setError(null);
      } catch (err) {
        console.error('Error loading agents:', err);
        setAgents([]);
        setError('Unable to load agents');
      } finally {
        setIsLoading(false);
      }
    }

    loadAgents();
  }, [selectedAgent, setSelectedAgent]);

  // Handle agent selection
  const handleAgentChange = (agent: Agent) => {
    clearChat();
    setSelectedAgent(agent);
  };

  return {
    agents,
    isLoading,
    error,
    selectedAgent,
    handleAgentChange
  };
} 