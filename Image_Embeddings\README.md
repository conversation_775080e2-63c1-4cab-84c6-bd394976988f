# Image Embedding Service

A high-performance image embedding and captioning service powered by Google's SigLIP-2 model with GPU acceleration.

## Features

- **Image Embeddings**: Generate high-quality embeddings for image similarity search
- **Image Captions**: Generate descriptive captions for images (SigLIP-2)
- **Text Embeddings**: Generate text embeddings for image-text retrieval
- **GPU Acceleration**: Optimized for NVIDIA GPUs with CUDA support
- **Batch Processing**: Efficient batch processing for multiple images
- **REST API**: FastAPI-based REST API for easy integration

## Architecture

- **Model**: `google/siglip-so400m-patch14-384`
- **Framework**: PyTorch with CUDA support
- **API**: FastAPI with async support
- **Port**: 8019

## Requirements

### Hardware
- NVIDIA GPU with CUDA support (recommended)
- Minimum 8GB GPU memory
- 16GB+ RAM

### Software
- Docker with NVIDIA Container Toolkit
- Docker Compose (optional)

## Installation

### Option 1: Using Docker Compose (Recommended)

1. **<PERSON>lone and navigate to the directory**:
   ```bash
   cd Image_Embeddings
   ```

2. **Copy environment configuration**:
   ```bash
   cp env_example.txt .env
   ```

3. **Build and run with Docker Compose**:
   ```bash
   docker-compose up -d --build
   ```

4. **Check service health**:
   ```bash
   curl http://localhost:8019/health
   ```

### Option 2: Using Docker

1. **Build the Docker image**:
   ```bash
   docker build -t image-embedding-service .
   ```

2. **Run the container**:
   ```bash
   docker run -d \
     --name image-embedding-service \
     --gpus all \
     -p 8019:8019 \
     -v $(pwd)/model_cache:/app/model_cache \
     image-embedding-service
   ```

### Option 3: Local Development

1. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # or
   .\venv\Scripts\activate  # Windows
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the service**:
   ```bash
   python image_embedding_service.py
   ```

## API Endpoints

### Health Check
```bash
GET /health
```

Response:
```json
{
  "status": "healthy",
  "model": "google/siglip-so400m-patch14-384",
  "device": "cuda:0",
  "cuda_available": true,
  "gpu_name": "NVIDIA GeForce RTX 3090"
}
```

### Generate Image Embeddings
```bash
POST /embed
Content-Type: application/json

{
  "images": ["base64_encoded_image_1", "base64_encoded_image_2"]
}
```

Response:
```json
{
  "embeddings": [[0.123, 0.456, ...], [0.789, 0.012, ...]],
  "dimension": 1152,
  "processing_time": 0.234
}
```

### Generate Image Captions
```bash
POST /caption
Content-Type: application/json

{
  "images": ["base64_encoded_image_1", "base64_encoded_image_2"],
  "max_length": 100
}
```

Response:
```json
{
  "captions": ["A description of image 1", "A description of image 2"],
  "processing_time": 1.234
}
```

### Generate Text Embeddings
```bash
POST /embed-text
Content-Type: application/json

["query text 1", "query text 2"]
```

Response:
```json
{
  "embeddings": [[0.123, 0.456, ...], [0.789, 0.012, ...]],
  "dimension": 1152,
  "processing_time": 0.123
}
```

### Upload and Embed Images
```bash
POST /upload-embed
Content-Type: multipart/form-data

files: [image1.jpg, image2.png]
```

### Model Information
```bash
GET /model-info
```

Response:
```json
{
  "model_name": "google/siglip-so400m-patch14-384",
  "device": "cuda:0",
  "cuda_available": true,
  "cuda_version": "12.1",
  "gpu_count": 1,
  "gpu_names": ["NVIDIA GeForce RTX 3090"],
  "batch_size": 8
}
```

## Usage Examples

### Python Client

```python
import requests
import base64
from PIL import Image
import io

# Service URL - Change based on your setup:
# - Local testing: "http://localhost:8019"
# - Docker network: "http://image-embedding-service:8019"
# - External server: "http://YOUR_SERVER_IP:8019"
SERVICE_URL = "http://image-embedding-service:8019"

# Load and encode image
def encode_image(image_path):
    with open(image_path, "rb") as f:
        return base64.b64encode(f.read()).decode('utf-8')

# Generate embeddings
image_base64 = encode_image("path/to/image.jpg")
response = requests.post(
    f"{SERVICE_URL}/embed",
    json={"images": [image_base64]}
)
result = response.json()
print(f"Embedding dimension: {result['dimension']}")
print(f"Processing time: {result['processing_time']}s")

# Generate captions
response = requests.post(
    f"{SERVICE_URL}/caption",
    json={"images": [image_base64], "max_length": 100}
)
result = response.json()
print(f"Caption: {result['captions'][0]}")

# Upload files directly
with open("path/to/image.jpg", "rb") as f:
    response = requests.post(
        f"{SERVICE_URL}/upload-embed",
        files={"files": f}
    )
result = response.json()
print(f"Embeddings: {result['embeddings']}")
```

### cURL Examples

```bash
# Note: Replace localhost with your server IP or service name when calling from external services

# Health check
curl http://YOUR_SERVER_IP:8019/health

# Model info
curl http://YOUR_SERVER_IP:8019/model-info

# Generate embeddings (replace BASE64_IMAGE with actual base64 string)
curl -X POST http://YOUR_SERVER_IP:8019/embed \
  -H "Content-Type: application/json" \
  -d '{"images": ["BASE64_IMAGE"]}'

# Generate captions
curl -X POST http://YOUR_SERVER_IP:8019/caption \
  -H "Content-Type: application/json" \
  -d '{"images": ["BASE64_IMAGE"], "max_length": 100}'

# Upload and embed
curl -X POST http://YOUR_SERVER_IP:8019/upload-embed \
  -F "files=@image.jpg"
```

## Service URL Configuration

The service URL depends on your deployment setup:

### Local Testing
```python
client = ImageEmbeddingClient("http://localhost:8019")
```

### Docker Network (Services in same docker-compose)
```python
# Use the service name from docker-compose.yml
client = ImageEmbeddingClient("http://image-embedding-service:8019")
```

### External Server / Different Machine
```python
# Replace with actual server IP or hostname
client = ImageEmbeddingClient("http://*************:8019")
# or
client = ImageEmbeddingClient("http://your-server-hostname:8019")
```

### Using Environment Variables (Recommended)
```python
import os
service_url = os.getenv("IMAGE_EMBEDDING_SERVICE_URL", "http://image-embedding-service:8019")
client = ImageEmbeddingClient(service_url)
```

## Configuration

Edit `.env` file or set environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | 8019 | Service port |
| `HOST` | 0.0.0.0 | Service host |
| `MODEL_NAME` | google/siglip-so400m-patch14-384 | SigLIP model to use |
| `DEVICE` | cuda | Device (cuda/cpu) |
| `BATCH_SIZE` | 8 | Batch size for processing |
| `MAX_CAPTION_LENGTH` | 100 | Maximum caption length |
| `CACHE_DIR` | ./model_cache | Model cache directory |
| `LOG_LEVEL` | INFO | Logging level |

## Performance

### GPU Performance (NVIDIA RTX 3090)
- **Embedding Generation**: ~50-100ms per image
- **Caption Generation**: ~800ms-1.5s per image
- **Batch Processing (8 images)**: ~400ms for embeddings
- **Throughput**: ~80-100 images/sec for embeddings

### CPU Performance
- **Embedding Generation**: ~300-500ms per image
- **Caption Generation**: ~2-3s per image
- **Batch Processing**: Slower, not recommended for production

## Monitoring

### Check Logs
```bash
# Docker Compose
docker-compose logs -f image-embedding-service

# Docker
docker logs -f image-embedding-service
```

### Check GPU Usage
```bash
nvidia-smi
```

### Check Service Status
```bash
curl http://localhost:8019/health
```

## Troubleshooting

### GPU Not Detected
1. Verify NVIDIA drivers are installed:
   ```bash
   nvidia-smi
   ```

2. Verify Docker has GPU access:
   ```bash
   docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi
   ```

3. Install NVIDIA Container Toolkit if missing:
   ```bash
   distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
   curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
   curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
   sudo apt-get update && sudo apt-get install -y nvidia-container-toolkit
   sudo systemctl restart docker
   ```

### Out of Memory
- Reduce `BATCH_SIZE` in `.env`
- Use smaller model variant
- Monitor GPU memory: `nvidia-smi`

### Slow Performance
- Verify GPU is being used: Check `/model-info` endpoint
- Increase `BATCH_SIZE` for batch processing
- Use SSD for model cache
- Enable model quantization (requires code modification)

## Integration with Data Ingestion Service

To integrate with your Data Ingestion Service:

```python
import requests
import base64
import os

class ImageEmbeddingClient:
    def __init__(self, service_url=None):
        # Use environment variable or parameter
        # For Docker networks, use service name: "http://image-embedding-service:8019"
        # For external calls, use server IP: "http://YOUR_SERVER_IP:8019"
        self.service_url = service_url or os.getenv("IMAGE_EMBEDDING_SERVICE_URL", "http://image-embedding-service:8019")
    
    def embed_images(self, images_base64):
        """Generate embeddings for images"""
        response = requests.post(
            f"{self.service_url}/embed",
            json={"images": images_base64}
        )
        return response.json()
    
    def caption_images(self, images_base64, max_length=100):
        """Generate captions for images"""
        response = requests.post(
            f"{self.service_url}/caption",
            json={"images": images_base64, "max_length": max_length}
        )
        return response.json()

# Usage in your pipeline
# Pass the service URL based on your deployment:
# - Same Docker network: "http://image-embedding-service:8019"
# - External server: "http://YOUR_SERVER_IP:8019"
client = ImageEmbeddingClient("http://image-embedding-service:8019")
result = client.embed_images([image_base64])
embeddings = result['embeddings']
```

## Model Information

### SigLIP-so400m-patch14-384
- **Parameters**: 400M
- **Embedding Dimension**: 1152
- **Input Size**: 384x384
- **Architecture**: Vision Transformer (ViT)
- **Training**: Sigmoid loss (more stable than CLIP)
- **Capabilities**: 
  - Image embeddings
  - Text embeddings
  - Image-text retrieval
  - Caption generation (if decoder available)

## Development

### Running Tests
```bash
pytest tests/
```

### Code Formatting
```bash
black .
isort .
```

### Type Checking
```bash
mypy .
```

## License

This service uses the SigLIP-2 model from Google Research, which is open-source.

## Support

For issues and questions:
1. Check the logs: `docker-compose logs -f`
2. Verify GPU setup: `nvidia-smi`
3. Test API endpoints: `curl http://localhost:8019/health`

