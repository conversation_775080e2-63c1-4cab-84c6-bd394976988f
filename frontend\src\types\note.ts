/**
 * Note-related type definitions
 */

export interface NoteItem {
  id: number;
  user_id?: number;
  interaction_id?: number | null;
  title: string;
  combined_text?: string | null;
  extra_text?: string | null;
  sources?: any;
  agent_type?: string | null;
  session_id?: string | null;
  created_at: string;
  updated_at?: string | null;
}

export interface CreateNotePayload {
  // Prefer linking to an interaction when available
  response_id?: string;
  interaction_id?: number;
  title: string;
  combined_text?: string;
  extra_text?: string;
  // Optional snapshot for manual notes (backend may use this directly)
  sources?: any;
  agent_type?: string;
  session_id?: string;
}

export interface UpdateNotePayload {
  title?: string;
  extra_text?: string;
  combined_text?: string;
}

export interface NotesModalProps {
  isOpen: boolean;
  onClose: () => void;
  preset?: NotePreset;
  onSaved?: (noteId: number) => void;
}

export interface NotePreset {
  responseId?: string;
  interactionId?: number;
  title?: string;
  combinedText?: string;
  sources?: any;
  agentType?: string;
  sessionId?: string;
}

export interface ParsedContent {
  query: string;
  answer: string;
}

export interface ListNotesParams {
  search?: string;
  page?: number;
  page_size?: number;
}
